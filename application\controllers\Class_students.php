<?php

  defined('BASEPATH') OR exit('No direct script access allowed');

  class Class_students extends CI_Controller {
    private $year_id;

    public function __construct() {
      parent::__construct();

      // header('Access-Control-Allow-Origin: *');
      // header('Cross-Origin-Resource-Policy: cross-origin');
      // header('Access-Control-Allow-Headers: Authorization,Content-Type,Accept,Origin,User-Agent,DNT,Cache-Control,X-Mx-ReqToken,Keep-Alive,X-Requested-With,If-Modified-Since');

      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('Class_students_model', 'cs_model');
      $this->load->model('class_section');
    }

    public function get_section(){
      $class_id =$this->input->post('className');
      $acad_year =$this->input->post('acad_year');
      $batch =$this->input->post('batch');
      $section = $this->class_section->getSectionsByClassId($class_id, 1);
      $stdName = $this->class_section->get_studentclassSectionwise($class_id, 0, $acad_year, $batch);
      echo json_encode(array('section'=>$section,'stdname'=>$stdName));
    }

    public function get_student(){
      $class_id =$this->input->post('className');
      $section_id =$this->input->post('section');
      $acad_year =$this->input->post('acad_year');
      $batch =$this->input->post('batch');
      $result = $this->class_section->get_studentclassSectionwise($class_id,$section_id, $acad_year, $batch);
      echo json_encode($result);
    }

    public function get_student_counselling(){
      $class_id =$this->input->post('className');
      $section_id =$this->input->post('section');
      $result = $this->class_section->get_student_counselling($class_id,$section_id);
      echo json_encode($result);
    }

}