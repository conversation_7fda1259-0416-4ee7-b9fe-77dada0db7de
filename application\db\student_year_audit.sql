DROP TRIGGER IF EXISTS student_year_audit;
DELIMITER $$
CREATE TRIGGER student_year_audit
AFTER UPDATE ON student_year FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.roll_no, NEW.roll_no, 'roll_no');
    CALL merge_object(@oldJson, @newJson, OLD.class_id, NEW.class_id, 'class_id');
    CALL merge_object(@oldJson, @newJson, OLD.class_section_id, NEW.class_section_id, 'class_section_id');
    CALL merge_object(@oldJson, @newJson, OLD.semester, NEW.semester, 'semester');
    CALL merge_object(@oldJson, @newJson, OLD.boarding, NEW.boarding, 'boarding');
    CALL merge_object(@oldJson, @newJson, OLD.admission_type, NEW.admission_type, 'admission_type');
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.board, NEW.board, 'board');
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.medium, NEW.medium, 'medium');
    CALL merge_object(@oldJson, @newJson, OLD.donor, NEW.donor, 'donor');
    CALL merge_object(@oldJson, @newJson, OLD.student_house, NEW.student_house, 'student_house');
    CALL merge_object(@oldJson, @newJson, OLD.previous_class_id, NEW.previous_class_id, 'previous_class_id');
    CALL merge_object(@oldJson, @newJson, OLD.fee_mode, NEW.fee_mode, 'fee_mode');
    CALL merge_object(@oldJson, @newJson, OLD.student_admission_id, NEW.student_admission_id, 'student_admission_id');
    CALL merge_object(@oldJson, @newJson, OLD.acad_year_id, NEW.acad_year_id, 'acad_year_id');
    CALL merge_object(@oldJson, @newJson, OLD.promotion_status, NEW.promotion_status, 'promotion_status');
    CALL merge_object(@oldJson, @newJson, OLD.has_transport_km, NEW.has_transport_km, 'has_transport_km');
    CALL merge_object(@oldJson, @newJson, OLD.stop, NEW.stop, 'stop');
    CALL merge_object(@oldJson, @newJson, OLD.pickup_mode, NEW.pickup_mode, 'pickup_mode');
    CALL merge_object(@oldJson, @newJson, OLD.after_school_sport, NEW.after_school_sport, 'after_school_sport');
    CALL merge_object(@oldJson, @newJson, OLD.after_school_sport_days, NEW.after_school_sport_days, 'after_school_sport_days');
    CALL merge_object(@oldJson, @newJson, OLD.high_quality_picture_url, NEW.high_quality_picture_url, 'high_quality_picture_url');
    CALL merge_object(@oldJson, @newJson, OLD.combination, NEW.combination, 'combination');
    CALL merge_object(@oldJson, @newJson, OLD.terminate_date, NEW.terminate_date, 'terminate_date');
    CALL merge_object(@oldJson, @newJson, OLD.tc_number, NEW.tc_number, 'tc_number');
    CALL merge_object(@oldJson, @newJson, OLD.profile_confirmed, NEW.profile_confirmed, 'profile_confirmed');
    CALL merge_object(@oldJson, @newJson, OLD.profile_confirmed_date, NEW.profile_confirmed_date, 'profile_confirmed_date');
    CALL merge_object(@oldJson, @newJson, OLD.is_rte, NEW.is_rte, 'is_rte');
    CALL merge_object(@oldJson, @newJson, OLD.drop_stop, NEW.drop_stop, 'drop_stop');
    CALL merge_object(@oldJson, @newJson, OLD.picture_url, NEW.picture_url, 'picture_url');
    CALL merge_object(@oldJson, @newJson, OLD.previous_class_section_id, NEW.previous_class_section_id, 'previous_class_section_id');
    CALL merge_object(@oldJson, @newJson, OLD.terminate_remarks, NEW.terminate_remarks, 'terminate_remarks');
    CALL merge_object(@oldJson, @newJson, OLD.alpha_rollnum, NEW.alpha_rollnum, 'alpha_rollnum');
    CALL merge_object(@oldJson, @newJson, OLD.hall_ticket_num, NEW.hall_ticket_num, 'hall_ticket_num');
      
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'student_year',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        NEW.last_modified_by
    );
    END IF;
END;
$$
DELIMITER ;