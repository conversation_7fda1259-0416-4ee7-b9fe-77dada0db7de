<?php

class Video_controller extends CI_Controller {

	function __construct() {
		parent::__construct();
		// if (!$this->ion_auth->logged_in()) {
		// 	redirect('auth/login', 'refresh');
		// }
		// if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
		// 	redirect('dashboard', 'refresh');
        // }
        $this->load->model('Video_model','video_model');
	}

	// profile
	public function index() {
        $data['main_content'] = 'video/index.php';
        $this->load->view('inc/template', $data);
    }

    public function create_room() {
        $result = $this->video_model->create_room($_POST['room_name'], $_POST['localDescription']);
        echo $result;
    }

    public function get_participants () {
        $result = $this->video_model->get_participants('1');

        echo json_encode($result);
    }


}
