<?php 
class Dompdfmaster extends CI_Controller{
function __construct() {
        parent::__construct();
        $this->load->library('Pdf1');
        $this->load->model('student/Certificates_Model');
}
public function index($data = array()){
    $templateId = $this->uri->segment(3);
    $studentId = $this->uri->segment(4);
    $template = $this->Certificates_Model->getTemplate($templateId);
    $stdData = $this->Certificates_Model->getFullStudentDataById($studentId);
    $stdFatherData = $this->Certificates_Model->getParentDataById($studentId,"Father",array());
    $stdMotherData = $this->Certificates_Model->getParentDataById($studentId,"Mother",array());
    $a = date("d-m-Y", strtotime($stdData->dob));
    $date = explode("-",$a);
    $d = $this->Certificates_Model->convert_number($date[0]);
    $m = $this->Certificates_Model->convert_number($date[1]);
    $y = $this->Certificates_Model->convert_number($date[2]);
    $dob_in_words = $d.'-'.$m.'-'.$y;
    $time = time();

    $year = date('Y', $time);
    if(date('n', $time) < 6)
        $ayear = ($year - 1).'/'.$year;
    else
        $ayear = ($year).'/'.($year + 1);
    $academic_year = $ayear;

    if(!empty($stdData->address)){
        $student_address = $stdData->address->Address_line1."<br>".$stdData->address->Address_line2."<br>".$stdData->address->area."<br>".$stdData->address->district."<br>".$stdData->address->state."<br>".$stdData->address->country."<br>".$stdData->address->pin_code;
    }else if(!empty($stdFatherData->address)){
        $student_address = $stdFatherData->address->Address_line1."<br>".$stdFatherData->address->Address_line2."<br>".$stdFatherData->address->area."<br>".$stdFatherData->address->district."<br>".$stdFatherData->address->state."<br>".$stdFatherData->address->country."<br>".$stdFatherData->address->pin_code;
    }else if(!empty($stdMotherData->address)){
        $student_address = $stdMotherData->address->Address_line1."<br>".$stdMotherData->address->Address_line2."<br>".$stdMotherData->address->area."<br>".$stdMotherData->address->district."<br>".$stdMotherData->address->state."<br>".$stdMotherData->address->country."<br>".$stdMotherData->address->pin_code;
    }else{
        $student_address = ' ';
    }
    if($stdData->gender=="M"){$g = "MALE"; $a = "His";}else{$g="FEMALE"; $a = "Her";}
    if($stdData->gender=="M"){$g = "MALE"; $b = "S";}else{$g="FEMALE"; $b = "D";}
    $template->html_content = str_replace('%%admission_no%%',$stdData->admission_no, $template->html_content); 
    $template->html_content =  str_replace('%%student_name%%',$stdData->stdName, $template->html_content);
    $template->html_content =  str_replace('%%academic_year%%',$academic_year, $template->html_content);
    $template->html_content = str_replace('%%class%%',$stdData->className.'/'.$stdData->sectionName, $template->html_content);
    $template->html_content =  str_replace('%%gender%%',$g, $template->html_content);
    $template->html_content = str_replace('%%dob%%',date("d-m-Y", strtotime($stdData->dob)), $template->html_content);
    $template->html_content = str_replace('%%dob_in_words%%',$dob_in_words, $template->html_content);
    $template->html_content =  str_replace('%%father_name%%',$stdFatherData->pName, $template->html_content);
    $template->html_content = str_replace('%%mother_name%%',$stdMotherData->pName, $template->html_content);
    $template->html_content =  str_replace('%%address%%',$student_address, $template->html_content);
    $template->html_content =  str_replace('%%date%%',date("d/m/Y"), $template->html_content);
    $template->html_content =  str_replace('His / Her',$a, $template->html_content);
    $template->html_content =  str_replace('S/D',$b, $template->html_content);
    $template->html_content =  str_replace(site_url(),'././', $template->html_content);
    $template->html_content .= "<link rel='stylesheet' type='text/css' href='././assets/css/bootstrap/bootstrap.min.css' />";

    $html = "<div class='panel'><div class='panel-body'>";
    $html .= $template->html_content;
    $html .= "</div></div>";
    $dompdf = new Dompdf();
	$dompdf->load_html($html);
    $dompdf->render();
    $dompdf->stream($template->template_name.'-'.$stdData->id.'.pdf',array("Attachment" => 0));
    } 
 
}