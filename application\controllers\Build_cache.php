<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Build_cache extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			// redirect them to the login page
			redirect('auth/login', 'refresh');
    }
    //This should come up only for super admins
    if (!$this->authorization->isSuperAdmin()) {
      redirect('dashboard', 'refresh');
    }
		$this->load->model('Build_cache_model');
	}

	/**
	 * Build Substitution cache
	 */
  public function initSubstitutionCache() {
    $result = $this->Build_cache_model->buildSubstitutionCache();

    if ($result) { 
      $this->session->set_flashdata('flashSuccess', 'Successfully created substitution cache');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('timetable/substitution_menu/index');
  }
}