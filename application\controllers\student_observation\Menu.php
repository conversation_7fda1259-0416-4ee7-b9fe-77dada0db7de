<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Menu extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_OBSERVATION')) {
      redirect('dashboard', 'refresh');
    }
  }

  //Landing function to show the timetable menu
  public function index() {
    $site_url = site_url();

    $data['tiles'] = array(
      [
        'title' => 'My Observations',
        'sub_title' => 'View and add observations',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'student_observation/observation/my_observations',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_OBSERVATION.ADD')
      ],
      [
        'title' => 'De-activate Observations',
        'sub_title' => 'De-active observations',
        'icon' => 'svg_icons/assestdiscardreport.svg',
        'url' => $site_url.'student_observation/observation/deactivate_observations',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_OBSERVATION.DEACTIVATE')
      ],
      [
        'title' => 'Mass Observation',
        'sub_title' => 'Mass Observation',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'student_observation/observation/mass_observation',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_OBSERVATION.MASS_OBSERVATION')
      ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
      [
        'title' => 'Observation Report',
        'sub_title' => 'View observations',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'student_observation/observation/observation_report',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_OBSERVATION.OBSERVATION_REPORT')
      ],
      [
        'title' => 'Student Observation Report',
        'sub_title' => 'View observations of a specific student',
        'icon' => 'svg_icons/student.svg',
        'url' => $site_url.'student_observation/observation/student_observation_report',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_OBSERVATION.STUDENT_OBSERVATION_REPORT')
      ],
      [
        'title' => 'Observation Analytics',
        'sub_title' => 'View Section level Analytics',
        'icon' => 'svg_icons/circularreport.svg',
        'url' => $site_url.'student_observation/observation/section_observation_summary',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_OBSERVATION.SECTION_SUMMARY_REPORT')
      ],
      [
        'title' => 'De-activated Observation Report',
        'sub_title' => 'View deactivated observations report',
        'icon' => 'svg_icons/assestdiscardreport.svg',
        'url' => $site_url.'student_observation/observation/deactived_observations_report',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_OBSERVATION.DEACTIVATE')
      ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['admin_tiles'] = array(
      [
        'title' => 'Manage Categories',
        'sub_title' => 'View, add and modify categories',
        'icon' => 'svg_icons/assestcategory.svg',
        'url' => $site_url.'student_observation/observation/manage_categories',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_OBSERVATION.MANAGE_CATEGORIES')
      ],
      [
        'title' => 'Manage Staff Capacity',
        'sub_title' => 'View, add and modify Staff Capacity',
        'icon' => 'svg_icons/staff.svg',
        'url' => $site_url.'student_observation/observation/manage_capacity',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_OBSERVATION.MANAGE_CAPACITY')
      ],
      [
        'title' => 'Predefined Observations',
        'sub_title' => 'Add Predefined Remarks',
        'icon' => 'svg_icons/circularreport.svg',
        'url' => $site_url.'student_observation/observation/predefined_remarks',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_OBSERVATION.PREDEFINED_REMARKS')
      ]
    );
    $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);

    $data['main_content']    = 'student_observation/menu';
    $this->load->view('inc/template', $data);
  }

}