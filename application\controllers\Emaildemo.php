<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Emaildemo extends CI_Controller 
{
    function __construct()
	{
		parent::__construct();
		/*if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isSuperAdmin()) {
            redirect('dashboard', 'refresh');
        }
	    $this->load->model('competition_model');
        $this->load->model('avatar');*/
	}

    public function email()
    {
        // Call interface
        $this->load->library('mailer', 'mailer');

        // Send email out, using demo template with following variables
        $this->mailer->send('<EMAIL>', 'demo', array(
            'username'  => 'Demo Handler',
            'message'   => 'Hello World',
            'mailtype'  => 'text',
        ));
    }


    public function updatePdfLinkStatus() {
        $this->load->model('examination/assessment_model');
        $input = $this->input->post();
        $this->assessment_model->updatePdfLinkStatus($input['path'], $input['status']);
    }

    public function updateMarksCardZipFile() {
        $this->load->model('examination/assessment_model');
        $input = $this->input->post();
        $this->assessment_model->updatePdfZipLink($input['path'], $input['reference_id']);
    }


    public function testPost() {

        die()
;


        $path   = "npsrnr/testfolder/1234.pdf";
        $bucket = "ne-demoserver";

        $data = '<html><body>
<style type="text/css">
*{
    margin:0px;
    padding: 0px;
}
.container, .container2{
    margin-top:8%;
    width: 100%;
    padding-right: 35px;
    padding-left: 35px;
    margin-right: auto;
    margin-left: auto;
}
.container2{
    margin-top: 5%;
}
.row{
    width: 100%;
}

table tr th {
    vertical-align: middle;
    border: solid 1px #474747;
    text-align: center;
    border-collapse: collapse;
    word-wrap: break-word;
    background:#474747;
    color:#fff;
    padding:1px;
    font-size: 13px;
    height:20px;
}

table tr td {
    vertical-align: middle;
    border: solid 1px #474747;
    border-collapse: collapse;
    word-wrap: break-word;
    padding:1px;
    font-size: 13px;
    height:20px;
}

table{
    border: solid 1px #474747;
    border-collapse: collapse;
    width: 100%;
}
#qrCode{
    position: absolute;
    top: 13%;
    right: 25;
}
#background{
    position: absolute;
    top: 0%;
    left: 0%;
    bottom: 0%;
    right: 0%;
    margin: 0px;
    padding: 0px;
    z-index: -1000;
}

.header-bar, .header-bar2{
    margin:13% auto 0px auto;
    padding:5px;
    width:450px;
    border:2px solid #000;
    text-align: center;
}
.header-bar2{
    margin:2% auto 0px auto;
}

.pad-5{
    padding-left:15px;
}

.padding-5{
    padding-left: 5px;
}

.center{
    text-align: center;
}

#qrCode{
    position: absolute;
    top: 13%;
    right: 25;
}
</style>
<img id="background" src="/home/<USER>/silicon/assets/card-background.png" width="100%"/>
<!-- <img id="qrCode" src="https://s3.amazonaws.com/ne-demoserver/npsrnr/marks_cards/5befe3fb6041f-1542448123.pdf" width="90px"/> -->
<img id="qrCode" src="" width="90px"/>
<div class="header-bar"><h3>SCHOLASTIC ACHIEVEMENT RECORD</h3></div>
<div style="text-align: center;"><h3>2018 - 19</h3></div>
<div class="container">

    <div class="row" style="font-size: 18px;">
        <table style="border:none;"><tr><td style="width: 75%;border:none;"><b>Name : </b>Aadi Narayanan P V </td><td style="width: 25%;border:none;text-align: right;"><b>Class :</b> 1 <b>  Section :</b> A</td></tr></table>
    </div>

    <div style="margin-top: 1%;">

        <table>

            <tr>
                <td colspan="4"><b>A. LANGUAGES</b></td>
            </tr>

            <tr>
                <td colspan="2"><b>ENGLISH</b></td>
                <td colspan="2"><b>HINDI</b></td>
            </tr>

            <tr>
                <td><b>Reading Skills</b></td>
                <td style="text-align: center;"><b>Grade</b></td>
                <td><b>Reading Skills</b></td>
                <td style="text-align: center;"><b>Grade</b></td>
            </tr>

            <tr>
                <td class="padding-5">*  Pronunciation</td>
                <td style="text-align: center;">A </td>
                <td class="padding-5">*  Pronunciation</td>
                <td style="text-align: center;">A </td>
            </tr>

            <tr>
                <td class="padding-5">*  Fluency</td>
                <td style="text-align: center;">B</td>
                <td class="padding-5">*  Fluency</td>
                <td style="text-align: center;">A </td>
            </tr>

            <tr>
                <td class="padding-5">*  Comprehension</td>
                <td style="text-align: center;">D</td>
                <td class="padding-5">*  Comprehension</td>
                <td style="text-align: center;">B</td>
            </tr>

            <tr>
                <td colspan="2"><b>Writing Skills</b></td>
                <td colspan="2"><b>Writing Skills</b></td>
            </tr>

            <tr>
                <td class="padding-5">*  Creative Writing</td>
                <td style="text-align: center;">C</td>
                <td class="padding-5">*  Handwriting</td>
                <td style="text-align: center;">B</td>
            </tr>

            <tr>
                <td class="padding-5">*  Handwriting</td>
                <td style="text-align: center;">B</td>
                <td class="padding-5">*  Spelling</td>
                <td style="text-align: center;">D</td>
            </tr>

            <tr>
                <td class="padding-5">*  Grammar</td>
                <td style="text-align: center;">C</td>
                <td class="padding-5">*  Vocabulary</td>
                <td style="text-align: center;">A</td>
            </tr>

            <tr>
                <td class="padding-5">*  Spelling</td>
                <td style="text-align: center;">B</td>
                <td style="text-align: center;"></td>
                <td style="text-align: center;"></td>
            </tr>

            <tr>
                <td class="padding-5">*  Vocabulary</td>
                <td style="text-align: center;">D</td>
                <td style="text-align: center;"></td>
                <td style="text-align: center;"></td>
            </tr>

            <tr>
                <td class="padding-5">*  Guided Written Work</td>
                <td style="text-align: center;">C</td>
                <td style="text-align: center;"></td>
                <td style="text-align: center;"></td>
            </tr>

            <tr>
                <td colspan="2"><b>Speaking Skills</b></td>
                <td colspan="2"><b>Speaking Skills</b></td>
            </tr>

            <tr>
                <td class="padding-5">*  Conversation</td>
                <td style="text-align: center;">B</td>
                <td class="padding-5">*  Conversation</td>
                <td style="text-align: center;">C</td>
            </tr>

            <tr>
                <td class="padding-5">*  Recitation</td>
                <td style="text-align: center;">A </td>
                <td class="padding-5">*  Recitation</td>
                <td style="text-align: center;">C</td>
            </tr>

            <tr>
                <td colspan="2"><b>Listening Skills</b></td>
                <td colspan="2"></td>
            </tr>

            <tr>
                <td class="padding-5">*  Comprehension</td>
                <td style="text-align: center;">C</td>
                <td style="text-align: center;"></td>
                <td style="text-align: center;"></td>
            </tr>

            <tr>
                <td colspan="2"><b>B. MATHEMATICS</b></td>
                <td colspan="2"><b>C. ENVIRONMENTAL SCIENCES</b></td>
            </tr>

            <tr>
                <td><b>Aspects</b></td>
                <td style="text-align: center;"><b>Grade</b></td>
                <td><b>Social Science</b></td>
                <td style="text-align: center;"><b>Grade</b></td>
            </tr>

            <tr>
                <td class="padding-5">*  Mastery of Concepts</td>
                <td style="text-align: center;">B</td>
                <td class="padding-5">*  Environmental Sensitivity</td>
                <td style="text-align: center;">D</td>
            </tr>

            <tr>
                <td class="padding-5">*  Activity</td>
                <td style="text-align: center;">A</td>
                <td class="padding-5">*  Activity / Project</td>
                <td style="text-align: center;">B</td>
            </tr>

            <tr>
                <td class="padding-5">*  Tables</td>
                <td style="text-align: center;">B</td>
                <td class="padding-5">*  Group Discussion</td>
                <td style="text-align: center;">B</td>
            </tr>

            <tr>
                <td class="padding-5">*  Mental Math Skills</td>
                <td style="text-align: center;">A</td>
                <td style="text-align: center;"></td>
                <td style="text-align: center;"></td>
            </tr>

            <tr>
                <td colspan="2"><b>D. COMPUTER EDUCATION</b></td>
                <td></td>
                <td></td>
            </tr>

            <tr>
                <td><b>Aspects</b></td>
                <td style=" text-align: center;"><b>Grade</b></td>
                <td></td>
                <td></td>
            </tr>

            <tr>
                <td class="padding-5">*  Skill</td>
                <td style="text-align: center;">A</td>
                <td></td>
                <td></td>
            </tr>

            <tr>
                <td class="padding-5">*  Aptitude</td>
                <td style="text-align: center;">A </td>
                <td></td>
                <td></td>
            </tr>

        </table>
        <br>

        <table>
            <tr>
                <td style="width: 50%;height: 25px;"><b>Principal: </b>Ms. Mini Jayan</td>
                <td style="width: 50%;"><b>Parent: </b></td>
            </tr>
            <tr>
                <td style="width: 50%;height: 25px;"><b>Class Teacher: </b>Sheela H V  </td>
                <td style="width: 50%;"><b>Date: </b>17-11-2018</td>
            </tr>
        </table>
    </div>
</div>
<img style="page-break-before: always;" src="/home/<USER>/silicon/assets/card-background.png" width="100%"/>
<div style="margin-top: -90%;">
<div class="header-bar2"><h3>CO-SCHOLASTIC ACHIEVEMENT RECORD</h3></div>
<div style="text-align: center;"><h3>2018 - 19</h3></div>
<div class="container2">

    <div class="row" style="font-size: 18px;">
        <table style="border:none;"><tr><td style="width: 75%;border:none;"><b>Name : </b>Aadi Narayanan P V </td><td style="width: 25%;border:none;text-align: right;"><b>Class :</b> 1 <b>  Section :</b> A</td></tr></table>
    </div>

    <div style="margin-top: 1%;">

        <table>

            <tr>
                <td colspan="6"><b>E. CO - CURRICULAR ACTIVITIES</b></td>
            </tr>

            <tr>
                <td><b>Games</b></td>
                <td style="text-align: center;"><b>Grade</b></td>
                <td><b>Western Music</b></td>
                <td style="text-align: center;"><b>Grade</b></td>
                <td><b>Indian Music</b></td>
                <td style="text-align: center;"><b>Grade</b></td>
            </tr>

            <tr>
                <td class="padding-5">*  Enthusiasm</td>
                <td style="text-align: center;">B</td>
                <td class="padding-5">*  Interest</td>
                <td style="text-align: center;">B</td>
                <td class="padding-5">*  Interest</td>
                <td style="text-align: center;">A</td>
            </tr>

            <tr>
                <td class="padding-5">*  Discipline</td>
                <td style="text-align: center;">C</td>
                <td class="padding-5">*  Rhythm</td>
                <td style="text-align: center;">C</td>
                <td class="padding-5">*  Rhythm</td>
                <td style="text-align: center;">A</td>
            </tr>

            <tr>
                <td class="padding-5">*  Team Spirit</td>
                <td style="text-align: center;">B</td>
                <td class="padding-5">*  Melody</td>
                <td style="text-align: center;">A</td>
                <td class="padding-5">*  Melody</td>
                <td style="text-align: center;">A</td>
            </tr>

            <tr>
                <td class="padding-5">*  Talent / Skill</td>
                <td style="text-align: center;">B</td>
                <td style="text-align: center;"></td>
                <td style="text-align: center;"></td>
                <td style="text-align: center;"></td>
                <td style="text-align: center;"></td>
            </tr>

            <tr>
                <td><b>Dance</b></td>
                <td style="text-align: center;"><b>Grade</b></td>
                <td><b>Art</b></td>
                <td style="text-align: center;"><b>Grade</b></td>
                <td><b>Craft</b></td>
                <td style="text-align: center;"><b>Grade</b></td>
            </tr>

            <tr>
                <td class="padding-5">*  Interest</td>
                <td style="text-align: center;">A</td>
                <td class="padding-5">*  Interest</td>
                <td style="text-align: center;">A</td>
                <td class="padding-5">*  Interest</td>
                <td style="text-align: center;">B</td>
            </tr>

            <tr>
                <td class="padding-5">*  Creativity</td>
                <td style="text-align: center;">A</td>
                <td class="padding-5">*  Creativity</td>
                <td style="text-align: center;">B</td>
                <td class="padding-5">*  Creativity</td>
                <td style="text-align: center;">B</td>
            </tr>

            <tr>
                <td class="padding-5">*  Skill</td>
                <td style="text-align: center;">A</td>
                <td class="padding-5">*  Skill</td>
                <td style="text-align: center;">B</td>
                <td class="padding-5">*  Skill</td>
                <td style="text-align: center;">B</td>
            </tr>

            <tr>
                <td><b>F. GENERAL KNOWLEDGE</b></td>
                <td style="text-align: center;">C</td>
                <td colspan="4"></td>
            </tr>

        </table>

        <table style="margin-top:5px;">

            <tr>
                <td colspan="4"><b>G. PERSONALITY DEVELOPMENT</b></td>
            </tr>

            <tr>
                <td><b>Personal and Social Traits</b></td>
                <td style="text-align: center;"><b>Grade</b></td>
                <td><b>Personal and Social Traits</b></td>
                <td style="text-align: center;"><b>Grade</b></td>
            </tr>

            <tr>
                <td class="padding-5">*  Courteousness</td>
                <td style="text-align: center;">A</td>
                <td class="padding-5">*  Initiative</td>
                <td style="text-align: center;">B</td>
            </tr>

            <tr>
                <td class="padding-5">*  Confidence</td>
                <td style="text-align: center;">A</td>
                <td class="padding-5">*  Sharing and Caring</td>
                <td style="text-align: center;">A</td>
            </tr>

            <tr>
                <td class="padding-5">*  Care of Belongings</td>
                <td style="text-align: center;">A</td>
                <td class="padding-5">*  Respect for others property</td>
                <td style="text-align: center;">A</td>
            </tr>

            <tr>
                <td class="padding-5">*  Neatness</td>
                <td style="text-align: center;">A</td>
                <td class="padding-5">*  Self - control</td>
                <td style="text-align: center;">A</td>
            </tr>

            <tr>
                <td class="padding-5">*  Regularity and Punctuality</td>
                <td style="text-align: center;">A</td>
                <td style="text-align: center;"></td>
                <td style="text-align: center;"></td>
            </tr>

            <tr>
                <td colspan="4"><b>H. HEALTH</b></td>
            </tr>

            <tr>
                <td style="width: 25%;"><b>Height</b> (Cms.)</td>
                <td style="width: 25%;text-align: center;">113</td>
                <td style="width: 25%;"><b>Weight</b> (Kgs.)</td>
                <td style="width: 25%;text-align: center;">22</td>
            </tr>
        </table>
        <br>

        <table>

            <tr>
                <td style="height: 100px;vertical-align: top;font-size: 16px;"><b>Remarks:</b><br> Soft- spoken and polite, Aadi is a friendly child. He enjoys music and outdoor activities.
Has to put in more efforts to achieve higher grades.
*Absent for MR1 English</td>
            </tr>

            <tr>
                <td><b>Specific Participation: </b></td>
            </tr>

            <tr>
                <td><b>Attendace:</b> 83.00 / 85</td>
            </tr>

        </table>
        <br>

        <table>

            <tr>
                <td style="width: 50%;height: 25px;"><b>Principal: </b>Ms. Mini Jayan</td>
                <td style="width: 50%;"><b>Parent: </b></td>
            </tr>

            <tr>
                <td style="width: 50%;height: 25px;"><b>Class Teacher: </b>Sheela H V  </td>
                <td style="width: 50%;"><b>Date: </b>17-11-2018</td>
            </tr>
            
        </table>

    </div>

</div></div></body></html>';
        $curl = curl_init();


        $postData = urlencode($data);

        $username = 'admin';
        $password = '1234';

        curl_setopt_array($curl, array(
            CURLOPT_URL => "http://localhost/silicon/index.php/api/Pdfgenerator/",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&data=".$postData,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
        echo "cURL Error #:" . $err;
        } else {
        echo $response;
        }
    }
}
