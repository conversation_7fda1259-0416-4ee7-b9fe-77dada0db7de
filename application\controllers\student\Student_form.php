<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Student_controller
 *
 * <AUTHOR>
 */
class Student_form extends CI_Controller {

    function __construct() {
        parent::__construct();
       
        $this->load->model('student/Student_Model');
        $this->config->load('form_elements');
        $this->load->library('filemanager');
    }

    // public function activatePage() {
    //     $iv = substr(hash('sha256', 'cbc790d23ef09d14'), 0, 16);
    //     $result = openssl_encrypt(17, 'aes-256-cbc', '846546546', 0, $iv);
    //     $data['id'] = base64_encode($result); 
    //     $this->load->view('student/update_form/update_info.php',$data);
    // }

    public function s3FileUpload($file) {
        if($file['tmp_name'] == '' || $file['name'] == '') {
          return ['status' => 'empty', 'file_name' => ''];
        }        
        return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'profile');
    }
    
    public function updateInfoPage($code) {
        //$code = $this->input->post('code');
        $iv = substr(hash('sha256', 'cbc790d23ef09d14'), 0, 16);
        $id = openssl_decrypt(base64_decode($code), 'aes-256-cbc', '846546546', 0, $iv);
        $states = $this->Student_Model->getStates($id);
        $data['completed'] = $states->completed;
        $data['disabled'] = $states->control_status;
        $stdData = $this->Student_Model->getstdId($id);
        //$studentData = $this->Student_Model->editStudentbyId($id);
        $data['dob'] = date('d-m-Y', strtotime($stdData->dob));
        $data['name'] = $stdData->stdName;
        $data['id'] = $id;
        $data['code'] = $code;
        //echo "<pre>"; print_r($data); die();
        $this->load->view('student/update_form/getDOB.php',$data);
    }
    
    public function updateForm($code) {
        $id = $this->input->post('stdId');
        $sentCode = $this->input->post('code');
        if($sentCode == $code) {
            $data['addressTypes'] = $this->settings->getSetting('address_types');
            $data['student_info'] = $this->Student_Model->getStudentandParentInfo($id);
            $data['tongues'] = $this->Student_Model->getMotherTongues($id);
            $studentData = $this->Student_Model->editStudentbyId($id);
            $data['stdAdd'] = $this->Student_Model->getAddressInfo($data['student_info']['student']->id, "student");
            $data['fatherAdd'] = $this->Student_Model->getAddressInfo($data['student_info']['Father']->id, "parent");
            $data['motherAdd'] = $this->Student_Model->getAddressInfo($data['student_info']['Mother']->id, "parent");
            $data['classSection'] = $this->Student_Model->getclassSectionName($studentData['student']->class_section_id);
            $data['healthData'] = $this->Student_Model->getHealthData($data['student_info']['student']->id);
            $data['edit_data'] = $studentData;
            // echo "<pre>"; print_r($data['edit_data']); die();

            $this->load->view('student/update_form/updateForm.php',$data);
        } else {
            $this->load->view('student/update_form/errorCode.php');
        }
        
    }

    private function _prepareStudentInput(&$input) {
        $return_data = [];
        foreach ($input as $k => $v) {
          $start_key = substr($k, 0, 2);
          if ($start_key == 'f_') {
            $key = str_replace("f_","",$k);
            $return_data['father'][$key] = $v;
          }
          elseif ($start_key == 'm_') {
            $key = str_replace("m_","",$k);
            $return_data['mother'][$key] = $v;
          }
          else {
             $return_data['student'][$k] = $v;
          }
        }
  
        //echo '<pre>';print_r($return_data);
  
        return $return_data;
  
    }

    private function _prepareStudentAddressInput(&$input) {

        $return_data = [];
        $present_flag = 0;
        $permanent_flag = 0;
  
        foreach ($input as $k => $v) {
  
          $start_key = substr($k, 0, 2);
          
          if ($start_key == 'a_') {
            if(substr($k, 0, 9) == 'a_present') {
              if(!empty($v)) 
                $present_flag = 1;
  
              $key = str_replace("a_present_","",$k);
              $return_data['address_present'][$key] = $v;
            }
            else {
              if(!empty($v)) 
                $permanent_flag = 1;
  
              $key = str_replace("a_permanent_","",$k);
              $return_data['address_permanent'][$key] = $v;
            }
          } 
        }
  
        $return_data['address_present']['flag'] = $present_flag;
        $return_data['address_permanent']['flag'] = $permanent_flag;
  
        //echo '<pre>';print_r($return_data);
  
        return $return_data;
      }
    
    public function updateStudentDetails(){
        $input_form = $_POST;
        $addressData = $this->_prepareStudentAddressInput($input_form);
        //echo "<pre>"; print_r($addressData); die();
    
        $this->db->trans_begin();
        if (isset($_FILES['student_photo'])) {
            $student_uid = $this->Student_Model->updateByParent($input_form['stdId'],$input_form['std_aadhar'],$input_form['smonther_tongue'],$this->s3FileUpload($_FILES['student_photo']));
        } else {
            $student_uid = $this->Student_Model->updateByParent($input_form['stdId'],$input_form['std_aadhar'],$input_form['smonther_tongue']);
        }

        //update student address
        if($addressData['address_present']['flag'] == 1)
            $this->Student_Model->addAddressInfo($addressData['address_present'], $input_form['stdId'], 'student', 0, 1);

        if($addressData['address_permanent']['flag'] == 1)
            $this->Student_Model->addAddressInfo($addressData['address_permanent'], $input_form['stdId'], 'student', 1, 1);
     
        if($student_uid == 0) {
            $this->db->trans_rollback();
            echo 0;
        } else {
            $status = (int)$this->Student_Model->setStatus($input_form['stdId'],'student');
            if($status) {
                $this->db->trans_commit();
                echo 1;
            }
            else {
                $this->db->trans_rollback();
                echo 0;
            }
        }
    }

    public function updateHealthDetails(){
        $input_form = $_POST;
        //echo "<pre>"; print_r($input_form); die();
    
        $student_uid = $this->Student_Model->updateHealthDetails($input_form);

        if($student_uid == 0) {
            $this->db->trans_rollback();
            echo 0;
        } else {
            $status = (int)$this->Student_Model->setStatus($input_form['stdId'],'health');
            if($status) {
                $this->db->trans_commit();
                echo 1;
            }
            else {
                $this->db->trans_rollback();
                echo 0;
            }
        }
    }

    public function updateParentDetails(){
        $input = $_POST;
        $trim = 'm';
        $id = 'motherId';
        $parent = 'mother';
        $uId = 'userMotherId';
        $photo = 'm_mother_photo';
        if(array_key_exists('fatherId', $input)) {
            $trim = 'f';
            $id = 'fatherId';
            $parent = 'father';
            $uId = 'userFatherId';
            $photo = 'f_father_photo';
        } 
        $addressArr = array();
        $input_form = array();
        foreach ($input as $key => $val){ 
            $input_form[ltrim($key, $trim.'_')] = $val;
        }

        if(array_key_exists('motherId', $input)) {
            $input_form['mobile_no'] = $input_form['obile_no'];
            unset($input_form['obile_no']);
            $input_form['motherId'] = $input_form['otherId'];
            unset($input_form['otherId']);
        } else {
            $input_form['fatherId'] = $input_form['atherId'];
            unset($input_form['atherId']);
        }
        
        $addressData = $this->_prepareStudentAddressInput($input_form);
        // echo "<pre>"; print_r($input_form); 
        // echo "<pre>"; print_r($addressData);
    
        $this->db->trans_begin();
        if (isset($_FILES[$photo])) {
            $student_uid = $this->Student_Model->updateParentDetails($input_form,$input_form[$id],$input_form[$uId],$this->s3FileUpload($_FILES[$photo]));
        } else {
            $student_uid = $this->Student_Model->updateParentDetails($input_form,$input_form[$id],$input_form[$uId]);
        }

        //update student address
        if($addressData['address_present']['flag'] == 1)
            $this->Student_Model->addAddressInfo($addressData['address_present'], $input_form[$id], $parent, 0, 1);

        if($addressData['address_permanent']['flag'] == 1)
            $this->Student_Model->addAddressInfo($addressData['address_permanent'], $input_form[$id], $parent, 1, 1);
     
        if($student_uid == 0) {
            $this->db->trans_rollback();
            echo 0;
        } else {
            $status = (int)$this->Student_Model->setStatus($input_form['stdId'], $parent);
            if($status) {
                $this->db->trans_commit();
                echo 1;
            }
            else {
                $this->db->trans_rollback();
                echo 0;
            }
        }
    }

    public function updateStatus(){
        $stdId = $_POST['stdId'];
        $status = (int)$this->Student_Model->setStatus($stdId, 'complete',1);
        echo $status;
    }

    public function completed(){
        $this->load->view('student/update_form/completed.php');
    }
}