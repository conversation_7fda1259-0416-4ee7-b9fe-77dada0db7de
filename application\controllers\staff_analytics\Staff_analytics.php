<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  17 August 2022
 *
 * Description: Controller for Classroom Dashboard Module.
 *
 * Requirements: PHP5 or above
 *
 */

class Staff_analytics extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('STAFF_360_DEGREE')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('staff/Attendance_model', 'staff_attendance');
        $this->load->model('staff/Staff_analytics_model', 'staff_analytics_model');
        $this->load->library('filemanager');
        // $this->load->library('filemanager');
    }

    public function index($id = '')
    {
        // echo "hello"; die();
        $data['staff_id'] = $id;
        $data['acad_year'] = $this->staff_analytics_model->get_acad_year_name_by_id();
        $data['staff_list'] = $this->staff_analytics_model->get_saff_data_analytics();
        $custom = $this->settings->getSetting('staff_custom_fields');
        // $array = array('key1'=>'value', 'key2' => 'value');
        // $array1 = array('key3' => 'value', 'key4' => 'value');
        $tempKey = [];
        if (!empty($custom)) {
            $data['chunkData'] = array_chunk($custom, 2);
            foreach ($custom as $key => $val) {
                $tempKey[$val] = $key;
            }
        }

        $data['custom'] = $tempKey;
        
        if ($this->mobile_detect->isMobile()) {
            $data['staff_id'] = $id;
            $data['main_content'] = 'staff_analytics/mobile_index';
        } else {
            $data['main_content'] = 'staff_analytics/index';
        }

        $this->load->view('inc/template', $data);
    }
    public function view_mobile_wise_tab($tabname, $selected_staff_id)
    {
        $display_fields = $this->staff_analytics_model->get_config_enabled_fields();
        if ($tabname == 'profile') {
            $result = $this->staff_analytics_model->get_staff_detils($selected_staff_id);
            $designation = '';
            $department = '';
            $previous_designation_name = '';
            if ($result->designation != '-') {
                $designation = $this->staff_analytics_model->get_staff_designation_name($result->designation);
            } else {
                $designation = $result->designation;
            }

            if ($result->department != '-') {
                $department = $this->staff_analytics_model->get_staff_department_name($result->department);
            } else {
                $department = $result->department;
            }

            if ($result->previous_designation_name != '-') {
                $previous_designation_name = $this->staff_analytics_model->get_staff_designation_name($result->previous_designation_name);
            } else {
                $previous_designation_name = $result->previous_designation_name;
            }

            $result->designation = $designation;
            $result->department = $department;
            $result->previous_designation_name = $previous_designation_name;

            $decoded_fields = json_decode($display_fields[0]->value, true);
            $data['display_fields'] = is_array($decoded_fields) ? $decoded_fields : [];
            $data['selected_staff_id'] = $selected_staff_id;
            $data['result'] = ($result);
            $data['main_content'] = 'staff_analytics/profile_mobile_view';
        } elseif ($tabname == 'attendance') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['staff_attendance'] = $this->staff_analytics_model->get_indv_staff_attendance($selected_staff_id);
            $data['main_content'] = 'staff_analytics/attendace_mobile_view';
        } elseif ($tabname == 'leave_records') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['staff_leaves'] = $this->staff_analytics_model->get_staff_leave_details($selected_staff_id);
            $data['main_content'] = 'staff_analytics/leave_records';
        } elseif ($tabname == 'assigned_tasks') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['staff_task'] = $this->staff_analytics_model->get_task_list($selected_staff_id);
            $data['main_content'] = 'staff_analytics/task_mobile';

        } elseif ($tabname == 'qual') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['staff_qualification'] = $this->staff_analytics_model->get_qualifications($selected_staff_id);
            $data['main_content'] = 'staff_analytics/qualification_mobile';
        } elseif ($tabname == 'awards') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['staff_awards'] = $this->staff_analytics_model->get_awards($selected_staff_id);
            $data['main_content'] = 'staff_analytics/awards_mobile';
        } elseif ($tabname == 'experience') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['staff_experiene'] = $this->staff_analytics_model->get_staff_experience($selected_staff_id);
            $data['main_content'] = 'staff_analytics/experience_mobile';
        } elseif ($tabname == 'docs') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['docs'] = $this->staff_analytics_model->get_staff_documents($selected_staff_id);
            $data['main_content'] = 'staff_analytics/docs_mobile';
        } elseif ($tabname == 'trainings') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['trainings'] = $this->staff_analytics_model->get_staff_workshop_training($selected_staff_id);
            $data['main_content'] = 'staff_analytics/moble_training';
        } elseif ($tabname == 'interests') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['interests'] = $this->staff_analytics_model->get_staff_interests($selected_staff_id);
            $data['main_content'] = 'staff_analytics/mobile_interests';

        } elseif ($tabname == 'citations') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['citations'] = $this->staff_analytics_model->get_staff_citations($selected_staff_id);
            $data['main_content'] = 'staff_analytics/mobile_citations';
        } elseif ($tabname == 'circular') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['result'] = $this->staff_analytics_model->get_staff_circular($selected_staff_id);
            $data['main_content'] = 'staff_analytics/mobile_circular';
        } elseif ($tabname == 'message') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['result'] = $this->staff_analytics_model->get_staff_message($selected_staff_id);
            $data['main_content'] = 'staff_analytics/mobile_messages';
        } elseif ($tabname == 'initiative') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['initiative'] = $this->staff_analytics_model->get_staff_initiative($selected_staff_id);
            $data['main_content'] = 'staff_analytics/mobile_initiative';
        } elseif ($tabname === 'infirmary') {
            $data['selected_staff_id'] = $selected_staff_id;
            $data['infirmary'] = $this->staff_analytics_model->get_infrimary_details($selected_staff_id);
            $data['main_content'] = 'staff_analytics/mobile_infirmary';
        } elseif ($tabname === 'payroll_details') {
            $data['selected_staff_id'] = $selected_staff_id;
            $result = $this->staff_analytics_model->get_payroll_details($selected_staff_id);
            if(!empty($result)){
                // $display_fields = $this->staff_analytics_model->get_config_enabled_fields();

                $enabled_fields = json_decode($display_fields[0]->value, true);

                if (!is_array($enabled_fields)) {
                    $enabled_fields = [];
                }

                foreach ($result as $property => $value) {
                    if (!in_array($property, $enabled_fields)) {
                        unset($result->$property);
                    }
                }
            }
            $data['payroll_details'] = $result;
            $data['main_content'] = 'staff_analytics/payroll_details';
        }
        $this->load->view('inc/template', $data);

    }

    public function get_staff_basic_data()
    {
        $selected_staff_id = isset($_POST['selected_staff_id']) ? $_POST['selected_staff_id'] : '';
        $result = $this->staff_analytics_model->get_staff_detils($selected_staff_id);
        $designation = '';
        $department = '';
        $previous_designation_name = '';
        if ($result->designation != 'Not Assigned') {
            $designation = $this->staff_analytics_model->get_staff_designation_name($result->designation);
        } else {
            $designation = $result->designation;
        }

        if ($result->department != 'Not Assigned') {
            $department = $this->staff_analytics_model->get_staff_department_name($result->department);
        } else {
            $department = $result->department;
        }

        if ($result->previous_designation_name != '-') {
            $previous_designation_name = $this->staff_analytics_model->get_staff_designation_name($result->previous_designation_name);
        } else {
            $previous_designation_name = $result->previous_designation_name;
        }

        $result->designation = $designation;
        $result->department = $department;
        $result->previous_designation_name = $previous_designation_name;
        
        $default_personal = ['salutation','marital_status', 'gender', 'dob', 'contact_number', 'email'];
        $personal_details = ['short_name', 'passport_number', 'passport_date_of_issue', 'passport_expiry_date', 'passport_place_of_issue', 'visa_details', 'personal_mail_id', 'nationality', 'religion', 'category', 'caste', 'alternative_number', 'emergency_info', 'aadhar_number', 'voter_id'];

        if ($result->marital_status == 'Single') {
            $default_family = ['father_name', 'father_contact_no', 'mother_name'];
            $family_details = ['father_occupation', 'father_dob', 'father_is_dependent', 'mother_dob', 'mother_is_dependent'];
        } else {
            $default_family = ['father_name', 'father_contact_no', 'mother_name', 'spouse_name', 'spouse_contact_no', 'child1_name', 'child2_name', 'child3_name'];
            $family_details = ['father_occupation', 'father_dob', 'father_is_dependent', 'include_father_insurance', 'mother_dob', 'mother_is_dependent', 'include_mother_insurance', 'spouse_occupation', 'spouse_dob', 'spouse_gender', 'spouse_is_dependent', 'include_spouse_insurance','child1_dob', 'child1_gender', 'child1_is_dependent', 'include_child1_insurance', 'child2_dob', 'child2_gender', 'child2_is_dependent', 'include_child2_insurance', 'child3_dob', 'child3_gender', 'child3_is_dependent', 'include_child3_insurance'];
        }

        $default_professional = ['staff_type', 'department', 'designation', 'employee_code', 'has_completed_any_bed', 'joining_date'];
        $professional_details = ['qualification', 'subject_specialization', 'staff_reference_code', 'math_high_grade', 'english_high_grade', 'social_high_grade', 'trained_to_teach', 'nature_of_appointment', 'has_completed_any_pgdei', 'previous_designation_name', 'total_education_experience', 'total_experience', 'appointed_subject', 'classes_taught', 'main_sub_taught', 'add_sub_taught', 'staff_code', 'is_primary_instance', 'staff_house'];

        $default_health = ['blood_group'];
        $health_details = ['height', 'weight', 'allergies', 'medical_issues', 'identification_mark', 'person_with_disability'];

        $display_fields = $this->staff_analytics_model->get_config_enabled_fields();
        // $decoded_fields = array_map(function ($enabled) {
        //     return json_decode($enabled->value, true);
        // }, $display_fields);

        // $data['display_fields'] = $default_enabled;

        $filter_details = function ($details, $default) use ($display_fields) {
            $filtered_details = [];
            foreach ($display_fields as $enabled) {
                $enabled_fields = json_decode($enabled->value, true);
                foreach ($details as $detail) {
                    if (in_array($detail, $enabled_fields)) {
                        $filtered_details[] = $detail;
                    }
                }
            }
            return array_merge($default, $filtered_details);
        };

        $data['personal_details'] = $filter_details($personal_details, $default_personal);
        $data['family_details'] = $filter_details($family_details, $default_family);
        $data['professional_details'] = $filter_details($professional_details, $default_professional);
        $data['health_details'] = $filter_details($health_details, $default_health);
        $data['staff_details'] = $result;

        echo json_encode($data);

        // echo json_encode($result);
    }

    public function get_indv_staff_attendance()
    {
        $selected_staff_id = $_POST['selected_staff_id'];
        $staff = $this->staff_analytics_model->get_indv_staff_attendance($selected_staff_id);
        echo json_encode($staff);
    }

    // nov 19 interests
    public function get_staff_interests()
    {
        $selected_staff_id = $_POST['selected_staff_id'];

        $staff = $this->staff_analytics_model->get_staff_interests($selected_staff_id);
        echo json_encode($staff);
    }
    // nov 19 citations
    public function get_staff_citations()
    {
        $selected_staff_id = $_POST['selected_staff_id'];

        $staff = $this->staff_analytics_model->get_staff_citations($selected_staff_id);
        echo json_encode($staff);
    }

    public function get_staff_leave_details()
    {
        $staff_id = $_POST['selected_staff_id'];
        $staff_leaves = $this->staff_analytics_model->get_staff_leave_details($staff_id);
        echo json_encode($staff_leaves);
    }

    public function balance_report_summary()
    {
        $staff_id = $_POST['selected_staff_id'];
        $balance = $this->staff_analytics_model->get_leave_balance($staff_id);
        echo json_encode($balance);
    }

    public function get_task_list()
    {
        $staff_id = $_POST['selected_staff_id'];
        $task = $this->staff_analytics_model->get_task_list($staff_id);
        echo json_encode($task);
    }
    public function get_qualification()
    {
        $staff_id = $_POST['selected_staff_id'];
        $result = $this->staff_analytics_model->get_qualifications($staff_id);
        echo json_encode($result);
    }

    public function get_awards()
    {
        $staff_id = $_POST['selected_staff_id'];
        $result = $this->staff_analytics_model->get_awards($staff_id);
        echo json_encode($result);
    }
    public function get_staff_experience()
    {
        $staff_id = $_POST['selected_staff_id'];
        $result = $this->staff_analytics_model->get_staff_experience($staff_id);
        echo json_encode($result);
    }
    public function get_staff_documents()
    {
        $staff_id = $_POST['selected_staff_id'];
        $result = $this->staff_analytics_model->get_staff_documents($staff_id);
        echo json_encode($result);
    }
    public function get_staff_workshop_data()
    {
        $staff_id = $_POST['selected_staff_id'];
        $result = $this->staff_analytics_model->get_staff_workshop_training($staff_id);
        echo json_encode($result);
    }
    public function get_staff_circular()
    {
        $staff_id = $_POST['selected_staff_id'];
        $result = $this->staff_analytics_model->get_staff_circular($staff_id);
        echo json_encode($result);
    }
    public function get_staff_message()
    {
        $staff_id = $_POST['selected_staff_id'];
        $result = $this->staff_analytics_model->get_staff_message($staff_id);
        echo json_encode($result);
    }
    public function get_staff_initiative()
    {

        $staff_id = $_POST['selected_staff_id'];
        $result = $this->staff_analytics_model->get_staff_initiative($staff_id);
        echo json_encode($result);
    }
    public function get_infirmary_details()
    {
        $staff_id = $_POST['selected_staff_id'];
        $result = $this->staff_analytics_model->get_infrimary_details($staff_id);
        echo json_encode($result);
    }

    public function get_payroll_details()
    {
        $staff_id = $_POST['selected_staff_id'];
        $result = $this->staff_analytics_model->get_payroll_details($staff_id);

        if(!empty($result)){
            $display_fields = $this->staff_analytics_model->get_config_enabled_fields();

            $enabled_fields = json_decode($display_fields[0]->value, true);

            if (!is_array($enabled_fields)) {
                $enabled_fields = [];
            }

            foreach ($result as $property => $value) {
                if (!in_array($property, $enabled_fields)) {
                    unset($result->$property);
                }
            }
        }
        echo json_encode($result);
    }

    public function staff_fields_enabled_for_school()
    {
        $display_fields = $this->staff_analytics_model->get_config_enabled_fields();
        // echo "<pre>"; print_r($display_fields);die();
        echo json_encode($display_fields);
    }
}