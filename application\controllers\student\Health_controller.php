<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Student_controller
 *
 * <AUTHOR>
 */
class Health_controller extends CI_Controller {

  public $columnList = [
    [
      'displayName'=>'Blood Group',
      'columnNameWithTable'=>'sh.blood_group',
      'columnName'=>'blood_group',
      'varName'=>'blood_group',
      'table'=>'student_health',
      'index'=>'1',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Physical Disability',
      // 'columnNameWithTable'=>'sh.physical_disability',
      'columnNameWithTable'=>'if(sh.physical_disability=0,\'No\',\'Yes\') as physical_disability',
      'columnName'=>'physical_disability',
      'varName'=>'physical_disability',
      'table'=>'student_health',
      'index'=>'2',
      'displayType'=>'text',
      'dataType'=>'string' 
    ],
    
    [
      'displayName'=>'Physical Disability Reason',
      'columnNameWithTable'=>'sh.physical_disability_reason',
      'columnName'=>'physical_disability_reason',
      'varName'=>'physical_disability_reason',
      'table'=>'student_health',
      'index'=>'3',
      'displayType'=>'text',
      'dataType'=>'string' 
    ],
    [
      'displayName'=>'Learning Disability',
      'columnNameWithTable'=>'if(sh.learning_disability=0,\'No\',\'Yes\') as learning_disability',
      'columnName'=>'learning_disability',
      'varName'=>'learning_disability',
      'table'=>'student_health',
      'index'=>'4',
      'displayType'=>'text',
      'dataType'=>'string' 
    ],
    [
      'displayName'=>'Learning Disability Reason',
      'columnNameWithTable'=>'sh.learning_disability_reason',
      'columnName'=>'learning_disability_reason',
      'varName'=>'learning_disability_reason',
      'table'=>'student_health',
      'index'=>'5',
      'displayType'=>'text',
      'dataType'=>'string' 
    ],
    [
      'displayName'=>'allergy',
      'columnNameWithTable' => 'sh.allergy',
      'columnName'=>'allergy',
      'varName'=>'allergy',
      'table'=>'student_health',
      'index'=>'6',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
   
    [
      'displayName'=>'Family History',
      'columnNameWithTable' => 'sh.family_history',
      'columnName'=>'family_history',
      'varName'=>'family_history',
      'table'=>'student_health',
      'index'=>'7',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Anaemia',
      'columnNameWithTable' => 'CASE WHEN sh.anaemia = 0 THEN \'Absent\' WHEN sh.anaemia = 1 THEN \'Mild\' WHEN sh.anaemia =  2 THEN \'Moderate\' WHEN sh.anaemia = 3 THEN \'Severe\' END as  anaemia',
      'columnName'=>'anaemia',
      'varName'=>'anaemia',
      'table'=>'student_health',
      'index'=>'8',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Fit To Participate',
      'columnNameWithTable'=>'CASE WHEN sh.fit_to_participate = 0 THEN \'Fit\' WHEN sh.fit_to_participate = 1 THEN \'Fit with precautions\' WHEN sh.fit_to_participate =  2 THEN \'Not fit\' END as fit_to_participate',
      'columnName'=>'fit_to_participate',
      'varName'=>'fit_to_participate',
      'table'=>'student_health',
      'index'=>'9',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Height',
      'columnNameWithTable'=>'sh.height',
      'columnName'=>'height',
      'varName'=>'height',
      'table'=>'student_health',
      'index'=>'10',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Weight',
      'columnNameWithTable'=>'sh.weight',
      'columnName'=>'weight',
      'varName'=>'weight',
      'table'=>'student_health',
      'index'=>'11',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Hair',
      'columnNameWithTable'=>'sh.hair',
      'columnName'=>'hair',
      'varName'=>'hair',
      'table'=>'student_health',
      'index'=>'12',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Skin',
      'columnNameWithTable'=>'sh.skin',
      'columnName'=>'skin',
      'varName'=>'skin',
      'table'=>'student_health',
      'index'=>'13',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Ear',
      'columnNameWithTable'=>'sh.ear',
      'columnName'=>'ear',
      'varName'=>'ear',
      'table'=>'student_health',
      'index'=>'14',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Nose',
      'columnNameWithTable'=>'sh.nose',
      'columnName'=>'nose',
      'varName'=>'nose',
      'table'=>'student_health',
      'index'=>'15',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Throat',
      'columnNameWithTable'=>'sh.throat',
      'columnName'=>'throat',
      'varName'=>'throat',
      'table'=>'student_health',
      'index'=>'16',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Neck',
      'columnNameWithTable'=>'sh.neck',
      'columnName'=>'neck',
      'varName'=>'neck',
      'table'=>'student_health',
      'index'=>'17',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Respiratory',
      'columnNameWithTable'=>'sh.respiratory',
      'columnName'=>'respiratory',
      'varName'=>'respiratory',
      'table'=>'student_health',
      'index'=>'18',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Cardio Vascular',
      'columnNameWithTable'=>'sh.cardio_vascular',
      'columnName'=>'cardio_vascular',
      'varName'=>'cardio_vascular',
      'table'=>'student_health',
      'index'=>'19',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Abdomen',
      'columnNameWithTable'=>'sh.abdomen',
      'columnName'=>'abdomen',
      'varName'=>'abdomen',
      'table'=>'student_health',
      'index'=>'20',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Nervous System',
      'columnNameWithTable'=>'sh.nervous_system',
      'columnName'=>'nervous_system',
      'varName'=>'nervous_system',
      'table'=>'student_health',
      'index'=>'21',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Left Eye',
      'columnNameWithTable'=>'sh.left_eye',
      'columnName'=>'left_eye',
      'varName'=>'left_eye',
      'table'=>'student_health',
      'index'=>'22',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Right Eye',
      'columnNameWithTable'=>'sh.right_eye',
      'columnName'=>'right_eye',
      'varName'=>'right_eye',
      'table'=>'student_health',
      'index'=>'23',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Extra oral',
      'columnNameWithTable'=>'sh.extra_oral',
      'columnName'=>'extra_oral',
      'varName'=>'extra_oral',
      'table'=>'student_health',
      'index'=>'24',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Bad Breath',
      'columnNameWithTable'=>'sh.bad_breath',
      'columnName'=>'bad_breath',
      'varName'=>'bad_breath',
      'table'=>'student_health',
      'index'=>'25',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Tooth Cavity',
      'columnNameWithTable'=>'sh.tooth_cavity',
      'columnName'=>'tooth_cavity',
      'varName'=>'tooth_cavity',
      'table'=>'student_health',
      'index'=>'26',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Plaque',
      'columnNameWithTable'=>'sh.plaque',
      'columnName'=>'plaque',
      'varName'=>'plaque',
      'table'=>'student_health',
      'index'=>'27',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Gum Inflamation',
      'columnNameWithTable'=>'sh.gum_inflamation',
      'columnName'=>'gum_inflamation',
      'varName'=>'gum_inflamation',
      'table'=>'student_health',
      'index'=>'28',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Stains',
      'columnNameWithTable'=>'sh.stains',
      'columnName'=>'stains',
      'varName'=>'stains',
      'table'=>'student_health',
      'index'=>'29',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Gum Bleeding',
      'columnNameWithTable'=>'sh.gum_bleeding',
      'columnName'=>'gum_bleeding',
      'varName'=>'gum_bleeding',
      'table'=>'student_health',
      'index'=>'30',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Soft Tissue',
      'columnNameWithTable'=>'sh.soft_tissue',
      'columnName'=>'soft_tissue',
      'varName'=>'soft_tissue',
      'table'=>'student_health',
      'index'=>'31',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Covid',
      'columnNameWithTable'=>'CASE WHEN sh.covid = 0 THEN \'Vaccinated\' WHEN sh.covid = 1 THEN \'Not Vaccinated\' WHEN sh.covid =  2 THEN \'Recovered\' END as  covid',
      'columnName'=>'covid',
      'varName'=>'covid',
      'table'=>'student_health',
      'index'=>'32',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Vitamin B12',
      'columnNameWithTable'=>'sh.vitaminb12',
      'columnName'=>'vitaminb12',
      'varName'=>'vitaminb12',
      'table'=>'student_health',
      'index'=>'33',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Vitamin D',
      'columnNameWithTable'=>'sh.vitamind',
      'columnName'=>'vitamind',
      'varName'=>'vitamind',
      'table'=>'student_health',
      'index'=>'34',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Iron',
      'columnNameWithTable'=>'sh.iron',
      'columnName'=>'iron',
      'varName'=>'iron',
      'table'=>'student_health',
      'index'=>'35',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Calcium',
      'columnNameWithTable'=>'sh.calcium',
      'columnName'=>'calcium',
      'varName'=>'calcium',
      'table'=>'student_health',
      'index'=>'36',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Renal Issues',
      'columnNameWithTable'=>'sh.renal_issues',
      'columnName'=>'renal_issues',
      'varName'=>'renal_issues',
      'table'=>'student_health',
      'index'=>'37',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Skin Issues',
      'columnNameWithTable'=>'sh.skin_issues',
      'columnName'=>'skin_issues',
      'varName'=>'skin_issues',
      'table'=>'student_health',
      'index'=>'38',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Is Daily Medication',
      'columnNameWithTable'=>'sh.daily_medication',
      'columnName'=>'daily_medication',
      'varName'=>'daily_medication',
      'table'=>'student_health',
      'index'=>'39',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Mental Health Issues',
      'columnNameWithTable'=>'sh.mental_issues',
      'columnName'=>'mental_issues',
      'varName'=>'mental_issues',
      'table'=>'student_health',
      'index'=>'40',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Vaccines',
      'columnNameWithTable'=>'sh.vaccines',
      'columnName'=>'vaccines',
      'varName'=>'vaccines',
      'table'=>'student_health',
      'index'=>'41',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Head injury',
      'columnNameWithTable'=>'sh.head_injury',
      'columnName'=>'head_injury',
      'varName'=>'head_injury',
      'table'=>'student_health',
      'index'=>'42',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Ear impartement',
      'columnNameWithTable'=>'sh.ear_impartement',
      'columnName'=>'ear_impartement',
      'varName'=>'ear_impartement',
      'table'=>'student_health',
      'index'=>'43',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Difficulty in breathing',
      'columnNameWithTable'=>'sh.difficulty_in_breathing',
      'columnName'=>'difficulty_in_breathing',
      'varName'=>'difficulty_in_breathing',
      'table'=>'student_health',
      'index'=>'44',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Child has join pain',
      'columnNameWithTable'=>'sh.child_has_join_pain',
      'columnName'=>'child_has_join_pain',
      'varName'=>'child_has_join_pain',
      'table'=>'student_health',
      'index'=>'45',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Child past hsitory fracture',
      'columnNameWithTable'=>'sh.child_past_hsitory_fracture',
      'columnName'=>'child_past_hsitory_fracture',
      'varName'=>'child_past_hsitory_fracture',
      'table'=>'student_health',
      'index'=>'46',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Fracture',
      'columnNameWithTable'=>'sh.fracture',
      'columnName'=>'fracture',
      'varName'=>'fracture',
      'table'=>'student_health',
      'index'=>'47',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Vegetarian/Non-Vegetarian',
      'columnNameWithTable'=>'sh.foodytpe',
      'columnName'=>'foodytpe',
      'varName'=>'foodytpe',
      'table'=>'student_health',
      'index'=>'48',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Child Nervous Breakdown',
      'columnNameWithTable'=>'sh.child_nervous_breakdown',
      'columnName'=>'child_nervous_breakdown',
      'varName'=>'child_nervous_breakdown',
      'table'=>'student_health',
      'index'=>'49',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Child color blindness',
      'columnNameWithTable'=>'sh.child_color_blindness',
      'columnName'=>'child_color_blindness',
      'varName'=>'child_color_blindness',
      'table'=>'student_health',
      'index'=>'50',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Child Diabities',
      'columnNameWithTable'=>'sh.child_diabities',
      'columnName'=>'child_diabities',
      'varName'=>'child_diabities',
      'table'=>'student_health',
      'index'=>'51',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Congenital heart disease',
      'columnNameWithTable'=>'sh.congenital_heart_disease',
      'columnName'=>'congenital_heart_disease',
      'varName'=>'congenital_heart_disease',
      'table'=>'student_health',
      'index'=>'52',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'More than month disease',
      'columnNameWithTable'=>'sh.more_than_month_disease',
      'columnName'=>'more_than_month_disease',
      'varName'=>'more_than_month_disease',
      'table'=>'student_health',
      'index'=>'53',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Medicine name for month',
      'columnNameWithTable'=>'sh.medicine_name_for_month',
      'columnName'=>'medicine_name_for_month',
      'varName'=>'medicine_name_for_month',
      'table'=>'student_health',
      'index'=>'54',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Any other medical treatment',
      'columnNameWithTable'=>'sh.any_other_medical_treatment',
      'columnName'=>'any_other_medical_treatment',
      'varName'=>'any_other_medical_treatment',
      'table'=>'student_health',
      'index'=>'55',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Any other medical treatment',
      'columnNameWithTable'=>'sh.any_other_medical_treatment',
      'columnName'=>'any_other_medical_treatment',
      'varName'=>'any_other_medical_treatment',
      'table'=>'student_health',
      'index'=>'56',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Father Blood Group',
      'columnNameWithTable'=>'sh.father_bld_group',
      'columnName'=>'father_bld_group',
      'varName'=>'father_bld_group',
      'table'=>'student_health',
      'index'=>'57',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Mother Blood Group',
      'columnNameWithTable'=>'sh.mother_bld_group',
      'columnName'=>'mother_bld_group',
      'varName'=>'mother_bld_group',
      'table'=>'student_health',
      'index'=>'58',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Intra oral',
      'columnNameWithTable'=>'sh.intra_oral',
      'columnName'=>'intra_oral',
      'varName'=>'intra_oral',
      'table'=>'student_health',
      'index'=>'59',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Child past history sea',
      'columnNameWithTable'=>'sh.child_past_hsitory_sea',
      'columnName'=>'child_past_hsitory_sea',
      'varName'=>'child_past_hsitory_sea',
      'table'=>'student_health',
      'index'=>'60',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Running Height',
      'columnNameWithTable'=>'shw.height_in_cm',
      'columnName'=>'height_in_cm',
      'varName'=>'height_in_cm',
      'table'=>'student_height_weight',
      'index'=>'61',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Running Weight',
      'columnNameWithTable'=>'shw.weight_in_kg',
      'columnName'=>'weight_in_kg',
      'varName'=>'weight_in_kg',
      'table'=>'student_height_weight',
      'index'=>'62',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
  ];

  public $mandatoryFields = [

    [
      'displayName'=>'Name',
      'columnNameWithTable'=>'concat(ifnull(sa.first_name,""), " ", ifnull(sa.last_name,""))',
      'columnName'=>'first_name',
      'varName'=>'sFirstName',
      'table'=>'student_admission',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Date',
      'columnNameWithTable'=>'created_on',
      'columnName'=>'created_on',
      'varName'=>'created_on',
      'table'=>'student_health',
      'displayType'=>'text',
      'dataType'=>'string'
    ]
  ];


  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_MASTER')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT.BASIC_VIEW')) {
      redirect('dashboard', 'refresh');
    }
    $stdcsId = $this->uri->segment(5);
    $permitHealthCRUD = $this->__resolvePermission('STUDENT.HEALTH_CRUD', 'STUDENT.HEALTH_CRUD_IF_CT', $stdcsId) || $this->authorization->isAuthorized('STUDENT.FULL_INFO_CRUD');
    if (!$permitHealthCRUD) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('student/Health_model', 'healthModel');
    $this->load->model('student/Student_Model', 'stdModel');
    $this->load->model('class_section');
    $this->config->load('form_elements');
  }

  public function addStudentHealth() {
    $data['student_uid'] = $this->uri->segment(4);
    // $stdcsId = $this->uri->segment(5);
    $data['stdData'] = $this->stdModel->getStdDataById($data['student_uid']);
    // $data['health_info'] = $this->healthModel->getStudentHealthInfo($data['student_uid']);
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
    $data['disabled_fields'] = $this->healthModel->get_health_disabled_fields();
    $required_fields = $this->healthModel->get_health_required_fields();
    $data['required_fields'] = $this->__construct_name_wise_health_required($required_fields);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/health_mobile';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student/student_registration/more/health_mobile';
    }else{
      $data['main_content'] = 'student/student_registration/more/health';     	
    }
    $this->load->view('inc/template', $data);
  }

  private function __construct_name_wise_health_required($requiredData){
        $fields = $this->db->list_fields('student_health');
        $rData = [];
        foreach ($fields as $key => $val) {
            if (in_array($val, $requiredData)) {
                $rData[$val] = array('font' =>'TRUE', 'required' =>'required');
            }else{
                $rData[$val] = array('font' =>'', 'required' =>'');
            }
        }
        return $rData;
    }

  public function save_health_record() { 
    $input_form = $this->input->post();
    $input_form['academic_year_id'] = $this->acad_year->getAcadYearId();
    $result = $this->healthModel->addStudentHealthInfo($input_form);
    $classId = $this->input->post('classId');

    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Submitted');
    } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
    }
    // $data['stdData'] = $this->stdModel->getStdDataById($input_form['student_id']);
    redirect('student/student_controller/addMoreStudentInfo/' . $input_form['student_uid']);
    // echo "<pre>"; print_r($input_form); die();

    // $this->session->set_flashdata('flashSuccess', 'Saved');
  }

  // public function edit_health_record($student_id, $health_record_id) { 
  //   $data['student_uid'] = $student_id;
  //   $data['health_record_id'] = $health_record_id;
  //   // $input_form = $this->input->post();
  //   // $input_form['academic_year_id'] = $this->acad_year->getAcadYearId();
  //   $data['health_info'] = $this->healthModel->getHealthInfo($health_record_id);
  //   $data['disabled_fields'] = $this->healthModel->get_health_disabled_fields();
  //   $data['stdData'] = $this->stdModel->getStdDataById($student_id);
  //   // echo "<pre>"; print_r($data['health_info']); die();
  //   $data['main_content'] = 'student/student_registration/more/health_edit';
  //   $this->load->view('inc/template', $data);
  // }


  // public function previewStudentHealth() { 
  //   $input_form = $this->input->post();    
  //   $input_form['academic_year_id'] = $this->acad_year->getAcadYearId();
  //   $data['health_info'] = $this->healthModel->addStudentHealthInfo($input_form);
  //   $data['stdData'] = $this->stdModel->getStdDataById($data['health_info']->student_id);
  //   $data['disabled_fields'] = $this->healthModel->get_health_disabled_fields();
  //   // echo "<pre>"; print_r($data['health_info']); die();
  //   $data['main_content'] = 'student/student_registration/more/health_preview';
  //   $this->load->view('inc/template', $data);
  // }

  private function __resolvePermission($fullPerm, $ctPerm, $csId) {
    if ($this->authorization->isAuthorized($fullPerm)) {
      return 1;  
    } else if ($this->authorization->isAuthorized($ctPerm)){
      if ($this->Student_Model->getSectionIdIfClassTeacher() == $csId) {
        return 1;
      } else {
        return 0;
      }
    } else {
      return 0;
    }
  }
  public function health_report($student_id, $sc_id){
    $data['stdData'] = $this->stdModel->getStdDataById($student_id);
    $data['health_info'] = $this->healthModel->getStudentHealthInfo($student_id);
    $data['health_disabled_fields']= $this->settings->getSetting('health_show_disabled_fields');
    // echo "<pre>"; print_r($data['health_info']); die();
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/health_report_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student/student_registration/more/health_report_mobile';
    }else{
      $data['main_content'] = 'student/student_registration/more/health_report';   	
    }
    $this->load->view('inc/template', $data);
  }

  public function health_class_report()
  {
    

    $data['supportedColumns'] = [ 'blood_group', 'physical_disability', 'learning_disability', 'physical_disability_reason', 'learning_disability_reason', 
    'created_on', 'modified_on',  'allergy', 'family_history', 'anaemia', 'fit_to_participate', 'height', 'weight', 'hair', 'skin', 'ear', 'nose', 'throat', 'neck', 'respiratory', 
    'cardio_vascular', 'abdomen', 'nervous_system', 'left_eye', 'right_eye', 'extra_oral', 'bad_breath', 'tooth_cavity', 'plaque', 'gum_inflamation', 'stains', 'gum_bleeding', 
    'soft_tissue', 'academic_year_id', 'covid', 'vitaminb12', 'vitamind', 'iron', 'calcium'    ];
    

    $data['classList'] = $this->stdModel->getclass();

    $data['columnList_json'] = json_encode($this->columnList);
    $health_disabled_fields= $this->settings->getSetting('health_show_disabled_fields');
    $column_List = $this->columnList;

    $enable_fields = [];
    if(!empty($health_disabled_fields)){
      foreach($column_List as $key => $val){
        if(!in_array($val['columnName'] , $health_disabled_fields)){
          $enable_fields[] = $val; 
        }
      }
    }
    $data['columnList'] = $enable_fields;
    // echo '<pre>'; print_r($data['columnList']); die();

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/health_class_report_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student/student_registration/more/health_class_report_mobile';
    }else{
      $data['main_content'] = 'student/student_registration/more/health_class_report';
    }

    $this->load->view('inc/template', $data);
  }


  public function getStudentList() {
    $sectionId = $_POST['sectionId'];
    $classId = $_POST['classId'];
    
    
    $student_ids = $this->healthModel->getStudentList($classId,$sectionId);
    // echo '<pre>'; print_r($data); die();

    $std_ids = array_chunk($student_ids, 50);
    echo json_encode($std_ids);
  }


  public function getStdReport() {
    $columnListJSON = $_POST['columnListJSON'];
    // $from_date = $_POST['from_date'];
    // $to_date = $_POST['to_date'];
    $selectedIndex = $_POST['selectedColumns'];
    $columnList = $this->columnList;
    
    $classId = $_POST['classId'];
    $sectionId = $_POST['sectionId'];
    // $selectedStudents = $_POST['student_ids'];
    $displayColumns = array();
    
    foreach($selectedIndex as $fIndex) { 
      foreach ($columnList as $col) {
        if ($col['index'] == $fIndex) {
            $selectedColumns[] = (array)$col;
            $displayColumns[] = $col;
        }
      }
    }

    
    $allColumns = array_merge($this->mandatoryFields, $selectedColumns);
    $data['selectedColumns'] = array_merge($this->mandatoryFields, $displayColumns);

    
    

    $new_columns = ' ';
    
    for( $i =0; $i< count($selectedColumns); $i++){
      $new_columns = $new_columns . $selectedColumns[$i]['columnNameWithTable'].', ';
    }
    // echo "<pre>"; print_r($new_columns); die();
    $new_columns = $new_columns . $selectedColumns[count($selectedColumns)-1]['columnNameWithTable'];
    $stdData = $this->healthModel->get_class_health_report($classId,$sectionId, $new_columns);
    $data['exportData'] = $stdData;


    echo json_encode($data);
  }
}
