<?php

require APPPATH . 'libraries/REST_Controller.php';

class Texts extends REST_Controller
{
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
            $data = ['error' => 'User not logged in'];
            $this->response($data, REST_Controller::HTTP_UNAUTHORIZED);
			$this->output->_display();
			exit;
		}
		$this->load->model('parentv2/texting_model', 'text');
	}

	public function index_get($parent_id = 0) {
		//TBD: Validate that Parent ID belongs to this User

		//Get all the Texts sent to this parent and also his kid.
		$data = $this->text->get_texts($parent_id, 'all');

        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function mark_read_unread_post() {
		//TBD: Validate that this parent can actually view this circular

		$text_id = $this->post('text_id');
		$is_read = $this->post('is_read');

		if (empty($text_id) || $text_id <= 0) {
			$data = ['error' => 'INVALID_INPUT'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}

		$is_read = ($is_read == '1' || $is_read == 1) ? '1' : '0';

		$status = $this->text->markAsReadUnRead($text_id, $is_read);

		switch ($status) {
			case -1:
				$data = ['message' => 'PARENT_NOT_AUTHORIZED'];
				break;
			case 0:
				$data = ['message' => 'UPDATE_FAILED'];
				break;
			case 1:
				$data = ['message' => 'SUCCESS'];
				break;
			default:
				$data = ['message' => 'UNKNOWN_ERROR'];
				break;
		}

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

}