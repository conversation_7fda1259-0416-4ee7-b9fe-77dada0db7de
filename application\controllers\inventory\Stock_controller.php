<?php

class Stock_controller extends CI_Controller {
	function __construct() {
		  parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      //This should come up only for super admins
      if (!$this->authorization->isModuleEnabled('INVENTORY')) {
        redirect('dashboard', 'refresh');
    }
    $this->load->model('inventory/stock_model');   
    }

    public function index(){
        $data['stock'] = $this->stock_model->getStockData();
        foreach($data['stock'] as $stock){
            $stock->attr = json_decode($stock->attr);
        }
        //echo "<pre>"; print_r($data['stock']); die();
        $data['main_content'] = 'inventory/stock/index';
        $this->load->view('inc/template', $data);
    }

    public function addStock(){
        $data['vendor_list'] = $this->stock_model->getVendorsData();
        $data['product_list'] = $this->stock_model->getProductsData();
        $data['main_content'] = 'inventory/stock/add_stock';
        $this->load->view('inc/template', $data);
    }

    public function getVariants(){
        $pid = $_POST['pid'];
        $data = $this->stock_model->getVariantsData($pid);
        foreach($data as $stock){
            $stock->attr = json_decode($stock->attr);
            $stock->str = '';
            foreach($stock->attr as $key=>$value){
                $stock->str .= $key.':<b>'.$value.'</b>, ';
            }
        }
        echo json_encode($data);
    }

    public function submitStock(){
        $status = $this->stock_model->insertStock();
        if($status != 0){
            $this->db->trans_commit();
            $this->session->set_flashdata('flashSuccess', 'Stock Detailes Added Successfully.');
        } else{
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        }

        redirect('inventory/stock_controller');
    }


    public function stock_threshold() {
        // $data['classes'] = $this->stock_model->getClasses();
        $data['categories'] = $this->stock_model->getProductCategories();
        $data['main_content'] = 'inventory/stock/stock_threshold';
        $this->load->view('inc/template', $data);
    }

    public function getVariantsByCategory() {
        $category_id = $_POST['category_id'];
        $filter = $_POST['filter'];
        $variants = $this->stock_model->getVariantsByCategory($category_id, $filter);
        $data = array();
        foreach ($variants as $key => $variant) {
            if(!array_key_exists($variant->product_id, $data)) {
                $data[$variant->product_id] = array();
                $data[$variant->product_id]['product_name'] = $variant->product_name;
                $data[$variant->product_id]['variants'] = array();
            }
            $variants = array();
            $variants['name'] = $variant->variant_name;
            $variants['variant_id'] = $variant->variant_id;
            $variants['current_quantity'] = $variant->current_quantity;
            $variants['allocated'] = $variant->allocated;
            $variants['purchased'] = $variant->purchased;
            $variants['initial_quantity'] = $variant->initial_quantity;
            $variants['threshold_quantity'] = $variant->threshold_quantity;
            $variants['attributes'] = json_decode($variant->attributes);
            $data[$variant->product_id]['variants'][] = $variants;
        }
        // echo "<pre>"; print_r($data); die();
        echo json_encode($data);
    }
}