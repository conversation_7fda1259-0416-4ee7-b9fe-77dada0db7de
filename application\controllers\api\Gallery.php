<?php

  defined('BASEPATH') OR exit('No direct script access allowed');

  class Gallery extends CI_Controller {
    private $apiKey;
    public function __construct() {
      parent::__construct();
      date_default_timezone_set('Asia/Kolkata');
      $this->load->model('api/Gallery_model', 'gallery');
      $this->load->library('filemanager');
      $this->apiKey = $this->settings->getSetting('gallery_api_key');
    }

    //Post body - apiKey Required
    //return list of albums
    //accepts json data and return json data
    public function albums() {
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $albums = [];
       if(!array_key_exists('apiKey', $data)) {
        $albums = ['status' => 403, 'message' => 'Unauthorized', 'data' => []];
      } else {
        if($data['apiKey'] != $this->apiKey) {
          $albums = ['status' => 403, 'message' => 'Unauthorized', 'data' => []];
        } else {
          $result = $this->gallery->getAlbums();
          if(empty($result)) {
            $albums = ['status' => '200', 'message' => 'No Data', 'data' => []];
          } else {
            $albums = ['status' => '200', 'message' => 'Successull', 'data' => $result];
          }
        }
      }

      echo json_encode($albums);
    }

    //Post body - apiKey Required
    //Post body - albulId Required
    //return list of picture urls from the given album
    //accepts json data and return json data
    public function pictures() {
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $album = [];
      if(!array_key_exists('apiKey', $data)) {
        $album = ['status' => 403, 'message' => 'Unauthorized', 'data' => []];
      } else {
        if($data['apiKey'] != $this->apiKey) {
          $albums = ['status' => 403, 'message' => 'Unauthorized', 'data' => []];
        } else {
          if(!array_key_exists('albumId', $data)) {
            $album = ['status' => 400, 'message' => 'Album not found', 'data' => []];
          } else {
            $result = $this->gallery->getAlbumPictures($data['albumId']);
              if(empty($result)) {
              $album = ['status' => '200', 'message' => 'No Data', 'data' => []];
            } else {
              $album = ['status' => '200', 'message' => 'Successull', 'data' => $result];
            }
          }
        }
      }

      echo json_encode($album);
    }   

  } 
?>