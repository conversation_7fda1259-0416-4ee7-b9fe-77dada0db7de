<?php

class Leaves extends CI_Controller {

    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      $this->load->model('staff/Staff_leave', 'staff_leave');
    }

    public function dashboard() {
        $data['apply_leave_v2'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPLY') && $this->authorization->isModuleEnabled('LEAVE_V2');
        $data['apply_comp_leave'] = $this->authorization->isAuthorized('LEAVE.COMPENSATION_STAFF_LEAVE_APPLY') && $this->authorization->isModuleEnabled('LEAVE_V2');
        $data['approve_comp_leave'] = $this->authorization->isAuthorized('LEAVE.COMPENSATION_STAFF_LEAVE_APPROVE') && $this->authorization->isModuleEnabled('LEAVE_V2');
        $data['approve_leave_v2'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPROVE') && $this->authorization->isModuleEnabled('LEAVE_V2');
        $data['leave_report_v2'] = $this->authorization->isAuthorized('LEAVE.LEAVE_REPORT') && $this->authorization->isModuleEnabled('LEAVE_V2');
        $data['leave_category_v2'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_CATEGORY') && $this->authorization->isModuleEnabled('LEAVE_V2');
        $data['staff_leave_quota_v2'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_QUOTA') && $this->authorization->isModuleEnabled('LEAVE_V2');
        $data['staff_leave_year_v2'] = $this->authorization->isSuperAdmin();
        $data['staff_leave_yearly_report'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_YEARLY_REPORT') && $this->authorization->isModuleEnabled('LEAVE_V2');

        $enable_level_config = $this->settings->getSetting('enable_multi_level_leave_approver_mode');
        $site_url = site_url();
        $data['staff_tiles'] = array(
            [
            'title' => 'Staff Leaves',
            'sub_title' => 'View/apply leaves',
            'icon' => 'svg_icons/staff.svg',
            'url' => $site_url.'staff/leaves/staff_leaves',
            'permission' => $data['apply_leave_v2'] && !$enable_level_config
            // 'permission' => $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPLY')
            ],
            [
                'title' => 'Compensatory Leaves',
                'sub_title' => 'View/apply compensatory leaves',
                'icon' => 'svg_icons/applyleave.svg',
                'url' => $site_url.'staff/leaves/view_comp_leave',
                'permission' => $data['apply_comp_leave']
                // 'permission' => $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPLY')
            ],
            [
            'title' => 'Approve Leaves',
            'sub_title' => 'Approve staff leave',
            'icon' => 'svg_icons/provisionstaff.svg',
            'url' => $site_url.'staff/leaves/staff_leave_approval',
            'permission' => $data['approve_leave_v2'] && !$enable_level_config
            ],
            [
                'title' => 'Approve Compensatory Leaves',
                'sub_title' => 'Approve compensatory leave',
                'icon' => 'svg_icons/provisionstaff.svg',
                'url' => $site_url.'staff/leaves/comp_leave_approval',
                'permission' => $data['approve_comp_leave']
                // 'permission' => $this->authorization->isSuperAdmin()
            ],
            [
            'title' => 'Staff Leaves 3-Level',
            'sub_title' => 'View/apply leaves',
            'icon' => 'svg_icons/staff.svg',
            'url' => $site_url.'staff/leaves/staff_leaves_3level',
            'permission' => $data['apply_leave_v2'] && $enable_level_config
            // 'permission' => $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPLY')
            ],
            [
            'title' => 'Approve/Cancel Leaves 3-Level',
            'sub_title' => 'Approve staff leave',
            'icon' => 'svg_icons/provisionstaff.svg',
            'url' => $site_url.'staff/leaves/staff_leave_approval_3level',
            'permission' => $data['approve_leave_v2'] && $enable_level_config
            ]
        );
        $data['staff_tiles'] = checkTilePermissions($data['staff_tiles']);

        $school_code = $this->settings->getSetting('school_short_name');

        $data['administration'] = array(
            [
            'title' => 'Leave Categories',
            'sub_title' => 'Define Leave Categories',
            'icon' => 'svg_icons/aggregatereport.svg',
            'url' => $site_url.'staff/leaves/leave_categories',
            'permission' => $data['leave_category_v2']
            ],
            [
            'title' => 'Staff Quota',
            'sub_title' => 'Add Staff Leave Quota',
            'icon' => 'svg_icons/staff.svg',
            'url' => $site_url.'staff/leaves/staff_quota',
            'permission' => $data['staff_leave_quota_v2'] && !$enable_level_config
            ],
            [
            'title' => 'Staff Leave Year',
            'sub_title' => 'Manage staff leave years',
            'icon' => 'svg_icons/calendar.svg',
            'url' => $site_url.'staff/leaves/staff_leave_year',
            'permission' => $data['staff_leave_year_v2']
            ],
            [
            'title' => 'Staff Quota 3-Level',
            'sub_title' => 'Add Staff Leave Quota',
            'icon' => 'svg_icons/staff.svg',
            'url' => $site_url.'staff/leaves/staff_quota_3level',
            'permission' => $data['staff_leave_quota_v2'] && ($enable_level_config || ($school_code == 'iisp'))
            ]
        );
        $data['administration'] = checkTilePermissions($data['administration']);

        $data['report_tiles'] = array(
            [
                'title' => 'Leaves Report',
                'sub_title' => 'Staff Leave Report',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url.'staff/leaves/staff_leave_report',
                'permission' => $data['leave_report_v2']
            ],
            [
                'title' => 'Leaves Report 3-level',
                'sub_title' => 'Staff Leave Report',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url.'staff/leaves/staff_leave_report_3level',
                'permission' => $data['leave_report_v2'] && $enable_level_config
            ],
            [
                'title' => 'Leaves by Date-Range',
                'sub_title' => 'Staff Leave Report',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url.'staff/leaves/date_range_leaves',
                'permission' => $data['leave_report_v2']
            ],
            [
                'title' => 'Leave Balance',
                'sub_title' => 'Staff Leave Balance Report',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url.'staff/leaves/balance_report',
                'permission' => $data['leave_report_v2']
            ],
            [
                'title' => 'Month wise Leave Balance',
                'sub_title' => 'Staff Leave Month wise Balance Report',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url.'staff/leaves/month_wise_balance_report',
                'permission' => $this->authorization->isSuperAdmin()
            ],
            [
                'title' => 'Compensatory Leaves Report',
                'sub_title' => 'Compensatory Leave(v2) Report',
                'icon' => 'svg_icons/circularreport.svg',
                'url' => $site_url.'staff/leaves/comp_leave_report',
                'permission' => $data['leave_report_v2']
                // 'permission' => $this->authorization->isSuperAdmin()
            ],
            [
                'title' => 'Staff Leave Yearly Report',
                'sub_title' => 'Compensatory Leave(v2) Report',
                'icon' => 'svg_icons/circularreport.svg',
                'url' => $site_url.'staff/leaves/staff_leave_yearly_report',
                'permission' => $data['staff_leave_yearly_report']
            ],
            [
                'title' => 'Staff leaves LOP report',
                'sub_title' => 'Staff LOP report',
                'icon' => 'svg_icons/circularreport.svg',
                'url' => $site_url . 'staff/leaves/staff_leaves_lop_report',
                'permission' => $this->authorization->isModuleEnabled("LEAVE_V2") && $this->authorization->isAuthorized("LEAVE.ENABLE_STAFF_LEAVES_LOP_REPORT")
            ]
        );
        $data['report_tiles'] = checkTilePermissions($data['report_tiles']);
        
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'leave_management/tablet_dashboard';
        }else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'leave_management/mobile_dashboard';
        } else {
            $data['main_content'] = 'leave_management/dashboard';
        } 
        $this->load->view('inc/template', $data);
    }

    public function leave_categories() {
	    $saved_categories = $this->staff_leave->getLeaveCategories();
        // echo '<pre>'; print_r($saved_categories); die();
	    
	    $data['leave_categories'] = [];
	    foreach($saved_categories as $key => $value) {
            $data['leave_categories'][$value['name']] = $value;
	    }
	    foreach(LEAVE_CATEGORIES as $cat) {
	      if(!array_key_exists($cat['name'], $data['leave_categories'])) {
	        $data['leave_categories'][$cat['name']] = $cat;
	      }
	    }
	    // echo '<pre>'; print_r($data['leave_categories']); die();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'leave_management/categories_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'leave_management/categories_mobile';
        }else{
          $data['main_content'] = 'leave_management/categories';     	
        }

	    $this->load->view('inc/template', $data);
  	}

  	public function saveLeaveCategory() {
    	echo $this->staff_leave->saveLeaveCategory($_POST);
  	}

    public function getLeaveApplyInstruction(){
        $result=$this->staff_leave->getLeaveApplyInstruction($_POST);
        echo json_encode($result);
    }

    public function staff_quota() {
	    $data['leave_categories'] = $this->staff_leave->getLeaveActiveCategories();
	    // $data['leave_categories'] = $this->staff_leave->getLeaveCategoriesHavingQuota();
        $data['staff_list'] = $this->staff_leave->getStaffList();
	    $data['leave_year'] = $this->staff_leave->getLeaveYear();
	    $data['staff_quota'] = $this->staff_leave->getStaffLeaveQuota($data['leave_year']->id);
	    $data['leave_years'] = $this->staff_leave->getLeaveYears();

        $data['staff_types'] = $this->settings->getSetting("staff_type");
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'leave_management/staff_quota_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'leave_management/staff_quota_mobile';
        }else{
          $data['main_content'] = 'leave_management/staff_quota';      	
        }

	    $this->load->view('inc/template', $data);
	}

  	public function assignStaffQuota() {
	    // echo "<pre>"; print_r($_POST); die();
	    $status = $this->staff_leave->saveAssignedQuota();
	    if($status) {
	      $this->session->set_flashdata('flashSuccess', 'Saved succussfully');
	    } else {
	      $this->session->set_flashdata('flashError', 'Failed to save');
	    }
	    redirect('staff/leaves/staff_quota');
  	}

  	// public function updateStaffQuota() {
	//     // echo "<pre>"; print_r($_POST); die();
	//     $status = $this->staff_leave->updateAssignedQuota();
	//     if($status) {
	//       $this->session->set_flashdata('flashSuccess', 'Updated succussfully');
	//     } else {
	//       $this->session->set_flashdata('flashError', 'Failed to update');
	//     }
	//     redirect('staff/leaves/staff_quota');
  	// }

  	public function staff_leaves() {
        // $applyLeave = $this->authorization->isAuthorized('LEAVE.APPLY_LEAVE_FOR_OTHER_STAFF');
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $applyLeave = $this->_checkHasApplyLeave($staffId);
        
        if($applyLeave) {
            $data['staffDetails'] = $this->staff_leave->getReportingStaffList($applyLeave, $staffId);
        }
        // echo $applyLeave; die();
        $data['leave_year'] = $this->staff_leave->getLeaveYear();
        $data['leave_years'] = $this->staff_leave->getLeaveYears();

        // $data['approved'] = $this->staff_leave->getCountOfLeaves($staffId, 0);
        // $data['applied'] = $this->staff_leave->getCountOfLeaves($staffId, 1);
        $data['applyLeave'] = $applyLeave;
        $data['staff'] = $staffId;
        $data['quota_usage'] = $this->staff_leave->getQuotaUsage($staffId);
        // $data['staff_list'] = $this->staff_leave->list_staffbyuserId();
        // echo '<pre>'; print_r($data); die();
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'staff/staff_leave_application/leave_index_tablet';
        }
        else if($this->mobile_detect->isMobile()) { 
            $data['main_content'] = 'staff/staff_leave_application/leave_index_mobile';
        } else {
            $data['main_content'] = 'staff/staff_leave_application/leave_index_desktop';
            // $data['main_content'] = 'staff/staff_leave_application/apply_leave';
        }
        $this->load->view('inc/template', $data);
    }

    public function staff_leave_approval($selected_staff_id=0) {
        // if($data['approve_leave_v2'])
        // echo $data['approve_leave_v2']; die();
        if( $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPROVE')!=1){
            redirect('staff/leaves/dashboard');
        }
        // $applyLeave = $this->authorization->isAuthorized('LEAVE.APPLY_LEAVE_FOR_OTHER_STAFF');
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $applyLeave = $this->_checkHasApplyLeave($staffId);
        // $data['approved'] = $this->staff_leave->getCountOfLeaves($staffId, 0);
        // $data['applied'] = $this->staff_leave->getCountOfLeaves($staffId, 1);
        $data['applyLeave'] = $applyLeave;
        $data['staff'] = $staffId;

        $data['selected_staff'] = $selected_staff_id;

        $data['staff_list'] = $this->staff_leave->get_staff_list();
        // echo "<pre>"; echo print_r($data["staff_list"]); die();
        $data['status_list'] = $this->staff_leave->get_status_list();
        
        $data['quota_usage'] = $this->staff_leave->getQuotaUsage($staffId);
        // $data['staff_list'] = $this->staff_leave->list_staffbyuserId();
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'staff/staff_leave_application/leave_approval_tablet';
        }else if($this->mobile_detect->isMobile()) { 
            $data['main_content'] = 'staff/staff_leave_application/leave_approval_mobile';
        } else {
            $data['main_content'] = 'staff/staff_leave_application/leave_approval_desktop';
        }
        $this->load->view('inc/template', $data);
    }

    private function _checkHasApplyLeave($staff_id) {
        if($this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN'))
            return 1;
            if($this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPROVE') && $this->staff_leave->is_reporting_manager($staff_id)) 
            return 2;
            if($this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPROVE'))
            return 3;
        return 0;
    }

    public function apply_leave($staff_id=0) {
        $data['staff_id'] = $staff_id > 0 ? $staff_id : $this->authorization->getAvatarStakeHolderId();
        // $data['applyLeave'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
        $data['applyLeave'] = $this->_checkHasApplyLeave($data['staff_id']);
        if($data['applyLeave']) {
            $data['staffDetails'] = $this->staff_leave->getReportingStaffList($data['applyLeave'], $data['staff_id']);
        }
        $data['staff_data'] = $this->staff_leave->list_staffbyuserId();
        $data['leave_year'] = $this->staff_leave->getLeaveYear();

        $data['isEditingNoOfDaysEnabled'] = (int) $this->settings->getSetting("staff_leaves_enable_number_of_leaves_edit");
        //print_r($data['staff_list']); die();
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/staff_leave_application/apply_leave_tablet';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/staff_leave_application/apply_leave_mobile';
        }else{
            $data['main_content'] = 'staff/staff_leave_application/apply_leave';   	
        }
        $this->load->view('inc/template', $data);
    }

    public function getHolidayCountV2(){
        if (!isset($_POST['startDate']) || !isset($_POST['endDate'])) {
            echo 0;
            return;
        }

        $start_date = $_POST['startDate'];
        $end_date = $_POST['endDate'];
        $session = $_POST['session'];
        $staff_id = $_POST['staffId'];
        
        if (empty($start_date) || empty($end_date)) {
            echo 0;
            return;
        }

        if (isset($_POST['conHoliday'])) {
            $conHoliday = $_POST['conHoliday'];
        } else {
            $conHoliday = 'No';
        }

        $start_date_formatted = strtotime($start_date);
        $end_date_formatted = strtotime($end_date);
        $datediff = $end_date_formatted - $start_date_formatted;
        $leaveDays = ($datediff / (60 * 60 * 24)) + 1;

        $holidayList = $this->staff_leave->getHolidayCountV2($staff_id);
        $holidayDates = [];
        $holidays = 0;
        if ($conHoliday != 'Yes') {
            for ($i = 0; $i < $leaveDays; $i++) {
                $date = date("Y-m-d", strtotime($start_date . " +" . $i . " days"));
                if (array_key_exists($date, $holidayList)) {
                    $holidays++;
                    $holidayDates[]= date("d-m-Y", strtotime($date));
                } else if (date('w', strtotime($date)) == 0 || ((date('w', strtotime($date)) == 6) && ($session == 'noon'))) {
                    $holidays++;

                    $holidayDates[] = date("d-m-Y", strtotime($date));
                }
            }
        }
        $noOfDays=$leaveDays - $holidays;
        $result=["noOfDays"=>$noOfDays,"holidayDates"=>$holidayDates];
        echo json_encode($result);
    }
    public function getHolidayCount() {
        if(!isset($_POST['startDate']) || !isset($_POST['endDate'])){
            echo 0;
            return;
        }

        $start_date = $_POST['startDate'];
        $end_date = $_POST['endDate'];
        $session = $_POST['session'];

        if(empty($start_date) || empty($end_date)){
            echo 0;
            return;
        }

        if(isset($_POST['conHoliday'])){
            $conHoliday = $_POST['conHoliday'];
        }else{
            $conHoliday = 'No';
        }

        $start_date_formatted = strtotime($start_date);
        $end_date_formatted = strtotime($end_date);
        $datediff = $end_date_formatted - $start_date_formatted;
        $leaveDays = ($datediff / (60 * 60 * 24)) + 1;
 
        $holidays = 0;
        if ($conHoliday !='Yes') {
            for($i = 0; $i < $leaveDays; $i++){
                $date = date("Y-m-d", strtotime($start_date." +".$i." days"));
                $val = $this->staff_leave->getHoliday($date);
                if($val > 0){
                    $holidays++;
                }
                if($val == 0 && (date('w', strtotime($date)) == 0 || ((date('w', strtotime($date)) == 6) && ($session == 'noon')))){
                    $holidays++;
                }
            }
        }
       
        echo $leaveDays - $holidays;
    }

    public function getAvailableStaffQuota() {
        // $start_date = '2021-01-01';
        // $end_date = '2021-12-31';
        $staff_id = $_POST['staff_id'];
        $available_quota = $this->staff_leave->getAvailableStaffQuota($staff_id);
        /*$no_quota = $this->staff_leave->getLeaveCategoryWithNotQuota();
        foreach ($no_quota as $q) {
        	$available_quota[] = $q;
        }*/
        echo json_encode($available_quota);
    }

    public function getReportingStaffDataperstaff(){
        $staff_id = $_POST['staff_id'];
        $data = $this->staff_leave->getReportingStaffDataperstaff($staff_id);
        echo json_encode($data);
    }

    public function saveLeave() {
        // $_POST["noofdays"]=0;
        $is_application_from_manage_attendance = $_POST["is_application_from_manage_attendance"];

        if ($_POST["noofdays"] <= 0) {
            $this->session->set_flashdata('flashWarning', 'Leave no of days cannot be 0, Please try again!');

            if ($is_application_from_manage_attendance == 0) {
                redirect('staff/leaves/apply_leave');
            } else {
                $data['status'] = 0;
                echo json_encode($data);
            }
            return;
        }

        // $this->session->set_flashdata('flashSuccess', 'Successful to send SMS');

        // Get leave auto approval permission can be given for admin or not
        $_POST["enable_leave_auto_approval_by_admin"] = $this->staff_leave->getStaffLeaveAutoApprovalPermissionForAdmin($_POST["leave_category"]);

        $can_staff_apply_leave = $this->staff_leave->can_staff_apply_leave($_POST);

        if ((int)!$can_staff_apply_leave) {
            if($is_application_from_manage_attendance==0){
                $selection_type = $_POST['selection_type'];
                $this->session->set_flashdata('flashError', $selection_type . ' Leave cannot be taken on this date.');
                redirect('staff/leaves/staff_leaves');
                return 0;
            }else{
                $data['status'] = 0;
                echo json_encode($data);
                return;
            }
        }

        $status =(int) $this->staff_leave->checkAlreadyTaken(null);
        if($status == 0){
            if($is_application_from_manage_attendance==0){
                $this->session->set_flashdata('flashError', 'Leave already taken on this date.');
                redirect('staff/leaves/staff_leaves');
            }else{
                $data['status'] = 0;
                echo json_encode($data);
                return;
            }
        }

        $insert = $this->staff_leave->save_leave_appication($_POST);
        if ($insert) {
            //send notification to attendance admin and reporting manager
            $this->load->model('role');
            $staff_id = $_POST['staff_id'];
            $admins = $this->role->getStaffListByPrivilege('LEAVE', 'STAFF_LEAVE_ADMIN');
            $this->load->model('staff/Attendance_model', 'staff_attendance');
            $rep_manager_id = $this->staff_attendance->getReportingManager($staff_id);
            $staff_ids = $admins;
            if($rep_manager_id) {
                $staff_ids[] = $rep_manager_id;
            }

            $leave_applied_type=$this->staff_leave->get_leave_applied_category($_POST['leave_category']);

            $this->load->helper('texting_helper');
            if(!empty($staff_ids)) {
                $staff = $this->staff_leave->getStaffData($staff_id);
                $staffName = $staff->staffName;
                $from_date = $this->input->post('from_date');
                $to_date = $this->input->post('to_date');
                $title = 'Leave application';
                $message = $staffName . ' applied '.$leave_applied_type;
                if($from_date == $to_date) {
                    $message .= ' on '.$from_date;
                } else {
                    $message .= ' From '.$from_date.' to '.$to_date;
                }
                $input_arr = array();
                $input_arr['staff_ids'] = $staff_ids;
                $input_arr['mode'] = 'notification';
                $input_arr['source'] = 'Staff Leave';
                $input_arr['message'] = $message;
                $response = sendText($input_arr);
            }

            $author = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
            if($author == 1 && $_POST["enable_leave_auto_approval_by_admin"] == 1) {
                $message = 'Leave applied and approved.';
                if($from_date == $to_date) {
                    $message .= 'On '.$from_date;
                } else {
                    $message .= 'From '.$from_date.' to '.$to_date;
                }
                $input_arr = array();
                $input_arr['staff_ids'] = [$staff_id];
                $input_arr['mode'] = 'notification';
                $input_arr['source'] = 'Staff Leave';
                $input_arr['message'] = $message;
                sendText($input_arr);
            }
            // success here
            if($is_application_from_manage_attendance==0){
                $this->session->set_flashdata('flashSuccess', 'Successfully Saved.');
            }else{
                $date = date('Y-m-d', strtotime($from_date));
                $staff_ids = [$staff_id];
                $staff = $this->staff_attendance->getStaffAttendanceByDate($date, $staff_ids);
                $data['staff'] = $staff[0];
                $data['status'] = 1;
            }
        } else {
            // failure here
            if($is_application_from_manage_attendance==0){
                $this->session->set_flashdata('flashError', 'Something Wrong.');
            }else{
                $data['status'] = 0;
            }
        }

        if($is_application_from_manage_attendance==0){
            redirect('staff/leaves/staff_leaves');
        }else{
            echo json_encode($data);
        }
    }

    public function staffLeaves(){
        $staffId = $_POST['staff_id'];
        $statusId = $_POST['status_id'];

        $staffLoggedInId = $this->authorization->getAvatarStakeHolderId();

        $hasApplyLeave = $this->_checkHasApplyLeave($staffLoggedInId);
        $result = $this->staff_leave->getLeavesData($staffId,$hasApplyLeave,$statusId,$staffLoggedInId);
        echo json_encode($result);
    }

    public function staffLeave() {
        $leave_id = $_POST['leave_id'];

        $leave=$this->staff_leave->get_leave_final_status($leave_id);
        if($leave->status!=3 && $leave->status!=4){
            $result = $this->staff_leave->getLeaveData($leave_id);
            echo json_encode($result);
        }else{
            echo 0;
        }
    }

    public function staffLeave3Level() {
        $leave_id = $_POST['leave_id'];

        $leave=$this->staff_leave->get_leave_final_status($leave_id);
        if($leave->final_status!=3 && $leave->final_status!=4){
            $result = $this->staff_leave->getLeaveData($leave_id);
            echo json_encode($result);
        }else{
            echo 0;
        }
    }

    public function saveLeaveStatus() {
        $leave_id = $_POST['id'];

        $isLeaveExists=$this->staff_leave->get_leave_final_status($leave_id);

        if($isLeaveExists->status!=3 && $isLeaveExists->status!=4){
            $status = $_POST['status'];
            $description = $_POST['description'];
            $staff_name = $_POST['staff_name'];
            $response = $this->staff_leave->saveLeaveStatus($leave_id, $status, $description);
            if($response) {
                $leave = $this->staff_leave->getLeaveData($leave_id);
                $approve_status = ($status == 1)?'Approved':'Rejected';
                $message = "Leave by $staff_name";
                // $message = "Leave $approve_status, for application of leave by ".$staff_name;
                if($leave->from_date == $leave->to_date) {
                    $message .= ' On '.$leave->from_date;
                } else {
                    $message .= ' From '.$leave->from_date.' to '.$leave->to_date;
                }
                $message .= " is  $approve_status";
                $this->load->model('role');
                $admins = $this->role->getStaffListByPrivilege('LEAVE', 'STAFF_LEAVE_ADMIN');
                $staff_ids = $admins;
                $staff_ids[] = $leave->staff_id;
                $this->load->model('staff/Attendance_model', 'staff_attendance');
                $rep_manager_id = $this->staff_attendance->getReportingManager($leave->staff_id);
                if($rep_manager_id) {
                    $staff_ids[] = $rep_manager_id;
                }

                if(!empty($staff_ids)) {
                    $this->load->helper('texting_helper');
                    $input_arr = array();
                    $input_arr['staff_ids'] = $staff_ids;
                    $input_arr['mode'] = 'notification';
                    $input_arr['source'] = 'Staff Leave';
                    $input_arr['message'] = $message;
                    sendText($input_arr);
                }
            }
            $result=$response;
        }else{
            $result=0;
        }
        echo $result;
    }

    public function staff_leave_report() {
        $data['leave_years'] = $this->staff_leave->getLeaveYears();
        $data['leave_categories'] = $this->staff_leave->getLeaveCategories();
        $data['isAdmin'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
        
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/staff_leave_application/staff_leave_v2_report_tablet';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/staff_leave_application/staff_leave_v2_report_mobile';
        }else{
            $data['main_content'] = 'staff/staff_leave_application/staff_leave_v2_report';  	
        }


        $this->load->view('inc/template', $data);
    }
    public function staff_leave_report_3level() {
        $data["leave_status_list"] = ["Pending", "Approved", "Auto Approved", "Rejected", "Cancelled"];
        $data['leave_years'] = $this->staff_leave->getLeaveYears();
        $data['isAdmin'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
        $data['staff_list'] = $this->staff_leave->get_staff_list_3level();
        $data['main_content'] = 'staff/staff_leave_application/staff_leave_v2_report_3level';
        $this->load->view('inc/template',$data);
    }
    public function balance_report() {
        $data['leave_years'] = $this->staff_leave->getLeaveYears();
        // $data['categories'] = $this->staff_leave->getLeaveCategoriesHavingQuota();
        $data['categories'] = $this->staff_leave->getLeaveActiveCategories();
        // echo "<pre>"; print_r($data); die();
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/staff_leave_application/staff_leave_v2_balance_report_tablet';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/staff_leave_application/staff_leave_v2_balance_report_mobile';
        }else{
            $data['main_content'] = 'staff/staff_leave_application/staff_leave_v2_balance_report';      
        }


        $this->load->view('inc/template', $data);
    }

    public function month_wise_balance_report(){
        $data['leave_years'] = $this->staff_leave->getLeaveYears();
        $data['financial_leave_years'] = $this->staff_leave->getFinancialLeaveYears();
        $data['categories'] = $this->staff_leave->getLeaveActiveCategories();
        $data['acad_year'] =  $this->staff_leave->get_acad_year_namebyid();
        
        $data['staff_status'] = $this->settings->getSetting("staff_status");

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/staff_leave_application/staff_leave_v2_month_balance_report_tablet';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/staff_leave_application/staff_leave_v2_month_balance_report_mobile';
        }else{
            $data['main_content'] = 'staff/staff_leave_application/staff_leave_v2_month_balance_report';      
        }

        $this->load->view('inc/template', $data);
    }

    public function getPayrollMonthsByFinancialYearId(){
        $financialMonths = $this->staff_leave->getPayrollMonthsByFinancialYearId($_POST);
        echo json_encode($financialMonths);
    }

    public function getStaffLeaveBalance() {
        $leave_year_id = $_POST['leave_year'];
        $balance = $this->staff_leave->getLeaveBalance($leave_year_id);
        echo json_encode($balance);
    }

     public function getStaffLeaveMonthBalance() {
        $month = $_POST['month'];
        $yearId = $_POST['yearId'];
        $staffStatus = $_POST['staffStatus'];
        $selectedDateRangeType = $_POST['selectedDateRangeType'];
        $new_payroll_schedules_id = $_POST['new_payroll_schedules_id'];

        $balance = $this->staff_leave->getLeaveMonthBalance($month,$yearId,$staffStatus,$selectedDateRangeType,$new_payroll_schedules_id);
        echo json_encode($balance);
    }

    public function getLeaveYearById()
    {
        $leave_year_name=$_POST["yearId"];
        echo json_encode($this->staff_leave->getLeaveYearById($leave_year_name));
    }

    public function date_range_leaves() {
        $data['leave_years'] = $this->staff_leave->getLeaveYears();
        $data['isAdmin'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/staff_leave_application/date_range_report_tablet';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/staff_leave_application/date_range_report_mobile';
        }else{
            $data['main_content'] = 'staff/staff_leave_application/date_range_report_desktop';      
        }
        $this->load->view('inc/template', $data);
    }

    private function _getDatesByRange($from_date, $to_date, $format="d-m-Y") {
      $dates = [];
      $from = $from_date;
      while($from != $to_date) {
        $dates[] = array(
          'format1' => date('d-m-Y', strtotime($from)),
          'format2' => date('d M Y', strtotime($from))
        );
        $from = date('Y-m-d', strtotime("$from +1 days"));
      }
      $dates[] = array(
        'format1' => date('d-m-Y', strtotime($from)),
        'format2' => date('d M Y', strtotime($from))
      );
      return $dates;
    }

    private function _getStaffIdsByPrivilege() {
      $staff_ids = [];
      if($this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN')) {
        return $staff_ids;
      }
      $staff_id = $this->authorization->getAvatarStakeHolderId();
      $this->load->model('staff/Attendance_model', 'staff_attendance');
      $ids = $this->staff_attendance->getSatffReporters($staff_id);
      $staff_ids = [0];
      if(!empty($ids)) {
        $staff_ids = $ids;
      }
      return $staff_ids;
    }

    public function getDayWiseStaffLeaves() {
        $leave_year_id = $_POST['leave_year'];
        $leave_category_ids = $_POST['category_id'];
        $leave_year = $this->staff_leave->getLeaveYearById($leave_year_id);
        $staff = $this->staff_leave->getStaffList();
        $staff_ids = $this->_getStaffIdsByPrivilege();
        $leaves = $this->staff_leave->getStaffLeaveByDate($leave_year->start_date, $leave_year->end_date, $staff_ids,$leave_category_ids, $leave_year_id);
        $staff_leaves = [];
        
        foreach ($staff as $i => $stf) {
            $staff[$i]->total_applied = 0;
            $staff[$i]->pending = 0;
            $staff[$i]->approved = 0;
            $staff[$i]->rejected = 0;
            $staff[$i]->cancelled = 0;
            if(array_key_exists($stf->staff_id, $leaves)) {
                $staff[$i]->total_applied = (float)$leaves[$stf->staff_id]->total_applied;
                $staff[$i]->pending = (float)$leaves[$stf->staff_id]->pending;
                $staff[$i]->approved = (float)$leaves[$stf->staff_id]->approved;
                $staff[$i]->rejected = (float)$leaves[$stf->staff_id]->rejected;
                $staff[$i]->cancelled = (float)$leaves[$stf->staff_id]->cancelled;
            }
        }
        //echo "<pre>"; print_r($staff);die();
        $data['leaves'] = $staff;
        echo json_encode($data);
    }

    public function getStaffLeavesByDateRange() {
        $from_date = date('Y-m-d', strtotime($_POST['from_date']));
        $to_date = date('Y-m-d', strtotime($_POST['to_date']));
        $staff = $this->staff_leave->getStaffList();
        $staff_ids = $this->_getStaffIdsByPrivilege();
        $leaves = $this->staff_leave->getStaffLeaveByDate($from_date, $to_date, $staff_ids, []);
        $staff_leaves = [];
        
        foreach ($staff as $i => $stf) {
            $staff[$i]->total_applied = 0;
            $staff[$i]->pending = 0;
            $staff[$i]->approved = 0;
            $staff[$i]->rejected = 0;
            if(array_key_exists($stf->staff_id, $leaves)) {
                $staff[$i]->total_applied = (float)$leaves[$stf->staff_id]->total_applied;
                $staff[$i]->pending = (float)$leaves[$stf->staff_id]->pending;
                $staff[$i]->approved = (float)$leaves[$stf->staff_id]->approved;
                $staff[$i]->rejected = (float)$leaves[$stf->staff_id]->rejected;
            }
        }
        //echo "<pre>"; print_r($staff);die();
        $data['leaves'] = $staff;
        echo json_encode($data);
    }

    public function getStaffLeaveDetails() {
        $leave_year_id = $_POST['leave_year'];
        $staff_id = $_POST['staff_id'];
        $leave_category_ids = $_POST['category_id'];
        $leave_year = $this->staff_leave->getLeaveYearById($leave_year_id);
        $staff_leaves = $this->staff_leave->getStaffLeaveDetails($staff_id, $leave_year->start_date, $leave_year->end_date,$leave_category_ids,$leave_year_id);
        echo json_encode($staff_leaves);
    }

    public function getStaffLeaveDetailsByRange() {
        $from_date = date('Y-m-d', strtotime($_POST['from_date']));
        $to_date = date('Y-m-d', strtotime($_POST['to_date']));
        $staff_id = $_POST['staff_id'];
        $staff_leaves = $this->staff_leave->getStaffLeaveDetails($staff_id, $from_date, $to_date);
        echo json_encode($staff_leaves);
    }

    public function getLeavesHistory() {
        $staff_id = $_POST['staff_id'];
        $leave_year = $this->staff_leave->getLeaveYear();
        $leaves = $this->staff_leave->getStaffLeaveDetails($staff_id, $leave_year->start_date, $leave_year->end_date);
        echo json_encode($leaves);
        // echo "<pre>"; print_r($leaves); die();
    }

    public function getLeavesStatistics() {
        $staff_id = $_POST['staff_id'];
        $leave_year = $this->staff_leave->getLeaveYear();

        // echo "<pre>"; print_r($leave_year); die();
        $leaves = $this->staff_leave->getLeavesStatistics($staff_id,$leave_year->id);
        echo json_encode($leaves);
    }

    public function cancelLeave_3level() {
        $leave_id = $_POST['leave_id'];
        $reason = $_POST['reason'];

        $leave=$this->staff_leave->get_leave_final_status($leave_id);

        if($leave->final_status!=3 && $leave->final_status!=4){
            $isCancelled= $this->staff_leave->cancelLeave($leave_id, $reason);
            if($isCancelled){
                //send leave cancel notification to attendance admin and reporting manager
                $this->load->model('role');
                $staff_id = $this->authorization->getAvatarStakeHolderId();

                $staffName=$_POST["staffName"];
                $leaveType = $_POST["leaveType"];
                $fromDate = $_POST["fromDate"];
                $toDate = $_POST["toDate"];

                $admins = $this->role->getStaffListByPrivilege('LEAVE', 'STAFF_LEAVE_ADMIN');
                $this->load->model('staff/Attendance_model', 'staff_attendance');
                $rep_manager_id = $this->staff_leave->get_reporting_manager_id($staff_id);
                $staff_ids = $admins;

                if(!empty($rep_manager_id)){
                    foreach ($rep_manager_id as $id) {
                        $staff_ids[] = $id;
                    }
                }

                $this->load->helper('texting_helper');
                $staffDtaa = $this->staff_leave->getStaffData($staff_id);

                if (!empty($staff_ids)) {
                    $staff = $staffDtaa;
                    $from_date = $fromDate;
                    $to_date = $toDate;
                    $title = 'Leave application';
                    $message = $staffName . "'s " . $leaveType;
                    if ($fromDate == $toDate) {
                        $message .= ' on ' . $fromDate. ' has been cancelled';
                    } else {
                        $message .= ' From ' . $fromDate . ' to ' . $toDate. ' has been cancelled';
                    }
                    $input_arr = array();
                    $input_arr['staff_ids'] = $staff_ids;
                    $input_arr['mode'] = 'notification';
                    $input_arr['source'] = 'Staff Leave';
                    $input_arr['message'] = $message;
                    $response = sendText($input_arr);
                }
            }
            echo $isCancelled;
        }else{
            echo 0;
        }
    }

    public function cancelLeave() {
        $leave_id = $_POST['leave_id'];
        $reason = $_POST['reason'];
        
        $leave=$this->staff_leave->get_leave_final_status($leave_id);
        if($leave->status!=3 && $leave->status!=4){
            echo $this->staff_leave->cancelLeave($leave_id, $reason);
        }else{
            echo 0;
        }
    }

    public function staff_leave_year() {
        $data['leave_years'] = $this->staff_leave->getLeaveYears();
        $data['main_content'] = 'staff/staff_leave_application/leave_years';
        $this->load->view('inc/template', $data);
    }

    public function activateLeaveYear() {
        $leave_year_id = $_POST['leave_year_id'];
        echo $this->staff_leave->activateLeaveYear($leave_year_id);
    }

    public function saveLeaveYear() {
        echo $this->staff_leave->saveLeaveYear();
    }

    public function view_comp_leave($leaves_filed_type=1){ //1 -> All My leaves ELSE -> All leaves filed by me
        $data['staff_data'] = $this->staff_leave->list_staffbyuserId();
        if (!empty($data['staff_data'])) {
            $data['leave_year'] = $this->staff_leave->getLeaveYear();
            $data['approved'] = $this->staff_leave->getCompCountOfLeaves($data['staff_data']->id ,'approved');
            $data['applied'] = $this->staff_leave->getCompCountOfLeaves($data['staff_data']->id ,'total');
            $data['comp_leave_details'] = $this->staff_leave->getCompLeaveDetails($data['staff_data']->id,$leaves_filed_type);
        }
        
        $data['is_leave_admin']=(int)$this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
        $data['staff_list'] = $this->staff_leave->getStaffList();
        $data['leaves_filed_type'] = $leaves_filed_type;

        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'staff/staff_leave_application/comp_leave_view_tablet';
        }else if($this->mobile_detect->isMobile()) { 
            $data['main_content'] = 'staff/staff_leave_application/comp_leave_view_mobile';
        } else {
            $data['main_content'] = 'staff/staff_leave_application/comp_leave_view';
        }
        $this->load->view('inc/template', $data);
    }

    public function add_comp_leave(){
        $data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
        // $data['applyLeave'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
        $data['applyLeave'] = $this->_checkHasApplyLeave($data['staff_id']);
        if($data['applyLeave']) {
            $data['staffDetails'] = $this->staff_leave->getReportingStaffList($data['applyLeave'], $data['staff_id']);
        }
        $data['staff_data'] = $this->staff_leave->list_staffbyuserId();
        $data['leave_year'] = $this->staff_leave->getLeaveYear();
        $data['is_leave_admin'] = (int) $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
        $data['staff_list'] = $this->staff_leave->getStaffList();

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/staff_leave_application/apply_comp_leave_tablet';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/staff_leave_application/apply_comp_leave_mobile';
        }else{
            $data['main_content'] = 'staff/staff_leave_application/apply_comp_leave';     	
        }


        $this->load->view('inc/template', $data);
    }

    public function request_comp_leave(){
        $status =(int) $this->staff_leave->checkAlreadyRequested($_POST);
        if($status!=0){
            echo $status;
            return;
        }

        $insert = $this->staff_leave->insert_comp_leave_details($_POST);
        //echo "<pre>"; print_r($insert);
        if ($insert) {
            //send notification to attendance admin and reporting manager
            $this->load->model('role');
            $staff_id = $_POST['requested_by'];
            $admins = $this->role->getStaffListByPrivilege('LEAVE', 'STAFF_LEAVE_ADMIN');
            $this->load->model('staff/Attendance_model', 'staff_attendance');
            $rep_manager_id = $this->staff_attendance->getReportingManager($staff_id);
            $staff_ids = $admins;
            if($rep_manager_id) {
                $staff_ids[] = $rep_manager_id;
            }

            $this->load->helper('texting_helper');
            if(!empty($staff_ids)) {
                $staff = $this->staff_leave->getStaffData($staff_id);
                $staffName = $staff->staffName;
                $worked_on = $this->input->post('worked_on');
                //$to_date = $this->input->post('to_date');
                $title = 'Compensatory Leave application';
                $message = $staffName . ' applied compensatory leave - ';
                $message .= 'Worked on '.$worked_on;
                
                $input_arr = array();
                $input_arr['staff_ids'] = $staff_ids;
                $input_arr['mode'] = 'notification';
                $input_arr['source'] = 'Staff Leave';
                $input_arr['message'] = $message;
                $response = sendText($input_arr);
            }
            echo 1;
            return;
        } else {
            echo 0;
            return;
        }

    }

    public function comp_leave_report() {
        $data['leave_years'] = $this->staff_leave->getLeaveYears();
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/staff_leave_application/comp_leave_report_tablet';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/staff_leave_application/comp_leave_report_mobile';
        }else{
            $data['main_content'] = 'staff/staff_leave_application/comp_leave_report';   	
        }
        $this->load->view('inc/template', $data);
    }

    public function staff_leave_yearly_report($leaveYearId=-1,$staffTypeId=-1) {
        $data['staff_types'] = $this->settings->getSetting("staff_type");
        $data['leave_years'] = $this->staff_leave->getLeaveYears();

        $data['get_month_wise_new_leave_report'] = $this->staff_leave->get_month_wise_new_leave_report($leaveYearId,$staffTypeId);

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/staff_leave_application/month_wise_new_leave_report/index_tablet';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/staff_leave_application/month_wise_new_leave_report/index_mobile';
        }else{
            $data['main_content'] = 'staff/staff_leave_application/month_wise_new_leave_report/index';   	
        }
        
        $this->load->view('inc/template', $data);
    }

    public function staff_leaves_lop_report() {
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/staff_leave_application/staff_leaves_lop_report/tablet_index';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/staff_leave_application/staff_leaves_lop_report/mobile_index';
        }else{
            $data['main_content'] = 'staff/staff_leave_application/staff_leaves_lop_report/desktop_index';   	
        }
        $this->load->view('inc/template', $data);
    }

    public function comp_leave_approval(){
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $applyLeave = $this->_checkHasApplyLeave($staffId);
        $data['applyLeave'] = $applyLeave;
        $data['staff'] = $staffId;
        $data['approved'] = $this->staff_leave->getCompLeaves('approved');
        $data['applied'] = $this->staff_leave->getCompLeaves('total');

        // echo '<pre>';print_r($data['comp_leave_details']); die();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'staff/staff_leave_application/comp_leave_approval_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'staff/staff_leave_application/comp_leave_approval_mobile';
        }else{
          $data['main_content'] = 'staff/staff_leave_application/comp_leave_approval';     	
        }

        
        $this->load->view('inc/template', $data);

    }

    public function get_individual_staff_attendance_info(){
        $staffId=$this->input->post("staffId");
        $requestDate = $this->input->post("requestDate");
        $data=$this->staff_leave->get_individual_staff_attendance_info($staffId, $requestDate);
        echo json_encode($data);
    }

    public function getAllCompLeaves(){
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $applyLeave = $this->_checkHasApplyLeave($staffId);
        $result = $this->staff_leave->getCompDetails($staffId, $applyLeave, $_POST);
        echo json_encode($result);
    }

    public function cancelCompLeave(){
        $leave_id = $_POST['leave_id'];
        $reason = $_POST['reason'];
        $staffId = $this->authorization->getAvatarStakeHolderId();
        echo $this->staff_leave->cancelCompLeave($leave_id, $reason, $staffId);
    }


    public function saveCompStatus() {
        $leave_id = $_POST['id'];
        $old_status = $_POST['old_status'];
        $new_status = $_POST['new_status'];
        $description = $_POST['description'];
        $leave_applied_for = $_POST['leave_applied_for'];
        $leave_year_id = $_POST['leave_year_id'];
        $no_of_days = $_POST['no_of_days'];
        
        $staffId = $this->authorization->getAvatarStakeHolderId();

        // check for quota remianing
        // 0-> Pending
        // 1-> Approved
        // 2-> Rejected
        if($old_status==1 && $new_status==2){
            $remainingTotalQuota=$this->staff_leave->getCompensatoryLeaveQuotaDetails($leave_applied_for,$leave_year_id);
            if($no_of_days>$remainingTotalQuota){
                echo 0;
                return 0;
            }
        }

        $response = $this->staff_leave->saveCompLeaveStatus($leave_id, $old_status, $new_status, $description, $staffId, $leave_applied_for, $no_of_days, $leave_year_id);
        //Sms sending... 
        
        if($response) {
            $leave = $this->staff_leave->getCompData($leave_id);
            $approve_status = ($new_status == 1)?'Approved':'Rejected';
            $message = "Leave $approve_status, for application of Compensatory leave ";
            $message .= 'Worked on '.$leave->worked_on;
            $this->load->model('role');
            $admins = $this->role->getStaffListByPrivilege('LEAVE', 'STAFF_LEAVE_ADMIN');

            $this->load->model('staff/Attendance_model', 'staff_attendance');
            $rep_manager_id = $this->staff_attendance->getReportingManager($leave_applied_for);

            $staff_ids = $admins;
            if ($rep_manager_id) {
                $staff_ids[] = $rep_manager_id;
            }

            $staff_ids[] = $leave->staff_id;

            if(!empty($staff_ids)) {
                $this->load->helper('texting_helper');
                $input_arr = array();
                $input_arr['staff_ids'] = $staff_ids;
                $input_arr['mode'] = 'notification';
                $input_arr['source'] = 'Staff Leave';
                $input_arr['message'] = $message;
                sendText($input_arr);
            }
        }
        echo $response;
    }


    public function getDayWiseCompLeaves() {
        $leave_year_id = $_POST['leave_year'];
        $leave_year = $this->staff_leave->getLeaveYearById($leave_year_id);
        
        $staff = $this->staff_leave->getStaffList();
        
        $staff_ids = $this->_getStaffIdsByPrivilege();
        //echo "<pre>"; print_r($staff);die();
        $leaves = $this->staff_leave->getCompLeaveByDate($leave_year->start_date, $leave_year->end_date, $staff_ids);
        //echo "<pre>"; print_r($leaves);die();
        $staff_leaves = [];
        // echo "<pre>"; print_r($leaves);
        
        foreach ($staff as $i => $stf) {
            $staff[$i]->total_applied = 0;
            $staff[$i]->pending = 0;
            $staff[$i]->approved = 0;
            $staff[$i]->rejected = 0;
            if(array_key_exists($stf->staff_id, $leaves)) {
                $staff[$i]->total_applied = (float)$leaves[$stf->staff_id]->total_applied;
                $staff[$i]->pending = (float)$leaves[$stf->staff_id]->pending;
                $staff[$i]->approved = (float)$leaves[$stf->staff_id]->approved;
                $staff[$i]->rejected = (float)$leaves[$stf->staff_id]->rejected;
            }
        }
        // echo "<pre>"; print_r($staff);die();
        $data['leaves'] = $staff;
        echo json_encode($data);
    }

    public function getCompLeavesDetails() {
        $leave_year_id = $_POST['leave_year'];
        $staff_id = $_POST['staff_id'];
        $leave_year = $this->staff_leave->getLeaveYearById($leave_year_id);
        $staff_leaves = $this->staff_leave->getCompLeavesDetails($staff_id, $leave_year->start_date, $leave_year->end_date);
        //echo "<pre>"; print_r($staff_leaves);die();
        echo json_encode($staff_leaves);
    }

    public function staffCompLeaves(){
        $leave_id = $_POST['leave_id'];
        $staff_id = $this->authorization->getAvatarStakeHolderId();
        $hasApplyLeave = $this->_checkHasApplyLeave($staff_id);
        $result = $this->staff_leave->getSingleCompData($leave_id);
        echo json_encode($result);
    }

    public function get_staff_quota_by_staffType(){
        $staff_type = $_POST['staff_type'];
        $leave_year = $_POST['leave_year'];

        $is_multilevel_leave_approval_mode_enabled=$this->settings->getSetting('enable_multi_level_leave_approver_mode');
        if((int)$is_multilevel_leave_approval_mode_enabled==1){
            $show_aprovals = 1;
        }else{
            $show_aprovals=0;
        }

        $hasStaffLeaveQuotaWritePermission=$this->staff_leave->get_staff_leaves_acad_year_id($leave_year);
        
	    $staff_quota = $this->staff_leave->getStaffLeaveQuota_by_staffType($leave_year, $staff_type, $show_aprovals);
        echo json_encode(["staff_quota"=>$staff_quota,"hasStaffLeaveQuotaWritePermission"=>$hasStaffLeaveQuotaWritePermission]);
    }

    public function mass_update_quota() {
        echo $this->staff_leave->mass_update_quota();
    }

    public function edit_quota_for_a_staff() {
        $enable_3level_config = $this->settings->getSetting('enable_multi_level_leave_approver_mode');
        if ($enable_3level_config) {
            echo $this->staff_leave->edit_quota_for_a_staff_level3();
        } else {
            echo $this->staff_leave->edit_quota_for_a_staff();   
        }
        //echo $this->staff_leave->edit_quota_for_a_staff();
    }
   

    public function get_leave_categories() {
        echo json_encode( $this->staff_leave->get_leave_categories() );
    }

    public function get_quota_for_single_staff() {
        echo json_encode( $this->staff_leave->get_quota_for_single_staff() );
    }

    public function disable_leave_category() {
        echo $this->staff_leave->disable_leave_category($_POST['category_id']);
    }

    public function activate_leave_category() {
        echo $this->staff_leave->activate_leave_category($_POST['category_id']);
    }

    public function updateLeaveCategory() {
    	echo $this->staff_leave->updateLeaveCategory($_POST);
  	}
    
    public function getReportingStaffDataperstaff_3level(){
        $staff_id = $_POST['staff_id'];
        $data = $this->staff_leave->getReportingStaffDataperstaff_3level($staff_id);
        echo json_encode($data);
    }
    
    public function staff_leaves_by_value(){
        $staffId = $_POST['staff_id'];
        //$statusId = $_POST['status_id'];
        $val=$_POST['val'];
        $leave_year_id = $_POST['leave_year_id'];

        $staffLoggedInId = $this->authorization->getAvatarStakeHolderId();

        $hasApplyLeave = $this->_checkHasApplyLeave($staffLoggedInId);
        $result = $this->staff_leave->staff_leaves_by_value($staffId,$hasApplyLeave,$val,$staffLoggedInId,$leave_year_id);
        echo json_encode($result);
    }
    
   public function staff_quota_3level() {
	    $data['leave_categories'] = $this->staff_leave->getLeaveActiveCategories();
	    // $data['leave_categories'] = $this->staff_leave->getLeaveCategoriesHavingQuota();
        $data['staff_list'] = $this->staff_leave->getStaffList();
	    $data['leave_year'] = $this->staff_leave->getLeaveYear();
	    $data['staff_quota'] = $this->staff_leave->getStaffLeaveQuota($data['leave_year']->id);
        $data['leave_years'] = $this->staff_leave->getLeaveYears();

        $data['staff_types'] = $this->settings->getSetting("staff_type");
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'leave_management/staff_quota_3level_mobile';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'leave_management/staff_quota_3level_mobile';
        }else{
          $data['main_content'] = 'leave_management/staff_quota_3level';
        }

	    $this->load->view('inc/template', $data);
	}

    public function staff_leave_approval_3level($selected_staff_id=0) {
        // if($data['approve_leave_v2'])
        // echo $data['approve_leave_v2']; die();
        if( $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPROVE')!=1){
            redirect('staff/leaves/dashboard');
        }
        $data['isLeaveAdmin'] = (int)$this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
        // $applyLeave = $this->authorization->isAuthorized('LEAVE.APPLY_LEAVE_FOR_OTHER_STAFF');
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $data['applyLeave'] = $this->_checkHasApplyLeave($staffId);
        // $data['approved'] = $this->staff_leave->getCountOfLeaves($staffId, 0);
        // $data['applied'] = $this->staff_leave->getCountOfLeaves($staffId, 1);
        $data['staff'] = $staffId;

        $data['selected_staff'] = $selected_staff_id;

        $data['staff_list'] = $this->staff_leave->get_staff_list_3level(); // $data['quota_usage'] = $this->staff_leave->getQuotaUsage($staffId);
        $data["leave_status_list"]=["Pending","Approved","Auto Approved","Rejected","Cancelled"];

        $data['enableStaffLeaveConversion'] = (int) $this->authorization->isAuthorized('LEAVE.ENABLE_STAFF_LEAVE_CONVERSION');

        $data['leave_years'] = $this->staff_leave->getLeaveYears();
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'staff/staff_leave_application/leave_approval_3level_mobile';
        }else if($this->mobile_detect->isMobile()) { 
            $data['main_content'] = 'staff/staff_leave_application/leave_approval_3level_mobile';
        } else {
            $data['main_content'] = 'staff/staff_leave_application/leave_approval_desktop_3level';
        }
        $this->load->view('inc/template', $data);
    }

   //Multi-level Staff Approval
    public function staff_leaves_3level() {
        // $applyLeave = $this->authorization->isAuthorized('LEAVE.APPLY_LEAVE_FOR_OTHER_STAFF');
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $applyLeave = $this->_checkHasApplyLeave($staffId);
        
        if($applyLeave) {
            $data['staffDetails'] = $this->staff_leave->getReportingStaffList($applyLeave, $staffId);
        }
        // echo $applyLeave; die();
        $data['leave_year'] = $this->staff_leave->getLeaveYear();
	    $data['leave_years'] = $this->staff_leave->getLeaveYears();

        // $data['approved'] = $this->staff_leave->getCountOfLeaves($staffId, 0);
        // $data['applied'] = $this->staff_leave->getCountOfLeaves($staffId, 1);
        $data['applyLeave'] = $applyLeave;
        $data['staff'] = $staffId;
        $data['quota_usage'] = $this->staff_leave->getQuotaUsage($staffId);
        // $data['staff_list'] = $this->staff_leave->list_staffbyuserId();
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'staff/staff_leave_application/leave_index_mobile_3level';
        }
        else if($this->mobile_detect->isMobile()) { 
            $data['main_content'] = 'staff/staff_leave_application/leave_index_mobile_3level';
        } else {
            $data['main_content'] = 'staff/staff_leave_application/leave_index_desktop_3level';
            // $data['main_content'] = 'staff/staff_leave_application/apply_leave';
        }
        $this->load->view('inc/template', $data);
    }

    public function apply_leave_3level($staff_id=0) {
        $data['staff_id'] = $staff_id>0 ? $staff_id : $this->authorization->getAvatarStakeHolderId();
        // echo $data['staff_id']; die();
        // $data['applyLeave'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
        $data['applyLeave'] = $this->_checkHasApplyLeave($data['staff_id']);
        if($data['applyLeave']) {
            $data['staffDetails'] = $this->staff_leave->getReportingStaffList($data['applyLeave'], $data['staff_id']);
        }
        $data['staff_data'] = $this->staff_leave->list_staffbyuserId();
        $data['leave_year'] = $this->staff_leave->getLeaveYear();

        $data['isEditingNoOfDaysEnabled'] = (int)$this->settings->getSetting("staff_leaves_enable_number_of_leaves_edit");
        //print_r($data['staff_list']); die();
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/staff_leave_application/apply_leave_3level_tablet';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/staff_leave_application/apply_leave_3level_mobile';
        }else{
            $data['main_content'] = 'staff/staff_leave_application/apply_leave_3level';   	
        }
        $this->load->view('inc/template', $data);
    }

    // public function check_is_Staff_absent(){
    //     echo $this->staff_leave->check_is_Staff_absent($_POST);
    // }

    public function saveLeave_3level() {
        // $_POST["noofdays"]=0;
        $is_application_from_manage_attendance=$_POST["is_application_from_manage_attendance"];

        if ($_POST["noofdays"] <= 0) {
            $this->session->set_flashdata('flashWarning', 'Leave no of days cannot be 0, Please try again!');

            if($is_application_from_manage_attendance==0){
                redirect('staff/leaves/apply_leave_3level');
            }else{
                $data['status'] = 0;
                echo json_encode($data);
            }
            return;
        }
        
        // Get leave auto approval permission can be given for admin or not
        $_POST["enable_leave_auto_approval_by_admin"]=$this->staff_leave->getStaffLeaveAutoApprovalPermissionForAdmin($_POST["leave_category"]);
        //$rep_manager_id = $_POST['reporting-manager'];
        // 1. check whether he is applicalble to take leaves or not

        $can_staff_apply_leave= $this->staff_leave->can_staff_apply_leave($_POST);

        if((int)!$can_staff_apply_leave){
            
            if($is_application_from_manage_attendance==0){
                $selection_type = $_POST['selection_type'];
                $this->session->set_flashdata('flashError', $selection_type.' Leave cannot be taken on this date.');
                redirect('staff/leaves/staff_leaves_3level');
                return 0;
            }else{
                $data['status'] = 0;
                echo json_encode($data);
                return;
            }
        }

        $status =(int) $this->staff_leave->checkAlreadyTaken_3_level(null);
        if($status == 0){
            if($is_application_from_manage_attendance==0){
                $this->session->set_flashdata('flashError', 'Leave already taken on this date.');
                redirect('staff/leaves/staff_leaves_3level');
            }else{
                $data['status'] = 0;
                echo json_encode($data);
                return;
            }
        }

        $insert = $this->staff_leave->save_leave_appication_3level($_POST);
        if ($insert) {
            //send notification to attendance admin and reporting manager
            $this->load->model('role');
            $staff_id = $_POST['staff_id'];
            
            $admins = $this->role->getStaffListByPrivilege('LEAVE', 'STAFF_LEAVE_ADMIN');
            $this->load->model('staff/Attendance_model', 'staff_attendance');
            // $rep_manager_id = $this->staff_attendance->getReportingManager($staff_id);
            //  $staff_ids = $admins;
            // if($rep_manager_id) {
            //     $staff_ids[] = $rep_manager_id;
            // }
            $rep_manager_id = $this->staff_leave->get_reporting_manager_id($staff_id);
            $staff_ids = $admins;
            foreach($rep_manager_id  as $id) {
                $staff_ids[] = $id;
            }

            $leave_applied_type=$this->staff_leave->get_leave_applied_category($_POST['leave_category']);

            $this->load->helper('texting_helper');

            $staffDtaa = $this->staff_leave->getStaffData($staff_id);

            if(!empty($staff_ids)) {
                $staff = $staffDtaa;
                $staffName = $staff->staffName;
                $from_date = $this->input->post('from_date');
                $to_date = $this->input->post('to_date');
                $title = 'Leave application';
                $message = $staffName . ' applied '.$leave_applied_type;
                if($from_date == $to_date) {
                    $message .= ' on '.$from_date;
                } else {
                    $message .= ' From '.$from_date.' to '.$to_date;
                }
                $input_arr = array();
                $input_arr['staff_ids'] = $staff_ids;
                $input_arr['mode'] = 'notification';
                $input_arr['source'] = 'Staff Leave';
                $input_arr['message'] = $message;
                $response = sendText($input_arr);
            }

            // sending email
            $email_body = "$staffDtaa->staffName . ' applied '. $leave_applied_type";
            $this->sendStaffLeaveEmail($staff_ids, $email_body);
            // sent email

            $author = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
            if($author == 1 && $_POST["enable_leave_auto_approval_by_admin"]==1) {
                $message = 'Leave applied and approved.';
                if($from_date == $to_date) {
                    $message .= 'On '.$from_date;
                } else {
                    $message .= 'From '.$from_date.' to '.$to_date;
                }
                $input_arr = array();
                $input_arr['staff_ids'] = [$staff_id];
                $input_arr['mode'] = 'notification';
                $input_arr['source'] = 'Staff Leave';
                $input_arr['message'] = $message;
                sendText($input_arr);
            }

            // success here
            if($is_application_from_manage_attendance==0){
                $this->session->set_flashdata('flashSuccess', 'Successfully Saved.');
            }else{
                $date = date('Y-m-d',strtotime($from_date));
                $staff_ids = [$staff_id];
                $staff = $this->staff_attendance->getStaffAttendanceByDate($date, $staff_ids);
                $data['staff'] = $staff[0];
                $data['status'] = 1;
            }
        } else {
            // failure here
            if($is_application_from_manage_attendance==0){
                $this->session->set_flashdata('flashError', 'Something Wrong.');
            }else{
                $data['status'] = 0;
            }
        }

        if($is_application_from_manage_attendance==0){
            redirect('staff/leaves/staff_leaves_3level');
        }else{
            echo json_encode($data);
        }

    }
    public function staffLeaves_3level(){
        $staffId = $_POST['staff_id'];
        $statusId = $_POST['status_id'];
        $leave_year_id = $_POST['leave_year_id'];
        //$val=$_POST['val'];
        $staffLoggedInId = $this->authorization->getAvatarStakeHolderId();

        $hasApplyLeave = $this->_checkHasApplyLeave($staffLoggedInId);
        $result = $this->staff_leave->getLeavesData_3level($staffId,$hasApplyLeave,$statusId,$staffLoggedInId,$leave_year_id);
        echo json_encode($result);
    }

    // public function getSingleLeaveDetails3Level(){
    //     $leaveId=$_POST["leaveId"];
    //     $staffLoggedInId = $this->authorization->getAvatarStakeHolderId();
    //     $hasApplyLeave = $this->_checkHasApplyLeave($staffLoggedInId);
    //     $result = $this->staff_leave->getSingleLeaveDetails3Level($leaveId,$hasApplyLeave,$staffLoggedInId);
    //     echo json_encode($result);
    // }

    public function get_staff_leaves_report_3_level() {
        $staffId = $_POST['staff_id'];
        $statusId = $_POST['status_id'];
        $staffLoggedInId = $this->authorization->getAvatarStakeHolderId();
        $leave_year_id = $_POST['leave_year_id'];
        $has_admin_permission = $this->_checkHasApplyLeave($staffLoggedInId);
        $result = $this->staff_leave->get_report_leaves_data_3_level($staffId,$has_admin_permission,$statusId,$staffLoggedInId,$leave_year_id);
        echo json_encode($result);
    }
    public function get_leaves_data_by_value(){
        $staffId = $_POST['staff_id'];
        //$statusId = $_POST['status_id'];
        $val = $_POST['val'];
        $leave_year_id=$_POST['leave_year_id'];

        $staffLoggedInId = $this->authorization->getAvatarStakeHolderId();

        $hasApplyLeave = $this->_checkHasApplyLeave($staffLoggedInId);
        $result = $this->staff_leave->get_leaves_data_by_value($staffId,$hasApplyLeave,$val,$staffLoggedInId,$leave_year_id);
        // echo "<pre>" ;
        // print_r($result);
        // die();
        echo json_encode($result);
    }

    public function sendStaffLeaveEmail($staff_ids,$email_body){
        $this->load->helper('email_helper');
        $this->load->model('communication/emails_model');

        $from_email = $this->settings->getSetting('leave_application_from_email');
        if (empty($from_email)) {
            return false;
            // $from_email = "<EMAIL>";
        }

        $sender_list = [];
        $emaildata = array();
        $leave_approvers = $this->staff_leave->get_leave_approvers($staff_ids);
        $email_ids = [];

        if (!empty($leave_approvers)) {
            foreach ($leave_approvers as $key => $val) {
                $sender_list['staff'] = [
                    'send_to_type' => 'Staff',
                    'id' => $val->staff_id,
                ];

                $object = new stdclass();
                $object->id = $val->staff_id;
                $object->email = $val->email;
                $object->avatar_type = '4';
                array_push($emaildata, $object);

                if ($val->email) {
                    array_push($email_ids, $val->email);
                }
            }
        }

        $email_master_data = array(
            'subject' => 'Leave application',
            'body' => $email_body,
            'source' => 'Staff Leave',
            'sent_by' => $this->authorization->getAvatarId(),
            'recievers' => 'Staff',
            'from_email' => $from_email,
            'files' => '',
            'acad_year_id' => $this->acad_year->getAcadYearId(),
            'visible' => 1,
            'sender_list' => empty($sender_list) ? NULL : json_encode($sender_list),
            'sending_status' => 'Send Email'
        );

        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $this->emails_model->save_sending_data((object) $emaildata, $email_master_id);
        $email = $this->emails_model->getEmailInfo($email_master_id);
        return sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
    }
    public function saveLeaveStatus_3level() {
        $approval_mode = $_POST['approval_mode'];
        $leave_id = $_POST['id'];

        $isLeaveExists=$this->staff_leave->get_leave_final_status($leave_id);

        if(isset($_POST['status_type']) && $isLeaveExists->final_status!=3 && $isLeaveExists->final_status!=4){        
            $status = $_POST['radio_status'];
            $description = $_POST['description'];
            $staff_name = $_POST['staff_name'];
            $approved_status_type = $_POST['status_type'];
            $approved_status_2 = $_POST['approve_status_2'];
            $approved_status_3 = $_POST['approve_status_3'];
            $response = $this->staff_leave->saveLeaveStatus_3level($leave_id, $status, $description,$approved_status_type,$approved_status_2,$approved_status_3,$approval_mode);

            if($response) {
                $leave = $this->staff_leave->getLeaveData_3level($leave_id);
                $reporting_manager_name = $this->staff_leave->get_reporting_manager_name_by_status_type($approved_status_type,$leave->staff_id,$leave_id);
            
                // if ($approved_status_type == 1) {

                // }
                
                $approve_status = ($status == 1)?'Approved':'Rejected';
                $message = "Leave by $staff_name";
                // $message = "Leave $approve_status, for application of leave by ".$staff_name;
                if($leave->from_date == $leave->to_date) {
                    $message .= ' On '.$leave->from_date;
                } else {
                    $message .= ' From '.$leave->from_date.' to '.$leave->to_date;
                }
                $message .= " is  $approve_status by $reporting_manager_name->staff_name";
                $this->load->model('role');
                $admins = $this->role->getStaffListByPrivilege('LEAVE', 'STAFF_LEAVE_ADMIN');
                $staff_ids = $admins;
                
                
            
                
                // $this->load->model('staff/Attendance_model', 'staff_attendance');
                // $rep_manager_id = $this->staff_attendance->getReportingManager($leave->staff_id);
                // if($rep_manager_id) {
                //     $staff_ids[] = $rep_manager_id;
                // }
                $rep_manager_id = $this->staff_leave->get_reporting_manager_id($leave->staff_id);
                
                $staff_ids = $admins;
                
                foreach($rep_manager_id  as $id) {
                    if(!empty($id)) {
                        $staff_ids[] = $id;
                    }
                }
                $staff_ids[] = $leave->staff_id;
                if(!empty($staff_ids)) {
                    $this->load->helper('texting_helper');
                    $input_arr = array();
                    $input_arr['staff_ids'] = $staff_ids;
                    $input_arr['mode'] = 'notification';
                    $input_arr['source'] = 'Staff Leave';
                    $input_arr['message'] = $message;
                    sendText($input_arr);
                }

                // sending email
                // $email_body=$message;
                $this->sendStaffLeaveEmail($staff_ids,$message);
                // sent email
            }
            $result=$response;
        }else{
            $result=0;
        }
        // echo "<pre>"; print_r($result); die();
        echo $result;
    }
    public function mass_update_quota_3level() {
        echo $this->staff_leave->mass_update_quota_level3();
    }

    public function reject_approved_staff_leave(){
        echo $this->staff_leave->reject_approved_staff_leave($_POST);
    }

    function getStaffLeaveLops(){
        $response=$this->staff_leave->getStaffLeaveLops($_POST);
        echo json_encode($response);
    }
    
    public function get_staff_leave_info_to_convert_leave_from_one_type_to_another(){
        $leave_id=$_POST["leaveId"];
        if(empty($leave_id)){
            echo json_encode([]);
            return;
        }

        // bring leave info
        $converting_leave_info=$this->staff_leave->get_staff_leave_info($leave_id);

        if(empty($converting_leave_info)){
            echo json_encode([]);
            return;
        }

        $start_date = $converting_leave_info->from_date;
        $end_date = $converting_leave_info->to_date;
        
        $datediff = strtotime($end_date) - strtotime($start_date);
        $leaveDays = ($datediff / (60 * 60 * 24)) + 1;

        // Generate leave dates
        //2. Now bring the actual dates that will be displayed on fron-end
        $generated_leave_dates=[];
        for ($i = 0; $i < $leaveDays; $i++) {
            $date_format_1 = date("Y-m-d", strtotime($start_date . " +" . $i . " days"));
            $date_format_2 = date("d-m-Y", strtotime($start_date . " +" . $i . " days"));

            // After getting data see, if consider_holiday is Active/De-active
            if($converting_leave_info->consider_holiday == "Yes"){
                $generated_leave_dates["format_1"][] = $date_format_1;
                $generated_leave_dates["format_2"][] = $date_format_2;
            }else{
                $val = $this->staff_leave->getHoliday($date_format_1);
                // Note: test holiday for sunday and saturday instead check with staff shift type of 'Week-off' and 'Holiday';
                if ($val > 0 || $val == 0 && (date('w', strtotime($date_format_1)) == 0 || ((date('w', strtotime($date_format_1)) == 6)))) {
                    continue;
                }else{
                    $generated_leave_dates["format_1"][] = $date_format_1;
                    $generated_leave_dates["format_2"][] = $date_format_2;
                }
            }
        }

        $total_no_of_days_leave_taken=$converting_leave_info->noofdays;
        $total_no_of_leave_dates=count($generated_leave_dates["format_1"]);

        $generate_available_no_of_days_leave_can_taken_for_dates=[];
        foreach($generated_leave_dates["format_1"] as $key => $date){
            if($total_no_of_days_leave_taken/$total_no_of_leave_dates==1){
                $total_no_of_days_leave_taken--;
                $generate_available_no_of_days_leave_can_taken_for_dates[$key]=1;
            }else{
                if($total_no_of_days_leave_taken>0.50){
                    $total_no_of_days_leave_taken--;
                    $generate_available_no_of_days_leave_can_taken_for_dates[$key] = 1.00;
                }else{
                    $total_no_of_days_leave_taken= $total_no_of_days_leave_taken-0.5;
                    $generate_available_no_of_days_leave_can_taken_for_dates[$key] = 0.50;
                }
            }
        }

        // Bring all the available leave categories excuding they applied
        $leave_categories=$this->staff_leave->get_available_staff_leave_categories($converting_leave_info->leave_v2_category_id,$converting_leave_info->staff_id, $converting_leave_info->leave_year_id);

        $data["available_leave_dates"]=$generated_leave_dates;
        $data["available_no_of_days_for_each_date"]=$generate_available_no_of_days_leave_can_taken_for_dates;
        $data["available_leave_categories"] = $leave_categories["available_leave_categories"];
        $data["original_available_leave_categories_with_balance_quota"] = $leave_categories["original_available_leave_categories_with_balance_quota"];
        $data["leave_info"] = $converting_leave_info;

        echo json_encode($data);
    }

    public function convert_staff_leaves_from_one_leave_type_to_another(){
        $is_multi_level_leave_approver_mode_enabled=$this->settings->getSetting("enable_multi_level_leave_approver_mode");
        $leave_info=$_POST["leaveInfo"];

        $leavesToConvert = $_POST["leavesToConvert"];
        $leave_id= $leave_info["leave_v2_staff_id"];
        $old_leave_category_id = $leave_info["leave_category_id"];

        // check for what if all the converted leave has same leave category ids
        $isLeaveConverted=false;
        if(empty($leavesToConvert)){
            echo json_encode(["status" => 0, "message" => "Found empty leave convert request, Please try again!"]);
            return;
        }

        $total_applied_leave_categories_with_leave_applied_count=[];
        foreach($leavesToConvert as $key => $val){
            if($old_leave_category_id!=$val["leave_category"]){
                $isLeaveConverted=true;
            }
            // accumulate all the applied leave categories to compare with actual leave quota
            if(array_key_exists($val["leave_category"],$total_applied_leave_categories_with_leave_applied_count)){
                $total_applied_leave_categories_with_leave_applied_count[$val["leave_category"]]+=$val["noofdays"];
            }else{
                $total_applied_leave_categories_with_leave_applied_count[$val["leave_category"]]=$val["noofdays"];
            }
        }

        if(!$isLeaveConverted){
            echo json_encode(["status" => 0, "message" => "Found no changes in the leave, Please try again!"]);
            return;
        }

        // checking for available leave quotas
        $leave_categories = $this->staff_leave->get_available_staff_leave_categories($old_leave_category_id, $leave_info["staff_id"], $leave_info["leave_year_id"]);
        if(!empty($leave_categories)){
            $available_leave_categories=$leave_categories["total_has_quota_leave_categories_with_balance_quota"];

            if(!empty($available_leave_categories)){
                foreach($total_applied_leave_categories_with_leave_applied_count as $leave_category => $applied_leave_count){
                    if(array_key_exists($leave_category,$available_leave_categories)){
                        if($available_leave_categories[$leave_category]<$applied_leave_count){
                            echo json_encode(["status"=>0,"message"=>"No sufficient leave to convert!"]);
                            return;
                        }
                    }
                }
            }
        }
        $reason = "Cancelling this leave due to leave convert request";

        $leave = $this->staff_leave->get_leave_final_status($leave_id);

        // for multi-level
        if($is_multi_level_leave_approver_mode_enabled==1){
            // Cancelling leave
            if($leave->final_status != 3 && $leave->final_status != 4) {
                $this->staff_leave->cancelLeave($leave_id, $reason);
            }else{
                echo json_encode(["status" => 0, "message" => "Leave is either got rejected or cancelled earlier, Please try again!"]);
                return;
            }

            // Applying leave
            $leave_info["source_convert_leave_id"] = $leave_info["id"];
            foreach($leavesToConvert as $key => $val){
                // Previous leave
                if($val["leave_category"]==$old_leave_category_id){
                    if($leave_info["final_status"]==1 || $leave_info["final_status"]==2){
                        // change from_date, to_date and no_of_days
                        // It is the same leave and it is approved, so in this case we just need to dump this  data as it is
                        $leave_info["from_date"] = date("Y-m-d", strtotime($val["from_date"]));
                        $leave_info["to_date"] = date("Y-m-d",strtotime($val["to_date"]));
                        $leave_info["noofdays"] = $val["noofdays"];
                        $leave_info["reason"] = $val["reason"];
                        $leave_info["selection_type"] = $val["selection_type"];
                        // Insert this as it is in the DB.
                        $this->staff_leave->insert_approved_staff_leave_from_leave_convert($leave_info);
                    }else{
                        $val["enable_leave_auto_approval_by_admin"] = $this->staff_leave->getStaffLeaveAutoApprovalPermissionForAdmin($val["leave_category"]);
                        $insert = $this->staff_leave->save_leave_appication_3level($val);
                    }
                }else{
                    $val["enable_leave_auto_approval_by_admin"] = $this->staff_leave->getStaffLeaveAutoApprovalPermissionForAdmin($val["leave_category"]);
                    $val["source_convert_leave_id"]=$leave_info["source_convert_leave_id"];
                    $insert = $this->staff_leave->save_leave_appication_3level($val);
                }
            }
            echo json_encode(["status" => 1, "message" => "Leave Converted successfully!"]);
            return;
        }else{
            // for single-level
            // Cancelling leave
            if($leave->status != 3 && $leave->status != 4) {
                $this->staff_leave->cancelLeave($leave_id, $reason);
            } else {
                echo json_encode(["status" => 0, "message" => "Leave is either got rejected or cancelled earlier, Please try again!"]);
                return;
            }
            // Applying leave
            $leave_info["source_convert_leave_id"] = $leave_info["id"];
            foreach ($leavesToConvert as $key => $val) {
                // New leave
                if ($val["leave_category"] == $old_leave_category_id) {
                    if ($leave_info["status"] == 1 || $leave_info["status"] == 2) {
                        // change from_date, to_date and no_of_days
                        // It is the same leave and it is approved, so in this case we just need to dump this  data as it is
                        $leave_info["from_date"] = date("Y-m-d", strtotime($val["from_date"]));
                        $leave_info["to_date"] = date("Y-m-d", strtotime($val["to_date"]));
                        $leave_info["noofdays"] = $val["noofdays"];
                        $leave_info["reason"] = $val["reason"];
                        $leave_info["selection_type"] = $val["selection_type"];
                        // Insert this as it is in the DB.
                        $this->staff_leave->insert_approved_staff_leave_from_leave_convert($leave_info);
                    } else {
                        $val["enable_leave_auto_approval_by_admin"] = $this->staff_leave->getStaffLeaveAutoApprovalPermissionForAdmin($val["leave_category"]);
                        $insert = $this->staff_leave->save_leave_appication($val);
                    }
                } else {
                    $val["enable_leave_auto_approval_by_admin"] = $this->staff_leave->getStaffLeaveAutoApprovalPermissionForAdmin($val["leave_category"]);
                    $val["source_convert_leave_id"] = $leave_info["source_convert_leave_id"];
                    $insert = $this->staff_leave->save_leave_appication($val);
                }
            }
            echo json_encode(["status" => 1, "message" => "Leave Converted successfully!"]);
            return;
        }
    }

    public function getPreviousLeaves(){
        $response = $this->staff_leave->getPreviousLeaves($_POST);
        echo json_encode($response);       
    }
}