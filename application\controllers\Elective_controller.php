<?php

class Elective_controller extends CI_Controller 
{
               
	public function __construct()
	{
		parent::__construct();
    $this->load->model('elective_model');
    $this->load->model('student/Student_Model','student');
    if (!$this->ion_auth->logged_in())
      {
        redirect('auth/login', 'refresh');
      }
	}

  public function index()
  {
    $data['classlist']       = $this->elective_model->getClassList(); 
    $data['elective']        = $this->elective_model->getElective(); 

    $data['main_content']    = 'elective/index';
    $this->load->view('inc/template', $data); 
  }

  public function getElectiveGroup()
  {
    $data['elective_group']  = $this->elective_model->getElectiveGroup(); 
    $data['main_content']    = 'elective/addElectiveGroup';
    $this->load->view('inc/template', $data); 
  }

  public function saveElectiveGroup()
  {
    if($this->elective_model->saveElectiveGroup()) {
      $this->session->set_flashdata('flashSuccess', 'Insertion Successful');
      redirect('Elective_controller/getElectiveGroup'); 
    } 
    $this->session->set_flashdata('flashError', 'Insertion Failed on Elective Group');
    redirect('Elective_controller/getElectiveGroup'); 
    
  }

  public function deleteElectiveGroup($id) {
    $this->elective_model->deleteElectiveGroup($id);
    $this->session->set_flashdata('flashSuccess', 'Delection Successful');
    redirect('Elective_controller/getElectiveGroup'); 
  }

  public function saveElective()
  {
    if($this->elective_model->saveElective()) {
      $this->session->set_flashdata('flashSuccess', 'Insertion Successful');
      redirect('Elective_controller'); 
    } 
    $this->session->set_flashdata('flashError', 'Insertion Failed on Elective Group');
    redirect('Elective_controller'); 
    
  }

  public function deleteElective($id) {
    $this->elective_model->deleteElective($id);
    $this->session->set_flashdata('flashSuccess', 'Delection Successful');
    redirect('Elective_controller'); 
  }


  public function getStudentsAndElectives() {

    $data['classlist']       = $this->elective_model->getClassList(); 
    $data['main_content']    = 'elective/selectClass';
    $this->load->view('inc/template', $data); 
  }

  private function _prepareElectiveData($elective_data)
  {
     $tmp = [];
     $names = [];
     foreach ($elective_data as $key => $value) {
       $tmp[$value['subject_name']][] = $value;
     }

     return $tmp;
  }


  public function assignStudentsAndElectives() {

    $input = $this->input->post();

    //checking if exists

    $data['section'] = $classSectionId = $input['section'];
    $existing_elective_data = $this->elective_model->getStudentElectiveByClassAndSection($input['class_name'],$classSectionId);

    if(!empty($existing_elective_data)) {

      $data['all_details']     = $this->_prepareStudentElectiveData($existing_elective_data);
      $data['type']            = 'show';
      $data['main_content']    = 'elective/confirmStudentAndElectives';
      $this->load->view('inc/template', $data);

    } else { 
      
      $elective_data = $this->elective_model->getElectiveByClass($input['class_name']);

      $flag = false;

      if(!empty($elective_data))
      {
        $data['elective'] = $this->_prepareElectiveData($elective_data);
        $flag = true;
      }

      if($flag) {
        $data['student'] = $this->elective_model->getStudentsByClassAndSection($input['class_name'],$input['section']);

        if(!empty($data['student'])) {
          $data['main_content']    = 'elective/assignStudentAndElectives';   
          // echo '<pre>'; print_r($data); die();       
          $this->load->view('inc/template', $data);
        }
        else {
          $this->session->set_flashdata('flashError', 'No students assigned for class '.$input['class_name'].' section '.$input['section']);
          redirect('Elective_controller'); 

        }
      } else {
        $this->session->set_flashdata('flashError', 'No Electives assigned for Class Section');
        redirect('Elective_controller'); 
      } 
    }
  }

  private function _prepareStudentElectiveData($elective_data)
  {
     $tmp = [];
     $names = [];
     foreach ($elective_data as $key => $value) {
       $tmp[$value['group']][$value['subject']][] = $value;
     }

     return $tmp;
  }

  public function confirmStudentsAndElectives() {

    $input = $this->input->post();

    $all_input_details = [];

    foreach ($input as $key => $value) {

      $tmp = explode('_', $value);
      $tmp1['group'] = $tmp[0];
      $tmp1['subject'] = $tmp[1];
      $tmp1['studentno'] = $tmp[2];
      $tmp1['student'] = $tmp[3];
      $tmp1['subjectId']  = $tmp[4];
      $tmp1['groupId']  = $tmp[5];
      $tmp1['classId']  = $tmp[6];
      $tmp1['classSectionId']  = $tmp[7];
      $all_input_details[] = $tmp1;
    }   

    $data['all_details'] = $this->_prepareStudentElectiveData($all_input_details);
    $data['type']        = 'saveAndConfirm';
    $newdata['StudentAndElectives'] = $data['all_details'];
    $this->session->set_userdata($newdata);

    //echo '<pre>'; print_r($data); die();
    $data['main_content']    = 'elective/confirmStudentAndElectives';
    $this->load->view('inc/template', $data);

  }


  public function showStudentsAndElectives() {

    $input = $this->input->post();
    $all_input_details = [];

    $data['all_details'] = $this->_prepareStudentElectiveData($all_input_details);
    $newdata['StudentAndElectives'] = $data['all_details'];
    $this->session->set_userdata($newdata);
    $data['main_content']    = 'elective/confirmStudentAndElectives';
    $this->load->view('inc/template', $data);

  }


  public function getSavedStudentsAndElectives($class, $section) {
    
    $classSectionId = $this->elective_model->getClassSectionId($class,$section);
    $elective_data = $this->elective_model->getStudentElectiveByClassAndSection($classSectionId->class_id,$classSectionId->id);

    $data['all_details'] = $this->_prepareStudentElectiveData($elective_data);
    $data['type']        = 'show';
    $data['main_content']    = 'elective/confirmStudentAndElectives';
    $this->load->view('inc/template', $data);

  }

  public function saveStudentsAndElectives() {

    $studentAndElectives = $this->session->all_userdata('StudentAndElectives');
    if($this->elective_model->saveStudentElective($studentAndElectives['StudentAndElectives'])) {
      $this->session->unset_userdata('StudentAndElectives');
      $this->session->set_flashdata('flashSuccess', 'Student And Electives Added Successfully');
      redirect('Elective_controller/getSavedStudentsAndElectives/1/1');
    }
  }

  public function getSectionByClass() {
      
    $classid = $_POST['classid'];
    $getclassectioninfo = $this->student->getclassection($classid);
    echo json_encode($getclassectioninfo);
  }


}

