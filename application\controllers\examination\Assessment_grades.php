<?php
class Assessment_grades extends CI_Controller {
    private $yearId;
    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
       redirect('auth/login', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('examination/Assessment_model','assessment_model');
      $this->load->model('examination/Assessment_grades_model', 'assessment_grades');
      
    }

  public function index(){
    $data['grades'] = $this->assessment_grades->getGradingSystems();
    if($this->mobile_detect->isMobile() || $this->mobile_detect->isTablet()) { 
      $data['main_content'] = 'examination/marks_cards/grades_mobile';
    } else {
      $data['main_content'] = 'examination/marks_cards/grades';
    }
    $this->load->view('inc/template', $data);
  }

  public function createGradingSystem(){
    $data['main_content'] = 'examination/marks_cards/add_grades';
    $this->load->view('inc/template', $data);
  }

  public function addGrades(){
    $data['classList'] = $this->assessment_grades->getGradeClass();
    $data['main_content'] = 'examination/marks_cards/add_grades';
    $this->load->view('inc/template', $data);
  }

  public function submitGrades(){
    $status = $this->assessment_grades->addGrades();
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Added grades successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_grades/');
  }

  public function editGrades($id){
    $data['grades'] = $this->assessment_grades->getGrades($id);      
    $data['main_content'] = 'examination/marks_cards/edit_grades';
    $this->load->view('inc/template', $data);
  }

  public function bindGrades(){
    $data['classList'] = $this->assessment_model->getClassess();
    $data['grades'] = $this->assessment_grades->getGradingSystems();
    $data['main_content'] = 'examination/marks_cards/bind_grades';
    $this->load->view('inc/template', $data);
  }

  public function saveBinding(){
    $status = $this->assessment_grades->updateEntityGrades();
    if ($status){
     echo 1;
    }else{
      echo 0;
    } 
  }

  public function updateGrades($id){
    $status = $this->assessment_grades->addGrades($id);
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Grades updated successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_grades/');
  }

  public function getEntitiesByClass(){
    $classId = $_POST['classId'];
    $data['components'] = $this->assessment_grades->getEntitiesOfClass($classId);
    $data['groups'] = $this->assessment_grades->getGroupsOfClass($classId);
    echo json_encode($data);
  }

  public function getGradingSystemById(){
    $gid = $_POST['gid'];
    $gradingSystem = $this->assessment_grades->getGradingSystemBYId($gid);
    echo $gradingSystem->grades;
  }

}