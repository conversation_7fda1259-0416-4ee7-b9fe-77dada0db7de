<?php
class Afl_dashboard extends CI_Controller {
    private $yearId;
    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
       redirect('auth/login', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      // $this->load->model('examination/Afl_subjects_model','subjects_model');
    }

  public function index(){
    $data['permit_subjects'] = $this->authorization->isAuthorized('AFL.SUBJECTS');
    $data['permit_assessments'] = $this->authorization->isModuleEnabled('AFL'); //$this->authorization->isAuthorized('AFL.ASSESSMENTS');
    $data['permit_grading_system'] = $this->authorization->isAuthorized('AFL.GRADING_SYSTEM');
    $data['permit_rubrics'] = $this->authorization->isAuthorized('AFL.RUBRICS');
    $data['permit_marks'] = $this->authorization->isAuthorized('AFL.MARKS');
    $data['permit_student_report'] = $this->authorization->isAuthorized('AFL.STUDENT_REPORT');
    $data['permit_performance_pointers'] = $this->authorization->isAuthorized('AFL.PERFORMANCE_POINTERS');
    $site_url = site_url();
    $data['tiles'] = array(
      [
        'title' => 'Subjects',
        'sub_title' => 'Add/Edit subjects',
        'icon' => 'svg_icons/managesubjects.svg',
        'url' => $site_url.'afl/afl_subjects',
        'permission' => $this->authorization->isAuthorized('AFL.SUBJECTS')
      ],
      [
        'title' => 'Assessments',
        'sub_title' => 'Add/Edit Assessments',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'afl/afl_assessments',
        'permission' => $this->authorization->isModuleEnabled('AFL')
      ],
      [
        'title' => 'Grading Systems',
        'sub_title' => 'Add/Edit grading system',
        'icon' => 'svg_icons/createelectivegroups.svg',
        'url' => $site_url.'afl/afl_grading_system',
        'permission' => $this->authorization->isAuthorized('AFL.GRADING_SYSTEM')
      ],
      [
        'title' => 'Performance Pointers',
        'sub_title' => 'Add/Edit performance pointers',
        'icon' => 'svg_icons/performancepointers.svg',
        'url' => $site_url.'afl/afl_performance_pointers',
        'permission' => $this->authorization->isAuthorized('AFL.PERFORMANCE_POINTERS')
      ],
      [
        'title' => 'Student Report',
        'sub_title' => 'Student wise report',
        'icon' => 'svg_icons/studentwiseanalysisdata.svg',
        'url' => $site_url.'afl/afl_reports',
        'permission' => $this->authorization->isAuthorized('AFL.STUDENT_REPORT')
      ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'afl/dashboard_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'afl/dashboard_mobile';
    }else{
      $data['main_content'] = 'afl/dashboard'; 	
    }    
    $this->load->view('inc/template', $data);
  }

}