<?php

class Email_controller extends CI_Controller {
    private $yearId;
	function __construct()
	{
     	parent::__construct();
      	if (!$this->ion_auth->logged_in()) {
        	redirect('auth/login', 'refresh');
		}
		if(!$this->authorization->isAuthorized('COMMUNICATION.MODULE')) {
			redirect('dashboard', 'refresh');
		}
		$this->yearId = $this->acad_year->getAcadYearId();
		$this->load->model('class_section');
		$this->load->model('email_model');
	}

	public function index(){
		$data['classes'] = $this->class_section->getAllClassess();
		$data['batches'] = $this->class_section->get_batches();
		$data['class_section'] = $this->class_section->getAllClassSections();
		$this->load->model('staff/Staff_Model');
		$data['staff_details'] = $this->Staff_Model->getAll_Staff();
		// echo "<pre>"; print_r($data); die();
		$data['main_content'] = 'email/index';
        $this->load->view('inc/template', $data);
	}


	public function email_templates(){
		$data['category'] = $this->email_model->get_all_category();
		$data['email_template'] = $this->email_model->get_all_templates();
        if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'email/template_index_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'email/template_index_mobile';
        }else{
			$data['main_content'] = 'email/template_index';     	
        }
        $this->load->view('inc/template', $data);
	}

	public function sms_templates(){
		$sms_template = $this->email_model->get_all_sms_templates();
		$preDefined = json_decode($this->settings->getSetting('sms_templates'));
		$category = [];
		$templateName = [];
		foreach ($preDefined as $cat => $value) {
			foreach ($value->Items as $v) {
				array_push($templateName, $v);
			}
			array_push($category, $value->category);
		}
		$data['category'] =  $category;
		$mergePreDefined = [];
		$temp = [];
		foreach ($preDefined as $cat => $value) {
			foreach ($value->Items as $v) {
				$preTemplate = new stdClass();
				$defTemplate = new stdClass();
				$definedTemp = [];
				if (!empty($sms_template)) {
					foreach ($sms_template as $key => $tem) {
						if ($v === $tem->name) {
							$preTemplate->id = $tem->id;
							$preTemplate->category = $tem->category;
							$preTemplate->name = $tem->name;
							$preTemplate->content = $tem->content;
							$preTemplate->acad_year_id = $tem->acad_year_id;
							array_push($temp, $tem->name);
						}else{
							if(!in_array($tem->name, $temp))
								array_push($definedTemp, $tem);			
							$preTemplate->category = $value->category;
							$preTemplate->name = $v;
						}
					}
				}else{
					$preTemplate->category = $value->category;
					$preTemplate->name = $v;
				}
				
				array_push($mergePreDefined, $preTemplate);
			}
		}
		$data['preDefined'] = $mergePreDefined;
		$data['defined'] = $definedTemp;

        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'sms/template_index_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'sms/template_index_mobile';
        }else{
          $data['main_content'] = 'sms/template_index';    	
        }

		
        $this->load->view('inc/template', $data);
	}

	public function add_email_template(){
		// $template = $this->email_model->get_all_templates_name();
		$preDefined = json_decode($this->settings->getSetting('email_templates'));
		$data['email_template'] = $preDefined;
        if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'email/template_add_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'email/template_add_mobile';
        }else{
			$data['main_content'] = 'email/template_add';     	
        }
        $this->load->view('inc/template', $data);
	}

	public function add_sms_template(){
		$template = $this->email_model->get_all_templates_name_sms();
		$preDefined = json_decode($this->settings->getSetting('sms_templates'));
		$category = [];
		$templateName = [];
		foreach ($preDefined as $cat => $value) {
			foreach ($value->Items as $v) {
				array_push($templateName, $v);
			}
			array_push($category, $value->category);
		}
		$data['category'] =  $category;
		$data['sms_template'] = array_diff($template, $templateName);
		$data['main_content'] = 'sms/template_add';
        $this->load->view('inc/template', $data);
	}

	public function create_email_template(){
		$result = $this->email_model->insert_email_template();
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully inserted');
		}else{
			$this->session->set_flashdata('flashError', 'Something went wrong');
		}
		redirect('email_controller/email_templates');
	}

	public function create_sms_template(){
		$result = $this->email_model->insert_sms_template();
		if ($result == 2) {
			$this->session->set_flashdata('flashError', 'Template name already exists.');
		}else{
			if ($result) {
				$this->session->set_flashdata('flashSuccess', 'Successfully inserted');
			}else{
				$this->session->set_flashdata('flashError', 'Something went wrong');
			}
		}
		redirect('email_controller/sms_templates');
	}

	public function get_sms_template() {
		$template_id = $_POST['template_id'];
		echo json_encode($this->email_model->get_sms_template($template_id));
	}

	public function edit_email_template($id){
		$template = $this->email_model->get_all_templates_name();
		$preDefined = json_decode($this->settings->getSetting('email_templates'));
		$data['email_template'] = $template;
		if(!empty($preDefined)){
			$data['email_template'] = array_unique(array_merge($preDefined,$template));
		}
		$data['edit_email'] = $this->email_model->edit_email_templatebyId($id);
        if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'email/template_edit_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'email/template_edit_mobile';
        }else{
			$data['main_content'] = 'email/template_edit';      	
        }

		
        $this->load->view('inc/template', $data);
	}

	public function edit_sms_template($id){
		$template = $this->email_model->get_all_templates_name_sms();
		$preDefined =json_decode($this->settings->getSetting('sms_templates'));

		$category = [];
		$templateName = [];
		foreach ($preDefined as  $value) {
			foreach ($value->Items as $v) {
				array_push($templateName, $v);
			}
			array_push($category, $value->category);
		}
		$data['category'] =  $category;
		$data['sms_template'] = array_diff($template, $templateName);
		$data['edit_sms'] = $this->email_model->edit_sms_templatebyId($id);
		$data['main_content'] = 'sms/template_edit';
        $this->load->view('inc/template', $data);
	}

	public function delete_email_template($id){
		$result = $this->email_model->delete_email_templatebyId($id);
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully Deleted');
		}else{
			$this->session->set_flashdata('flashError', 'Something went wrong');
		}
		redirect('email_controller/email_templates');
	}

	public function delete_sms_template($id){
		$result = $this->email_model->delete_sms_templatebyId($id);
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully Deleted');
		}else{
			$this->session->set_flashdata('flashError', 'Something went wrong');
		}
		redirect('email_controller/sms_templates');
	}

	public function update_email_template($id){
		$result = $this->email_model->update_email_templatebyId($id);
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully Updated');
		}else{
			$this->session->set_flashdata('flashError', 'Something went wrong');
		}
		redirect('email_controller/email_templates');
	}

	public function update_sms_template($id){
		$result = $this->email_model->update_sms_templatebyId($id);
		if ($result == 1) {
			$this->session->set_flashdata('flashSuccess', 'Successfully Updated');
		}else if($result == -1){
			$this->session->set_flashdata('flashError', 'Template name already exists');
		}else {
			$this->session->set_flashdata('flashError', 'Something went wrong');
		}
		redirect('email_controller/sms_templates');
	}

	public function management_templates() {
		$data['templates'] = $this->email_model->getManagementTemplates();
		$data['main_content'] = 'email/management/index';
        $this->load->view('inc/template', $data);
	}

	public function add_management_email_template() {
		$this->load->model('staff/Staff_Model', 'staff_model');
		$data['staffs'] = $this->staff_model->getAll_Staff();
		$data['main_content'] = 'email/management/add';
        $this->load->view('inc/template', $data);
	}

	public function save_management_template() {
		$status = $this->email_model->save_management_template();
		if ($status) {
			$this->session->set_flashdata('flashSuccess', 'Successfully added');
		}else{
			$this->session->set_flashdata('flashError', 'Something went wrong');
		}
		redirect('email_controller/management_templates');
	}

	public function edit_management_email_template($id) {
		$this->load->model('staff/Staff_Model', 'staff_model');
		$data['staffs'] = $this->staff_model->getAll_Staff();
		$data['template'] = $this->email_model->getManagementTemplate($id);
		$data['main_content'] = 'email/management/edit';
        $this->load->view('inc/template', $data);
	}

	public function update_management_template() {
		$status = $this->email_model->update_management_template();
		if ($status) {
			$this->session->set_flashdata('flashSuccess', 'Successfully updated');
		}else{
			$this->session->set_flashdata('flashError', 'Something went wrong');
		}
		redirect('email_controller/management_templates');
	}

	public function delete_management_email_template($id) {
		$status = $this->email_model->delete_management_template($id);
		if ($status) {
			$this->session->set_flashdata('flashSuccess', 'Successfully deleted');
		}else{
			$this->session->set_flashdata('flashError', 'Something went wrong');
		}
		redirect('email_controller/management_templates');
	}

	public function insert_predefined_template(){
		$status = $this->email_model->insert_preDefinedTemplate();
		if ($status) {
			$this->session->set_flashdata('flashSuccess', 'Successfully added');
		}else{
			$this->session->set_flashdata('flashError', 'Something went wrong');
		}
		redirect('email_controller/sms_templates');
	}

	public function update_predefined_template($id){
		$status = $this->email_model->update_preDefinedTemplate($id);
		if ($status) {
			$this->session->set_flashdata('flashSuccess', 'Successfully updated');
		}else{
			$this->session->set_flashdata('flashError', 'Something went wrong');
		}
		redirect('email_controller/sms_templates');
	}

}