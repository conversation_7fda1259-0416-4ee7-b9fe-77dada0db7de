<div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding padding8px">
            <h3 class="card-title panel_title_new_style">
                <strong>Parent Tickets</strong>
            </h3>
        </div>
        <div class="card-body padding8px">
        <?php
                    //Show summary only if the person is a ticketing admin
                if ($is_ticketing_admin == 1) {
                
            ?>
                <div class="col-md-12 p-0">
                    <div class="card cd_border">
                        <div class="card-header panel_heading_new_style_staff_border">
                            <div class="m-0">
                                <label for="ticket_types">Ticket Filter</label>
                                <select name="ticket_types" class="form-control" id="ticket_types" style="width: 100%;margin-bottom: 2rem;">
                                <?php if($is_ticketing_admin == 1){ ?>
                                        <option value="1" <?php echo $ticket_mode == 1 ? "selected" : "" ?>>Open Tickets</option>
                                        <option value="2" <?php echo $ticket_mode == 2 ? "selected" : "" ?>>Assigned to me</option>
                                        <option value="3" <?php echo $ticket_mode == 3 ? "selected" : "" ?>>All Tickets</option>
                                <?php }else{ ?>
                                        <option value="2" <?php echo $ticket_mode == 2 ? "selected" : "" ?>>Assigned to me</option>
                                <?php } ?>
                                </select>
                                <div class="d-flex justify-content-between" style="width:100%;">
                                    <h3 class="panel-title"><strong>Ticket Summary</strong></h3>
                                </div>
                            </div>
                        </div>
                       
                        
                        
                        <div class="card-body pt-1">
                            <div class="">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <tbody>
                                            <tr><td>Total Open tickets</td><td><?= $ticketsummary->open_tickets; ?></td>
                                            </tr>
                                            <tr><td>Assigned to me</td><td><?= $ticketsummary->assigned; ?> </td>
                                            </tr>
                                            <tr><td>All tickets</td><td><?= ($ticketsummary->all >=1000) ?  $ticketsummary->all.'+' : $ticketsummary->all ?></td>
                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <?php } else if($is_ticketing_admin == 0){?>
                                <div class="m-0">
                                <label for="ticket_types">Ticket Filter</label>
                                <select name="ticket_types" class="form-control" id="ticket_types" style="width: 100%;margin-bottom: 2rem;">
                                <option value="1" <?php echo $ticket_mode == 1 ? "selected" : "" ?>>Open Tickets</option>
                                    <option value="2" <?php echo $ticket_mode == 2 ? "selected" : "" ?>>Assigned to me</option>
                                    <option value="3" <?php echo $ticket_mode == 3 ? "selected" : "" ?>>Total Tickets</option>
                                </select>
                            </div>

                           <?php  } ?>
                        </div> <!--Panel body-->
                    </div> <!--panel-->
                </div> <!--col-md-12-->
            

               
           
            <br>
            <div class="col-md-12 p-0">
                <div class="card cd_border">
                    <div class="card-header panel_heading_new_style_staff_border">
                        <div class="row" style="margin: 0px;">
                            <div class="d-flex justify-content-between" style="width:100%;">
                                <h3 class="panel-title"><strong>
                                    <?php if ($ticket_mode == 1) {
                                        echo "Total Open ";
                                    } else if ($ticket_mode == 2) {
                                        echo "Assigned ";
                                    } else if ($ticket_mode == 3) {
                                        echo "Total ";
                                    } ?>
                                    Tickets</strong></h3>
                            </div>
                        </div>
                    </div>
                
                    <div class="card-body pt-1">
                        <!-- only mobile -->
                        <div class="leaveData">
                            <?php
                                if(empty($list_issue))
                                    echo 'No data available';
                            ?>
                            <?php $i=1; foreach ($list_issue as $key => $list) { ?>
                                <?php if($list->status=='Open'){
                                    $bg='#000';
                                }
                                else{
                                    $bg='#000';
                                }?>
                            <div class="card-body" style="color: <?= $bg ?>">
                                <p><strong><?= $list->ticket_number ?></strong><strong> : </strong><?= $list->title ?></p>
                                <p>
                                    <?php
                                        $status_color = '';
                                        $status_text = $list->status;

                                        if (strtolower($status_text) == 'closed') {
                                            $status_color = '#3DA755';
                                        } else if (strtolower($status_text) == 'open') {
                                            $status_color = '#D93E39';
                                        } else {
                                            $status_color = '#6c757d'; // Default gray for other statuses
                                        }
                                    ?>
                                    <span style="background-color: <?= $status_color ?>; color: white; padding: 4px 12px; border-radius: 15px; font-size: 12px; font-weight: 500; display: inline-block;">
                                        <?= $status_text ?>
                                    </span>
                                </p>
                                <p><strong>Description : </strong>
                                    <?php
                                        if (!empty($list->description))
                                            echo substr($list->description, 0,75).'...';
                                        else 
                                            echo 'No Description';
                                    ?>
                                </p>
                                <p><strong>Assigned to : </strong><?= $list->assigned_to ?></p> 
                                <p>
                                <a  href="<?php echo site_url('parent_ticketing/view_ticket_details/' . $list->id); ?>" class='btn btn-primary  pull-right' style="margin:-5px; margin-right: 3px;" data-placement='top'  data-toggle='tooltip' data-original-title='View and Action'>View</i></a></p>
                                <!--<a style="margin-right:2px" href="<?php // echo site_url('parent_controller/issue_raise_delete'); ?>" class='btn btn-warning  pull-right' data-placement='top'  data-toggle='tooltip' data-original-title='Delete'><i class='fa fa-trash-o'></i></a>
                                <a style="margin-right:2px" href="<?php // echo site_url('parent_controller/issue_raise_edit'); ?>" class='btn btn-warning  pull-right' data-placement='top'  data-toggle='tooltip' data-original-title='Edit'><i class='fa fa-edit'></i></a> </p>-->
                            </div>
                            <div class="col-md-12 line" style="border:1px solid #d0d0d0"></div> 
                            <?php } ?> 

                            <a href="<?php echo site_url('dashboard');?>" id="backBtn"><span class="fa fa-mail-reply"></span></a>

                        </div> 
                    </div>
                </div>
            </div>
        </div>  
    </div>
</div>

<a href="<?php echo site_url('communication_dashboard');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>

<script>
    $("#ticket_types").change(e=>{
        const ticketType=$("#ticket_types").val();
        const name="<?php echo site_url('parent_ticketing/view_assigned_tickets') ?>"
        window.location.href=`${name}/${ticketType}`;
    })
</script>