<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  24 March 2018
 *
 * Description:  Contains the configuration details for NorthHills.
 *
 * Requirements: PHP5 or above
 *
 */
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| School General Settings
| -------------------------------------------------------------------------
| All the meta data for the school is stored in this section.
*/

/*
| -------------------------------------------------------------------------
| School Meta data
|---------------------------------------------------------------------------
*/

/*
| -------------------------------------------------------------------------
| Admission Number
| Config settings for generating admission number - 
| manual: (TRUE/FALSE) - If false, generate admission number automatically.
| admission_generation_algo - Algorithm to use to generate admission number. Vaues -
| 1) NEXTELEMENT - default
| 2) WPL
| 3) NH
| infix - The infix to use in the algo
| digit-count - ?
| index_offset - The starting number for admissions.
|---------------------------------------------------------------------------
*/
$config['school_name'] = 'Yashasvi International School';
$config['school_name_line1'] = 'YASHASVI INTERNATIONAL SCHOOL';
$config['school_name_line2'] = 'Kanakapura Main Road, Near Thalagattaura ';
$config['school_name_line3'] = 'Police Station, Bangalore - 560 109';
$config['school_short_name'] = "yashasvi";
$config['school_image'] = 'assets/img/yashasvi.jpg';
$config['school_logo'] = 'assets/img/yashasvi_logo.png';
$config['company_logo'] = 'assets/img/yashasvi_logo.png';
$config['company_name'] = 'nextelement';
$config['login_background'] = 'assets/img/yashasvi.jpg';
$config['favicon'] = 'https://s3.amazonaws.com/nextelement-common/scicon16pxnext.png';

$config['admission_number'] = [ 
                                'manual' => TRUE, 
                                'admission_generation_algo' => 'YASHASVI',
                                'infix' => 'YASH',
                                'digit_count' => 5,
                                'index_offset' => 0
                              ];

/*
| -------------------------------------------------------------------------------------
| Timetable
| -------------------------------------------------------------------------------------
| header_class_section_id: Provide the class_section id of the section timetable from which the timetable header should be used.
| -------------------------------------------------------------------------------------
*/
$config['timetable']['header_class_section_id'] = 9;
$config['timetable']['timetable_template_for_staff'] = 1;
$config['timetable']['timetable_template_for_room'] = 1;
$config['timetable']['consider_4_alloc_p6_for_cs_lab'] = 0;

/*
| --------------------------------------------------------------------------
| Modules
| Define all the modules enabled for the school. The ones that are not here aren't enabled for the school
|
| Basic modules allowed for all schools by default - 
| SCHOOL_ADMIN - Enable School Admin in sidemenu.
| PROFILE - Enable Staff Profile
| ACTIVITY - Enable Activity in sidemenu.
| COMMUNCIATION - Enable Communication in sidemenu.
| PERMISSIONS - Manage roles, assign privileges.
| CLASS_MASTER - Add classes.
| STUDENT_MASTER - CRUD Student data.
| STAFF_MASTER - CRUD Staff data.
| SUBJECTS_MASTER - CRUD Subjects, add and assign elective subjects to students.
| STAFF_LOGIN - Staff console, Staff dashboard.
| 
| The following are optional modules and are enabled based on school requirements -
| REGISTER - Enable Register in sidemenu
| STUDENT_ATTENDANCE - Take student attendance.
| LEAVE_MANAGEMENT: Enable Leave management in sidemenu.
| STUDENT_LEAVE - Students can apply leave.
| STAFF_LEAVE - Staff can apply leave.
| STAFF_ATTENDANCE - Take Staff attendance.
| EXAMINATION - Create and publish Exam timetable and portions, Marks card generation.
| TIMETABLE - Add Staff, Subject and Section association, Construct Timetable.
| SUBSTITUTION - Substitute absent staff
| FEES - Create Fee Structure, Pay Fees.
| LIBRARY - CRUD Books, manage Library transacations.
| INVENTORY - CRUD Inventory, manage inventory transacations.
| PARENTS_LOGIN - Parents console, Parents dashboard.
| ONLINE_EXAM - Teachers create online examination. Students can take the exam under Parent's guidance.
| ASSIGNMENT - Teachers can create assignment. Parents can view assignment.
| PUBLICATIONS - Teachers can create publications for school, class, section and students.
| PTM - To handle Parent-Teachers' meeting.
| PARENT_INITIATIVE - Parents can take an initiative in the school.
| STAFF_INITIATIVE - Staff can take an initiative in the school.
| WORKSHOP - Similar to Events.
| COMPETITION - CRUD Competitions.
| EVENTS - CRUD Events.
| VISITOR - Track visitors.
| STUDENT_EMERGENCY_EXIT - Track students leaving the class in between the day.
| SCHOOL_CALENDAR - Enables entering holiday list, events, competitions, examination calendar, etc.
| TRANSPORT - CRUD Transport.
| SMS - Send SMS to School, Class, Section or Student.
| REPORTS - Access to reports side-menu bar.
| 
| --------------------------------------------------------------------------
*/
$config['modules'] = 
array( 'SCHOOL_ADMIN', 'STUDENT_MASTER', 'STAFF_MASTER','REPORTS','TIMETABLE','SUBSTITUTION', 'BUILDING_MASTER','STAFF_LOGIN','COMPETITION','VISITOR','REGISTER','COMMUNICATION','ACTIVITY','TASKSTODO','PARENTS_LOGIN','FEES_CONCORDE','STUDENT_LEAVE','SCHOOL_CALENDAR','INVENTORY','STAFF_LEAVE','EXAMINATION','ROOM_BOOKING', 'STUDENT_ATTENDANCE','STUDENT_OBSERVATION','TRANSPORT','PERMISSIONS','FEES');

/**
 * 
 * Modules that should be shown in Parent module
 * STUDENT_LEAVE: Shows Student Leave
 * ATTENDANCE: Shows Student Attendance
 * CIRCULARS: Shows Student Circulars
 * SMS: Shows Student Messages
 * ASSESSMENTS: Shows Student Assessments
 * SCHOOL_CALENDAR: Shows Student School Calendar
 * PARENT_INITIATIVE: Show Parent Initiative
 * FEES: Shows Fees
 * PTM: Parents Teachers' meeting
 */
$config['parent_modules'] = 
array('CIRCULARS', 'SMS', 'TIMETABLE', 'SCHOOL_CALENDAR', 'MARKS_CARD', 'FEES','FLASH_NEWS','STUDENT_LEAVE','ATTENDANCE', 'ATTENDANCE_SUMMARY');
/*
| --------------------------------------------------------------------------
| Board
| Define all the boards the school supports in this array.
|
| Allowed values are -
| 1 -> 'State', 2->CBSE, 3 -> 'ICSE', 4-> 'IGCSE'
| --------------------------------------------------------------------------
*/
$config['board'] = array(
    '2' => 'CBSE',
    '1' => 'STATE'
  );

/*
| --------------------------------------------------------------------------
| Medium
| Define all the mediums the school supports in this array.
|
| Allowed values are -
| 1 -> English, 2 -> Kannada, 3 -> Hindi
| --------------------------------------------------------------------------
*/
$config['medium'] = array(
  '1' => 'English'
);

/*
| --------------------------------------------------------------------------
| classType
| Define the type of class per the school standards
|
| Values here will just show up when creating a class
| --------------------------------------------------------------------------
*/
$config['classType'] = array(
  '1' => 'Pre-Nursery (PREP-1 TO  PREP-3)',
  '2' => 'High School (1 TO 10)',
  '3' => 'Pre-universty (1PUC TO 2PUC)',
);

/*
| --------------------------------------------------------------------------
| Admission Type
| Define all the admission types the school supports in this array.
|
| Allowed values are -
| 1 -> Re-admission, 2 -> New Admission
| --------------------------------------------------------------------------
*/
$config['admission_type'] = array(
  '1'=>'Re-admission',
  '2'=>'New Admission'
);

/*
|
| Attendance
|
|
*/
$config['attendance']['absentee_sms'] = 'Your ward %std_name% of %cs_name% is absent today %date%. Kindly ignore the sms if you have already informed the school. Principal - Yashasvi International School';
$config['attendance']['latecomer_sms'] = 'Your ward %std_name% of %cs_name% has come to school late today %date%. Principal - Yashasvi International School';

/*
| --------------------------------------------------------------------------
| Admission Status
| Define all the admission status the school supports in this array.
|
| Allowed values are -
|1 -> Pending, 2 -> Approved, 3 -> Rejected 4 -> Alumni
| --------------------------------------------------------------------------
*/

$config['admission_status'] = array(
  '1'=>'Pending',
  '2'=>'Approved',
  '3'=>'Rejected',
  '4'=>'Alumni'
);

/*
| --------------------------------------------------------------------------
| Staff Selection Status
| Define all the Staff status the school supports in this array.
|
| Allowed values are -
|1 -> Pending, 2 -> Approved, 3 -> Rejected 4 -> Resigned
| --------------------------------------------------------------------------
*/
$config['staff_status'] = array(
  '1'=>'Pending',
  '2'=>'Approved',
  '3'=>'Rejected',
  '4'=>'Resigned',
  '5'=>'Retired'
);

/*
| --------------------------------------------------------------------------
| RTE Options
| Define all the RTE Options the school supports in this array.
|
| Allowed values are -
| 1 -> RTE, 2 -> Non-RTE
| --------------------------------------------------------------------------
*/
$config['rte'] = array(
  '2'=>'Non-RTE',
  '1'=>'RTE'
);


/*
| --------------------------------------------------------------------------
| Boarding
| Define all the boarding types the school supports in this array.
|
| Allowed values are -
| 1-> Day School, 2 -> Residential, 3-> Special Education
| --------------------------------------------------------------------------
*/
$config['boarding'] = array(
  '1'=>'Day School',
  '3'=>'Special Education'
);


/*
| --------------------------------------------------------------------------
| Category
| Define all the student categories the school supports in this array.
| 
| Allowed values are -
| '1' => 'General',
| '2' => 'SC/ST',
| '3' => 'CATEGORY IIA',
| '4' => 'CATEGORY IIB',
| '5' => 'CATEGORY IIIA',
| '6' => 'OBC'
| eg: $config['category'] = array('General', 'SC/ST');
| --------------------------------------------------------------------------
*/
$config['category'] = array(
  '1' => 'General',
  '2' => 'SC/ST',
  '3' => 'CATEGORY IIA',
  '4' => 'CATEGORY IIB',
  '5' => 'CATEGORY IIIA',
  '6' => 'OBC'
);

/*
|-----------------------------------------------------------------------
| This setting is used to set the allocation template for online room booking. Set this value to an existing (i.e. already created) timetable_template->id.
|-----------------------------------------------------------------------
*/

$config['online_room_booking_template_id'] = '2';

/*
| --------------------------------------------------------------------------
| Address Types
| Define all the address types the school likes to collect from student and parents.
|
| Allowed values are -
|(1: Present Address and 2: Permanent Address) or (1: Office Address And 2: Home Address)
| --------------------------------------------------------------------------
*/

$config['address_types'] = array(
  'Student'=> array("0" => "Present Address"),
  'Father'=>  array(),
  'Mother'=>  array()
);

/*
| --------------------------------------------------------------------------
| Staff Profile
| All configuration parameters for Staff Profile edit
|
| Allowed values are -
| enableStaffProfileEdit: 
| 1: Enables Staff to edit their profile in staff App.
| 0: Disables Staff to edit their profile in staff App.
| --------------------------------------------------------------------------
*/

$config['staff_profile']['enableStaffProfileEdit'] = 0;
$config['staff_profile']['enableQualificationEdit'] = 0;

/*
| -------------------------------------------------------------------------
| Fees.
| -------------------------------------------------------------------------
| Fee Specific settings
*/

/*
| --------------------------------------------------------------------------
| Filter Criteria
|
| This is used for creating Master Fee Structure.
|
| Different schools have different filter criteria. For eg: NPS RNR uses academic year
| when student joined and whether he is RTE candidate to determine Fee Structure.
| NPS CNP uses class the student is going into and RTE candidature.
| Specify here the filter criteria to be used.
| This should be the same table column name you use in Student.
|
| The currently supported school criteria are - academic_year_of_joining, rte, class,
| medium, category, admission_type, boards, boarding
| --------------------------------------------------------------------------
|   Allowed values are -
|   0 -> 'admission_type',
|   1 -> 'medium
|   2 -> 'rte
|   3 -> 'category
|   4 -> 'academic_year_of_joining
|   5 -> 'class
|   6 -> 'boards
|   7 -> 'boarding
*/
$config['fees']['filter_criteria'] = array('admission_type','class','rte');

/*
| ------------------------------------------------------------------------------
| Fee Components
|
| This is used to create Fee Components in Master.
|
| Different schools have different fee components. We assume that the components are same across all
| fee Structures. If this is wrong, we have to handle this as a database table rather than config.
|
| Add all the different components in 'Allocation Order' (See Allocation algorithm).
|      
| --------------------------------------------------------------------------------
*/

$config['fees']['components'] = array(
  array ( 'name' => 'Sri Dharmagiri Educational Trust','concession_eligible' => FALSE ),
  array ( 'name' => 'Tuition Fee','concession_eligible' => TRUE ),
  array ( 'name' => 'Miscellaneous', 'concession_eligible' => FALSE ),
);


$config['fees']['fee_type'] = array(
  array ('name'=>'Academic fee','value'=>'1', 'required' => TRUE, 'parent_view_enabled'=>TRUE, 'icon'=>'fa fa-inr'),
  array ('name'=>'Transporation fee','value'=>'2', 'required' => TRUE, 'parent_view_enabled'=>TRUE, 'icon'=>'fa fa-truck'),
  array ('name'=>'Facilities fee','value'=>'3', 'required' => TRUE, 'parent_view_enabled'=>FALSE, 'icon'=>'fa fa-square'),
  array ('name'=>'Miscellaneous fee','value'=>'4', 'required' => FALSE, 'parent_view_enabled'=>FALSE, 'icon'=>'fa fa-language'),
);

/*
| ------------------------------------------------------------------------------
| Fee Installement Type
|
| This is used to create Fee type.
| Differents type are 
|  1. STANDARD (INSTALLEMENT WILL BE SPLIT BY BASED ON PERCENTAGES PROVIDED)
|  2. NORTHHILL (2ND AND 3RD INSTALLMENT WILL BE FIXED AMOUNT THE 1ST INSTALLMENT WILL TAKE THE |     REMAINING AMOUNT)
|      
| --------------------------------------------------------------------------------
*/
$config['fees']['fee_installment_mode'] = 'STANDARD'; 


$config['fees']['fee_installment'] = array(
  array ('name'=>'1st Installment','installment_per'=>100,'due_date'=>'01-06-2018'),
);


/*
| ----------------------------------------------------------------------------------
| Fee Payment modes
| 
| This is used in Fee payment.
| Add all the payment modes supported by this school
| Allowed values - 

            $config['allowed_payment_modes'] = array(
                            
                              '1'=>'dd',
                              '2'=>'credit card',
                              '3'=>'debit card',
                              '4'=>'cheque'
                              '5'=>'wallet payment'
                              '6'=>'challan'
                              '7'=>'Card (POS)'
                              '8'=> Net Banking (POS)
                              '9'=>'cash',
                            );

| ------------------------------------------------------------------------------------
*/
$config['fees']['allowed_payment_modes'] = array(
  array ('name'=>'cash','value'=>'9', 'reconcilation_reqd' => FALSE),
  array ('name'=>'cheque','value'=>'4', 'reconcilation_reqd' => TRUE),
  array ('name'=>'DD','value'=>'1', 'reconcilation_reqd' => TRUE),
  array ('name'=>'net banking','value'=>'8', 'reconcilation_reqd' => TRUE)
);
/*
| ------------------------------------------------------------------------------
| Fee Mode
|
| This is used to create Fee mode Special Education Students.
|
|      
| --------------------------------------------------------------------------------
*/
$config['fee_modes'] = array(
  'auto' => 'Standard',
  'manual' => 'Flexible',  
);

$config['fee_mode_required'] = TRUE; 

/*
| ----------------------------------------------------------------------------------
| Fee Receipt template
| 
| This is used to generate the fee receipt.
| Add the template file that should be used (probably customized) for this school.
| ------------------------------------------------------------------------------------
*/
$config['fees']['receipt_template'] = 'yashasvi';

/*
| ----------------------------------------------------------------------------------
| Fee Miscellaneous template
| 
| This is used to generate the fee receipt.
| Add the template file that should be used (probably customized) for this school.
| ------------------------------------------------------------------------------------
*/
$config['fees']['receipt_template_misc'] = 'yashasvi_misc';

/*
| ----------------------------------------------------------------------------------
| Fee Transporation template
| 
| This is used to generate the fee receipt.
| Add the template file that should be used (probably customized) for this school.
| ------------------------------------------------------------------------------------
*/
$config['fees']['receipt_template_trans'] = 'yashasvi_transportation';
/*

/*
| ----------------------------------------------------------------------------------
| Fee Transporation template
| 
| This is used to generate the fee receipt.
| Add the template file that should be used (probably customized) for this school.
| ------------------------------------------------------------------------------------
*/
$config['fees']['transport_gprs'] = FALSE;
/*

/*
| ----------------------------------------------------------------------------------
| Fee Transporation template
| 
| This is used to generate the fee receipt.
| Add the template file that should be used (probably customized) for this school.
| ------------------------------------------------------------------------------------
*/
$config['fees']['transport_concession'] = FALSE;
/*
| ----------------------------------------------------------------------------------
| Fee Component Allocation Type
| 
| This is used in Fee transaction.
|
| If TRUE, the allocation algorithm will kick in to allocate the total amount entered across Fee Components.
| If FALSE, the allocation has to be manually keyed in by the user (cashier).
|
| ------------------------------------------------------------------------------------
*/
$config['fees']['component_allocation_type_auto'] = TRUE;

$config['fees']['cComponent_allocation_type_auto'] = FALSE;
/*
| --------------------------------------------------------------------------------------
| Fee Component Allocation algorithm
| 
| This is used in conjunction with Fee transaction.
|
| If Allocation algorithm is AUTO, then the specified algorithm is used to allocate total amount across Fee Components.
|
| Algo 1: 'ALLOCATE_FROM_LEFT_TO_RIGHT' - This algorithm allocates the total amount from left to right as specified in the fee component array above.
| It will allocate the full amount for the component before proceeding to the next.
|
| ----------------------------------------------------------------------------------------
*/
$config['fees']['component_allocation_algorithm_if_auto'] = 'ALLOCATE_FROM_LEFT_TO_RIGHT';


/*
| --------------------------------------------------------------------------------------
| Fee receipt generation
| ----------------------------------------------------------------------------------------
*/

$config['fees']['recipt_number_gen'] = [ 
                      'fee_generation_algo' => 'YASH',
                      'infix' => 'YASH',
                      'digit_count' => 4,
                      ];

/*
| --------------------------------------------------------------------------------------
| Discount for fully paid fee amount  
| ----------------------------------------------------------------------------------------
*/
$config['fees']['discount'] =[
                        'percent'=> 5,
                        'isProvided' => FALSE,
                        'isManual' => FALSE
                       ];

/*
| ------------------------------------------------------------------------------
| Fee Filters supported by school
|
| 
|      
| --------------------------------------------------------------------------------
*/

$config['fees']['supportedFilters'] = [
  'class', 'from_to_date', 'payment_mode', 'boarding', 'admission_type', 'payment_options', 'isRTE', 'collectedBy','stop','route','items'
];

$config['fees']['supportedColumns'] = [
  'slNo', 'date', 'receiptNo', 'stdName', 'className', 'paidAmount', 'concession', 'remarks', 'pType','feeAmount','balance','stop','route','items'
];

/*
| --------------------------------------------------------------------------------------
| SMS Integration
| ----------------------------------------------------------------------------------------
*/

if (ENVIRONMENT !== 'production') {
   $config['smsintergration']  = 
    array('url' => 'alerts.valueleaf.com/api/v4/index.php',
      'api_key' => 'Ae6b5684768b2741508f447a71545290a',
      'sender' => 'YASHAS');
} else {
  $config['smsintergration']  = 
    array('url' => 'alerts.valueleaf.com/api/v4/index.php',
      'api_key' => 'Ae6b5684768b2741508f447a71545290a',
      'sender' => 'YASHAS');
}


/*
| --------------------------------------------------------------------------------------
| SMS Credits per message (unicode/non-unicode)
| ----------------------------------------------------------------------------------------
*/
$config['sms_credit_length']['unicode_single'] = '70';
$config['sms_credit_length']['unicode_multi'] = '60';
$config['sms_credit_length']['non_unicode_single'] = '160';
$config['sms_credit_length']['non_unicode_multi'] = '150';


/*
| --------------------------------------------------------------------------------------
| Circular Related Config
| ----------------------------------------------------------------------------------------
*/
$config['circular']['sms_msg1'] = "You have a new circular with title ";
$config['circular']['sms_msg2'] = ", Please check your School App.";

$config['circular']['categories'] = ['Dairy', 'Events', 'Miscellaneous'];
$config['circular']['from_email'] = "xxxxxx.in";
$config['circular']['enable_email'] = 0;
$config['circular']['enable_sms'] = 0;

/*
| --------------------------------------------------------------------------------------
| User Provisioning Module
| Challenge Fields: class, section, dob, admission_no, class_teacher
| ----------------------------------------------------------------------------------------
*/
//To customize the Instructions Page
$config['user_provisioning']['instruction_page'] = 'npsrnr';


//To customize Validation Fiels
$config['user_provisioning']['challenge_fields'] = [
  ['name'=>'class','help_text'=>''],
  ['name'=>'section','help_text'=>''],
  ['name'=>'dob','help_text'=>'Date should be in DD-MM-YYYY format.'],
  ['name'=>'admission_no','help_text'=>'Please visit XXXXXXXX to know admission number.']
];

//User cridential message for provisioning
$config['user_provisioning']['cridential_message'] = "Your Yashasvi school account is activated. Link: https://yashasvi.nextelement.in/. ";

$config['user_provisioning']['school_code'] = 'yashasvi';

//User manual activation message
$config['user_provisioning']['manual_cridential_part_1'] = "\nGreetings!\nCredentials for your Yashasvi school SchoolElement account.\n\n";

$config['user_provisioning']['manual_cridential_part_2'] = ".\nAndroid App Link: https://play.google.com/store/apps/details?id=com.ne.nextelement\niOS App Link: https://itunes.apple.com/in/app/schoolelement/id1423094042?mt=8\nBrowser Link: https://yashasvi.nextelement.in/.\n\nKindly change your password on your first login. Do not share this SMS to anybody. In case of any support, mail <EMAIL> .";

//provisioning process to be followed
$config['user_provisioning']['process'] = 'direct_activate';

/*
| --------------------------------------------------------------------------------------
| Adding a switch to use either old or new login box.
| ----------------------------------------------------------------------------------------
*/
//$config['loginPageToUse'] = 'old';

/*
|-----------------------------
| EMAIL: General Settings
|-----------------------------
|
| 'settings'   => General settings
|               Can be overriden with the template settings
|               or when calling the send() method
|
| Following configs tested working on gmail account
*/
$config['email']['settings'] = array( 
  'from_email'    => '',
  'smtp_user'     => getenv('SMTP_USER'),
  'smtp_pass'     => getenv('SMTP_PASS'),
  'from_name'     => 'Yashasvi International School',
  'smtp_host'     => getenv('SMTP_HOST'),
  'smtp_port'     => getenv('SMTP_PORT'),
  'protocol'      => 'smtp',
  'smtp_crypto'   => 'ssl',
  'mailtype'      => 'html',
  'charset'       => 'utf-8',
  'newline'       => "\r\n",
);

/*
|-----------------------------
| Templates location
|-----------------------------
|
| 'templates' = Folder located @ application/views/{}
|
| Each template created must have a config in templates
*/

$config['email']['templates'] = 'emails';

/*
|-----------------------------
| Dev Email controls
|-----------------------------
|
|
| Stop remove actual users from DB and sent it to specified 
*/

$config['email']['dev_email'] = '<EMAIL>';

/*
|-----------------------------
| Email Templates
|-----------------------------
|
| 'mailtype'    = Mail type, if not set will use general type
| 'charset'     = Charset, if not set will use general charset
| 'from_email'  = From email, if not set will use general email
| 'from_name'   = From name, if not set will use general name
| 'subject'     = Email Subject
| 'send_email'  = If false, it will send the email to site owner instead of actual user
| 'bcc_admin'   = Add the site admin as a BCC to the email
*/

$config['email']['templates'] = array(
  // Demo
  'demo'   => array(
      'subject'    => 'Test Demo',
      'send_email' => true,
      'bcc_admin'  => false,
  ),
  'forgot_password'   => array(
      'subject'    => '',
      'send_email' => true,
      'bcc_admin'  => false,
  ),
);

// Enable Competition Attendance
$config['competition_attendance'] = false;

// Latecomer Attendance if set to true only absents students would be show else all class students would be displayed.
$config['latecomer_attendance'] = true;

// Emergency Exit requires Visitor entry?.
$config['emergency_exit_visitor'] = false;

//Forgot Password 
$config['forgot_password']['email'] = true;
$config['forgot_password']['mobile'] = true;
