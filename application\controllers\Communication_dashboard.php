<?php

class Communication_dashboard extends CI_Controller {
               
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if(!$this->authorization->isAuthorized('COMMUNICATION.MODULE')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->library('filemanager');
        $this->load->model('Helpdesk_model', 'helpdesk');
        $this->load->model('communication/texting_model');
    }
           
    public function index(){
        $data['permit_cicrular'] = $this->authorization->isAuthorized('CIRCULAR.CREATE') && $this->authorization->isAuthorized('CIRCULAR.MODULE') && $this->authorization->isModuleEnabled('CIRCULAR');
        $data['permit_email'] = $this->authorization->isAuthorized('EMAILS.SEND') && $this->authorization->isAuthorized('EMAILS.MODULE') && $this->authorization->isModuleEnabled('EMAILS');
        $data['permit_cicrular_report'] = $this->authorization->isAuthorized('CIRCULAR.VIEW_REPORT') && $this->authorization->isModuleEnabled('CIRCULAR');
        $data['permit_flash_news'] = $this->authorization->isAuthorized('FLASH_NEWS.MODULE') && $this->authorization->isModuleEnabled('FLASH_NEWS');
        $data['permit_homework'] = $this->authorization->isAuthorized('HOMEWORK.MODULE') && $this->authorization->isModuleEnabled('HOMEWORK');
        $data['permit_student_tasks'] = $this->authorization->isAuthorized('STUDENT_TASKS.MODULE');// && $this->authorization->isModuleEnabled('STUDENT_TASKS');
        $data['permit_homework_report'] = $this->authorization->isAuthorized('HOMEWORK.MODULE') && $this->authorization->isModuleEnabled('HOMEWORK');
        $data['permit_parent_initiative'] = $this->authorization->isAuthorized('COMMUNICATION.PARENT_INITIATIVE') && $this->authorization->isModuleEnabled('PARENT_INITIATIVE');
        $data['permit_ptm'] = $this->authorization->isAuthorized('COMMUNICATION.PTM') && $this->authorization->isModuleEnabled('PTM');
        $data['permit_parent_ticketing'] = $this->authorization->isAuthorized('PARENT_TICKETING.MODULE') && $this->authorization->isModuleEnabled('PARENT_TICKETING');
        $data['permit_parent_ticketing_category'] = $this->authorization->isAuthorized('PARENT_TICKETING.CRUD_CATEGORY') && $this->authorization->isModuleEnabled('PARENT_TICKETING');

        $data['permit_sms'] = $this->authorization->isAuthorized('SMS.SEND_SMS') && $this->authorization->isModuleEnabled('SMS');
        $data['permit_circular_email'] = $this->authorization->isAuthorized('CIRCULARV2.CREATE') && $this->authorization->isModuleEnabled('CIRCULARS_V2');
        $data['permit_sms_report'] = $this->authorization->isAuthorized('SMS.SMS_REPORT') && $this->authorization->isModuleEnabled('SMS');
        $data['permit_sms_delivery_report'] = $this->authorization->isAuthorized('SMS.SMS_DELIVERY_REPORT') && $this->authorization->isModuleEnabled('SMS');
        $data['permit_send_text'] = $this->authorization->isAuthorized('TEXTING.SEND') && $this->authorization->isModuleEnabled('TEXTING');
        $data['permit_texting_report'] = $this->authorization->isAuthorized('TEXTING.REPORT') && $this->authorization->isModuleEnabled('TEXTING');
        $data['permit_load_sms_credits'] = $this->authorization->isSuperAdmin();
        $data['permit_cicrularv2_report'] = $this->authorization->isAuthorized('CIRCULARV2.REPORT') && $this->authorization->isModuleEnabled('CIRCULARS_V2');
        $data['tileName'] = $this->settings->getSetting('circular_tile_name');
        if($data['tileName'] == '') {
            $data['tileName'] = 'Circulars';
        }
        $data['permit_email_template'] = $this->authorization->isAuthorized('COMMUNICATION.EMAIL_TEMPLATE');
        $data['permit_sms_template'] = $this->authorization->isAuthorized('COMMUNICATION.SMS_TEMPLATE');
        $data['permit_sms_template_approval'] = $this->authorization->isAuthorized('COMMUNICATION.SMS_TEMPLATE_APPROVAL');
        $data['permit_group_creation'] = $this->authorization->isAuthorized('COMMUNICATION.GROUP_CREATION');
        $data['permit_communication_report'] = $this->authorization->isAuthorized('COMMUNICATION.REPORT');
        $data['text_credits'] = $this->texting_model->getRemainingSMSCredits();
        // echo "<pre>";print_r($data);die();

        $site_url = site_url();
        $data['tiles'] = array(
          [
            'title' => 'Texting',
            'sub_title' => 'Send SMS/Notifications to students/staff',
	          'icon' => 'svg_icons/texting.svg',
            'url' => $site_url.'communication/texting',
            'permission' => $this->authorization->isAuthorized('TEXTING.SEND') && $this->authorization->isModuleEnabled('TEXTING')
          ],
          [
            'title' => $data['tileName'],
            'sub_title' => 'Send '.$data['tileName'].' to students/staff',
	          'icon' => 'svg_icons/circular.svg',
            'url' => $site_url.'communication/circulars/list',
            // 'mobile_enabled' => 0,
            'tablet_enabled' => 0,
            // 'url' => $site_url.'communication/circulars',
            'permission' => $this->authorization->isAuthorized('CIRCULARV2.CREATE') && $this->authorization->isModuleEnabled('CIRCULARS_V2')
          ],
          [
            'title' => 'Send Email',
            'sub_title' => 'Send Emails to students/staff',
	          'icon' => 'svg_icons/sendemail.svg',
            'url' => $site_url.'communication/emails',
            'permission' => $this->authorization->isAuthorized('EMAILS.SEND') && $this->authorization->isAuthorized('EMAILS.MODULE') && $this->authorization->isModuleEnabled('EMAILS')
          ],
          [
            'title' => 'Flash News',
            'sub_title' => 'Send Flash News',
	          'icon' => 'svg_icons/flashnews.svg',
            'url' => $site_url.'flash/showflashnews',
            'permission' => $this->authorization->isAuthorized('FLASH_NEWS.MODULE') && $this->authorization->isModuleEnabled('FLASH_NEWS')
          ],
          [
            'title' => ($this->settings->getSetting('homework_module_name'))?$this->settings->getSetting('homework_module_name'):'Homework',
            'sub_title' => 'Send homework to students',
	          'icon' => 'svg_icons/homework.svg',
            'url' => $site_url.'homework',
            'permission' => $this->authorization->isAuthorized('HOMEWORK.MODULE') && $this->authorization->isModuleEnabled('HOMEWORK')
          ],
          [
            'title' => 'Test Notification',
            'sub_title' => 'Test student notifications',
	          'icon' => 'svg_icons/notification.svg',
            'url' => $site_url.'PushNotification_controller/notifications',
            'permission' => $this->authorization->isSuperAdmin()
          ],
          
          
          [
            'title' => 'Email Template',
            'sub_title' => '',
	          'icon' => 'svg_icons/text.svg',
            'url' => $site_url.'email_controller/email_templates',
            'permission' => $this->authorization->isAuthorized('CIRCULARV2.CREATE') && $this->authorization->isModuleEnabled('CIRCULARS_V2')
          ],
          [
            'title' => 'SMS Template',
            'sub_title' => '',
	          'icon' => 'svg_icons/smstemplate.svg',
            'url' => $site_url.'email_controller/sms_templates',
            'permission' => $this->authorization->isAuthorized('COMMUNICATION.SMS_TEMPLATE')
          ],
          [
            'title' => 'Manage Groups',
            'sub_title' => '',
	          'icon' => 'svg_icons/group.svg',
            'url' => $site_url.'communication/group/group_creation',
            'permission' => $this->authorization->isAuthorized('COMMUNICATION.GROUP_CREATION')          
          ],
          [
            'title' => 'Resend-Ciruclar',
            'sub_title' => '',
            'icon' => 'svg_icons/circular.svg',
            'url' => $site_url.'communication/circulars/circular_resend',
            'permission' => $this->authorization->isAuthorized('COMMUNICATION.RESEND_CIRCULAR')          
          ]
        );
        $data['tiles'] = checkTilePermissions($data['tiles']);

        $data['report_tiles'] = array(
          [
            'title' => 'Communication Report',
            'sub_title' => 'Student/Staff wise report of sms/notifications/circulars & emails',
	          'icon' => 'svg_icons/communicationreport.svg',
            'url' => $site_url.'communication/reports',
            'permission' => $this->authorization->isAuthorized('COMMUNICATION.REPORT')
          ],
          [
            'title' => 'Aggregate Report',
            'sub_title' => 'Date wise report of sms/notifications/circulars & emails',
	          'icon' => 'svg_icons/aggregatereport.svg',
            'url' => $site_url.'communication/reports/aggregate_report',
            'permission' => $this->authorization->isAuthorized('COMMUNICATION.REPORT')
          ],
          [
            'title' => 'Failed Emails',
            'sub_title' => 'Report of email ids which have problems recieving emails',
	          'icon' => 'svg_icons/failedemails.svg',
            'url' => $site_url.'communication/reports/failed_emails',
            'permission' => $this->authorization->isAuthorized('COMMUNICATION.REPORT')
          ],
          [
            'title' => 'Parent Initiative Report',
            'sub_title' => 'Date wise report of sms/notifications/circulars & emails',
	          'icon' => 'svg_icons/talktous.svg',
            'url' => $site_url.'ptm_controller/parent_initiative_view',
            'permission' => $this->authorization->isAuthorized('COMMUNICATION.PARENT_INITIATIVE') && $this->authorization->isModuleEnabled('PARENT_INITIATIVE')
          ],
          [
            'title' => $data['tileName'].' Report',
            'sub_title' => 'Circular Report',
	          'icon' => 'svg_icons/circularreport.svg',
            'url' => $site_url.'communication/circulars/report',
            'permission' => $this->authorization->isAuthorized('CIRCULARV2.REPORT') && $this->authorization->isModuleEnabled('CIRCULARS_V2')
          ],
          [
            'title' => 'Homework Report',
            'sub_title' => 'Homework Report',
	          'icon' => 'svg_icons/homeworkreports.svg',
            'url' => $site_url.'homework/homework_reports',
            'permission' => $this->authorization->isAuthorized('HOMEWORK.MODULE') && $this->authorization->isModuleEnabled('HOMEWORK')
          ],
          [
            'title' => 'Student Wise Homework Report',
            'sub_title' => 'Student Wise Homework Report',
	          'icon' => 'svg_icons/studentwisehomeworkreport.svg',
            'url' => $site_url.'homework/student_wise_homework_report',
            'permission' => $this->authorization->isAuthorized('HOMEWORK.MODULE') && $this->authorization->isModuleEnabled('HOMEWORK')
          ],
          [
            'title' => 'Notification Log',
            'sub_title' => 'Notification Log',
	          'icon' => 'svg_icons/notification.svg',
            'url' => $site_url.'PushNotification_controller/pushNotificationLog',
            'permission' => $this->authorization->isSuperAdmin()
          ],
          [
            'title' => 'Texting Report',
            'sub_title' => 'Texting Report',
	          'icon' => 'svg_icons/textingreport.svg',
            'url' => $site_url.'communication/texting/report',
            'permission' => $this->authorization->isAuthorized('TEXTING.REPORT') && $this->authorization->isModuleEnabled('TEXTING')
          ],
          [
            'title' => 'Texting Delivery Report',
            'sub_title' => 'Texting Delivery Report',
	          'icon' => 'svg_icons/smsdeliveryreportnew.svg',
            'url' => $site_url.'communication/texting/sms_delivery',
            'permission' => $this->authorization->isAuthorized('TEXTING.REPORT') && $this->authorization->isModuleEnabled('TEXTING')
          ]
        );
        $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

        $data['other_tiles'] = array();
        if ($this->authorization->isSuperAdmin())  {
            $data['other_tiles'] = array(
                [
                    'title' => 'PTM',
                    'sub_title' => 'PTM',
	                  'icon' => 'svg_icons/ptm.svg',
                    'url' => $site_url.'ptm_controller/ptm',
                    'permission' => $this->authorization->isSuperAdmin()
                ],
                [
                    'title' => 'Load SMS Credits',
                    'sub_title' => 'Load SMS Credits',
	                  'icon' => 'svg_icons/loadsmscredits.svg',
                    'url' => $site_url.'sms/smsloader',
                    'permission' => $this->authorization->isSuperAdmin()
                ],
                [
                    'title' => 'Circular Categories',
                    'sub_title' => 'Circular Categories',
	                  'icon' => 'svg_icons/circularcategory.svg',
                    'url' => $site_url.'Communication_dashboard/circular_categories',
                    'permission' => $this->authorization->isSuperAdmin()
                ],
                [
                    'title' => 'SMS Delivery Report',
                    'sub_title' => 'SMS Delivery Report',
	                  'icon' => 'svg_icons/smsdeliveryreport.svg',
                    'url' => $site_url.'sms/smsAggregateReport',
                    'permission' => $this->authorization->isSuperAdmin()
                ],
                [
                  'title' => 'SMS Template Approval',
                  'sub_title' => 'Not Used',
                  'icon' => 'svg_icons/smsdeliveryreportnew.svg',
                  'url' => $site_url.'communication/texting/sms_template_approval',
                  'permission' => $this->authorization->isAuthorized('COMMUNICATION.SMS_TEMPLATE_APPROVAL')
                ]
            );
        }
        //$data['chatData'] = $this->helpdesk->getChatContent('Online Class');
      //$this->load->helper('chatdata_helper');
      //$data['chatData'] = getChatData('Communication');
      $data['type'] = 'Communication';
      $data['back_url'] = site_url('communication_dashboard');
      //echo "<pre>"; print_r($data); die();
      $data['path_prefix'] = $this->filemanager->getFilePath('');
      
        if ($this->mobile_detect->isTablet()) {
          $data['main_content']    = 'sms_nh/sms_dashboard_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'sms_nh/sms_dashboard_mobile';
        }else{
          $data['main_content']    = 'sms_nh/sms_dashboard';     	
        }
    
        
        $this->load->view('inc/template', $data);
    }

    public function circular_categories() {
        $this->load->model('communication/circular_model', 'circular');
        $data['config'] = json_decode($this->settings->getSetting('circularv2_categories'));
        $data['categories'] = $this->circular->getCircularCategories();
        $data['staff_list'] = $this->circular->getAllStaff();
        $data['main_content']    = 'communication/circular/categories';
        $this->load->view('inc/template', $data);
    }

    public function add_category() {
        $this->load->model('communication/circular_model', 'circular');
        $input = $_POST;
        // echo "<pre>"; print_r($input); die();
        $status = $this->circular->add_category($input);
        // if(isset($input['require_approval_by']) && !empty(array_filter($input['require_approval_by']))){
        //   $require_approval_by = array_filter($input['require_approval_by'], function($value) {
        //     return !empty($value);
        //   });

        //   $this->load->model('communication/texting_model', 'texting_model');
        //   $staff_id_str= implode(',', $require_approval_by);
        //   $category_name = $input['category_name'];
        //   $notification = "Dear staff member, you are appointed to approve a circular. Please check your school app for more details. Thank you!";
        //   $credits = $this->texting_model->_calculateCredits($notification, 0);
        //   $text_master = array(
        //     'title' => $category_name,
        //     'message' => $notification,
        //     'sent_by' => $this->authorization->getAvatarId(),
        //     'reciever' => $staff_id_str,
        //     'acad_year_id' => $this->acad_year->getAcadYearId(),
        //     'source' => 'Ciucular Approval',
        //     'text_count' => 0,
        //     'visible' => 1,
        //     'mode' => 'notification',
        //     'sms_credits' => $credits,
        //     'is_unicode' => 0,
        //     'sender_list' => NULL,
        //     'sending_status' => 'Initiated'
        //   );
        //   $texting_master_id = $this->texting_model->save_texts($text_master);

        //   $url = site_url('communication/circulars/list');
        //   $textingData = [];
        //   $staff_details = $this->circular->get_staff_user_details($staff_id_str);
        //   foreach($staff_details as $staff){
        //     $textingData[] = array(
        //       'texting_master_id' => $texting_master_id,
        //       'stakeholder_id' => $staff->staff_id,
        //       'mobile_no' => $staff->staff_mobile_no,
        //       'mode' => 1,
        //       'status' => ($staff->tokenState == 0 )? 'No Token' : 'Sent',
        //       'avatar_type' => 4,
        //       'is_read' => 0,
        //       'user_id' => $staff->user_id,
        //       'token' => $staff->user_token,
        //     );
        //   }
        //   $token_data = $this->texting_model->save_notifications($textingData);
        //   $this->load->helper('notification_helper');
        //   $notification_status = commonNotifications($token_data, $school_name, $notification, $url);
        // }
        if($status) {
            $this->session->set_flashdata('flashSuccess', 'Successfully added.');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong.');
        }
        redirect('Communication_dashboard/circular_categories');
    }

    public function deleteCategory() {
        $this->load->model('communication/circular_model', 'circular');
        $category_id = $_POST['category_id'];
        echo $this->circular->delete_category($category_id);
    }

    public function addCategoryFromConfig() {
        $this->load->model('communication/circular_model', 'circular');
        $categories = json_decode($this->settings->getSetting('circularv2_categories'));
        echo $this->circular->add_category_from_config($categories);
    }
}
