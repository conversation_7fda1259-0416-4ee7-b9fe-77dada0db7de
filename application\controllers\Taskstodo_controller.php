<?php

class Taskstodo_controller extends CI_Controller {

    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isModuleEnabled('TASKSTODO')) {
        redirect('dashboard', 'refresh');
      }    
      $this->load->model('taskstodo_model');
      $this->load->model('avatar');
    }

    public function index($id = null) {
        $AvatarId = $this->authorization->getAvatarId();
        $data1['avatar'] = $this->avatar->getAvatarById($AvatarId);
        $staffId = $data1['avatar']->stakeholderId;
        $data['staffId'] = $staffId;
        if ($id != null) {
            $data['id'] = $id;
            $data['task'] = $this->taskstodo_model->getTasksById($id);
            //print_r($data['id']); die();
        }
        $today = date('Y-m-d');
        //echo $today; die();
        $data['todaysTasks'] = $this->taskstodo_model->getTasksByDay($today, null, $staffId);
        $tomorrow = date('Y-m-d', strtotime($today . ' +1 day'));
        $data['upcomingTasks'] = $this->taskstodo_model->getTasksByTomarrow($tomorrow, $staffId);
        //print_r($data['todaysTasks']); die();
        $data['main_content'] = 'staff/todo/index';
        $this->load->view('inc/template', $data);
    }

    public function addTask($id = null){
        $status = (int) $this->taskstodo_model->addTasks($id);

        if($status){
            $this->session->set_flashdata('flashSuccess', 'Task added successfully.');
	    }else{
	       $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        }
        redirect('taskstodo_controller/index');
    }

    public function endTask(){
        $id = $_POST['id'];
        //$data['task'] = $this->taskstodo_model->getTasksById($id);
        $status = (int) $this->taskstodo_model->endTasks($id);

        echo $status;
    }

    public function redoTask(){
        $id = $_POST['id'];
        $status = (int) $this->taskstodo_model->redoTasks($id);

        echo $status;
    }

    public function doTomarrow(){
        $id = $_POST['id'];
        $data['task'] = $this->taskstodo_model->getTasksById($id);
        $status = (int) $this->taskstodo_model->doTomarrow($id,$data['task']->date);

        echo $status;
    }

    public function removeTask(){
        $id = $_POST['id'];
        $status = (int) $this->taskstodo_model->removeTasks($id);

        return $status;
    }
    

}
