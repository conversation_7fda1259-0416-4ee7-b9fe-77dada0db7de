<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Staff_workload extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
      redirect('dashboard', 'refresh');
    }  
    $this->load->model('timetablev2/section_workload_model','section_workload_model');
    $this->load->model('timetablev2/staff_workload_model','staff_workload_model');
    $this->load->model('timetablev2/room_workload_model','room_workload_model');
    $this->load->model('timetablev2/template_model','template_model');
  }

  //Landing function to show the timetable menu
  public function index($temp_id) {
    $data['template_obj'] = $this->template_model->get_template_by_id($temp_id);
    $data['main_content']    = 'timetablev2/staff_workload/staff_workload.php';
    $sec_names = $this->section_workload_model->sec_names($temp_id);
    $data['sec_names'] = $sec_names;
    $data['statffType'] = $this->settings->getSetting('staff_type');
    $this->load->view('inc/template', $data);
  }

  public function get_staff_workload_2() {
    $data['staff_workload'] = $this->staff_workload_model->get_staff_workload_2($_POST['template_id'], $_POST['section_id']);
    $data['has_freeze_permission'] = $this->authorization->isAuthorized('TIMETABLE.FREEZE_SUBJECT_ALLOCATION');
    echo json_encode($data);
  }
  
  public function get_sw_staff_list() {
    $data['staff_list'] = $this->staff_workload_model->get_sw_staff_list($_POST['template_id'], $_POST['section_id']);
    echo json_encode($data);
  }

  public function get_sw_room_list() {
    $data['room_list'] = $this->room_workload_model->get_sw_room_list($_POST['template_id'], $_POST['section_id']);
    echo json_encode($data);
  }

  public function get_sec_workload_details(){
    $temp_id = $_POST['temp_id'];
    $sec_id = $_POST['sec_id'];
    $staffType = $_POST['staffType'];
    $sec_workload = $this->staff_workload_model->get_sec_workload($temp_id,$sec_id);
    $total_workload = 0;
    $allocated_workload = 0;
    foreach($sec_workload as $i){
      $total_workload += (int)$i->workload;
      if($i->allocation_type_staff==1){
        $allocated_workload += (int)$i->workload;
      } 
    }
    $data["total_workload"] = $allocated_workload.'/'.$total_workload;
    
    $data["sec_workload"] = $sec_workload;
    $staff_workload = $this->staff_workload_model->get_staff_workload($temp_id,$staffType);
    $data['staff_workload'] = $staff_workload;
    $sec_names = $this->section_workload_model->sec_names($temp_id);
     foreach($sec_names as $i){
       if($i->class_section_id==$sec_id){
         $sec_name = $i->class_name;
       }
     }

     $data["room_workload"] = $this->room_workload_model->get_room_workload($temp_id);

     $data['sec_name'] = $sec_name;
    echo json_encode($data);
  }

  public function single_staff_workload(){
    $temp_id = $_POST['temp_id'];
    $staff_id = $_POST['staff_id'];
    $staff_workload = $this->staff_workload_model->get_single_staff_workload($temp_id,$staff_id);
    //echo "<pre>"; print_r($staff_workload);die();
    $data["staff_workload"] = $staff_workload;
    echo json_encode($data);
  }

  public function get_single_room_workload () {
    $temp_id = $_POST['temp_id'];
    $room_id = $_POST['room_id'];
    $room_workload = $this->room_workload_model->get_single_room_workload($temp_id,$room_id);
    $data["room_workload"] = $room_workload;
    echo json_encode($data);
  }

  public function allocate_staff_to_subject(){
    $s_short_name= $_POST['s_short_name'];
    $allocation_type_staff = $_POST['allocation_type_staff'];
    $s_name= $_POST['s_name'];
    $temp_id = $_POST['temp_id'];
    $staff_id = $_POST['s_id'];
    $sec_id = $_POST['sec_id'];
    $sub_id = $_POST['sub_id'];

    //Check if there is already a staff allocated. If so, check if the staff is allocated.
    $allocate = $this->staff_workload_model->insert_staff_toSubject($temp_id,$staff_id,$sec_id,$sub_id,$s_short_name,$allocation_type_staff,$s_name);
    echo json_encode($allocate);
  }

  public function allocate_room_to_subject(){
    $room_id = $_POST['room_id'];
    $allocation_type_room = $_POST['allocation_type_room'];
    $temp_id = $_POST['temp_id'];
    $sec_id = $_POST['sec_id'];
    $sub_id = $_POST['sub_id'];

    //Check if there is already a staff allocated. If so, check if the staff is allocated.
    $allocate = $this->room_workload_model->allocate_room_to_subject($temp_id, $room_id, $sec_id, $sub_id, $allocation_type_room);
    echo json_encode($allocate);
  }

  public function unallocate_staff_to_subject(){
    $temp_id = $_POST['temp_id'];
    $sec_id = $_POST['class_sec_id'];
    $sub_id = $_POST['sub_id'];
    
    $data['result'] = $this->staff_workload_model->delete_staff_toSubject($temp_id,$sec_id,$sub_id);
    $data['sec_id'] = $sec_id;

    echo json_encode($data);
  }

  public function unallocate_room_to_subject(){
    $temp_id = $_POST['temp_id'];
    $sec_id = $_POST['class_sec_id'];
    $sub_id = $_POST['sub_id'];
    $data['result'] = $this->room_workload_model->delete_room_to_subject($temp_id,$sec_id,$sub_id);
    $data['sec_id'] = $sec_id;

    echo json_encode($data);
  }

  public function unallocate_timetable_to_subject(){
    $temp_id = $_POST['temp_id'];
    $sec_id = $_POST['class_sec_id'];
    $sub_id = $_POST['sub_id'];
    $unallocate = $this->staff_workload_model->delete_timetable_toSubject($temp_id,$sec_id,$sub_id);

    echo json_encode($sec_id);
  }

  public function freeze_subjects(){
    $temp_id = $_POST['template_id'];
    $sec_id = $_POST['section_id'];
    $sub_id = $_POST['subject_id'];
    $do_freeze = $_POST['do_freeze'];
    $status = $this->staff_workload_model->freeze_subjects($temp_id,$sec_id,$sub_id,$do_freeze);

    echo json_encode($status);
  }

}