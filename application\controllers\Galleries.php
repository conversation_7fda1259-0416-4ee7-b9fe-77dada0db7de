

<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Galleries extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		// $this->load->library('session');
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isModuleEnabled('GALLERY')) {
			redirect('dashboard', 'refresh');
		}
		$this->load->model('gallery_model');
		$this->load->helper('text');
		$this->load->library('filemanager');
		$this->load->model('communication/texting_model');
		$this->load->model('class_section');
	}
	public function index()
	{

		$gallery_info['image_count'] = 0;
		if ($this->authorization->isAuthorized('GALLERY.VIEW_EDIT_DELETE')) {
			//This code is to prevent edit functionality in mobile
			$gallery_info = $this->gallery_model->get_all_galleries();
			// $data['classSectionList'] = $this->gallery_model->getClassSectionNames();
			$data['class_section'] = $this->gallery_model->getAllClassSections_check_gallery_list();
			// echo "<pre>"; print_r($gallery_info); die();
			$data['gallery_info'] = $gallery_info;
			$data['groups'] = $this->texting_model->getTextingGroups();

			if ($this->mobile_detect->isTablet()){
				$data['main_content'] = 'galleries/index';
				//echo '<pre>'; print_r($data); die();
			}else if ($this->mobile_detect->isMobile()) {
				$data['main_content'] = 'galleries/mobile_tablet_index';
				// $data['main_content'] = 'staff/galleries/index';
			}else{
				$data['main_content'] = 'galleries/index';
			}
		}
		else {
			$gallery_info = $this->gallery_model->get_staff_galleries();
			$data['gallery_info'] = $gallery_info;
			$data['main_content'] = 'staff/galleries/index';
		}
		//echo '<pre>'; print_r($data); die();
		$this->load->view('inc/template', $data);
	}

	private function _resize_image($file, $max_resolution, $type) {
		if(file_exists($file)) {
			if($type == 'image/jpeg')
				$original_image = imagecreatefromjpeg($file);
			else 
				$original_image = imagecreatefrompng($file);

			//resolution
			$original_width = imagesx($original_image);
			$original_height = imagesy($original_image);

			//try width first
			$ratio = $max_resolution / $original_width;
			$new_width = $max_resolution;
			$new_height = $original_height * $ratio;

			//if that dosn't work
			if($new_height > $max_resolution) {
				$ratio = $max_resolution / $original_height;
				$new_height = $max_resolution;
				$new_width = $original_width * $ratio;
			}

			if($original_image) {
				$new_image = imagecreatetruecolor($new_width, $new_height);
				imagecopyresampled($new_image, $original_image, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
				if($type == 'image/jpeg')
					imagejpeg($new_image, $file);
				else 
					imagepng($new_image, $file);
			}

			return $file;
			/*echo '<br>Resized: ';
			echo filesize($file); 

			echo '<pre>'; print_r($file); die();*/
		}
	}


	public function uploadImages() {
		$input = $this->input->post();
		$gid = $input['gal_id'];
		$img_desc = $input['image_description'];
		$img_tags = $input['image_tags'];
		$files = $_FILES['photo'];
		$failed = 0;
		$total = count($files['name']);
		$save_images = array();
		foreach($files['tmp_name'] as $i => $file_name) {
			if($files['size'][$i] > 100000) {
				$tmp_name = $this->_resize_image($file_name, "500", $files['type'][$i]);
				$file = array(
					'tmp_name' => $tmp_name,
					'name' => $files['name'][$i]
				);
			} else {
				$file = array(
					'tmp_name' => $file_name,
					'name' => $files['name'][$i]
				);
			}
			$img = $this->s3FileUpload($file, $gid);
			if($img['file_name'] == null || $img['file_name'] == ''){
				$failed++;
			}else {
				$save_images[] = array(
					'gallery_id' => $gid,
		            'image_name' => $img['file_name'],
		            'image_description' => $img_desc,
		            'image_tags' => $img_tags
				);
			}
		}

		if(!empty($save_images)) {
			$this->gallery_model->save_images($save_images);
		}

		if($failed) {
			$this->session->set_flashdata('flashError', $failed.' / '.$total.' failed.');
		} else {
			$this->session->set_flashdata('flashSuccess', 'Successfully uploaded the images.');
		}
		redirect('galleries/view_gallery/'.$gid);
	}


	public function create_gallery($identification_code = '')
	{
		$AvatarId = $this->authorization->getAvatarId();
		$data['AvatarId'] = $AvatarId;
		$data['classSectionList'] = $this->gallery_model->getClassSectionNames();
		$data['main_content'] = 'galleries/create_gallery';


		$this->load->view('inc/template', $data);
	}

	public function edit_gallery($gallery_id){
		$data['gallery_info'] = $this->gallery_model->get_gallery_info($gallery_id);
		// $data['classSectionList'] = $this->gallery_model->getClassSectionNames();
		// echo "<pre>";print_r($data);die();
		$data['main_content'] = 'galleries/edit_gallery';
		//$data['gallery_tags'] = $this->gallery_model->get_gallery_tags();

		$this->load->view('inc/template', $data);
	}

	public function gallery_created()
	{  #CREATES A NEW GALLERY
		//echo "<pre>"; print_r($this->input->post()); die();

		// $_POST['event_date'] = date('Y-m-d', strtotime($_POST['event_date']));

		// if (!(isset($_POST['gallery_name']))) {
		// 	redirect('galleries');
		// }
		$result = $this->gallery_model->addGallery();
		if($result) {
			$this->session->set_flashdata('flashSuccess','Successfully Inserted');
		} else {
			$this->session->set_flashdata('flashError', 'Something went wrong');
		}
		redirect('galleries');

		// /*if ($_POST['publish_status'] ==1) {
		// 	$this->_send_notification_published_gallery($data['gallery']['id']);
		// }*/
		// //echo '<pre>'; print_r(); die();
		// // echo $data['galleryName']; die();
		// //$data['main_content'] = 'galleries/upload_images';
		// //$this->load->view('inc/template', $data);
		// redirect('galleries/view_gallery/' . $data['gallery']['id']);
	}

	private function _send_notification_published_gallery($gallery_id){
		$click_action = $this->settings->getSetting('push_notification_activity_name');
        $push_notiification_key = $this->settings->getSetting('push_notification_key');
        if($click_action != '' && $push_notiification_key != '') {
			$gallery = $this->gallery_model->getGallery($gallery_id);
			$this->load->helper('texting_helper');
			$input_array = array();
			$input_array['mode'] = 'notification';
		    $input_array['source'] = 'Gallery';
		    $input_array['title'] = 'Gallery';
		    $input_array['message'] = 'New photos published in the gallery.';
			if($gallery->gallery_visibility == 3 || $gallery->gallery_visibility == 4) {
				//send Notification to all parent
				$this->load->model('class_section');
				$class_sections = $this->class_section->getAllClassSections();
				$input_array['class_section_ids'] = array();
				foreach ($class_sections as $key => $section) {
					$input_array['class_section_ids'][] = $section->id;
				}
			}
			if($gallery->gallery_visibility != 1) {
				//send Notification to all staff
				$staff_ids = $this->gallery_model->getStaffList();
				$input_array['staff_ids'] = array();
				foreach ($staff_ids as $key => $staff) {
					$input_array['staff_ids'][] = $staff->id;
				}
			}
			// echo '<pre>'; print_r($input_array); die();
			sendText($input_array);
		}
	}
	public function upload_multiple_images() {
		$files = $_FILES['file_name'];
		$input = $this->input->post();
		$gid = $input['gal_id'];
		$img_desc = $input['desc'];
		$img_tags = $input['tags'];
		$failed = 0;

		$original_image = $_FILES['photo'];
		foreach($original_image['tmp_name'] as $i => $file_name) {
			$original_image_file = array(
				'tmp_name' => $file_name,
				'name' => 'img'.$i.'.png'
			);
			$original_img[$i] = $this->s3FileUpload($original_image_file, $gid);
		}
		// $original_image_file_upload = $this->s3FileUpload($original_image_file, $gid);
		
		foreach($original_image['name'] as $k => $m) {
			foreach($files['tmp_name'] as $i => $file_name) {
				// echo '<pre>';print_r($m);die();
				if($m == $files['name'][$i]){
					$file = array(
						'tmp_name' => $file_name,
						'name' => 'img'.$i.'.png'
					);
					$img = $this->s3FileUpload($file, $gid);
					if($img['file_name'] == null || $img['file_name'] == ''){
						$failed++;
					}else {
						$save_images[] = array(
							'gallery_id' => $gid,
							'image_name' => $img['file_name'],
							'original_image_url'=>$original_img[$k]['file_name'],
							'image_description' => $img_desc,
							'image_tags' => $img_tags,
							'is_image' => 1,
							'created_by'=>$this->authorization->getAvatarId()
						);
					}
				}
			}
		}
		if(!empty($save_images)) {
			$this->gallery_model->save_images($save_images);
		}

		echo $failed;
	}

	public function upload_multiple_videos(){
		// echo "<pre>"; echo print_r($_POST); die();
		$input = $this->input->post();
		$gid = $input['gal_id'];
		$img_desc = $input['desc'];
		$img_tags = $input['tags'];

		$video = $_FILES['photo'];
		$failed = 0;

		$file = array(
			'tmp_name' => $video['tmp_name'][0],
			'name' => $video['name'][0]
		);
		$upload = $this->s3FileUpload($file, $gid);

		if($upload['file_name'] == null || $upload['file_name'] == ''){
			$failed++;
		}else {
		$save_images[] = array(
			'gallery_id' => $gid,
			'image_name' => $upload['file_name'],
			'image_description' => $img_desc,
			'image_tags' => $img_tags,
			'is_image' => 0,
			'created_by'=>$this->authorization->getAvatarId()
		);
	    }

		if(!empty($save_images)) {
			$this->gallery_model->save_images($save_images);
		}
		echo $failed;
	}

	public function upload_images($gallery_id)
	{  #ADDING IMAGES TO EXISTING ALBUM



		$data['gallery1'] = $this->gallery_model->get_gallery_info($gallery_id);

		// echo $data['galleryName']; die();
		$data['main_content'] = 'galleries/upload_images';
		$this->load->view('inc/template', $data);
	}

	public function ajax_insert_images()
	{

		// $desc = $this->input->post('desc');
		// $tag = $this->input->post('tag');
		// echo "<pre>";
		//  print_r($desc);
		//  print_r($tag);
		// die();
		$g_id = $_POST['gallery_id'];
		$gallery_id = $this->gallery_model->upload_image_to_s3($this->s3FileUpload($_FILES['photo'], $g_id));
		redirect('galleries/view_gallery/' . $gallery_id);
	}
	
	
	public function ajax_insert_data()
	{
		$gid = $this->input->post('gal_id');
		$img_desc = $this->input->post('desc');
		$img_tags = $this->input->post('tags');
		
		if ($_FILES['file_name']['size'] < $_FILES['file']['size']) {
			$img = $this->s3FileUpload($_FILES['file_name'], $gid);
		}else{
			$img = $this->s3FileUpload($_FILES['file'], $gid);
		}
	
		if($img['file_name'] == null || $img['file_name'] == ''){
			$image = 'error';
		}else {
			$image = $this->gallery_model->upload_image_info_to_db($img, $gid, $img_desc, $img_tags);
		}
		echo json_encode($image);
	}

	public function ajax_insert_data_without_resize()
	{
		$gid = $this->input->post('gal_id');
		$img_desc = $this->input->post('desc');
		$img_tags = $this->input->post('tags');

		$img = $this->s3FileUpload($_FILES['file'], $gid);
		$image = $this->gallery_model->upload_image_info_to_db($img, $gid, $img_desc, $img_tags);

		echo json_encode($image);
	}

	public function ajax_insert_data_with_server_side_resize()
	{
		$gid = $this->input->post('gal_id');
		$img_desc = $this->input->post('desc');
		$img_tags = $this->input->post('tags');

		$uploadedFile = $_FILES['file']['tmp_name'];
		// echo '<pre>';print_r($uploadedFile);die();
		$sourceProperties = getimagesize($uploadedFile);

		$imageSrc = imagecreatefromjpeg($uploadedFile); 
		$tmp = $this->imageResize($imageSrc,$sourceProperties[0],$sourceProperties[1]);
		// imagejpeg($tmp,$dirPath. $newFileName. "_thump.". $ext);
				
		$img = $this->s3FileUpload($tmp, $gid);
		$image = $this->gallery_model->upload_image_info_to_db($img, $gid, $img_desc, $img_tags);

		echo json_encode($image);
	}

	public function imageResize($imageSrc,$imageWidth,$imageHeight) {

		$newImageWidth =200;
		$newImageHeight =200;
	
		$newImageLayer=imagecreatetruecolor($newImageWidth,$newImageHeight);
		imagecopyresampled($newImageLayer,$imageSrc,0,0,0,0,$newImageWidth,$newImageHeight,$imageWidth,$imageHeight);
	
		return $newImageLayer;
	}

	public function s3FileUpload($file, $g_id)
	{
		if ($file['tmp_name'] == '' || $file['name'] == '') {
			return ['status' => 'empty', 'file_name' => ''];
		}
		$uploadResult = $this->filemanager->uploadFile($file['tmp_name'], $file['name'], 'gallary_images/' . $g_id);
		//print_r($uploadResult); die();
		return $uploadResult;
	}

	public function view_gallery($gallery_id)
	{
		$data['gallery_info'] = $this->gallery_model->get_gallery_info($gallery_id);
		$data['image_info'] = $this->gallery_model->get_images_info($gallery_id);
		$data['permitGalleryEdit'] = $this->authorization->isAuthorized('GALLERY.VIEW_EDIT_DELETE');
		$data['main_content'] = 'galleries/view_gallery';
		$this->load->view('inc/template', $data);
	}

	public function gallery_wander($where, $gallary_id)
	{
		switch ($where) {
			case 'prev':
				$gallary_id = $this->gallery_model->getPrevGallaryId($gallary_id);
				break;
			case 'next':
				$gallary_id = $this->gallery_model->getNextGalleryId($gallary_id);
				break;
			default:
				break;
		}
		echo "success";
		die();
		redirect('dashboard');
	}





	public function update_gallery_info($id){
		$result = $this->gallery_model->upload_edited_gallery($id);
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Gallery Update succesfully.');
		} else {
			$this->session->set_flashdata('flashError', 'Something went wrong.');
		}
		redirect('galleries');
	}

	public function edit_cancel()
	{
		//$this->session->set_flashdata('flashError', 'Gallery not Edited.');
		redirect('galleries');
	}
	public function delete_gallery()
	{
		$gallery_id = $_POST['gId'];
		$result = $this->gallery_model->delete_gallery_db($gallery_id);
		echo $result;
	}
	
	public function send_notification_for_publish_gallery(){
		$gallery_id = $_POST['gId'];
		$status = $_POST['status'];
		if ($status == 1) {
			$click_action = $this->settings->getSetting('push_notification_activity_name');
            $push_notiification_key = $this->settings->getSetting('push_notification_key');
            if($click_action != '' && $push_notiification_key != '') {
				$this->load->helper('texting_helper');
				$input_array = array();
				$input_array['mode'] = 'notification';
		    	$input_array['source'] = 'Gallery';
		    	$input_array['title'] = 'Gallery';
		    	$input_array['message'] = 'New photos published in the gallery.';
		    	$input_array['student_url'] = '';
		    	$input_array['staff_url'] = '';
		    	$gallery = $this->gallery_model->get_gallery_visibility($gallery_id);
		    	$input_array['class_section_ids'] = array();
		    	if (!empty($gallery['classSectionIds'])) {
		    	 	foreach ($gallery['classSectionIds'] as $key => $sectionId) {
			    		$input_array['class_section_ids'][] = $sectionId;
			    	}
		    	}
		    	$input_array['student_ids'] = array();
		    	if (!empty($gallery['studentIds'])) {
		    	 	foreach ($gallery['studentIds'] as $key => $studentId) {
			    		$input_array['student_ids'][] = $studentId;
			    	}
		    	}

		    	$input_array['staff_ids'] = array();
		    	if (!empty($gallery['staffIds'])) {
		    	 	foreach ($gallery['staffIds'] as $key => $staffId) {
			    		$input_array['staff_ids'][] = $staffId;
			    	}
		    	}
				sendText($input_array);
			}
		}
	}
	public function publish_gallery() {
		$gallery_id = $_POST['gId'];
		$status = $_POST['status'];

		$result = $this->gallery_model->publish_gallery($gallery_id, $status);
		if($result && $status) {
			$click_action = $this->settings->getSetting('push_notification_activity_name');
            $push_notiification_key = $this->settings->getSetting('push_notiification_key');
            if($click_action != '' && $push_notiification_key != '') {
				$gallery = $this->gallery_model->getGallery($gallery_id);
				$this->load->helper('texting_helper');
				$input_array = array();
				$input_array['mode'] = 'notification';
		    $input_array['source'] = 'Gallery';
		    $input_array['title'] = 'Gallery';
		    $input_array['message'] = 'New photos published in the gallery.';
				if($gallery->gallery_visibility == 3 || $gallery->gallery_visibility == 4) {
					//send Notification to all parent
					$this->load->model('class_section');
					$class_sections = $this->class_section->getAllClassSections();
					$input_array['class_section_ids'] = array();
					foreach ($class_sections as $key => $section) {
						$input_array['class_section_ids'][] = $section->id;
					}
				}
				if($gallery->gallery_visibility != 1) {
					//send Notification to all staff
					$staff_ids = $this->gallery_model->getStaffList();
					$input_array['staff_ids'] = array();
					foreach ($staff_ids as $key => $staff) {
						$input_array['staff_ids'][] = $staff->id;
					}
				}
				// echo '<pre>'; print_r($input_array); die();
				sendText($input_array);
			}
		}
		echo $result;
	}

	public function get_gallery_name($gallery_id)
	{
		$data = $this->gallery_model->get_gallery_info($gallery_id);
		return $data['gallery_name'];
	}

	public function delete_image()
	{
		$image_id = $_POST['image_id'];
		$result = $this->gallery_model->delete_image_db($image_id);
		echo $result;
	}

	public function gallery_publish_switch_status(){
		$stngId = $_POST['stngId'];
    	$value = $_POST['value'];
    	echo $this->gallery_model->update_gallery_publish_switch_status($stngId,$value); 
	}

	public function view_gallery_visibility_by_id(){
		$gallery_id = $_POST['gallery_id'];
		$result = $this->gallery_model->get_updated_gallery_visibility_list($gallery_id);
		echo  json_encode($result);

	}

	public function insert_galleries_visibility(){
		$result = $this->gallery_model->insert_galleries_visibility_details();
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Gallery visibility list succesfully.');
		} else {
			$this->session->set_flashdata('flashError', 'Something went wrong.');
		}
		redirect('galleries');
	}

	public function remove_gallery_list_by_id(){
		$gallery_list_id = $_POST['gallery_list_id'];
		echo $this->gallery_model->remove_gallery_list_by_id($gallery_list_id);
	}

	public function download_original_image($image_id){
        $url=$this->gallery_model->get_original_image_url($image_id);
		$link = $url->original_image_url;
        $file = explode("/", $link);
        $file_name = 'image';
        $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
        // echo '<pre>'; print_r($fname); die();
        $url = $this->filemanager->getFilePath($link);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download($fname, $data, TRUE);
	}
}
