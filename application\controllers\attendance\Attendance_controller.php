<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Attendance_controller
 *
 * <AUTHOR>
 */
class Attendance_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('STAFF_MASTER')) {
            redirect('dashboard', 'refresh');
        }    

        date_default_timezone_set('Asia/Kolkata');

        $this->yearId = $this->acad_year->getAcadYearId();        
        $this->load->model('student/Student_Model','student');
        $this->load->model('attendance/Attendance_model','attendanceModel');
        $this->load->model('attendance/Attendance_master_model','attendanceMaster');
        $this->load->library('filemanager');
    }

    public function index(){
      $site_url = site_url();
      $data['tiles'] = array(
          [
            'title' => 'Take Attendance',
            'sub_title' => 'Take Attendance',
            'icon' => 'svg_icons/attendance.svg',
            'url' => $site_url.'attendance/Attendance_controller/markAttendance',
            'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.TAKE')
          ],
          [
            'title' => 'Late-comer Entry',
            'sub_title' => 'Late-comer Entry',
            'icon' => 'svg_icons/latecomerentry.svg',
            'url' => $site_url.'attendance/1/createAction',
            'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.LATECOMER')
          ],
          [
            'title' => 'Fresh Entry',
            'sub_title' => 'Fresh Entry',
            'icon' => 'svg_icons/freshentry.svg',
            'url' => $site_url.'attendance/3/createAction',
            'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.FRESH_ENTRY')
          ],
          [
            'title' => 'Health Care',
            'sub_title' => 'Health care',
            'icon' => 'svg_icons/healthcare.svg',
            'url' => $site_url.'attendance/2/createAction',
            'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.HEALTH_CARE')
          ],
          [
            'title' => 'Emergency Exit',
            'sub_title' => 'Emergency exit',
            'icon' => 'svg_icons/logout.svg',
            'url' => $site_url.'attendance/0/createAction',
            'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.EMERGENCY_EXIT')
          ]
      );
      $data['tiles'] = checkTilePermissions($data['tiles']);

      $data['report_tiles'] = array(
          [
            'title' => 'Day-wise Attendance',
            'sub_title' => 'Day-wise Attendance',
            'icon' => 'svg_icons/daywiseclassattendance.svg',
            'url' => $site_url.'reports/attendance/student_attendance/reports/1',
            'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.CLASS_ATTENDANCE_REPORT')
          ],
          [
            'title' => 'Month-wise Attendance',
            'sub_title' => 'Month-wise Attendance',
            'icon' => 'svg_icons/monthwiseclassattendance.svg',
            'url' => $site_url.'reports/attendance/student_attendance/reports/3',
            'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.CLASS_ATTENDANCE_REPORT')
          ],
          [
            'title' => 'Special Case Report',
            'sub_title' => 'Special Case Report',
            'icon' => 'svg_icons/nonreconciledreport.svg',
            'url' => $site_url.'reports/attendance/student_attendance/special_case_report',
            'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.SPECIAL_CASE_REPORT')
          ],
          [
            'title' => 'Day Attendance',
            'sub_title' => 'Day Attendance',
            'icon' => 'svg_icons/dayattendance.svg',
            'url' => $site_url.'reports/attendance/student_attendance/dayAttendance',
            'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.CLASS_ATTENDANCE_REPORT')
          ],
          [
            'title' => 'Class Level Day Attendance',
            'sub_title' => 'Class Level Day Attendance',
            'icon' => 'svg_icons/dayattendance.svg',
            'url' => $site_url.'reports/attendance/student_attendance/class_level_day_attendance',
            'permission' => $this->authorization->isSuperAdmin()
            // 'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.CLASS_ATTENDANCE_REPORT')
          ],[
            'title' => 'Class Wise Summary',
            'sub_title' => 'Class Wise Summary',
            'icon' => 'svg_icons/daywiseclassattendance.svg',
            'url' => $site_url.'reports/attendance/student_attendance/ClassWiseSummary',
            // 'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.CLASS_ATTENDANCE_REPORT')
            'permission'=>$this->authorization->isSuperAdmin()
          ],
          [
            'title' => 'Emergency Exit Report',
            'sub_title' => 'Emergency Exit Report',
            'icon' => 'svg_icons/dayattendance.svg',
            'url' => $site_url.'attendance/0/action',
            'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.EMERGENCY_EXIT')
          ],
          [
            'title' => 'Attendance Not Taken',
            'sub_title' => 'Class Wise Not Taken Attendance',
            'icon' => 'svg_icons/daywiseclassattendance.svg',
            'url' => $site_url.'reports/attendance/student_attendance/attenance_not_taken',
            'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE.ATTENDANCE_NOT_TAKEN_REPORT')
          ]
      );
      $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

      $data['other_tiles'] = array(
          [
            'title' => 'Attendance Template',
            'sub_title' => 'Attendance Template',
            'icon' => 'svg_icons/attendancetemplate.svg',
            'url' => $site_url.'attendance/Attendance_master_controller',
            'permission' => $this->authorization->isSuperAdmin()
          ],
          [
            'title' => 'Edit Attendance',
            'sub_title' => 'Edit Attendance',
            'icon' => 'svg_icons/edit.svg',
            'url' => $site_url.'attendance/editAttendanceBy/AdmissionNumber',
            'permission' => $this->authorization->isSuperAdmin()
          ],
          [
            'title' => 'Edit Competition Attendance',
            'sub_title' => 'Edit Competition Attendance',
            'icon' => 'svg_icons/competition.svg',
            'url' => $site_url.'competition/showEditAttendance',
            'permission' => $this->authorization->isModuleEnabled('COMPETITION') && $this->authorization->isSuperAdmin()
          ]
      );
      $data['other_tiles'] = checkTilePermissions($data['other_tiles']);      
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'attendance/attendance_tablet_dashboard';
      }else if ($this->mobile_detect->isMobile()) {
        $data['main_content'] = 'attendance/attendance_mobile_dashboard';
      } else {
        $data['main_content'] = 'attendance/attendance_desktop_dashboard';          
      }
      $this->load->view('inc/template', $data);
    }

    public function markAttendance($sessionId=0,$selectedDate=0) {
      $show_all_sections = $this->authorization->isAuthorized('STUDENT_ATTENDANCE.SHOW_ALL_SECTIONS');
      // $data['getclassinfo']  = $this->student->getclass();
      $enable_notification = $this->settings->getSetting('student_attendancev2_enable_day_absentees_notification');

      // echo $enable_notification; die();
      $data['notification_mode'] = $this->settings->getSetting('student_attendancev2_day_attendance_enable_mode');
      $data['enable_notification'] = 0;
      if ($enable_notification) {
          $data['enable_notification'] = 1;
      }
      $data['sessionId']=$sessionId;
      $data['selectedDate']=$selectedDate;
      $data['class_section'] = $this->attendanceModel->getAllClassandSection($show_all_sections);
      $data['main_content'] = 'attendance/index';
      $this->load->view('inc/template', $data);
    }

    public function prepare_existing_data($getStudents) {
      $attendance_history=json_decode($getStudents[0]['history'])[0];
     
      $taken_by_name=$this->attendanceModel->get_staff_name_by_avatar_id($attendance_history->staff_id);
      $taken_on_time=date('h-i a',strtotime($attendance_history->created_at));

      $grouped_arr['stu'] = [];
      $grouped_master_arr = [];
      $attendance_master_group_id = '';
      $grouped_arr['summery']['present'] = 0;
      $grouped_arr['summery']['absent'] = 0;
      $grouped_arr['summery']['total'] = 0;
      $grouped_arr['summery']['taken_by_name'] = $taken_by_name;
      $grouped_arr['summery']['taken_on_time'] =$taken_on_time;
      // echo '<pre>'; print_r($getStudents); die();
      foreach ($getStudents as $kstu => $vstu) {
       

        if(!isset($grouped_arr['stu'][$vstu['student_id']])) {
          $attendance_master_group_id = '';
          $attendance_master_group_id .= $vstu['attendance_master_group_id'].',';

          $grouped_arr['stu'][$vstu['student_id']] = [
            'student_id' => $vstu['student_id'],
            'student_admission_id' => $vstu['student_admission_id'],
            'roll_no' => $vstu['roll_no'],
            'admission_no' => $vstu['admission_no'],
            'name' => $vstu['name'],
            'attendance_session_id' => $vstu['attendance_session_id'],
            'picture_url' => $vstu['picture_url'],
            'high_quality_picture_url' => $vstu['high_quality_picture_url'],
            'gender' => $vstu['gender'],
            'event_type' => $vstu['event_type'],
          ];

          $grouped_arr['stu'][$vstu['student_id']]['master'][$vstu['attendance_master_id']] = [
            'id' => $vstu['id'],
            'attendance_master_id' => $vstu['attendance_master_id'],
            'attendance_master_group_id' => $attendance_master_group_id,
            'status' => $vstu['status']
          ];
          $grouped_arr['summery']['total'] ++;
          if($vstu['status'] == 1){
            $grouped_arr['summery']['present'] ++;
          }
          else{
            $grouped_arr['summery']['absent'] ++;
          }

        } else {
          $attendance_master_group_id .= $vstu['attendance_master_group_id'].',';
        }
      }


      return $grouped_arr;
    }

    public function combineSessionData($data1 , $data2) {

      foreach ($data1 as $key1 => $value1) {
        $data1[$key1]['pervious_session_status'] = 2;
        foreach ($data2 as $key2 => $value2) {
          if($value1['id'] == $value2['student_id']) {
            $data1[$key1]['pervious_session_status'] = ($value2['rowCount'] == $value2['attendanceSum'] ? 1 : 0);
          }
        }
      }

      return $data1;
    }

    public function submitAttendanceRequest($finput = []) {
      $input = $this->input->post();

      $sessionId = '';
      if(empty($finput) && !empty($input)) {
        $selectedSection        = $input['section'];
        $select_date            = $input['select_date'];
        $sessionId              = $input['selectsession'];
        $selectedSection = $input['section']; 
        $selectedSectionDetails = $this->attendanceModel->getSectionDetails($selectedSection);
        // echo '<pre>'; print_r($selectedSectionDetails); die();
        unset($input);
        $input['classid'] =  $selectedSectionDetails['class_id'].'-'.$selectedSectionDetails['class_name'];
        $input['classsection'] = $selectedSectionDetails['id'].'-'.$selectedSectionDetails['section_name'];
        $input['select_date'] = $select_date;
        $input['selectsession'] = $sessionId;  
        //echo '<pre>'; print_r($input); die();
      } else {
        $input = $finput;
      }
      

      if($input['selectsession']) {
        $sessionId = $input['selectsession'];
      }

      list($class_id,$class_name) = explode('-', $input['classid']);      
      if(!empty($input['classsection'])) {
        list($section_id,$section_name) = explode('-', $input['classsection']);
      } else {
        $section_id = $section_name = '';
      }

      $data['class_id'] = $class_id;
      $data['class_name'] = $class_name;
      $data['section_id'] = $section_id;
      $data['section_name'] = $section_name;
      $data['sessionId'] = $sessionId;
      $selected_date = date("Y-m-d", strtotime($input['select_date']));
      $data['selected_date'] = $selected_date;
      $timestamp = strtotime($selected_date);
      $day  = date("Y-m-d", $timestamp); 

      if(date('l', $timestamp) == 'Saturday') 
        $type = 2;
      else
        $type = 1;
        //echo $sessionId; die();
      if(empty($sessionId)) {
        $attendanceMaster = $this->attendanceMaster->getAttendanceMasterByClass($class_id, $type);
      } else {
        $attendanceMaster = $this->attendanceMaster->getAttendanceMasterById($sessionId);
      }

      if(empty($attendanceMaster)) {
        $this->session->set_flashdata('flashInfo', 'Attendance Template not found. Contact your administrator');
        redirect('attendance');
      } else {
        $data['attendanceMaster'] = $attendanceMaster;
      } 

      // Check if attendance was already taken
      $new_status = true;

      $attendance_details = $this->attendanceModel->checkIfAttendanceTaken($class_id, $section_id, $sessionId, $day);
      
      if($attendance_details != 0) {
        $getStudents  = $this->attendanceModel->getAttendance($attendance_details,$class_id, $section_id); 
       
        $data['getStudents']  = $this->prepare_existing_data($getStudents['current']);
        $data['getNEWStudents']  = $getStudents['new'];
               

        $data['main_content']  = 'attendance/editAttendance';
        $new_status = false;
      }      

      if($new_status) {

        $event['day']  = $day; 
        $data['getStudents'] = $this->student->getStudentByClassSection($class_id, $section_id, $event);
        //Check if pervious session taken 
        $getPerviousSesssions = $this->attendanceMaster->getAttendanceMasterByClass($class_id, $type);

        $prevSessionName = '';
        $prevSessionStudents = [];
        $prevSessionFlag = false;
        if(count($getPerviousSesssions) > 1) {
          
          $pervious_session = 0; 

          foreach ($getPerviousSesssions as $key => $value) {
            if($sessionId == $value->id) 
              break;

            $pervious_session = $value->id;
            $prevSessionName = $value->name;
          }

          if($pervious_session != 0) {
            $attendanceTaken = $this->attendanceModel->checkIfAttendanceTaken($class_id, $section_id, $pervious_session, $day);
            if($attendanceTaken != 0) {
              $prevSessionStudents  = $this->attendanceModel->getFilteredAttendance($attendanceTaken);
            }
          }

          if(!empty($prevSessionStudents)) {
            $data['getStudents'] = $this->combineSessionData($data['getStudents'], $prevSessionStudents);
            $prevSessionFlag = true;
          }
        }

        $data['prevSessionName'] = $prevSessionName;
        $data['prevSessionFlag'] = $prevSessionFlag;

        $data['main_content']  = 'attendance/takeAttendance';
      }

      $data['irequest'] = $input;

      if(empty($data['getStudents'])) {
        $this->session->set_flashdata('flashError', 'No student data for the selected class/section');
        redirect('attendance');
      } else {
        
        $this->load->view('inc/template', $data);
      }
    }  

    public function get_student_tracking_info() {
      $section = $this->input->post('section');
      $attendance_date = $this->input->post('attendance_date');
      echo json_encode($this->attendanceModel->get_students_from_tracking($section, $attendance_date));
    }

    public function submitAttendance() {

      $input = $this->input->post();
      $day = $input['date'];   
      $day = date("Y-m-d", strtotime($day));
      $class = $input['class'];
      $section = $input['section'];
      unset($input['class']); unset($input['section']); unset($input['date']);

      $this->db->trans_begin();
      $insert_status = $this->attendanceModel->save_attendance($input, $day);

      if ($insert_status) {
        $this->db->trans_commit();

        $displayText = $this->attendanceModel->getClassSectionInfoByIds($class,$section);
        unset($input['sessionId']);
        $tAtt = count($input);
        $pAtt = 0;

        foreach ($input as $key => $value) {
          if($value == 1)
            $pAtt++;
        }

        $msg = ''; 

        $msg .= '<p>Attendance taken successfully.</p>';
        $msg .= '<p>Class Section: <b>'. $displayText[0]->class_name.'-'.$displayText[0]->section_name.'</b></p>';
        $msg .= '<p>Total Students: <b>'.$tAtt.'</b></p>';
        $msg .= '<p>Present Students: <b>'.$pAtt.'</b></p>';
        $msg .= '<p>Absent Students: <b>'.($tAtt-$pAtt).'</b></p>';

        $this->session->set_flashdata('flashSuccess', $msg);
      } else {
        $this->db->trans_rollback();
        $this->session->set_flashdata('flashError', 'Session expired unable to Insert Attendance Data.');
      }

      // $day_wise_notification=$this->settings->getSetting("student_attendancev2_enable_day_absentees_notification");

      // if($day_wise_notofication){
        redirect('attendance/Attendance_controller/markAttendance/'.$insert_status."/".$day);
      // }else{
        // redirect('attendance/Attendance_controller/markAttendance');
      // }

    }

    public function editAttendance() {

      $input = $this->input->post();
      // echo '<pre>'; print_r($input); die();
      unset($input['class']); unset($input['section']); unset($input['date']); 


      $insert_status = $this->attendanceModel->edit_attendance($input);

      if ($insert_status) {
        $this->session->set_flashdata('flashSuccess', 'Attendance Edited Successfully Inserted.');
      } else {
        $this->session->set_flashdata('flashError', 'Unable to Edit Attendance Data.');
      }

      redirect('attendance');
    }

    public function getHistory() {
      $input = $this->input->post('id');

      $historyDetails = $this->attendanceModel->getHistoryDetails($input);
      //echo "<pre>"; print_r($historyDetails); die();

      $html_str = '';
      $check = [];
      if(!empty($historyDetails['prepare_data'])) {
        foreach ($historyDetails['prepare_data'] as $key => $value) {

          foreach ($value as $key1 => $value1) {
            $html_str .= '<tr>';
            $html_str .= '<td class="larger">' . (isset($value1['staff']['name']) && !empty($value1['staff']['name']) ? $value1['staff']['name'] : $value1['staff']) . '</td>';
            $html_str .= '<td class="smaller">'.$value1['type'].'</td>';
            if($value1['history']['event'] == 0)
              $html_str .= '<td class="smaller">A</td>';
            else 
              $html_str .= '<td class="smaller">P</td>';
            if(isset($value1['history']['type'])) {
              $ctype = $value1['history']['type'];
              if($ctype == 4) {
                $html_str .= '<td class="smaller">Class</td>';
              } elseif ($ctype == 5) {
                $html_str .= '<td class="smaller">Class-update</td>';
              } elseif ($ctype == 0) {
               $html_str .= '<td class="smaller">Emergency Exit</td>';
              } elseif ($ctype == 1) {
                $html_str .= '<td class="smaller">Late-comer</td>';
              } elseif ($ctype == 2) {
                $html_str .= '<td class="smaller">Health Care</td>';
              } elseif ($ctype == 3) {
                $html_str .= '<td class="smaller">Fresh Entry</td>';
              } elseif ($ctype == 6) {
                $html_str .= '<td class="smaller">Competition</td>';
              } elseif ($ctype == 7) {
                $html_str .= '<td class="smaller">Competition-update</td>';
              }elseif ($ctype == 8) {
                $html_str .= '<td class="smaller">Back Fill</td>';
              }
            } else {
              $html_str .= '<td class="smaller"></td>';
            }

            if(isset($value1['history']['created_at']))
              $html_str .= '<td class="larger">'.date('d-M-Y h:i A', strtotime($value1['history']['created_at'])).'</td>';

            if(isset($value1['history']['updated_at']))
              $html_str .= '<td class="larger">'.date('d-M-Y h:i A', strtotime($value1['history']['updated_at'])).'</td>';

            
            $html_str .= '</tr>';
          }
        }
      }

      echo json_encode(['student' => $historyDetails['student'][0]->name,'history' => $html_str]);
    }

    public function getStudentAttendance() {

      $studentId =  $this->input->post('studentId');
      $result = $this->attendanceModel->getStudentAttendance($studentId);

      // echo '<pre>'; print_r($result); die();
      if(!empty($result)) {
        $html_str = '';
        $html_str .= '<table id="attendanceDataTable" class="table" cellspacing="0" width="100%"><thead><tr><th>Period Name</th><th>Status</th></tr></thead>
          <tbody><tr>';
        foreach ($result as $key => $value) {
            $html_str .='<tr>';
            $html_str .='<td>'.$value->short_name.'</td>';
            $html_str .='<td>';
            if($value->status == 0) {
              $html_str .='<label class="switch">';
              $html_str .='<input type="hidden" name="'.$value->id.'" value="0">';
              $html_str .='<input class="attendance_event" type="checkbox" value="0" name="'.$value->id.'">';
              $html_str .='<span></span></label>';
            } else {
              $html_str .='<label class="switch">';
              $html_str .='<input type="hidden" name="'.$value->id.'" value="0">';
              $html_str .='<input class="attendance_event" type="checkbox" checked value="1" name="'.$value->id.'">';
              $html_str .='<span></span></label>';
            }
            $html_str .='</td>';
            $html_str .='</tr>';
        }  

        $html_str .= '</tr></tbody></table>';            

      } 

      echo $html_str;

    
    }

    public function getClassess() {
      
        $sectionid = $_POST['sectionid'];
        $selected_date = $_POST['selected_date'];
        $getclassectioninfo = $this->student->getclassection_NEW($sectionid);

        $timestamp = strtotime($selected_date);
        if(date('l', $timestamp) == 'Saturday') 
          $type = 2;
        else
          $type = 1;

         $attendanceMaster = $this->attendanceMaster->getAttendanceMasterByClass_NEW($sectionid, $type);  


        echo json_encode(['getclassectioninfo' =>$getclassectioninfo, 'count' => count($attendanceMaster), 'attendanceMaster' => $attendanceMaster ]);
    }

    public function getSectionByClass() {

      if(isset($_POST['classid'])) {
        $classid = $_POST['classid'];
        $getclassectioninfo = $this->student->getclassection($classid);
        echo json_encode($getclassectioninfo);
      } 

    }

    public function getStudentsByClassSection() {

      
      $sectionid = $_POST['sectionid'];
      $type = $_POST['type'];

      $session_id = $this->attendanceModel->getAttendanceSessionID_NEW($sectionid, date("Y-m-d")); 

      $latecomerConfig = $this->settings->getSetting('show_all_students_in_latecomer');

      if($session_id == 0) {
        //TODO: Ask Karthik what does this session_id==0 mean?
        if($type == 2 || (!$latecomerConfig)) {
          $getclassectioninfo = $this->student->getStudentsByClassSectionids($sectionid);  
          echo json_encode($getclassectioninfo);        
        } else {
          echo json_encode([]);       
        }

      } else {

        if($type == 0) {
          $getclassectioninfo = $this->attendanceModel->getStuByAttStatus($session_id, 1);
        } elseif($type == 1) {
          // Get only Absent Students
          $getclassectioninfo = $this->attendanceModel->getStuByAttStatus($session_id, $latecomerConfig);
        } if($type == 2) {
          $getclassectioninfo = $this->student->getStudentsByClassSectionids_NEW($sectionid);     
        } elseif($type == 3) {
          $getclassectioninfo = $this->attendanceModel->getStuByAttStatus($session_id, 0);
        }
        echo json_encode($getclassectioninfo);
      }
    }

    public function editStudentAttendance() {

        $ids    = $_POST['dataId'];
        $status = $_POST['status'];

        if($status == 0) {
          $status = 1;
        } else {
          $status = 0;
        }

        $session_id = $this->attendanceModel->edit_attendance_ids($ids, $status);
        echo json_encode(['status' => true, 'message' => 'updated']);
    }

    public function editStudentPeriodwise($input) {

      list($studentId, $student_admission_id, $session_id, $classid, $classsection, $select_date, $selectsession) = explode('_', $input);

      $data['result'] = $this->attendanceModel->getStudentPeriodwise($studentId, $student_admission_id, $session_id);
      $data['student_info'] = $this->attendanceModel->getStudentInfo($studentId, $student_admission_id);
      $data['classid'] = $classid;
      $data['classsection'] = $classsection;
      $data['select_date'] = $select_date;
      $data['selectsession'] = $selectsession;


      $data['main_content'] = 'attendance/editStudentDayWiseAttendance';
      $this->load->view('inc/template', $data);
    }

    public function submitEditAttendance() {
      $all_input = $this->input->post();

      $input['classid'] = $all_input['classid'];
      $input['classsection'] = $all_input['classsection'];
      $input['select_date'] = $all_input['select_date'];
      $input['selectsession'] = $all_input['selectsession'];

      if($all_input['submittedvalue'] == 'submit') {

        unset($all_input['classid']);
        unset($all_input['classsection']);
        unset($all_input['select_date']);
        unset($all_input['selectsession']);
        unset($all_input['submittedvalue']);

        $this->attendanceModel->updateAttendenceDataById($all_input, 5);
        $this->session->set_flashdata('flashSuccess', 'Attendance edited successfully.');
        $this->submitAttendanceRequest($input);
      } else {
        $this->session->set_flashdata('flashWarning', 'Edit cancelled successfully.');
        $this->submitAttendanceRequest($input);
      }
    }

    public function manuelInsertStudentAttendance() {
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'attendance/manuelInsertStudentAttendance_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'attendance/manuelInsertStudentAttendance_mobile';
      }else{
        $data['main_content'] = 'attendance/manuelInsertStudentAttendance';    	
      }
      $this->load->view('inc/template', $data);
        
    }

    public function submitManuelInsertStudent() {

      $input = $this->input->post();
      $student_data = $this->attendanceModel->getStudentDetailsByAdmiID($input['admission_number']);

      $selected_date = date("Y-m-d", strtotime($input['select_date']));

      if(empty($student_data)) {
        $this->session->set_flashdata('flashError', 'Student data not found for this admission number');
        redirect('attendance/Attendance_controller/manuelInsertStudentAttendance');
      }

      $status_check = $this->attendanceModel->checkAndInsertAttandance($student_data, $selected_date);

      if(!$status_check['status']) {
        $this->session->set_flashdata('flashError', $status_check['msg']);
        redirect('attendance/Attendance_controller/manuelInsertStudentAttendance');
      } else {
        $this->session->set_flashdata('flashSuccess', $status_check['msg']);
        redirect('attendance/Attendance_controller/manuelInsertStudentAttendance');

      }
    }

    public function deleteAttendanceByClassSection() {
      $data['getclassinfo'] = $this->student->getclass();
      $data['class_section'] = $this->attendanceModel->getAllClassandSection();

      $data['main_content'] = 'attendance/indexDelete';
      $this->load->view('inc/template', $data);
    }

    public function submitDeleteAttendanceRequest() {

      $input = $this->input->post();
      $return_data = $this->getClassess($input['section'], $input['select_date'], false);
      echo '<pre>';
      print_r($input);
      print_r($return_data);

    }

    public function get_class_section_ids_according_to_staffAccessibility() {
      echo json_encode( $this->attendanceModel->get_class_section_ids_according_to_staffAccessibility() );
  }

  public function submit_new_std_Attendance(){
    $input = $this->input->post();
      $day = $input['date'];   
      $day = date("Y-m-d", strtotime($day));
      $class = $input['class'];
      $section = $input['section'];
      unset($input['class']); unset($input['section']); unset($input['date']);
      $insert_status = $this->attendanceModel->save_newly_added_std_attendance($input, $day);

      if ($insert_status) {
        $this->session->set_flashdata('flashSuccess', 'Edited Attendance Successfully Inserted.');
      } else {
        $this->session->set_flashdata('flashError', 'Session expired unable to Insert Attendance Data.');
      }
        redirect('attendance/Attendance_controller/markAttendance/'.$insert_status."/".$day);

  }
    
}


