<?php

class Examination extends CI_Controller {

	function __construct()
	{
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
			redirect('dashboard', 'refresh');
		}
		$this->load->model('parent_model');
		$this->load->model('parent/examination_model');
	}

	public function assessment_marks() {
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['acad_years'] = $this->examination_model->get_assessment_acad_years();
		if ($this->mobile_detect->isTablet()) {
			$data['main_content']    = 'parent/examination/tablet_marks_index';
		}else if($this->mobile_detect->isMobile()){
			$data['main_content']    = 'parent/examination/mobile_marks_index';
		}else{
			$data['main_content']    = 'parent/examination/marks_index';     	
		}
		$this->load->view('inc/template', $data);
	}

	public function getAssessments() {
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$acad_year_id = $_POST['acad_year_id'];
		$class_id = $this->examination_model->getStudentClassId($student_id, $acad_year_id);
		$data['assessments'] = $this->examination_model->getAssessmentsByClass($class_id);
		echo json_encode($data);
	}

	public function getAssessmentMarks() {
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$assessment_id = $_POST['assessment_id'];
		$data['marks'] = $this->examination_model->getAssessmentMarks($assessment_id, $student_id);
		$data['computed'] = $this->examination_model->getComputedMarks($assessment_id, $student_id);
		// echo "<pre>"; print_r($data); die();
		echo json_encode($data);
	}
}