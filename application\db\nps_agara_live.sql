-- MySQL dump 10.13  Distrib 5.7.17, for Win64 (x86_64)
--
-- Host: localhost    Database: npsagara
-- ------------------------------------------------------
-- Server version	5.6.40-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `academic_year`
--

DROP TABLE IF EXISTS `academic_year`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `academic_year` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `acad_year` varchar(100) NOT NULL,
  `is_current_year` tinyint(4) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `accounts`
--

DROP TABLE IF EXISTS `accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `tracknpay_vendor_id` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `address_info`
--

DROP TABLE IF EXISTS `address_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `address_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Address_line1` varchar(255) NOT NULL,
  `Address_line2` varchar(255) NOT NULL,
  `area` varchar(100) NOT NULL,
  `district` varchar(100) NOT NULL,
  `state` varchar(100) NOT NULL,
  `country` varchar(100) NOT NULL,
  `pin_code` varchar(10) NOT NULL,
  `address_type` tinyint(4) NOT NULL COMMENT '0: Present Address, 1: Permanent Address',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `avatar_type` tinyint(4) NOT NULL COMMENT '1: Student, 2: Parent, 3: Super admin, 4: Staff',
  `stakeholder_id` int(11) NOT NULL COMMENT 'Avatar ID is a staff_id or student_id or parent_id depending on avatar type',
  `unformatted_address` text,
  `phone_number` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_documents`
--

DROP TABLE IF EXISTS `admission_documents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admission_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `af_id` int(11) NOT NULL,
  `document_type` varchar(100) NOT NULL,
  `document_uri` varchar(100) NOT NULL,
  `document_other` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_forms`
--

DROP TABLE IF EXISTS `admission_forms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admission_forms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `au_id` int(11) NOT NULL,
  `academic_year_applied_for` varchar(100) NOT NULL,
  `grade_applied_for` varchar(100) NOT NULL,
  `std_name` varchar(255) NOT NULL,
  `dob` date NOT NULL,
  `gender` char(2) NOT NULL COMMENT 'M:Male, F:Female',
  `birth_taluk` varchar(255) NOT NULL,
  `birth_district` varchar(100) NOT NULL,
  `nationality` varchar(100) NOT NULL,
  `religion` varchar(100) NOT NULL,
  `std_mother_tongue` varchar(100) NOT NULL,
  `physical_disability` char(2) NOT NULL,
  `learning_disability` char(2) NOT NULL,
  `f_name` varchar(255) NOT NULL,
  `f_addr` text NOT NULL,
  `f_district` varchar(100) NOT NULL,
  `f_state` varchar(100) NOT NULL,
  `f_county` varchar(100) NOT NULL,
  `f_pincode` varchar(50) NOT NULL,
  `f_office_ph` varchar(100) NOT NULL,
  `f_res_ph` varchar(100) NOT NULL,
  `f_mobile_no` varchar(100) NOT NULL,
  `f_email_id` varchar(100) NOT NULL,
  `f_position` varchar(100) NOT NULL,
  `f_company_name` varchar(255) NOT NULL,
  `f_company_addr` text NOT NULL,
  `f_company_district` varchar(100) NOT NULL,
  `f_company_state` varchar(100) NOT NULL,
  `f_company_county` varchar(100) NOT NULL,
  `f_company_pincode` varchar(50) NOT NULL,
  `f_annual_gross_income` varchar(100) NOT NULL,
  `m_name` varchar(255) NOT NULL,
  `m_addr` text NOT NULL,
  `m_district` varchar(100) NOT NULL,
  `m_state` varchar(100) NOT NULL,
  `m_county` varchar(100) NOT NULL,
  `m_office_ph` varchar(100) NOT NULL,
  `m_res_ph` varchar(100) NOT NULL,
  `m_mobile_no` varchar(100) NOT NULL,
  `m_email_id` varchar(100) NOT NULL,
  `m_position` varchar(100) NOT NULL,
  `m_company_name` varchar(100) NOT NULL,
  `m_company_addr` text NOT NULL,
  `m_company_district` varchar(100) NOT NULL,
  `m_company_state` varchar(100) NOT NULL,
  `m_company_county` varchar(100) NOT NULL,
  `m_company_pincode` varchar(100) NOT NULL,
  `m_pincode` varchar(100) NOT NULL,
  `m_annual_gross_income` varchar(100) NOT NULL,
  `std_photo_uri` varchar(255) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `nationality_other` varchar(100) DEFAULT NULL,
  `religion_other` varchar(100) DEFAULT NULL,
  `mother_tongue_other` varchar(100) DEFAULT NULL,
  `application_no` varchar(100) NOT NULL,
  `sibling_student_name` varchar(255) DEFAULT NULL,
  `sibling_student_class` varchar(100) DEFAULT NULL,
  `f_area` varchar(255) NOT NULL,
  `m_area` varchar(255) NOT NULL,
  `f_company_area` varchar(255) NOT NULL,
  `m_company_area` varchar(255) NOT NULL,
  `instruction_file` varchar(100) NOT NULL COMMENT 'config instruction file name',
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `father_mother_tongue` varchar(255) DEFAULT NULL,
  `father_mother_tongue_other` varchar(255) DEFAULT NULL,
  `mother_mother_tongue` varchar(255) DEFAULT NULL,
  `mother_mother_tongue_other` varchar(255) DEFAULT NULL,
  `custom_field` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_prev_school`
--

DROP TABLE IF EXISTS `admission_prev_school`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admission_prev_school` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `af_id` int(11) NOT NULL,
  `au_id` int(11) NOT NULL,
  `year` varchar(100) NOT NULL,
  `school_name` varchar(255) NOT NULL,
  `class` varchar(100) NOT NULL,
  `board` varchar(100) DEFAULT NULL,
  `board_other` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_prev_school_marks`
--

DROP TABLE IF EXISTS `admission_prev_school_marks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admission_prev_school_marks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sub_name` varchar(100) NOT NULL,
  `grade` varchar(100) NOT NULL,
  `percentage` decimal(10,2) NOT NULL,
  `aps_id` int(11) NOT NULL COMMENT 'adm_prev_school_id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_status`
--

DROP TABLE IF EXISTS `admission_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admission_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `af_id` varchar(100) NOT NULL,
  `prev_status` varchar(100) NOT NULL,
  `curr_status` varchar(100) NOT NULL,
  `status_changed_by` varchar(100) NOT NULL,
  `status_changed_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `comments` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_user`
--

DROP TABLE IF EXISTS `admission_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admission_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` int(11) NOT NULL,
  `mobile_no` varchar(50) DEFAULT NULL,
  `otp` varchar(10) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_elective_group`
--

DROP TABLE IF EXISTS `assessment_elective_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessment_elective_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(100) NOT NULL,
  `class_id` int(11) NOT NULL,
  `mapping_string` varchar(100) DEFAULT NULL,
  `friendly_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_entities_group`
--

DROP TABLE IF EXISTS `assessment_entities_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessment_entities_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `entity_name` varchar(100) NOT NULL,
  `class_id` int(11) NOT NULL,
  `elective_group_id` int(11) DEFAULT NULL,
  `is_elective` tinyint(1) NOT NULL DEFAULT '0',
  `mapping_string` varchar(255) DEFAULT NULL,
  `grading_system_id` int(11) DEFAULT NULL,
  `sorting_order` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_entity_master`
--

DROP TABLE IF EXISTS `assessment_entity_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessment_entity_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `mapping_string` varchar(255) NOT NULL,
  `class_id` int(11) NOT NULL,
  `ass_type` varchar(45) NOT NULL,
  `ass_entity_gid` int(11) NOT NULL,
  `grading_system_id` int(11) DEFAULT NULL,
  `sorting_order` int(11) DEFAULT NULL,
  `short_name` varchar(255) DEFAULT NULL,
  `derived_formula` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_grades`
--

DROP TABLE IF EXISTS `assessment_grades`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessment_grades` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_id` int(11) NOT NULL,
  `grades` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_grading_system`
--

DROP TABLE IF EXISTS `assessment_grading_system`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessment_grading_system` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `grades` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_marks_card_templates`
--

DROP TABLE IF EXISTS `assessment_marks_card_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessment_marks_card_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) NOT NULL,
  `class_id` int(11) NOT NULL,
  `template_content` text,
  `assessments` text NOT NULL,
  `lock_remarks` tinyint(1) DEFAULT '0',
  `publish_status` tinyint(1) DEFAULT '0',
  `grading_systems` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_portions`
--

DROP TABLE IF EXISTS `assessment_portions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessment_portions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` int(11) NOT NULL,
  `portions` text,
  `publish_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0: Not published, 1: Published',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_remarks_history`
--

DROP TABLE IF EXISTS `assessment_remarks_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessment_remarks_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ass_marks_card_id` int(11) NOT NULL,
  `remarks` text NOT NULL,
  `action_by` int(11) NOT NULL,
  `modified_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_sections`
--

DROP TABLE IF EXISTS `assessment_sections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessment_sections` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` int(11) NOT NULL,
  `class_section_id` int(11) NOT NULL,
  `class_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_students_elective`
--

DROP TABLE IF EXISTS `assessment_students_elective`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessment_students_elective` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `ass_elective_gid` int(11) NOT NULL,
  `ass_entity_gid` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_subject_group`
--

DROP TABLE IF EXISTS `assessment_subject_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessment_subject_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` int(11) NOT NULL,
  `ass_entity_gid` int(11) NOT NULL,
  `portions` text,
  `date` date DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `portions_at` tinyint(1) DEFAULT NULL COMMENT '0:Subject Level, 1:Entity Level',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments`
--

DROP TABLE IF EXISTS `assessments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ass_type` varchar(45) NOT NULL COMMENT 'Internal:0, External:1. External will be published to parents.',
  `short_name` varchar(255) DEFAULT NULL,
  `long_name` varchar(255) DEFAULT NULL,
  `description` text,
  `publish_status` varchar(25) NOT NULL DEFAULT 'created' COMMENT 'Possible Status: created, pending_approval, approval, published',
  `release_marks` tinyint(1) DEFAULT '0',
  `generation_type` varchar(20) NOT NULL DEFAULT 'Manual',
  `formula` text,
  `sorting_order` int(11) DEFAULT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='To create Assessments for all classes';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_entities`
--

DROP TABLE IF EXISTS `assessments_entities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessments_entities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` int(11) NOT NULL,
  `ass_subject_gid` int(11) DEFAULT NULL,
  `entity_id` int(11) NOT NULL,
  `portions` text,
  `date` date DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `total_marks` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_entities_access`
--

DROP TABLE IF EXISTS `assessments_entities_access`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessments_entities_access` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessments_entities_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `access_level` varchar(45) NOT NULL COMMENT 'read, write, admin',
  `class_section_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_entities_marks_students`
--

DROP TABLE IF EXISTS `assessments_entities_marks_students`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessments_entities_marks_students` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `assessments_entities_id` int(11) NOT NULL COMMENT 'Assessment->SubSkill->TotalMarks ID',
  `marks` decimal(10,2) NOT NULL,
  `percentage` decimal(10,2) NOT NULL,
  `grade` varchar(10) NOT NULL,
  `status` tinyint(2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_entities_marks_students_history`
--

DROP TABLE IF EXISTS `assessments_entities_marks_students_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessments_entities_marks_students_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `assessments_entities_marks_students_id` bigint(20) NOT NULL,
  `action` text,
  `action_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_history`
--

DROP TABLE IF EXISTS `assessments_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessments_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` int(11) NOT NULL,
  `action` text COMMENT 'What are the fields that changed?',
  `action_by` int(11) NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_marks_card_history`
--

DROP TABLE IF EXISTS `assessments_marks_card_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessments_marks_card_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ass_marks_card_id` int(11) NOT NULL,
  `pdf_link` varchar(255) NOT NULL,
  `date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint(4) NOT NULL DEFAULT '0',
  `action_comment` text,
  `action_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_marks_cards`
--

DROP TABLE IF EXISTS `assessments_marks_cards`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessments_marks_cards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `remarks` text NOT NULL,
  `status` varchar(100) NOT NULL DEFAULT 'Not Generated' COMMENT 'Generated, Published, Verified',
  `created_by` int(11) NOT NULL,
  `active_marks_card_id` int(11) NOT NULL,
  `marks_card_temp_id` int(11) NOT NULL,
  `remark_status` varchar(45) DEFAULT 'Not Verified',
  `ack_status` tinyint(1) NOT NULL DEFAULT '0',
  `ack_on` datetime DEFAULT NULL,
  `ack_by` int(11) DEFAULT NULL,
  `additional_remarks` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `asset_allocation`
--

DROP TABLE IF EXISTS `asset_allocation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `asset_allocation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_id` int(11) NOT NULL,
  `asset_id` int(11) NOT NULL,
  `asset_count` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `asset_master`
--

DROP TABLE IF EXISTS `asset_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `asset_master` (
  `asset_id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_name` varchar(255) NOT NULL,
  `total_assets` int(11) NOT NULL,
  `available_assets` int(11) NOT NULL,
  `allocated_assets` int(11) NOT NULL,
  `asset_cost` decimal(10,2) NOT NULL,
  `asset_owner` varchar(255) NOT NULL,
  `total_cost` decimal(10,2) NOT NULL,
  PRIMARY KEY (`asset_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `attendance_leave_event`
--

DROP TABLE IF EXISTS `attendance_leave_event`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `attendance_leave_event` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `attendance_session_id` int(11) NOT NULL,
  `event_type` varchar(20) NOT NULL,
  `event_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `attendance_master`
--

DROP TABLE IF EXISTS `attendance_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `attendance_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_id` int(11) NOT NULL,
  `type` tinyint(4) NOT NULL COMMENT '"1:weekday2:saturday"',
  `name` varchar(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `attendance_master_group`
--

DROP TABLE IF EXISTS `attendance_master_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `attendance_master_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `attendance_master_id` int(11) NOT NULL,
  `timetable_template_periods_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `attendance_session`
--

DROP TABLE IF EXISTS `attendance_session`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `attendance_session` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `day` date NOT NULL,
  `attendance_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `avatar_id` int(11) NOT NULL,
  `class_id` int(11) NOT NULL,
  `class_section_id` int(11) NOT NULL,
  `attendance_master_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `attendance_student`
--

DROP TABLE IF EXISTS `attendance_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `attendance_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `attendance_session_id` int(11) NOT NULL,
  `attendance_master_id` int(11) NOT NULL,
  `attendance_master_group_id` int(11) NOT NULL,
  `status` tinyint(4) NOT NULL COMMENT '"1:present,0:absent"',
  `subject_id` int(11) NOT NULL,
  `substitute_id` int(11) NOT NULL,
  `history` text NOT NULL COMMENT 'json_value("staff_id,event,event_time")',
  `reference_type` tinyint(4) DEFAULT NULL COMMENT '1->competation',
  `reference_id` int(11) DEFAULT NULL,
  `reference_status` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `avatar`
--

DROP TABLE IF EXISTS `avatar`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `avatar` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `avatar_type` tinyint(4) NOT NULL COMMENT '1: Student, 2: Parent, 3: Super admin, 4: Staff',
  `stakeholder_id` int(11) NOT NULL COMMENT 'Avatar ID is a staff_id or student_id or parent_id depending on avatar type',
  `friendly_name` varchar(255) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `old_user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `books_cart`
--

DROP TABLE IF EXISTS `books_cart`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `books_cart` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lbc_id` int(11) NOT NULL,
  `avatar_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `building_master`
--

DROP TABLE IF EXISTS `building_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `building_master` (
  `room_id` int(11) NOT NULL AUTO_INCREMENT,
  `room_no` varchar(255) NOT NULL,
  `room_name` varchar(255) NOT NULL,
  `room_type` varchar(255) NOT NULL,
  `floor` varchar(20) NOT NULL,
  `block` varchar(20) NOT NULL,
  `schedulable` tinyint(1) NOT NULL,
  `schedulable_online_booking` tinyint(1) DEFAULT '0',
  `section_id` int(11) DEFAULT NULL,
  `allow_booking_for` tinyint(1) DEFAULT NULL COMMENT '0:all 1: only principal',
  PRIMARY KEY (`room_id`),
  UNIQUE KEY `room_no` (`room_no`,`floor`,`block`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cache`
--

DROP TABLE IF EXISTS `cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cache` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cache_key` varchar(100) NOT NULL,
  `cache_value` text NOT NULL,
  `Note` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `canteen_menu`
--

DROP TABLE IF EXISTS `canteen_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `canteen_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `menu_name` varchar(100) NOT NULL,
  `menu_price` decimal(10,0) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `canteen_transaction`
--

DROP TABLE IF EXISTS `canteen_transaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `canteen_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identification_code` varchar(100) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `canteen_transaction_items`
--

DROP TABLE IF EXISTS `canteen_transaction_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `canteen_transaction_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `canteen_trans_id` int(11) NOT NULL,
  `menu_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price_per_piece` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `card_transaction`
--

DROP TABLE IF EXISTS `card_transaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `card_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_mode` varchar(100) NOT NULL,
  `amount` float NOT NULL,
  `identification_code` varchar(100) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `transcation_type` varchar(100) NOT NULL,
  `transaction_type_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cert_templates`
--

DROP TABLE IF EXISTS `cert_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cert_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) NOT NULL,
  `html_content` text NOT NULL,
  `purpose` varchar(255) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `type` varchar(45) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ci_sessions`
--

DROP TABLE IF EXISTS `ci_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ci_sessions` (
  `id` varchar(40) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `timestamp` int(10) unsigned NOT NULL DEFAULT '0',
  `data` blob NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ci_sessions_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `circular_master`
--

DROP TABLE IF EXISTS `circular_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `circular_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by` int(11) NOT NULL,
  `circular_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `circular_content` text NOT NULL,
  `user_type` char(10) NOT NULL,
  `circular_title` varchar(255) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `is_published` tinyint(1) NOT NULL,
  `sent_type` varchar(45) NOT NULL,
  `category` varchar(100) NOT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `user_type_detail` text,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `circular_sent_to`
--

DROP TABLE IF EXISTS `circular_sent_to`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `circular_sent_to` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `circular_id` int(11) NOT NULL,
  `stakeholder_id` int(11) NOT NULL,
  `avatar_type` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `class`
--

DROP TABLE IF EXISTS `class`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `class` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_name` varchar(20) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `board` varchar(50) NOT NULL,
  `medium` varchar(50) NOT NULL,
  `type` tinyint(4) NOT NULL,
  `principal_id` int(11) DEFAULT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  `promotion_class` int(11) DEFAULT NULL,
  `is_placeholder` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `class_section`
--

DROP TABLE IF EXISTS `class_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `class_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_id` int(11) NOT NULL,
  `section_name` varchar(20) NOT NULL,
  `class_teacher_id` int(11) NOT NULL COMMENT 'Staff id of class teacher',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `room_id` int(11) DEFAULT NULL,
  `class_name` varchar(20) DEFAULT NULL,
  `is_placeholder` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `class_id` (`class_id`),
  CONSTRAINT `section_class_id` FOREIGN KEY (`class_id`) REFERENCES `class` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `combinations`
--

DROP TABLE IF EXISTS `combinations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `combinations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `combination` varchar(255) DEFAULT NULL,
  `af_id` int(11) DEFAULT NULL,
  `au_id` int(11) DEFAULT NULL,
  `combination_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `competition_attendance`
--

DROP TABLE IF EXISTS `competition_attendance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `competition_attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `competition_id` int(11) NOT NULL,
  `day` date NOT NULL,
  `student_id` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `history` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `competition_master`
--

DROP TABLE IF EXISTS `competition_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `competition_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `competition_name` varchar(100) NOT NULL,
  `venue_address` varchar(150) NOT NULL,
  `registration_fees` varchar(50) NOT NULL,
  `lastdate_registration` date NOT NULL,
  `venue_iscampus` char(10) NOT NULL,
  `transportation_mode` char(6) DEFAULT NULL,
  `staff_assigned` text NOT NULL,
  `organizer` varchar(100) NOT NULL,
  `status` int(11) NOT NULL,
  `created_by` int(11) NOT NULL,
  `staff_remarks` text NOT NULL,
  `principal_remarks` text NOT NULL,
  `description` text NOT NULL,
  `created_at` date NOT NULL,
  `competition_code` varchar(105) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `competition_student_registration`
--

DROP TABLE IF EXISTS `competition_student_registration`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `competition_student_registration` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `std_userId` int(11) NOT NULL,
  `competition_id` varchar(100) NOT NULL,
  `remarks` varchar(100) NOT NULL,
  `other_remarks` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `competition_time_details`
--

DROP TABLE IF EXISTS `competition_time_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `competition_time_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `competition_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `reporting_time` time NOT NULL,
  `competition_id` int(11) NOT NULL,
  `substitution_added` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `config`
--

DROP TABLE IF EXISTS `config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `value` text,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `type` varchar(45) NOT NULL DEFAULT 'string',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_UNIQUE` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `elective_student`
--

DROP TABLE IF EXISTS `elective_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `elective_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `subject_id` bigint(20) NOT NULL,
  `elective_group_id` int(11) NOT NULL,
  `class_section_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `class_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `student_id` (`student_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `event_master`
--

DROP TABLE IF EXISTS `event_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `event_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_name` varchar(100) NOT NULL,
  `venue_address` varchar(150) NOT NULL,
  `registration_fees` varchar(50) NOT NULL,
  `lastdate_registration` date NOT NULL,
  `venue_iscampus` char(10) NOT NULL,
  `transportation_mode` char(6) DEFAULT NULL,
  `staff_assigned` text NOT NULL,
  `organizer` varchar(100) NOT NULL,
  `status` int(11) NOT NULL,
  `created_by` int(11) NOT NULL,
  `staff_remarks` text NOT NULL,
  `principal_remarks` text NOT NULL,
  `description` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `event_time_details`
--

DROP TABLE IF EXISTS `event_time_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `event_time_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `reporting_time` time NOT NULL,
  `event_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `expense`
--

DROP TABLE IF EXISTS `expense`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `expense` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(55) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `purpose_id` int(11) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `voucher_url` varchar(255) DEFAULT NULL,
  `approved_by` varchar(50) DEFAULT NULL,
  `status` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_blueprint`
--

DROP TABLE IF EXISTS `feev2_blueprint`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_blueprint` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `filter_blueprint` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `receipt_gen_algo` varchar(45) NOT NULL,
  `allowed_payment_modes` text NOT NULL,
  `concession_mode` varchar(45) DEFAULT 'none' COMMENT '''percentage'',''amount''',
  `concession_algo` varchar(100) DEFAULT 'none' COMMENT '''none''',
  `enable_custom_fee` tinyint(4) NOT NULL,
  `enable_fee_cohort_check` tinyint(4) NOT NULL,
  `acad_year_id` int(11) NOT NULL,
  `is_acad_fee` tinyint(4) NOT NULL DEFAULT '0',
  `is_split` tinyint(4) NOT NULL DEFAULT '0',
  `display_fields` text,
  `select_filter` text NOT NULL,
  `select_columns` text NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_blueprint_components`
--

DROP TABLE IF EXISTS `feev2_blueprint_components`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_blueprint_components` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feev2_blueprint_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `vendor_code` varchar(100) DEFAULT NULL,
  `is_concession_eligible` tinyint(1) NOT NULL DEFAULT '1',
  `enable_if_partial` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_blueprint_installment_types`
--

DROP TABLE IF EXISTS `feev2_blueprint_installment_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_blueprint_installment_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feev2_installment_type_id` int(11) NOT NULL,
  `feev2_blueprint_id` int(11) NOT NULL,
  `discount_algo` varchar(100) NOT NULL,
  `discount_amount` varchar(100) DEFAULT NULL,
  `allow_partial` int(11) DEFAULT '0' COMMENT '''0 or 1''',
  `allocation_algo` varchar(45) NOT NULL DEFAULT 'none' COMMENT '''equal'',''custom''',
  `allocation_params` varchar(45) DEFAULT NULL,
  `fine_amount_algo` varchar(45) NOT NULL DEFAULT 'none' COMMENT '''fine_per_day'',''fine_per_month''',
  `fine_amount_params` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_cohort_installment_components`
--

DROP TABLE IF EXISTS `feev2_cohort_installment_components`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_cohort_installment_components` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feev2_cohort_id` int(11) NOT NULL,
  `feev2_blueprint_installment_types_id` int(11) NOT NULL,
  `feev2_installment_id` int(11) NOT NULL,
  `feev2_blueprint_component_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_cohort_student`
--

DROP TABLE IF EXISTS `feev2_cohort_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_cohort_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `blueprint_id` int(11) NOT NULL,
  `feev2_cohort_id` int(11) NOT NULL,
  `publish_status` varchar(100) NOT NULL DEFAULT 'NOT_PUBLISHED' COMMENT 'NOT_PUBLISHED, PUBLISHED',
  `fee_cohort_status` varchar(100) NOT NULL DEFAULT 'STANDARD' COMMENT '''STANDARD'', ''CUSTOM''',
  `fee_collect_status` varchar(45) DEFAULT 'NOT_STARTED' COMMENT 'NOT_STARTED, STARTED',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_cohorts`
--

DROP TABLE IF EXISTS `feev2_cohorts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_cohorts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filter` text NOT NULL,
  `total_fee` decimal(10,2) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL COMMENT 'This is the avatar id that last modified this table',
  `acad_year_id` int(11) NOT NULL,
  `blueprint_id` int(11) NOT NULL,
  `default_ins` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_concessions`
--

DROP TABLE IF EXISTS `feev2_concessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_concessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cohort_student_id` int(11) NOT NULL,
  `concession_by` int(11) NOT NULL,
  `concession_name` varchar(255) NOT NULL,
  `is_applied` tinyint(4) NOT NULL,
  `transaction_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_concessions_installment_components`
--

DROP TABLE IF EXISTS `feev2_concessions_installment_components`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_concessions_installment_components` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feev2_concession_id` int(11) NOT NULL,
  `feev2_blueprint_installment_types_id` int(11) NOT NULL,
  `feev2_installments_id` int(11) NOT NULL,
  `feev2_blueprint_components_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_installment_types`
--

DROP TABLE IF EXISTS `feev2_installment_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_installment_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_installments`
--

DROP TABLE IF EXISTS `feev2_installments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_installments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `end_date` date NOT NULL,
  `start_date` date NOT NULL,
  `feev2_installment_type_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_student_installments`
--

DROP TABLE IF EXISTS `feev2_student_installments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_student_installments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_student_schedule_id` int(11) NOT NULL,
  `feev2_installments_id` int(11) NOT NULL,
  `installment_amount` decimal(10,2) NOT NULL,
  `installment_amount_paid` decimal(10,2) DEFAULT NULL,
  `status` varchar(100) NOT NULL DEFAULT 'Not Started' COMMENT 'Not Started, Full, Partial',
  `total_concession_amount` decimal(10,2) DEFAULT NULL,
  `total_concession_amount_paid` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_student_installments_components`
--

DROP TABLE IF EXISTS `feev2_student_installments_components`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_student_installments_components` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_student_installment_id` int(11) NOT NULL,
  `blueprint_component_id` int(11) NOT NULL,
  `component_amount` decimal(10,2) NOT NULL,
  `component_amount_paid` decimal(10,2) DEFAULT NULL,
  `concession_amount` decimal(10,2) DEFAULT NULL,
  `concession_amount_paid` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_student_schedule`
--

DROP TABLE IF EXISTS `feev2_student_schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_student_schedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feev2_cohort_student_id` int(11) NOT NULL,
  `total_fee` decimal(10,2) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) DEFAULT NULL,
  `approval_date` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `approved_by` int(11) DEFAULT NULL,
  `total_fee_paid` decimal(10,2) DEFAULT NULL,
  `payment_status` varchar(100) NOT NULL COMMENT 'Full, Partial, Not started',
  `discount` decimal(10,2) DEFAULT NULL,
  `total_concession_amount` decimal(10,2) DEFAULT NULL,
  `total_concession_amount_paid` decimal(10,2) DEFAULT NULL,
  `total_fine_amount` decimal(10,2) DEFAULT NULL,
  `total_card_charge_amount` decimal(10,2) DEFAULT NULL,
  `carry_over_total_amount` decimal(10,2) DEFAULT NULL,
  `carry_over_concession` decimal(10,2) DEFAULT NULL,
  `carry_over_amount_paid` decimal(10,2) DEFAULT NULL,
  `carry_over_status` varchar(100) DEFAULT 'Not Paid' COMMENT 'Not Paid, Paid',
  `acad_year_id` int(11) NOT NULL,
  `feev2_blueprint_installment_types_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_transaction`
--

DROP TABLE IF EXISTS `feev2_transaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `fee_student_schedule_id` int(11) NOT NULL,
  `amount_paid` decimal(10,2) NOT NULL,
  `fine_amount` decimal(10,2) DEFAULT NULL,
  `discount_amount` decimal(10,2) DEFAULT NULL,
  `paid_datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `collected_by` int(11) NOT NULL,
  `soft_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '1: soft delete ',
  `concession_amount` decimal(10,2) NOT NULL,
  `transaction_mode` int(11) NOT NULL COMMENT 'counter, online, via link',
  `receipt_pdf_link` varchar(255) NOT NULL,
  `receipt_number` varchar(255) NOT NULL,
  `card_charge_amount` decimal(10,2) DEFAULT NULL,
  `acad_year_id` varchar(100) NOT NULL,
  `status` varchar(45) NOT NULL DEFAULT 'INITIATED',
  `op_recon_status` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

ALTER TABLE `feev2_transaction` ADD `receipt_html` LONGTEXT NOT NULL AFTER `op_recon_status`;
ALTER TABLE `feev2_transaction` ADD `pdf_status` TINYINT(4) NOT NULL AFTER `receipt_html`;

/* Fee receipt template */
CREATE TABLE `feev2_receipt_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template` longtext NOT NULL,
  `blueprint_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Table structure for table `feev2_transaction_installment_component`
--

DROP TABLE IF EXISTS `feev2_transaction_installment_component`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_transaction_installment_component` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_transaction_id` int(11) NOT NULL,
  `blueprint_component_id` int(11) NOT NULL,
  `blueprint_installments_id` int(11) NOT NULL,
  `amount_paid` decimal(10,2) NOT NULL,
  `concession_amount` decimal(10,2) NOT NULL,
  `fee_student_installments_components_id` int(11) NOT NULL,
  `fee_student_installments_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_transaction_payment`
--

DROP TABLE IF EXISTS `feev2_transaction_payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feev2_transaction_payment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_transaction_id` int(11) NOT NULL,
  `payment_type` varchar(100) NOT NULL COMMENT 'dd, cash, cheque, cc, dc, payment gateway',
  `bank_name` varchar(255) DEFAULT NULL,
  `bank_branch` varchar(255) DEFAULT NULL,
  `cheque_or_dd_date` date DEFAULT NULL,
  `card_reference_number` varchar(255) DEFAULT NULL COMMENT 'Used for credit and debit cards',
  `reconciliation_status` tinyint(4) NOT NULL COMMENT '0:not required, 1:pending, 2:completed',
  `recon_created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `recon_lastmodified_by` int(11) NOT NULL,
  `recon_submitted_on` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `remarks` varchar(255) DEFAULT NULL,
  `cheque_dd_nb_cc_dd_number` varchar(255) DEFAULT NULL,
  `payment_gateway_info` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flash_master`
--

DROP TABLE IF EXISTS `flash_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flash_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `flash_content` varchar(256) NOT NULL,
  `user_type` tinyint(4) NOT NULL COMMENT '0 - Student All, 1 - Staff All, 2 - Class',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 - Active, 1 - Inactive, 2 - Template',
  `published_by` int(11) NOT NULL,
  `published_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `send_to` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `gallery_images_master`
--

DROP TABLE IF EXISTS `gallery_images_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `gallery_images_master` (
  `image_id` int(11) NOT NULL AUTO_INCREMENT,
  `gallery_id` int(11) NOT NULL,
  `image_name` varchar(255) NOT NULL,
  `image_description` text NOT NULL,
  `image_tags` text NOT NULL,
  `image_status` tinyint(4) NOT NULL DEFAULT '1',
  PRIMARY KEY (`image_id`),
  KEY `galary_id` (`gallery_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `gallery_list`
--

DROP TABLE IF EXISTS `gallery_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `gallery_list` (
  `gallery_id` int(11) NOT NULL AUTO_INCREMENT,
  `gallery_name` varchar(255) NOT NULL,
  `gallery_description` text NOT NULL,
  `gallery_date` date NOT NULL,
  `gallery_location` varchar(255) NOT NULL,
  `gallery_visibility` tinyint(1) NOT NULL COMMENT '1 for only me, 2 for only staff, 3 for parents and staff and four for public',
  `gallery_status` int(11) NOT NULL DEFAULT '1' COMMENT '0 for deleted and 1 for active',
  `created_by` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`gallery_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `gallery_tags`
--

DROP TABLE IF EXISTS `gallery_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `gallery_tags` (
  `galary_tag_id` int(11) NOT NULL AUTO_INCREMENT,
  `galary_tag_name` text NOT NULL,
  PRIMARY KEY (`galary_tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `homework`
--

DROP TABLE IF EXISTS `homework`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `homework` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `section_id` int(11) NOT NULL,
  `created_date` datetime NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `body` text,
  `status` tinyint(1) NOT NULL,
  `staff_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_invoice_items`
--

DROP TABLE IF EXISTS `inventory_invoice_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_invoice_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_master_id` int(11) NOT NULL,
  `variant_id` int(11) NOT NULL,
  `variant_type` char(50) NOT NULL,
  `description` text,
  `hsn_sac_no` varchar(255) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `unit` varchar(50) DEFAULT NULL,
  `price` float DEFAULT NULL,
  `cgst` decimal(5,2) DEFAULT NULL,
  `sgst` decimal(5,2) DEFAULT NULL,
  `total_amount` float DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_invoice_master`
--

DROP TABLE IF EXISTS `inventory_invoice_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_invoice_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `invoice_no` varchar(255) DEFAULT NULL,
  `bill_no` varchar(255) DEFAULT NULL,
  `invoice_date` date DEFAULT NULL,
  `delivery_note` text,
  `mode_of_payment` varchar(100) DEFAULT NULL,
  `supplier_ref_no` varchar(100) DEFAULT NULL,
  `order_no` varchar(100) DEFAULT NULL,
  `order_date` date DEFAULT NULL,
  `dispatch_doc_no` varchar(100) DEFAULT NULL,
  `delivery_note_date` date DEFAULT NULL,
  `total_amount` float DEFAULT NULL,
  `payment_instrument_id` int(11) NOT NULL,
  `dispatched_by` varchar(255) DEFAULT NULL,
  `destination` varchar(255) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_payment_instruments`
--

DROP TABLE IF EXISTS `inventory_payment_instruments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_payment_instruments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `vendor_id` int(11) NOT NULL,
  `payment_type` char(20) NOT NULL,
  `bank_name` varchar(255) DEFAULT NULL,
  `account_no` int(11) DEFAULT NULL,
  `ifsc_code` varchar(50) DEFAULT NULL,
  `branch` varchar(100) DEFAULT NULL,
  `cheque_in_favor_of` varchar(255) DEFAULT NULL,
  `last_modified_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_product_master`
--

DROP TABLE IF EXISTS `inventory_product_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_product_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_code` varchar(255) NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `stockable` tinyint(1) NOT NULL,
  `picture_url` varchar(255) DEFAULT NULL,
  `description` text,
  `attributes` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_product_variant`
--

DROP TABLE IF EXISTS `inventory_product_variant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_product_variant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(256) NOT NULL,
  `attributes` text NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_stock_master`
--

DROP TABLE IF EXISTS `inventory_stock_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_stock_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `variant_id` int(11) NOT NULL,
  `initial_quantity` int(11) NOT NULL,
  `price_per_unit` int(11) NOT NULL,
  `vendor_name` varchar(256) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_vendor_address_info`
--

DROP TABLE IF EXISTS `inventory_vendor_address_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_vendor_address_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `address_type` tinyint(4) NOT NULL,
  `address_line1` varchar(255) NOT NULL,
  `address_line2` varchar(255) NOT NULL,
  `area` varchar(100) NOT NULL,
  `district` varchar(100) NOT NULL,
  `state` varchar(100) NOT NULL,
  `country` varchar(100) NOT NULL,
  `pin_code` varchar(10) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_vendor_master`
--

DROP TABLE IF EXISTS `inventory_vendor_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_vendor_master` (
  `vendor_id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_code` varchar(100) NOT NULL,
  `vendor_name` varchar(255) NOT NULL,
  `gst_no` varchar(255) NOT NULL,
  `vendor_email` varchar(100) NOT NULL,
  `vendor_website` varchar(255) NOT NULL,
  `contact_first_name` varchar(255) NOT NULL,
  `contact_last_name` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `contact_no` varchar(20) NOT NULL,
  `customer_service_no` varchar(20) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `on_board` date NOT NULL,
  `status` int(11) NOT NULL,
  PRIMARY KEY (`vendor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_vendor_products`
--

DROP TABLE IF EXISTS `inventory_vendor_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_vendor_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `product_variant_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `iot_test_data`
--

DROP TABLE IF EXISTS `iot_test_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `iot_test_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `payload` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `issue_master`
--

DROP TABLE IF EXISTS `issue_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `issue_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(100) NOT NULL,
  `answered_by` int(11) NOT NULL,
  `answered_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `remarks` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `item`
--

DROP TABLE IF EXISTS `item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_name` varchar(100) NOT NULL,
  `default_price` decimal(10,2) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `item_type` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `leave_staff`
--

DROP TABLE IF EXISTS `leave_staff`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `leave_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `leave_type` varchar(100) NOT NULL,
  `request_date` date NOT NULL,
  `from_date` date NOT NULL,
  `to_date` date NOT NULL,
  `noofdays` decimal(10,2) NOT NULL,
  `reason` text NOT NULL,
  `status` tinyint(1) NOT NULL COMMENT '0: Pending, 1: Approved, 2: Auto Approved, 3: Rejected',
  `description` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `leave_for` char(10) DEFAULT NULL,
  `leave_filed_by` int(11) NOT NULL,
  `leave_approved_by` int(11) DEFAULT NULL,
  `leave_staffcol` varchar(45) DEFAULT NULL,
  `substitution_added` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `leave_staff_substitute`
--

DROP TABLE IF EXISTS `leave_staff_substitute`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `leave_staff_substitute` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leave_id` int(11) NOT NULL,
  `staff_id` int(11) DEFAULT NULL,
  `substitute_date` date NOT NULL,
  `from_period` tinyint(1) DEFAULT NULL,
  `to_period` tinyint(1) DEFAULT NULL,
  `substitution_added` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `leave_student`
--

DROP TABLE IF EXISTS `leave_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `leave_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `leave_type` varchar(100) NOT NULL,
  `request_date` date NOT NULL,
  `from_date` date NOT NULL,
  `to_date` date NOT NULL,
  `reason` text NOT NULL,
  `doctor_certificate` varchar(255) DEFAULT NULL,
  `status` varchar(100) NOT NULL,
  `noofdays` int(11) NOT NULL,
  `description` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `leave_filed_by` int(11) NOT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  `leave_approved_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_books`
--

DROP TABLE IF EXISTS `library_books`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `library_books` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `book_type` varchar(50) NOT NULL,
  `category` varchar(100) NOT NULL,
  `language` varchar(100) NOT NULL,
  `author` varchar(100) NOT NULL,
  `yearof_publishing` varchar(50) NOT NULL,
  `publisher_name` varchar(50) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` text NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `book_title` varchar(100) NOT NULL,
  `series` varchar(100) DEFAULT NULL,
  `volume` varchar(100) DEFAULT NULL,
  `subject` varchar(100) DEFAULT NULL,
  `location_book` varchar(100) DEFAULT NULL,
  `source` varchar(100) DEFAULT NULL,
  `file_url` varchar(100) DEFAULT NULL,
  `contains` varchar(100) DEFAULT NULL,
  `edition` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_books_copies`
--

DROP TABLE IF EXISTS `library_books_copies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `library_books_copies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `book_id` int(11) NOT NULL,
  `access_code` varchar(100) DEFAULT NULL,
  `costof_book` varchar(100) DEFAULT NULL,
  `date_of_accession` date DEFAULT NULL,
  `remarks` varchar(255) DEFAULT NULL,
  `discarded_date` date DEFAULT NULL,
  `status` varchar(45) DEFAULT 'Available',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_cards`
--

DROP TABLE IF EXISTS `library_cards`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `library_cards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stake_holder_id` int(11) NOT NULL,
  `stake_holder_type` varchar(50) NOT NULL,
  `status` tinyint(4) NOT NULL COMMENT '1: Active , 2: In-active',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `master_id` int(11) NOT NULL,
  `card_access_code` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_fine_transaction`
--

DROP TABLE IF EXISTS `library_fine_transaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `library_fine_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trans_id` int(11) DEFAULT NULL,
  `identification_code` varchar(100) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `remarks` varchar(255) DEFAULT NULL,
  `payment_type` tinyint(4) DEFAULT NULL COMMENT 'config',
  `transaction_mode` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_master`
--

DROP TABLE IF EXISTS `library_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `library_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_type` varchar(50) NOT NULL,
  `hold_period` tinyint(4) NOT NULL,
  `fine_per_day` decimal(10,2) NOT NULL,
  `num_books` tinyint(4) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `book_type` varchar(250) NOT NULL,
  `display_color` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_npsrnr_books_raw_data`
--

DROP TABLE IF EXISTS `library_npsrnr_books_raw_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `library_npsrnr_books_raw_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `book_name` varchar(255) NOT NULL,
  `author` varchar(255) NOT NULL,
  `price` varchar(255) NOT NULL,
  `discount` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `publisher` varchar(255) NOT NULL,
  `published_year` varchar(255) NOT NULL,
  `book_arrival` date DEFAULT NULL,
  `subject_category` varchar(255) NOT NULL,
  `language` varchar(255) NOT NULL,
  `source` varchar(255) NOT NULL,
  `loanable` varchar(255) NOT NULL,
  `media_type` varchar(255) NOT NULL,
  `book_category` varchar(255) NOT NULL,
  `library` varchar(255) NOT NULL,
  `barcode` varchar(255) NOT NULL,
  `due_back_date` date DEFAULT NULL,
  `circulation_status` varchar(255) NOT NULL,
  `circulation_reason` varchar(255) NOT NULL,
  `discarded_date_Year` date DEFAULT NULL,
  `remarks` varchar(255) NOT NULL,
  `currency` varchar(255) NOT NULL,
  `value_in_currency` varchar(255) NOT NULL,
  `exchange_rate` varchar(255) NOT NULL,
  `cost_in_rupee` varchar(255) NOT NULL,
  `discount_amount` varchar(255) NOT NULL,
  `discount_price` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL,
  `class_section` varchar(255) NOT NULL,
  `checked_out_by` varchar(255) NOT NULL,
  `book_in_queue` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_stock_check_books`
--

DROP TABLE IF EXISTS `library_stock_check_books`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `library_stock_check_books` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `access_code` varchar(255) NOT NULL,
  `check_id` int(11) NOT NULL,
  `checked_by` int(11) NOT NULL,
  `checked_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_stock_check_master`
--

DROP TABLE IF EXISTS `library_stock_check_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `library_stock_check_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(55) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_transacation`
--

DROP TABLE IF EXISTS `library_transacation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `library_transacation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `issue_date` date NOT NULL,
  `status` tinyint(4) NOT NULL COMMENT '1:issue, 2: return',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `return_date` date DEFAULT NULL,
  `book_access_id` int(11) NOT NULL,
  `lbr_access_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `list_of_holidays`
--

DROP TABLE IF EXISTS `list_of_holidays`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `list_of_holidays` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `type` varchar(11) NOT NULL,
  `holiday_date` date NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `login_attempts`
--

DROP TABLE IF EXISTS `login_attempts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `login_attempts` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `login` varchar(100) NOT NULL,
  `time` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `logs`
--

DROP TABLE IF EXISTS `logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `errno` int(2) NOT NULL,
  `errtype` varchar(32) NOT NULL,
  `errstr` text NOT NULL,
  `errfile` varchar(255) NOT NULL,
  `errline` int(4) NOT NULL,
  `user_agent` varchar(120) NOT NULL,
  `ip_address` varchar(45) NOT NULL DEFAULT '0',
  `time` datetime NOT NULL,
  PRIMARY KEY (`id`,`ip_address`,`user_agent`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `multisection_subjects`
--

DROP TABLE IF EXISTS `multisection_subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `multisection_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(100) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `online_bookings`
--

DROP TABLE IF EXISTS `online_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `online_bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_id` int(11) NOT NULL,
  `period_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `date_booked` date NOT NULL,
  `class_section_id` int(11) DEFAULT NULL,
  `subject` varchar(45) DEFAULT NULL,
  `remarks` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `online_payment_master`
--

DROP TABLE IF EXISTS `online_payment_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `online_payment_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(255) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `init_date_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `source` varchar(225) DEFAULT NULL,
  `source_id` int(11) DEFAULT NULL,
  `source_callback_url` varchar(255) DEFAULT NULL,
  `payment_callback_url` varchar(255) DEFAULT NULL,
  `payment_service_provider` varchar(45) DEFAULT NULL,
  `currency` varchar(100) NOT NULL,
  `execution_mode` varchar(100) NOT NULL,
  `payment_to` varchar(255) NOT NULL,
  `hash_match` tinyint(4) NOT NULL,
  `tx_id` varchar(100) DEFAULT NULL,
  `tx_cardmasked` varchar(45) DEFAULT NULL,
  `tx_payment_channel` varchar(100) DEFAULT NULL,
  `tx_payment_mode` varchar(100) DEFAULT NULL,
  `tx_response_code` int(11) NOT NULL DEFAULT '-1',
  `tx_response_message` varchar(255) NOT NULL,
  `tx_date_time` datetime DEFAULT NULL,
  `status` varchar(100) NOT NULL DEFAULT 'INITIATED',
  `recon_status` varchar(100) NOT NULL DEFAULT 'NOT_CALLED',
  `transaction_by` int(11) NOT NULL,
  `is_split_payment` tinyint(4) NOT NULL,
  `split_json` text,
  `split_api_status` varchar(100) NOT NULL DEFAULT 'NOT_CALLED',
  `api_request` text,
  `api_response` text,
  `url_only_mode` varchar(40) NOT NULL,
  `settlement_status` varchar(40) NOT NULL DEFAULT 'NOT_SETTLED',
  `settlement_verification` varchar(40) NOT NULL,
  `settlement_id_json` text,
  `settlement_confirmed_by` int(11) DEFAULT NULL,
  `settlement_confirmed_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `outgoing_register_student`
--

DROP TABLE IF EXISTS `outgoing_register_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `outgoing_register_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_user_id` int(11) unsigned NOT NULL,
  `options_select` varchar(400) NOT NULL,
  `other_visitor_id` int(11) NOT NULL,
  `remarks` text NOT NULL,
  `create_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `parent`
--

DROP TABLE IF EXISTS `parent`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `parent` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `qualification` varchar(100) DEFAULT NULL,
  `occupation` varchar(100) DEFAULT NULL,
  `mobile_no` varchar(50) DEFAULT NULL,
  `aadhar_no` varchar(25) DEFAULT NULL,
  `student_id` int(11) NOT NULL,
  `picture_url` varchar(255) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `annual_income` decimal(10,2) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `mother_tongue` varchar(100) DEFAULT NULL,
  `email` varchar(150) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `parent_feedback`
--

DROP TABLE IF EXISTS `parent_feedback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `parent_feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feedback_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `feedback` text NOT NULL,
  `parent_id` int(11) NOT NULL,
  `ptm_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `parent_initiative`
--

DROP TABLE IF EXISTS `parent_initiative`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `parent_initiative` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `initiative` varchar(100) NOT NULL,
  `audience` varchar(150) NOT NULL,
  `duration` varchar(50) NOT NULL,
  `availability` varchar(150) NOT NULL,
  `description` text NOT NULL,
  `brief_about_parent` text NOT NULL,
  `status` varchar(255) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `principal_comments` text NOT NULL,
  `principal_remarks_date` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payroll_master`
--

DROP TABLE IF EXISTS `payroll_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payroll_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `payroll_slab_id` int(11) NOT NULL,
  `staff_salary` int(11) NOT NULL,
  `vpf` int(11) NOT NULL,
  `pan_number` varchar(255) NOT NULL,
  `account_number` varchar(255) NOT NULL,
  `ifsc` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payroll_payslip`
--

DROP TABLE IF EXISTS `payroll_payslip`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payroll_payslip` (
  `id` int(11) NOT NULL,
  `staffid` int(11) NOT NULL,
  `payroll_schedule_id` int(11) NOT NULL,
  `no_working_days` int(11) NOT NULL,
  `no_of_leave` int(11) NOT NULL,
  `basic_salary` decimal(10,2) NOT NULL,
  `staff_hra` decimal(10,2) NOT NULL,
  `staff_da` decimal(10,2) NOT NULL,
  `esi` decimal(10,2) NOT NULL,
  `epf` decimal(10,2) NOT NULL,
  `tds` int(11) NOT NULL,
  `vpf` decimal(10,2) NOT NULL,
  `loss_of_pay` int(11) NOT NULL,
  `total_earning` decimal(10,2) NOT NULL,
  `net_pay` decimal(10,2) NOT NULL,
  `total_deduct` decimal(10,2) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) DEFAULT NULL,
  `url` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payroll_profiles`
--

DROP TABLE IF EXISTS `payroll_profiles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payroll_profiles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `basic_salary_percent` tinyint(4) NOT NULL,
  `hra_percent` tinyint(4) NOT NULL,
  `other_allowance_percent` tinyint(4) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payroll_schedule`
--

DROP TABLE IF EXISTS `payroll_schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payroll_schedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `no_working_days` int(11) NOT NULL,
  `order` int(11) NOT NULL,
  `acad_year_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payroll_slabs`
--

DROP TABLE IF EXISTS `payroll_slabs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payroll_slabs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `slab_name` varchar(255) NOT NULL,
  `basic` float NOT NULL,
  `hra_percent` float NOT NULL,
  `cca_percent` float NOT NULL,
  `tds_percent` int(11) NOT NULL,
  `esi_percent` float NOT NULL,
  `epf_percent` int(11) NOT NULL,
  `slab_decription` text NOT NULL,
  `slab_start_value` int(11) NOT NULL,
  `slab_end_value` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `privileges`
--

DROP TABLE IF EXISTS `privileges`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `privileges` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(100) NOT NULL,
  `active` tinyint(4) NOT NULL COMMENT '1: Active, 0: Inactive',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `privileges_sub`
--

DROP TABLE IF EXISTS `privileges_sub`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `privileges_sub` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `privilege_id` int(11) NOT NULL,
  `active` tinyint(4) NOT NULL COMMENT '1:Active 0: Inactive',
  `is_critical` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_brands`
--

DROP TABLE IF EXISTS `product_brands`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_brands` (
  `brand_id` int(11) NOT NULL AUTO_INCREMENT,
  `brand_name` varchar(255) NOT NULL,
  PRIMARY KEY (`brand_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_colors`
--

DROP TABLE IF EXISTS `product_colors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_colors` (
  `color_id` int(11) NOT NULL AUTO_INCREMENT,
  `color_name` varchar(255) NOT NULL,
  PRIMARY KEY (`color_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_master`
--

DROP TABLE IF EXISTS `product_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_code` varchar(255) NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `stockable` tinyint(1) NOT NULL,
  `initial_quantity` int(11) NOT NULL,
  `measuring_unit` varchar(100) NOT NULL,
  `picture_url` varchar(255) NOT NULL,
  `color_flag` tinyint(1) NOT NULL,
  `size_flag` tinyint(1) NOT NULL,
  `description` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_sizes`
--

DROP TABLE IF EXISTS `product_sizes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_sizes` (
  `size_id` int(11) NOT NULL AUTO_INCREMENT,
  `size` int(11) NOT NULL,
  `measuring_unit` varchar(100) NOT NULL,
  PRIMARY KEY (`size_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_variant`
--

DROP TABLE IF EXISTS `product_variant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_variant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `brand_id` int(11) NOT NULL,
  `color_id` int(11) NOT NULL,
  `size_id` int(11) NOT NULL,
  `initial_quantity` int(11) NOT NULL,
  `reorder_point1` int(11) NOT NULL,
  `reorder_point2` int(11) NOT NULL,
  `storage_location` varchar(255) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ptm_cls_section`
--

DROP TABLE IF EXISTS `ptm_cls_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ptm_cls_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class` varchar(50) NOT NULL,
  `section` varchar(10) NOT NULL,
  `ptm_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ptm_master`
--

DROP TABLE IF EXISTS `ptm_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ptm_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `meeting_code` varchar(50) NOT NULL,
  `description` text NOT NULL,
  `date_of_meeting` date NOT NULL,
  `class_section` varchar(50) NOT NULL,
  `current_status` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ptm_master_history`
--

DROP TABLE IF EXISTS `ptm_master_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ptm_master_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `status` varchar(50) NOT NULL,
  `action_by` int(11) NOT NULL,
  `action_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ptm_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `publication_master`
--

DROP TABLE IF EXISTS `publication_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `publication_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by` int(11) NOT NULL,
  `published_date` date NOT NULL,
  `category` varchar(255) DEFAULT NULL,
  `content` text,
  `picture` varchar(255) DEFAULT NULL,
  `user_type` char(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `published_to`
--

DROP TABLE IF EXISTS `published_to`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `published_to` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `publication_id` int(11) NOT NULL,
  `student_or_section_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` varchar(255) NOT NULL,
  `active` tinyint(4) NOT NULL COMMENT '1: Active, 0: Inactive',
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL COMMENT 'Last Modified by which staff',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roles_privileges_sub`
--

DROP TABLE IF EXISTS `roles_privileges_sub`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles_privileges_sub` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `privilege_sub_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL COMMENT 'Last Modified by which avatar',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roles_staff`
--

DROP TABLE IF EXISTS `roles_staff`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL COMMENT 'Which Staff last modified this record?',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `room_timetable`
--

DROP TABLE IF EXISTS `room_timetable`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `room_timetable` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_id` int(11) NOT NULL,
  `allocation_data` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  `timetable_template_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roomlist_subject_section`
--

DROP TABLE IF EXISTS `roomlist_subject_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roomlist_subject_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sss_id` int(11) NOT NULL,
  `room_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `route_allocate`
--

DROP TABLE IF EXISTS `route_allocate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `route_allocate` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stop_id` int(11) NOT NULL,
  `route_no` int(11) NOT NULL COMMENT 'vehicle_data_id ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `school_calender`
--

DROP TABLE IF EXISTS `school_calender`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `school_calender` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_name` varchar(255) NOT NULL,
  `event_type` tinyint(4) NOT NULL COMMENT '1: event,  2: holiday  3: holiday range',
  `from_date` date NOT NULL,
  `to_date` date DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `applicable_to` tinyint(4) NOT NULL COMMENT '1: Staff, 2: Parents3: Both',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sms_credits_usage`
--

DROP TABLE IF EXISTS `sms_credits_usage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sms_credits_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sms_credits` int(11) NOT NULL,
  `action` varchar(50) NOT NULL,
  `action_by` int(11) NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `sms_master_id` int(11) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1: Active, 0:Deleted',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sms_master`
--

DROP TABLE IF EXISTS `sms_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sms_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action_by` int(11) NOT NULL,
  `sms_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `sms_content` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `user_type` varchar(50) NOT NULL,
  `msg_id` varchar(255) DEFAULT NULL,
  `sms_count` int(11) DEFAULT '0',
  `sent_to` text,
  `source` varchar(100) DEFAULT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sms_sent_to`
--

DROP TABLE IF EXISTS `sms_sent_to`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sms_sent_to` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sms_id` int(11) NOT NULL,
  `student_staff_id` int(11) NOT NULL,
  `mobile_no` varchar(50) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `retry_count` int(11) DEFAULT NULL,
  `response_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sms_templates`
--

DROP TABLE IF EXISTS `sms_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sms_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` text NOT NULL,
  `editable` tinyint(1) NOT NULL COMMENT '0: No, 1: Yes',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `applicable_to` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_duties`
--

DROP TABLE IF EXISTS `staff_duties`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff_duties` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_initiative_master`
--

DROP TABLE IF EXISTS `staff_initiative_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff_initiative_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `initiative_name` varchar(100) NOT NULL,
  `who_attend` varchar(100) NOT NULL,
  `duration` varchar(100) NOT NULL,
  `date` date NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(4) NOT NULL,
  `remarks` text,
  `principal_remarks` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_logs`
--

DROP TABLE IF EXISTS `staff_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `log` text NOT NULL,
  `task` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_logs_team`
--

DROP TABLE IF EXISTS `staff_logs_team`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff_logs_team` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_logs_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_master`
--

DROP TABLE IF EXISTS `staff_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `short_name` varchar(250) DEFAULT NULL,
  `status` tinyint(4) NOT NULL COMMENT '1: Pending, 2: Approved, 3: Rejected, 4: Alumni',
  `staff_type` varchar(100) NOT NULL COMMENT 'Teaching, Non-teaching, etc',
  `designation` varchar(255) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `father_name` varchar(255) DEFAULT NULL,
  `mother_name` varchar(255) DEFAULT NULL,
  `marital_status` tinyint(4) NOT NULL COMMENT '0: single 1:married',
  `spouse_name` varchar(255) DEFAULT NULL,
  `picture_url` varchar(255) DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `nationality` varchar(255) DEFAULT NULL,
  `gender` char(2) NOT NULL COMMENT 'M: male, F:female',
  `aadhar_number` varchar(30) DEFAULT NULL,
  `qualification` varchar(255) DEFAULT NULL,
  `subject_specialization` varchar(255) DEFAULT NULL,
  `total_experience` int(11) DEFAULT NULL,
  `total_education_experience` int(11) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `active` tinyint(4) NOT NULL COMMENT '0: blocked 1: active',
  `isdummy` tinyint(4) NOT NULL COMMENT 'Dummy Staff are used for testing and for creating floating periods',
  `contact_number` varchar(100) DEFAULT NULL,
  `alternative_number` varchar(255) DEFAULT NULL,
  `spouse_contact_no` varchar(100) DEFAULT NULL,
  `joining_date` date DEFAULT NULL,
  `emergency_info` text,
  `math_high_grade` varchar(45) DEFAULT NULL,
  `english_high_grade` varchar(45) DEFAULT NULL,
  `social_high_grade` varchar(45) DEFAULT NULL,
  `trained_to_teach` tinyint(1) DEFAULT NULL,
  `biometric_attendance_code` varchar(100) DEFAULT NULL,
  `appointed_subject` varchar(100) DEFAULT NULL,
  `classes_taught` varchar(100) DEFAULT NULL,
  `main_sub_taught` varchar(100) DEFAULT NULL,
  `add_sub_taught` varchar(100) DEFAULT NULL,
  `identification_code` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_observation`
--

DROP TABLE IF EXISTS `staff_observation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff_observation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `taken_by` int(11) NOT NULL,
  `observation` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_qualification`
--

DROP TABLE IF EXISTS `staff_qualification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff_qualification` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `qualification` varchar(255) NOT NULL,
  `year_of_passing` year(4) NOT NULL,
  `percentage` float NOT NULL,
  `institute` varchar(255) NOT NULL,
  `qualification_type` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_salary_details`
--

DROP TABLE IF EXISTS `staff_salary_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff_salary_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `total_salary` decimal(10,2) NOT NULL,
  `basic_salary` decimal(10,2) NOT NULL,
  `staff_hra` decimal(10,2) NOT NULL,
  `staff_da` decimal(10,2) NOT NULL,
  `total_gross` decimal(10,2) NOT NULL,
  `staff_esi` decimal(10,2) NOT NULL,
  `staff_pf` decimal(10,2) NOT NULL,
  `staff_pt` decimal(10,2) NOT NULL,
  `bank_account` varchar(50) NOT NULL,
  `pan_number` varchar(50) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `esi_number` varchar(100) DEFAULT NULL,
  `pf_uan_number` varchar(100) DEFAULT NULL,
  `salary_profile_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_special_requests`
--

DROP TABLE IF EXISTS `staff_special_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff_special_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `reason` text NOT NULL,
  `class_section_id` int(11) NOT NULL,
  `num_periods` tinyint(4) NOT NULL,
  `from_date` date NOT NULL,
  `to_date` date NOT NULL,
  `status` tinyint(4) NOT NULL COMMENT '0:pending,1:completed',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_subject_section`
--

DROP TABLE IF EXISTS `staff_subject_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff_subject_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_section_id` int(11) NOT NULL,
  `subject_id` bigint(20) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `period_span` tinyint(4) NOT NULL,
  `allotted` tinyint(1) NOT NULL,
  `allocation_type_staff` tinyint(4) NOT NULL COMMENT '1: Only one class can be taken, 2: Two classes can be taken, 4: 4 classes can be combined and taken',
  `allocation_type_room` tinyint(4) NOT NULL COMMENT '1: Room for 1 Section, 2: Room for 2 sections, 3: Room for 3 sections, 4: Room for 4 sections',
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`),
  KEY `class_section_id` (`class_section_id`),
  KEY `subject_id` (`subject_id`),
  KEY `subject_id_2` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_timetable`
--

DROP TABLE IF EXISTS `staff_timetable`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff_timetable` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `allocation_data` text NOT NULL COMMENT 'Denotes the allocation of a staff for use for processing. Stored in the format <num_periods_mon>,<num_periods_tue>,....|<1:allocated for Mon-P1, 0:NA>,....',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  `timetable_template_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staffduties_dates`
--

DROP TABLE IF EXISTS `staffduties_dates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staffduties_dates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `from_periods` varchar(20) NOT NULL,
  `to_periods` varchar(20) NOT NULL,
  `staffduties_id` int(11) NOT NULL,
  `substitution_added` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staffinitiative_staffs`
--

DROP TABLE IF EXISTS `staffinitiative_staffs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staffinitiative_staffs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `initiative_id` int(11) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staffinitiative_student`
--

DROP TABLE IF EXISTS `staffinitiative_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staffinitiative_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `initiative_id` int(11) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `stafflist_duties`
--

DROP TABLE IF EXISTS `stafflist_duties`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `stafflist_duties` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staffduties_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `stafflist_subject_section`
--

DROP TABLE IF EXISTS `stafflist_subject_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `stafflist_subject_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sss_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_actions`
--

DROP TABLE IF EXISTS `student_actions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `student_actions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `type` tinyint(4) NOT NULL COMMENT 'type 0-OutGoing,1-LateComer,2-HealthCare',
  `picked_by` varchar(50) DEFAULT NULL,
  `picked_id` int(11) NOT NULL,
  `entry_at` timestamp NULL DEFAULT NULL,
  `exit_at` timestamp NULL DEFAULT NULL,
  `remarks` text NOT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_admission`
--

DROP TABLE IF EXISTS `student_admission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `student_admission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admission_no` varchar(100) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `gender` char(2) NOT NULL COMMENT 'M : Male, F: Female',
  `nationality` varchar(50) DEFAULT NULL,
  `religion` varchar(50) DEFAULT NULL,
  `category` tinyint(4) DEFAULT NULL COMMENT 'See school config for definition',
  `mother_tongue` varchar(100) DEFAULT NULL,
  `contact_no` varchar(100) DEFAULT NULL,
  `sibling_type` tinyint(4) DEFAULT '0' COMMENT '11: no sibling, 12: first_joined_sibling, 13: second_joined_sibling, 14: third_joined_sibling, 15: fourth_joined_sibling',
  `sibling_id` int(11) DEFAULT NULL COMMENT 'stundent id of the sibling',
  `has_staff` tinyint(4) NOT NULL DEFAULT '0',
  `staff_id` int(11) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `is_rte` tinyint(1) DEFAULT NULL COMMENT 'is he an rte candidate  ',
  `admission_status` tinyint(4) NOT NULL COMMENT 'See school config for definition',
  `date_of_joining` date DEFAULT NULL COMMENT 'date of joining',
  `birth_taluk` varchar(100) DEFAULT NULL,
  `birth_district` varchar(255) DEFAULT NULL,
  `caste` varchar(100) DEFAULT NULL,
  `aadhar_no` varchar(50) DEFAULT NULL,
  `class_admitted_to` int(11) DEFAULT NULL,
  `admission_year` varchar(100) DEFAULT NULL,
  `emergency_info` text,
  `identification_code` varchar(100) DEFAULT NULL,
  `second_language_choice` varchar(255) NOT NULL DEFAULT 'NA',
  `third_language_choice` varchar(255) NOT NULL DEFAULT 'NA',
  `admission_acad_year_id` int(11) DEFAULT NULL,
  `email` varchar(150) DEFAULT NULL,
  `has_transport` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `admission_no` (`admission_no`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_health`
--

DROP TABLE IF EXISTS `student_health`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `student_health` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `blood_group` varchar(20) NOT NULL,
  `physical_disability` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:No,1:Yes',
  `learning_disability` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:No,1:Yes',
  `physical_disability_reason` varchar(100) DEFAULT NULL,
  `learning_disability_reason` varchar(100) DEFAULT NULL,
  `student_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `allergy` varchar(255) NOT NULL,
  `family_history` text NOT NULL,
  `anaemia` tinyint(1) NOT NULL COMMENT '0:absent, 1:mild, 2:moderate, 3:sever',
  `fit_to_participate` tinyint(1) NOT NULL COMMENT '0:fit, 1:with precaution, 2:not fit',
  `height` decimal(10,2) NOT NULL,
  `weight` decimal(10,2) NOT NULL,
  `hair` varchar(255) NOT NULL,
  `skin` varchar(255) NOT NULL,
  `ear` varchar(255) NOT NULL,
  `nose` varchar(255) NOT NULL,
  `throat` varchar(255) NOT NULL,
  `neck` varchar(255) NOT NULL,
  `respiratory` varchar(255) NOT NULL,
  `cardio_vascular` varchar(255) NOT NULL,
  `abdomen` varchar(255) NOT NULL,
  `nervous_system` varchar(255) NOT NULL,
  `left_eye` varchar(255) NOT NULL,
  `right_eye` varchar(255) NOT NULL,
  `extra_oral` text NOT NULL,
  `bad_breath` varchar(255) NOT NULL,
  `tooth_cavity` varchar(255) NOT NULL,
  `plaque` varchar(255) NOT NULL,
  `gum_inflamation` varchar(255) NOT NULL,
  `stains` varchar(255) NOT NULL,
  `gum_bleeding` varchar(255) NOT NULL,
  `soft_tissue` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_height_weight`
--

DROP TABLE IF EXISTS `student_height_weight`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `student_height_weight` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `height_in_cm` double NOT NULL,
  `weight_in_kg` double NOT NULL,
  `student_id` int(11) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_house`
--

DROP TABLE IF EXISTS `student_house`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `student_house` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `house` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_observation`
--

DROP TABLE IF EXISTS `student_observation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `student_observation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `std_id` int(11) NOT NULL,
  `observation` text NOT NULL,
  `staff_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_profile_update`
--

DROP TABLE IF EXISTS `student_profile_update`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `student_profile_update` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `student_status` tinyint(1) DEFAULT '0' COMMENT 'NULL:not saved, 0:std data saved, 1: health data saved, 2:father data saved, 3: mother data saved, 5: Completed',
  `health_status` tinyint(1) DEFAULT '0',
  `father_status` tinyint(1) DEFAULT '0',
  `mother_status` tinyint(1) DEFAULT '0',
  `completed` tinyint(1) DEFAULT '0',
  `control_status` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_relation`
--

DROP TABLE IF EXISTS `student_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `student_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `std_id` int(11) NOT NULL,
  `relation_id` int(11) NOT NULL,
  `relation_type` varchar(100) NOT NULL,
  `active` int(11) NOT NULL COMMENT '1:yes, 0: no',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_to_stops`
--

DROP TABLE IF EXISTS `student_to_stops`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `student_to_stops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `stop_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `moidified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `route` int(11) NOT NULL,
  `usage_mode` varchar(100) NOT NULL,
  `land_mark` varchar(150) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_year`
--

DROP TABLE IF EXISTS `student_year`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `student_year` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `roll_no` tinyint(4) NOT NULL,
  `class_id` int(11) NOT NULL,
  `class_section_id` int(11) DEFAULT NULL COMMENT 'class_section table id',
  `boarding` tinyint(4) DEFAULT NULL COMMENT 'See school config for definition',
  `picture_url` varchar(100) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `admission_type` tinyint(4) NOT NULL COMMENT 'See school config for definition',
  `board` tinyint(4) DEFAULT NULL COMMENT 'See school config for definition',
  `medium` tinyint(4) DEFAULT NULL COMMENT 'See school config for definition',
  `donor` varchar(200) DEFAULT NULL,
  `student_house` varchar(50) DEFAULT NULL,
  `previous_class_id` int(11) DEFAULT NULL,
  `previous_class_section_id` int(11) DEFAULT NULL,
  `fee_mode` varchar(45) NOT NULL DEFAULT 'auto' COMMENT '''auto'' or ''manual''',
  `student_admission_id` int(11) NOT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  `promotion_status` varchar(45) NOT NULL DEFAULT 'STUDYING',
  `promoted_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subject_elective_group`
--

DROP TABLE IF EXISTS `subject_elective_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subject_elective_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='master table, table to store subject elective groups.';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subjects`
--

DROP TABLE IF EXISTS `subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subjects` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `class_id` int(11) NOT NULL,
  `subject_code` varchar(10) NOT NULL,
  `short_name` varchar(25) NOT NULL,
  `long_name` varchar(50) NOT NULL,
  `description` varchar(150) NOT NULL,
  `type` varchar(75) NOT NULL COMMENT '0: scholastic-language, 1: scholastic-coresubject, 2: non-scholastic',
  `is_activity` tinyint(1) NOT NULL,
  `is_physical` tinyint(1) NOT NULL,
  `has_skills` tinyint(1) NOT NULL COMMENT 'whether the subject has sub skills?',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `display_color` varchar(20) NOT NULL,
  `is_multisection` tinyint(1) NOT NULL,
  `multisection_id` int(11) DEFAULT NULL,
  `is_elective` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `class_id` (`class_id`),
  CONSTRAINT `subjects_ibfk_1` FOREIGN KEY (`class_id`) REFERENCES `class` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subjects_skills`
--

DROP TABLE IF EXISTS `subjects_skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subjects_skills` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` bigint(11) NOT NULL,
  `name` varchar(20) NOT NULL,
  `sub_skills` varchar(150) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subjects_sub_skills`
--

DROP TABLE IF EXISTS `subjects_sub_skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subjects_sub_skills` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `skill_id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `assessment_type` smallint(5) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `skill_id` (`skill_id`),
  CONSTRAINT `subjects_sub_skills_ibfk_1` FOREIGN KEY (`skill_id`) REFERENCES `subjects_skills` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `substitute_dd`
--

DROP TABLE IF EXISTS `substitute_dd`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `substitute_dd` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sub_date` date NOT NULL,
  `week_day` int(11) NOT NULL,
  `class_section_id` int(11) NOT NULL,
  `sub_staff_id` int(11) NOT NULL,
  `origin_type` varchar(75) NOT NULL COMMENT 'Can be Leave, Competition, Event, Request, Duties, etc.',
  `origin_id` int(11) NOT NULL COMMENT 'origin type id',
  `status` varchar(25) NOT NULL,
  `new_staff_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `sub_tta_id` int(11) NOT NULL,
  `sub_pseqeb` tinyint(4) NOT NULL,
  `new_pseqeb` tinyint(4) DEFAULT NULL,
  `sub_staff_id_json` varchar(255) DEFAULT NULL,
  `origin_mode` varchar(45) NOT NULL COMMENT 'Can be Staff OR Period. Period can happen when this got created due to a swap.',
  `substitute_type` varchar(45) NOT NULL COMMENT 'Can be ''Staff'' or ''Period''.',
  `new_tta_id` int(11) DEFAULT NULL,
  `new_period_display` varchar(60) DEFAULT NULL COMMENT 'Is used only when substitute_type is ''Period''',
  `new_staff_id_json` varchar(255) DEFAULT NULL,
  `sub_period_display` varchar(150) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tasks_todo`
--

DROP TABLE IF EXISTS `tasks_todo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tasks_todo` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `date` date NOT NULL,
  `time` varchar(20) NOT NULL,
  `status` tinyint(1) NOT NULL COMMENT '1: complete  0 : incomplete',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `staff_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_allocated`
--

DROP TABLE IF EXISTS `timetable_allocated`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `timetable_allocated` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_period_id` int(11) NOT NULL,
  `class_section_id` int(11) NOT NULL,
  `week_day` tinyint(4) NOT NULL,
  `short_name` varchar(10) NOT NULL,
  `period_type` tinyint(4) NOT NULL,
  `period_seq` tinyint(4) NOT NULL,
  `period_seq_excl_break` tinyint(4) NOT NULL,
  `sss_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `period_id` (`class_period_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_override`
--

DROP TABLE IF EXISTS `timetable_override`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `timetable_override` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `date` date NOT NULL,
  `status` tinyint(4) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_override_periods`
--

DROP TABLE IF EXISTS `timetable_override_periods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `timetable_override_periods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `to_id` int(11) NOT NULL,
  `class_section_id` int(11) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `week_day` tinyint(4) DEFAULT NULL,
  `period_seq_excl_break` int(11) NOT NULL,
  `period_seq` int(11) NOT NULL,
  `period_short_name` varchar(100) NOT NULL,
  `period_long_name` varchar(255) NOT NULL,
  `subject_name` varchar(255) NOT NULL,
  `period_type` int(11) NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_override_periods_staff`
--

DROP TABLE IF EXISTS `timetable_override_periods_staff`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `timetable_override_periods_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `top_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_template`
--

DROP TABLE IF EXISTS `timetable_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `timetable_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_template_class_section`
--

DROP TABLE IF EXISTS `timetable_template_class_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `timetable_template_class_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `timetable_template_id` int(11) NOT NULL,
  `class_section_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_template_periods`
--

DROP TABLE IF EXISTS `timetable_template_periods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `timetable_template_periods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ttt_id` int(11) NOT NULL,
  `week_day` tinyint(4) NOT NULL COMMENT '1: Mon, 2: Tue, 3: Wed, 4: Thu, 5: Fri, 6: Sat, 7: Sun',
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `long_name` varchar(25) NOT NULL,
  `short_name` varchar(50) NOT NULL,
  `period_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: Normal period, 1: First Period 2: Last Period, 3: Short Break, 4: Long Break, 5: Second Period, 6: Period After Long Break',
  `period_seq` tinyint(4) NOT NULL COMMENT 'Used to denote the sequence of periods in a given day',
  `period_seq_excl_break` tinyint(4) NOT NULL COMMENT 'Used for staff timetable',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `class_section_id` (`ttt_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tracker_data`
--

DROP TABLE IF EXISTS `tracker_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tracker_data` (
  `id` int(11) NOT NULL,
  `schoolId` int(11) NOT NULL,
  `readerId` int(11) NOT NULL,
  `time` datetime NOT NULL,
  `cardId` int(11) NOT NULL,
  `temp` int(11) NOT NULL,
  `calories` int(11) NOT NULL,
  `steps` int(11) NOT NULL,
  `batteryVoltage` decimal(1,1) NOT NULL,
  `signalStrength` int(11) NOT NULL,
  `flag` tinyint(4) NOT NULL DEFAULT '1',
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tracker_student`
--

DROP TABLE IF EXISTS `tracker_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tracker_student` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `cardId` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tracking_master`
--

DROP TABLE IF EXISTS `tracking_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tracking_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `data` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `transport_fee_structure`
--

DROP TABLE IF EXISTS `transport_fee_structure`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transport_fee_structure` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stop_id` int(11) NOT NULL,
  `pickup_mode` varchar(100) NOT NULL,
  `amount` decimal(15,0) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gprs` decimal(10,0) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `transport_stops`
--

DROP TABLE IF EXISTS `transport_stops`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transport_stops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(150) NOT NULL DEFAULT 'Select Stop',
  `kilometer` float NOT NULL,
  `time` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_login_history`
--

DROP TABLE IF EXISTS `user_login_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_login_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `action` varchar(25) NOT NULL COMMENT 'Logout/login/etc',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_prov_login_att`
--

DROP TABLE IF EXISTS `user_prov_login_att`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_prov_login_att` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL,
  `attempts` int(11) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `communication_type` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `username` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `salt` varchar(255) DEFAULT NULL,
  `email` varchar(254) NOT NULL,
  `activation_code` varchar(40) DEFAULT NULL,
  `forgotten_password_code` varchar(40) DEFAULT NULL,
  `forgotten_password_time` int(11) unsigned DEFAULT NULL,
  `remember_code` varchar(40) DEFAULT NULL,
  `created_on` int(11) unsigned NOT NULL,
  `last_login` int(11) unsigned DEFAULT NULL,
  `active` tinyint(1) unsigned DEFAULT NULL,
  `phone_number` varchar(15) NOT NULL,
  `freeze_username` tinyint(1) NOT NULL DEFAULT '0',
  `donot_show` tinyint(1) NOT NULL DEFAULT '0',
  `loggedin_atleast_once` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username_UNIQUE` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `variant`
--

DROP TABLE IF EXISTS `variant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `variant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) NOT NULL,
  `item_name` varchar(100) NOT NULL,
  `item_type` varchar(100) NOT NULL,
  `var_option` varchar(20) NOT NULL COMMENT 'stop_id or class_id or sizes ',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `variant_qty`
--

DROP TABLE IF EXISTS `variant_qty`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `variant_qty` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `variant_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `vehicle_data`
--

DROP TABLE IF EXISTS `vehicle_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vehicle_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tax_paid_upto` date NOT NULL,
  `emission_expiry_date` date NOT NULL,
  `routeNo` int(11) NOT NULL,
  `permiteNo` varchar(100) NOT NULL,
  `fcNo` varchar(100) NOT NULL,
  `insuranceNo` varchar(100) NOT NULL,
  `rc_certificateNo` varchar(100) NOT NULL,
  `permite_expiry_date` date NOT NULL,
  `fcNo_date` date NOT NULL,
  `insuranceNo_date` date NOT NULL,
  `rc_certificateNo_date` date NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `vendor_address_info`
--

DROP TABLE IF EXISTS `vendor_address_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vendor_address_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `address_type` tinyint(4) NOT NULL,
  `address_line1` varchar(255) NOT NULL,
  `address_line2` varchar(255) NOT NULL,
  `area` varchar(100) NOT NULL,
  `district` varchar(100) NOT NULL,
  `state` varchar(100) NOT NULL,
  `country` varchar(100) NOT NULL,
  `pin_code` varchar(10) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `vendor_master`
--

DROP TABLE IF EXISTS `vendor_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vendor_master` (
  `vendor_id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_code` varchar(100) NOT NULL,
  `vendor_name` varchar(255) NOT NULL,
  `gst_no` varchar(255) NOT NULL,
  `vendor_email` varchar(100) NOT NULL,
  `vendor_website` varchar(255) NOT NULL,
  `contact_first_name` varchar(255) NOT NULL,
  `contact_last_name` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `contact_no` varchar(20) NOT NULL,
  `customer_service_no` varchar(20) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`vendor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `vendor_products`
--

DROP TABLE IF EXISTS `vendor_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vendor_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `product_variant_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `visitor_info`
--

DROP TABLE IF EXISTS `visitor_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `visitor_info` (
  `id` int(15) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `email` varchar(50) NOT NULL,
  `mobile` varchar(50) NOT NULL,
  `coming_from` varchar(50) NOT NULL,
  `check_in` timestamp NULL DEFAULT NULL,
  `duty_app` int(11) NOT NULL,
  `reason` varchar(150) NOT NULL,
  `check_out` timestamp NULL DEFAULT NULL,
  `visitor_img` varchar(255) NOT NULL,
  `tomeet_user_type` varchar(45) DEFAULT NULL,
  `pass_code` varchar(250) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;



--
-- Dumping data for table `academic_year`
--

LOCK TABLES `academic_year` WRITE;
/*!40000 ALTER TABLE `academic_year` DISABLE KEYS */;
INSERT INTO `academic_year` VALUES (1,'2001-02',0),(2,'2002-03',0),(3,'2003-04',0),(4,'2004-05',0),(5,'2005-06',0),(6,'2006-07',0),(7,'2007-08',0),(8,'2008-09',0),(9,'2009-10',0),(10,'2010-11',0),(11,'2011-12',0),(12,'2012-13',0),(13,'2013-14',0),(14,'2014-15',0),(15,'2015-16',0),(16,'2016-17',0),(17,'2017-18',0),(18,'2018-19',1),(19,'2019-20',0),(20,'2020-21',0),(21,'2021-22',0),(22,'2022-23',0),(23,'2023-24',0);
/*!40000 ALTER TABLE `academic_year` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `config`
--

LOCK TABLES `config` WRITE;
/*!40000 ALTER TABLE `config` DISABLE KEYS */;
INSERT INTO `config` VALUES (1,'modules','[\"COMMUNICATION\",\"FEESV2\",\"PARENTS_LOGIN\",\"PERMISSIONS\",\"SCHOOL_ADMIN\",\"SCHOOL_CALENDAR\",\"SMS\",\"STAFF_LOGIN\",\"STAFF_MASTER\",\"STAFF_OBSERVATION\",\"STUDENT_MASTER\",\"STUDENT_OBSERVATION\"]','2019-03-04 05:45:44','multiple'),(2,'parent_modules','[\"FEESV2\",\"SCHOOL_CALENDAR\",\"SMS\"]','2019-03-04 18:39:07','multiple'),(3,'school_image','assets/img/temp_school_bg.jpg','2019-01-23 10:01:44','string'),(4,'school_logo','assets/img/nps_agara_logo.png','2019-03-02 10:22:18','string'),(5,'company_logo','assets/img/nps_agara_logo.png','2019-03-02 12:50:37','string'),(6,'company_name','NextElement','2019-01-25 13:26:16','string'),(7,'login_background','assets/img/nps_agara_building.jpg','2019-03-02 10:27:46','string'),(8,'school_name','National Public School Agara','2019-03-02 10:10:34','string'),(9,'school_name_line2','Bangalore','2019-01-23 13:24:47','string'),(10,'school_short_name','npsaga','2019-03-02 12:44:09','string'),(11,'classType','[{\"name\":\"Junior School (Nursery - IV)\", \"value\":\"1\"}, {\"name\":\"High School (V TO IX)\", \"value\":\"2\"}]','2019-03-02 10:49:36','json'),(12,'admission_type','[{\"name\":\"Re-admission\", \"value\":\"1\"}, {\"name\":\"New Admission\", \"value\":\"2\"}]','2019-01-24 12:25:45','array'),(13,'board','[ {\"name\":\"CBSE\", \"value\":\"2\"}]','2019-03-02 13:36:34','array'),(14,'admission_status','[{\"name\":\"Pending\", \"value\":\"1\"}, {\"name\":\"Approved\", \"value\":\"2\"}, {\"name\":\"Rejected\", \"value\":\"3\"}, {\"name\":\"Alumni\", \"value\":\"4\"}]','2019-01-24 12:37:27','array'),(15,'medium','[{\"name\":\"English\", \"value\":\"1\"}]','2019-01-24 12:37:48','array'),(16,'student_record_selected_lang','0','2019-01-28 06:09:12','boolean'),(17,'student_record_height_weight','1','2019-01-28 06:10:24','boolean'),(18,'staff_status','[{\"name\":\"Pending\", \"value\":\"1\"}, {\"name\":\"Approved\", \"value\":\"2\"}, {\"name\":\"Rejected\", \"value\":\"3\"}, {\"name\":\"Resigned\", \"value\":\"4\"}, {\"name\":\"Retired\", \"value\":\"5\"}]','2019-01-25 16:55:59','array'),(19,'rte','[{\"name\":\"RTE\", \"value\":\"1\"}, {\"name\":\"Non-RTE\", \"value\":\"2\"}]','2019-01-25 16:59:01','array'),(20,'category','[{\"name\":\"General\", \"value\":\"1\"}, {\"name\":\"SC/ST\", \"value\":\"2\"}, {\"name\":\"CATEGORY IIA\", \"value\":\"3\"}, {\"name\":\"CATEGORY IIB\", \"value\":\"4\"}, {\"name\":\"CATEGORY IIIA\", \"value\":\"5\"}, {\"name\":\"OBC\", \"value\":\"6\"}]','2019-01-25 17:00:32','array'),(21,'boarding','[{\"name\":\"Day School\", \"value\":\"1\"}]','2019-01-25 17:03:43','array'),(22,'staff_profile_enableStaffProfileEdit','0','2019-01-25 17:26:58','boolean'),(23,'staff_profile_enableQualificationEdit','0','2019-01-28 06:09:16','boolean'),(24,'circular_categories','[\"Competition\",\"Co-Scholastic\",\"Co-curricular\",\"Scholastic\",\"Time-Table\",\"Board-Circular\",\"Fee\",\"Student-Info\"]','2019-01-25 18:08:07','string'),(25,'circular_enable_email','0','2019-01-25 18:05:04','string'),(26,'circular_enable_sms','1','2019-01-25 18:05:08','string'),(27,'parent_profile_display_student','{\"display\":\"1\", \"fields\":[\"admission_no\",\"dob\",\"gender\",\"roll_no\"]}','2019-01-26 08:23:51','json'),(28,'parent_profile_display_father','{\"display\":\"1\", \"fields\":[\"name_photo\",\"email\",\"contact_no\",\"occupation\"], \"address\":\"1\"}','2019-01-26 09:29:59','json'),(29,'parent_profile_display_mother','{\"display\":\"1\", \"fields\":[\"name_photo\",\"email\",\"contact_no\",\"occupation\"],\"address\":\"1\"}','2019-01-26 08:23:57','json'),(30,'school_name_line1','Rajajinagar','2019-01-28 05:56:23','string'),(31,'latecomer_attendance','1','2019-01-28 06:38:14','boolean'),(32,'show_absent_students_in_latecomer','1','2019-01-28 08:36:36','boolean'),(33,'show_all_students_in_latecomer','0','2019-01-28 08:52:39','boolean'),(34,'school_header','assets/img/npsrnr_school_header.png','2019-01-28 10:50:47','image'),(35,'favicon','assets/img/npsrnr_favicon.png','2019-01-28 10:55:02','image'),(36,'competition_attendance','0','2019-01-28 10:56:15','boolean'),(37,'academic_year_id','19','2019-03-02 10:50:09','string'),(38,'promotion_academic_year_id','20','2019-03-02 10:50:16','string'),(58,'admission_number','{\"manual\":\"TRUE\", \"admission_generation_algo\":\"NEXTELEMENT\", \"infix\":\"NULL\", \"digit_count\":\"5\", \"index_offset\":\"TRUE\"}','2019-03-02 12:22:43','json'),(59,'student_address_types','[{\"name\":\"Home Address\", \"value\":\"0\"}]','2019-03-02 12:27:50','array'),(60,'add_class_admitted_to','0','2019-03-02 13:03:16','boolean'),(61,'email_settings','{\"from_email\":\"<EMAIL>\",\"from_name\":\"National Public School Agara\"}','2019-03-04 17:01:14','json'),(62,'user_provisioning_challenge_fields','{\"fields\":[{\"name\": \"dob\",\"value\": \"As per school records (in DD-MM-YYYY).\"}]}','2019-03-04 17:04:30','json'),(63,'user_provisioning_process','validate_and_activate','2019-03-04 17:04:49','string'),(64,'user_provisioning_school_code','npsaga','2019-03-04 17:05:34','string'),(65,'user_provisioning_link_message_body','Welcome to the all-new refreshed school management application.\n','2019-03-04 17:06:22','string'),(66,'user_provisioning_link_message_footer','\nThanks and Regards,\n-NPS Agara','2019-03-04 17:06:40','string'),(67,'smsintergration','{\"url\":\"alerts.valueleaf.com/api/v4/index.php\", \"api_key\":\"Ae6b5684768b2741508f447a71545290a\", \"sender\":\"NPSAGA\"}','2019-03-05 06:47:32','json'),(68,'sms_credit_length','{\"unicode_single\":\"70\", \"unicode_multi\":\"60\", \"non_unicode_single\":\"160\", \"non_unicode_multi\":\"150\"}','2019-03-10 11:06:29','json');
/*!40000 ALTER TABLE `config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `privileges`
--

LOCK TABLES `privileges` WRITE;
/*!40000 ALTER TABLE `privileges` DISABLE KEYS */;
INSERT INTO `privileges` VALUES (1,'STUDENT','',1,1),(2,'STUDENT_MASSUPDATE','',1,1),(3,'STUDENT_OBSERVATION','',1,1),(4,'STUDENT_PROFILE','',1,1),(5,'STUDENT_PROMOTION','',1,1),(6,'STAFF','',1,1),(7,'STAFF_OBSERVATION','',1,1),(8,'PERMISSIONS','',1,1),(9,'SMS','',1,1),(10,'COMMUNICATION','',1,1),(11,'FEESV2','',1,1),(12,'TIMETABLE','',1,1),(13,'SUBSTITUTION','',1,1);
/*!40000 ALTER TABLE `privileges` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `privileges_sub`
--

LOCK TABLES `privileges_sub` WRITE;
/*!40000 ALTER TABLE `privileges_sub` DISABLE KEYS */;
INSERT INTO `privileges_sub` VALUES (1,'ADDRESS_CRUD','',1,1,0),(2,'HEALTH_CRUD','',1,1,0),(3,'BASIC_VIEW','',1,1,0),(4,'DETAIL_VIEW','',1,1,0),(5,'MODULE','',1,1,0),(6,'COMMUNICATION_CRUD','',1,1,0),(7,'FULL_INFO_CRUD','',1,1,0),(8,'NEW_STUDENT_ADD','',1,1,0),(9,'MOTHER_TONGUE_CRUD','',1,1,0),(10,'MOTHER_TONGUE_CRUD_IF_CT','',1,1,0),(11,'HEALTH_CRUD_IF_CT','',1,1,0),(12,'ADDRESS_CRUD_IF_CT','',1,1,0),(13,'COMMUNICATION_CRUD_IF_CT','',1,1,0),(14,'DETAIL_VIEW_IF_CT','',1,1,0),(15,'VIEW_PRINT_CERTIFICATES','',1,1,0),(16,'ADD_SIBLINGS','',1,1,0),(17,'VIEW_SMS_REPORT','',1,1,0),(18,'VIEW_REPORT','',1,1,0),(19,'MODULE','',2,1,0),(20,'VIEW','',3,1,0),(21,'CREATE','',3,1,0),(22,'VIEW_SUMMARY','',3,1,0),(23,'VIEW_ALL','',3,1,0),(24,'MODULE','',3,1,0),(25,'VIEW','',4,1,0),(26,'VIEW_ACTIVITY_DETAILS','',4,1,0),(27,'VIEW_EMERGENCY_INFO','',4,1,0),(28,'VIEW_FEES_DETAILS','',4,1,0),(29,'VIEW_HEALTH_DETAILS','',4,1,0),(30,'VIEW_PERSONAL_DETAILS','',4,1,0),(31,'VIEW_SCHOOL_DETAILS','',4,1,0),(32,'VIEW_TRANSPORT_DETAILS','',4,1,0),(33,'MODULE','',5,1,0),(34,'ADD_EDIT_DELETE','',6,1,0),(35,'VIEW_DETAILS','',6,1,0),(36,'VIEW','',6,1,0),(37,'MODULE','',6,1,0),(38,'VIEW_SMS_REPORT','',6,1,0),(39,'VIEW_REPORT','',6,1,0),(40,'CREATE','',7,1,0),(41,'VIEW_SUMMARY','',7,1,0),(42,'MODULE','',7,1,0),(43,'VIEW_ALL','',7,1,0),(44,'VIEW','',7,1,0),(45,'ASSIGN','',8,1,0),(46,'CREATE_PARENTS_LOGIN','',8,1,0),(47,'CREATE_STAFF_LOGIN','',8,1,0),(48,'SEND_SMS','',9,1,0),(49,'SMS_REPORT','',9,1,0),(50,'SMS_DELIVERY_REPORT','',9,1,0),(51,'MODULE','',10,1,0),(52,'MODULE','',11,1,0),(53,'VIEW_BIRTHDAYS','',1,1,0),(54,'SHOW_STAFF_SMS','',9,1,0),(55,'COLLECT_FEES','',11,1,0),(56,'VIEW_DAILY_TX_REPORT','',11,1,0),(57,'VIEW_BALANCE_REPORT','',11,1,0),(58,'VIEW_ONLINE_TX_REPORT','',11,1,0),(59,'ASSIGN_INVOICE','',11,1,0),(60,'VIEW_CONCESSIONS','',11,1,0),(61,'MY_TIMETABLE','',12,1,0),(62,'MASTER','',12,1,0),(63,'MODULE','',12,1,0),(64,'STAFF_TIMETABLE_VIEW','',12,1,0),(65,'SECTION_TIMETABLE_VIEW','',12,1,0),(66,'VIEW_ROOM_TT','',12,1,0),(67,'ADDITIONAL_PERIOD_REQUEST','',13,1,0),(68,'MASTER','',13,1,0),(69,'MODULE','',13,1,0),(70,'OUTPUT','',13,1,0);
/*!40000 ALTER TABLE `privileges_sub` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Add Super Admin record
--
INSERT INTO `users` (`id`, `ip_address`, `username`, `password`, `salt`, `email`, `activation_code`, `forgotten_password_code`, `forgotten_password_time`, `remember_code`, `created_on`, `last_login`, `active`, `phone_number`, `freeze_username`, `donot_show`) VALUES (NULL, '127.0.0.1', 'admin', '$2y$08$up/8GwPRRGeVJzZzGnTpmOm57t6vFm9iiSjY3GTMWoDeF/Zlcfv1e', '', '<EMAIL>', '', NULL, NULL, NULL, '1268889823', '1551510464', '1', '', '0', '0');
INSERT INTO `avatar` (`id`, `user_id`, `avatar_type`, `stakeholder_id`, `friendly_name`, `created_on`, `modified_on`, `last_modified_by`, `old_user_id`) VALUES (NULL, '1', '3', '0', 'Super Admin', '2018-03-09 00:52:44', '2018-03-09 00:52:44', '0', NULL);

/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2019-03-11 19:46:32

