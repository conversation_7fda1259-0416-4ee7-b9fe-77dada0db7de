<?php

  defined('BASEPATH') OR exit('No direct script access allowed');

  class Dashboard extends CI_Controller {

    private $year_id;

    public function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isAuthorized('ONLINE_CLASS.MODULE')) {
        redirect('dashboard', 'refresh');
      }
      $this->load->library('filemanager');
      $this->load->model('Helpdesk_model', 'helpdesk');
      $this->yearId = $this->acad_year->getAcadYearId();
    }


    public function index(){
      $data['is_admin'] = $this->authorization->isAuthorized('ONLINE_CLASS.ADMIN');

      $site_url = site_url();
    $data['tiles'] = array(
        [
          'title' => 'Classes',
          'sub_title' => 'Class schedules',
          'icon' => 'svg_icons/class.svg',
          'url' => $site_url.'online_class/schedule_controller',
          'permission' => 1
        ],
        [
          'title' => 'Licences',
          'sub_title' => 'Add/Edit Licences',
          'icon' => 'svg_icons/homework.svg',
          'url' => $site_url.'online_class/schedule_controller/licences',
          'permission' => $data['is_admin']
        ],
        [
          'title' => 'View Attendance',
          'sub_title' => 'View Attendance',
          'icon' => 'svg_icons/view.svg',
          'url' => $site_url.'online_class/online_attendance_controller/dashboard',
          'permission' => 1
        ],
        [
          'title' => 'FAQ',
          'sub_title' => 'FAQs',
          'icon' => 'svg_icons/faq.svg',
          'url' => '#',
          'click_function' => 'troubleshoot()',
          'permission' => 1
        ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);
      
    //$data['chatData'] = $this->helpdesk->getChatContent('Online Class');
      // $this->load->helper('chatdata_helper');
      // $data['chatData'] = getChatData('Online Class');

      //echo "<pre>"; print_r($data); die();
      // $data['path_prefix'] = $this->filemanager->getFilePath('');
      $data['type'] = 'Online Class';
      $data['back_url'] = site_url('online_class/dashboard');  
      $data['super_admin'] = $this->authorization->isSuperAdmin();
      $data['main_content']    = 'online_class/dashboard.php';
      $this->load->view('inc/template', $data);  
    }
  }

?>