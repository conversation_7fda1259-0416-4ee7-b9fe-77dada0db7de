<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd"><html><head>
<link rel="stylesheet" media="screen, projection" href="css/linkdisplay.css">
<link rel="stylesheet" media="print" href="css/linkprint.css">
<link rel="stylesheet" media="all" href="css/linkall.css">
<link rel="stylesheet" href="css/linkdefault.css">
<style>
@page { margin:1cm; color:#0000ff; font-family:sans-serif;} 

@media print {
  p.media {background-color:#eeeeee;}
}
@media screen {
  p.media {background-color:#c0c0ff;}
}
@media projection {
  p.media {background-color:#ffffc0;}
}

@import url(css/importall.css);
@import url(css/importprint.css) print;
@import url(css/importdisplay.css) projection, screen;

@import url(css/importsub.css);

@import url(/absimagetest/importabs.css);

body { background-color:#eeeeee; margin:0pt; padding:1cm; border:0.5pt solid red; }
</style>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
</head>
<body>
<h1>css @media media types</h1>
<p class="media">
Depending on dompdf_config.inc.php setting DOMPDF_DEFAULT_MEDIA_TYPE here the
background color appeares different:
</p>
<ul>
<li>print: light gray</li>
<li>screen: light blue</li>
<li>projection: light yellow</li>
</ul>

<h1>css @import media types</h1>
<p class="import">
Depending on dompdf_config.inc.php setting DOMPDF_DEFAULT_MEDIA_TYPE here the
background color appeares different:
</p>
<ul>
<li>print: light gray</li>
<li>screen or projection: light blue</li>
<li class="import">all: this line yellow</li>
</ul>

<p class="importsub">yellow by import css from subfolder</p>

<p class="importabs">yellow by import css from absolute local folder.
Note: Only works if www\test\images/importabs.css was copied to /absimagetest/importabs.css
</p>


<h1>css link media types</h1>
<p class="link">
Depending on dompdf_config.inc.php setting DOMPDF_DEFAULT_MEDIA_TYPE here the
background color appeares different:
</p>
<ul>
<li>print: light gray</li>
<li>screen or projection: light blue</li>
<li class="link">all: this line yellow background with red color</li>
</ul>


<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
<p>x</p>
</html>
