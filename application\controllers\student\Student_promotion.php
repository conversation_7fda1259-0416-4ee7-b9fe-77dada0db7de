<?php

/**
 * Description of Student_promotion
 *
 * <AUTHOR>
 */
class Student_promotion extends CI_Controller {
  private $yearId;
  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_MASTER')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT_PROMOTION.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->yearId = $this->acad_year->getAcadYearId();
    $this->load->model('student/Student_Model', 'student');
  }

  function index($classId=0) {
    if(isset($_POST['class_id'])) {
      $classId = $_POST['class_id'];
    }

    $data['class_curr_year_id'] = $this->yearId;
    $data['canPromote'] = $this->__canPromoteStudent();
    $data['classes'] = $this->student->getClassNames();

    $data['classSelected'] = $classId;
    $data['className'] = '';
    $data['promotionClass'] = 0;
    $data['promotionClassId'] = 0;
    foreach ($data['classes'] as $key => $value) {
      if($value->classId == $classId) {
        $data['className'] = $value->className;
        $data['promotionClassId'] = $value->promotion_class;
        if($value->promotion_class != NULL) {
          $data['promotionClass'] = $this->student->getClassDetail($value->promotion_class);
        }
        break;
      }
    }
    // echo "<pre>"; print_r($data);die();
    $data['students'] = array();
    $data['promotion_academic_year_id'] = $this->acad_year->getPromotionAcadYearId();
    $data['promotion_academic_year'] = $this->acad_year->getPromotionAcadYear();
    $data['current_academic_year'] = $this->acad_year->getAcadYear();
    if($classId!=0) {
      $data['class_curr_year_id'] = $this->student->getCurrYearIdFromClsId($classId);
      $data['students'] = $this->student->getStdDataByClsId($classId);
      $nextYearStds = $this->student->getPromotedStdDataByAcadId($classId, $data['promotion_academic_year_id']);
      $stds = array();
      foreach ($nextYearStds as $value) {
        $stds[$value->id] = $value;
      }
      foreach ($data['students'] as $key => $value) {
        if(array_key_exists($value->id, $stds)) {
          $value->promotionStdYearId = $stds[$value->id]->stdYearId;
          $value->promotion_pStatus = $stds[$value->id]->pStatus;
        } else {
          $value->promotionStdYearId = null;
          $value->promotion_pStatus = '-';
        }
      }
    }
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_promotion/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student/student_promotion/index_mobile';
    }else{
      $data['main_content'] = 'student/student_promotion/index';   	
    }
    $this->load->view('inc/template', $data);
  }

  private function __canPromoteStudent() {
    return $data['canPromote'] = ($this->settings->getSetting('academic_year_id') == $this->yearId)?'1':'0';
  }

  public function promoteTemporarily() {
    $classId = $this->input->post('currentClass');
    $status = $this->student->promoteStudentsTemp();
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Students promoted succcessfully!');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/Student_promotion/index/'.$classId);
  }

  public function promoteCompletely() {
    $classId = $this->input->post('currentClass');
    $status = $this->student->promoteStudent();
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Students promoted succcessfully!');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/Student_promotion/index/'.$classId);
  }

  public function change_promotion_status(){
   echo $this->student->change_promotion_status($_POST['acad_year']); 
  }
}