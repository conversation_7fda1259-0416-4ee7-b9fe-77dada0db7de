<?php

require APPPATH . 'libraries/REST_Controller.php';

class ReportCard extends REST_Controller
{
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
            $data = ['error' => 'User not logged in'];
            $this->response($data, REST_Controller::HTTP_UNAUTHORIZED);
			$this->output->_display();
			exit;
		}
		$this->load->model('parentv2/ReportCard_model');
		
	}

	public function report_card_get() {
        $student_id = $this->get('student_id');

		$data['marksCards'] = $this->ReportCard_model->getMarksCards($student_id);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}
    
    public function grading_system_get()	{
        $tempId = $this->get('template_id');

		$template = $this->ReportCard_model->assInTemplate($tempId);
		// $data['template'] = $template;
		$gIds = explode(",", $template->grading_systems);
		$data['gradingSystems'] = $this->ReportCard_model->getGradingSystem($gIds);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function mark_read_post() {

		$card_id = $this->post('card_id');
		$avatar_id = $this->post('avatar_id');

		$data['status'] = $this->ReportCard_model->acknowledged($card_id, $avatar_id);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

}
?>