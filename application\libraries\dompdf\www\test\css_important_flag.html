<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
<style>
ul { line-height:160% }
a {
  border-bottom:dashed 1pt red !important;
  text-decoration:none !important;
}
.monospace { font-family:monospace !important; }

</style>
</head>
<body>

<h1>Handling of "!important" property flag</h1>
<p>
Normally later css style properties defined later are overriding earlier ones.<br>
Except if they are marked with the flag "!important".<br>
Those can only be overridden by style properties which are also marked "!important".
</p>

<p>There are two classes of property overriding</p>
<ul>
<li>inherit (nested html tags)</li>
<li>merging (more css properties to the same html tag)</li>
</ul>
<p>This is handled similarly for all styles, so we check only examples here</p>

<p>ul { line-height:160% }<p>

<h2>merge a { border-bottom:dashed 1pt red !important; text-decoration:none !important; }</h2>

<p>dummy links, text decoration/border bottom:</p>

<ul>
<li><a href="">(default)</a></li>
<li><a href="" style="border-bottom-style:none;">border-bottom-style:none; (ignore)</a></li>
<li><a href="" style="border:1pt solid blue;">border:1pt solid blue; (ignore)</a></li>
<li><a href="" style="border-bottom-style:none!important;">border-bottom-style:none!important; (override)</a></li>
<li><a href="" style="border:1pt solid blue!important;">border:1pt solid blue!important; (override)</a></li>
</ul>

<h2>Inherit .monospace { font-family:monospace !important; }</h2>
<p>font family selection:</p>
<ul class="monospace">
<li>(default)</li>
<li class="font-family:sans-serif;">font-family:sans-serif; (ignored)</li>
<li style="font-family:sans-serif!important;">font-family:sans-serif!important; (override)</li>
</ul>

</body>
</html>
