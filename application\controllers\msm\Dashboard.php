<?php

class Dashboard extends CI_Controller {
  private $avatar_type;

  public $columnList = [    
    [
      'displayName'=>'Date of Birth',
      'columnNameWithTable' => 'DATE_FORMAT(sm.dob, "%d-%m-%Y")',
      'columnName'=>'dob',
      'varName'=>'sDOB',
      'table'=>'staff_master',
      'index'=>'2',
      'displayType'=>'text',
      'dataType'=>'date'
    ],
    [
      'displayName'=>'Designation',
      'columnNameWithTable'=>'sm.designation',
      'columnName'=>'designation',
      'varName'=>'designation',
      'table'=>'staff_master',
      'index'=>'3',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Department',
      'columnNameWithTable'=>'sm.department',
      'columnName'=>'department',
      'varName'=>'department',
      'table'=>'staff_master',
      'index'=>'4',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Father Name',
      'columnNameWithTable'=>'concat(ifnull(sm.father_first_name,""), " ", ifnull(sm.father_last_name,""))',
      'columnName'=>'father_name',
      'varName'=>'sFatherName',
      'table'=>'staff_master',
      'index'=>'5',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Mother Name',
      'columnNameWithTable'=>'concat(ifnull(sm.mother_first_name,""), " ", ifnull(sm.mother_last_name,""))',
      'columnName'=>'mother_name',
      'varName'=>'smotherName',
      'table'=>'staff_master',
      'index'=>'6',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Martial Status',
      'columnNameWithTable'=>'sm.marital_status',
      'columnName'=>'marital_status',
      'varName'=>'marital_status',
      'table'=>'staff_master',
      'index'=>'7',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
     [
      'displayName'=>'Spouse Name',
      'columnNameWithTable'=>'sm.spouse_name',
      'columnName'=>'spouse_name',
      'varName'=>'spouseName',
      'table'=>'staff_master',
      'index'=>'8',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Nationality',
      'columnNameWithTable'=>'sm.nationality',
      'columnName'=>'nationality',
      'varName'=>'nationality',
      'table'=>'staff_master',
      'index'=>'9',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Gender',
      'columnNameWithTable'=>'sm.gender',
      'columnName'=>'gender',
      'varName'=>'gender',
      'table'=>'staff_master',
      'index'=>'10',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

     [
      'displayName'=>'Aadhar Number',
      'columnNameWithTable'=>'sm.aadhar_number',
      'columnName'=>'aadhar_number',
      'varName'=>'AdNumber',
      'table'=>'staff_master',
      'index'=>'11',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

     [
      'displayName'=>'Qualification',
      'columnNameWithTable'=>'sm.qualification',
      'columnName'=>'qualification',
      'varName'=>'qualification',
      'table'=>'staff_master',
      'index'=>'12',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

     [
      'displayName'=>'Subject Specialization',
      'columnNameWithTable'=>'sm.subject_specialization',
      'columnName'=>'subject_specialization',
      'varName'=>'subject_specialization',
      'table'=>'staff_master',
      'index'=>'13',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Total Experience',
      'columnNameWithTable'=>'sm.total_experience',
      'columnName'=>'total_experience',
      'varName'=>'total_experience',
      'table'=>'staff_master',
      'index'=>'14',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Total Education Experience',
      'columnNameWithTable'=>'sm.total_education_experience',
      'columnName'=>'total_education_experience',
      'varName'=>'total_eductation_experience',
      'table'=>'staff_master',
      'index'=>'15',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
   
    [
      'displayName'=>'Alternative Number',
      'columnNameWithTable'=>'sm.alternative_number',
      'columnName'=>'alternative_number',
      'varName'=>'alternative_number',
      'table'=>'staff_master',
      'index'=>'16',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Spouse Contact Number',
      'columnNameWithTable'=>'sm.spouse_contact_no',
      'columnName'=>'spouse_contact_no',
      'varName'=>'spouse_contact_number',
      'table'=>'staff_master',
      'index'=>'17',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Date of Joining',
      'columnNameWithTable' => 'DATE_FORMAT(sm.joining_date, "%d-%m-%Y")',
      'columnName'=>'joining_date',
      'varName'=>'staffDOJ',
      'table'=>'staff_master',
      'index'=>'18',
      'displayType'=>'text',
      'dataType'=>'date'
    ],

     [
      'displayName'=>'User Name',
      'columnNameWithTable' => 'u.username',
      'columnName'=>'username',
      'varName'=>'uName',
      'table'=>'users',
      'index'=>'19',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Email',
      'columnNameWithTable' => 'u.email',
      'columnName'=>'email',
      'varName'=>'email',
      'table'=>'users',
      'index'=>'20',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName' => ' Personal Email',
      'columnNameWithTable' => 'sm.personal_mail_id',
      'columnName' => 'personal_mail_id',
      'varName' => 'personal_mail_id',
      'table' => 'staff_master',
      'index' => '70',
      'displayType' => 'text',
      'dataType' => 'string'
    ],
     [
      'displayName'=>'Profile Confirmed',
      'columnNameWithTable' => 'profile_confirmed',
      'columnName'=>'profile_confirmed',
      'varName'=>'profile_confirmed',
      'table'=>'staff_master',
      'index'=>'21',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'ESI Number',
      'columnNameWithTable' => 'npm.esi_number',
      'columnName'=>'esi_number',
      'varName'=>'esi_number',
      'table'=>'new_payroll_master npm',
      'index'=>'22',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'UAN Number',
      'columnNameWithTable' => 'npm.uan_number',
      'columnName'=>'uan_number',
      'varName'=>'uan_number',
      'table'=>'new_payroll_master npm',
      'index'=>'23',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
     [
      'displayName'=>'PF Number',
      'columnNameWithTable' => 'npm.pf_number',
      'columnName'=>'pf_number',
      'varName'=>'pf_number',
      'table'=>'new_payroll_master npm',
      'index'=>'24',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'PAN Details',
      'columnNameWithTable' => 'npm.pan_number',
      'columnName'=>'pan_number',
      'varName'=>'pan_number',
      'table'=>'new_payroll_master npm',
      'index'=>'29',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Blood Group',
      'columnNameWithTable'=>'sm.blood_group',
      'columnName'=>'blood_group',
      'varName'=>'blood_group',
      'table'=>'staff_master',
      'index'=>'30',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Staff Type',
      'columnNameWithTable' => 'staff_type',
      'columnName'=>'staff_type',
      'varName'=>'staff_type',
      'table'=>'staff_master',
      'index'=>'31',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    //new
    [
      'displayName'=>'Emergency Info',
      'columnNameWithTable' => 'emergency_info',
      'columnName'=>'emergency_info',
      'varName'=>'emergency_info',
      'table'=>'staff_master',
      'index'=>'32',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Math high grade',
      'columnNameWithTable' => 'math_high_grade',
      'columnName'=>'math_high_grade',
      'varName'=>'math_high_grade',
      'table'=>'staff_master',
      'index'=>'33',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'English high grade',
      'columnNameWithTable' => 'english_high_grade',
      'columnName'=>'english_high_grade',
      'varName'=>'english_high_grade',
      'table'=>'staff_master',
      'index'=>'34',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Social high grade',
      'columnNameWithTable' => 'social_high_grade',
      'columnName'=>'social_high_grade',
      'varName'=>'social_high_grade',
      'table'=>'staff_master',
      'index'=>'35',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Trained to teach',
      'columnNameWithTable' => 'trained_to_teach',
      'columnName'=>'trained_to_teach',
      'varName'=>'trained_to_teach',
      'table'=>'staff_master',
      'index'=>'36',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
     [
      'displayName'=>'Appointed subject',
      'columnNameWithTable' => 'appointed_subject',
      'columnName'=>'appointed_subject',
      'varName'=>'appointed_subject',
      'table'=>'staff_master',
      'index'=>'37',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
     [
      'displayName'=>'Classes taught',
      'columnNameWithTable' => 'classes_taught',
      'columnName'=>'classes_taught',
      'varName'=>'classes_taught',
      'table'=>'staff_master',
      'index'=>'38',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
     [
      'displayName'=>'Main sub taught',
      'columnNameWithTable' => 'main_sub_taught',
      'columnName'=>'main_sub_taught',
      'varName'=>'main_sub_taught',
      'table'=>'staff_master',
      'index'=>'39',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Add sub taught',
      'columnNameWithTable' => 'add_sub_taught',
      'columnName'=>'add_sub_taught',
      'varName'=>'add_sub_taught',
      'table'=>'staff_master',
      'index'=>'40',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Boarding',
      'columnNameWithTable' => 'boarding',
      'columnName'=>'boarding',
      'varName'=>'boarding',
      'table'=>'staff_master',
      'index'=>'41',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Last working day',
      'columnNameWithTable' => 'DATE_FORMAT(sm.last_working_day, "%d-%m-%Y")',
      'columnName'=>'last_working_day',
      'varName'=>'last_working_day',
      'table'=>'staff_master',
      'index'=>'43',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Voter id',
      'columnNameWithTable' => 'voter_id',
      'columnName'=>'voter_id',
      'varName'=>'voter_id',
      'table'=>'staff_master',
      'index'=>'44',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Height',
      'columnNameWithTable' => 'height',
      'columnName'=>'height',
      'varName'=>'height',
      'table'=>'staff_master',
      'index'=>'45',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Weight',
      'columnNameWithTable' => 'weight',
      'columnName'=>'weight',
      'varName'=>'weight',
      'table'=>'staff_master',
      'index'=>'46',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Allergies',
      'columnNameWithTable' => 'allergies',
      'columnName'=>'allergies',
      'varName'=>'allergies',
      'table'=>'staff_master',
      'index'=>'47',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Medical issues',
      'columnNameWithTable' => 'medical_issues',
      'columnName'=>'medical_issues',
      'varName'=>'medical_issues',
      'table'=>'staff_master',
      'index'=>'48',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Identification mark',
      'columnNameWithTable' => 'identification_mark',
      'columnName'=>'identification_mark',
      'varName'=>'identification_mark',
      'table'=>'staff_master',
      'index'=>'49',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Person with disability',
      'columnNameWithTable' => 'person_with_disability',
      'columnName'=>'person_with_disability',
      'varName'=>'person_with_disability',
      'table'=>'staff_master',
      'index'=>'50',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Passport number',
      'columnNameWithTable' => 'passport_number',
      'columnName'=>'passport_number',
      'varName'=>'passport_number',
      'table'=>'staff_master',
      'index'=>'51',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Passport place of issue',
      'columnNameWithTable' => 'passport_place_of_issue',
      'columnName'=>'passport_place_of_issue',
      'varName'=>'passport_place_of_issue',
      'table'=>'staff_master',
      'index'=>'52',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Passport date of issue',
      'columnNameWithTable' => 'DATE_FORMAT(sm.passport_date_of_issue, "%d-%m-%Y")',
      'columnName'=>'passport_date_of_issue',
      'varName'=>'passport_date_of_issue',
      'table'=>'staff_master',
      'index'=>'53',
      'displayType'=>'text',
      'dataType'=>'date'
    ],
    [
      'displayName'=>'Passport expiry date',
      'columnNameWithTable' => 'DATE_FORMAT(sm.passport_expiry_date, "%d-%m-%Y")',
      'columnName'=>'passport_expiry_date',
      'varName'=>'passport_expiry_date',
      'table'=>'staff_master',
      'index'=>'61',
      'displayType'=>'text',
      'dataType'=>'date'
    ],
    [
      'displayName'=>'Visa details',
      'columnNameWithTable' => 'visa_details',
      'columnName'=>'visa_details',
      'varName'=>'visa_details',
      'table'=>'staff_master',
      'index'=>'57',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Religion',
      'columnNameWithTable' => 'religion',
      'columnName'=>'religion',
      'varName'=>'religion',
      'table'=>'staff_master',
      'index'=>'58',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Nature of appointment',
      'columnNameWithTable' => 'nature_of_appointment',
      'columnName'=>'nature_of_appointment',
      'varName'=>'nature_of_appointment',
      'table'=>'staff_master',
      'index'=>'59',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Has completed any bed',
      'columnNameWithTable' => 'has_completed_any_bed',
      'columnName'=>'has_completed_any_bed',
      'varName'=>'has_completed_any_bed',
      'table'=>'staff_master',
      'index'=>'60',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
  ];

  function __construct(){
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('MSM')) {
      redirect('dashboard');
    }
    $this->avatar_type = $this->authorization->getAvatarType();
    //if not staf or super admin
    if($this->avatar_type != 3 && $this->avatar_type != 4) {
        redirect('dashboard');
    }
		$this->load->model('msm/msm_model','msm_model');
  }
  public function index_v1() {
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());

    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm/tablet/dashboard_indus_tablet';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm/mobile/dashboard_indus';
    } else {
        $data['main_content']    = 'msm/desktop/dashboard_indus';
    }
    $this->load->view('msm/inc/template', $data);
  }

  public function index() {
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['academic_year'] = $this->settings->getSetting('academic_year');

    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v2/tablet/dashboard_indus';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v2/mobile/dashboard_indus';
    } else {
        $data['main_content']    = 'msm_v2/desktop/dashboard_indus';
    }
    $this->load->view('msm/inc/template', $data);
  }

  public function fee_collection() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/fee_collection_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/fee_collection_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/fee_collection';
    }
    $this->load->view('msm/inc/template', $data);
  }   

  public function fee_management_summary(){
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/fee_management_summary_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/fee_management_summary_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/fee_management_summary';
    }    
    $this->load->view('msm/inc/template', $data);
  }

  public function fee_management_summary_2(){
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['school_list'] = $this->msm_model->get_restricted_school_list();

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/fee_management_summary_2_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/fee_management_summary_2_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/fee_management_summary_2';
    }    
    $this->load->view('msm/inc/template', $data);
  }

  public function activity_tracker() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/activity_tracker_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/activity_tracker_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/activity_tracker';
    }
    $this->load->view('msm/inc/template', $data);
  }   

  public function student_analytics() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['school_list'] = $this->msm_model->get_restricted_school_list();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/student_analytics_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/student_analytics_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/student_analytics';
    }
    $this->load->view('msm/inc/template', $data);
  }   

  public function staff_analytics() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['school_list'] = $this->msm_model->get_restricted_school_list();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/staff_analytics_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/staff_analytics_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/staff_analytics';
    }
    $this->load->view('msm/inc/template', $data);
  }
  
  public function infirmary_analytics() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['school_list'] = $this->msm_model->get_restricted_school_list();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/infirmary_analytics_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/infirmary_analytics_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/infirmary_analytics';
    }
    $this->load->view('msm/inc/template', $data);
  }

  public function payroll_analytics() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['school_list'] = $this->msm_model->get_restricted_school_list();
		$this->load->model('msm/payroll_model','payroll_model');
    $data['schedule_list'] = $this->payroll_model->getSchedules();
    $data['ini_schedule_id'] = json_encode($this->payroll_model->get_ini_schedule_id());

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/desktop/payroll_analytics';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/desktop/payroll_analytics';
    } else {
      $data['main_content']    = 'msm/desktop/payroll_analytics';
    }
    $this->load->view('msm/inc/template', $data);
  } 
  
  public function student_attendance() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/student_attendance_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/student_attendance_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/student_attendance';
    }
    $this->load->view('msm/inc/template', $data);
  }

  public function staff_report() {
    $data['school_list'] = $this->msm_model->get_restricted_school_list();
    $this->columnList = array_merge($this->columnList,$this->__prepareAddressFields());

    $this->columnList = array_merge($this->columnList,$this->__prepareStaffCustomFields());

    $data['columnList_json'] = json_encode($this->columnList);
    $data['columnList'] = $this->columnList;
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm_v2/desktop/staff_report';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/staff_report_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/staff_report';
    }    
    $this->load->view('msm/inc/template', $data);
  }
  
  public function lead_management() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['school_list'] = $this->msm_model->get_restricted_school_list();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/lead_management_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/lead_management_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/lead_management';
    }    
    $this->load->view('msm/inc/template', $data);
  }

  public function parent_ticketing() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['school_list'] = $this->msm_model->get_restricted_school_list();

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/parent_ticketing_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/parent_ticketing_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/parent_ticketing';
    }     
    $this->load->view('msm/inc/template', $data);
  }

  public function internal_ticketing() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['school_list'] = $this->msm_model->get_restricted_school_list();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/internal_ticketing_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/internal_ticketing_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/internal_ticketing';
    }      
    $this->load->view('msm/inc/template', $data);
  }

  public function staff_attendance() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/staff_attendance_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/staff_attendance_mobile';
    } else {
      $data['main_content']    = 'msm/desktop/staff_attendance';
    }
    $this->load->view('msm/inc/template', $data);
  }   

  public function admissions() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['school_list'] = $this->msm_model->get_restricted_school_list();

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm/tablet/admissions_tablet';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm/mobile/admissions';
    } else {
      $data['main_content']    = 'msm/desktop/admissions';
    }
    $this->load->view('msm/inc/template', $data);
  } 

  public function individual_school_dashboard() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm_individual_dashboard/tablet/individual_school_dashboard';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm_individual_dashboard/mobile/individual_school_dashboard';
    } else {
      $data['main_content']    = 'msm_individual_dashboard/desktop/individual_school_dashboard';
    }
    $data['school_code'] = $this->input->get('school_code');
    $data['school_domain'] = $this->input->get('school_domain');
    $this->load->view('msm/inc/template', $data);
  }

  private function __call_api($data, $end_point) {
    // print_r(json_encode($data));die();
    $curl_request = [
        CURLOPT_URL => $end_point,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POST => 1,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_SSL_VERIFYPEER => 0,
        CURLOPT_HTTPHEADER => [
            "content-type: application/json"
        ]
    ];

    $curl = curl_init();
    curl_setopt_array($curl, $curl_request);
    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);
    return $response;
  }

  public function bridge() {
    if(empty($input)) {
      $data = $_POST;
    } else {
      $data = $input;
    }    

    // $end_point = "https://" . $data['school_code'] . ".localhost.in/oxygenv2/msm/api/" . $data['api'];
    $end_point = "https://" . $data['school_code'] . "." . $data['school_domain'] . ".in/msm/api/" . $data['api'];
    $data['response'] = $this->__call_api($data, $end_point);
    $data['status'] = 1;

    if(empty($input)) {
      echo json_encode($data);
    } else {
      return $data;
    }
  }

  public function bridge_dashboard() {
    $data = $_POST;

    // $end_point = "https://" . $data['school_code'] . ".localhost.in/oxygenv2/msm/api/" . $data['api'];
    $end_point = "https://" . $data['school_code'] . "." . $data['school_domain'] . ".in/msm/api/" . $data['api'];

    $response = $this->__call_api($data, $end_point);
    $response_data = json_decode($response);

    // echo '<pre>';print_r($response_data);
    $data['student_count_obj'] = $response_data->student_count_obj->statistic_value;
    $data['staff_count_obj'] = $response_data->staff_count_obj->statistic_value;
    $data['staff_attendance_obj'] = $response_data->staff_attendance_obj->statistic_value;
    $data['status'] = '1';

    if(empty($input)) {
      echo json_encode($data);
    } else {
      return $data;
    }
  }

  public function bridge_report() {
    $data = $_POST;
    // $end_point = "https://" . $data['school_code'] . ".localhost.in/oxygenv2/msm/api/" . $data['api'];
    $end_point = "https://" . $data['school_code'] . "." . $data['school_domain'] . ".in/msm/api/" . $data['api'];
    
    $data['response'] = $this->__call_api($data, $end_point);
    $data['status'] = '1';
    echo json_encode($data);
  }

  private function __prepareAddressFields() {
    $addresses = array('0' => 'Present Address', '1'=>'Permanent Address');
  
    $addArr = array();
    $index = count($this->columnList);
    foreach ($addresses as $addKey => $add) {
        $obj = array();
        $obj['displayName'] = $add;
        $obj['varName'] = $addKey .'_' . 'address';
        $obj['table'] = 'address_info';
        $obj['index'] = ++$index;
        $obj['displayType'] = 'text';
        $obj['dataType'] = 'string';
        $obj['addressType'] = $addKey;
        switch ($addKey) {
          case '0':
          $obj['addressOf'] = 'present_address';
            break;
          case '1':
          $obj['addressOf'] = 'permanent_address';
            break;
        }
        $addArr[] = $obj;    
    }
    return $addArr;
  }

  private function __prepareStaffCustomFields(){
    $custom_fields =$this->settings->getSetting('staff_custom_fields');
    $customFIelds = array();
    if($custom_fields){
      $indexNumber = 200;
      foreach ($custom_fields as $displayName => $columnName) {
        $obj = array();
        $obj['displayName'] = $displayName;
        $obj['columnNameWithTable'] = $columnName;
        $obj['varName'] = $columnName;
        $obj['columnName'] = $columnName;
        $obj['table'] = 'staff_master';
        $obj['index'] = $indexNumber++;
        $obj['displayType'] = 'text';
        $obj['dataType'] = 'string';
        $customFIelds[] = $obj;
      }  
    }
    return $customFIelds;
  }

}