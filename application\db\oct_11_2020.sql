-- MySQL dump 10.13  Distrib 8.0.21, for Win64 (x86_64)
--
-- Host: localhost    Database: pncc
-- ------------------------------------------------------
-- Server version	5.6.44-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `academic_year`
--

DROP TABLE IF EXISTS `academic_year`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `academic_year` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `acad_year` varchar(100) NOT NULL,
  `is_current_year` tinyint(4) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `accounts`
--

DROP TABLE IF EXISTS `accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `tracknpay_vendor_id` varchar(100) DEFAULT NULL,
  `account` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `additional_income_category`
--

DROP TABLE IF EXISTS `additional_income_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `additional_income_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(45) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `additional_income_master`
--

DROP TABLE IF EXISTS `additional_income_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `additional_income_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `received_on` date DEFAULT NULL,
  `voucher_url` varchar(225) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `vendor_id` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `acad_year_id` int(11) NOT NULL,
  `description` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `address_info`
--

DROP TABLE IF EXISTS `address_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `address_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Address_line1` varchar(255) NOT NULL,
  `Address_line2` varchar(255) NOT NULL,
  `area` varchar(100) NOT NULL,
  `district` varchar(100) NOT NULL,
  `state` varchar(100) NOT NULL,
  `country` varchar(100) NOT NULL,
  `pin_code` varchar(10) NOT NULL,
  `address_type` tinyint(4) NOT NULL COMMENT '0: Present Address, 1: Permanent Address',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `avatar_type` tinyint(4) NOT NULL COMMENT '1: Student, 2: Parent, 3: Super admin, 4: Staff',
  `stakeholder_id` int(11) NOT NULL COMMENT 'Avatar ID is a staff_id or student_id or parent_id depending on avatar type',
  `unformatted_address` text,
  `phone_number` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_documents`
--

DROP TABLE IF EXISTS `admission_documents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admission_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `af_id` int(11) NOT NULL,
  `document_type` varchar(100) NOT NULL,
  `document_uri` varchar(100) NOT NULL,
  `document_other` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_email`
--

DROP TABLE IF EXISTS `admission_email`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admission_email` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `af_id` int(11) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `template_content` text NOT NULL,
  `father_mail_id` varchar(255) DEFAULT NULL,
  `mother_mail_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_forms`
--

DROP TABLE IF EXISTS `admission_forms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admission_forms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `au_id` int(11) NOT NULL,
  `academic_year_applied_for` varchar(100) NOT NULL,
  `grade_applied_for` varchar(100) NOT NULL,
  `std_name` varchar(255) NOT NULL,
  `dob` date NOT NULL,
  `gender` char(2) NOT NULL COMMENT 'M:Male, F:Female',
  `birth_taluk` varchar(255) NOT NULL,
  `birth_district` varchar(100) NOT NULL,
  `nationality` varchar(100) NOT NULL,
  `religion` varchar(100) NOT NULL,
  `std_mother_tongue` varchar(100) NOT NULL,
  `physical_disability` char(2) NOT NULL,
  `learning_disability` char(2) NOT NULL,
  `f_name` varchar(255) NOT NULL,
  `f_addr` text NOT NULL,
  `f_district` varchar(100) NOT NULL,
  `f_state` varchar(100) NOT NULL,
  `f_county` varchar(100) NOT NULL,
  `f_pincode` varchar(50) NOT NULL,
  `f_office_ph` varchar(100) NOT NULL,
  `f_res_ph` varchar(100) NOT NULL,
  `f_mobile_no` varchar(100) NOT NULL,
  `f_email_id` varchar(100) NOT NULL,
  `f_position` varchar(100) NOT NULL,
  `f_company_name` varchar(255) NOT NULL,
  `f_company_addr` text NOT NULL,
  `f_company_district` varchar(100) NOT NULL,
  `f_company_state` varchar(100) NOT NULL,
  `f_company_county` varchar(100) NOT NULL,
  `f_company_pincode` varchar(50) NOT NULL,
  `f_annual_gross_income` varchar(100) NOT NULL,
  `m_name` varchar(255) NOT NULL,
  `m_addr` text NOT NULL,
  `m_district` varchar(100) NOT NULL,
  `m_state` varchar(100) NOT NULL,
  `m_county` varchar(100) NOT NULL,
  `m_office_ph` varchar(100) NOT NULL,
  `m_res_ph` varchar(100) NOT NULL,
  `m_mobile_no` varchar(100) NOT NULL,
  `m_email_id` varchar(100) NOT NULL,
  `m_position` varchar(100) NOT NULL,
  `m_company_name` varchar(100) NOT NULL,
  `m_company_addr` text NOT NULL,
  `m_company_district` varchar(100) NOT NULL,
  `m_company_state` varchar(100) NOT NULL,
  `m_company_county` varchar(100) NOT NULL,
  `m_company_pincode` varchar(100) NOT NULL,
  `m_pincode` varchar(100) NOT NULL,
  `m_annual_gross_income` varchar(100) NOT NULL,
  `std_photo_uri` varchar(255) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `nationality_other` varchar(100) DEFAULT NULL,
  `religion_other` varchar(100) DEFAULT NULL,
  `mother_tongue_other` varchar(100) DEFAULT NULL,
  `application_no` varchar(100) NOT NULL,
  `sibling_student_name` varchar(255) DEFAULT NULL,
  `sibling_student_class` varchar(100) DEFAULT NULL,
  `f_area` varchar(255) NOT NULL,
  `m_area` varchar(255) NOT NULL,
  `f_company_area` varchar(255) NOT NULL,
  `m_company_area` varchar(255) NOT NULL,
  `instruction_file` varchar(100) NOT NULL COMMENT 'config instruction file name',
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `father_mother_tongue` varchar(255) DEFAULT NULL,
  `father_mother_tongue_other` varchar(255) DEFAULT NULL,
  `mother_mother_tongue` varchar(255) DEFAULT NULL,
  `mother_mother_tongue_other` varchar(255) DEFAULT NULL,
  `custom_field` varchar(255) DEFAULT NULL,
  `f_profession` varchar(255) NOT NULL,
  `m_profession` varchar(255) NOT NULL,
  `student_aadhar` varchar(255) NOT NULL,
  `student_caste` varchar(255) NOT NULL,
  `father_aadhar` varchar(255) NOT NULL,
  `mother_aadhar` varchar(255) NOT NULL,
  `student_blood_group` varchar(255) DEFAULT NULL,
  `g_name` varchar(255) DEFAULT NULL,
  `g_mobile_no` varchar(255) DEFAULT NULL,
  `g_email_id` varchar(255) DEFAULT NULL,
  `g_addr` varchar(255) DEFAULT NULL,
  `g_area` varchar(255) DEFAULT NULL,
  `g_district` varchar(255) DEFAULT NULL,
  `g_state` varchar(255) DEFAULT NULL,
  `g_county` varchar(255) DEFAULT NULL,
  `g_pincode` varchar(255) DEFAULT NULL,
  `g_office_ph` varchar(255) DEFAULT NULL,
  `g_annual_gross_income` varchar(255) DEFAULT NULL,
  `g_res_ph` varchar(255) DEFAULT NULL,
  `g_position` varchar(255) DEFAULT NULL,
  `g_company_name` varchar(255) DEFAULT NULL,
  `g_company_addr` varchar(255) DEFAULT NULL,
  `g_company_area` varchar(255) DEFAULT NULL,
  `g_company_district` varchar(255) DEFAULT NULL,
  `g_company_state` varchar(255) DEFAULT NULL,
  `g_company_county` varchar(255) DEFAULT NULL,
  `g_company_pincode` varchar(255) DEFAULT NULL,
  `guardian_mother_tongue_other` varchar(255) DEFAULT NULL,
  `guardian_aadhar` varchar(255) DEFAULT NULL,
  `g_profession` varchar(255) DEFAULT NULL,
  `guardian_mother_tongue` varchar(255) DEFAULT NULL,
  `f_qualification` varchar(255) DEFAULT NULL,
  `m_qualification` varchar(255) DEFAULT NULL,
  `g_qualification` varchar(255) DEFAULT NULL,
  `admission_setting_id` int(11) NOT NULL,
  `filled_by` int(11) NOT NULL DEFAULT '0',
  `enquiry_id` int(11) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_prev_school`
--

DROP TABLE IF EXISTS `admission_prev_school`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admission_prev_school` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `af_id` int(11) NOT NULL,
  `au_id` int(11) NOT NULL,
  `year` varchar(100) NOT NULL,
  `school_name` varchar(255) NOT NULL,
  `class` varchar(100) NOT NULL,
  `board` varchar(100) DEFAULT NULL,
  `board_other` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_prev_school_marks`
--

DROP TABLE IF EXISTS `admission_prev_school_marks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admission_prev_school_marks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sub_name` varchar(100) NOT NULL,
  `grade` varchar(100) NOT NULL,
  `percentage` decimal(10,2) NOT NULL,
  `aps_id` int(11) NOT NULL COMMENT 'adm_prev_school_id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_settings`
--

DROP TABLE IF EXISTS `admission_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admission_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `form_name` varchar(255) NOT NULL,
  `form_year` varchar(255) NOT NULL,
  `school_name` varchar(255) NOT NULL,
  `instruction_file` text NOT NULL,
  `school_short` varchar(255) NOT NULL,
  `school` varchar(255) NOT NULL,
  `open_for_admissions` tinyint(4) NOT NULL,
  `class_applied_for` text NOT NULL,
  `documents` text,
  `prev_eduction_info` text,
  `streams` text,
  `guidelines` text,
  `application_no_gen` text,
  `show_guardian_details` tinyint(1) NOT NULL DEFAULT '0',
  `address` text,
  `receipt_book_id` int(11) DEFAULT NULL,
  `online_payment` tinyint(1) NOT NULL DEFAULT '0',
  `admission_logo` varchar(255) DEFAULT NULL,
  `admission_bg_logo` varchar(255) DEFAULT NULL,
  `final_description` text,
  `admission_fee_amount` decimal(10,2) DEFAULT NULL,
  `email_template_id` int(11) DEFAULT NULL,
  `dob_instructions` varchar(255) DEFAULT NULL,
  `email_template_id_staff` int(11) DEFAULT NULL,
  `staff_id` varchar(255) DEFAULT NULL,
  `acad_year` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_status`
--

DROP TABLE IF EXISTS `admission_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admission_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `af_id` varchar(100) NOT NULL,
  `prev_status` varchar(100) NOT NULL,
  `curr_status` varchar(100) NOT NULL,
  `status_changed_by` varchar(100) NOT NULL,
  `status_changed_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `comments` text NOT NULL,
  `payment_status` varchar(255) DEFAULT NULL,
  `student_admission_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_transaction`
--

DROP TABLE IF EXISTS `admission_transaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admission_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `af_id` varchar(45) NOT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `collected_by` int(11) DEFAULT NULL,
  `payment_type` varchar(100) DEFAULT NULL,
  `cheque_or_dd_number` varchar(100) DEFAULT NULL,
  `cheque_or_dd_date` date DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `bank_name` varchar(100) DEFAULT NULL,
  `branch_name` varchar(100) DEFAULT NULL,
  `remarks` varchar(255) DEFAULT NULL,
  UNIQUE KEY `id_UNIQUE` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admission_user`
--

DROP TABLE IF EXISTS `admission_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admission_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` int(11) NOT NULL,
  `mobile_no` varchar(50) DEFAULT NULL,
  `otp` varchar(10) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_assessment_rubrics`
--

DROP TABLE IF EXISTS `afl_assessment_rubrics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_assessment_rubrics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `afl_rubric_id` int(11) NOT NULL,
  `afl_assessment_subject_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_assessment_subject_rubrics`
--

DROP TABLE IF EXISTS `afl_assessment_subject_rubrics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_assessment_subject_rubrics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `afl_rubric_id` int(11) NOT NULL,
  `afl_assessment_subject_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_assessment_subjects`
--

DROP TABLE IF EXISTS `afl_assessment_subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_assessment_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `afl_assessment_id` int(11) NOT NULL,
  `afl_subject_id` int(11) NOT NULL,
  `afl_topic_ids_json` varchar(255) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `last_modified_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `publish_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: not published, 1: published',
  `verification_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: not added, 1: created, 2: verified, 3:need changes',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_assessments`
--

DROP TABLE IF EXISTS `afl_assessments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_assessments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_name` varchar(255) NOT NULL,
  `class_id` varchar(45) NOT NULL,
  `show_self_evaluation` tinyint(4) NOT NULL DEFAULT '1',
  `schedule` varchar(45) NOT NULL,
  `afl_grading_scale_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_grading_scale`
--

DROP TABLE IF EXISTS `afl_grading_scale`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_grading_scale` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `grading_scale_name` varchar(255) NOT NULL,
  `description` tinytext,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_grading_scale_values`
--

DROP TABLE IF EXISTS `afl_grading_scale_values`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_grading_scale_values` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `afl_grading_scale_id` int(11) NOT NULL,
  `range_name` varchar(255) NOT NULL,
  `start_range` varchar(20) NOT NULL,
  `end_range` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_performance_pointers`
--

DROP TABLE IF EXISTS `afl_performance_pointers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_performance_pointers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pointer_name` mediumtext NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_rubric_perf_parameters`
--

DROP TABLE IF EXISTS `afl_rubric_perf_parameters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_rubric_perf_parameters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `perf_pointer_id` int(11) NOT NULL,
  `afl_assessment_subject_id` int(11) NOT NULL,
  `description` mediumtext,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_rubric_perf_parameters_grading_scale_desc`
--

DROP TABLE IF EXISTS `afl_rubric_perf_parameters_grading_scale_desc`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_rubric_perf_parameters_grading_scale_desc` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `afl_grading_scale_values_id` int(11) NOT NULL,
  `afl_rubric_perf_parameters_id` int(11) NOT NULL,
  `description` mediumtext,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_rubrics`
--

DROP TABLE IF EXISTS `afl_rubrics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_rubrics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rubric_name` varchar(255) NOT NULL,
  `afl_grading_scale_id` int(11) DEFAULT NULL,
  `description` mediumtext,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_strands`
--

DROP TABLE IF EXISTS `afl_strands`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_strands` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `strand_name` varchar(255) NOT NULL,
  `afl_subject_id` int(11) NOT NULL,
  `description` text,
  `status` tinyint(4) NOT NULL DEFAULT '0',
  `last_status_changed_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_student_marks`
--

DROP TABLE IF EXISTS `afl_student_marks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_student_marks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `afl_student_perf_id` int(11) NOT NULL,
  `afl_rubric_perf_parameter_id` int(11) NOT NULL,
  `self_marks` decimal(10,2) DEFAULT NULL,
  `evaluated_marks` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_student_marks_history`
--

DROP TABLE IF EXISTS `afl_student_marks_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_student_marks_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `afl_student_marks_id` int(11) NOT NULL,
  `marks` decimal(10,2) DEFAULT NULL,
  `marks_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1:self marks, 2: facilitator marks',
  `stakeholder_id` int(11) NOT NULL,
  `avatar_type` int(4) NOT NULL,
  `status` tinyint(4) NOT NULL COMMENT '1:saved, 2:locked',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_student_perf_pointers`
--

DROP TABLE IF EXISTS `afl_student_perf_pointers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_student_perf_pointers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `afl_assessment_subject_id` int(11) NOT NULL,
  `self_marks_status` tinyint(4) DEFAULT '0' COMMENT '0: Not Added, 1: Partially Added, 2: Completed',
  `evaluated_marks_status` tinyint(4) DEFAULT '0' COMMENT '0: Not Added, 1: Partially Added, 2: Completed',
  `publish_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0:Not Published, 1: Published',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_student_remarks`
--

DROP TABLE IF EXISTS `afl_student_remarks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_student_remarks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `afl_assessment_subject_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `remarks` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_sub_strands`
--

DROP TABLE IF EXISTS `afl_sub_strands`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_sub_strands` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sub_strand_name` tinytext NOT NULL,
  `afl_strand_id` int(11) NOT NULL,
  `description` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_subject_access`
--

DROP TABLE IF EXISTS `afl_subject_access`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_subject_access` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `afl_subject_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `section_id` int(11) NOT NULL,
  `access_level` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_subjects`
--

DROP TABLE IF EXISTS `afl_subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_name` varchar(255) NOT NULL,
  `class_id` int(11) NOT NULL,
  `description` text,
  `is_elective` tinyint(4) NOT NULL DEFAULT '0',
  `afl_subject_elective_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `afl_topics`
--

DROP TABLE IF EXISTS `afl_topics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `afl_topics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `topic` mediumtext NOT NULL,
  `afl_sub_strand_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_elective_group`
--

DROP TABLE IF EXISTS `assessment_elective_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_elective_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(100) NOT NULL,
  `class_id` int(11) NOT NULL,
  `mapping_string` varchar(100) DEFAULT NULL,
  `friendly_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_entities_group`
--

DROP TABLE IF EXISTS `assessment_entities_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_entities_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `entity_name` varchar(100) NOT NULL,
  `class_id` int(11) NOT NULL,
  `elective_group_id` int(11) DEFAULT NULL,
  `is_elective` tinyint(1) NOT NULL DEFAULT '0',
  `mapping_string` varchar(255) DEFAULT NULL,
  `grading_system_id` int(11) DEFAULT NULL,
  `sorting_order` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_entity_master`
--

DROP TABLE IF EXISTS `assessment_entity_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_entity_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `mapping_string` varchar(255) NOT NULL,
  `class_id` int(11) NOT NULL,
  `ass_type` varchar(45) NOT NULL,
  `ass_entity_gid` int(11) NOT NULL,
  `grading_system_id` int(11) DEFAULT NULL,
  `sorting_order` int(11) DEFAULT NULL,
  `short_name` varchar(255) DEFAULT NULL,
  `derived_formula` text,
  `evaluation_type` varchar(20) DEFAULT 'marks',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_grades`
--

DROP TABLE IF EXISTS `assessment_grades`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_grades` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_id` int(11) NOT NULL,
  `grades` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_grading_system`
--

DROP TABLE IF EXISTS `assessment_grading_system`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_grading_system` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `grades` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_halltickets`
--

DROP TABLE IF EXISTS `assessment_halltickets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_halltickets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` int(11) NOT NULL,
  `template` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_marks_card_templates`
--

DROP TABLE IF EXISTS `assessment_marks_card_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_marks_card_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) NOT NULL,
  `class_id` int(11) NOT NULL,
  `template_content` text,
  `assessments` text NOT NULL,
  `lock_remarks` tinyint(1) DEFAULT '0',
  `publish_status` tinyint(1) DEFAULT '0',
  `grading_systems` varchar(255) DEFAULT NULL,
  `generation_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: Auto, 1: Manual',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_portions`
--

DROP TABLE IF EXISTS `assessment_portions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_portions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` int(11) NOT NULL,
  `portions` text,
  `publish_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0: Not published, 1: Published',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_remarks_history`
--

DROP TABLE IF EXISTS `assessment_remarks_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_remarks_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ass_marks_card_id` int(11) NOT NULL,
  `remarks` text NOT NULL,
  `action_by` int(11) NOT NULL,
  `modified_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_sections`
--

DROP TABLE IF EXISTS `assessment_sections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_sections` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` int(11) NOT NULL,
  `class_section_id` int(11) NOT NULL,
  `class_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_students_elective`
--

DROP TABLE IF EXISTS `assessment_students_elective`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_students_elective` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `ass_elective_gid` int(11) NOT NULL,
  `ass_entity_gid` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_subject_group`
--

DROP TABLE IF EXISTS `assessment_subject_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_subject_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` int(11) NOT NULL,
  `ass_entity_gid` int(11) NOT NULL,
  `portions` text,
  `date` date DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `portions_at` tinyint(1) DEFAULT NULL COMMENT '0:Subject Level, 1:Entity Level',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_subject_remarks`
--

DROP TABLE IF EXISTS `assessment_subject_remarks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_subject_remarks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `subject_remarks` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_subject_remarks_description`
--

DROP TABLE IF EXISTS `assessment_subject_remarks_description`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_subject_remarks_description` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `remarks_group_id` int(11) NOT NULL,
  `subject_remarks` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessment_subject_remarks_group`
--

DROP TABLE IF EXISTS `assessment_subject_remarks_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessment_subject_remarks_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL,
  `remarks_for` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: Subject Remarks, 1: Reprt Card Remarks',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments`
--

DROP TABLE IF EXISTS `assessments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ass_type` varchar(45) NOT NULL COMMENT 'Internal:0, External:1. External will be published to parents.',
  `short_name` varchar(255) DEFAULT NULL,
  `long_name` varchar(255) DEFAULT NULL,
  `description` text,
  `publish_status` varchar(25) NOT NULL DEFAULT 'created' COMMENT 'Possible Status: created, pending_approval, approval, published',
  `release_marks` tinyint(1) DEFAULT '0',
  `generation_type` varchar(20) NOT NULL DEFAULT 'Manual',
  `formula` text,
  `sorting_order` int(11) DEFAULT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  `class_id` int(11) NOT NULL,
  `enable_subject_remarks` tinyint(4) NOT NULL DEFAULT '0',
  `remarks_group_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='To create Assessments for all classes';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_entities`
--

DROP TABLE IF EXISTS `assessments_entities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessments_entities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` int(11) NOT NULL,
  `ass_subject_gid` int(11) DEFAULT NULL,
  `entity_id` int(11) NOT NULL,
  `portions` text,
  `date` date DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `total_marks` int(11) DEFAULT NULL,
  `class_average` decimal(10,2) NOT NULL DEFAULT '-1.00',
  `class_highest` decimal(10,2) DEFAULT '-1.00',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_entities_access`
--

DROP TABLE IF EXISTS `assessments_entities_access`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessments_entities_access` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessments_entities_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `access_level` varchar(45) NOT NULL COMMENT 'read, write, admin',
  `class_section_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_entities_marks_students`
--

DROP TABLE IF EXISTS `assessments_entities_marks_students`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessments_entities_marks_students` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `assessments_entities_id` int(11) NOT NULL COMMENT 'Assessment->SubSkill->TotalMarks ID',
  `marks` decimal(10,2) NOT NULL,
  `percentage` decimal(10,2) NOT NULL,
  `grade` varchar(255) NOT NULL,
  `status` tinyint(2) NOT NULL,
  `acad_year_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_entities_marks_students_history`
--

DROP TABLE IF EXISTS `assessments_entities_marks_students_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessments_entities_marks_students_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `assessments_entities_marks_students_id` bigint(20) NOT NULL,
  `action` text,
  `action_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_history`
--

DROP TABLE IF EXISTS `assessments_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessments_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` int(11) NOT NULL,
  `action` text COMMENT 'What are the fields that changed?',
  `action_by` int(11) NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_marks_card_history`
--

DROP TABLE IF EXISTS `assessments_marks_card_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessments_marks_card_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ass_marks_card_id` int(11) NOT NULL,
  `pdf_link` varchar(255) NOT NULL,
  `date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint(4) NOT NULL DEFAULT '0',
  `action_comment` text,
  `action_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `assessments_marks_cards`
--

DROP TABLE IF EXISTS `assessments_marks_cards`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assessments_marks_cards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `remarks` text NOT NULL,
  `status` varchar(100) NOT NULL DEFAULT 'Not Generated' COMMENT 'Generated, Published, Verified',
  `created_by` int(11) NOT NULL,
  `active_marks_card_id` int(11) NOT NULL,
  `marks_card_temp_id` int(11) NOT NULL,
  `remark_status` varchar(45) DEFAULT 'Not Verified',
  `ack_status` tinyint(1) NOT NULL DEFAULT '0',
  `ack_on` datetime DEFAULT NULL,
  `ack_by` int(11) DEFAULT NULL,
  `additional_remarks` text,
  `pictures` text,
  `published` tinyint(4) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `asset_allocation`
--

DROP TABLE IF EXISTS `asset_allocation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `asset_allocation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_id` int(11) DEFAULT NULL,
  `asset_tag` varchar(255) NOT NULL,
  `allocating_date` date NOT NULL,
  `allocating_to` int(11) NOT NULL COMMENT 'contains room id or staff id',
  `allocation_type` int(11) DEFAULT NULL COMMENT '1 for Room\n2 for Staff',
  `description` text,
  `collected_on` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `asset_category`
--

DROP TABLE IF EXISTS `asset_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `asset_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `asset_master`
--

DROP TABLE IF EXISTS `asset_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `asset_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_name` varchar(255) NOT NULL,
  `available_assets` int(11) NOT NULL,
  `allocated_assets` int(11) NOT NULL,
  `total_cost` decimal(10,2) NOT NULL,
  `quantity` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `description` text,
  `created_on` date DEFAULT NULL,
  `created_by` varchar(150) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `asset_repair`
--

DROP TABLE IF EXISTS `asset_repair`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `asset_repair` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_tag` varchar(45) DEFAULT NULL,
  `repair_moveby` varchar(45) DEFAULT NULL,
  `repair_moveon` date DEFAULT NULL,
  `repair_by` varchar(200) DEFAULT NULL,
  `repair_on` date DEFAULT NULL,
  `repair_cost` double DEFAULT NULL,
  `status` int(11) DEFAULT NULL COMMENT '1 for under repair and 2 for repaired',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `asset_tags`
--

DROP TABLE IF EXISTS `asset_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `asset_tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_id` int(11) DEFAULT NULL,
  `asset_tag` varchar(256) DEFAULT NULL,
  `date_of_addition` date DEFAULT NULL,
  `remarks` text,
  `discarded_date` date DEFAULT NULL,
  `status` int(11) DEFAULT NULL COMMENT '1 for available , 2 for alloted, 3 for discarded and 4 for missing',
  `discarded_by` varchar(45) DEFAULT NULL,
  `reason` varchar(200) DEFAULT NULL,
  `purchase_date` date NOT NULL,
  `asset_cost` decimal(10,2) NOT NULL,
  `warrenty_period` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `attendance_leave_event`
--

DROP TABLE IF EXISTS `attendance_leave_event`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `attendance_leave_event` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `attendance_session_id` int(11) NOT NULL,
  `event_type` varchar(20) NOT NULL,
  `event_id` int(11) NOT NULL,
  `student_admission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `attendance_master`
--

DROP TABLE IF EXISTS `attendance_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `attendance_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_id` int(11) NOT NULL,
  `type` tinyint(4) NOT NULL COMMENT '"1:weekday2:saturday"',
  `name` varchar(10) NOT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `attendance_master_group`
--

DROP TABLE IF EXISTS `attendance_master_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `attendance_master_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `attendance_master_id` int(11) NOT NULL,
  `timetable_template_periods_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `attendance_session`
--

DROP TABLE IF EXISTS `attendance_session`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `attendance_session` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `day` date NOT NULL,
  `attendance_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `avatar_id` int(11) NOT NULL,
  `class_id` int(11) NOT NULL,
  `class_section_id` int(11) NOT NULL,
  `attendance_master_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `attendance_student`
--

DROP TABLE IF EXISTS `attendance_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `attendance_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `attendance_session_id` int(11) NOT NULL,
  `attendance_master_id` int(11) NOT NULL,
  `attendance_master_group_id` int(11) NOT NULL,
  `status` tinyint(4) NOT NULL COMMENT '"1:present,0:absent"',
  `subject_id` int(11) NOT NULL,
  `substitute_id` int(11) NOT NULL,
  `history` text NOT NULL COMMENT 'json_value("staff_id,event,event_time")',
  `reference_type` tinyint(4) DEFAULT NULL COMMENT '1->competation',
  `reference_id` int(11) DEFAULT NULL,
  `reference_status` tinyint(4) DEFAULT '0',
  `student_admission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `avatar`
--

DROP TABLE IF EXISTS `avatar`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `avatar` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `avatar_type` tinyint(4) NOT NULL COMMENT '1: Student, 2: Parent, 3: Super admin, 4: Staff',
  `stakeholder_id` int(11) NOT NULL COMMENT 'Avatar ID is a staff_id or student_id or parent_id depending on avatar type',
  `friendly_name` varchar(255) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `old_user_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `books_cart`
--

DROP TABLE IF EXISTS `books_cart`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `books_cart` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lbc_id` int(11) NOT NULL,
  `avatar_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `branches`
--

DROP TABLE IF EXISTS `branches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `branches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `building_master`
--

DROP TABLE IF EXISTS `building_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `building_master` (
  `room_id` int(11) NOT NULL AUTO_INCREMENT,
  `room_no` varchar(255) NOT NULL,
  `room_name` varchar(255) NOT NULL,
  `room_type` varchar(255) NOT NULL,
  `floor` varchar(20) NOT NULL,
  `block` varchar(20) NOT NULL,
  `schedulable` tinyint(1) NOT NULL,
  `schedulable_online_booking` tinyint(1) DEFAULT '0',
  `section_id` int(11) DEFAULT NULL,
  `allow_booking_for` tinyint(1) DEFAULT NULL COMMENT '0:all 1: only principal',
  PRIMARY KEY (`room_id`),
  UNIQUE KEY `room_no` (`room_no`,`floor`,`block`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cache`
--

DROP TABLE IF EXISTS `cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cache_key` varchar(100) NOT NULL,
  `cache_value` text NOT NULL,
  `Note` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `canteen_menu`
--

DROP TABLE IF EXISTS `canteen_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `canteen_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `menu_name` varchar(100) NOT NULL,
  `menu_price` decimal(10,0) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `canteen_transaction`
--

DROP TABLE IF EXISTS `canteen_transaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `canteen_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identification_code` varchar(100) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `canteen_transaction_items`
--

DROP TABLE IF EXISTS `canteen_transaction_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `canteen_transaction_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `canteen_trans_id` int(11) NOT NULL,
  `menu_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price_per_piece` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `card_transaction`
--

DROP TABLE IF EXISTS `card_transaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `card_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_mode` varchar(100) NOT NULL,
  `amount` float NOT NULL,
  `identification_code` varchar(100) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `transcation_type` varchar(100) NOT NULL,
  `transaction_type_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cert_templates`
--

DROP TABLE IF EXISTS `cert_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cert_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) NOT NULL,
  `html_content` text NOT NULL,
  `purpose` varchar(255) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `type` varchar(45) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ci_sessions`
--

DROP TABLE IF EXISTS `ci_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ci_sessions` (
  `id` varchar(40) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `timestamp` int(10) unsigned NOT NULL DEFAULT '0',
  `data` blob NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ci_sessions_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `circular_master`
--

DROP TABLE IF EXISTS `circular_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `circular_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by` int(11) NOT NULL,
  `circular_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `circular_content` text NOT NULL,
  `user_type` char(10) NOT NULL,
  `circular_title` varchar(255) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `is_published` tinyint(1) NOT NULL,
  `sent_type` varchar(45) NOT NULL,
  `category` varchar(100) NOT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `user_type_detail` text,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `circular_sent_to`
--

DROP TABLE IF EXISTS `circular_sent_to`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `circular_sent_to` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `circular_id` int(11) NOT NULL,
  `stakeholder_id` int(11) NOT NULL,
  `avatar_type` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `circularv2_categories`
--

DROP TABLE IF EXISTS `circularv2_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `circularv2_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(255) NOT NULL,
  `require_approval` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `circularv2_master`
--

DROP TABLE IF EXISTS `circularv2_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `circularv2_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `category` varchar(255) NOT NULL,
  `sent_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `sent_by` int(11) NOT NULL,
  `recievers` text NOT NULL COMMENT '''Student'', ''Staff'', ''1A,1B,1C...''',
  `mode` varchar(45) NOT NULL,
  `file_path` mediumtext,
  `acad_year_id` int(11) NOT NULL,
  `visible` tinyint(4) NOT NULL DEFAULT '1',
  `is_approved` tinyint(4) NOT NULL DEFAULT '1',
  `is_file_path_json` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: No, 1: Yes',
  `sender_list` text,
  `sending_status` varchar(20) NOT NULL DEFAULT 'Initiated',
  `texting_master_id` int(11) DEFAULT NULL,
  `email_master_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `circularv2_sent_to`
--

DROP TABLE IF EXISTS `circularv2_sent_to`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `circularv2_sent_to` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `circularv2_master_id` int(11) NOT NULL,
  `stakeholder_id` int(11) NOT NULL COMMENT 'ParentId / StaffId',
  `email` varchar(255) DEFAULT NULL,
  `avatar_type` int(3) NOT NULL COMMENT '2: parent, 4: Staff',
  `is_read` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `class`
--

DROP TABLE IF EXISTS `class`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `class` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_name` varchar(20) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `board` varchar(50) NOT NULL,
  `medium` varchar(50) NOT NULL,
  `type` tinyint(4) NOT NULL,
  `principal_id` int(11) DEFAULT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  `promotion_class` int(11) DEFAULT NULL,
  `is_placeholder` tinyint(1) NOT NULL DEFAULT '0',
  `coordinator_id` int(11) DEFAULT NULL,
  `academic_director_id` int(11) DEFAULT NULL,
  `enquiry_budget` int(11) NOT NULL DEFAULT '0',
  `academic_budget` int(11) DEFAULT '0',
  `branch_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `class_section`
--

DROP TABLE IF EXISTS `class_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `class_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_id` int(11) NOT NULL,
  `section_name` varchar(20) NOT NULL,
  `class_teacher_id` int(11) NOT NULL COMMENT 'Staff id of class teacher',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `room_id` int(11) DEFAULT NULL,
  `class_name` varchar(20) DEFAULT NULL,
  `is_placeholder` tinyint(1) NOT NULL DEFAULT '0',
  `assistant_class_teacher_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `class_id` (`class_id`),
  CONSTRAINT `section_class_id` FOREIGN KEY (`class_id`) REFERENCES `class` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `combinations`
--

DROP TABLE IF EXISTS `combinations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `combinations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `combination` varchar(255) DEFAULT NULL,
  `af_id` int(11) DEFAULT NULL,
  `au_id` int(11) DEFAULT NULL,
  `combination_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `competition_attendance`
--

DROP TABLE IF EXISTS `competition_attendance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `competition_attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `competition_id` int(11) NOT NULL,
  `day` date NOT NULL,
  `student_id` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `history` text NOT NULL,
  `student_admission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `competition_master`
--

DROP TABLE IF EXISTS `competition_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `competition_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `competition_name` varchar(100) NOT NULL,
  `venue_address` varchar(150) NOT NULL,
  `registration_fees` varchar(50) NOT NULL,
  `lastdate_registration` date NOT NULL,
  `venue_iscampus` char(10) NOT NULL,
  `transportation_mode` char(6) DEFAULT NULL,
  `staff_assigned` text NOT NULL,
  `organizer` varchar(100) NOT NULL,
  `status` int(11) NOT NULL,
  `created_by` int(11) NOT NULL,
  `staff_remarks` text NOT NULL,
  `principal_remarks` text NOT NULL,
  `description` text NOT NULL,
  `created_at` date NOT NULL,
  `competition_code` varchar(105) DEFAULT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `competition_student_registration`
--

DROP TABLE IF EXISTS `competition_student_registration`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `competition_student_registration` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `std_userId` int(11) NOT NULL,
  `competition_id` varchar(100) NOT NULL,
  `remarks` varchar(100) NOT NULL,
  `other_remarks` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `competition_time_details`
--

DROP TABLE IF EXISTS `competition_time_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `competition_time_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `competition_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `reporting_time` time NOT NULL,
  `competition_id` int(11) NOT NULL,
  `substitution_added` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `config`
--

DROP TABLE IF EXISTS `config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `value` text,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `type` varchar(45) NOT NULL DEFAULT 'string',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_UNIQUE` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `elective_student`
--

DROP TABLE IF EXISTS `elective_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `elective_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `subject_id` bigint(20) NOT NULL,
  `elective_group_id` int(11) NOT NULL,
  `class_section_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `class_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `student_id` (`student_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `email_master`
--

DROP TABLE IF EXISTS `email_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `sent_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `sent_by` int(11) NOT NULL,
  `source` varchar(255) DEFAULT NULL,
  `from_email` varchar(255) NOT NULL,
  `visible` tinyint(4) NOT NULL DEFAULT '1',
  `acad_year_id` int(11) NOT NULL,
  `recievers` mediumtext,
  `files` text,
  `sender_list` text,
  `sending_status` varchar(45) DEFAULT 'Initiated',
  `texting_master_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `email_sent_to`
--

DROP TABLE IF EXISTS `email_sent_to`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_sent_to` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email_master_id` int(11) NOT NULL,
  `stakeholder_id` int(11) NOT NULL,
  `avatar_type` int(11) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `status` varchar(45) DEFAULT 'Awaited',
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `email_template`
--

DROP TABLE IF EXISTS `email_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `members_email` text,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `registered_email` varchar(100) DEFAULT NULL,
  `email_subject` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `enquiry`
--

DROP TABLE IF EXISTS `enquiry`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `enquiry` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_name` varchar(100) DEFAULT NULL,
  `gender` char(4) DEFAULT NULL,
  `student_dob` date DEFAULT NULL,
  `class_name` int(11) DEFAULT NULL,
  `parent_name` varchar(100) NOT NULL,
  `mobile_number` varchar(100) NOT NULL,
  `alternate_mobile_number` varchar(100) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(100) NOT NULL,
  `academic_year` int(11) NOT NULL,
  `student_current_school` varchar(255) DEFAULT NULL,
  `board` varchar(100) DEFAULT NULL,
  `assigned_to` int(11) DEFAULT NULL,
  `source` varchar(100) DEFAULT NULL,
  `admission_form_id` int(11) DEFAULT '0',
  `got_to_know_by` varchar(255) DEFAULT 'Others',
  `age` int(11) DEFAULT NULL,
  `has_sibling` tinyint(4) NOT NULL DEFAULT '0',
  `where_is_sibling` varchar(255) DEFAULT NULL,
  `next_follow_date` date DEFAULT NULL,
  `lead_status` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `enquiry_follow_up`
--

DROP TABLE IF EXISTS `enquiry_follow_up`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `enquiry_follow_up` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enquiry_id` int(11) NOT NULL,
  `follow_up_by` int(11) NOT NULL,
  `status` varchar(100) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `message` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `event_master`
--

DROP TABLE IF EXISTS `event_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `event_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_name` varchar(100) NOT NULL,
  `venue_address` varchar(150) NOT NULL,
  `registration_fees` varchar(50) NOT NULL,
  `lastdate_registration` date NOT NULL,
  `venue_iscampus` char(10) NOT NULL,
  `transportation_mode` char(6) DEFAULT NULL,
  `staff_assigned` text NOT NULL,
  `organizer` varchar(100) NOT NULL,
  `status` int(11) NOT NULL,
  `created_by` int(11) NOT NULL,
  `staff_remarks` text NOT NULL,
  `principal_remarks` text NOT NULL,
  `description` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `event_master_v2`
--

DROP TABLE IF EXISTS `event_master_v2`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `event_master_v2` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_name` varchar(100) NOT NULL,
  `event_date` date NOT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `description` text,
  `status` tinyint(4) DEFAULT '0' COMMENT '1:PUBLISH,0:UNPUBLISH',
  `acad_year_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `event_code` varchar(100) DEFAULT NULL,
  `organizer` varchar(100) DEFAULT NULL,
  `event_venue` varchar(100) DEFAULT NULL,
  `event_venue_address` varchar(100) DEFAULT NULL,
  `no_of_person_allowed` int(11) DEFAULT '0',
  `event_file` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `event_register_details`
--

DROP TABLE IF EXISTS `event_register_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `event_register_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_id` int(11) DEFAULT NULL,
  `student_id` int(11) DEFAULT NULL,
  `qr_code` varchar(100) DEFAULT NULL,
  `no_of_person_confirm` int(11) DEFAULT NULL,
  `no_of_person_entry` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `section_id` int(11) DEFAULT NULL,
  `follow_up_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `event_time_details`
--

DROP TABLE IF EXISTS `event_time_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `event_time_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `reporting_time` time NOT NULL,
  `event_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `expense_category`
--

DROP TABLE IF EXISTS `expense_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `expense_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `expense_master`
--

DROP TABLE IF EXISTS `expense_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `expense_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `made_on` date NOT NULL,
  `voucher_url` varchar(100) DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `approve_status` int(11) DEFAULT NULL,
  `made_by` int(11) DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `acad_year_id` int(11) DEFAULT NULL,
  `vendor_id` int(11) DEFAULT NULL,
  `description` text,
  `payment_type` int(11) NOT NULL,
  `voucher_number` varchar(100) NOT NULL,
  `vendor_bill_no` varchar(100) DEFAULT NULL,
  `vendor_other` varchar(100) DEFAULT NULL,
  `bank_name` varchar(100) DEFAULT NULL,
  `cheque_number` varchar(100) DEFAULT NULL,
  `bank_date` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `expense_reject_comments`
--

DROP TABLE IF EXISTS `expense_reject_comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `expense_reject_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `expense_id` int(11) DEFAULT NULL,
  `comments` text,
  `status` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `expense_sub_category`
--

DROP TABLE IF EXISTS `expense_sub_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `expense_sub_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sub_category` varchar(100) DEFAULT NULL,
  `cat_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `expense_sub_category_tx`
--

DROP TABLE IF EXISTS `expense_sub_category_tx`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `expense_sub_category_tx` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sub_cat_id` int(11) NOT NULL,
  `expense_master_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_blueprint`
--

DROP TABLE IF EXISTS `feev2_blueprint`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_blueprint` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `filter_blueprint` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `receipt_gen_algo` varchar(45) NOT NULL,
  `allowed_payment_modes` text NOT NULL,
  `concession_mode` varchar(45) DEFAULT 'none' COMMENT '''percentage'',''amount''',
  `concession_algo` varchar(100) DEFAULT 'none' COMMENT '''none''',
  `enable_custom_fee` tinyint(4) NOT NULL,
  `enable_fee_cohort_check` tinyint(4) NOT NULL,
  `acad_year_id` int(11) NOT NULL,
  `is_acad_fee` tinyint(4) NOT NULL DEFAULT '0',
  `is_split` tinyint(4) NOT NULL DEFAULT '0',
  `display_fields` text,
  `select_filter` text NOT NULL,
  `select_columns` text NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `receipt_book_id` int(11) DEFAULT NULL,
  `receipt_for` varchar(100) NOT NULL,
  `enable_sms` tinyint(4) NOT NULL DEFAULT '0',
  `consolidated_receipt_html` text,
  `terms_conditions` text,
  `branches` int(11) DEFAULT '0',
  `pre_condition_blueprint` tinyint(1) DEFAULT '0',
  `transport_selection` tinyint(4) DEFAULT NULL,
  `message` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_blueprint_components`
--

DROP TABLE IF EXISTS `feev2_blueprint_components`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_blueprint_components` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feev2_blueprint_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `vendor_code` varchar(100) DEFAULT NULL,
  `is_concession_eligible` tinyint(1) NOT NULL DEFAULT '1',
  `enable_if_partial` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_blueprint_installment_types`
--

DROP TABLE IF EXISTS `feev2_blueprint_installment_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_blueprint_installment_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feev2_installment_type_id` int(11) NOT NULL,
  `feev2_blueprint_id` int(11) NOT NULL,
  `discount_algo` varchar(100) NOT NULL,
  `discount_amount` varchar(100) DEFAULT NULL,
  `allow_partial` int(11) DEFAULT '0' COMMENT '''0 or 1''',
  `allocation_algo` varchar(45) NOT NULL DEFAULT 'none' COMMENT '''equal'',''custom''',
  `allocation_params` varchar(45) DEFAULT NULL,
  `fine_amount_algo` varchar(45) NOT NULL DEFAULT 'none' COMMENT '''fine_per_day'',''fine_per_month''',
  `fine_amount_params` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_cohort_installment_components`
--

DROP TABLE IF EXISTS `feev2_cohort_installment_components`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_cohort_installment_components` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feev2_cohort_id` int(11) NOT NULL,
  `feev2_blueprint_installment_types_id` int(11) NOT NULL,
  `feev2_installment_id` int(11) NOT NULL,
  `feev2_blueprint_component_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_cohort_student`
--

DROP TABLE IF EXISTS `feev2_cohort_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_cohort_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `blueprint_id` int(11) NOT NULL,
  `feev2_cohort_id` int(11) NOT NULL,
  `publish_status` varchar(100) NOT NULL DEFAULT 'NOT_PUBLISHED' COMMENT 'NOT_PUBLISHED, PUBLISHED',
  `fee_cohort_status` varchar(100) NOT NULL DEFAULT 'STANDARD' COMMENT '''STANDARD'', ''CUSTOM''',
  `fee_collect_status` varchar(45) DEFAULT 'NOT_STARTED' COMMENT 'NOT_STARTED, STARTED',
  `online_payment` varchar(100) DEFAULT 'NOT_PUBLISHED',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_cohorts`
--

DROP TABLE IF EXISTS `feev2_cohorts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_cohorts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filter` text NOT NULL,
  `total_fee` decimal(10,2) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL COMMENT 'This is the avatar id that last modified this table',
  `acad_year_id` int(11) NOT NULL,
  `blueprint_id` int(11) NOT NULL,
  `default_ins` int(11) NOT NULL,
  `friendly_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_concessions`
--

DROP TABLE IF EXISTS `feev2_concessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_concessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cohort_student_id` int(11) NOT NULL,
  `concession_by` int(11) NOT NULL,
  `concession_name` varchar(255) NOT NULL,
  `is_applied` tinyint(4) NOT NULL,
  `transaction_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_concessions_installment_components`
--

DROP TABLE IF EXISTS `feev2_concessions_installment_components`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_concessions_installment_components` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feev2_concession_id` int(11) NOT NULL,
  `feev2_blueprint_installment_types_id` int(11) NOT NULL,
  `feev2_installments_id` int(11) NOT NULL,
  `feev2_blueprint_components_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_installment_types`
--

DROP TABLE IF EXISTS `feev2_installment_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_installment_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_installments`
--

DROP TABLE IF EXISTS `feev2_installments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_installments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `end_date` date NOT NULL,
  `start_date` date NOT NULL,
  `feev2_installment_type_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_km`
--

DROP TABLE IF EXISTS `feev2_km`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_km` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kilometer` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_receipt_book`
--

DROP TABLE IF EXISTS `feev2_receipt_book`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_receipt_book` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_format` varchar(100) NOT NULL,
  `infix` varchar(100) NOT NULL,
  `digit_count` int(11) NOT NULL,
  `running_number` int(11) NOT NULL,
  `year` varchar(100) DEFAULT NULL,
  `gst_no` varchar(150) DEFAULT NULL,
  `pan_no` varchar(150) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_receipt_template`
--

DROP TABLE IF EXISTS `feev2_receipt_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_receipt_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template` longtext NOT NULL,
  `blueprint_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_refund_transactions`
--

DROP TABLE IF EXISTS `feev2_refund_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_refund_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trans_id` int(11) DEFAULT NULL,
  `student_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `blueprint_id` int(11) DEFAULT NULL,
  `payment_type` varchar(45) DEFAULT NULL,
  `refund_date` date DEFAULT NULL,
  `bank_name` varchar(100) DEFAULT NULL,
  `bank_branch` varchar(100) DEFAULT NULL,
  `cheque_or_dd_date` date DEFAULT NULL,
  `remarks` varchar(255) DEFAULT NULL,
  `cheque_dd_nb_cc_dd_number` varchar(255) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_stops`
--

DROP TABLE IF EXISTS `feev2_stops`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_stops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `kilometer` varchar(100) DEFAULT NULL,
  `route` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_student_installments`
--

DROP TABLE IF EXISTS `feev2_student_installments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_student_installments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_student_schedule_id` int(11) NOT NULL,
  `feev2_installments_id` int(11) NOT NULL,
  `installment_amount` decimal(10,2) NOT NULL,
  `installment_amount_paid` decimal(10,2) DEFAULT NULL,
  `status` varchar(100) NOT NULL DEFAULT 'Not Started' COMMENT 'Not Started, Full, Partial',
  `total_concession_amount` decimal(10,2) DEFAULT NULL,
  `total_concession_amount_paid` decimal(10,2) DEFAULT NULL,
  `fine_amount` decimal(10,2) DEFAULT NULL,
  `refund_amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_student_installments_components`
--

DROP TABLE IF EXISTS `feev2_student_installments_components`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_student_installments_components` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_student_installment_id` int(11) NOT NULL,
  `blueprint_component_id` int(11) NOT NULL,
  `component_amount` decimal(10,2) NOT NULL,
  `component_amount_paid` decimal(10,2) DEFAULT NULL,
  `concession_amount` decimal(10,2) DEFAULT NULL,
  `concession_amount_paid` decimal(10,2) DEFAULT NULL,
  `refund_amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_student_schedule`
--

DROP TABLE IF EXISTS `feev2_student_schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_student_schedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feev2_cohort_student_id` int(11) NOT NULL,
  `total_fee` decimal(10,2) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) DEFAULT NULL,
  `approval_date` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `approved_by` int(11) DEFAULT NULL,
  `total_fee_paid` decimal(10,2) DEFAULT NULL,
  `payment_status` varchar(100) NOT NULL COMMENT 'Full, Partial, Not started',
  `discount` decimal(10,2) DEFAULT NULL,
  `total_concession_amount` decimal(10,2) DEFAULT NULL,
  `total_concession_amount_paid` decimal(10,2) DEFAULT NULL,
  `total_fine_amount` decimal(10,2) DEFAULT NULL,
  `total_card_charge_amount` decimal(10,2) DEFAULT NULL,
  `carry_over_total_amount` decimal(10,2) DEFAULT NULL,
  `carry_over_concession` decimal(10,2) DEFAULT NULL,
  `carry_over_amount_paid` decimal(10,2) DEFAULT NULL,
  `carry_over_status` varchar(100) DEFAULT 'Not Paid' COMMENT 'Not Paid, Paid',
  `acad_year_id` int(11) NOT NULL,
  `feev2_blueprint_installment_types_id` int(11) NOT NULL,
  `consolidated_fee_html` text,
  `consolidated_fee_pdf_path` varchar(255) DEFAULT NULL,
  `pdf_status` varchar(100) DEFAULT NULL,
  `refund_amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_transaction`
--

DROP TABLE IF EXISTS `feev2_transaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `fee_student_schedule_id` int(11) NOT NULL,
  `amount_paid` decimal(10,2) NOT NULL,
  `fine_amount` decimal(10,2) DEFAULT NULL,
  `discount_amount` decimal(10,2) DEFAULT NULL,
  `paid_datetime` datetime NOT NULL,
  `collected_by` int(11) NOT NULL,
  `soft_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '1: soft delete ',
  `concession_amount` decimal(10,2) NOT NULL,
  `transaction_mode` varchar(100) NOT NULL COMMENT 'counter, online, via link',
  `receipt_pdf_link` varchar(255) NOT NULL,
  `receipt_number` varchar(255) NOT NULL,
  `card_charge_amount` decimal(10,2) DEFAULT NULL,
  `acad_year_id` varchar(100) NOT NULL,
  `status` varchar(45) NOT NULL DEFAULT 'INITIATED',
  `op_recon_status` varchar(100) DEFAULT NULL,
  `receipt_html` longtext NOT NULL,
  `pdf_status` tinyint(4) NOT NULL,
  `pdf_status_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `refund_amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_transaction_installment_component`
--

DROP TABLE IF EXISTS `feev2_transaction_installment_component`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_transaction_installment_component` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_transaction_id` int(11) NOT NULL,
  `blueprint_component_id` int(11) NOT NULL,
  `blueprint_installments_id` int(11) NOT NULL,
  `amount_paid` decimal(10,2) NOT NULL,
  `concession_amount` decimal(10,2) NOT NULL,
  `fee_student_installments_components_id` int(11) NOT NULL,
  `fee_student_installments_id` int(11) NOT NULL,
  `refund_amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `feev2_transaction_payment`
--

DROP TABLE IF EXISTS `feev2_transaction_payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feev2_transaction_payment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_transaction_id` int(11) NOT NULL,
  `payment_type` varchar(100) NOT NULL COMMENT 'dd, cash, cheque, cc, dc, payment gateway',
  `bank_name` varchar(255) DEFAULT NULL,
  `bank_branch` varchar(255) DEFAULT NULL,
  `cheque_or_dd_date` date DEFAULT NULL,
  `card_reference_number` varchar(255) DEFAULT NULL COMMENT 'Used for credit and debit cards',
  `reconciliation_status` tinyint(4) NOT NULL COMMENT '0:not required, 1:pending, 2:completed',
  `recon_created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `recon_lastmodified_by` int(11) NOT NULL,
  `recon_submitted_on` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `remarks` varchar(255) DEFAULT NULL,
  `cheque_dd_nb_cc_dd_number` varchar(255) DEFAULT NULL,
  `payment_gateway_info` varchar(255) DEFAULT NULL,
  `canceled_remarks` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flash_master`
--

DROP TABLE IF EXISTS `flash_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `flash_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `flash_content` varchar(256) NOT NULL,
  `user_type` tinyint(4) NOT NULL COMMENT '0 - Student All, 1 - Staff All, 2 - Class',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 - Active, 1 - Inactive, 2 - Template',
  `published_by` int(11) NOT NULL,
  `published_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `send_to` text NOT NULL,
  `enforce_reading` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `follow_up`
--

DROP TABLE IF EXISTS `follow_up`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `follow_up` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `follow_up_type` varchar(100) NOT NULL DEFAULT 'Enquiry',
  `follow_up_action` varchar(100) NOT NULL DEFAULT 'Phone-call',
  `source_id` int(11) NOT NULL,
  `email_subject` varchar(255) DEFAULT NULL,
  `email_ids` varchar(255) DEFAULT NULL,
  `template_name` varchar(100) DEFAULT NULL,
  `template_content` text,
  `status` varchar(100) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `remarks` text NOT NULL,
  `delivery_status` varchar(100) DEFAULT NULL,
  `next_follow_date` date DEFAULT NULL,
  `registered_email` varchar(100) DEFAULT NULL,
  `closure_reason` varchar(255) DEFAULT NULL,
  `lead_status` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `gallery_images_master`
--

DROP TABLE IF EXISTS `gallery_images_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gallery_images_master` (
  `image_id` int(11) NOT NULL AUTO_INCREMENT,
  `gallery_id` int(11) NOT NULL,
  `image_name` varchar(255) NOT NULL,
  `image_description` text NOT NULL,
  `image_tags` text NOT NULL,
  `image_status` tinyint(4) NOT NULL DEFAULT '1',
  PRIMARY KEY (`image_id`),
  KEY `galary_id` (`gallery_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `gallery_list`
--

DROP TABLE IF EXISTS `gallery_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gallery_list` (
  `gallery_id` int(11) NOT NULL AUTO_INCREMENT,
  `gallery_name` varchar(255) NOT NULL,
  `gallery_description` text NOT NULL,
  `gallery_date` date NOT NULL,
  `gallery_location` varchar(255) NOT NULL,
  `gallery_visibility` tinyint(1) NOT NULL COMMENT '1 for only me, 2 for only staff, 3 for parents and staff and four for public',
  `gallery_status` int(11) NOT NULL DEFAULT '1' COMMENT '0 for deleted and 1 for active',
  `created_by` int(11) NOT NULL DEFAULT '1',
  `gallery_visibility_class` longtext,
  `publish_status` tinyint(4) NOT NULL DEFAULT '1',
  PRIMARY KEY (`gallery_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `gallery_tags`
--

DROP TABLE IF EXISTS `gallery_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gallery_tags` (
  `galary_tag_id` int(11) NOT NULL AUTO_INCREMENT,
  `galary_tag_name` text NOT NULL,
  PRIMARY KEY (`galary_tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `homework`
--

DROP TABLE IF EXISTS `homework`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `homework` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `section_id` int(11) NOT NULL,
  `created_date` datetime NOT NULL,
  `image` mediumtext,
  `body` longblob,
  `status` tinyint(1) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `acad_year_id` int(11) NOT NULL,
  `is_image_json` tinyint(4) NOT NULL DEFAULT '0',
  `expect_submissions` tinyint(4) NOT NULL DEFAULT '0',
  `submission_date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `homework_sent_to`
--

DROP TABLE IF EXISTS `homework_sent_to`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `homework_sent_to` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `homework_id` int(11) NOT NULL,
  `stakeholder_id` int(11) NOT NULL,
  `student_id` int(11) DEFAULT NULL,
  `is_read` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `homework_submissions`
--

DROP TABLE IF EXISTS `homework_submissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `homework_submissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `homework_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `file` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `description` mediumtext,
  `remarks` mediumtext,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_checked` tinyint(4) NOT NULL DEFAULT '0',
  `checked_by` int(11) DEFAULT NULL COMMENT 'Id of staff who checked the homework',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `idcard_order_stakeholders`
--

DROP TABLE IF EXISTS `idcard_order_stakeholders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `idcard_order_stakeholders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idcard_order_id` int(11) DEFAULT NULL,
  `stakeholder_id` varchar(45) DEFAULT NULL,
  `approved_by` varchar(45) DEFAULT NULL,
  `approved_on` varchar(45) DEFAULT NULL,
  `status` varchar(45) DEFAULT NULL COMMENT '1 for approved and 2 for not approved',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `idcard_orders`
--

DROP TABLE IF EXISTS `idcard_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `idcard_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_name` varchar(120) DEFAULT NULL,
  `idcard_type` varchar(15) DEFAULT NULL COMMENT '1 for staff/ 2 for student',
  `created_by` varchar(45) DEFAULT NULL,
  `created_on` varchar(45) DEFAULT NULL,
  `order_placed_by` varchar(45) DEFAULT NULL,
  `order_placed_on` varchar(45) DEFAULT NULL,
  `status` int(11) DEFAULT NULL COMMENT '1 for order placed,2 for order not placed and 3 for order deleted',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `idcard_templates`
--

DROP TABLE IF EXISTS `idcard_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `idcard_templates` (
  `template_html` longtext,
  `template_type` varchar(15) DEFAULT NULL COMMENT '1 for staff and 2 for staff',
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_invoice_items`
--

DROP TABLE IF EXISTS `inventory_invoice_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_invoice_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_master_id` int(11) NOT NULL,
  `variant_id` int(11) NOT NULL,
  `variant_type` char(50) NOT NULL,
  `description` text,
  `hsn_sac_no` varchar(255) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `unit` varchar(50) DEFAULT NULL,
  `price` float DEFAULT NULL,
  `cgst` decimal(5,2) DEFAULT NULL,
  `sgst` decimal(5,2) DEFAULT NULL,
  `total_amount` float DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_invoice_master`
--

DROP TABLE IF EXISTS `inventory_invoice_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_invoice_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `invoice_no` varchar(255) DEFAULT NULL,
  `bill_no` varchar(255) DEFAULT NULL,
  `invoice_date` date DEFAULT NULL,
  `delivery_note` text,
  `mode_of_payment` varchar(100) DEFAULT NULL,
  `supplier_ref_no` varchar(100) DEFAULT NULL,
  `order_no` varchar(100) DEFAULT NULL,
  `order_date` date DEFAULT NULL,
  `dispatch_doc_no` varchar(100) DEFAULT NULL,
  `delivery_note_date` date DEFAULT NULL,
  `total_amount` float DEFAULT NULL,
  `payment_instrument_id` int(11) DEFAULT NULL,
  `dispatched_by` varchar(255) DEFAULT NULL,
  `destination` varchar(255) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_payment_instruments`
--

DROP TABLE IF EXISTS `inventory_payment_instruments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_payment_instruments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `vendor_id` int(11) NOT NULL,
  `payment_type` char(20) NOT NULL,
  `bank_name` varchar(255) DEFAULT NULL,
  `ifsc_code` varchar(50) DEFAULT NULL,
  `branch` varchar(100) DEFAULT NULL,
  `cheque_in_favor_of` varchar(255) DEFAULT NULL,
  `last_modified_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `account_number` int(11) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_product_category`
--

DROP TABLE IF EXISTS `inventory_product_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_product_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(100) NOT NULL,
  `receipt_book_id` int(11) NOT NULL,
  `receipt_template` longtext,
  `is_sellable` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_product_master`
--

DROP TABLE IF EXISTS `inventory_product_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_product_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_code` varchar(255) NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `picture_url` varchar(255) DEFAULT NULL,
  `description` text,
  `attributes` text,
  `is_stockable` tinyint(1) NOT NULL,
  `category_id` int(11) NOT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `modified_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) DEFAULT NULL,
  `unit_type` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_product_variant`
--

DROP TABLE IF EXISTS `inventory_product_variant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_product_variant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(256) NOT NULL,
  `attributes` text NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `initial_quantity` int(11) NOT NULL,
  `threshold_quantity` int(11) NOT NULL,
  `current_quantity` int(11) NOT NULL,
  `total_quantity` int(11) NOT NULL,
  `cost_prodcut` decimal(10,2) NOT NULL,
  `selling_price` decimal(10,2) NOT NULL,
  `hsn_sac` varchar(100) DEFAULT NULL,
  `vendor_code` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_product_variant_staff`
--

DROP TABLE IF EXISTS `inventory_product_variant_staff`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_product_variant_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `collected_by` int(11) NOT NULL,
  `variant_id` int(11) NOT NULL,
  `allocated_quantity` int(11) NOT NULL,
  `given_by` int(11) NOT NULL,
  `given_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `purpose` varchar(255) DEFAULT NULL,
  `allocate_date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_purchase_order`
--

DROP TABLE IF EXISTS `inventory_purchase_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_purchase_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `current_status` varchar(100) NOT NULL COMMENT 'created, approved, not-approved, need-correction',
  `description` text NOT NULL,
  `po_number` varchar(100) NOT NULL,
  `category_id` int(11) NOT NULL,
  `vendor_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_purchase_order_items`
--

DROP TABLE IF EXISTS `inventory_purchase_order_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_purchase_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `description` text NOT NULL,
  `quantity` int(11) NOT NULL,
  `variant_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_purchase_status`
--

DROP TABLE IF EXISTS `inventory_purchase_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_purchase_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `status` varchar(20) NOT NULL COMMENT 'created, approved, not-approved, need-correction',
  `status_by` int(11) NOT NULL,
  `remarks` text NOT NULL,
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_vendor_address_info`
--

DROP TABLE IF EXISTS `inventory_vendor_address_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_vendor_address_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `address_type` tinyint(4) NOT NULL,
  `address_line1` varchar(255) NOT NULL,
  `address_line2` varchar(255) NOT NULL,
  `area` varchar(100) NOT NULL,
  `district` varchar(100) NOT NULL,
  `state` varchar(100) NOT NULL,
  `country` varchar(100) NOT NULL,
  `pin_code` varchar(10) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_vendor_master`
--

DROP TABLE IF EXISTS `inventory_vendor_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_vendor_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_code` varchar(100) NOT NULL,
  `vendor_name` varchar(255) NOT NULL,
  `gst_no` varchar(255) DEFAULT NULL,
  `vendor_email` varchar(100) DEFAULT NULL,
  `vendor_website` varchar(255) DEFAULT NULL,
  `contact_first_name` varchar(255) NOT NULL,
  `contact_last_name` varchar(255) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `on_board` date NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `contact_number` varchar(20) NOT NULL,
  `customer_service_number` varchar(20) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_vendor_observations`
--

DROP TABLE IF EXISTS `inventory_vendor_observations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_vendor_observations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `observation` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `rating` int(5) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_vendor_products`
--

DROP TABLE IF EXISTS `inventory_vendor_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_vendor_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `iot_test_data`
--

DROP TABLE IF EXISTS `iot_test_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `iot_test_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `payload` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `item`
--

DROP TABLE IF EXISTS `item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_name` varchar(100) NOT NULL,
  `default_price` decimal(10,2) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `item_type` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `leave_staff`
--

DROP TABLE IF EXISTS `leave_staff`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `leave_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `leave_type` varchar(100) NOT NULL,
  `request_date` date NOT NULL,
  `from_date` date NOT NULL,
  `to_date` date NOT NULL,
  `noofdays` decimal(10,2) NOT NULL,
  `reason` text NOT NULL,
  `status` tinyint(1) NOT NULL COMMENT '0: Pending, 1: Approved, 2: Auto Approved, 3: Rejected',
  `description` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `leave_for` char(10) DEFAULT NULL,
  `leave_filed_by` int(11) NOT NULL,
  `leave_approved_by` int(11) DEFAULT NULL,
  `leave_staffcol` varchar(45) DEFAULT NULL,
  `substitution_added` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `leave_staff_substitute`
--

DROP TABLE IF EXISTS `leave_staff_substitute`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `leave_staff_substitute` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leave_id` int(11) NOT NULL,
  `staff_id` int(11) DEFAULT NULL,
  `substitute_date` date NOT NULL,
  `from_period` tinyint(1) DEFAULT NULL,
  `to_period` tinyint(1) DEFAULT NULL,
  `substitution_added` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `leave_student`
--

DROP TABLE IF EXISTS `leave_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `leave_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `leave_type` varchar(100) NOT NULL,
  `request_date` date NOT NULL,
  `from_date` date NOT NULL,
  `to_date` date NOT NULL,
  `reason` text NOT NULL,
  `doctor_certificate` varchar(255) DEFAULT NULL,
  `status` varchar(100) NOT NULL,
  `noofdays` int(11) NOT NULL,
  `description` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `leave_filed_by` int(11) NOT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  `leave_approved_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `libraries`
--

DROP TABLE IF EXISTS `libraries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `libraries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_books`
--

DROP TABLE IF EXISTS `library_books`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_books` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `book_type` varchar(50) NOT NULL,
  `category` varchar(100) NOT NULL,
  `language` varchar(100) NOT NULL,
  `author` varchar(100) NOT NULL,
  `yearof_publishing` varchar(50) NOT NULL,
  `publisher_name` varchar(50) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` text NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `book_title` varchar(100) NOT NULL,
  `series` varchar(100) DEFAULT NULL,
  `volume` varchar(100) DEFAULT NULL,
  `subject` varchar(100) DEFAULT NULL,
  `location_book` varchar(100) DEFAULT NULL,
  `source` varchar(100) DEFAULT NULL,
  `file_url` varchar(100) DEFAULT NULL,
  `contains` varchar(100) DEFAULT NULL,
  `edition` varchar(255) DEFAULT NULL,
  `acc_no` varchar(255) DEFAULT NULL,
  `isbn` varchar(255) DEFAULT NULL,
  `pages` varchar(255) DEFAULT NULL,
  `call_number` varchar(255) DEFAULT NULL,
  `supplier` varchar(255) DEFAULT NULL,
  `bill_no_date` varchar(255) DEFAULT NULL,
  `soft_delete` int(11) DEFAULT '0',
  `libraries` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_books_copies`
--

DROP TABLE IF EXISTS `library_books_copies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_books_copies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `book_id` int(11) NOT NULL,
  `access_code` varchar(100) DEFAULT NULL,
  `costof_book` varchar(100) DEFAULT NULL,
  `date_of_accession` date DEFAULT NULL,
  `remarks` varchar(255) DEFAULT NULL,
  `discarded_date` date DEFAULT NULL,
  `status` varchar(45) DEFAULT 'Available',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_cards`
--

DROP TABLE IF EXISTS `library_cards`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_cards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stake_holder_id` int(11) NOT NULL,
  `stake_holder_type` varchar(50) NOT NULL,
  `status` tinyint(4) NOT NULL COMMENT '1: Active , 2: In-active',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `master_id` int(11) NOT NULL,
  `card_access_code` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_fine_transaction`
--

DROP TABLE IF EXISTS `library_fine_transaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_fine_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trans_id` int(11) DEFAULT NULL,
  `identification_code` varchar(100) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `remarks` varchar(255) DEFAULT NULL,
  `payment_type` tinyint(4) DEFAULT NULL COMMENT 'config',
  `transaction_mode` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_master`
--

DROP TABLE IF EXISTS `library_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_type` varchar(50) NOT NULL,
  `hold_period` int(11) NOT NULL,
  `fine_per_day` decimal(10,2) NOT NULL,
  `num_books` tinyint(4) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `book_type` varchar(250) NOT NULL,
  `display_color` varchar(100) DEFAULT NULL,
  `libraries` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_npsrnr_books_raw_data`
--

DROP TABLE IF EXISTS `library_npsrnr_books_raw_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_npsrnr_books_raw_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `book_name` varchar(255) NOT NULL,
  `author` varchar(255) NOT NULL,
  `price` varchar(255) NOT NULL,
  `discount` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `publisher` varchar(255) NOT NULL,
  `published_year` varchar(255) NOT NULL,
  `book_arrival` date DEFAULT NULL,
  `subject_category` varchar(255) NOT NULL,
  `language` varchar(255) NOT NULL,
  `source` varchar(255) NOT NULL,
  `loanable` varchar(255) NOT NULL,
  `media_type` varchar(255) NOT NULL,
  `book_category` varchar(255) NOT NULL,
  `library` varchar(255) NOT NULL,
  `barcode` varchar(255) NOT NULL,
  `due_back_date` date DEFAULT NULL,
  `circulation_status` varchar(255) NOT NULL,
  `circulation_reason` varchar(255) NOT NULL,
  `discarded_date_Year` date DEFAULT NULL,
  `remarks` varchar(255) NOT NULL,
  `currency` varchar(255) NOT NULL,
  `value_in_currency` varchar(255) NOT NULL,
  `exchange_rate` varchar(255) NOT NULL,
  `cost_in_rupee` varchar(255) NOT NULL,
  `discount_amount` varchar(255) NOT NULL,
  `discount_price` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL,
  `class_section` varchar(255) NOT NULL,
  `checked_out_by` varchar(255) NOT NULL,
  `book_in_queue` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_stock_check_books`
--

DROP TABLE IF EXISTS `library_stock_check_books`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_stock_check_books` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `access_code` varchar(255) NOT NULL,
  `check_id` int(11) NOT NULL,
  `checked_by` int(11) NOT NULL,
  `checked_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_stock_check_master`
--

DROP TABLE IF EXISTS `library_stock_check_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_stock_check_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(55) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `library_transacation`
--

DROP TABLE IF EXISTS `library_transacation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `library_transacation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `issue_date` date NOT NULL,
  `status` tinyint(4) NOT NULL COMMENT '1:issue, 2: return',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `return_date` date DEFAULT NULL,
  `book_access_id` int(11) NOT NULL,
  `lbr_access_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `list_of_holidays`
--

DROP TABLE IF EXISTS `list_of_holidays`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `list_of_holidays` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `type` varchar(11) NOT NULL,
  `holiday_date` date NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `login_attempts`
--

DROP TABLE IF EXISTS `login_attempts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `login_attempts` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `login` varchar(100) NOT NULL,
  `time` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `logs`
--

DROP TABLE IF EXISTS `logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `errno` int(2) NOT NULL,
  `errtype` varchar(32) NOT NULL,
  `errstr` text NOT NULL,
  `errfile` varchar(255) NOT NULL,
  `errline` int(4) NOT NULL,
  `user_agent` varchar(120) NOT NULL,
  `ip_address` varchar(45) NOT NULL DEFAULT '0',
  `time` datetime NOT NULL,
  PRIMARY KEY (`id`,`ip_address`,`user_agent`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_assessment`
--

DROP TABLE IF EXISTS `lp_assessment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_assessment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` mediumtext NOT NULL,
  `lp_subject_id` int(11) NOT NULL,
  `lp_class_name` varchar(45) NOT NULL,
  `total_points` double DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0: Inactive, 1: Active',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `is_ready` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_assessment_answers`
--

DROP TABLE IF EXISTS `lp_assessment_answers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_assessment_answers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lp_task_student_id` int(11) NOT NULL,
  `lp_question_id` int(11) NOT NULL,
  `answer` text,
  `status` tinyint(4) DEFAULT NULL COMMENT '0: Incorrect,\n1: Correct',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_assessment_questions`
--

DROP TABLE IF EXISTS `lp_assessment_questions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_assessment_questions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lp_question_id` int(11) NOT NULL,
  `lp_assessment_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_lessons`
--

DROP TABLE IF EXISTS `lp_lessons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_lessons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lp_subject_id` int(11) NOT NULL,
  `lesson_name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_objectives`
--

DROP TABLE IF EXISTS `lp_objectives`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_objectives` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `objective_name` varchar(45) NOT NULL,
  `objective_description` mediumtext,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_questions`
--

DROP TABLE IF EXISTS `lp_questions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_questions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lp_sub_topic_id` int(11) NOT NULL,
  `question` text NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `question_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1: Multiple Choice\n2: Fill in the blank\n3: One Liner',
  `options` mediumtext,
  `answer` mediumtext,
  `points` double DEFAULT NULL,
  `explaination` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_scheme`
--

DROP TABLE IF EXISTS `lp_scheme`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_scheme` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `scheme_name` varchar(255) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `grade` varchar(45) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_skills`
--

DROP TABLE IF EXISTS `lp_skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_skills` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `skill_name` varchar(45) NOT NULL,
  `skill_description` mediumtext,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_student_rewards`
--

DROP TABLE IF EXISTS `lp_student_rewards`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_student_rewards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `source` varchar(255) NOT NULL COMMENT 'Formative Assessment, Decipline, etc',
  `secured_points` double NOT NULL,
  `total_points` double NOT NULL,
  `given_by` int(11) NOT NULL COMMENT 'staff_id',
  `given_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_sub_topics`
--

DROP TABLE IF EXISTS `lp_sub_topics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_sub_topics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lp_lesson_id` int(11) NOT NULL,
  `sub_topic_name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_subjects`
--

DROP TABLE IF EXISTS `lp_subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_name` varchar(255) NOT NULL,
  `class_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_tasks`
--

DROP TABLE IF EXISTS `lp_tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_tasks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `class_section_id` text,
  `subject_id` int(11) DEFAULT NULL,
  `task_name` varchar(255) DEFAULT NULL,
  `task_description` text,
  `task_type` varchar(45) DEFAULT NULL COMMENT 'reading,writing,viewing',
  `task_last_date` date DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(45) DEFAULT NULL COMMENT 'published, disabled',
  `require_evaluation` tinyint(4) DEFAULT NULL,
  `disabled_by` int(11) DEFAULT NULL,
  `disabled_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `resource_ids` text,
  `download_status` tinyint(4) DEFAULT NULL,
  `lp_sub_topic_id` int(11) DEFAULT NULL,
  `lp_assessment_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_tasks_students`
--

DROP TABLE IF EXISTS `lp_tasks_students`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_tasks_students` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lp_tasks_id` int(11) DEFAULT NULL,
  `student_id` int(11) NOT NULL,
  `submission_files_path` text,
  `read_status` varchar(45) DEFAULT 'unread' COMMENT 'read,unread',
  `submission_status` tinyint(4) DEFAULT '0' COMMENT '0 for not submitted and 1 for submitted',
  `evaluation_status` tinyint(4) DEFAULT '0',
  `evaluation_comments` text,
  `class_section_id` int(11) DEFAULT NULL,
  `submission_on` timestamp NULL DEFAULT NULL,
  `evaluation_on` timestamp NULL DEFAULT NULL,
  `evaluation_files` text,
  `assessment_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: Not attended,\n1: Attended',
  PRIMARY KEY (`id`,`student_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lp_tasks_students_submission_files`
--

DROP TABLE IF EXISTS `lp_tasks_students_submission_files`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lp_tasks_students_submission_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lp_tasks_students_id` int(11) NOT NULL,
  `file_path` text,
  `file_order` int(11) DEFAULT NULL,
  `type` varchar(45) DEFAULT NULL,
  `file_name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `management_report_templates`
--

DROP TABLE IF EXISTS `management_report_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `management_report_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) NOT NULL,
  `from_email` varchar(255) NOT NULL,
  `members` text,
  `email_subject` varchar(255) NOT NULL,
  `email_template` text,
  `interval` tinyint(4) NOT NULL,
  `execution_time` time DEFAULT NULL,
  `last_executed_time` datetime DEFAULT NULL,
  `status` tinyint(4) DEFAULT NULL COMMENT '0:inactive, 1:active',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `multisection_subjects`
--

DROP TABLE IF EXISTS `multisection_subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `multisection_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(100) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `myt`
--

DROP TABLE IF EXISTS `myt`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `myt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `s_admission_no` varchar(100) DEFAULT NULL,
  `s_roll_no` int(5) DEFAULT NULL,
  `s_first_name` varchar(100) DEFAULT NULL,
  `s_last_name` varchar(100) DEFAULT NULL,
  `s_class` varchar(20) DEFAULT NULL,
  `s_class_section` varchar(20) DEFAULT NULL,
  `s_cources` varchar(20) DEFAULT NULL,
  `s_boarding` varchar(100) DEFAULT NULL,
  `s_board` varchar(100) DEFAULT NULL,
  `s_medium` varchar(100) DEFAULT NULL,
  `s_donor` varchar(100) DEFAULT NULL,
  `s_student_house` varchar(100) DEFAULT NULL,
  `s_dob` varchar(100) DEFAULT NULL,
  `s_gender` varchar(100) DEFAULT NULL,
  `s_nationality` varchar(100) DEFAULT NULL,
  `s_religion` varchar(100) DEFAULT NULL,
  `s_category` varchar(100) DEFAULT NULL,
  `s_mother_tongue` varchar(100) DEFAULT NULL,
  `s_contact_no` varchar(100) DEFAULT NULL,
  `s_is_rte` varchar(100) DEFAULT NULL,
  `s_date_of_joining` varchar(100) DEFAULT NULL,
  `s_birth_taluk` varchar(100) DEFAULT NULL,
  `s_birth_district` varchar(100) DEFAULT NULL,
  `s_caste` varchar(100) DEFAULT NULL,
  `s_aadhar_no` varchar(100) DEFAULT NULL,
  `s_class_admitted_to` varchar(100) DEFAULT NULL,
  `s_admission_year` varchar(100) DEFAULT NULL,
  `s_admission_acad_year` varchar(100) DEFAULT NULL,
  `s_emergency_info` varchar(255) DEFAULT NULL,
  `s_email` varchar(100) DEFAULT NULL,
  `f_first_name` varchar(255) DEFAULT NULL,
  `f_last_name` varchar(255) DEFAULT NULL,
  `f_qualification` varchar(255) DEFAULT NULL,
  `f_occupation` varchar(255) DEFAULT NULL,
  `f_mobile_no` varchar(100) DEFAULT NULL,
  `f_aadhar_no` varchar(100) DEFAULT NULL,
  `f_annual_income` varchar(100) DEFAULT NULL,
  `f_company` varchar(255) DEFAULT NULL,
  `f_mother_tongue` varchar(255) DEFAULT NULL,
  `f_email` varchar(100) DEFAULT NULL,
  `m_first_name` varchar(255) DEFAULT NULL,
  `m_last_name` varchar(255) DEFAULT NULL,
  `m_qualification` varchar(255) DEFAULT NULL,
  `m_occupation` varchar(255) DEFAULT NULL,
  `m_mobile_no` varchar(100) DEFAULT NULL,
  `m_aadhar_no` varchar(100) DEFAULT NULL,
  `m_annual_income` varchar(100) DEFAULT NULL,
  `m_company` varchar(255) DEFAULT NULL,
  `m_mother_tongue` varchar(255) DEFAULT NULL,
  `m_email` varchar(100) DEFAULT NULL,
  `s_address1` text,
  `s_address2` text,
  `s_blood_group` varchar(20) DEFAULT NULL,
  `f_home_address` text,
  `f_office_address` text,
  `m_home_address` text,
  `m_office_address` text,
  `s_sts_number` text,
  `life_time_fee_mode` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_advance`
--

DROP TABLE IF EXISTS `new_payroll_advance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_advance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `despersement_method` int(11) DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `amount_per_month` int(11) DEFAULT NULL,
  `paid_advance` int(11) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_arrears`
--

DROP TABLE IF EXISTS `new_payroll_arrears`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_arrears` (
  `id` int(11) NOT NULL,
  `accumlated_payslip_id` int(11) DEFAULT NULL,
  `paidoff_payslip_id` int(11) DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_bonus`
--

DROP TABLE IF EXISTS `new_payroll_bonus`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_bonus` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `schedule_id` int(11) DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `reason` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_disbursement`
--

DROP TABLE IF EXISTS `new_payroll_disbursement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_disbursement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` int(11) NOT NULL,
  `status` varchar(100) NOT NULL,
  `description` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `modified_by` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_disbursement_list`
--

DROP TABLE IF EXISTS `new_payroll_disbursement_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_disbursement_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `disbursement_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_financial_year`
--

DROP TABLE IF EXISTS `new_payroll_financial_year`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_financial_year` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `f_year` varchar(45) DEFAULT NULL,
  `from_date` date DEFAULT NULL,
  `to_date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_increments`
--

DROP TABLE IF EXISTS `new_payroll_increments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_increments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `increment_amount` int(11) DEFAULT NULL,
  `increment_date` date DEFAULT NULL,
  `increment_doc_link` varchar(256) DEFAULT NULL,
  `start_schedule_id` int(11) DEFAULT NULL,
  `old_ctc` int(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_master`
--

DROP TABLE IF EXISTS `new_payroll_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `aadhar_number` varchar(45) DEFAULT NULL,
  `account_number` varchar(45) DEFAULT NULL,
  `pan_number` varchar(45) DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `doj` date DEFAULT NULL,
  `uan_number` varchar(45) DEFAULT NULL,
  `ifsc_code` varchar(45) DEFAULT NULL,
  `branch_name` varchar(45) DEFAULT NULL,
  `bank_name` varchar(45) DEFAULT NULL,
  `contact_number` varchar(45) DEFAULT NULL,
  `staff_designation` varchar(45) DEFAULT NULL,
  `location` varchar(45) DEFAULT NULL,
  `pf_number` varchar(45) DEFAULT NULL,
  `employee_id` int(11) DEFAULT NULL,
  `esi_number` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_payslip`
--

DROP TABLE IF EXISTS `new_payroll_payslip`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_payslip` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `schedule_id` int(11) DEFAULT NULL,
  `no_of_days_present` decimal(10,2) DEFAULT NULL,
  `accumlative_gratuity` decimal(10,2) DEFAULT NULL,
  `paid_gratuity` decimal(10,2) DEFAULT NULL,
  `lop` decimal(10,2) DEFAULT NULL,
  `monthly_gross` decimal(10,2) DEFAULT NULL,
  `basic` decimal(10,2) DEFAULT NULL,
  `da` decimal(10,2) DEFAULT NULL,
  `hra` decimal(10,2) DEFAULT NULL,
  `special_allowance` decimal(10,2) DEFAULT NULL,
  `bonus_amount` decimal(10,2) DEFAULT NULL,
  `bonus_id` decimal(10,2) DEFAULT NULL,
  `total_earnings` decimal(10,2) DEFAULT NULL,
  `professional_tax` decimal(10,2) DEFAULT NULL,
  `tds` decimal(10,2) DEFAULT NULL,
  `pf_employee_contribution` decimal(10,2) DEFAULT NULL,
  `monthly_net` decimal(10,2) DEFAULT NULL,
  `standard_deduction` decimal(10,2) DEFAULT NULL,
  `hra_deductions` decimal(10,2) DEFAULT NULL,
  `total_deductions` decimal(10,2) DEFAULT NULL,
  `monthly_net_tax_calculation` decimal(10,2) DEFAULT NULL,
  `advance` decimal(10,2) DEFAULT NULL,
  `arrears` decimal(10,2) DEFAULT NULL,
  `path` text,
  `status` int(11) DEFAULT NULL,
  `esi` decimal(10,2) DEFAULT NULL,
  `cca` decimal(10,2) DEFAULT NULL,
  `ta` decimal(10,2) DEFAULT NULL,
  `medical_allowance` decimal(10,2) DEFAULT NULL,
  `conveyance` decimal(10,2) DEFAULT NULL,
  `vpf` decimal(10,2) DEFAULT NULL,
  `esi_employee_contribution` decimal(10,2) DEFAULT NULL,
  `transport` decimal(10,2) DEFAULT NULL,
  `other_deductions` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_pfs`
--

DROP TABLE IF EXISTS `new_payroll_pfs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_pfs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` int(11) DEFAULT NULL,
  `pf_amount_to_be_paid` int(11) DEFAULT NULL,
  `pf_paid` int(11) DEFAULT NULL,
  `payment_reference` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_salary`
--

DROP TABLE IF EXISTS `new_payroll_salary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_salary` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `yearly_gross` decimal(10,2) DEFAULT NULL,
  `payroll_master_id` int(11) DEFAULT NULL,
  `yearly_ctc` decimal(10,2) DEFAULT NULL,
  `pf` decimal(10,2) DEFAULT NULL,
  `monthly_gross` decimal(10,2) NOT NULL,
  `monthly_basic_salary` decimal(10,2) NOT NULL,
  `slab_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_schedules`
--

DROP TABLE IF EXISTS `new_payroll_schedules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_schedules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `financial_year_id` int(11) DEFAULT NULL,
  `schedule_name` varchar(45) DEFAULT NULL,
  `no_of_days` decimal(10,2) DEFAULT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_settings`
--

DROP TABLE IF EXISTS `new_payroll_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `slab_name` varchar(100) NOT NULL,
  `gratuity` decimal(10,2) DEFAULT NULL,
  `basic_salary` decimal(10,2) NOT NULL,
  `pf` decimal(10,2) DEFAULT NULL,
  `da` decimal(10,2) DEFAULT NULL,
  `hra` decimal(10,2) DEFAULT NULL,
  `medical_allowance` decimal(10,2) DEFAULT NULL,
  `conveyance` decimal(10,2) DEFAULT NULL,
  `esi` decimal(10,2) DEFAULT NULL,
  `special_allowance` decimal(10,2) DEFAULT NULL,
  `vpf` decimal(10,2) DEFAULT NULL,
  `advance` int(11) DEFAULT NULL,
  `bonus` int(11) DEFAULT NULL,
  `lunch_allowance` decimal(10,2) DEFAULT NULL,
  `uniform_allowance` decimal(10,2) DEFAULT NULL,
  `cleaning_allowance` decimal(10,2) DEFAULT NULL,
  `tds` decimal(10,2) DEFAULT NULL,
  `loan` int(11) DEFAULT NULL,
  `loss_of_pay` int(11) DEFAULT NULL,
  `professional_tax` int(11) DEFAULT NULL,
  `cca` decimal(10,0) DEFAULT NULL,
  `basic_mode` tinyint(4) NOT NULL,
  `arrears` decimal(10,0) DEFAULT NULL,
  `ta` decimal(10,2) DEFAULT NULL,
  `cca_algo` varchar(100) DEFAULT NULL,
  `esi_employee_contribution` decimal(10,2) DEFAULT NULL,
  `transport` decimal(10,2) DEFAULT NULL,
  `other_deductions` decimal(10,2) DEFAULT NULL,
  `hra_algo` decimal(10,2) DEFAULT NULL,
  `da_algo` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_staff_declaration`
--

DROP TABLE IF EXISTS `new_payroll_staff_declaration`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_staff_declaration` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `declare_LIC_premium_year` varchar(256) DEFAULT NULL,
  `declare_hra` varchar(256) DEFAULT NULL,
  `declare_vpf` int(11) DEFAULT NULL,
  `declare_fd_five_year_contributaion` int(11) DEFAULT NULL,
  `declare_loan_principle_payment` int(11) DEFAULT NULL,
  `declare_loan_interest_payment` int(11) DEFAULT NULL,
  `declare_ELSS_mutual_fund` int(11) DEFAULT NULL,
  `declare_financial_year` int(11) DEFAULT NULL,
  `actual_LIC_premium_year` varchar(256) DEFAULT NULL,
  `actual_hra` varchar(256) DEFAULT NULL,
  `actual_vpf` int(11) DEFAULT NULL,
  `actual_fd_five_year_contributaion` int(11) DEFAULT NULL,
  `actual_loan_principle_payment` int(11) DEFAULT NULL,
  `actual_loan_interest_payment` int(11) DEFAULT NULL,
  `actual_ELSS_mutual_fund` int(11) DEFAULT NULL,
  `actual_financial_year` int(11) DEFAULT NULL,
  `proof_LIC_premium_year` varchar(256) DEFAULT NULL,
  `proof_hra` varchar(256) DEFAULT NULL,
  `proof_vpf` int(11) DEFAULT NULL,
  `proof_fd_five_year_contributaion` int(11) DEFAULT NULL,
  `proof_loan_principle_payment` int(11) DEFAULT NULL,
  `proof_loan_interest_payment` int(11) DEFAULT NULL,
  `proof_ELSS_mutual_fund` int(11) DEFAULT NULL,
  `proof_financial_year` int(11) DEFAULT NULL,
  `form_16_link` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_tds`
--

DROP TABLE IF EXISTS `new_payroll_tds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_tds` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` int(11) DEFAULT NULL,
  `tds_amount_to_be_paid` int(11) DEFAULT NULL,
  `tds_paid` int(11) DEFAULT NULL,
  `payment_reference` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `new_payroll_template`
--

DROP TABLE IF EXISTS `new_payroll_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `new_payroll_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `online_application_fee_payment_master`
--

DROP TABLE IF EXISTS `online_application_fee_payment_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `online_application_fee_payment_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(255) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `init_date_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `source` varchar(225) DEFAULT NULL,
  `source_id` int(11) DEFAULT NULL,
  `source_callback_url` varchar(255) DEFAULT NULL,
  `payment_callback_url` varchar(255) DEFAULT NULL,
  `payment_service_provider` varchar(45) DEFAULT NULL,
  `currency` varchar(100) NOT NULL,
  `execution_mode` varchar(100) NOT NULL,
  `payment_to` varchar(255) NOT NULL,
  `hash_match` tinyint(4) NOT NULL,
  `tx_id` varchar(100) DEFAULT NULL,
  `tx_cardmasked` varchar(45) DEFAULT NULL,
  `tx_payment_channel` varchar(100) DEFAULT NULL,
  `tx_payment_mode` varchar(100) DEFAULT NULL,
  `tx_response_code` int(11) NOT NULL DEFAULT '-1',
  `tx_response_message` varchar(255) NOT NULL,
  `tx_date_time` datetime DEFAULT NULL,
  `status` varchar(100) NOT NULL DEFAULT 'INITIATED',
  `recon_status` varchar(100) NOT NULL DEFAULT 'NOT_CALLED',
  `transaction_by` int(11) NOT NULL,
  `api_request` text,
  `api_response` text,
  `url_only_mode` varchar(40) NOT NULL,
  `settlement_status` varchar(40) NOT NULL DEFAULT 'NOT_SETTLED',
  `settlement_verification` varchar(40) NOT NULL,
  `settlement_id_json` text,
  `settlement_confirmed_by` int(11) DEFAULT NULL,
  `settlement_confirmed_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `online_attendance_students`
--

DROP TABLE IF EXISTS `online_attendance_students`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `online_attendance_students` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` int(11) DEFAULT NULL,
  `jitsi_id` varchar(255) DEFAULT NULL,
  `stakeholder_id` int(11) DEFAULT NULL,
  `join_time` timestamp NULL DEFAULT NULL,
  `left_time` timestamp NULL DEFAULT NULL,
  `type` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `online_bookings`
--

DROP TABLE IF EXISTS `online_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `online_bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_id` int(11) NOT NULL,
  `period_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `date_booked` date NOT NULL,
  `class_section_id` int(11) DEFAULT NULL,
  `subject` varchar(45) DEFAULT NULL,
  `remarks` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `online_classroom_schedule_slots`
--

DROP TABLE IF EXISTS `online_classroom_schedule_slots`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `online_classroom_schedule_slots` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `classroom_id` int(11) NOT NULL,
  `schedule_id` int(11) NOT NULL,
  `slot_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `status` varchar(100) DEFAULT NULL COMMENT 'created, cancelled',
  `class_section_ids` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `online_classroom_slots`
--

DROP TABLE IF EXISTS `online_classroom_slots`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `online_classroom_slots` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0: Inactive, 1:Active',
  `classroom_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `online_classrooms`
--

DROP TABLE IF EXISTS `online_classrooms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `online_classrooms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `occupied_by` varchar(255) DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0: Inactive, 1:Active',
  `student_video_quality` varchar(255) DEFAULT '320x240',
  `student_frame_rate` int(11) DEFAULT '10',
  `staff_video_quality` varchar(255) DEFAULT '640x480',
  `staff_frame_rate` int(11) DEFAULT '10',
  `staff_screen_share_quality` varchar(255) DEFAULT '640x480',
  `staff_screen_frame_rate` int(11) DEFAULT '10',
  `video_to_student` tinyint(4) DEFAULT '0',
  `audio_to_student` tinyint(4) DEFAULT '0',
  `recording` tinyint(4) DEFAULT '1',
  `push_to_talk` tinyint(4) DEFAULT '1',
  `asking_questions` tinyint(4) DEFAULT '0',
  `classmates_video` tinyint(4) DEFAULT '1',
  `start_video` tinyint(4) DEFAULT '0',
  `start_audio` tinyint(4) DEFAULT '0',
  `control_video` tinyint(4) DEFAULT '0',
  `classmates_videos` tinyint(4) DEFAULT '0',
  `grid_view` tinyint(4) DEFAULT '0',
  `max_student_participants` int(11) NOT NULL DEFAULT '50',
  `student_screen_share` tinyint(4) DEFAULT '0',
  `disable_simulcast` tinyint(4) NOT NULL DEFAULT '0',
  `student_screen_share_frame_rate` int(11) DEFAULT '5',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `online_payment_master`
--

DROP TABLE IF EXISTS `online_payment_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `online_payment_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(255) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `init_date_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `source` varchar(225) DEFAULT NULL,
  `source_id` int(11) DEFAULT NULL,
  `source_callback_url` varchar(255) DEFAULT NULL,
  `payment_callback_url` varchar(255) DEFAULT NULL,
  `payment_service_provider` varchar(45) DEFAULT NULL,
  `currency` varchar(100) NOT NULL,
  `execution_mode` varchar(100) NOT NULL,
  `payment_to` varchar(255) NOT NULL,
  `hash_match` tinyint(4) NOT NULL,
  `tx_id` varchar(100) DEFAULT NULL,
  `tx_cardmasked` varchar(45) DEFAULT NULL,
  `tx_payment_channel` varchar(100) DEFAULT NULL,
  `tx_payment_mode` varchar(100) DEFAULT NULL,
  `tx_response_code` int(11) NOT NULL DEFAULT '-1',
  `tx_response_message` varchar(255) NOT NULL,
  `tx_date_time` datetime DEFAULT NULL,
  `status` varchar(100) NOT NULL DEFAULT 'INITIATED',
  `recon_status` varchar(100) NOT NULL DEFAULT 'NOT_CALLED',
  `transaction_by` int(11) NOT NULL,
  `is_split_payment` tinyint(4) NOT NULL,
  `split_json` text,
  `split_api_status` varchar(100) NOT NULL DEFAULT 'NOT_CALLED',
  `api_request` text,
  `api_response` text,
  `url_only_mode` varchar(40) NOT NULL,
  `settlement_status` varchar(40) NOT NULL DEFAULT 'NOT_SETTLED',
  `settlement_verification` varchar(40) NOT NULL,
  `settlement_id_json` text,
  `settlement_confirmed_by` int(11) DEFAULT NULL,
  `settlement_confirmed_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `online_schedule`
--

DROP TABLE IF EXISTS `online_schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `online_schedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `description` text,
  `acad_year_id` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `actual_start_time` datetime DEFAULT NULL,
  `actual_end_time` datetime DEFAULT NULL,
  `session_id` varchar(45) DEFAULT NULL,
  `classroom_id` varchar(45) DEFAULT NULL,
  `status` varchar(45) NOT NULL DEFAULT 'created' COMMENT 'created, cancelled',
  `is_online` tinyint(4) NOT NULL DEFAULT '0',
  `recording` varchar(255) DEFAULT NULL,
  `files` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `online_schedule_invitees`
--

DROP TABLE IF EXISTS `online_schedule_invitees`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `online_schedule_invitees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` int(11) DEFAULT NULL,
  `type` varchar(45) DEFAULT NULL COMMENT 'staff, student, class_section',
  `stake_holder_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `online_settlement_status`
--

DROP TABLE IF EXISTS `online_settlement_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `online_settlement_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `check_date` date DEFAULT NULL,
  `settelement_amount` double DEFAULT NULL,
  `transaction_date` date DEFAULT NULL,
  `status` tinyint(2) DEFAULT '0' COMMENT '0 / 1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `other_links`
--

DROP TABLE IF EXISTS `other_links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `other_links` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `link_name` varchar(255) NOT NULL,
  `link` text NOT NULL,
  `description` text,
  `show_to` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:parent, 1:staff, 2:both',
  `is_enabled` tinyint(4) NOT NULL DEFAULT '1',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `modified_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `outgoing_register_student`
--

DROP TABLE IF EXISTS `outgoing_register_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `outgoing_register_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_user_id` int(11) unsigned NOT NULL,
  `options_select` varchar(400) NOT NULL,
  `other_visitor_id` int(11) NOT NULL,
  `remarks` text NOT NULL,
  `create_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `parent`
--

DROP TABLE IF EXISTS `parent`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `parent` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `qualification` varchar(100) DEFAULT NULL,
  `occupation` varchar(100) DEFAULT NULL,
  `mobile_no` varchar(50) DEFAULT NULL,
  `aadhar_no` varchar(25) DEFAULT NULL,
  `student_id` int(11) NOT NULL,
  `picture_url` varchar(255) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `annual_income` decimal(15,2) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `mother_tongue` varchar(100) DEFAULT NULL,
  `email` varchar(150) DEFAULT NULL,
  `identification_code` varchar(100) DEFAULT NULL,
  `language_spoken` varchar(100) DEFAULT NULL,
  `designation` varchar(100) DEFAULT NULL,
  `high_quality_picture_url` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `parent_feedback`
--

DROP TABLE IF EXISTS `parent_feedback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `parent_feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `feedback_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `feedback` text NOT NULL,
  `parent_id` int(11) NOT NULL,
  `ptm_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `parent_initiative`
--

DROP TABLE IF EXISTS `parent_initiative`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `parent_initiative` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `initiative` varchar(100) NOT NULL,
  `audience` varchar(150) NOT NULL,
  `duration` varchar(50) NOT NULL,
  `availability` varchar(150) NOT NULL,
  `description` text NOT NULL,
  `brief_about_parent` text NOT NULL,
  `status` varchar(255) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `principal_comments` text NOT NULL,
  `principal_remarks_date` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payroll_master`
--

DROP TABLE IF EXISTS `payroll_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payroll_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `payroll_slab_id` int(11) NOT NULL,
  `staff_salary` int(11) NOT NULL,
  `vpf` int(11) NOT NULL,
  `pan_number` varchar(255) NOT NULL,
  `account_number` varchar(255) NOT NULL,
  `ifsc` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payroll_payslip`
--

DROP TABLE IF EXISTS `payroll_payslip`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payroll_payslip` (
  `id` int(11) NOT NULL,
  `staffid` int(11) NOT NULL,
  `payroll_schedule_id` int(11) NOT NULL,
  `no_working_days` int(11) NOT NULL,
  `no_of_leave` int(11) NOT NULL,
  `basic_salary` decimal(10,2) NOT NULL,
  `staff_hra` decimal(10,2) NOT NULL,
  `staff_da` decimal(10,2) NOT NULL,
  `esi` decimal(10,2) NOT NULL,
  `epf` decimal(10,2) NOT NULL,
  `tds` int(11) NOT NULL,
  `vpf` decimal(10,2) NOT NULL,
  `loss_of_pay` int(11) NOT NULL,
  `total_earning` decimal(10,2) NOT NULL,
  `net_pay` decimal(10,2) NOT NULL,
  `total_deduct` decimal(10,2) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) DEFAULT NULL,
  `url` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payroll_profiles`
--

DROP TABLE IF EXISTS `payroll_profiles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payroll_profiles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `basic_salary_percent` tinyint(4) NOT NULL,
  `hra_percent` tinyint(4) NOT NULL,
  `other_allowance_percent` tinyint(4) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payroll_schedule`
--

DROP TABLE IF EXISTS `payroll_schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payroll_schedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `no_working_days` int(11) NOT NULL,
  `order` int(11) NOT NULL,
  `acad_year_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payroll_slabs`
--

DROP TABLE IF EXISTS `payroll_slabs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payroll_slabs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `slab_name` varchar(255) NOT NULL,
  `basic` float NOT NULL,
  `hra_percent` float NOT NULL,
  `cca_percent` float NOT NULL,
  `tds_percent` int(11) NOT NULL,
  `esi_percent` float NOT NULL,
  `epf_percent` int(11) NOT NULL,
  `slab_decription` text NOT NULL,
  `slab_start_value` int(11) NOT NULL,
  `slab_end_value` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `privileges`
--

DROP TABLE IF EXISTS `privileges`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `privileges` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(100) NOT NULL,
  `active` tinyint(4) NOT NULL COMMENT '1: Active, 0: Inactive',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `privileges_sub`
--

DROP TABLE IF EXISTS `privileges_sub`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `privileges_sub` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `privilege_id` int(11) NOT NULL,
  `active` tinyint(4) NOT NULL COMMENT '1:Active 0: Inactive',
  `is_critical` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_brands`
--

DROP TABLE IF EXISTS `product_brands`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_brands` (
  `brand_id` int(11) NOT NULL AUTO_INCREMENT,
  `brand_name` varchar(255) NOT NULL,
  PRIMARY KEY (`brand_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_colors`
--

DROP TABLE IF EXISTS `product_colors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_colors` (
  `color_id` int(11) NOT NULL AUTO_INCREMENT,
  `color_name` varchar(255) NOT NULL,
  PRIMARY KEY (`color_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_master`
--

DROP TABLE IF EXISTS `product_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_code` varchar(255) NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `stockable` tinyint(1) NOT NULL,
  `initial_quantity` int(11) NOT NULL,
  `measuring_unit` varchar(100) NOT NULL,
  `picture_url` varchar(255) NOT NULL,
  `color_flag` tinyint(1) NOT NULL,
  `size_flag` tinyint(1) NOT NULL,
  `description` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_sizes`
--

DROP TABLE IF EXISTS `product_sizes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_sizes` (
  `size_id` int(11) NOT NULL AUTO_INCREMENT,
  `size` int(11) NOT NULL,
  `measuring_unit` varchar(100) NOT NULL,
  PRIMARY KEY (`size_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `product_variant`
--

DROP TABLE IF EXISTS `product_variant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_variant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `brand_id` int(11) NOT NULL,
  `color_id` int(11) NOT NULL,
  `size_id` int(11) NOT NULL,
  `initial_quantity` int(11) NOT NULL,
  `reorder_point1` int(11) NOT NULL,
  `reorder_point2` int(11) NOT NULL,
  `storage_location` varchar(255) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ptm_cls_section`
--

DROP TABLE IF EXISTS `ptm_cls_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ptm_cls_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class` varchar(50) NOT NULL,
  `section` varchar(10) NOT NULL,
  `ptm_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ptm_master`
--

DROP TABLE IF EXISTS `ptm_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ptm_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `meeting_code` varchar(50) NOT NULL,
  `description` text NOT NULL,
  `date_of_meeting` date NOT NULL,
  `class_section` varchar(50) NOT NULL,
  `current_status` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ptm_master_history`
--

DROP TABLE IF EXISTS `ptm_master_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ptm_master_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `status` varchar(50) NOT NULL,
  `action_by` int(11) NOT NULL,
  `action_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ptm_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `publication_master`
--

DROP TABLE IF EXISTS `publication_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `publication_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_by` int(11) NOT NULL,
  `published_date` date NOT NULL,
  `category` varchar(255) DEFAULT NULL,
  `content` text,
  `picture` varchar(255) DEFAULT NULL,
  `user_type` char(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `published_to`
--

DROP TABLE IF EXISTS `published_to`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `published_to` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `publication_id` int(11) NOT NULL,
  `student_or_section_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `push_notification`
--

DROP TABLE IF EXISTS `push_notification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `push_notification` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `code` text,
  `curl_status` varchar(256) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `resources`
--

DROP TABLE IF EXISTS `resources`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resources` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `resource_type` varchar(45) NOT NULL,
  `resource_file` mediumtext NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `approved_by` int(11) NOT NULL,
  `approved_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` varchar(45) DEFAULT NULL,
  `description` mediumtext,
  `grade` varchar(45) DEFAULT NULL,
  `subject` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` varchar(255) NOT NULL,
  `active` tinyint(4) NOT NULL COMMENT '1: Active, 0: Inactive',
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL COMMENT 'Last Modified by which staff',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roles_privileges_sub`
--

DROP TABLE IF EXISTS `roles_privileges_sub`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles_privileges_sub` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `privilege_sub_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL COMMENT 'Last Modified by which avatar',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roles_staff`
--

DROP TABLE IF EXISTS `roles_staff`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL COMMENT 'Which Staff last modified this record?',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `room_timetable`
--

DROP TABLE IF EXISTS `room_timetable`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `room_timetable` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_id` int(11) NOT NULL,
  `allocation_data` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  `timetable_template_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roomlist_subject_section`
--

DROP TABLE IF EXISTS `roomlist_subject_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roomlist_subject_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sss_id` int(11) NOT NULL,
  `room_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `route_allocate`
--

DROP TABLE IF EXISTS `route_allocate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `route_allocate` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stop_id` int(11) NOT NULL,
  `route_no` int(11) NOT NULL COMMENT 'vehicle_data_id ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sales_master`
--

DROP TABLE IF EXISTS `sales_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sales_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` varchar(100) NOT NULL,
  `receipt_no` varchar(100) NOT NULL,
  `student_id` int(11) NOT NULL,
  `payment_type` tinyint(4) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `receipt_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `bank_name` varchar(255) DEFAULT NULL,
  `bank_branch` varchar(255) DEFAULT NULL,
  `cheque_dd_number` varchar(100) DEFAULT NULL,
  `recon_status` tinyint(4) DEFAULT NULL,
  `recon_submitted_on` timestamp NULL DEFAULT NULL,
  `cheque_dd_date` date DEFAULT NULL,
  `soft_delete` tinyint(4) NOT NULL,
  `remarks` varchar(255) DEFAULT NULL,
  `card_charge_amount` decimal(10,2) DEFAULT NULL,
  `receipt_pdf_path` varchar(255) DEFAULT NULL,
  `html_sales` text,
  `sales_type` varchar(20) NOT NULL DEFAULT 'existing' COMMENT 'existing | new',
  `student_name` varchar(255) DEFAULT NULL,
  `parent_name` varchar(255) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `pdf_status` tinyint(4) DEFAULT NULL,
  `soft_delete_by` int(11) DEFAULT NULL,
  `soft_delete_on` date DEFAULT NULL,
  `soft_delete_remarks` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sales_receipt_book`
--

DROP TABLE IF EXISTS `sales_receipt_book`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sales_receipt_book` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_format` int(11) NOT NULL,
  `prefix` varchar(100) NOT NULL,
  `digit_count` int(11) NOT NULL,
  `running_number` int(11) NOT NULL,
  `year` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sales_transactions`
--

DROP TABLE IF EXISTS `sales_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sales_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sales_master_id` int(11) NOT NULL,
  `inventory_product_id` int(11) NOT NULL,
  `inventory_product_variant_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Temporary view structure for view `sample`
--

DROP TABLE IF EXISTS `sample`;
/*!50001 DROP VIEW IF EXISTS `sample`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `sample` AS SELECT 
 1 AS `student_admission_id`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `school_calender`
--

DROP TABLE IF EXISTS `school_calender`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_calender` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_name` varchar(255) NOT NULL,
  `event_type` tinyint(4) NOT NULL COMMENT '1: event,  2: holiday  3: holiday range',
  `from_date` date NOT NULL,
  `to_date` date DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `applicable_to` tinyint(4) NOT NULL COMMENT '1: Staff, 2: Parents3: Both',
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  `board` int(3) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shift_timings`
--

DROP TABLE IF EXISTS `shift_timings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shift_timings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shift_id` int(11) NOT NULL,
  `weekday` varchar(20) NOT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shifts`
--

DROP TABLE IF EXISTS `shifts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shifts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sms_credits_usage`
--

DROP TABLE IF EXISTS `sms_credits_usage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sms_credits_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sms_credits` int(11) NOT NULL,
  `action` varchar(50) NOT NULL,
  `action_by` int(11) NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `sms_master_id` int(11) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1: Active, 0:Deleted',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sms_master`
--

DROP TABLE IF EXISTS `sms_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sms_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action_by` int(11) NOT NULL,
  `sms_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `sms_content` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `user_type` varchar(50) NOT NULL,
  `msg_id` varchar(255) DEFAULT NULL,
  `sms_count` int(11) DEFAULT '0',
  `sent_to` text,
  `source` varchar(100) DEFAULT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sms_sent_to`
--

DROP TABLE IF EXISTS `sms_sent_to`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sms_sent_to` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sms_id` int(11) NOT NULL,
  `student_staff_id` int(11) NOT NULL,
  `mobile_no` varchar(50) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `retry_count` int(11) DEFAULT NULL,
  `response_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sms_template_new`
--

DROP TABLE IF EXISTS `sms_template_new`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sms_template_new` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `category` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sms_templates`
--

DROP TABLE IF EXISTS `sms_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sms_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` text NOT NULL,
  `editable` tinyint(1) NOT NULL COMMENT '0: No, 1: Yes',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `applicable_to` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sms_templates_acad_year`
--

DROP TABLE IF EXISTS `sms_templates_acad_year`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sms_templates_acad_year` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `sms_templates_id` int(11) NOT NULL,
  `content` text NOT NULL,
  `acad_year_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_attendance`
--

DROP TABLE IF EXISTS `staff_attendance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `staff_attendance_session_id` int(11) NOT NULL,
  `status` varchar(45) NOT NULL DEFAULT 'AB',
  `check_in_time` timestamp NULL DEFAULT NULL,
  `check_out_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_attendance_code`
--

DROP TABLE IF EXISTS `staff_attendance_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_attendance_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `staff_code` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_attendance_logs`
--

DROP TABLE IF EXISTS `staff_attendance_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_attendance_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` int(11) NOT NULL,
  `staff_code` varchar(100) NOT NULL,
  `punch_time` timestamp NULL DEFAULT NULL,
  `access_type` int(5) NOT NULL,
  `direction` tinyint(4) NOT NULL,
  `timezone_code` int(3) NOT NULL,
  `is_updated` tinyint(4) DEFAULT '0',
  `status` tinyint(4) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_attendance_session`
--

DROP TABLE IF EXISTS `staff_attendance_session`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_attendance_session` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `day` date NOT NULL,
  `attendance_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_attendance_transaction`
--

DROP TABLE IF EXISTS `staff_attendance_transaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_attendance_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `staff_attendance_session_id` int(11) NOT NULL,
  `event_time` timestamp NULL DEFAULT NULL,
  `action` text,
  `comment` text,
  `action_by` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_duties`
--

DROP TABLE IF EXISTS `staff_duties`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_duties` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_initiative_master`
--

DROP TABLE IF EXISTS `staff_initiative_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_initiative_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `initiative_name` varchar(100) NOT NULL,
  `who_attend` varchar(100) NOT NULL,
  `duration` varchar(100) NOT NULL,
  `date` date NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(4) NOT NULL,
  `remarks` text,
  `principal_remarks` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_logs`
--

DROP TABLE IF EXISTS `staff_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `log` text NOT NULL,
  `task` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_logs_team`
--

DROP TABLE IF EXISTS `staff_logs_team`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_logs_team` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_logs_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_master`
--

DROP TABLE IF EXISTS `staff_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `short_name` varchar(250) DEFAULT NULL,
  `status` tinyint(4) NOT NULL COMMENT '1: Pending, 2: Approved, 3: Rejected, 4: Alumni',
  `staff_type` varchar(100) NOT NULL COMMENT 'Teaching, Non-teaching, etc',
  `designation` varchar(255) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `father_name` varchar(255) DEFAULT NULL,
  `mother_name` varchar(255) DEFAULT NULL,
  `marital_status` tinyint(4) NOT NULL COMMENT '0: single 1:married',
  `spouse_name` varchar(255) DEFAULT NULL,
  `picture_url` varchar(255) DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `nationality` varchar(255) DEFAULT NULL,
  `gender` char(2) NOT NULL COMMENT 'M: male, F:female',
  `aadhar_number` varchar(30) DEFAULT NULL,
  `qualification` varchar(255) DEFAULT NULL,
  `subject_specialization` varchar(255) DEFAULT NULL,
  `total_experience` int(11) DEFAULT NULL,
  `total_education_experience` int(11) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `active` tinyint(4) NOT NULL COMMENT '0: blocked 1: active',
  `isdummy` tinyint(4) NOT NULL COMMENT 'Dummy Staff are used for testing and for creating floating periods',
  `contact_number` varchar(100) DEFAULT NULL,
  `alternative_number` varchar(255) DEFAULT NULL,
  `spouse_contact_no` varchar(100) DEFAULT NULL,
  `joining_date` date DEFAULT NULL,
  `emergency_info` text,
  `math_high_grade` varchar(45) DEFAULT NULL,
  `english_high_grade` varchar(45) DEFAULT NULL,
  `social_high_grade` varchar(45) DEFAULT NULL,
  `trained_to_teach` tinyint(1) DEFAULT NULL,
  `biometric_attendance_code` varchar(100) DEFAULT NULL,
  `appointed_subject` varchar(100) DEFAULT NULL,
  `classes_taught` varchar(100) DEFAULT NULL,
  `main_sub_taught` varchar(100) DEFAULT NULL,
  `add_sub_taught` varchar(100) DEFAULT NULL,
  `identification_code` varchar(100) DEFAULT NULL,
  `shift_id` int(11) DEFAULT NULL,
  `webcam_avatar` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_observation`
--

DROP TABLE IF EXISTS `staff_observation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_observation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `taken_by` int(11) NOT NULL,
  `observation` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_qualification`
--

DROP TABLE IF EXISTS `staff_qualification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_qualification` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `qualification` varchar(255) NOT NULL,
  `year_of_passing` year(4) NOT NULL,
  `percentage` float NOT NULL,
  `institute` varchar(255) NOT NULL,
  `qualification_type` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_salary_details`
--

DROP TABLE IF EXISTS `staff_salary_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_salary_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `total_salary` decimal(10,2) NOT NULL,
  `basic_salary` decimal(10,2) NOT NULL,
  `staff_hra` decimal(10,2) NOT NULL,
  `staff_da` decimal(10,2) NOT NULL,
  `total_gross` decimal(10,2) NOT NULL,
  `staff_esi` decimal(10,2) NOT NULL,
  `staff_pf` decimal(10,2) NOT NULL,
  `staff_pt` decimal(10,2) NOT NULL,
  `bank_account` varchar(50) NOT NULL,
  `pan_number` varchar(50) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `esi_number` varchar(100) DEFAULT NULL,
  `pf_uan_number` varchar(100) DEFAULT NULL,
  `salary_profile_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_special_requests`
--

DROP TABLE IF EXISTS `staff_special_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_special_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `reason` text NOT NULL,
  `class_section_id` int(11) NOT NULL,
  `num_periods` tinyint(4) NOT NULL,
  `from_date` date NOT NULL,
  `to_date` date NOT NULL,
  `status` tinyint(4) NOT NULL COMMENT '0:pending,1:completed',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_subject_section`
--

DROP TABLE IF EXISTS `staff_subject_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_subject_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_section_id` int(11) NOT NULL,
  `subject_id` bigint(20) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `period_span` tinyint(4) NOT NULL,
  `allotted` tinyint(1) NOT NULL,
  `allocation_type_staff` tinyint(4) NOT NULL COMMENT '1: Only one class can be taken, 2: Two classes can be taken, 4: 4 classes can be combined and taken',
  `allocation_type_room` tinyint(4) NOT NULL COMMENT '1: Room for 1 Section, 2: Room for 2 sections, 3: Room for 3 sections, 4: Room for 4 sections',
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`),
  KEY `class_section_id` (`class_section_id`),
  KEY `subject_id` (`subject_id`),
  KEY `subject_id_2` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff_timetable`
--

DROP TABLE IF EXISTS `staff_timetable`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_timetable` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `allocation_data` text NOT NULL COMMENT 'Denotes the allocation of a staff for use for processing. Stored in the format <num_periods_mon>,<num_periods_tue>,....|<1:allocated for Mon-P1, 0:NA>,....',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  `timetable_template_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staffduties_dates`
--

DROP TABLE IF EXISTS `staffduties_dates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staffduties_dates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `from_periods` varchar(20) NOT NULL,
  `to_periods` varchar(20) NOT NULL,
  `staffduties_id` int(11) NOT NULL,
  `substitution_added` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staffinitiative_staffs`
--

DROP TABLE IF EXISTS `staffinitiative_staffs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staffinitiative_staffs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `initiative_id` int(11) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staffinitiative_student`
--

DROP TABLE IF EXISTS `staffinitiative_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staffinitiative_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `initiative_id` int(11) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `stafflist_duties`
--

DROP TABLE IF EXISTS `stafflist_duties`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `stafflist_duties` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staffduties_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `stafflist_subject_section`
--

DROP TABLE IF EXISTS `stafflist_subject_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `stafflist_subject_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sss_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_actions`
--

DROP TABLE IF EXISTS `student_actions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_actions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `type` tinyint(4) NOT NULL COMMENT 'type 0-OutGoing,1-LateComer,2-HealthCare',
  `picked_by` varchar(50) DEFAULT NULL,
  `picked_id` int(11) NOT NULL,
  `entry_at` timestamp NULL DEFAULT NULL,
  `exit_at` timestamp NULL DEFAULT NULL,
  `remarks` text NOT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `student_admission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_admission`
--

DROP TABLE IF EXISTS `student_admission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_admission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admission_no` varchar(100) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `gender` char(2) NOT NULL COMMENT 'M : Male, F: Female',
  `nationality` varchar(50) DEFAULT NULL,
  `religion` varchar(50) DEFAULT NULL,
  `category` tinyint(4) DEFAULT NULL COMMENT 'See school config for definition',
  `mother_tongue` varchar(100) DEFAULT NULL,
  `preferred_contact_no` varchar(100) DEFAULT NULL,
  `sibling_type` tinyint(4) DEFAULT '0' COMMENT '11: no sibling, 12: first_joined_sibling, 13: second_joined_sibling, 14: third_joined_sibling, 15: fourth_joined_sibling',
  `sibling_id` int(11) DEFAULT NULL COMMENT 'stundent id of the sibling',
  `has_staff` tinyint(4) NOT NULL DEFAULT '0',
  `staff_id` int(11) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `is_rte` tinyint(1) DEFAULT NULL COMMENT 'is he an rte candidate  ',
  `admission_status` tinyint(4) NOT NULL COMMENT 'See school config for definition',
  `date_of_joining` date DEFAULT NULL COMMENT 'date of joining',
  `birth_taluk` varchar(100) DEFAULT NULL,
  `birth_district` varchar(255) DEFAULT NULL,
  `caste` varchar(100) DEFAULT NULL,
  `aadhar_no` varchar(50) DEFAULT NULL,
  `class_admitted_to` int(11) DEFAULT NULL,
  `admission_year` varchar(100) DEFAULT NULL,
  `emergency_info` text,
  `identification_code` varchar(100) DEFAULT NULL,
  `second_language_choice` varchar(255) NOT NULL DEFAULT 'NA',
  `third_language_choice` varchar(255) NOT NULL DEFAULT 'NA',
  `admission_acad_year_id` int(11) DEFAULT NULL,
  `email` varchar(150) DEFAULT NULL,
  `rfid_number` varchar(255) DEFAULT NULL,
  `has_transport` tinyint(4) NOT NULL DEFAULT '0',
  `sts_number` varchar(150) DEFAULT NULL,
  `admission_form_id` int(11) DEFAULT NULL,
  `preferred_parent` varchar(45) NOT NULL DEFAULT 'Both',
  `language_spoken` varchar(100) DEFAULT NULL,
  `quota` tinyint(4) DEFAULT NULL,
  `webcam_avatar` varchar(255) DEFAULT NULL,
  `life_time_fee_mode` tinyint(4) DEFAULT '0',
  `attempt` tinyint(4) DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `admission_no` (`admission_no`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_certificate`
--

DROP TABLE IF EXISTS `student_certificate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_certificate` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `certificate_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `html_template` longtext NOT NULL,
  `publish_status` varchar(100) NOT NULL,
  `pdf_path` text,
  `pdf_status` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_documents`
--

DROP TABLE IF EXISTS `student_documents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `document_type` varchar(100) NOT NULL,
  `document_url` varchar(100) NOT NULL,
  `document_other` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_health`
--

DROP TABLE IF EXISTS `student_health`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_health` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `blood_group` varchar(20) NOT NULL,
  `physical_disability` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:No,1:Yes',
  `learning_disability` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:No,1:Yes',
  `physical_disability_reason` varchar(100) DEFAULT NULL,
  `learning_disability_reason` varchar(100) DEFAULT NULL,
  `student_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `allergy` varchar(255) NOT NULL,
  `family_history` text NOT NULL,
  `anaemia` tinyint(1) NOT NULL COMMENT '0:absent, 1:mild, 2:moderate, 3:sever',
  `fit_to_participate` tinyint(1) NOT NULL COMMENT '0:fit, 1:with precaution, 2:not fit',
  `height` decimal(10,2) NOT NULL,
  `weight` decimal(10,2) NOT NULL,
  `hair` varchar(255) NOT NULL,
  `skin` varchar(255) NOT NULL,
  `ear` varchar(255) NOT NULL,
  `nose` varchar(255) NOT NULL,
  `throat` varchar(255) NOT NULL,
  `neck` varchar(255) NOT NULL,
  `respiratory` varchar(255) NOT NULL,
  `cardio_vascular` varchar(255) NOT NULL,
  `abdomen` varchar(255) NOT NULL,
  `nervous_system` varchar(255) NOT NULL,
  `left_eye` varchar(255) NOT NULL,
  `right_eye` varchar(255) NOT NULL,
  `extra_oral` text NOT NULL,
  `bad_breath` varchar(255) NOT NULL,
  `tooth_cavity` varchar(255) NOT NULL,
  `plaque` varchar(255) NOT NULL,
  `gum_inflamation` varchar(255) NOT NULL,
  `stains` varchar(255) NOT NULL,
  `gum_bleeding` varchar(255) NOT NULL,
  `soft_tissue` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_height_weight`
--

DROP TABLE IF EXISTS `student_height_weight`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_height_weight` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `height_in_cm` double NOT NULL,
  `weight_in_kg` double NOT NULL,
  `student_id` int(11) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_house`
--

DROP TABLE IF EXISTS `student_house`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_house` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `house` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_observation`
--

DROP TABLE IF EXISTS `student_observation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_observation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `std_id` int(11) NOT NULL,
  `observation` text NOT NULL,
  `staff_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_prev_school`
--

DROP TABLE IF EXISTS `student_prev_school`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_prev_school` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `year_id` varchar(100) NOT NULL,
  `school_name` varchar(255) NOT NULL,
  `class` varchar(100) NOT NULL,
  `board` varchar(100) DEFAULT NULL,
  `board_other` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_prev_school_marks`
--

DROP TABLE IF EXISTS `student_prev_school_marks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_prev_school_marks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sub_name` varchar(100) NOT NULL,
  `grade` varchar(100) NOT NULL,
  `percentage` decimal(10,2) NOT NULL,
  `sps_id` int(11) NOT NULL COMMENT 'std_prev_school_id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_profile_update`
--

DROP TABLE IF EXISTS `student_profile_update`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_profile_update` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `student_status` tinyint(1) DEFAULT '0' COMMENT 'NULL:not saved, 0:std data saved, 1: health data saved, 2:father data saved, 3: mother data saved, 5: Completed',
  `health_status` tinyint(1) DEFAULT '0',
  `father_status` tinyint(1) DEFAULT '0',
  `mother_status` tinyint(1) DEFAULT '0',
  `completed` tinyint(1) DEFAULT '0',
  `control_status` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_relation`
--

DROP TABLE IF EXISTS `student_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `std_id` int(11) NOT NULL,
  `relation_id` int(11) NOT NULL,
  `relation_type` varchar(100) NOT NULL,
  `active` int(11) NOT NULL COMMENT '1:yes, 0: no',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `student_to_stops`
--

DROP TABLE IF EXISTS `student_to_stops`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_to_stops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `stop_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `moidified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `route` int(11) NOT NULL,
  `usage_mode` varchar(100) NOT NULL,
  `land_mark` varchar(150) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Temporary view structure for view `student_view`
--

DROP TABLE IF EXISTS `student_view`;
/*!50001 DROP VIEW IF EXISTS `student_view`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `student_view` AS SELECT 
 1 AS `stdId`,
 1 AS `classId`,
 1 AS `section`,
 1 AS `rteid`,
 1 AS `admission_type`,
 1 AS `admission_status`,
 1 AS `roll_no`,
 1 AS `admission_no`,
 1 AS `nationality`,
 1 AS `religion`,
 1 AS `category`,
 1 AS `student_mother_tongue`,
 1 AS `student_contact_no`,
 1 AS `birth_taluk`,
 1 AS `birth_district`,
 1 AS `caste`,
 1 AS `std_aadhar_number`,
 1 AS `student_house`,
 1 AS `Student_Name`,
 1 AS `date_of_joining`,
 1 AS `dob`,
 1 AS `gender`,
 1 AS `class_name`,
 1 AS `section_name`,
 1 AS `father_Name`,
 1 AS `father_qualification`,
 1 AS `father_occupation`,
 1 AS `father_mobile_no`,
 1 AS `father_aadhar_no`,
 1 AS `father_annual_income`,
 1 AS `father_company`,
 1 AS `father_mother_tongue`,
 1 AS `Mother_Name`,
 1 AS `mother_qualification`,
 1 AS `mother_occupation`,
 1 AS `mother_mobile_number`,
 1 AS `mother_aadhar_number`,
 1 AS `mother_annual_income`,
 1 AS `mother_company`,
 1 AS `mother_mother_tongue`,
 1 AS `student_email`,
 1 AS `father_email`,
 1 AS `mother_email`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `student_year`
--

DROP TABLE IF EXISTS `student_year`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_year` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `roll_no` tinyint(4) NOT NULL,
  `class_id` int(11) NOT NULL,
  `class_section_id` int(11) DEFAULT NULL COMMENT 'class_section table id',
  `boarding` tinyint(4) DEFAULT NULL COMMENT 'See school config for definition',
  `picture_url` varchar(100) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `admission_type` tinyint(4) NOT NULL COMMENT 'See school config for definition',
  `board` tinyint(4) DEFAULT NULL COMMENT 'See school config for definition',
  `medium` tinyint(4) DEFAULT NULL COMMENT 'See school config for definition',
  `donor` varchar(200) DEFAULT NULL,
  `student_house` varchar(50) DEFAULT NULL,
  `previous_class_id` int(11) DEFAULT NULL,
  `previous_class_section_id` int(11) DEFAULT NULL,
  `fee_mode` varchar(45) NOT NULL DEFAULT 'auto' COMMENT '''auto'' or ''manual''',
  `student_admission_id` int(11) NOT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  `promotion_status` varchar(45) NOT NULL DEFAULT 'STUDYING',
  `promoted_by` int(11) DEFAULT NULL,
  `has_transport_km` varchar(255) DEFAULT NULL,
  `stop` int(11) DEFAULT NULL,
  `pickup_mode` varchar(255) DEFAULT NULL,
  `after_school_sport` varchar(255) DEFAULT NULL,
  `after_school_sport_days` int(11) DEFAULT NULL,
  `high_quality_picture_url` varchar(255) DEFAULT NULL,
  `combination` varchar(100) DEFAULT NULL,
  `status_modified_by` int(11) DEFAULT NULL,
  `status_modified_on` datetime DEFAULT NULL,
  `terminate_date` date DEFAULT NULL,
  `terminate_remarks` varchar(255) DEFAULT NULL,
  `tc_number` varchar(100) DEFAULT NULL,
  `profile_confirmed` char(3) DEFAULT 'No',
  `profile_confirmed_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subject_elective_group`
--

DROP TABLE IF EXISTS `subject_elective_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subject_elective_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='master table, table to store subject elective groups.';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subjects`
--

DROP TABLE IF EXISTS `subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subjects` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `class_id` int(11) NOT NULL,
  `subject_code` varchar(10) NOT NULL,
  `short_name` varchar(25) NOT NULL,
  `long_name` varchar(50) NOT NULL,
  `description` varchar(150) NOT NULL,
  `type` varchar(75) NOT NULL COMMENT '0: scholastic-language, 1: scholastic-coresubject, 2: non-scholastic',
  `is_activity` tinyint(1) NOT NULL,
  `is_physical` tinyint(1) NOT NULL,
  `has_skills` tinyint(1) NOT NULL COMMENT 'whether the subject has sub skills?',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `display_color` varchar(20) NOT NULL,
  `is_multisection` tinyint(1) NOT NULL,
  `multisection_id` int(11) DEFAULT NULL,
  `is_elective` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `class_id` (`class_id`),
  CONSTRAINT `subjects_ibfk_1` FOREIGN KEY (`class_id`) REFERENCES `class` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subjects_skills`
--

DROP TABLE IF EXISTS `subjects_skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subjects_skills` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` bigint(11) NOT NULL,
  `name` varchar(20) NOT NULL,
  `sub_skills` varchar(150) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subjects_sub_skills`
--

DROP TABLE IF EXISTS `subjects_sub_skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subjects_sub_skills` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `skill_id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `assessment_type` smallint(5) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `skill_id` (`skill_id`),
  CONSTRAINT `subjects_sub_skills_ibfk_1` FOREIGN KEY (`skill_id`) REFERENCES `subjects_skills` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `substitute_dd`
--

DROP TABLE IF EXISTS `substitute_dd`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `substitute_dd` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sub_date` date NOT NULL,
  `week_day` int(11) NOT NULL,
  `class_section_id` int(11) NOT NULL,
  `sub_staff_id` int(11) NOT NULL,
  `origin_type` varchar(75) NOT NULL COMMENT 'Can be Leave, Competition, Event, Request, Duties, etc.',
  `origin_id` int(11) NOT NULL COMMENT 'origin type id',
  `status` varchar(25) NOT NULL,
  `new_staff_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `sub_tta_id` int(11) NOT NULL,
  `sub_pseqeb` tinyint(4) NOT NULL,
  `new_pseqeb` tinyint(4) DEFAULT NULL,
  `sub_staff_id_json` varchar(255) DEFAULT NULL,
  `origin_mode` varchar(45) NOT NULL COMMENT 'Can be Staff OR Period. Period can happen when this got created due to a swap.',
  `substitute_type` varchar(45) NOT NULL COMMENT 'Can be ''Staff'' or ''Period''.',
  `new_tta_id` int(11) DEFAULT NULL,
  `new_period_display` varchar(60) DEFAULT NULL COMMENT 'Is used only when substitute_type is ''Period''',
  `new_staff_id_json` varchar(255) DEFAULT NULL,
  `sub_period_display` varchar(150) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `substitute_input_status`
--

DROP TABLE IF EXISTS `substitute_input_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `substitute_input_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `source` varchar(50) NOT NULL,
  `source_id` int(11) NOT NULL,
  `sub_date` date NOT NULL,
  `staff_id` int(11) NOT NULL,
  `status` varchar(45) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `substitute_output`
--

DROP TABLE IF EXISTS `substitute_output`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `substitute_output` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `substitute_input_id` int(11) DEFAULT NULL,
  `req_id` int(11) DEFAULT NULL,
  `old_staff_id` int(11) NOT NULL,
  `new_staff_id` int(11) NOT NULL,
  `tta_id` int(11) NOT NULL,
  `sub_type` varchar(50) NOT NULL COMMENT 'request or substitute',
  `fulfill_type` tinyint(11) DEFAULT NULL COMMENT '1: provided on substitute, 2: provided on activity, 3: provided on diff class swap, 4: provided on force, 5: provided on same class swap',
  `class_section_id` int(11) NOT NULL,
  `period_seq_excl_break` tinyint(4) NOT NULL,
  `week_day` int(11) NOT NULL,
  `sub_date` date NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tasks_todo`
--

DROP TABLE IF EXISTS `tasks_todo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tasks_todo` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `date` date NOT NULL,
  `time` varchar(20) NOT NULL,
  `status` tinyint(1) NOT NULL COMMENT '1: complete  0 : incomplete',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `staff_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `text_sent_to`
--

DROP TABLE IF EXISTS `text_sent_to`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `text_sent_to` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `texting_master_id` int(11) NOT NULL,
  `stakeholder_id` int(11) NOT NULL,
  `mobile_no` varchar(15) DEFAULT NULL,
  `mode` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1: notification, 2:  sms',
  `status` varchar(100) NOT NULL,
  `response_id` int(11) DEFAULT NULL,
  `avatar_type` int(4) NOT NULL,
  `is_read` tinyint(4) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `texting_credits_usage`
--

DROP TABLE IF EXISTS `texting_credits_usage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `texting_credits_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action` varchar(45) NOT NULL,
  `action_by` int(11) NOT NULL,
  `texting_master_id` int(11) DEFAULT NULL,
  `sms_credits` int(11) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `action_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `texting_groups`
--

DROP TABLE IF EXISTS `texting_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `texting_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL,
  `group_json` text NOT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(2) DEFAULT NULL,
  `acad_year_id` int(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `texting_master`
--

DROP TABLE IF EXISTS `texting_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `texting_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `sent_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `sent_by` int(11) NOT NULL,
  `reciever` text NOT NULL,
  `source` varchar(100) NOT NULL,
  `visible` tinyint(4) NOT NULL DEFAULT '1',
  `mode` varchar(45) NOT NULL COMMENT 'notification_sms / sms / notification',
  `acad_year_id` int(11) NOT NULL,
  `text_count` int(11) NOT NULL,
  `sms_credits` int(11) NOT NULL,
  `msg_id` varchar(100) DEFAULT NULL,
  `is_unicode` tinyint(4) NOT NULL DEFAULT '0',
  `sender_list` text,
  `sending_status` varchar(20) NOT NULL DEFAULT 'Initiated',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `texting_templates`
--

DROP TABLE IF EXISTS `texting_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `texting_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) DEFAULT NULL,
  `template_content` text,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `is_approved` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: No, 1: Yes',
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ticketing_category`
--

DROP TABLE IF EXISTS `ticketing_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ticketing_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `default_assignee_type` varchar(100) NOT NULL,
  `default_assignee_other_staff_id` int(11) DEFAULT NULL,
  `escalation_level_1_type` varchar(45) DEFAULT NULL,
  `escalation_level_2_type` varchar(45) DEFAULT NULL,
  `escalation_level_3_type` varchar(45) DEFAULT NULL,
  `escalation_level_4_type` varchar(45) DEFAULT NULL,
  `no_of_escalation_levels` tinyint(4) NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ticketing_history`
--

DROP TABLE IF EXISTS `ticketing_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ticketing_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) DEFAULT NULL,
  `action_by_staff_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `action_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ticketing_master`
--

DROP TABLE IF EXISTS `ticketing_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ticketing_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `issue_type` varchar(100) NOT NULL,
  `student_id` int(11) NOT NULL,
  `student_cs_id` int(11) NOT NULL,
  `escalation_level` tinyint(4) NOT NULL,
  `status` varchar(100) NOT NULL,
  `assigned_to` int(11) NOT NULL,
  `comments` text,
  `category_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ticket_number` varchar(45) NOT NULL,
  `attachments` mediumtext,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_allocated`
--

DROP TABLE IF EXISTS `timetable_allocated`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `timetable_allocated` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_period_id` int(11) NOT NULL,
  `class_section_id` int(11) NOT NULL,
  `week_day` tinyint(4) NOT NULL,
  `short_name` varchar(10) NOT NULL,
  `period_type` tinyint(4) NOT NULL,
  `period_seq` tinyint(4) NOT NULL,
  `period_seq_excl_break` tinyint(4) NOT NULL,
  `sss_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `acad_year_id` int(11) NOT NULL DEFAULT '18',
  PRIMARY KEY (`id`),
  KEY `period_id` (`class_period_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_override`
--

DROP TABLE IF EXISTS `timetable_override`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `timetable_override` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `date` date NOT NULL,
  `status` tinyint(4) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_override_periods`
--

DROP TABLE IF EXISTS `timetable_override_periods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `timetable_override_periods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `to_id` int(11) NOT NULL,
  `class_section_id` int(11) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `week_day` tinyint(4) DEFAULT NULL,
  `period_seq_excl_break` int(11) NOT NULL,
  `period_seq` int(11) NOT NULL,
  `period_short_name` varchar(100) NOT NULL,
  `period_long_name` varchar(255) NOT NULL,
  `subject_name` varchar(255) NOT NULL,
  `period_type` int(11) NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_override_periods_staff`
--

DROP TABLE IF EXISTS `timetable_override_periods_staff`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `timetable_override_periods_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `top_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_template`
--

DROP TABLE IF EXISTS `timetable_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `timetable_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_template_class_section`
--

DROP TABLE IF EXISTS `timetable_template_class_section`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `timetable_template_class_section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `timetable_template_id` int(11) NOT NULL,
  `class_section_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timetable_template_periods`
--

DROP TABLE IF EXISTS `timetable_template_periods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `timetable_template_periods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ttt_id` int(11) NOT NULL,
  `week_day` tinyint(4) NOT NULL COMMENT '1: Mon, 2: Tue, 3: Wed, 4: Thu, 5: Fri, 6: Sat, 7: Sun',
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `long_name` varchar(25) NOT NULL,
  `short_name` varchar(50) NOT NULL,
  `period_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: Normal period, 1: First Period 2: Last Period, 3: Short Break, 4: Long Break, 5: Second Period, 6: Period After Long Break',
  `period_seq` tinyint(4) NOT NULL COMMENT 'Used to denote the sequence of periods in a given day',
  `period_seq_excl_break` tinyint(4) NOT NULL COMMENT 'Used for staff timetable',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `class_section_id` (`ttt_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tracker_data`
--

DROP TABLE IF EXISTS `tracker_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tracker_data` (
  `id` int(11) NOT NULL,
  `schoolId` int(11) NOT NULL,
  `readerId` int(11) NOT NULL,
  `time` datetime NOT NULL,
  `cardId` int(11) NOT NULL,
  `temp` int(11) NOT NULL,
  `calories` int(11) NOT NULL,
  `steps` int(11) NOT NULL,
  `batteryVoltage` decimal(1,1) NOT NULL,
  `signalStrength` int(11) NOT NULL,
  `flag` tinyint(4) NOT NULL DEFAULT '1',
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tracker_student`
--

DROP TABLE IF EXISTS `tracker_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tracker_student` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `cardId` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tracking_master`
--

DROP TABLE IF EXISTS `tracking_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tracking_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `data` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `transport_fee_structure`
--

DROP TABLE IF EXISTS `transport_fee_structure`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transport_fee_structure` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stop_id` int(11) NOT NULL,
  `pickup_mode` varchar(100) NOT NULL,
  `amount` decimal(15,0) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gprs` decimal(10,0) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `transport_stops`
--

DROP TABLE IF EXISTS `transport_stops`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transport_stops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(150) NOT NULL DEFAULT 'Select Stop',
  `kilometer` float NOT NULL,
  `time` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tx_attendance`
--

DROP TABLE IF EXISTS `tx_attendance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tx_attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rfid` varchar(255) NOT NULL,
  `journey_id` int(11) NOT NULL,
  `journey_type` varchar(50) NOT NULL COMMENT 'pick_house, pick_school, drop_house, drop_school',
  `thing_id` int(11) NOT NULL,
  `latitude` varchar(50) DEFAULT NULL,
  `longitude` varchar(50) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tx_drivers`
--

DROP TABLE IF EXISTS `tx_drivers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tx_drivers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `phone_number` varchar(20) NOT NULL,
  `driver_licence` varchar(255) NOT NULL,
  `alternative_number` varchar(20) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0: Inactive 1: Active',
  `attender_name` varchar(255) DEFAULT NULL,
  `attender_number` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tx_journey_stops`
--

DROP TABLE IF EXISTS `tx_journey_stops`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tx_journey_stops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `journey_id` int(11) NOT NULL,
  `stop_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tx_journeys`
--

DROP TABLE IF EXISTS `tx_journeys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tx_journeys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `journey_name` varchar(255) NOT NULL,
  `thing_id` int(11) NOT NULL,
  `tentative_start_time` time DEFAULT NULL,
  `tentative_end_time` time DEFAULT NULL,
  `journey_type` varchar(50) NOT NULL,
  `days` text NOT NULL,
  `refresh_required` tinyint(4) NOT NULL DEFAULT '0',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0: Inactive, 1: Active',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tx_logs`
--

DROP TABLE IF EXISTS `tx_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tx_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `journey_id` int(11) NOT NULL,
  `journey_data` tinytext NOT NULL,
  `event` tinytext,
  `notifications_sent_to` text,
  `notification_json` text,
  `notification_response` text,
  `event_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `event_type` varchar(50) DEFAULT NULL,
  `thing_id` int(11) DEFAULT NULL,
  `stop_id` int(11) DEFAULT NULL,
  `message` tinytext,
  `student_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tx_mismatch_journeys`
--

DROP TABLE IF EXISTS `tx_mismatch_journeys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tx_mismatch_journeys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `actual_journey_id` int(11) DEFAULT NULL,
  `stored_journey_id` int(11) DEFAULT NULL,
  `journey_type` varchar(45) DEFAULT NULL,
  `student_id` int(11) NOT NULL,
  `remarks` varchar(255) DEFAULT NULL,
  `frequency` int(11) DEFAULT NULL,
  `resolved` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tx_staff_journeys`
--

DROP TABLE IF EXISTS `tx_staff_journeys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tx_staff_journeys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `journey_id` int(11) NOT NULL,
  `stop_id` int(11) NOT NULL,
  `day` varchar(15) NOT NULL,
  `journey_type` varchar(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tx_stops`
--

DROP TABLE IF EXISTS `tx_stops`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tx_stops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stop_name` varchar(255) NOT NULL,
  `distance_to_stop` decimal(10,2) DEFAULT NULL,
  `tentative_reach_time` time NOT NULL,
  `landmark` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `order` int(5) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tx_student_journeys`
--

DROP TABLE IF EXISTS `tx_student_journeys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tx_student_journeys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `entity_type` varchar(50) NOT NULL DEFAULT 'Student' COMMENT 'Student, Staff',
  `entity_source_id` int(11) NOT NULL COMMENT 'student_id or staff_id',
  `journey_id` int(11) NOT NULL,
  `stop_id` int(11) NOT NULL,
  `day` varchar(20) NOT NULL,
  `journey_type` varchar(45) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tx_student_journeys_override`
--

DROP TABLE IF EXISTS `tx_student_journeys_override`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tx_student_journeys_override` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `journey_id` int(11) NOT NULL,
  `stop_id` int(11) NOT NULL,
  `from_date` date NOT NULL,
  `to_date` date NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `journey_type` varchar(45) NOT NULL,
  `type` varchar(45) NOT NULL,
  `day` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tx_things`
--

DROP TABLE IF EXISTS `tx_things`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tx_things` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `thing_name` varchar(255) NOT NULL,
  `thing_reg_number` varchar(20) NOT NULL,
  `driver_id` int(11) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `tracking_url` text,
  `refresh_required` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_login_history`
--

DROP TABLE IF EXISTS `user_login_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_login_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `action` varchar(25) NOT NULL COMMENT 'Logout/login/etc',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_prov_login_att`
--

DROP TABLE IF EXISTS `user_prov_login_att`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_prov_login_att` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL,
  `attempts` int(11) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `communication_type` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `username` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `salt` varchar(255) DEFAULT NULL,
  `email` varchar(254) NOT NULL,
  `activation_code` varchar(40) DEFAULT NULL,
  `forgotten_password_code` varchar(40) DEFAULT NULL,
  `forgotten_password_time` int(11) unsigned DEFAULT NULL,
  `remember_code` varchar(40) DEFAULT NULL,
  `created_on` int(11) unsigned NOT NULL,
  `last_login` int(11) unsigned DEFAULT NULL,
  `active` tinyint(1) unsigned DEFAULT NULL,
  `phone_number` varchar(15) NOT NULL,
  `freeze_username` tinyint(1) NOT NULL DEFAULT '0',
  `donot_show` tinyint(1) NOT NULL DEFAULT '0',
  `loggedin_atleast_once` tinyint(1) NOT NULL DEFAULT '0',
  `restore_password` varchar(255) DEFAULT NULL,
  `token` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username_UNIQUE` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `variant`
--

DROP TABLE IF EXISTS `variant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `variant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) NOT NULL,
  `item_name` varchar(100) NOT NULL,
  `item_type` varchar(100) NOT NULL,
  `var_option` varchar(20) NOT NULL COMMENT 'stop_id or class_id or sizes ',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `variant_qty`
--

DROP TABLE IF EXISTS `variant_qty`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `variant_qty` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `variant_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `vehicle_data`
--

DROP TABLE IF EXISTS `vehicle_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `vehicle_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tax_paid_upto` date NOT NULL,
  `emission_expiry_date` date NOT NULL,
  `routeNo` int(11) NOT NULL,
  `permiteNo` varchar(100) NOT NULL,
  `fcNo` varchar(100) NOT NULL,
  `insuranceNo` varchar(100) NOT NULL,
  `rc_certificateNo` varchar(100) NOT NULL,
  `permite_expiry_date` date NOT NULL,
  `fcNo_date` date NOT NULL,
  `insuranceNo_date` date NOT NULL,
  `rc_certificateNo_date` date NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `vendor_address_info`
--

DROP TABLE IF EXISTS `vendor_address_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `vendor_address_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `address_type` tinyint(4) NOT NULL,
  `address_line1` varchar(255) NOT NULL,
  `address_line2` varchar(255) NOT NULL,
  `area` varchar(100) NOT NULL,
  `district` varchar(100) NOT NULL,
  `state` varchar(100) NOT NULL,
  `country` varchar(100) NOT NULL,
  `pin_code` varchar(10) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `vendor_master`
--

DROP TABLE IF EXISTS `vendor_master`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `vendor_master` (
  `vendor_id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_code` varchar(100) NOT NULL,
  `vendor_name` varchar(255) NOT NULL,
  `gst_no` varchar(255) NOT NULL,
  `vendor_email` varchar(100) NOT NULL,
  `vendor_website` varchar(255) NOT NULL,
  `contact_first_name` varchar(255) NOT NULL,
  `contact_last_name` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `contact_no` varchar(20) NOT NULL,
  `customer_service_no` varchar(20) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`vendor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `vendor_products`
--

DROP TABLE IF EXISTS `vendor_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `vendor_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `product_variant_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `visitor_info`
--

DROP TABLE IF EXISTS `visitor_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `visitor_info` (
  `id` int(15) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `email` varchar(50) NOT NULL,
  `mobile` varchar(50) NOT NULL,
  `coming_from` varchar(50) NOT NULL,
  `check_in` timestamp NULL DEFAULT NULL,
  `duty_app` int(11) NOT NULL,
  `reason` varchar(150) NOT NULL,
  `check_out` timestamp NULL DEFAULT NULL,
  `visitor_img` varchar(255) NOT NULL,
  `tomeet_user_type` varchar(45) DEFAULT NULL,
  `pass_code` varchar(250) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Add Data
--
INSERT INTO `users` (`id`, `ip_address`, `username`, `password`, `salt`, `email`, `activation_code`, `forgotten_password_code`, `forgotten_password_time`, `remember_code`, `created_on`, `last_login`, `active`, `phone_number`, `freeze_username`, `donot_show`) VALUES (NULL, '127.0.0.1', 'admin', '$2y$08$up/8GwPRRGeVJzZzGnTpmOm57t6vFm9iiSjY3GTMWoDeF/Zlcfv1e', '', '<EMAIL>', '', NULL, NULL, NULL, '1268889823', '1551510464', '1', '', '0', '0');
INSERT INTO `avatar` (`id`, `user_id`, `avatar_type`, `stakeholder_id`, `friendly_name`, `created_on`, `modified_on`, `last_modified_by`, `old_user_id`) VALUES (NULL, '1', '3', '0', 'Super Admin', '2018-03-09 00:52:44', '2018-03-09 00:52:44', '0', NULL);
INSERT INTO `academic_year` VALUES (1,'2001-02',0,NULL),(2,'2002-03',0,NULL),(3,'2003-04',0,NULL),(4,'2004-05',0,NULL),(5,'2005-06',0,NULL),(6,'2006-07',0,NULL),(7,'2007-08',0,NULL),(8,'2008-09',0,NULL),(9,'2009-10',0,NULL),(10,'2010-11',0,NULL),(11,'2011-12',0,NULL),(12,'2012-13',0,NULL),(13,'2013-14',0,NULL),(14,'2014-15',0,NULL),(15,'2015-16',0,NULL),(16,'2016-17',0,NULL),(17,'2017-18',0,1),(18,'2018-19',1,1),(19,'2019-20',0,1),(20,'2020-21',0,1),(21,'2021-22',0,1),(22,'2022-23',0,1),(23,'2023-24',0,1),(24,'2024-25',0,1),(25,'2025-26',0,1),(26,'2026-27',0,1),(27,'2027-28',0,1);
INSERT INTO `privileges` VALUES (1,'STUDENT','',1,1),(2,'STUDENT_MASSUPDATE','',1,1),(3,'STUDENT_OBSERVATION','',1,1),(4,'STUDENT_PROFILE','',1,1),(5,'STUDENT_PROMOTION','',1,1),(6,'STAFF','',1,1),(7,'STAFF_OBSERVATION','',1,1),(10,'COMMUNICATION','',1,1),(11,'FEESV2','',1,1),(12,'TIMETABLE','',1,1),(13,'SUBSTITUTION','',1,1),(14,'ACTIVITY','',1,0),(15,'ADDITIONAL_INCOME','',1,0),(16,'BUILDING_MASTER','',1,1),(18,'COMPETITION','',1,0),(19,'EXAMINATION','',1,1),(20,'EXPENSE','',1,1),(21,'GALLERY','',1,1),(22,'HOMEWORK','',1,0),(23,'INVENTORY','',1,1),(24,'LIBRARY','',1,1),(25,'MANAGEMENT','',1,1),(26,'PAYROLL','',1,1),(27,'PURCHASE','',1,0),(28,'ROOM_BOOKING','',1,0),(29,'SCHOOL_CALENDAR','',1,1),(31,'STUDENT_ATTENDANCE','',1,1),(32,'TODO','',1,0),(33,'TRANSPORTATION','',1,1),(34,'UPCOMING_BIRTHDAY','',1,1),(35,'VISITOR','',1,0),(36,'STAFF_PROFILE','',1,1),(37,'ASSETS','',1,0),(38,'FLASH_NEWS','',1,1),(39,'LEAVE','',1,1),(40,'USER_MANAGEMENT','',1,1),(41,'STAFF_MASSUPDATE','',1,1),(42,'PARENT_TICKETING','',1,1),(43,'TEXTING','',1,1),(44,'SALES','',1,0),(45,'CIRCULARV2','',1,1),(46,'SCHOOL','',1,1),(47,'AFL','',1,1),(48,'VENDOR','',1,0),(49,'ADMISSION','',1,1),(50,'ENQUIRY','',1,1),(51,'EVENT','',1,1),(52,'BIRTHDAY_SMS_SEND','',1,0),(53,'ACADEMICS','',1,1),(54,'STUDENT_TASKS','',1,1),(55,'WIDGET','',1,1),(56,'EMAILS','',1,1),(57,'ONLINE_CLASS','',1,1),(58,'VIRTUAL_CLASSROOM','',1,0),(59,'IDCARDS','',1,0),(60,'ELIBRARY','',1,1),(61,'LESSON_PLAN','',1,1),(62,'ONLINE_CLASS_V2','',1,1),(63,'QUESTION_BANK','',1,0),(64,'STAFF_ATTENDANCE','',1,1),(65,'STUDENT_ATTENDANCE_V2','',1,1),(66,'SUBJECT','',1,1),(68,'OTHERLINKS','',1,1),(69,'WALLET','',1,1),(70,'MSM','',1,1),(71,'STAFF_TASKS_BASKET','',1,1),(72,'STUDENT_NONCOMPLIANCE','',1,1),(73,'STUDENT_WALLET','',1,1),(74,'DONATION','',1,1),(75,'INTERNAL_TICKETING','',1,1),(76,'PROCUREMENT','',1,1),(77,'INDENT','',1,1),(78,'PROCUREMENT_VENDORS','',1,1),(79,'PROCUREMENT_ASSETS','',1,1),(80,'PROCUREMENT_INVENTORY','',1,1),(81,'PROCUREMENT_SALES','',1,1),(82,'STAFF_360_DEGREE','',1,1),(83,'STUDENT_360','',1,1),(84,'INFIRMARY','',1,1);
INSERT INTO `privileges_sub` VALUES (1,'ADDRESS_CRUD','',1,1,0),(2,'HEALTH_CRUD','',1,1,0),(3,'BASIC_VIEW','',1,1,0),(4,'DETAIL_VIEW','',1,1,0),(5,'MODULE','',1,1,0),(6,'COMMUNICATION_CRUD','',1,1,0),(7,'FULL_INFO_CRUD','',1,1,0),(8,'NEW_STUDENT_ADD','',1,1,0),(9,'MOTHER_TONGUE_CRUD','',1,1,0),(10,'MOTHER_TONGUE_CRUD_IF_CT','',1,1,0),(11,'HEALTH_CRUD_IF_CT','',1,1,0),(12,'ADDRESS_CRUD_IF_CT','',1,1,0),(13,'COMMUNICATION_CRUD_IF_CT','',1,1,0),(14,'DETAIL_VIEW_IF_CT','',1,1,0),(15,'VIEW_PRINT_CERTIFICATES','',1,1,0),(16,'ADD_SIBLINGS','',1,1,0),(17,'VIEW_SMS_REPORT','',1,1,0),(18,'VIEW_REPORT','',1,1,0),(19,'MODULE','',2,1,0),(25,'VIEW','',4,1,0),(26,'VIEW_ACTIVITY_DETAILS','',4,1,0),(27,'VIEW_EMERGENCY_INFO','',4,1,0),(28,'VIEW_FEES_DETAILS','',4,1,0),(29,'VIEW_HEALTH_DETAILS','',4,1,0),(30,'VIEW_PERSONAL_DETAILS','',4,1,0),(31,'VIEW_SCHOOL_DETAILS','',4,1,0),(32,'VIEW_TRANSPORT_DETAILS','',4,1,0),(33,'MODULE','',5,1,0),(34,'ADD_EDIT_DELETE','',6,1,0),(35,'VIEW_DETAILS','',6,1,0),(36,'VIEW','',6,1,0),(37,'MODULE','',6,1,0),(38,'VIEW_SMS_REPORT','',6,1,0),(39,'VIEW_REPORT','',6,1,0),(40,'CREATE','',7,1,0),(41,'VIEW_SUMMARY','',7,1,0),(42,'MODULE','',7,1,0),(43,'VIEW_ALL','',7,1,0),(44,'VIEW','',7,1,0),(51,'MODULE','',10,1,0),(52,'MODULE','',11,1,0),(55,'COLLECT_FEES','',11,1,0),(56,'VIEW_DAILY_TX_REPORT','',11,1,0),(57,'VIEW_BALANCE_REPORT','',11,1,0),(58,'VIEW_ONLINE_TX_REPORT','',11,1,0),(59,'ASSIGN_INVOICE','',11,1,0),(60,'VIEW_CONCESSIONS','',11,1,0),(61,'MY_TIMETABLE','',12,1,0),(63,'MODULE','',12,1,0),(87,'MODULE','',19,1,0),(88,'CREATE','',19,1,0),(89,'ACCESS_CONTROL','',19,1,0),(90,'PERFORMANCE_ANALYSIS','',19,1,0),(91,'CLASS_REPORT','',19,1,0),(92,'STUDENT_REPORT','',19,1,0),(93,'UNLOCK_MARKS_ENTRY','',19,1,0),(94,'ADD_REMARKS_CT','',19,1,0),(95,'CONSOLIDATION','',19,1,0),(96,'VERIFY_MARKS_CARDS','',19,1,0),(97,'ADD_REMARKS_ALL','',19,1,0),(98,'VERIFY_REMARKS','',19,1,0),(99,'EXAM_ADMIN','',19,1,0),(100,'REGENERATE_MARKSCARDS','',19,1,0),(106,'FEE_SUMMARY','',11,1,0),(107,'NON_RECONCILED','',11,1,0),(108,'CLASS_WISE_REPORT','',11,1,0),(110,'MODULE','',21,1,0),(115,'MODULE','',25,1,0),(123,'MODULE','',29,1,0),(124,'VIEW_QUALIFICATION','',6,1,0),(148,'VIEW','',34,1,0),(149,'VIEW_STAFF','',34,1,0),(153,'VIEW','',36,1,0),(155,'FAST_COLLECTION','',11,1,0),(156,'MODULE','',38,1,0),(157,'TRANSPORTATION_CRUD','',11,1,0),(159,'VIEW_CLASS_REPORT','',1,1,0),(167,'VIEW_EDIT_DELETE','',21,1,0),(168,'WIDGET','',11,1,0),(169,'MODULE','',40,1,0),(170,'PROVISION_PARENTS','',40,1,0),(171,'PROVISION_STAFF','',40,1,0),(172,'CONNECT_SIBLINGS','',40,1,0),(173,'ASSIGN_ROLES_AND_PRIVILEGES','',40,1,0),(174,'MODULE','',41,1,0),(176,'MODULE','',42,1,0),(178,'SEND','',43,1,0),(179,'REPORT','',43,1,0),(180,'MODULE','',45,1,0),(181,'CREATE','',45,1,0),(182,'VIEW_ALL','',45,1,0),(183,'REPORT','',45,1,0),(185,'CRUD_CATEGORY','',42,1,0),(186,'SCHOOL_DETAILS','',1,1,0),(187,'DOCUMENTS','',1,1,0),(204,'CANCELED_REPORT','',11,1,0),(213,'CREATE_ELECTIVES','',19,1,0),(214,'RANKING_REPORT','',19,1,0),(215,'SOFT_DELETE_RECEIPTS','',11,1,0),(216,'RECEIPT_CHANGE','',11,1,0),(223,'ATTENDANCE','',6,1,0),(224,'EMAIL_TEMPLATE','',10,1,0),(225,'SUBJECT_REMARKS','',19,1,0),(229,'ACTIVATE_PARENTS','',40,1,0),(230,'ACTIVATE_PARENTS_REPORTS','',40,1,0),(250,'CONSOLIDATED_FEE_REPORT','',11,1,0),(251,'CLASS_WISE_DAILY_REPORT','',11,1,0),(253,'SMS_TEMPLATE','',10,1,0),(255,'FINE_AMOUNT_ASSIGN','',11,1,0),(262,'TEMP_PASSWORD_RESET','',1,1,0),(264,'APPROVER','',45,1,0),(265,'VIEW_TOTAL_CLASS_WISE_STUDENT_REPORT','',1,1,0),(267,'MODULE','',52,1,0),(268,'CONCESSIONS_ASSIGN','',11,1,0),(269,'GUARDIAN_INFO_CRUD','',1,1,0),(270,'VIEW_ALUMNI_LIST','',1,1,0),(271,'DAY_BOOK_APS','',11,1,0),(272,'MODULE','',54,1,0),(273,'TASKS_ADMIN','',54,1,0),(274,'MODULE','',53,1,0),(277,'STAFF_DATA','',55,1,0),(278,'STUDENT_DATA','',55,1,0),(279,'TASK_ASSIGNED','',55,1,0),(280,'TASK_SUMMARY','',55,1,0),(281,'FEE_COLLECTION_TOTAL','',55,1,0),(282,'FEE_COLLECTION_TREND','',55,1,0),(283,'REPORT','',10,1,0),(284,'GROUP_CREATION','',10,1,0),(285,'STUDENT_COUNT','',55,1,0),(286,'SMS_TEMPLATE_APPROVAL','',10,1,0),(290,'VIEW_DAILY_TX_REPORT_PRARTHANA','',11,1,0),(291,'REFUND','',11,1,0),(297,'EXIT_STUDENT','',1,1,0),(298,'CATEGORY_REPORT','',1,1,0),(305,'COMMUNICATION_TREND','',55,1,0),(306,'VIEW_MARKS_CARD_ALL','',19,1,0),(307,'VIEW_MARKS_CARD_CT','',19,1,0),(308,'MODULE','',60,1,0),(310,'FINE_AMOUNT_WAIVER','',11,1,0),(311,'FEES_COLLECTION_STATUS','',11,1,0),(312,'DAY_BOOK_APS_COM','',11,1,0),(313,'RE_GENERATE_PDF_RECEIPTS','',11,1,0),(314,'REFUND_REPORTS','',11,1,0),(315,'MANAGEMENT_SUMMARY','',11,1,0),(316,'ADJUSTMENT_REPORT','',11,1,0),(318,'MANAGE_SYLLABUS','',61,1,0),(320,'MODULE','',62,1,0),(321,'CREATE_SCHEDULE','',62,1,0),(322,'ADMIN','',62,1,0),(323,'REPORTS','',62,1,0),(325,'PROVISION_USER_LOGIN','',6,1,0),(326,'REPORTING_MANAGER_ADMIN','',6,1,0),(327,'CREATE_SHIFT','',64,1,0),(328,'ASSIGN_SHIFT','',64,1,0),(329,'MANAGE_ATTENDANCE','',64,1,0),(330,'ADMIN','',64,1,0),(331,'DAY_WISE_REPORT','',64,1,0),(332,'TEMP_DEACTIVATION','',1,1,0),(333,'ADJUST_FEE_AMOUNT','',1,1,0),(335,'CONNECT_SIBLINGS','',1,1,0),(336,'PROVISION_PARENTS_CREDENTIALS','',1,1,0),(337,'MANAGE_ONLINE_CREDENTIALS','',1,1,0),(338,'TAKE_ATTENDANCE','',65,1,0),(339,'REPORTS','',65,1,0),(340,'MASTER','',66,1,0),(341,'ELECTIVE_MASTER','',66,1,0),(342,'STUDENT_ELECTIVE','',66,1,0),(343,'SUBJECTS','',12,1,0),(344,'SETTINGS','',12,1,0),(345,'CREATE_TIMETABLE','',12,1,0),(346,'CLONE_TIMETABLE','',12,1,0),(347,'STAFF_TIMETABLE_REPORT','',12,1,0),(348,'STAFF_WORKLOAD_REPORT','',12,1,0),(349,'SECTION_TIMETABLE_REPORT','',12,1,0),(350,'ROOM_TIMETABLE_REPORT','',12,1,0),(358,'MY_TIMETABLE','',55,1,0),(359,'STUDENT_ATTENDANCE_V2_SUMMARY','',55,1,0),(360,'MODULE','',16,1,0),(361,'VIEW_MARKS_STATUS','',19,1,0),(363,'MODULE','',61,1,0),(364,'ASSIGN_FEES','',1,1,0),(365,'MODULE','',20,1,0),(366,'ADD_CATEGORY','',20,1,0),(367,'APPROVAL','',20,1,0),(368,'ADD_EXPENSE','',20,1,0),(369,'VIEW_REPORTS','',20,1,0),(370,'CANCELLED_VOUCHER','',20,1,0),(381,'MODULE','',3,1,0),(382,'ADD','',3,1,0),(383,'DEACTIVATE','',3,1,0),(384,'OBSERVATION_REPORT','',3,1,0),(385,'SECTION_SUMMARY_REPORT','',3,1,0),(386,'STUDENT_OBSERVATION_REPORT','',3,1,0),(387,'MANAGE_CATEGORIES','',3,1,0),(388,'MANAGE_CAPACITY','',3,1,0),(389,'MODULE','',35,1,0),(392,'MODULE','',39,1,0),(394,'STAFF_LEAVE_APPROVE','',39,1,0),(395,'STAFF_LEAVE_APPLY','',39,1,0),(396,'LEAVE_REPORT','',39,1,0),(397,'APPLY_LEAVE_FOR_OTHER_STAFF','',39,1,0),(398,'STAFF_LEAVE_ADMIN','',39,1,0),(399,'STAFF_LEAVE_CATEGORY','',39,1,0),(400,'STAFF_LEAVE_QUOTA','',39,1,0),(401,'MODULE','',49,1,0),(402,'REPORTS','',49,1,0),(403,'FIELD_SELECTION','',49,1,0),(404,'SETTINGS','',49,1,0),(405,'VIEW_EDIT_APPLICATION','',49,1,0),(406,'UPDATE_DETAILS','',49,1,0),(407,'APPROVED_SEND_SMS','',49,1,0),(408,'ONLINE_SETTLEMENT_REPORT','',49,1,0),(409,'ONLINE_TRANSACTION_REPORT','',49,1,0),(410,'MOVE_TO_ERP','',49,1,0),(411,'CREATE_OFFLINE_APPLICATION','',49,1,0),(412,'FOLLOWUP','',49,1,0),(413,'PRINT_EMPTY_APPLICATION','',49,1,0),(414,'VIEW_DETAILS','',49,1,0),(415,'MOVE_TO_ERP_INDIVIDUAL','',49,1,0),(416,'MODULE','',50,1,0),(417,'MODULE','',68,1,0),(418,'ADMIN','',68,1,0),(419,'RESEND_CIRCULAR','',10,1,0),(420,'MODULE','',56,1,0),(421,'SEND','',56,1,0),(422,'VIEW_ALL','',56,1,0),(423,'MODULE','',47,1,0),(424,'SUBJECTS','',47,1,0),(425,'ASSESSMENTS','',47,1,0),(426,'GRADING_SYSTEM','',47,1,0),(427,'RUBRICS','',47,1,0),(428,'MARKS','',47,1,0),(429,'ADMIN','',47,1,0),(430,'PERFORMANCE_POINTERS','',47,1,0),(431,'STUDENT_REPORT','',47,1,0),(432,'ADMIN','',60,1,0),(433,'OVERVIEW_ONLINE_SETTLEMENT_REPORT','',11,1,0),(434,'FEE_STUDENT_HISTORY','',11,1,0),(435,'TRANSPORTATION_REPORT','',11,1,0),(436,'FEE_AUDIT_LOG','',11,1,0),(437,'PREDEFINED_CONCESSION','',11,1,0),(438,'FEE_SUMMARY_REPORT','',11,1,0),(439,'EDIT_FEES_AMOUNT','',11,1,0),(440,'REFUND_TRANSACTION','',11,1,0),(441,'MODULE','',22,1,0),(442,'HOMEWORK_ADMIN','',22,1,0),(443,'QUESTION_BANK','',54,1,0),(444,'MODULE','',51,1,0),(445,'EVENT_CRUD','',51,1,0),(446,'EVENT_ATTENDANCE','',51,1,0),(447,'CREATE_EVENT','',51,1,0),(448,'VIEW_EVENT_DETAILS','',51,1,0),(449,'RETURN_SAFETY_DEPOSIT','',51,1,0),(450,'MODULE','',69,1,0),(451,'LOAD_MONEY','',69,1,0),(452,'VIEW_WALLET_DETAILS','',69,1,0),(453,'MODULE','',23,1,0),(454,'MODULE','',24,1,0),(455,'VIEW_EDIT_DELETE','',24,1,0),(456,'STAFF_VIEW','',24,1,0),(457,'BOOKS','',24,1,0),(458,'BORROW_RETURN','',24,1,0),(459,'FINE_COLLECTION','',24,1,0),(460,'BULK_CURCULATION','',24,1,0),(461,'DAMAGE_OR_LOST_BOOK','',24,1,0),(462,'STOCK_CHECK','',24,1,0),(463,'DAILY_TX_REPORT','',24,1,0),(464,'BOOK_BORROWED_REPORT','',24,1,0),(465,'FINE_DEFAULTER_REPORT','',24,1,0),(466,'STOCK_REPORT','',24,1,0),(467,'MEMBER_TX_REPORT','',24,1,0),(468,'GENERATE_QR_CODES','',24,1,0),(469,'ASSIGN_CARD','',24,1,0),(470,'BULK_ASSIGN_CARD','',24,1,0),(471,'CARD_MASTER','',24,1,0),(472,'BORROW_RETURN_RENEWAL','',24,1,0),(473,'MODULE','',70,1,0),(474,'MODULE','',57,1,0),(475,'CREATE_SCHEDULE','',57,1,0),(476,'ADMIN','',57,1,0),(477,'MODULE','',26,1,0),(478,'VIEW_MY_PAYSLIPS','',26,1,0),(479,'PAYROLL_ADMIN','',26,1,0),(480,'VIEW_REPORTS','',26,1,0),(481,'DISBURSEMENT','',26,1,0),(482,'PF_REPORTS','',26,1,0),(483,'PT_REPORTS','',26,1,0),(484,'TICKETING_ADMIN','',42,1,0),(485,'MODULE','',27,1,0),(486,'MODULE','',63,1,0),(487,'MODULE','',46,1,0),(488,'ACAD_YEAR_CHANGE','',46,1,0),(489,'STAFF_QR_CODES','',46,1,0),(490,'STUDENT_QR_CODES','',46,1,0),(491,'STUDENT_PHOTOS','',46,1,0),(492,'PARENT_QR_CODES','',46,1,0),(493,'CLASS_MASTER','',46,1,0),(494,'CLASS_MASTERV2','',46,1,0),(496,'PAYROLL_DETAILS','',6,1,0),(497,'LATE_REPORT','',64,1,0),(498,'INDIVIDUAL_STAFF_REPORT','',64,1,0),(499,'OVERRIDE_REPORT','',64,1,0),(500,'REDRIVE_ATTENDANCE','',64,1,0),(501,'MODULE','',71,1,0),(502,'TASKS_ADMIN','',71,1,0),(503,'ADD','',71,1,0),(504,'REPORTS','',71,1,0),(505,'VIEW_TRANSPORT_REPORT','',1,1,0),(506,'VIEW_VACCINATION_STATUS','',1,1,0),(507,'CATEGORY_WISE_STUDENT_REPORT','',1,1,0),(508,'FEE_PAID_REPORT','',1,1,0),(509,'MAP_RFID','',1,1,0),(510,'SHOW_ALL_SECTIONS','',31,1,0),(511,'EMERGENCY_EXIT','',31,1,0),(512,'TAKE','',31,1,0),(513,'HEALTH_CARE','',31,1,0),(514,'LATECOMER','',31,1,0),(516,'MODULE','',31,1,0),(517,'EDIT_ANY_DATE_ATTENDANCE','',31,1,0),(518,'VIEW_SUMMARY','',31,1,0),(519,'CLASS_ATTENDANCE_REPORT','',31,1,0),(520,'SPECIAL_CASE_REPORT','',31,1,0),(521,'MODULE','',72,1,0),(522,'ADD','',72,1,0),(523,'DEACTIVATE','',72,1,0),(524,'MANAGE_CATEGORIES','',72,1,0),(525,'MANAGE_PENALTY','',72,1,0),(526,'NONCOMPLIANCE_REPORT','',72,1,0),(527,'STUDENT_NONCOMPLIANCE_REPORT','',72,1,0),(528,'SECTION_SUMMARY_REPORT','',72,1,0),(529,'ADMIN','',72,1,0),(530,'MODULE','',13,1,0),(531,'MODULE','',33,1,0),(532,'BUSES','',33,1,0),(533,'ROUTES','',33,1,0),(534,'STOPS','',33,1,0),(535,'ENTITIES','',33,1,0),(536,'JOURNEYS','',33,1,0),(537,'DRIVERS','',33,1,0),(538,'TAKE_ATTENDANCE','',33,1,0),(539,'TX_REPORT','',33,1,0),(540,'ADMIN_CONSOLE','',33,1,0),(541,'TX_MISMATCH_REPORT','',33,1,0),(542,'TX_JOURNEY_CHANGES','',33,1,0),(543,'TX_DAILY_TRACK','',33,1,0),(544,'TX_STUDENT_WISE_REPORT','',33,1,0),(545,'TX_AVG_JOURNEY_TIME','',33,1,0),(546,'MODULE','',48,1,0),(547,'CRUD','',48,1,0),(548,'STUDENT_LEAVE_APPLY','',39,1,0),(549,'STUDENT_LEAVE_APPROVE','',39,1,0),(550,'STUDENT_LEAVE_ADMIN','',39,1,0),(551,'MODULE','',44,1,0),(552,'SOFT_DELETE_RECEIPTS','',44,1,0),(553,'CAN_ADMIT_REJECT','',49,1,0),(554,'COLLECT_OFFLINE_FEE','',49,1,0),(555,'SEAT_ALLOTMENT','',49,1,0),(556,'EDIT_APPLICATION_FORM','',49,1,0),(557,'MODULE','',59,1,0),(558,'MODULE','',73,1,0),(559,'PERMIT_ASSING_STOP','',46,1,0),(560,'MODULE','',74,1,0),(561,'DONATION_ADMIN','',74,1,0),(562,'ADD','',74,1,0),(563,'VIEW_REPORTS','',74,1,0),(564,'EXCESS_REPORT','',11,1,0),(565,'INVOICE_GENERATE','',11,1,0),(566,'EXCESS_AMOUNT','',11,1,0),(567,'FEE_COMPONENT_DETAIL_REPORT','',11,1,0),(568,'VIEW_DAILY_ONLINE_TX','',11,1,0),(569,'CLASS_WISE_DATE_WISE_REPORT','',11,1,0),(570,'FEES_EDIT_HISTORY_REPORT','',11,1,0),(571,'PREDEFINED_FILTERS','',11,1,0),(572,'FINE_WAIVER_REPORT','',11,1,0),(573,'ONLINE_PAYMENT_REFUND','',11,1,0),(574,'VIEW_ONLINE_CHALLAN_REPORT','',11,1,0),(575,'STATEMENT_GENERATE','',11,1,0),(576,'CLASSWISE_STUDENT_FEE_COUNT','',11,1,0),(577,'FEES_RECEIPT_SEND','',11,1,0),(578,'VIEW_CONCESSIONS_DATE_WISE','',11,1,0),(579,'MASS_FEE_ASSISGNED_STUDENT_WISE','',11,1,0),(580,'CONCESSIONS_ASSIGN_APPROVER','',11,1,0),(581,'CONCESSIONS_APPROVAL','',11,1,0),(582,'FAST_COLLECTION_INSTALLMENT_WISE','',11,1,0),(583,'ADD_TRANSACTION','',69,1,0),(584,'DELETE_TRANSACTION','',69,1,0),(585,'PLAN_SYLLABUS_SCHEDULE','',61,1,0),(586,'DESIGN_SESSION','',61,1,0),(587,'CHECK_IN_OUT_SESSION','',61,1,0),(588,'ACTIVITY_TRACKER','',70,1,0),(589,'ACTIVITY_TRACKER','',70,1,0),(590,'BOARDING','',70,1,0),(591,'STUDENT_ANALYTICS','',70,1,0),(592,'FEE_COLLECTION','',70,1,0),(593,'LEAD_MANAGEMENT','',70,1,0),(594,'FEE_SUMMARY','',70,1,0),(595,'STAFF_ATTENDANCE','',70,1,0),(596,'STUDENT_ATTENDANCE','',70,1,0),(597,'PARENT_TICKETING','',70,1,0),(598,'INTERNAL_TICKETING','',70,1,0),(599,'ADMISSIONS','',70,1,0),(600,'INDUS_COGNITENSOR_DASHBOARD','',70,1,0),(601,'STAFF_REPORT','',70,1,0),(602,'INFIRMARY_ANALYTICS','',70,1,0),(603,'ADD_OFFLINE_TICKET','',42,1,0),(604,'TICKET_ANALYTICS','',42,1,0),(605,'STAFF_TICKET_REPORT','',42,1,0),(606,'STUDENT_TICKET_REPORT','',42,1,0),(607,'PUBLISH_MARKS_TO_PARENT','',19,1,0),(608,'RESULT_ANALYSIS_REPORT','',19,1,0),(609,'MODULE','',75,1,0),(610,'CREATE','',75,1,0),(611,'VIEW_REPORTS','',75,1,0),(612,'INTERNAL_TICKETING_ADMIN','',75,1,0),(613,'CREATE_TICKET_ON_BEHALF','',75,1,0),(614,'MODULE','',76,1,0),(615,'REQUISITION','',76,1,0),(616,'REPORTS','',76,1,0),(617,'ADMIN','',77,1,0),(618,'VIEW_INDENT','',77,1,0),(619,'MODULE','',78,1,0),(620,'MODULE','',80,1,0),(621,'MODULE','',79,1,0),(622,'MODULE','',81,1,0),(623,'SALES','',81,1,0),(624,'SALES_RETURN','',81,1,0),(625,'REPORTS','',81,1,0),(626,'CLASS_SECTION','',46,1,0),(627,'SEMESTER','',46,1,0),(628,'ORDER_OF_SECTION','',46,1,0),(629,'ORDER_OF_CLASS','',46,1,0),(630,'ORDER_OF_MASTER_CLASS','',46,1,0),(631,'CHANGE_CASE_OF_NAME','',46,1,0),(632,'MODULE','',82,1,0),(633,'MODULE','',82,1,0),(634,'ATTENDANCE','',82,1,0),(635,'LEAVE_RECORDS','',82,1,0),(636,'ASSIGNED_TASKS','',82,1,0),(637,'QUALIFICATION','',82,1,0),(638,'AWARDS','',82,1,0),(639,'EXPERIENCE','',82,1,0),(640,'DOCUMENTS','',82,1,0),(641,'TRAININGS','',82,1,0),(642,'INTERESTS','',82,1,0),(643,'PUBLICATIONS','',82,1,0),(644,'CIRCULARS','',82,1,0),(645,'MESSAGES','',82,1,0),(646,'INITIATIVES','',82,1,0),(647,'INFIRMARY','',82,1,0),(648,'MANAGE_GEOFENCE','',64,1,0),(649,'CHECK_IN_REPORT','',64,1,0),(650,'EXPORT_PERMISSION','',64,1,0),(651,'EDIT_ATTENDANCE','',64,1,0),(652,'RECTIFY_ATTENDANCE_TOOL','',64,1,0),(653,'DEDUPE_ATTENDANCE_TOOL','',64,1,0),(654,'STAFF_REGULARIZE_LEAVE','',64,1,0),(655,'MANAGE_DOCUMENTS','',6,1,0),(656,'STAFF_TRANING_WORKSHOP','',6,1,0),(657,'STAFF_PUBLICATIONS_CITAT','',6,1,0),(658,'STAFF_INTERESTS','',6,1,0),(659,'MANAGE_EXPERIENCE','',6,1,0),(660,'MANAGE_AWARDS','',6,1,0),(661,'MANAGE_QUALIFICATION','',6,1,0),(662,'STAFF_INITIATIVES','',6,1,0),(663,'VIEW_AUDIT_REPORT','',6,1,0),(664,'STAFF_DOCUMENT_REPORT','',6,1,0),(665,'MANAGE_PROFILE_DISPLAY','',6,1,0),(666,'MANAGE_PROFILE_EDITS','',6,1,0),(667,'APPROVE_ATTRIBUTE_UPDATES','',6,1,0),(668,'PROVISION_STAFF_NAME_CHANGE','',6,1,0),(669,'MANAGE_DEPARTMENTS','',6,1,0),(670,'MANAGE_DESIGNATIONS','',6,1,0),(671,'STAFF_DATA_MISSING_REPORT','',6,1,0),(672,'STAFF_EDIT_HISTORY_REPORT','',6,1,0),(673,'STAFF_EXIT','',6,1,0),(674,'STUDENT_DOCUMENT_REPORT','',1,1,0),(675,'STUDENT_AADHAR_REPORT','',1,1,0),(676,'STUDENT_PAN_REPORT','',1,1,0),(677,'STUDENT_PREVIOUS_SCHOOL_REPORT','',1,1,0),(678,'STUDENT_DATA_MISSING_REPORT','',1,1,0),(679,'STUDENT_DOCUMENTS_DELETE','',1,1,0),(680,'STUDENT_EDIT_HISTORY_REPORT','',1,1,0),(681,'MODULE','',83,1,0),(682,'SCHOOL_DETAILS','',83,1,0),(683,'DOCUMENTS','',83,1,0),(684,'PREV_SCHOOL_DETAILS','',83,1,0),(685,'STUDENT_HEALTH','',83,1,0),(686,'STUDENT_MEDICAL_FORM','',83,1,0),(687,'STUDENT_CONSENT_FORMS','',83,1,0),(688,'FEES','',83,1,0),(689,'CIRCULARS','',83,1,0),(690,'SMS','',83,1,0),(691,'ATTENDANCE','',83,1,0),(692,'EXAMINATION','',83,1,0),(693,'OBSERVATION','',83,1,0),(694,'ACADEMICANALYSIS','',83,1,0),(695,'NON_COMPLIANCE','',83,1,0),(696,'TRANSPORTATION','',83,1,0),(697,'RFID_REPORT','',83,1,0),(698,'EDIT_ATTENDANCE_SUBJECTWISE','',65,1,0),(699,'TAKE_PREVIOUS_DATE_ATTENDANCE','',65,1,0),(700,'RESOLVE_ANY_NON_COMPLIANCE','',72,1,0),(701,'FREEZE_SUBJECT_ALLOCATION','',12,1,0),(702,'COMPENSATION_STAFF_LEAVE_APPLY','',39,1,0),(703,'COMPENSATION_STAFF_LEAVE_APPROVE','',39,1,0),(704,'STAFF_LEAVE_YEARLY_REPORT','',39,1,0),(705,'ENABLE_HALF_DAY_STAFF_LEAVE_APPLY_3_LEVEL','',39,1,0),(706,'ACAD_YEAR_CHANGE','',50,1,0),(707,'ENQUIRY_ADMIN','',50,1,0),(708,'SHOW_COUNSELOR_LIST','',50,1,0),(709,'MASS_EMAIL','',50,1,0),(710,'CREATE_OFFERS','',49,1,0),(711,'JOINING_FORMS','',49,1,0),(712,'EDIT_HISTORY','',49,1,0),(713,'LINK_TO_ENQUIRY','',49,1,0),(714,'ADMISSION_ANALYSIS','',49,1,0),(715,'ASSIGN_COUNSELOR','',49,1,0),(716,'GENERATE_APPLICATION','',49,1,0),(717,'CHANGE_ACADEMIC_YEAR','',49,1,0),(718,'MASS_EMAIL','',49,1,0),(719,'RECEIPT_CANCLED_REPORT','',49,1,0),(720,'REPORTING_MANAGER','',55,1,0),(721,'REPORTING_MANAGER','',55,1,0),(722,'OTHER_LINKS','',55,1,0),(723,'GALLERY','',55,1,0),(724,'TICKETING','',55,1,0),(725,'PARENT_TICKETING','',55,1,0),(726,'STUDENT_COUNT_GENDERWISE','',55,1,0),(727,'ENQUIRY_WIDGET','',55,1,0),(728,'INTERNAL_TICKETING','',55,1,0),(729,'STAFF_ON_LEAVE_WIDGET','',55,1,0),(730,'STUDENT_NON_COMPLIANCE','',55,1,0),(731,'STUDENT_NON_COMPLIANCE_STATISTICS','',55,1,0),(732,'STUDENT_OBSERVATION','',55,1,0),(733,'STUDENT_WIDGET_STATISTICS','',55,1,0),(734,'STUDENT_COUNCELLING_TREND','',55,1,0),(735,'GET_APPROVAL_WIDGET_DATA','',55,1,0),(736,'STAFF_LEAVES_DETAILS','',55,1,0),(737,'MONTH_WISE_STAFF_CALENDAR','',55,1,0),(738,'INFIRMARY_VISITOR_WIDGET_DATA','',55,1,0),(739,'ATTENDANCE_CHECKIN_WIDGET','',55,1,0),(740,'VISITOR','',55,1,0),(741,'INFIRMARY_STATISTICS_WIDGET','',55,1,0),(742,'STAFF_ANNIVERSARY_WIDGET','',55,1,0),(743,'ENQUIRY_STATISTICS_WIDGET','',55,1,0),(744,'LIBRARY_STATISTICS_WIDGET','',55,1,0),(745,'TRANSPORTATION_STATISTICS_WIDGET','',55,1,0),(746,'RFID_WIDGET','',55,1,0),(747,'INVENTORY_STATISTICS_WIDGET','',55,1,0),(748,'BOOKS_TREND_WIDGET','',55,1,0),(749,'STUDENT_COUNSELLING_STAT','',55,1,0),(750,'STUDENT_CHECKIN_TRACKING','',55,1,0),(751,'SUBSTITUTION_STATISTICS','',55,1,0),(752,'MODULE','',84,1,0),(753,'CREATE','',84,1,0),(754,'VIEW_REPORTS','',84,1,0),(755,'INFIRMARY_ADMIN','',84,1,0),(756,'MEDICAL_EXPENCES','',84,1,0),(757,'MEDICAL_EXPENSE_MASS','',84,1,0);
INSERT INTO `roles` VALUES (1,'School Administrators','',1,'2019-06-03 12:12:33','2019-06-03 12:12:33',999),(2,'Teaching Staff','',1,'2019-06-07 17:55:37','2019-06-07 17:55:37',999),(3,'Accounts','',1,'2019-06-13 06:36:32','2019-06-13 06:36:32',999);
INSERT INTO `semester` VALUES (1,'I Semester',2,'sem_promotion','odd'),(2,'II Semester',3,'acad_promotion','even'),(3,'III Semester',4,'sem_promotion','odd'),(4,'IV Semester',5,'acad_promotion','even'),(5,'V Semester',6,'sem_promotion','odd'),(6,'VI Semester',NULL,'acad_promotion','even');
INSERT INTO `config` VALUES (1,'modules','[\"CIRCULARS_V2\",\"CIRCULAR_WIDGET\",\"COMMUNICATION\",\"FEESV2\",\"FLASH_NEWS\",\"GALLERY\",\"HOMEWORK\",\"LEAVE\",\"PARENTS_LOGIN\",\"PERMISSIONS\",\"SCHOOL_CALENDAR\",\"STAFF_LOGIN\",\"STAFF_MASTER\",\"STAFF_OBSERVATION\",\"STUDENT_ATTENDANCE\",\"STUDENT_CERTIFICATES\",\"STUDENT_MASTER\",\"STUDENT_OBSERVATION\",\"TEXTING\",\"TEXTING_WIDGET\",\"USER_MANAGEMENT\"]','2020-10-11 10:54:09','multiple'),(2,'parent_modules','[\"CIRCULARS_V2\",\"FEESV3\",\"FLASH_NEWS\",\"GALLERY\",\"HOMEWORK\",\"PARENT_TICKETING\",\"SCHOOL_CALENDAR\",\"TEXTING\"]','2020-10-11 10:55:02','multiple'),(3,'school_image','/assets/img/temp_school_bg.jpg','2019-01-23 10:01:44','string'),(4,'school_logo','/assets/img/nextelement_logo.png','2020-10-11 10:51:50','string'),(5,'company_logo','/assets/img/nextelement_logo.png','2020-10-11 10:51:50','string'),(6,'company_name','NextElement','2020-10-11 10:51:50','string'),(7,'login_background','/assets/img/temp_school_bg.jpg','2020-10-11 10:51:06','string'),(8,'school_name','SRN Evening College','2024-03-28 14:31:29','string'),(9,'school_name_line2','Bangalore','2019-01-23 13:24:47','string'),(10,'school_short_name','srneve','2024-03-28 14:31:29','string'),(11,'classType','[{\"name\":\"Toddler 1 To 2\", \"value\":\"1\"},{\"name\":\"PS-1\", \"value\":\"2\"},{\"name\":\"PS-2\", \"value\":\"4\"},{\"name\":\"Grade 1 To 9\", \"value\":\"3\"}]','2020-04-21 11:33:32','json'),(12,'admission_type','[{\"name\":\"Re-admission\", \"value\":\"1\"}, {\"name\":\"New Admission\", \"value\":\"2\"}]','2019-01-24 12:25:45','array'),(13,'board','[{\"name\":\"State\", \"value\":\"1\"}, {\"name\":\"CBSE\", \"value\":\"2\"}]','2020-10-11 10:51:06','array'),(14,'admission_status','[{\"name\":\"Pending\", \"value\":\"1\"}, {\"name\":\"Approved\", \"value\":\"2\"}, {\"name\":\"Rejected\", \"value\":\"3\"}, {\"name\":\"Alumni\", \"value\":\"4\"}]','2019-01-24 12:37:27','array'),(15,'medium','[{\"name\":\"English\", \"value\":\"1\"}]','2019-01-24 12:37:48','array'),(16,'student_record_selected_lang','0','2019-01-28 06:09:12','boolean'),(17,'student_record_height_weight','1','2019-01-28 06:10:24','boolean'),(18,'staff_status','[{\"name\":\"Pending\", \"value\":\"1\"}, {\"name\":\"Approved\", \"value\":\"2\"}, {\"name\":\"Rejected\", \"value\":\"3\"}, {\"name\":\"Resigned\", \"value\":\"4\"}, {\"name\":\"Retired\", \"value\":\"5\"}]','2019-01-25 16:55:59','array'),(19,'rte','[{\"name\":\"RTE\", \"value\":\"1\"}, {\"name\":\"Non-RTE\", \"value\":\"2\"}]','2019-01-25 16:59:01','array'),(20,'category','[{\"name\":\"General\", \"value\":\"1\"}, {\"name\":\"SC/ST\", \"value\":\"2\"}, {\"name\":\"CATEGORY IIA\", \"value\":\"3\"}, {\"name\":\"CATEGORY IIB\", \"value\":\"4\"}, {\"name\":\"CATEGORY IIIA\", \"value\":\"5\"}, {\"name\":\"OBC\", \"value\":\"6\"}]','2019-01-25 17:00:32','array'),(21,'boarding','[{\"name\":\"Day School\", \"value\":\"1\"}]','2019-01-25 17:03:43','array'),(22,'staff_profile_enableStaffProfileEdit','1','2020-06-04 10:46:05','boolean'),(23,'staff_profile_enableQualificationEdit','1','2020-06-04 13:17:50','boolean'),(24,'circular_categories','[\"Student-Info\", \"Food\", \"Circulars\", \"Transport\", \"Fee\", \"Time-Table\", \"Competition\",\"Learning-Milestone-Curriculum\",\"Teacher-s Voice\",\"Student Improvement Plan\"]','2020-08-12 06:50:44','string'),(25,'circular_enable_email','0','2019-01-25 18:05:04','string'),(26,'circular_enable_sms','0','2019-06-01 11:12:16','string'),(27,'parent_profile_display_student','{\"display\":\"1\", \"fields\":[\"admission_no\",\"dob\",\"gender\",\"roll_no\"], \"address\":\"1\"}','2020-10-10 09:27:14','json'),(28,'parent_profile_display_father','{\"display\":\"1\", \"fields\":[\"name_photo\",\"email\",\"contact_no\",\"occupation\"], \"address\":\"1\"}','2019-01-26 09:29:59','json'),(29,'parent_profile_display_mother','{\"display\":\"1\", \"fields\":[\"name_photo\",\"email\",\"contact_no\",\"occupation\"],\"address\":\"1\"}','2019-01-26 08:23:57','json'),(30,'school_name_line1','Bangalore','2022-09-10 14:01:32','string'),(31,'latecomer_attendance','1','2019-01-28 06:38:14','boolean'),(32,'show_absent_students_in_latecomer','1','2019-01-28 08:36:36','boolean'),(33,'show_all_students_in_latecomer','0','2019-01-28 08:52:39','boolean'),(34,'school_header',NULL,'2020-03-21 07:05:51','image'),(35,'favicon','/assets/img/nextelement_logo.png','2020-03-21 07:05:52','string'),(36,'competition_attendance','0','2019-01-28 10:56:15','boolean'),(37,'academic_year_id','23','2024-03-28 14:31:29','string'),(38,'promotion_academic_year_id','24','2024-03-28 14:31:30','string'),(58,'admission_number','{\"manual\":\"TRUE\", \"admission_generation_algo\":\"NEXTELEMENT\", \"infix\":\"TEMP\", \"digit_count\":\"5\", \"index_offset\":\"TRUE\"}','2020-10-11 10:51:06','json'),(59,'student_address_types','[{\"name\":\"Home Address\", \"value\":\"0\"}]','2019-03-02 12:27:50','array'),(60,'add_class_admitted_to','0','2019-03-02 13:03:16','boolean'),(61,'email_settings','','2020-09-09 12:53:48','json'),(62,'user_provisioning_challenge_fields','{\"fields\":[]}','2019-05-31 14:47:03','json'),(64,'user_provisioning_school_code','srneve','2024-03-28 14:31:30','string'),(65,'user_provisioning_link_message_body','','2022-09-10 14:01:32','string'),(66,'user_provisioning_link_message_footer','','2022-09-10 14:01:32','string'),(67,'smsintergration','{\"url\":\"api-alerts.kaleyra.com/v4/\", \"api_key\":\"Ae6b5684768b2741508f447a71545290a\", \"sender\":\"NXTSMS\", \"mode\":\"LIVE\"}','2024-03-28 14:44:26','json'),(68,'sms_credit_length','{\"unicode_single\":\"70\", \"unicode_multi\":\"60\", \"non_unicode_single\":\"160\", \"non_unicode_multi\":\"150\"}','2019-03-10 11:06:29','json'),(69,'attendance_latecomer_sms_message','','2022-09-10 14:01:32','string'),(70,'user_provisioning_manual_cridential_part_1','','2022-09-10 14:01:32','string'),(71,'user_provisioning_manual_cridential_part_2','','2022-09-10 14:01:32','string'),(72,'circular_sms_msg1','You have a new circular with title','2019-06-01 11:01:02','string'),(73,'circular_sms_msg2',', Please check your School App.','2019-06-01 11:01:05','string'),(77,'app_links','{\"android_app\":\"https://bit.ly/2U5Yr3C\",\"ios_app\":\"https://apple.co/2Yw1WDd\"}','2019-06-07 12:40:47','json'),(78,'school_abbreviation','srneve','2024-03-28 14:31:30','string'),(79,'forgot_password_mobile','1','2020-10-11 10:51:09','boolean'),(80,'push_notiification_key','AAAAsXm0Ol8:APA91bF_RENAAVnYwYwO_umXOc722pGNmwcQyWwbzYw_E79Tts4BEV_PVUdt7cJoCpgl9bPg6eVe4D_QfeYtYIb6k5_WTTsc1UoSs0WTiQXMRMTt3MIarEbR-JCP0fqAF00wd67xnuv5','2019-06-20 11:43:05','string'),(81,'click_action','com.ne.nextelement.NPSWebView','2019-06-20 12:10:50','string'),(82,'show_staff_handling_in_parent_profile','0','2020-10-11 10:51:09','boolean'),(84,'profile_edit_enabled','0','2020-10-11 10:51:09','boolean'),(85,'profile_edit_columns','[\"FATHER_ADDRESS\",\"FATHER_CONTACT_NO\",\"FATHER_EMAIL\",\"FATHER_PHOTO\",\"GUARDIAN_CONTACT_NO\",\"GUARDIAN_EMAIL\",\"GUARDIAN_NAME\",\"GUARDIAN_PHOTO\",\"MOTHER_ADDRESS\",\"MOTHER_CONTACT_NO\",\"MOTHER_EMAIL\",\"MOTHER_PHOTO\",\"STUDENT_ADDRESS\",\"STUDENT_BLOOD_GROUP\",\"STUDENT_PHOTO\"]','2020-07-28 06:30:46','multiple'),(86,'homework_module_name','Home Connect','2019-07-08 10:58:33','string'),(87,'notification_mode','LIVE','2019-07-10 05:36:06','string'),(88,'sms_module_to_use','new','2019-07-10 05:56:00','string'),(89,'circularv2_categories','[\"Student-Info\", \"Food\", \"Circulars\", \"Transport\", \"Fee\", \"Time-Table\", \"Competition\",\"Learning-Milestone-Curriculum\",\"Events\",\"Teacher\'s Voice\"]','2020-01-04 05:59:52','string'),(90,'parent_ticketing_types','[\"Compliment\",\"Feedback\",\"Concern\"]','2019-07-18 06:27:04','string'),(91,'attendance_absentee_sms_message','Dear Parent, Greetings from DemoSchool! Your ward %std_name% of %cs_name% is absent today %date%. Kindly ignore the message if you have already informed the school through email. - S/d Demo School','2020-10-11 10:51:09','string'),(92,'staff_type','[{\"name\":\"Teaching\", \"value\":\"0\"},{\"name\":\"Non-Teaching\", \"value\":\"1\"},{\"name\":\"Board Member\", \"value\":\"2\"},{\"name\":\"Supporting Staff\", \"value\":\"3\"}]','2019-09-18 10:41:11','array'),(93,'circular_tile_name','Circular','2019-09-23 10:03:23','string'),(94,'display_receipts_in_parent','1','2020-02-04 12:01:07','boolean'),(95,'double_promotion_academic_year_id','25','2024-03-28 14:31:30','string'),(96,'email_settings_enquiry','','2020-10-11 10:51:09','string'),(97,'registered_emails','','2020-10-11 10:51:09','string'),(98,'email_templates','[\"enquiry confirmation email to parent\",\"admission confirmation email to parent\"]','2019-10-16 06:59:45','string'),(99,'enquiry_dob_instruction','Temp DOB Instruction','2020-10-11 10:51:09','string'),(100,'enquiry_header_instruction','TEMP ENQUIRY HEADER','2020-10-11 10:51:09','string'),(101,'show_email_option_in_admission','1','2019-10-13 13:48:22','boolean'),(102,'fee_date','0','2020-10-11 10:51:09','boolean'),(103,'admissions_sms','Your online application for registration number %%admission_no%% is successfully submitted. Kindly visit the school with original documents. Further instructions will be provided at the school and on EMail.','2019-10-21 11:42:10','string'),(104,'sms_templates','[{\"category\":\"Enquiry\",\"Items\":[\"Enquiry Confirmation to Parent\"]},{\"category\":\"Admission\",\"Items\":[\"Admission Confirmation to Parent\"]},{\"category\":\"Library\",\"Items\":[\"Student Books Issued\",\"Staff Books Issued\",\"Student Books return\",\"Staff Books return\"]}]','2019-11-15 14:42:11','string'),(105,'forgot_username_password','1','2019-10-30 06:46:55','boolean'),(106,'admission_required_fields','[\"grade_applied_for\",\"std_name\",\"dob\",\"nationality\",\"f_name\",\"f_addr\",\"f_district\",\"f_state\",\"f_county\",\"f_pincode\",\"f_mobile_no\",\"f_email_id\",\"m_name\",\"m_addr\",\"m_district\",\"m_state\",\"m_county\",\"m_mobile_no\",\"m_email_id\",\"std_photo_uri\",\"student_blood_group\",\"enquiry_id\"]','2020-06-04 11:02:35','multiple'),(107,'admission_show_enabled_fields','[\"religion\",\"student_caste\"]','2019-11-05 15:24:07','multiple'),(108,'admission_follow_up_status','[\"Submitted\",\"Application Amount Paid\",\"Selected for exam\",\"Selected for interactions\",\"Completed interactions\",\"Admit\",\"Student added to ERP\",\"Rejected\"]','2019-11-08 13:40:41','string'),(109,'enquiry_follow_up_status','[\"Created\",\"Closed-not interested\",\"Follow-up required\",\"Processed for application\",\"Processed for registration\",\"Processed for admission\"]','2020-01-06 07:53:02','string'),(110,'parent_ticketing_default_assignee','','2022-09-10 14:01:32','string'),(112,'circularv2_from_email','','2020-10-11 10:51:09','string'),(113,'enquiry_how_did_you_get_to_know_about_us','[\"Friends\",\"News_Paper\",\"School_Banner\",\"Web_Site\",\"Others\"]','2020-01-03 06:47:36','string'),(114,'commity_members_staff_ids','[]','2020-10-11 10:51:09','string'),(115,'text_mode_to_use','sms, notification, notification_sms','2024-03-28 14:47:33','string'),(116,'fee_payment_card_charge_amount','[{\"value\":\"2\", \"amount\":\"0.00\"}, {\"value\":\"3\", \"amount\":\"0.00\"}, {\"value\":\"7\", \"amount\":\"0.02\"}]','2020-01-31 07:30:41','json'),(117,'fee_installment_selection','1','2020-01-09 13:30:46','boolean'),(118,'followup_closure_reason','1','2020-01-14 09:40:13','boolean'),(119,'admissions_payment_mode','[{\"name\":\"Cash\", \"value\":\"9\"},{\"name\":\"DD\", \"value\":\"1\"},{\"name\":\"Cheque\", \"value\":\"4\"},{\"name\":\"Net Banking\", \"value\":\"8\"},{\"name\":\"Debit card\", \"value\":\"3\"},{\"name\":\"Credit Card\", \"value\":\"2\"}]','2020-01-29 12:06:20','json'),(120,'fee_payment_sms','{\"sms_enabled\":\"FALSE\",\"source\":\"Fee\",\"sh_type\":\"Student\", \"message\":\"Fee payment of Rs. %%amount%% has been initiated. Txn Id: %%trans_id%%. Please keep this Txn ID for future reference.\"}','2020-10-11 10:51:10','json'),(121,'examination_remarks_length','1000','2020-03-12 09:35:27','string'),(122,'parent_profile_instructions','[\"Please upload from Gallery to ensure photos are clear and of good quality. Max size per photo is 10MB.\", \"Head should take up 70-80% of photo.\", \"Ensure the accuracy of data in all fields as it will be displayed on ID card.\"]','2020-04-07 16:05:21','string'),(123,'parent_ticketing_management_notif','[]','2022-09-10 14:01:32','string'),(125,'show_move_to_erp_doj','1','2020-05-06 09:05:54','boolean'),(126,'parent_profile_display_guardian','{\"display\":\"1\", \"fields\":[\"contact_no\",\"email\"]}','2020-05-13 15:30:15','json'),(127,'staff_attendance_timings','[{\"name\":\"FD\", \"from\":\"8:00 am\", \"to\":\"4:00 pm\"}, {\"name\":\"HD\", \"from\":\"8:00 am\", \"to\":\"12:00 pm\"}]','2020-09-15 12:54:43','json'),(128,'staff_attendance_type','manual','2020-09-15 12:54:47','string'),(129,'payroll','[\"advance\",\"basic_salary\",\"cca\",\"conveyance\",\"da\",\"esi\",\"hra\",\"other_deductions\",\"pf\",\"professional_tax\",\"special_allowance\",\"tds\",\"transport\"]','2020-09-16 07:22:56','multiple'),(130,'lesson_plan','{\"lp_task_max_submit_file\":\"5\", \"size\":\"10MB\"}','2020-09-21 08:48:42','json'),(131,'resources','{\"resource_size\":\"10MB\"}','2020-09-21 08:54:01','json'),(132,'marks_entry_version','v2','2024-03-28 14:36:08','string'),(133,'fee_collection_v1','1','2024-03-28 14:37:03','boolean'),(134,'staff_profile_view_v1','1','2024-03-28 14:38:14','boolean'),(135,'student_profile_view_v1','1','2024-03-28 14:38:46','boolean'),(136,'examination_enable_sms','1','2024-03-28 14:48:46','boolean'),(137,'admission_filter_version','v2','2024-03-28 14:49:36','string'),(138,'admission_new_view_details','1','2024-03-28 14:51:10','boolean'),(139,'admission_pick_status_from_table','1','2024-03-28 14:51:55','boolean'),(140,'enquiry_pick_status_from_table','1','2024-03-28 14:52:24','boolean');

--
-- Add Incremental Queries
--


--
-- Final view structure for view `sample`
--

/*!50001 DROP VIEW IF EXISTS `sample`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8 */;
/*!50001 SET character_set_results     = utf8 */;
/*!50001 SET collation_connection      = utf8_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`nextelement`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `sample` AS select `sy`.`student_admission_id` AS `student_admission_id` from (`student_year` `sy` join `student_admission` `sa` on((`sa`.`id` = `sy`.`student_admission_id`))) where ((`sy`.`class_section_id` = 8) and (`sa`.`admission_status` = 2)) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `student_view`
--

/*!50001 DROP VIEW IF EXISTS `student_view`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8 */;
/*!50001 SET character_set_results     = utf8 */;
/*!50001 SET collation_connection      = utf8_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`nextelement`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `student_view` AS select 1 AS `stdId`,1 AS `classId`,1 AS `section`,1 AS `rteid`,1 AS `admission_type`,1 AS `admission_status`,1 AS `roll_no`,1 AS `admission_no`,1 AS `nationality`,1 AS `religion`,1 AS `category`,1 AS `student_mother_tongue`,1 AS `student_contact_no`,1 AS `birth_taluk`,1 AS `birth_district`,1 AS `caste`,1 AS `std_aadhar_number`,1 AS `student_house`,1 AS `Student_Name`,1 AS `date_of_joining`,1 AS `dob`,1 AS `gender`,1 AS `class_name`,1 AS `section_name`,1 AS `father_Name`,1 AS `father_qualification`,1 AS `father_occupation`,1 AS `father_mobile_no`,1 AS `father_aadhar_no`,1 AS `father_annual_income`,1 AS `father_company`,1 AS `father_mother_tongue`,1 AS `Mother_Name`,1 AS `mother_qualification`,1 AS `mother_occupation`,1 AS `mother_mobile_number`,1 AS `mother_aadhar_number`,1 AS `mother_annual_income`,1 AS `mother_company`,1 AS `mother_mother_tongue`,1 AS `student_email`,1 AS `father_email`,1 AS `mother_email` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2020-10-11 13:38:40
