<?php
  /**
   * Name:    Oxygen
   * Author:  <PERSON>
   *          <EMAIL>
   *
   * Created:  17 May 2018
   *
   * Description:  
   *
   * Requirements: PHP5 or above
   *
   */

  defined('BASEPATH') OR exit('No direct script access allowed');

  /**
   * Class Fee
   * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
   * @property CI_Form_validation      $form_validation The form validation library
   */
  class Join_controller extends CI_Controller {

    private $year_id;

    public function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isAuthorized('VIRTUAL_CLASSROOM.MODULE')) {
        redirect('dashboard', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('virtual_classroom/Virtual_classroom_model','vcm');
      $this->load->library('openvidu');
      $this->load->library('filemanager');
    }

    public function index(){
      $data['schedule_list'] = $this->vcm->get_schedule_data();

      $data['main_content']    = 'virtual_classroom/join_class/index.php';
      $this->load->view('inc/template', $data);  
    }

    public function join_window() {
      $data['schedule_id'] = $_POST['schedule_id'];
      $data['schedule_room'] = $this->vcm->getRoomByScheduleId($data['schedule_id']);
      $data['main_content']    = 'virtual_classroom/join_class/join_window.php';
      if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'virtual_classroom/join_class/mobile_join_window.php';
      } else {
        $data['main_content']    = 'virtual_classroom/join_class/join_window.php';
      }
      $this->load->view('inc/template', $data);  
    }

    public function join_class() {
      $data['schedule_id'] = $_POST['schedule_id'];
      $staff_id = $this->authorization->getAvatarStakeHolderId();
      $data['staff_name'] = $this->vcm->get_staff_name($staff_id);
      $this->vcm->updateSessionIdInRoom($data['schedule_id']);

      //Append the staff id to schedule details if not exists. She might have joined without invite
      $data['schedule_details'] = $this->vcm->get_schedule_detail($data['schedule_id']);
      $data['questions'] = $this->vcm->get_schedule_questions($data['schedule_id']);
      $data['student_list'] = $this->vcm->get_invitee_list($data['schedule_details']->id, $staff_id);
      $data['no_students'] = count($data['student_list']);
      $data['client_id'] = 'staff_' . $staff_id;

      $video_session = $this->openvidu->create_session($data['schedule_details']->openvidu_session_id);
      $data['video_session_id'] = $video_session->session_id;

      $this->vcm->update_class_session_id($data['schedule_details']->virtual_classroom_code, $data['schedule_details']->openvidu_session_id);

      if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'virtual_classroom/video/mobile/mobile_index.php';
      } else {
        $data['main_content']    = 'virtual_classroom/video/desktop/index.php';
      }

      $this->load->view('inc/template_virtualClass', $data);
    }

    public function releave_classroom() {
      $classroom_id = $_POST['classroom_id'];
      $this->vcm->update_class_session_id($classroom_id, null);
    }

    //Creates a new OpenVidu session
    public function create_token () {
      // echo 'Hi Tere';
      $session_id = $_POST['session_id'];
      echo $this->openvidu->create_token($session_id, 100, 100);
    }

    public function get_results() {
      $schedule_id = $_POST['schedule_id'];
      $questions = $this->vcm->getResults($schedule_id);
      echo json_encode($questions);
    }
  }

?>