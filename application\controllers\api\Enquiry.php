<?php

  require APPPATH . 'libraries/REST_Controller.php';

class Enquiry extends REST_Controller {
  private $api_key;
  public function __construct() {
    parent::__construct();
    $this->load->model('email_model');
    $this->config->load('form_elements');
    $this->load->helper('texting_helper');
    $this->load->model('communication/emails_model');
    $this->load->helper('email_helper');

    $this->api_key = '1fEkQiYkD7Fqyrid06BFXccK1QdiIaXh';
    if($this->settings->getSetting('school_short_name') == 'npskr'){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      trigger_error('NPSKR Enquiry');
      trigger_error(json_encode($data));
      $this->api_key = '7uHPZ3c8TboEDyW18MeaJQKzS9hkh1hV';
    }
  }
  
  public function index_get() {
		$enquiry = ['status' => '204', 'message' => 'No Data'];
    echo json_encode($enquiry);
    exit();
	}

  public function add_suy_enquiry_post() {
    $this->load->model('suy/Suy_model');
    $data = (array) json_decode(file_get_contents('php://input'), TRUE);
     // empty form data check 
    if (empty($data)) {
      $enquiry = ['status' => '204', 'message' => 'No Data'];
      echo json_encode($enquiry);
      exit();
    }

    if(!array_key_exists('api_key', $data) || $data['api_key'] != $this->api_key) {
      $enquiry = ['status' => 403, 'message' => 'Unauthorized', 'data' => []];
      echo json_encode($enquiry);
      exit();
    }

    // validate form input data
    $return_value = $this->_validate_suy_input_data($data);
    if ($return_value != '1') {
      $enquiry = ['status' => 400, 'message' => $return_value, 'data' => []];
      echo json_encode($enquiry);
      exit();
    }

    // insert inputform data into database
    $result = $this->Suy_model->insert_suy_enquiry_data_api($data);
    if (!$result) {
      $enquiry = ['status' => '400', 'message' => 'Something went wrong in adding the data'];
    }
    $enquiry = ['status' => '200', 'message' => 'Successful'];
    echo json_encode($enquiry);
  }

  public function _validate_suy_input_data($data){
    if (!isset($data['student_name'])) {
      return 'Invalid Student Name';
    }
    if ($data['student_name'] == '') {
      return 'Invalid Student Name';
    }
    if (!isset($data['mobile_number'])) {
      return 'Invalid Mobile Number';
    }
    if ($data['mobile_number'] == '') {
      return 'Invalid Mobile Number';
    }
    return 1;
  }

  public function add_itari_enquiry_post() {
    $this->load->model('itari/Itari_Model');
    $data = (array) json_decode(file_get_contents('php://input'), TRUE);
     // empty form data check 
    if (empty($data)) {
      $enquiry = ['status' => '204', 'message' => 'No Data'];
      echo json_encode($enquiry);
      exit();
    }

    if(!array_key_exists('api_key', $data) || $data['api_key'] != $this->api_key) {
      $enquiry = ['status' => 403, 'message' => 'Unauthorized', 'data' => []];
      echo json_encode($enquiry);
      exit();
    }

    // validate form input data
    $return_value = $this->_validate_itari_input_data($data);
    if ($return_value != '1') {
      $enquiry = ['status' => 400, 'message' => $return_value, 'data' => []];
      echo json_encode($enquiry);
      exit();
    }

    // insert inputform data into database
    $result = $this->Itari_Model->insert_itari_enquiry_data_api($data);
    if (!$result) {
      $enquiry = ['status' => '400', 'message' => 'Something went wrong in adding the data'];
    }
    $enquiry = ['status' => '200', 'message' => 'Successful'];
    echo json_encode($enquiry);
  }

  public function add_post() {
    $this->load->model('enquiry_model');
    $data = (array) json_decode(file_get_contents('php://input'), TRUE);
    // trigger_error('Manchester input data');
    // trigger_error(json_encode($data));
     // empty form data check 
    if (empty($data)) {
      $enquiry = ['status' => '204', 'message' => 'No Data'];
      echo json_encode($enquiry);
      exit();
    }

    if(!array_key_exists('api_key', $data) || $data['api_key'] != $this->api_key) {
      $enquiry = ['status' => 403, 'message' => 'Unauthorized', 'data' => []];
      echo json_encode($enquiry);
      exit();
    }

    // validate form input data
    $return_value = $this->_validate_input_data($data);
    if ($return_value != '1') {
      $enquiry = ['status' => 400, 'message' => $return_value, 'data' => []];
      echo json_encode($enquiry);
      exit();
    }
    // insert inputform data into database
    $result = $this->enquiry_model->insert_enquiry_data_api($data);
    if ($result) {
      $this->enquiry_model->update_receipt_enquiry_wise($result);
      $enquiry_number = $this->enquiry_model->get_enquiry_number_by_id($result);
      $parent_name = $this->enquiry_model->get_enquiry_parent_name_id($result);

      
      // trigger_error('enqury last id');
      // trigger_error(json_encode($result));

      // trigger_error('enqury number api');
      // trigger_error(json_encode($enquiry_number));

      if (isset($data['send_email_to_parent'])) {
        if ($data['send_email_to_parent'] == 1) {
          $this->_send_email_to_parent($data['email'], $enquiry_number, $parent_name,$data['academic_year']);
        }  
      }
      if (isset($data['send_email_to_school'])) {
        if ($data['send_email_to_school'] == 1) {
          $this->_send_enquiry_email_to_school($enquiry_number, $parent_name,$data['academic_year']);
        }
      }
      if (isset($data['send_sms_to_parent'])) {
        if ($data['send_sms_to_parent'] == 1) {
          $message = $this->settings->getSetting('enquiry_sms',0);
          if (!empty($message)) {
            $message = str_replace('%%enquiry_number%%',$enquiry_number, $message);
          }
          $this->send_sms_to_parent($data['mobile_number'], $message);
        }
      }
    }
    $enquiry = ['status' => '200', 'message' => 'Successful'];
    echo json_encode($enquiry);
  }

  public function _validate_itari_input_data($data){
    if (!isset($data['student_name'])) {
      return 'Invalid Student Name';
    }
    if ($data['student_name'] == '') {
      return 'Invalid Student Name';
    }
    if (!isset($data['mobile_number'])) {
      return 'Invalid Mobile Number';
    }
    if ($data['mobile_number'] == '') {
      return 'Invalid Mobile Number';
    }
    if (!isset($data['email_id'])) {
      return 'Invalid Email Id';
    }
    if ($data['email_id'] == '') {
      return 'Invalid Email Id';
    }
    if (!isset($data['subscribe_to_news_letter'])) {
      $data['subscribe_to_news_letter'] = 1;
    }
    if ($data['subscribe_to_news_letter'] != '0' && $data['subscribe_to_news_letter'] != '1' ) {
      return 'Invalid Subscribe to Newsletter';
    }
    // if ($data['program'] == '') {
    //   return 'Invalid Program';
    // }
    return 1;
  }

  public function _validate_input_data($data){
    if (!isset($data['student_name'])) {
      return 'Invalid Student Name';
    }
    if ($data['student_name'] == '') {
      return 'Invalid Student Name';
    }
    if (!isset($data['mobile_number'])) {
      return 'Invalid Class ID';
    }
    if ($data['mobile_number'] == '') {
      return 'Invalid Mobile Number';
    }

    //Removing class id and gender validation
    // if (!isset($data['gender'])) {
    //   return 'Invalid Gender';
    // }
    // if ($data['gender'] == '' || $data['gender'] != 'M' && $data['gender'] !='F' && $data['gender'] !='O') {
    //   return 'Invalid Gender';
    // }
    // if (!isset($data['class_id'])) {
    //   return 'Invalid Class ID';
    // }
    // if ($data['class_id'] == '') {
    //   return 'Invalid Class Id';
    // }
    // $classId = $this->enquiry_model->validate_class_id($data['class_id'],$data['academic_year']);
    // if (!$classId) {
    //   return 'Invalid Class Id';
    // }
    return 1;
  }

  private function _send_email_to_parent($email, $enquiry_number, $parent_name,$acad_yr_id){
    $email_template = $this->email_model->get_all_templatebyassigned();
    if(empty($email_template)){
      return 0;
    }
    $members = explode(',', $email_template->members_email);
    array_push($members, $email);
    $memberEmail = [];
    foreach ($members as $key => $val) {
      // $memberEmail[]['email'] = $val;
      array_push($memberEmail,$val);
    }
    $emailBody =  $email_template->content;
    $emailBody = str_replace('%%enquiry_number%%',$enquiry_number, $emailBody);
    $emailBody = str_replace('%%parent_name%%',$parent_name, $emailBody);

    $email_master_data = array(
      'subject' => $email_template->email_subject,
      'body' => $emailBody,
      'source' => 'Enquiry Form Submitted',
      'recievers' => "Parents",
      'from_email' => $email_template->registered_email,
      'acad_year_id' => $acad_yr_id,
      'visible' => 1,
      'sending_status' => 'Completed',
      'sender_list'=>implode(',',$memberEmail)
    );
    $email_master_id = $this->emails_model->saveEmail($email_master_data);

    foreach ($memberEmail as $email) {
      if (!empty($email)) {
          $this->enquiry_model->save_sending_email_data([
              'stakeholder_id'   => 0,
              'avatar_type'      => 0,
              'email'            => $email,
              'email_master_id'  => $email_master_id,
              'status'           => 'Awaited'
          ]);
      }
    }

    return sendEmail($emailBody, $email_template->email_subject, $email_master_id, $memberEmail, $email_template->registered_email, '');
  }
  
  
  private function _send_enquiry_email_to_school($enquiry_number, $parent_name,$acad_yr_id){
    $email_template = $this->enquiry_model->get_enquiry_email_staff_template_byId();
    if (!empty($email_template)) {
      $memberEmail = [];
      foreach ($email_template->staffdetails as $key => $val) {
        // $memberEmail[]['email'] = $val->email;
        array_push($memberEmail,$val->email);
      }
      $emailBody =  $email_template->content;
      $emailBody = str_replace('%%enquiry_number%%',$enquiry_number, $emailBody);
      $emailBody = str_replace('%%parent_name%%',$parent_name, $emailBody);

      $email_master_data = array(
        'subject' => $email_template->email_subject,
        'body' => $emailBody,
        'source' => 'Enquiry Form Submitted',
        'recievers' => "Staff",
        'from_email' => $email_template->registered_email,
        'acad_year_id' => $acad_yr_id,
        'visible' => 1,
        'sending_status' => 'Completed',
        'sender_list'=>implode(',',$memberEmail)
      );
      $email_master_id = $this->emails_model->saveEmail($email_master_data);

      foreach ($email_template->staffdetails as $key => $val) {
          $this->enquiry_model->save_sending_email_data([
              'stakeholder_id'   => $val->staff_id,
              'avatar_type'      => 4,
              'email'            => $val->email,
              'email_master_id'  => $email_master_id,
              'status'           => 'Awaited'
          ]);
      }

      return sendEmail($emailBody, $email_template->email_subject, $email_master_id, $memberEmail, $email_template->registered_email, '');
    }
  }

  private function send_sms_to_parent($number, $message){
   
    $input_arr = array();
    $input_arr['source'] = 'Enquiry';
    $input_arr['message'] = $message;
    $input_arr['custom_numbers'] = (array)$number;
    $input_arr['mode'] = 'sms';
    $input_arr['avatar_id'] = '1';
    $response = sendText($input_arr);
    if($response['success'] != '') {
      $status = 1;
    } else {
      $status = 0;
    }
    return $status;  
  }
}