<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  05 June 2018
 *
 * Description: Controller for Section Timetable view with Substitutions.
 *
 * Requirements: PHP5 or above
 *
 */

class Substitution_timetables extends CI_Controller {
	function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isModuleEnabled('SUBSTITUTION')) {
        redirect('dashboard', 'refresh');
      }
      $this->load->model('timetable/timetable');
      $this->load->model('parent_model');
  }
  // staff wise only can see time table 

  //Landing function for get my timetable
  public function index(){
    $data['sectionList'] = $this->timetable->getSectionList();
    $data['school_admin'] = $this->authorization->isAuthorized('SCHOOL_ADMIN.MODULE');
    $data['main_content'] = 'timetable/substitution_timetables/sub_timetable';
    $this->load->view('inc/template', $data);
  }

  public function getStaffTTById(){
   $staffId = $this->input->post('staffId');
   $result = $this->staff_tt->getStaffTTById($staffId);
   echo json_encode($result);
  }

  public function getFullTTForStaff(){
    $staffId = $this->input->post('staffId');
    $staffTT = $this->staff_tt->getFullTTForStaff($staffId);
    $staffObj = $this->staff_tt->getStaffDetails($staffId);
    $headerTT = $this->timetable->getPeriodHeader('40');
    echo json_encode(array("staffObj"=>$staffObj, "staffTT" =>$staffTT, "headerTT" => $headerTT));
   }
   
   public function getPeriodHeader(){
    $result = $this->timetable->getPeriodHeader('40');
    echo json_encode($result);
   }

   public function getStaffTTSubstituteStaffById(){
    $staffId = $_POST['staffId'];
    $weekDay = $_POST['weekDay'];
    $todayDate = date('Y-m-d');
    $weekDay = date("w", strtotime($todayDate));
    $date = new DateTime($todayDate);
    $headerTT = $this->timetable->getPeriodHeader('40');
    $weekNum = $date->format("W");
//    $staffAllo = $this->staff_tt->getStaffTTById($staffId);
    $staffAllo = $this->staff_tt->getFullTTForStaff($staffId, $weekDay);
    // $staffsub = $this->staff_tt->getCSStaffTTByWeekDaySusbstitute($staffId,$weekDay,$weekNum);
    $staffsub = $this->staff_tt->getCSStaffTTByWeekDaySusbstitute($staffId,$todayDate);
    $old_staffId = $this->staff_tt->getData_isAssignStafftoOthers($staffId,$todayDate);

    echo json_encode(array("headerTT"=>$headerTT, "staffAlloc" =>$staffAllo, "subPeriodIn" => $staffsub, "subPeriodOut" => $old_staffId ));
    //echo 'here';
  }
}