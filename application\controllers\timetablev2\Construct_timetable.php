<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>chhipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Construct_timetable extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE') || !$this->authorization->isAuthorized('TIMETABLE.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('timetablev2/construct_timetable_model','ttv2_model');
    $this->load->model('timetablev2/administration_tools_model','admin_model');
    $this->load->model('timetablev2/template_model','template_model');
    $this->load->model('timetablev2/staff_workload_model','staff_workload_model');
  }

  //Landing function to show the timetable menu
  public function index($template_id) {
    $data['timetable_template_obj'] = $this->template_model->get_template_by_id($template_id);
    $data['main_content']    = 'timetablev2/construct/index.php';
    $this->load->view('inc/template', $data);
  }
  
  public function alloc_s1_period() {
    $result = $this->ttv2_model->alloc_s1_period($_POST['period_id'], $_POST['sw_id'], $_POST['check_only'], $_POST['origin_staff_id_list']);

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = $result;
    }

    echo json_encode($data);
  }

  public function alloc_all_periods() {
    $result = $this->ttv2_model->alloc_all_periods($_POST['alloc_data'], $_POST['section_id']);

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }

    echo json_encode($data);
  }

  public function dealloc_s1_period() {
    $result = $this->ttv2_model->dealloc_s1_period($_POST['section_period_id'], $_POST['ttv2_sw_id']);

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }

    echo json_encode($data);
  }

  public function reset_all_periods() {
    $result = $this->ttv2_model->reset_all_periods($_POST['template_id'], $_POST['section_id']);

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }

    echo json_encode($data);
  }

  public function reset_single_subject_periods() {
    $result = $this->ttv2_model->reset_single_subject_periods($_POST['template_id'], $_POST['section_id'], $_POST['subject_id']);

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }

    echo json_encode(1);
  }

  public function get_data_for_auto_alloc() {
    $template_id = $_POST['template_id'];
    $section_id = $_POST['section_id'];

    $data['section_tt'] = $this->template_model->get_section_template_periods($template_id, $section_id);
    $data['all_staff_tt'] = $this->template_model->get_all_staff_template_periods($template_id, $section_id);
    $data['all_room_tt'] = $this->template_model->get_all_room_template_periods($template_id, $section_id);
    $data['staff_wl'] = $this->staff_workload_model->get_staff_workload_2($template_id, $section_id);

    // echo '<pre>';print_r($data);

    echo json_encode($data);
  }

  public function show_autoallocate_ui($template_id) {
    $data['timetable_template_obj'] = $this->template_model->get_template_by_id($template_id);
    $data['class_list'] = $this->ttv2_model->get_classlist_by_template($template_id);
    $data['staff_list'] = $this->admin_model->get_staff_by_template($template_id);
    $data['main_content']    = 'timetablev2/autoallocate/index.php';
    $this->load->view('inc_v2/template', $data);
  }

  public function get_section_list_from_grade() {
    $grade_id = $this->input->post('grade_id');
    echo json_encode($this->ttv2_model->get_section_list_from_grade($grade_id));
  }
}