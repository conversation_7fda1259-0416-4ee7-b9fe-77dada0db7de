<?php

class Student_exit extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_MASTER')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT.BASIC_VIEW')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('student/Student_exit_model', 'student_exit');
    $this->load->model('student/Student_Model');
  }

  public function index($stdId) {
    // echo $stdId; die();
    $data['studentId'] = $stdId;
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['admission_status'] = $this->settings->getSetting('admission_status');
    $data['stdData'] = $this->student_exit->getFullStdDataById($stdId);
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
    // echo "<pre>"; print_r($data['stdData']); die();
    // $data['father'] = $this->student_exit->getParentData($stdId, "Father");
    // $data['mother'] = $this->student_exit->getParentData($stdId, "Mother");
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'student/student_exit/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'student/student_exit/index_mobile';
    }else{
      $data['main_content']    = 'student/student_exit/index';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function exit_student($stdId, $transvers_to = 0, $selected_acadYearId ='', $classId ='', $selected_blueprint ='') {
    $data['transvers_to'] = $transvers_to;
    $data['selected_acadYearId'] = $selected_acadYearId;
    $data['selected_blueprint'] = $selected_blueprint;
    $data['classId'] = $classId;
    $data['studentId'] = $stdId;
    $data['admission_status'] = $this->settings->getSetting('admission_status');
    // $data['terminate_status'] = $this->settings->getSetting('terminate_status');
    $data['stdData'] = $this->student_exit->getFullStdDataById($stdId);
    $data['fatherUserName'] = $this->Student_Model->getFatherUsername($stdId);
    $data['motherUserName'] = $this->Student_Model->getMotherUsername($stdId);
    $data['library'] = $this->student_exit->get_library_details($stdId);
    $data['fees'] = $this->student_exit->get_fees_details($stdId);
    // echo "<pre>"; print_r($data['fees']); die();
    $data['siblings'] = $this->student_exit->get_siblings_data($stdId);
    $data['fee_adjust_permission'] = $this->authorization->isAuthorized('STUDENT.ADJUST_FEE_AMOUNT');
    // if (empty($data['fees']) && empty($data['library']) && empty($data['siblings'])) {
    //   $data['status'] = $this->student_exit->assign_alumnibyStdId($stdId, $data['fatherUserName']->username, $data['motherUserName']->username);
    // }
    $data['main_content']    = 'student/student_exit/exit_info';
    $this->load->view('inc/template', $data);
  }

  public function assign_alumni(){
    $stdId = $_POST['stdId'];
    $fatherUserName = $_POST['fatherUserName'];
    $motherUserName = $_POST['motherUserName'];
    $terminate_date = $_POST['terminate_date'];
    $terminate_remarks = $_POST['terminate_remarks'];
    $tc_number = $_POST['tc_number'];
    $user_login_status = $_POST['user_login_status'];

    $result = $this->student_exit->assign_alumnibyStdId($stdId, $fatherUserName, $motherUserName, $terminate_date, $terminate_remarks, $tc_number, $user_login_status);
    if($result){
      $year_id = $this->acad_year->getAcadYearId();
      $year = $this->acad_year->getAcadYearById($year_id);
      $this->Student_Model->store_edit_history($stdId,'','Student is terminated in the year '.$year);
    }
    echo $result;
  }

  public function exit_final_page($stdId){
    $data['studentId'] = $stdId;
    $data['admission_status'] = $this->settings->getSetting('admission_status');
    // echo "<pre>"; print_r($data['admission_status']); die();
    $data['stdData'] = $this->student_exit->getFullStdDataById($stdId);
    $data['main_content']    = 'student/student_exit/exit';
    $this->load->view('inc/template', $data);
  }
  
}