<?php

require APPPATH . 'libraries/REST_Controller.php';

class OnlineClasses extends REST_Controller
{
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
            $data = ['error' => 'User not logged in'];
            $this->response($data, REST_Controller::HTTP_UNAUTHORIZED);
			$this->output->_display();
			exit;
		}

		$this->load->model('parentv2/Onlineclass_model', 'oc_model');
		$this->load->model('parentv2/Student_model', 'student_model');
	}

	public function index_get($student_id = 0) {
		//TBD: Validate that Parent ID belongs to this User

		//Validate and parse start date
		if (empty($this->get('start_date'))) {
            $data = ['error' => 'Invalid Start Date'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}
		$start_date = date('Y-m-d',strtotime($this->get('start_date')));
		if (empty($start_date)) {
            $data = ['error' => 'Invalid Start Date'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}

		//Validate and parse end date
		if (empty($this->get('end_date'))) {
            $data = ['error' => 'Invalid End Date'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}
		$end_date = date('Y-m-d',strtotime($this->get('end_date')));
		if (empty($end_date)) {
            $data = ['error' => 'Invalid End Date'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;
		}

		//Validate student_id
		if (empty($student_id)) {
            $data = ['error' => 'Invalid Student Id'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}

		$data = $this->oc_model->getOnlineClasses($student_id, $start_date, $end_date);

        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	/***
	 * Online_class_id: 
	 * Student_id: Student Id is required to generate a token
	 */
	public function online_class_detail_get($student_id) {
		
		//Validate Student ID input
		if (empty($student_id)) {
            $data = ['error' => 'Invalid Student Id'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}

		//Get Student Record and validate
		$student_record = $this->student_model->get_student($student_id);
		if (empty($student_record)) {
            $data = ['error' => 'Student record does not exist'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;
		}

		//Validate Online Class ID input
		$online_class_id = $this->get('online_class_id');
		if (empty($online_class_id)) {
            $data = ['error' => 'Invalid Class Id'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}

		//Generate the Online class settings
		$online_class_detail = $this->oc_model->get_online_class_detail($online_class_id);
		if (count($online_class_detail) != 1) {
            $data = ['error' => 'Error in retrieving class details'];
			$this->response($data, REST_Controller::HTTP_INTERNAL_SERVER_ERROR);
			$this->output->_display();
			exit;	
		}

		$jwt = $this->get_token($online_class_detail[0]->session_id, $student_id, $student_record->student_name);
		if ($jwt == NULL) {
            $data = ['error' => 'Error during token generation'];
			$this->response($data, REST_Controller::HTTP_INTERNAL_SERVER_ERROR);
			$this->output->_display();
			exit;	
		}

		$data = $online_class_detail[0];
		$data->jwt = $jwt;

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;	
	}

	public function has_session_started_get($online_class_id) {
		//Validate Online Class ID input
		if (empty($online_class_id)) {
            $data = ['error' => 'Invalid Class Id'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}

		$online_class_detail = $this->oc_model->get_online_class_detail($online_class_id);
		if (count($online_class_detail) != 1) {
            $data = ['error' => 'Error in retrieving class details'];
			$this->response($data, REST_Controller::HTTP_INTERNAL_SERVER_ERROR);
			$this->output->_display();
			exit;	
		}

		$data = new stdClass();
		$data->has_session_started = $online_class_detail[0]->has_teacher_loggedin;
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	private function get_token($session_id, $student_id, $student_name) {
		$this->load->library('JWT');
		$secret = "bmsjman09rac1ezin_tx";
  
		$payload = [
		  "context" => [
			"user" => [
			  "avatar" => "",
			  "name" => $student_name,
			  "id" => $student_id,
			]
		  ],
		  "aud" => "nextelement",
		  "iss" => "nextelement",
		  "sub" => $this->settings->getSetting('online_class_server'),
		  "moderator" => false,
		  "room" => $session_id,
		  "exp" => time() + (1 * 24 * 60 * 60)
		];
		return JWT::encode($payload, $secret, 'HS256');
	}
	
}