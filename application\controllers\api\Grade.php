<?php

 require APPPATH . 'libraries/REST_Controller.php';

  class Grade extends REST_Controller {
    public function __construct() {
      parent::__construct();
      $this->load->model('enquiry_model');
      $this->api_key = '1fEkQiYkD7Fqyrid06BFXccK1QdiIaXh';
      $this->acad_year->loadAcadYearDataToSession();
    }

    public function index_get($acad_year_id = 22) {
      $data = $this->enquiry_model->getClassByAcadYear($acad_year_id);
      $this->response($data, REST_Controller::HTTP_OK);
      $this->output->_display();
      exit;
    }

    public function student_get() {
      $this->load->model('feesv2/fees_collection_model');
      $result = $this->fees_collection_model->get_student_data_for_jodo($_GET['identifier']);
      $this->response($result, REST_Controller::HTTP_OK);
      $this->output->_display();
      exit;
    
    }
  }