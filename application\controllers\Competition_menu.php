<?php

class Competition_menu extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
    }

    public function index() {
        $data['competition_permit'] = $this->authorization->isAuthorized('COMPETITION.MODULE');

        $site_url = site_url();
        $data['tiles'] = array(
          [
            'title' => 'Add',
            'sub_title' => 'Add Competition',
	          'icon' => 'svg_icons/add.svg',
            'url' => $site_url.'competition',
            'permission' => $data['competition_permit']
          ],
          [
            'title' => 'Report',
            'sub_title' => 'View Competition Report',
	          'icon' => 'svg_icons/assessment.svg',
            'url' => $site_url.'reports/competition/competitionreport_controller/index/1',
            'permission' => $data['competition_permit']
          ]
        );
        $data['tiles'] = checkTilePermissions($data['tiles']);
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'competition/menu_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'competition/menu_mobile';
        }else{
          $data['main_content'] = 'competition/menu';    	
        }
        $this->load->view('inc/template', $data);
    }

}