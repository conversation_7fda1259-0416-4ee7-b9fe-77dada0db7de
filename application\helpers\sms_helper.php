<?php if (!defined('BASEPATH')) exit('No direct script access allowed');


function _callStatusCheck($msgIdArray, $doNotEcho = FALSE){
    $CI =& get_instance();
    $msgIds = implode(",", $msgIdArray);
    // $customIds = implode(",", $customIdArray);
    $smsint =$CI->settings->getSetting('smsintergration');
    $apiUrl = $smsint->url;
    $apiKey = $smsint->api_key;
    $curl = curl_init();

    $username = CONFIG_ENV['job_server_username'];
    $password = CONFIG_ENV['job_server_password'];
    $return_url = site_url().'Callback_Controller/updateSMSStatus';

    curl_setopt_array($curl, array(
        CURLOPT_URL => CONFIG_ENV['job_server_smsstatus_uri'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERPWD => $username . ":" . $password,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => "msg_ids=".$msgIds."&api_url=".$apiUrl."&api_key=".$apiKey."&return_url=".$return_url,
        CURLOPT_HTTPHEADER => array(
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Content-Type: application/x-www-form-urlencoded",
            "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
        ),
    ));

   $response = curl_exec($curl);
    $err = curl_error($curl);

    curl_close($curl);

    if ($err) {
        return 0;
    } else {
        return 1;
    }
}

if (!function_exists('sendCommonSMS')) {
	//$sh_ids - array of stakeholder ids
	//$sh_type - Staff | Student | Custom
	//$source - SMS_UI | Competition | Attendance | Substitution 
	//$message - message to be sent
	//$sent_by - Id of message sender
	//$sent_to_str - Student Individual, Staff Individual, 1-A, 10-B
	//$ph_type - Both, Father or Mother
    function sendCommonSMS($sh_ids, $sh_type, $source, $message, $sent_by, $sent_to_str='', $ph_type='Both', $isUnicode=0, $acadYearId=0) {
        if (ENVIRONMENT !== 'production') {
          //Do not allow send sms for local
         return 0;
        }
        $CI =& get_instance();
        $CI->load->model('sms_model');
        $credits = $CI->sms_model->_calculateCredits($message, $isUnicode);
        $sms_count = count($sh_ids);
        if($sh_type == 'Student' && ($ph_type == '' || $ph_type == 'Both')) {
            $sms_count = 2 * $sms_count;
        }
        $credits = $credits * $sms_count;
        $availableCredits = $CI->sms_model->getRemainingSMSCredits();
        if($availableCredits < $credits) {
            return -1; //no credits available
        }
        //echo "<pre>";print_r($sh_ids); die();
        $smsData = $CI->sms_model->save_sms($sh_ids, $sh_type, $source, $message, $sent_by, $sent_to_str, $ph_type, $isUnicode, $acadYearId);
        if(!empty($smsData)) {
        	// $resultData = $CI->sms_model->get_msgDetails($insId);
            // echo "<pre>"; print_r($smsData); die();
        	$response = $CI->sms_sender->sendMsg($smsData['data'], $message, $isUnicode);

        	//if sms is sent update the status
        	if(!empty($response) && $response->status == 'OK'){
                // $status =(int) $CI->sms_model->updateStatus($response->data, $smsData['smsId']);
				$status =(int) $CI->sms_model->updateStatusNew($response->data, $smsData['smsId']);
                $msgIdArray = array();
                $customIdArray = array();
                foreach ($response->data as $key => $value) {
                    if($value->status == 'AWAITED-DLR') {
                        array_push($msgIdArray, $value->id);
                        array_push($customIdArray, $value->customid);
                    }
                }
                if(!empty($msgIdArray)) {
                    $jobSubmit = _callStatusCheck($msgIdArray, TRUE);
                }
				return 1;
			}
			else {
				$CI->sms_model->updateErrorStatus($smsData['smsId']);
				return 0;
			}
        }
        return 0;
    }
}

if (!function_exists('sendToCustomNumbers')) {
	//$custom_numbers - array of numbers
	//$smsId - smsId from which sms to be sent
    function sendToCustomNumbers($custom_numbers,$source, $message, $smsId=0, $isUnicode=0, $acadYearId=0, $sender=0) {
        if (ENVIRONMENT !== 'production') {
          //Do not allow send sms for local
          return 0;
        }
        $CI =& get_instance();
        $CI->load->model('sms_model');
        $credits = $CI->sms_model->_calculateCredits($message, $isUnicode);
        $credits = count($custom_numbers) * $credits;
        $availableCredits = $CI->sms_model->getRemainingSMSCredits();
        if($availableCredits < $credits) {
            return -1; //no credits available
        }
        $smsData = $CI->sms_model->save_custom_numbers($custom_numbers, $source, $message, $smsId, $isUnicode, $acadYearId, $sender);
        if(!empty($smsData)) {
        	// $resultData = $CI->sms_model->get_msgDetails($insId, 1);
            // echo "<pre>"; print_r($smsData); die();
        	$response = $CI->sms_sender->sendMsg($smsData['data'], $message, $isUnicode);
            // echo "<pre>Tests"; print_r($response);die();
        	//if sms is sent update the status
        	if(!empty($response) && $response->status == 'OK'){
                // $status =(int) $CI->sms_model->updateStatus($response->data, $smsData['smsId']);
				$status =(int) $CI->sms_model->updateStatusNew($response->data, $smsData['smsId']);
                $msgIdArray = array();
                $customIdArray = array();
                foreach ($response->data as $key => $value) {
                    if($value->status == 'AWAITED-DLR') {
                        array_push($msgIdArray, $value->id);
                        array_push($customIdArray, $value->customid);
                    }
                }
                if(!empty($msgIdArray)) {
                    $jobSubmit = _callStatusCheck($msgIdArray, TRUE);
                }
				return 1;
			}
			else {
				$CI->sms_model->updateErrorStatus($smsData['smsId']);
				return 0;
			}
        }
        return 0;
    }
}

if (!function_exists('sendCustomMsg')) {
	//$shIds_msg - array of messages with stakeholder id as key.array([student_id] => "text message1",[student_id] => "text message2")
	//$sh_type - Staff | Student | Custom
	//$source - SMS_UI | Competition | Attendance | Substitution 
	//$message - message to be sent
	//$sent_by - Id of message sender
	//$sent_to_str - Student Individual | Staff Individual | 1-A, 10-B
	//$ph_type - Both, Father or Mother
    function sendCustomMsg($shIds_msg, $sh_type, $source, $message, $sent_by, $sent_to_str='', $ph_type='', $isUnicode=0, $acadYearId=0) {
        if (ENVIRONMENT !== 'production') {
          //Do not allow send sms for local
          return 0;
        }
        $CI =& get_instance();
        $CI->load->model('sms_model');
        $sh_ids = array();
        foreach ($shIds_msg as $key => $value) {
            array_push($sh_ids, $key);
		}

        $credits = $CI->sms_model->_calculateCredits($message, $isUnicode);
        $sms_count = count($sh_ids);
        if($sh_type == 'Student' && ($ph_type == '' || $ph_type == 'Both')) {
            $sms_count = 2 * $sms_count;
        }
        $credits = $credits * $sms_count;
        $availableCredits = $CI->sms_model->getRemainingSMSCredits();
        if($availableCredits < $credits) {
            return -1; //no credits available
        }
		
        $smsData = $CI->sms_model->save_custom_sms($shIds_msg, $sh_type, $source, $message, $sent_by, $sent_to_str, $ph_type, $isUnicode, $acadYearId);
        if(!empty($smsData)) {
        	$response = $CI->sms_sender->sendCustomMsg($smsData['data'], $isUnicode);

        	//if sms is sent update the status
        	if(!empty($response) && $response->status == 'OK'){
                // $status =(int) $CI->sms_model->updateCustomMsgStatus($response->data, $smsData['smsIds']);
				$status =(int) $CI->sms_model->updateCustomMsgStatusNew($response->data, $smsData['smsIds']);
                $msgIdArray = array();
                $customIdArray = array();
                foreach ($response->data as $key => $value) {
                    if($value->status == 'AWAITED-DLR') {
                        array_push($msgIdArray, $value->id);
                        array_push($customIdArray, $value->customid);
                    }
                }
                if(!empty($msgIdArray)) {
                    $jobSubmit = _callStatusCheck($msgIdArray, TRUE);
                }
				return 1;
			}
			else {
				$CI->sms_model->updateCustomErrorStatus($smsData['smsIds']);
				return 0;
			}
        }
        return 0;
    }
}

if (!function_exists('sendToCustomNumbersForFollowups')) {
    //$custom_numbers - array of numbers
    //$smsId - smsId from which sms to be sent
    function sendToCustomNumbersForFollowups($custom_numbers,$source, $message,$userType, $sent_to, $smsId=0, $isUnicode=0, $acadYearId=0, $sender=0) {
        if (ENVIRONMENT !== 'production') {
          //Do not allow send sms for local
          return 0;
        }
        $CI =& get_instance();
        $CI->load->model('sms_model');
        $credits = $CI->sms_model->_calculateCredits($message, $isUnicode);
        $credits = count($custom_numbers) * $credits;
        $availableCredits = $CI->sms_model->getRemainingSMSCredits();
        if($availableCredits < $credits) {
            return -1; //no credits available
        }
        $smsData = $CI->sms_model->save_custom_numbers_for_followup($custom_numbers, $source, $userType, $sent_to, $message, $smsId, $isUnicode, $acadYearId, $sender);
        if(!empty($smsData)) {
            // $resultData = $CI->sms_model->get_msgDetails($insId, 1);
            // echo "<pre>"; print_r($smsData); die();
            $response = $CI->sms_sender->sendMsg($smsData['data'], $message, $isUnicode);
            // echo "<pre>Tests"; print_r($response);die();
            //if sms is sent update the status
            if(!empty($response) && $response->status == 'OK'){
                // $status =(int) $CI->sms_model->updateStatus($response->data, $smsData['smsId']);
                $status =(int) $CI->sms_model->updateStatusNew($response->data, $smsData['smsId']);
                $msgIdArray = array();
                $customIdArray = array();
                foreach ($response->data as $key => $value) {
                    if($value->status == 'AWAITED-DLR') {
                        array_push($msgIdArray, $value->id);
                        array_push($customIdArray, $value->customid);
                    }
                }
                if(!empty($msgIdArray)) {
                    $jobSubmit = _callStatusCheck($msgIdArray, TRUE);
                }
                return 1;
            }
            else {
                $CI->sms_model->updateErrorStatus($smsData['smsId']);
                return 0;
            }
        }
        return 0;
    }
}