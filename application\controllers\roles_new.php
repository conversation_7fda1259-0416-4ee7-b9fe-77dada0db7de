<?php

class Roles_new extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.ASSIGN_ROLES_AND_PRIVILEGES')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('role_new');
    }

    //Landing function for viewing and adding roles
    public function index() {
        $data['roles'] = $this->role_new->get_all_roles();
        //echo "<pre>"; print_r($data['roles']); die();
        foreach ($data['roles'] as $key => $value) {
            $data['no_of_staff'][$value->id] = $this->role_new->getno_of_staff($value->id);
            $data['no_of_privileges'][$value->id] = $this->role_new->getno_of_privileges($value->id);
        }
        $data['main_content'] = 'auth/role_crud_new';
        $this->load->view('inc/template', $data);
    }

    public function add_staff($role_id){
        $data['role_id'] = $role_id;
        $data['staff'] = $this->role_new->get_all_staff();
        $data['staff_assinged'] = $this->role_new->get_staffassignedby_roleid($role_id);
        $data['main_content'] = 'auth/staff_crud_new';
        $this->load->view('inc/template', $data);
    }
    //Landing function for assigning staff and privileges to roles
    public function add_staff_privileges($role_id) {
        $data['role_id'] = $role_id;
        $data['staff'] = $this->role_new->get_all_staff();
        $data['privileges'] = $this->role_new->get_privileges();
        $data['staff_assinged'] = $this->role_new->get_staffassignedby_roleid($role_id);
        $data['privileges_assinged'] = $this->role_new->get_privilegesassignedby_roleid($role_id);
        $data['privileges_list'] = $this->role_new->getPriviligesList();
        foreach ($data['privileges_list'] as $key => $value) {
        	//echo "<pre>";print_r($data['privileges']); die();
        	$data['sub_priviliges'][$value->name] = $this->role_new->getSubPrivilegesList($value->id);
        }
        
        //echo "<pre>";print_r($data['privileges_assinged']); die();
        $data['main_content'] = 'auth/privileges_crud_new';
        $this->load->view('inc/template', $data);
    }

    // Privileges 

    public function add_privileges() {
        $data['privileges'] = $this->role_new->get_privilegesAll();

        // echo "<pre>"; print_r($data['privileges']); die();

        $data['main_content'] = 'auth/privileges_add_new';
        $this->load->view('inc/template', $data);
    }

    public function submit_privileges() {
        $insert_privileges = $this->role_new->submit_privileges();
        if ($insert_privileges) {
            $this->session->set_flashdata('flashSuccess', 'Privileges created successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('roles_new/add_privileges');
    }

    public function submit_sub_privileges($privilege_id){
        $insert_sub_privileges = $this->role_new->submit_sub_privileges($privilege_id);
        if ($insert_privileges) {
            $this->session->set_flashdata('flashSuccess', 'Privileges created successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('roles_new/getSubprivileges/'.$privilege_id);
    }

    public function add_sub_privilege($privilege_id){
        $data['privileges_name'] = $this->role_new->get_privilegesnamebyId($privilege_id);
        $data['privileges'] = $this->role_new->get_privilegesAll();
        //echo "<pre>";print_r($data['privileges_name']); die();
        $data['main_content'] = 'auth/add_sub_privilege_new';
        $this->load->view('inc/template', $data);
    }

    public function add_sub_privileges($privilege_id) {
        $data['privileges_name'] = $this->role_new->get_privilegesnamebyId($privilege_id);
        $data['privileges'] = $this->role_new->get_privilegesAll();

        $data['main_content'] = 'auth/privileges_add_new';
        $this->load->view('inc/template', $data);
    }

    // Add sub privileges update by privileges id
    public function insert_sub_privileges($privilege_id) {
        $insert_sub_privileges = $this->role_new->submit_add_sub_privileges($privilege_id);
        if ($insert_sub_privileges) {
            $this->session->set_flashdata('flashSuccess', 'Role created successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('roles_new/index');
    }

    public function submit_role() {
        $insert_role = $this->role_new->submit_role();
        if ($insert_role) {
            $this->session->set_flashdata('flashSuccess', 'Role created successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('roles_new');
    }

    public function switch_role_mode() {
        echo $this->role_new->switch_role_mode();
    }

    public function submit_staff($role_id){
        $result = $this->role_new->assign_staff($role_id);
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Submit Successfully');
            redirect('roles/index');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('roles_new/add_staff_privileges/' . $role_id);
        }
    }

    public function submit_staff_and_privileges_to_roles($role_id) {
    	//echo "<pre>";print_r($role_id); die(); 
        $result = $this->role_new->assign_staff_and_privileges_to_roles($role_id);
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Submit Successfully');
            redirect('roles/index');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('roles_new/add_staff_privileges/' . $role_id);
        }
    }

    ///newlly added code 
    public function editprivileges($id) {
           $data['privileges'] = $this->role_new->get_privilegesAll();
        $data['edit_privileges'] = $this->role_new->edit_Privileges($id);
        $data['main_content'] = 'auth/privileges_add_new';
        $this->load->view('inc/template', $data);
    }

    public function update_privileges($id) {
        $updated_result = $this->role_new->update_Privileges($id);
        if ($updated_result) {
            $this->session->set_flashdata('flashSuccess', 'Updated Successfully');
            redirect('roles_new/add_privileges');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('roles_new/add_privileges');
        }
    }

    public function deleteprivileges($id) {
        $delete_result = $this->role_new->delete_Privileges($id);
        if ($delete_result) {
            $this->session->set_flashdata('flashSuccess', 'Deleted Successfully');
            redirect('roles_new/add_privileges');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('roles_new/add_privileges');
        }
    }

    public function getSubprivileges($id) {
        $data['edit_privileges'] = $this->role_new->get_SubPrivileges($id);
        $data['privilege'] = $this->role_new->edit_Privileges($id);
        //delete_sub_privileges 
        //echo "<pre>"; print_r($data['privilege']); die();
        $data['main_content'] = 'auth/editsubprivileges_new';
        $this->load->view('inc/template', $data);
    }

    public function update_sub_privileges() {
        $name = $_POST['name'];
        $id = $_POST['id'];
        $privilege_id = $_POST['privilege_id'];
        $update_privileges = $this->role_new->update_SubPrivileges($id, $privilege_id, $name);
        if ($update_privileges) {

            echo "updated sub privileges successfully";
        } else {

            echo "something went wrong";
        }
    }

    public function delete_sub_privileges() {

        $id = $_POST['id'];
        $privilege_id = $_POST['privilege_id'];
        $update_privileges = $this->role_new->delete_SubPrivileges($id, $privilege_id);
        if ($update_privileges) {

            echo "Deleted sub privileges successfully";
        } else {

            echo "something went wrong";
        }
    }

    public function add_privilege(){
        $data['main_content'] = 'auth/add_privilege_new';
        $this->load->view('inc/template', $data);
    }

}
