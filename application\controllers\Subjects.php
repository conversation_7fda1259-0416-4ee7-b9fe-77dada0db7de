<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  15 March 2018
 *
 * Description: Controller class for Subjects
 *
 * Requirements: PHP5 or above
 *
 */
class Subjects extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
            redirect('dashboard', 'refresh');
        }  
        $this->load->model('subject');
        $this->config->load('form_elements');
    }

    /* Landing function for this page */
    public function index($classId = '') {
        if (empty($classId))
          $classId = $this->input->post('classId');
    
        $data['ayClassList'] = $this->subject->getAllYearClassList();
        $data['classList'] = $this->subject->getClassList();

        if (empty($classId) && !empty($data['classList']))
          $classId = $data['classList'][0]->id;

        $data['subjectList'] = $this->subject->getSubjectList($classId);
        $data['selectedClassId'] = $classId;
        $data['main_content'] = 'subjects/index';
        $this->load->view('inc/template', $data);
    }

    public function add($classId = 1) {
        $data['selectedClassId'] = $classId;
        $data['selectedClassName'] = $this->subject->getClassName($classId);
        $data['subjectColors'] = $this->config->item('subjectColors');
        $data['main_content'] = 'subjects/add';
        $this->load->view('inc/template', $data);
    }

    public function submit_subject($classId) {
        $result = $this->subject->addSubject($classId);

        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Subject inserted successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('subjects/index/' . $classId);
    }

    public function delete($classId, $subjectId) {
        if ($this->subject->deleteSubject($subjectId)) {
            $this->session->set_flashdata('flashSuccess', 'Subject deleted successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('subjects/index/' . $classId);
    }

    //edit subject
    public function edit($subjectId) {
        $data['subObj'] = $this->subject->getSubjectDetails($subjectId);
        $data['subjectColors'] = $this->config->item('subjectColors');
        //echo "<pre>";print_r($data['subObj']);die();
        $data['main_content'] = 'subjects/edit';
        $this->load->view('inc/template', $data);
    }

    //update subject
    public function update_subject($classId) {
        $result = $this->subject->updateSubject($classId);
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Subject Updated successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('subjects/index/' . $classId);
    }

    //Ajax call
    public function getSubjects() {
        $classId = $_POST['classId'];
        $data['subjects'] = $this->subject->getSubjectList($classId);
        echo json_encode($data);
    }

    //Form Call
    public function addCloneSubjects() {
        // echo '<pre>';print_r($this->input->post());die();
        $classId = $this->input->post('toClass');
        $result = $this->subject->insertCloneSubjects();
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Subjects Cloned successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('subjects/index/' . $classId);
    }

    public function skills_crud($subjectId) {
        $skillsList = $this->subject->getSkillsListWithSubskillsAsString($subjectId);


        $data['subjectObj'] = $this->subject->getSubjectById($subjectId);
        $data['skillsList'] = $skillsList;

        $data['main_content'] = 'subjects/skills_crud';
        $this->load->view('inc/template', $data);
    }

//esit subject sub skills
    public function subject_skill_sedit($Id) {

        $data['edit_subject_skill'] = $this->subject->editSubjectSkills($Id);
        $data['edit_subject_sub_skill'] = $this->subject->editSubjectsubSkills($Id);


        $data['main_content'] = 'subjects/skills_crud';
        $this->load->view('inc/template', $data);
    }

    //edit subject siklls
    public function getSubSkills($subId, $skill_id) {
        $data['datskillsListInfo'] = $this->subject->getSkillsSubList($subId, $skill_id);
        //echo "<pre>";print_r($data['datskillsListInfo']);die();
        $data['main_content'] = 'subjects/skills_edit';

        $this->load->view('inc/template', $data);
    }

    public function edit_subject_skillsId($SkillId, $subjectObj) {
        $skillsList = $this->subject->getSkillsListWithSubskillsAsString($subjectObj);

        $data['subjectObj'] = $this->subject->getSubjectById($subjectObj);
        $data['skillsList'] = $skillsList;
        $data['getSubjectSubSkillsDataId'] = $this->subject->getSubjectSubSkillsId($SkillId, $subjectObj);
        //$data['getSubjectSubSkillsDataId'] = $getSubjectSubSkillsDataId;
        //echo "<pre>";print_r( $data['getSubjectSubSkillsDataId']);
        $data['main_content'] = 'subjects/skills_crud';

        $this->load->view('inc/template', $data);
    }

    //update subject skills
    public function update_skills($skillId, $subjectId) {
        $getData = $this->subject->updateSkills($skillId, $subjectId);
        if ($getData) {
            $this->session->set_flashdata('flashSuccess', 'skills updated successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('subjects/skills_crud/' . $subjectId);
    }

    //edit sub skills
    public function edit_sub_skills() {
        $name = $_POST['name'];
        $id = $_POST['id'];
        $skillid = $_POST['skillid'];
        $updateSubjectsubSkillsinfo = $this->subject->updateSubjectsubSkills($name, $id, $skillid);
        //echo "<pre>";print_r( $data['getsubskils']);die();
        if ($updateSubjectsubSkillsinfo == "1") {
            echo "updated sub skill successfully";
        } else {
            echo "failed to update";
        }
    }

    //update subject sub  skills
    public function update_sub_skills($Id, $skillid) {
        foreach ($Id as $value) {
            $updatetsubskils = $this->subject->updateSubSkills($value);
        }

        if ($updatetsubskils) {
            $this->session->set_flashdata('flashSuccess', 'Subject sub Skills updated ');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('subjects/subject_crud');
    }

    public function submit_skills() {
        //echo '<pre>';print_r($this->input->post());

        $result = $this->subject->addSubjectSkills();

        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Subject Skills inserted successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('subjects/skills_crud/' . $this->input->post('subjectID'));
    }

    public function skills_delete($subjectId, $skillId) {

        $result = $this->subject->deleteSkill($subjectId, $skillId);

        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Subject Skills delete successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('subjects/skills_crud/' . $subjectId);
    }

    public function electives_crud($subjectId) {
        $electivesList = $this->subject->getElectivesList($subjectId);

        $data['subjectObj'] = $this->subject->getSubjectById($subjectId);
        $data['electivesList'] = $electivesList;

        $data['main_content'] = 'subjects/electives_crud';
        $this->load->view('inc/template', $data);
    }

    public function submit_electives() {
        //echo '<pre>';print_r($this->input->post()); die();

        $result = $this->subject->addSubjectElectives();

        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Subject Skills inserted successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('subjects/electives_crud/' . $this->input->post('subjectID'));
    }

    public function edit_subject_electivesId($electivesId, $subjectObj) {
        $electivesList = $this->subject->getElectivesList($subjectObj);

        $data['subjectObj'] = $this->subject->getSubjectById($subjectObj);
        $data['electivesList'] = $electivesList;
        $data['getSubjectSubElectivesDataId'] = $this->subject->getSubjectElectivesId($electivesId);
        //$data['getSubjectSubElectivesDataId'] = $getSubjectSubElectivesDataId;
        // echo "<pre>";print_r( $data); die();
        $data['main_content'] = 'subjects/electives_crud';

        $this->load->view('inc/template', $data);
    }

    //update subject electives
    public function update_electives($electivesId, $subjectId) {
        // echo "<pre>";print_r($this->input->post());die();
        $getData = $this->subject->updateElectives($electivesId, $subjectId);
        if ($getData) {
            $this->session->set_flashdata('flashSuccess', 'Electives updated successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('subjects/electives_crud/' . $subjectId);
    }

    public function electives_delete($subjectId, $skillId) {

        $result = $this->subject->deleteElectives($subjectId, $skillId);

        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Subject Electives delete successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('subjects/electives_crud/' . $subjectId);
    }

}

?>	