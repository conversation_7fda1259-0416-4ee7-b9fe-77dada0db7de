<?php

class Menu extends CI_Controller {
	function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('DAILY_PLANNER')) {
        redirect('dashboard', 'refresh');
        }
        if (!$this->authorization->isAuthorized('DAILY_PLANNER.MODULE')) {
        redirect('dashboard', 'refresh');
        }
    }

  //Landing function to show non-compliance menu
    public function index() {
        $site_url = site_url();

        $data['tiles'] = array(
            [
                'title' => 'Daily Planner Calendar',
                'sub_title' => 'View and add Tasks (Kanban and Calendar)',
                'icon' => 'svg_icons/timetable.svg',
                'url' => $site_url.'daily_planner/Daily_planner/daily_planner',
                'permission' =>  $this->authorization->isAuthorized('DAILY_PLANNER.DAILY_PLANNER_CALENDAR'),
            ],
        );
        $data['tiles'] = checkTilePermissions($data['tiles']);

        $data['adminstration'] = array(
            [
                'title' => 'Manage Task Types',
                'sub_title' => 'Task Type for Individual Tasks',
                'icon' => 'svg_icons/staff.svg',
                'url' => $site_url.'stb/staff_task_types/staff_task_types/'.urlencode('daily_planner'),
                'permission' =>  $this->authorization->isAuthorized('DAILY_PLANNER.MANAGE_STAFF_TASK_TYPES')
            ],
            [
                'title' => 'Manage Task Types (Admin)',
                'sub_title' => 'Task Type for Individual Tasks',
                'icon' => 'svg_icons/staff.svg',
                'url' => $site_url.'stb/staff_task_types/admin_task_types/'.urlencode('daily_planner'),
                'permission' =>  $this->authorization->isSuperAdmin(),
            ],
        );
        $data['adminstration'] = checkTilePermissions($data['adminstration']);

        $data['main_content']    = 'daily_planner/menu';
        $this->load->view('inc/template', $data);
    }
}