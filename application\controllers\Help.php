<?php

class Help extends CI_Controller {
               
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->load->library('filemanager');
        $this->load->model('Helpdesk_model', 'helpdesk');
    }
           
    public function index(){
        $type = $_POST['type'];
        $data['back_url'] = $_POST['back_url'];
        $data['path_prefix'] = $this->filemanager->getFilePath('');
        $this->load->helper('chatdata_helper');
        $data['chatData'] = getChatData($type);
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'help/tablet_index';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'help/mobile_index';
        }else{
          $data['main_content']    = 'help/index';      	
        }
        
        $this->load->view('inc/template', $data);
    }

}