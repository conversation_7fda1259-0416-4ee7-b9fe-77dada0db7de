<?php

class Suy_crm_controller extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
    }
    $this->load->model('suycrm/suy_model');
  } 

  public function index() { 
      $data['main_content'] = 'suy_crm/dashboard';
      $this->load->view('inc/template', $data);
  }

  public function addProgramLevel() {
    $data['main_content'] = 'suy_crm/add_program_details';
    $this->load->view('inc/template', $data);
  }

  public function getProgramsuyName(){
    $data = $this->suy_model->getProgramsuyName();
    echo json_encode($data);
  }

  public function getPrograms_suy(){
    $data['programs'] = $this->suy_model->getPrograms_suy();
    echo json_encode($data);
  }

  public function program_detail_insert(){
    $data = $this->suy_model->suy_programLevelData();
    echo ($data);
  }

  public function program_detail_update(){
    $data = $this->suy_model->programLevelUpdateData();
    echo ($data);
  }

  public function getProgramLevelDetails_suy(){
    $program_id = $_POST['program_id'];

    $data = $this->suy_model->getProgramLevelDetails_suy($program_id);
    echo json_encode($data);
  }

  public function suy_addProgram(){
    $data = $this->suy_model->suy_addProgram();
    echo json_encode($data);
  }

  public function registration_form(){
    $data['programNames'] = $this->suy_model->getProgramsuyName();
    $data['main_content'] = 'suy_crm/registration_form';
    $this->load->view('inc/template', $data);

  }

  public function getLevels(){
    $program = $_POST['program'];
    $language = $_POST['language'];
    $data['level'] = $this->suy_model->getLevels($program, $language);
    echo json_encode($data);
  }

  public function getLevelDetails(){
    $level = $_POST['level'];
    $program = $_POST['program'];
    $language = $_POST['language'];
    $data['batch'] = $this->suy_model->getBatch($level, $program, $language);

    echo json_encode($data);
  }

  public function getDurationAmount(){
    $level = $_POST['level'];
    $program = $_POST['program'];
    $language = $_POST['language'];
    $data['durationAmount'] = $this->suy_model->getDurationAmount($level, $program, $language);
    echo json_encode($data);
  }

  public function updateLevelStatusToInactive(){
      $level_id = $_POST['level_id'];
         
      $data = $this->suy_model->updateLevelStatusToInactive($level_id);
      echo ($data);
    }
  
    public function updateLevelStatusToActive() {
      $level_id = $_POST['level_id'];
      $data = $this->suy_model->updateLevelStatusToActive($level_id);
      echo($data);
    }

  public function submit_registration(){
    $status = $this->suy_model->submit_registration();
    if($status){
      $this->session->set_flashdata('flashSuccess', 'Registered successfully.');
      redirect('suy_crm/suy_crm_controler/display_summary/'.$status);
    }else{
      $this->session->set_flashdata('flashError', 'Failed to add.');
      redirect('suy_crm/suy_crm_controler/registration_form');

    }
  }

  public function display_summary($id){
    $data['details'] = $this->suy_model->getRegisteredDetails($id);
    $data['main_content'] = 'suy_crm/display_summary';
    $this->load->view('inc/template', $data);
  }

  public function getLanguages(){
    $program = $_POST['program'];
    $data['language'] = $this->suy_model->getLanguages($program);
    echo json_encode($data);
  }

  public function follow_up(){
    $data['main_content'] = 'suy_crm/follow_up';
    $this->load->view('inc/template', $data);
  }

  public function getRegistrations(){
    $data['details'] = $this->suy_model->getRegistrations();
    echo json_encode($data);
  }

  public function getRegistrationDetails(){
    $data['regDetails'] = $this->suy_model->getRegistrationDetails();
    $data['selected_status'] = $this->suy_model->getStatus();
    echo json_encode($data);
  }

  public function updateStatus(){
    $data = $this->suy_model->updateStatus();
    echo ($data);
  }

  public function get_add_programme_details_by_id(){
    $level_id = $_POST['level_id'];
    $result = $this->suy_model->get_programme_details_by_id($level_id);
    echo json_encode($result);
  }
  public function getBatchCodeDetails(){
    $level_id = $_POST['level_id'];
    $result = $this->suy_model-> get_batch_code($level_id);
    echo json_encode($result);
  }

  public function generate_batch_code(){
    $level_id = $_POST['level_id'];
    $data=$this->suy_model->generate_batch_code_insert($level_id);
    echo ($data);
  }


}
?>