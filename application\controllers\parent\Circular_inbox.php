<?php

class Circular_inbox extends CI_Controller
{
	function __construct()
	{
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN') || !$this->authorization->isModuleEnabled('CIRCULARS_V2')) {
			redirect('dashboard', 'refresh');
		}
		$this->load->model('communication/circular_inbox_model');
		$this->load->model('parent_model');
		$this->load->library('filemanager');
	}

	public function index_old($show_unread_only=0) {
		$data['parent_id'] = $this->authorization->getAvatarStakeHolderId();
		$data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
		$modules = json_decode($this->settings->getSetting('deactivation_modules'));
		$is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($data['student_id']);
		$data['acadYears'] = $this->circular_inbox_model->get_acad_year_id_from_circular();
		$data['is_deactivated'] = 0;
		if(!empty($modules)){
			if($is_deactivated && in_array('Circulars', $modules)) {
				$data['is_deactivated'] = 1;
			}
		}
		
		$avatar_type = 2;
		$data['category'] = 'latest';
		$data['cid'] = 0;
		if(isset($_POST['category_name'])) {
			$data['category'] = $_POST['category_name'];
		}
		if(isset($_POST['cid'])) {
			$data['cid'] = $_POST['cid'];
		}
		$data['academic_year'] = 0;
		if (isset($_POST['academic_year'])) {
			$data['academic_year'] = $_POST['academic_year'];
		}else if (!empty($data['acadYears'])) {
			//Changing this to get previous acad year as default if month is less than May
			// $data['academic_year'] = $data['acadYears'][0]->acad_year_id;
			$data['academic_year'] = $_SESSION['acad_year'];
		}
		$data['show_unread_only'] = $show_unread_only;
		$this->load->model('communication/circular_model', 'circular');
		$data['categories'] = $this->circular->getCircularCategories();
		$data['circulars'] = $this->circular_inbox_model->getCircularsAndEmails($data['parent_id'], $avatar_type, $data['category'], $data['academic_year']);
		$data['main_content'] = 'parent/notification/circularsv2/inbox';
		$this->load->view('inc/template', $data);
	}

	public function index(){
		$data['parent_id'] = $this->authorization->getAvatarStakeHolderId();
		// $data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['student_id'] = $this->parent_model->get_student_id_by_user_id($data['parent_id']);
		// echo "<pre>";print_r($data['student_id']);die();
		$modules = json_decode($this->settings->getSetting('deactivation_modules'));
		$is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($data['student_id']);
		$data['is_deactivated'] = 0;
		if(!empty($modules)){
			if($is_deactivated && in_array('Circulars', $modules)) {
				$data['is_deactivated'] = 1;
			}
		}
		
		$this->load->model('communication/circular_model', 'circular');
		$data['categories'] = $this->circular->getCircularCategories();
		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'parent/notification/circularsv2/tablet_inbox_new.php';
		}else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'parent/notification/circularsv2/mobile_inbox_new.php';
		}else{
			$data['main_content'] = 'parent/notification/circularsv2/newstyle_new.php';  	
		}
		$this->load->view('inc/template', $data);
	}

	public function getCirculars() {

		$parent_id = $this->input->post('parent_id');
		$category = $this->input->post('category');
		$start_date = $this->input->post('from_date');
		$end_date = $this->input->post('to_date');
		$show_unread_only = $this->input->post('show_unread');
		$avatar_type = 2;
		// echo "<pre>";print_r($_POST);die();
		$circulars = $this->circular_inbox_model->getCircularsAndEmailsByDateRange($parent_id, $avatar_type, $category, $start_date, $end_date, $show_unread_only);
		echo json_encode($circulars);
	}

	public function view() {
		if(!isset($_POST['circular_id'])) {
			redirect('parent/Circular_inbox');
		}
		$data['circular_id'] = $_POST['circular_id'];
		$data['parent_circular_id'] = isset($_POST['parent_circular_id'])?$_POST['parent_circular_id']:'';
		$data['is_read'] = $_POST['is_read'];
		$data['category'] = isset($_POST['category'])?$_POST['category']:'all';
		$data['circular'] = 'new';
		$data['circularData'] = $this->circular_inbox_model->getCircularDeatils($data['circular_id'], 0);

		if ($this->mobile_detect->isTablet()) {
			$data['main_content']    = 'parent/notification/circularsv2/view_tablet';	
        }else if($this->mobile_detect->isMobile()){
			$data['main_content']    = 'parent/notification/circularsv2/view_mobile';	
        }else{
			$data['main_content']    = 'parent/notification/circularsv2/view_desktop';		
        }
		// if(!$this->mobile_detect->isMobile()) { 
		// 	$data['main_content']    = 'parent/notification/circularsv2/view_desktop';		
		// } 
		// else { 
		// 	$data['main_content']    = 'parent/notification/circularsv2/view_mobile';			
		// } 
		$this->load->view('inc/template', $data);
	}

	public function downloadCircularAttachment() {
		// trigger_error(json_encode($this->input->post()));
		$path = $this->input->post('file_path');
		// trigger_error($path);
		$file = explode("/", $path);
		$file_name = $file[count($file)-1];
		$data = file_get_contents($path);
		$this->load->helper('download');
		force_download($file_name, $data, TRUE);
		$this->load->library('user_agent');
		redirect($this->agent->referrer());
	}

	public function downloadMobileCircularAttachment($cid, $index, $type) {
		if($type == 'new') {
			$table = 'circularv2_master';
		} else {
			$table = 'circular_master';
		}
		$file_link = $this->circular_inbox_model->downloadCircularAttachment($cid, $table, $type);
		if($file_link->is_file_path_json) {
			$paths = json_decode($file_link->file_path);
			$link = $paths[$index]->path;
			$fname = $paths[$index]->name;
		} else {
			$paths = explode(",", $file_link->file_path);
			$link = $paths[$index];
			$file = explode("/", $link);
			$file_name = 'circular'.($index+1);
			$fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
		}
		// echo '<pre>'; print_r($fname); die();
		$url = $this->filemanager->getFilePath($link);
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download($fname, $data, TRUE);
		$this->load->library('user_agent');
		redirect($this->agent->referrer());
	}

	public function downloadCircular($id, $type)
	{
		if($type == 'new') {
			$table = 'circularv2_master';
		} else {
			$table = 'circular_master';
		}
		$link = $this->circular_inbox_model->downloadCircularAttachment($id, $table);
		$file = explode("/", $link);
		$file_name = $file[count($file)-1];
		$url = $this->filemanager->getFilePath($link);
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download($file_name, $data, TRUE);
	}

	public function view_old() {
		$data['circular_id'] = $_POST['circular_id'];
		$data['is_read'] = 1;
		$data['circular'] = 'old';
		$data['category'] = $_POST['category'];
		$data['circularData'] = $this->circular_inbox_model->getOldCircularDeatils($data['circular_id']);
		// echo "<pre>"; print_r($data); die();
		if(!$this->mobile_detect->isMobile()) { 
			$data['main_content']    = 'parent/notification/circularsv2/view_desktop';		
		} 
		else { 
			$data['main_content']    = 'parent/notification/circularsv2/view_mobile';			
		} 
		$this->load->view('inc/template', $data);
	}

	public function makeRead() {
		$parent_circular_id = $_POST['parent_circular_id'];
		// $stakeholder_id = $this->authorization->getAvatarStakeHolderId();
		// $avatar_type = 2;
		echo $this->circular_inbox_model->makeCircularAsRead($parent_circular_id);
	}
	
	public function get_circular_details(){
		$circular_id = $_POST['circularId'];
		$parent_circular_id = $_POST['parent_circular_id'];
		$result = $this->circular_inbox_model->getCircularDeatils($circular_id, $parent_circular_id);
		echo json_encode($result);
	}
}