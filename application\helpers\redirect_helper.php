<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * JavaScript Redirect Helper
 * 
 * Contains functions for handling redirects via JavaScript
 * when PHP header redirects might not work
 */

if (!function_exists('js_redirect')) {
    /**
     * Redirect using JavaScript
     * 
     * Performs a client-side redirect using JavaScript, useful when:
     * - Headers have already been sent
     * - You need to clear client-side data before redirecting
     * - You want to show a message before redirecting
     * 
     * @param string $url The URL to redirect to
     * @param string $message Optional message to display during redirect
     * @param int $delay Delay in milliseconds before redirect
     * @param bool $clear_storage Whether to clear localStorage and sessionStorage
     * @param bool $exit Whether to exit PHP execution after sending redirect
     * @return void
     */
    function js_redirect($url, $message = 'Redirecting...', $delay = 1000, $clear_storage = true, $exit = true) {
        // Get CI instance to access settings
        $CI =& get_instance();
        
        // Make sure settings library is loaded
        if (!isset($CI->settings)) {
            $CI->load->library('settings');
        }
        
        // Get school information from settings
        $school_logo = 'assets/img/default-logo.png'; // Default fallback
        $school_name = 'School';
        
        // Try to get actual settings if available
        try {
            if (method_exists($CI->settings, 'getSetting')) {
                $school_logo = $CI->settings->getSetting('school_logo') ?: $school_logo;
                $school_name = $CI->settings->getSetting('school_name') ?: $school_name;
            }
        } catch (Exception $e) {
            // Silently fail and use defaults
        }
        
        // Force session write and close
        if (function_exists('session_write_close')) {
            session_write_close();
        }
        
        // Start output
        echo '<script>';
        
        // Clear storage if requested
        if ($clear_storage) {
            echo '
            // Clear localStorage and sessionStorage
            if (typeof(Storage) !== "undefined") {
                localStorage.clear();
                sessionStorage.clear();
            }';
        }
        
        // Add the redirect with delay and countdown timer
        echo '
        // Set up countdown timer
        var redirectCountdown = ' . ($delay / 1000) . ';
        var countdownTimer = setInterval(function() {
            redirectCountdown -= 1;
            var timerElement = document.getElementById("redirect-countdown");
            if (timerElement) {
                timerElement.textContent = redirectCountdown;
            }
            if (redirectCountdown <= 0) {
                clearInterval(countdownTimer);
            }
        }, 1000);
        
        // Redirect after delay
        setTimeout(function() {
            window.location.href = "' . $url . '";
        }, ' . $delay . ');';
        
        echo '</script>';
        
        // Add fallback message with header, footer and countdown timer
        echo '<style>
            /* Full screen overlay */
            .js-redirect-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: white;
                z-index: 9999;
                display: flex;
                flex-direction: column;
            }
            
            /* Header styling */
            .js-redirect-header {
                background-color: #f8f9fa;
                border-bottom: 1px solid #e9ecef;
                padding: 15px 20px;
                text-align: left;
            }
            
            .js-redirect-header img {
                height: 40px;
                width: auto;
            }
            
            /* Main content area */
            .js-redirect-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                padding: 20px;
                text-align: center;
            }
            
            /* Typography */
            .js-redirect-content h3 {
                color: #333;
                font-size: 24px;
                margin-bottom: 20px;
            }
            
            .js-redirect-content p {
                color: #666;
                font-size: 16px;
                line-height: 1.5;
                margin-bottom: 0;
            }
            
            .js-redirect-content a {
                color: #007bff;
                text-decoration: none;
                font-weight: bold;
                display: inline-block;
                margin-top: 10px;
            }
            
            /* Timer styling */
            .js-redirect-timer {
                margin-top: 20px;
                font-size: 18px;
                color: #6c757d;
            }
            
            .js-redirect-timer span {
                font-weight: bold;
                color: #dc3545;
            }
            
            /* Footer styling */
            .js-redirect-footer {
                background-color: #f8f9fa;
                border-top: 1px solid #e9ecef;
                padding: 15px 20px;
                text-align: center;
                font-size: 14px;
                color: #6c757d;
            }
            
            /* Responsive adjustments */
            @media (max-width: 768px) {
                .js-redirect-content h3 {
                    font-size: 22px;
                }
                .js-redirect-header {
                    padding: 10px 15px;
                }
                .js-redirect-header img {
                    height: 35px;
                }
                .js-redirect-timer {
                    font-size: 16px;
                }
            }
            
            @media (max-width: 480px) {
                .js-redirect-content h3 {
                    font-size: 20px;
                }
                .js-redirect-content p {
                    font-size: 14px;
                }
                .js-redirect-header img {
                    height: 30px;
                }
                .js-redirect-timer {
                    font-size: 14px;
                }
                .js-redirect-footer {
                    padding: 10px 15px;
                    font-size: 12px;
                }
            }
        </style>
        
        <div class="js-redirect-overlay">
            <div class="js-redirect-header">
                <img src="' . base_url() . $school_logo . '" alt="Logo">
            </div>
            <div class="js-redirect-content">
                <h3>' . htmlspecialchars($message) . '</h3>
                <p>If you are not redirected automatically, <a href="' . $url . '">click here</a>.</p>
                <div class="js-redirect-timer">
                    Redirecting in <span id="redirect-countdown">' . ($delay / 1000) . '</span> seconds...
                </div>
            </div>
            <div class="js-redirect-footer">
                &copy; ' . date('Y') . ' ' . $school_name . ' - All rights reserved
            </div>
        </div>';
        
        // Exit if requested
        if ($exit) {
            exit;
        }
    }
}

if (!function_exists('js_logout')) {
    /**
     * Logout using JavaScript redirect
     * 
     * Specialized version of js_redirect for logout operations
     * 
     * @param string $message Optional message to display during logout
     * @param int $delay Delay in milliseconds before redirect
     * @return void
     */
    function js_logout($message = 'Logging out...', $delay = 5000) {
        $logout_url = site_url('auth/logout') . '?redirect=true';
        
        js_redirect($logout_url, $message, $delay, true, true);
    }
}










