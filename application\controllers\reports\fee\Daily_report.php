<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  12 March 2018
 *
 * Description: Model class for Class Master table
 *
 * Requirements: PHP5 or above
 *
 */

require_once APPPATH.'third_party/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;


class Daily_report extends CI_Controller {
	function __construct() {
		  parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      $this->load->model('report/fee_report_model');

	}  

  public function bank_amount($filterMode=''){

    $data['classes'] =  $this->fee_report_model->get_Allclasses();
    $data['medium'] = $this->settings->getSetting('medium');

    if ($filterMode == '1')  {
      $data['SelectedClass'] = '';
      $data['SelectedMedium'] = '';
      $data['SelectedFrom_date'] = '';
      $data['SelectedTo_date'] = '';
      $bankRemitted  = $this->fee_report_model->bankremidtedDate();
    }else{
      $classId = $this->input->post('class_name');
      if (empty($classId)) {
        $classId = 0; //0 signifies show all classes
      } else {
        if ($classId == 'All') {
          $classId = 0; //0 signifies show all classes
        }
      }

      $bankRemitted = $this->fee_report_model->serachbankremitted($classId);
      $medium = $this->input->post('medium');
      $from_date = $this->input->post('from_date');
      $to_date = $this->input->post('to_date');
      
      $data['SelectedClass'] = $classId;
      $data['SelectedMedium'] = $medium;
      $data['SelectedFrom_date'] = $from_date;
      $data['SelectedTo_date'] = $to_date;  
    }

    $data['result_Data'] = $bankRemitted;
    $data['main_content'] = 'reports/fees/bank_report';
    $this->load->view('inc/template', $data);
  }


  public function daily_report_sms(){

    $data['allDate'] = $this->fee_report_model->getAlldatesInFeeinstallment();
    $data['total_challanAmount'] = $this->fee_report_model->getChallanAmountforDaywise();
    $data['total_bankAmount'] = $this->fee_report_model->getBankAmountforDaywise();
    $data['cDate']= date('d-m-Y');
    $data['main_content'] = 'reports/fees/sms_daily_report';
    $this->load->view('inc/template', $data);
  }

  public function serachdaywiseBankandchallaAmount(){

    $data['allDate'] = $this->fee_report_model->getAlldatesInFeeinstallment();
    $date = $this->input->post('date');
    $data['cDate']=$date;
    $data['total_challanAmount'] = $this->fee_report_model->getChallanAmountforDatewise($date);
    $data['total_bankAmount'] = $this->fee_report_model->getBankAmountforDatewise($date);
    $data['main_content'] = 'reports/fees/sms_daily_report';
    $this->load->view('inc/template', $data);
  }

  public function history_report(){
     // if fee collected student data only display 
    $data['std_search'] = $this->fee_report_model->searchStudentby();
    $data['classes'] =  $this->fee_report_model->get_Allclasses();
    $data['main_content'] = 'reports/fees/history';
    $this->load->view('inc/template', $data);
  }

  public function searchClasswiseStudent(){
    $data['classes'] =  $this->fee_report_model->get_Allclasses();
    $class = $this->input->post('stud_class');
    // if fee collected student data only display 
    $data['std_search'] = $this->fee_report_model->searchStudentbyClasswise($class);
    $data['main_content'] = 'reports/fees/history';
    $this->load->view('inc/template', $data);
  }

  public function studentpaymenthistory($std_id,$traverse_to,$classId){
   
    $data['traverse_to'] = $traverse_to;
    $data['selectedClassId'] = $classId;
    $data['stdId'] = $std_id;
    $data['showFullPayment'] = ($this->settings->getSetting('school_short_name') == 'nhis');
    $data['payment_history'] = $this->fee_report_model->getpaymenthistorbystudentwise($std_id);
    $feeConfig = $this->settings->getSetting('fees');
    $data['receipt_number_gen'] = $feeConfig['recipt_number_gen'];

    if (empty($data['payment_history'])) {
        $this->session->set_flashdata('flashInfo', 'Fees not paid yet. History not available.');
        $feeConfig = $this->settings->getSetting('fees');
        $receipt_number_gen = $feeConfig['recipt_number_gen'];
        //get last id fee transcation installment generate receipt no
        switch ($receipt_number_gen['fee_generation_algo']) {
          case 'NHS':
            redirect('fees/fee_standard/index/'.$classId); 
            break;
          case 'concorde':
            redirect('fees/fee_concorde/index/'.$classId); 
            break;
          default:
            $receipt_number = '';
            break;
        }
    }
   //echo "<pre>"; print_r($data['payment_history']); die();
    $data['main_content'] = 'reports/fees/view_history';
    $this->load->view('inc/template', $data);
  }

  public function fullPaidReceipt($std_id,$traverse_to,$classId){
    $data['traverse_to'] = $traverse_to;
    $data['selectedClassId'] = $classId;
    $data['stdId'] = $std_id;
    $feeConfig = $this->settings->getSetting('fees');
    $rTemplate = $feeConfig['receipt_template'];
    $data['fee_details']= $this->fee_report_model->get_feedetailsbystdId($std_id);
    //echo "<pre>"; print_r($data['fee_details']); die();
    $data['main_content'] = 'receipts/nhs_full';
    $this->load->view('inc/template', $data);
  }

  public function duplicatefee_receipt($std_id, $receiptNo,$traverse_to,$classId){
   
    $data['traverse_to'] = $traverse_to;
    $data['selectedClassId'] = $classId;
    $data['stdId'] = $std_id;
    $data['selectedstdId'] = $std_id;
    $feeConfig = $this->settings->getSetting('fees');
    $data['paymentType'] = $feeConfig['allowed_payment_modes'];
    $rTemplate = $feeConfig['receipt_template'];
    $data['fee_details']= $this->fee_report_model->get_feedetailsbyfee_no($receiptNo);
    //echo "<pre>"; print_r($data['fee_details']); die();
    $data['main_content'] = 'receipts/'.$rTemplate;
    $this->load->view('inc/template', $data);
  }

  public function non_rec_report($filterMode){
    $data['classes'] =  $this->fee_report_model->get_Allclasses();
    $data['medium'] = $this->settings->getSetting('medium');

    if ($filterMode == '1')  {
      $data['SelectedClass'] = '';
      $data['SelectedMedium'] = '';
      $data['SelectedFrom_date'] = '';
      $data['SelectedTo_date'] = '';
      $challansNonRecDetails = $this->fee_report_model->non_recn_report();
    }else{
      $classId = $this->input->post('class_name');
      if (empty($classId)) {
        $classId = 0; //0 signifies show all classes
      } else {
        if ($classId == 'All') {
          $classId = 0; //0 signifies show all classes
        }
      }

      $challansNonRecDetails = $this->fee_report_model->non_recn_reportSerachClasswise($classId);

      $medium = $this->input->post('medium');
      $from_date = $this->input->post('from_date');
      $to_date = $this->input->post('to_date');

      $data['SelectedClass'] = $classId;
      $data['SelectedMedium'] = $medium;
      $data['SelectedFrom_date'] = $from_date;
      $data['SelectedTo_date'] = $to_date;
    }
    $feeConfig = $this->settings->getSetting('fees');
    $data['paymentType'] = $feeConfig['allowed_payment_modes'];
    $data['result_Data'] = $challansNonRecDetails;
    //echo "<pre>"; print_r($challansNonRecDetails); die();
    $data['main_content'] = 'reports/fees/non_reconciled';
    $this->load->view('inc/template', $data);

  }

  public function concession_report($filterMode){
      
    $data['classes'] =  $this->fee_report_model->get_Allclasses();
    $data['medium'] = $this->settings->getSetting('medium');

    if ($filterMode == '1')  {
      $data['SelectedClass'] = '';
      $data['SelectedMedium'] = '';
      $concession  = $this->fee_report_model->concessionReport();
      //echo "<pre>"; print_r($concession); die();
    }else{
      $classId = $this->input->post('class_name');
      if (empty($classId)) {
        $classId = 0; //0 signifies show all classes
      } else {
        if ($classId == 'All') {
          $classId = 0; //0 signifies show all classes
        }
      }
      $concession = $this->fee_report_model->serachconCessionReport($classId);
       // echo "<pre>"; print_r($concession); die(); 
      $medium = $this->input->post('medium');
      $data['SelectedClass'] = $classId;
      $data['SelectedMedium'] = $medium;
     
    }

    $data['result_Data'] = $concession;
    $data['main_content'] = 'reports/fees/concession_report';
    $this->load->view('inc/template', $data);

  }


  /* Prepare All the Views 
    *
    * @params $columns Array
    * @params $type String 
    *
    * Returns specify views
    */

    private function _prepareViewReport($columns, $type) {

        $requiredviews = [];

        $column_name = 'Tables_in_'.$this->db->database;

        foreach ($columns as $column) {

            if (strpos($column->$column_name, $type) !== false) {
                $tmpcolumns['real'] = $column->$column_name;
                $tmpcolumns['fancy'] = ucwords(str_replace('_', ' ', $column->$column_name));
                $requiredviews[] = $tmpcolumns;
            }
        }

        return $requiredviews;
    }


    private function _prepareColumnFeildNames($columns) {

      $requiredcolumns = [];

      foreach ($columns as $column) {
          $tmpcolumns['real'] = $column->Field;
          $tmpcolumns['fancy'] = ucwords(str_replace('_', ' ', $column->Field));
          $requiredcolumns[] = $tmpcolumns;
      }
      return $requiredcolumns;
    }

    public function getColumns() {

        $view_name = $this->input->post('view_name');

        $raw_columns = $this->fee_report_model->getColumns($view_name);
        foreach ($raw_columns as $key => $v) {
          if ($v->Field =='stdId') {
            unset($raw_columns[$key]);
            continue;
          }
          if ($v->Field =='classId') {
            unset($raw_columns[$key]);
            continue;
          }
          if ($v->Field =='rteid') {
            unset($raw_columns[$key]);
            continue;
          }
          if ($v->Field =='section') {
            unset($raw_columns[$key]);
            continue;
          }
          if ($v->Field =='admission_type') {
            unset($raw_columns[$key]);
            continue;
          }
        }
        $columns = $this->_prepareColumnFeildNames($raw_columns);
        echo json_encode($columns);
    }



    //Unused. Replaced with better student report
  //  public function studentReport() {        

    //     $reports = $this->fee_report_model->getAllViewReport();
    //     $data['student_reports'] = $this->_prepareViewReport($reports,'student');
    //     $data['rte'] = $this->settings->getSetting('rte');
    //     $data['admis_type'] = $this->settings->getSetting('admission_type');
    //     $data['classes'] = $this->fee_report_model->get_AllclassesDetails(); 
    //     $data['main_content']    = 'reports/fees/student_report';
    //     $this->load->view('inc/template', $data);
    // }

    public function getSection(){
      $classId = $this->input->post('classId');
      $result = $this->fee_report_model->get_sectionClassWise($classId);
      echo json_encode($result);
    }

    // public function viewdownloadReport() {

    //     $fields = $this->input->post('fields');
    //     $field_string = implode(',', $fields);
    //     $report_view = $this->input->post('report_view');

    //     $section = $this->input->post('section');
    //     $admidType = $this->input->post('admidType');
    //     //$rteid = $this->input->post('rteid');
    //     $classId = $this->input->post('class');

    //     if (empty($classId)) {
    //       $classId = 0; //0 signifies show all classes
    //     } else {
    //       if ($classId == 'All') {
    //         $classId = 0; //0 signifies show all classes
    //       }
    //     }

    //     $fancy_fields = [];
    //     $data['return_data'] = json_encode(['fields' => $fields, 'report_view' => $report_view]);
    //     $data['title'] = 'Exported Pdf';

    //     foreach ($fields as $value) {
    //         $fancy_fields[] = ucwords(str_replace('_', ' ', $value));
    //     }
    //     $data['fancy_fields'] = $fancy_fields;
    //     $data['exportData'] = $this->fee_report_model->viewStudentReport($field_string,$report_view,$classId,$section,$admidType);
    //     //echo "<pre>"; print_r($data['exportData']); die();
    //     $data['main_content']    = 'templates/exportView';
    //     $this->load->view('inc/template', $data);   


    // }
//Replaced with a better student report
    // public function downloadReport() {

    //   $type = $this->input->get('type');
    //   $returndata = json_decode($this->input->get('returndata'));

    //   $section = $this->input->post('section');
    //   $admidType = $this->input->post('admidType');
    //   //$rteid = $this->input->post('rteid');
    //   $classId = $this->input->post('class');

    //   if (empty($classId)) {
    //     $classId = 0; //0 signifies show all classes
    //   } else {
    //     if ($classId == 'All') {
    //       $classId = 0; //0 signifies show all classes
    //     }
    //   }

    //   $fields = $returndata->fields;
    //   $field_string = implode(',', $fields);
    //   $report_view = $returndata->report_view;
    //   $fancy_fields = [];

    //   $data['title'] = 'Exported Pdf';

    //   foreach ($fields as $value) {
    //       $fancy_fields[] = ucwords(str_replace('_', ' ', $value));
    //   }

    //   $data['fancy_fields'] = $fancy_fields;
    //   $data['exportData'] = $this->fee_report_model->viewStudentReport($field_string, $report_view,$classId,$section,$admidType);

    //   //$this->load->view('template_export/exportPdf', $data);
    //   $downloadStatus = true;

    //   $filename = 'studentReport_'.date('d-m-Y_hia').'.'.$type;
 
    //   switch ($type) {
    //     case 'pdf': {
    //         try {
    //           $this->load->library('pdf');
    //           $this->pdf->load_view('templates/pdf/export_student', $data);
    //           $this->pdf->render();     
    //           $this->pdf->stream($filename);
    //         } catch(Exception $e) {
    //             $downloadStatus = false;             
    //         }
    //         exit;
    //         break;
    //     }
    //     case 'xls':    { 
    //         // Create new Spreadsheet object
    //         $spreadsheet = new Spreadsheet();
    //         //echo "<pre>"; print_r($spreadsheet); die();
    //         // Set document properties
    //         $spreadsheet->getProperties()->setCreator('Maarten Balliauw')
    //         ->setLastModifiedBy('Maarten Balliauw')
    //         ->setTitle('Office 2007 XLSX Test Document')
    //         ->setSubject('Office 2007 XLSX Test Document')
    //         ->setDescription('Test document for Office 2007 XLSX, generated using PHP classes.')
    //         ->setKeywords('office 2007 openxml php')
    //         ->setCategory('Test result file');
    //         // Add some data
    //         $str = 'A';
    //         $col = 1;
    //         $cell_val = $str.$col;
    //         foreach ($fancy_fields as $key1 => $value1) {     
    //             $spreadsheet->setActiveSheetIndex(0)->setCellValue($cell_val, $value1);
    //             $cell_val = ++$str.$col;
    //         }


    //         $str = 'A';
    //         $col = 2;
    //         foreach ($data['exportData'] as $key2 => $value2) {  
    //         // Add some data    
    //             $cell_val = $str.$col;
    //             foreach ($value2 as $key3 => $value3) {
    //                 $spreadsheet->setActiveSheetIndex(0)->setCellValue($cell_val, $value3);
    //                 $cell_val = ++$str.$col;       
    //             }
    //             $str = 'A';
    //             $col += 1;   

    //         }

    //         // Rename worksheet
    //         $spreadsheet->getActiveSheet()->setTitle('Simple');

    //         // Set active sheet index to the first sheet, so Excel opens this as the first sheet
    //         $spreadsheet->setActiveSheetIndex(0);
    //         ob_end_clean();
    //         header('Content-Type: application/vnd.ms-excel'); //mime type
    //         header('Content-Disposition: attachment;filename="'.$filename.'"'); //tell browser what's the file name
    //         header('Cache-Control: max-age=0'); //no cache

    //         $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
    //         $writer->save('php://output');
    //         exit;
    //         break;
    //     }
    //   }
    // }

  public function softdelete_receipt(){
    $feeNumber = $this->input->post('feeNumber');
    $stdId = $this->input->post('stdId');
    $traverse_to = $this->input->post('traverse_to');
    $selectedClassId = $this->input->post('selectedClassId');
    $result = $this->fee_report_model->updateSoftdeleteByFeeNumber($feeNumber,$stdId);
    if ($result) {
      echo "1";
    }else{
      echo "0";
    }
  }

  public function non_rec_reportlistStudent(){
    $feeConfig = $this->settings->getSetting('fees');
    $data['paymentType'] = $feeConfig['allowed_payment_modes'];
    $data['non_rec_lists'] = $this->fee_report_model->non_recn_stdList();
    //echo "<pre>"; print_r($data['non_rec_lists']); die();
    $data['main_content'] = 'fees/concorde/non_reconciled';
    $this->load->view('inc/template', $data);

  }

}