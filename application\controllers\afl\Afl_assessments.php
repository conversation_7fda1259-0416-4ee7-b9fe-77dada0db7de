<?php
class Afl_assessments extends CI_Controller {
    private $yearId;
    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
       redirect('auth/login', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('afl/Afl_assessment_model','assessment_model');
      $this->load->model('afl/Afl_rubrics_model','rubric_model');
      $this->load->model('afl/Afl_grading_model','grading_model');
      $this->load->model('afl/Afl_subjects_model','subject_model');
      $this->load->model('Class_section', 'class_section');
    }

  public function index($class_id=0){
    $data['class_id'] = $class_id;
    $data['grades'] = $this->grading_model->getGradingScales();
    $data['is_admin'] = $this->authorization->isAuthorized('AFL.ADMIN');
    if($data['is_admin']) {
      $data['classess'] = $this->class_section->getAllClassess();
    } else {
      $data['classess'] = $this->assessment_model->getPermittedClasses();
    }
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'afl/assessments/index';
    $this->load->view('inc/template', $data);
  }

  public function getAssessments() {
    $class_id = $_POST['class_id'];
    $data = $this->assessment_model->getAssessmentsByClass($class_id);
    echo json_encode($data);
  }

  public function getAssessment() {
    $assessment_id = $_POST['assessment_id'];
    $data = $this->assessment_model->getAssessmentDetail($assessment_id);
    echo json_encode($data);
  }

  public function saveAssessment() {
    $status = $this->assessment_model->saveAssessment();
    echo $status;
  }

  public function assessmentDetails($assessment_id) {
    $data['assessment'] = $this->assessment_model->getAssessmentDetail($assessment_id);
    $data['class'] = $this->class_section->getClassDetail($data['assessment']->class_id);
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'afl/assessments/details';
    $this->load->view('inc/template', $data);
  }

  public function subjects($assessment_id) {
    $data['is_admin'] = $this->authorization->isAuthorized('AFL.ADMIN');
    $data['assessment'] = $this->assessment_model->getAssessmentDetail($assessment_id);
    $data['class'] = $this->class_section->getClassDetail($data['assessment']->class_id);
    $data['main_content'] = 'afl/assessments/subjects';
    $this->load->view('inc/template', $data);
  }

  public function getAssessmentSubjects() {
    $assessment_id = $_POST['assessment_id'];
    $is_admin = $this->authorization->isAuthorized('AFL.ADMIN');
    $data['subjects'] = $this->assessment_model->getSubjects($assessment_id, $is_admin, 0);
    $data['marks_status'] = $this->assessment_model->getMarksStatus($assessment_id, $is_admin);
    echo json_encode($data);
  }

  public function getClassSubjects() {
    $class_id = $_POST['class_id'];
    $assessment_id = $_POST['assessment_id'];
    $subjects = $this->assessment_model->getClassSubjects($class_id, $assessment_id);
    echo json_encode($subjects);
  }

  public function getSubjectTopics() {
    $subject_id = $_POST['subject_id'];
    $topics = $this->assessment_model->getSubjectTopics($subject_id);
    echo json_encode($topics);
  }

  public function saveSubject() {
    $input = $this->input->post();
    $status = $this->assessment_model->saveSubject();
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Subject Added Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_assessments/subjects/'.$input['assessment_id']);
    // echo "<pre>"; print_r($input); die();
  }

  public function removeSubject() {
    $assessment_subject_id = $_POST['id'];
    $status = $this->assessment_model->removeSubject($assessment_subject_id);
    echo $status;
  }

  public function changeTopics() {
    $input = $this->input->post();
    $status = $this->assessment_model->changeTopics();
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Updated Added Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_assessments/subjects/'.$input['assessment_id']);
    // echo "<pre>"; print_r($input); die();
  }

  public function assessment_rubrics($assessment_id) {
    $data['subjects'] = $this->assessment_model->getAssessmentSubjects($assessment_id);
    $data['assessment'] = $this->assessment_model->getAssessmentDetail($assessment_id);
    $data['class'] = $this->class_section->getClassDetail($data['assessment']->class_id);
    $data['main_content'] = 'afl/assessments/rubrics';
    $this->load->view('inc/template', $data);
  }

  public function getRubrics() {
    $assessment_subject_id = $_POST['assessment_subject_id'];
    $data['rubric'] = $this->assessment_model->getAssessmentRubric($assessment_subject_id);
    if(!empty($data['rubric'])) {
      $data['rubric_added'] = 1;
      $data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($data['rubric']->afl_grading_scale_id);
      $data['parameters'] = $this->rubric_model->getRubricParameters($data['rubric']->rubric_id);
    } else {
      $data['rubric_added'] = 0;
      $data['rubrics'] = $this->rubric_model->getRubrics();
    }
    // echo "<pre>"; print_r($data); die();
    echo json_encode($data);
  }

  public function getRubricParameters() {
    $rubric_id = $_POST['rubric_id'];
    $rubric = $this->rubric_model->getRubric($rubric_id);
    $data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($rubric->afl_grading_scale_id);
    $data['parameters'] = $this->rubric_model->getRubricParameters($rubric_id);
    echo json_encode($data);
  }

  public function addAssessmentSubjectRubric() {
    $input = $this->input->post();
    // echo "<pre>"; print_r($input); die();
    $status = $this->assessment_model->addAssessmentSubjectRubric();
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Rubric Added Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_assessments/assessment_rubrics/'.$input['assessment_id']);
  }

  public function removeRubric() {
    $rubric_id = $_POST['rubric_id'];
    $assessment_subject_id = $_POST['assessment_subject_id'];
    $status = $this->assessment_model->removeRubric($rubric_id, $assessment_subject_id);
    echo $status;
  }

  // if(!empty($data['rubric'])) {
  //     $data['rubric_added'] = 1;
  //     $data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($data['rubric']->afl_grading_scale_id);
  //     $data['parameters'] = $this->rubric_model->getRubricParameters($data['rubric']->rubric_id);
  //   } else {
  //     $data['rubric_added'] = 0;
  //     $data['rubrics'] = $this->rubric_model->getRubrics();
  //   }

  public function addSubjectRubric($assessment_subject_id=0, $assessment_id=0, $subject_id=0) {
    if($assessment_subject_id==0) {
      $data['assessment_subject_id'] = $_POST['assessment_subject_id'];
      $data['assessment_id'] = $_POST['assessment_id'];
      $data['subject_id'] = $_POST['subject_id'];
    } else {
      $data['assessment_subject_id'] = $assessment_subject_id;
      $data['assessment_id'] = $assessment_id;
      $data['subject_id'] = $subject_id;
    }
    $data['is_admin'] = $this->authorization->isAuthorized('AFL.ADMIN');
    $data['assessment_subject'] = $this->assessment_model->getAssessmentSubject($data['assessment_subject_id']);
    $data['subject_ids'] = $this->assessment_model->getSubjectIdsByName($data['subject_id']);
    $data['perf_data'] = $this->assessment_model->getSubjectPerfParameters($data['assessment_subject_id']);
    // echo "<pre>"; print_r($data['perf_data']); die();
    $data['prev_assessments'] = $this->assessment_model->getSubjectAssessments($data['subject_ids']);
    $this->load->model('afl/Afl_perf_pointer_model','pointer_model');
    $data['pointers'] = $this->pointer_model->getPointers();
    $data['assessment'] = $this->assessment_model->getAssessmentDetail($data['assessment_id']);
    $data['subject'] = $this->subject_model->getSubjectDetail($data['subject_id']);
    $data['class'] = $this->class_section->getClassDetail($data['assessment']->class_id);
    $data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($data['assessment']->afl_grading_scale_id);
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'afl/assessments/subject_rubrics';
    $this->load->view('inc/template', $data);
  }

  public function getRubricClone() {
    $assessment_id = $_POST['assessment_id'];
    $subject_id = $_POST['subject_id'];
    $assessment_subject_id = $this->assessment_model->getAssessmentSubjectId($assessment_id, $subject_id);
    $perf_data = $this->assessment_model->getSubjectPerfParameters($assessment_subject_id);
    echo json_encode($perf_data);
    // echo "<pre>"; print_r($perf_data); die();
  }

  public function cloneRubricData() {
    $input = $this->input->post();
    $assessment_id = $input['assessment_id'];
    $assessment_subject_id = $input['assessment_subject_id'];
    $subject_id = $input['subject_id'];
    $this->db->trans_start();
    foreach ($input['perf_pointer'] as $key => $pointer_id) {
      $grade_desc = $input['grade_description'][$key];
      $perf_desc = $input['perf_description'][$key];
      $this->assessment_model->saveRubricCloneData($pointer_id, $perf_desc, $grade_desc, $assessment_subject_id);
    }
    $this->db->trans_complete();
    if($this->db->trans_status() === FALSE) {
      $this->db->trans_rollback();
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    } else {
      $this->db->trans_commit();
      $this->session->set_flashdata('flashSuccess', 'Added Successfully.');
      redirect('afl/afl_assessments/addSubjectRubric/'.$assessment_subject_id.'/'.$assessment_id.'/'.$subject_id);
    }
  }

  public function removeParameter() {
    $parameter_id = $_POST['parameter_id'];
    echo $this->assessment_model->removeParameter($parameter_id);
  }

  public function changePerfVerificationStatus() {
    $verification_status = $_POST['verification_status'];
    $assessment_subject_id = $_POST['assessment_subject_id'];
    $status = $this->assessment_model->updatePerfParameterVerificationStatus($assessment_subject_id, $verification_status);
    echo $status;
  }

  public function saveSubjectPerfPointers() {
    $input = $this->input->post();
    $status = $this->assessment_model->savePerfPointers();
    $data = array(
      'assessment_subject_id' => $input['assessment_subject_id'],
      'assessment_id' => $input['assessment_id'],
      'subject_id' => $input['subject_id']
    );
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Added Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_assessments/addSubjectRubric/'.$input['assessment_subject_id'].'/'.$input['assessment_id'].'/'.$input['subject_id']);
    // echo "<pre>"; print_r($input); die();
  }

  public function updatePerfGradeDescription() {
    $input = $this->input->post();
    $status = $this->assessment_model->updateParameters();
    $data = array(
      'assessment_subject_id' => $input['assessment_subject_id'],
      'assessment_id' => $input['assessment_id'],
      'subject_id' => $input['subject_id']
    );
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Updated Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_assessments/addSubjectRubric/'.$input['assessment_subject_id'].'/'.$input['assessment_id'].'/'.$input['subject_id']);
    // $this->addSubjectRubric($data);
    // echo "<pre>"; print_r($input); die();
  }

  public function publishSubject() {
    $assessment_subject_id = $_POST['assessment_subject_id'];
    $status = $_POST['status'];
    echo $this->assessment_model->publishSubject($assessment_subject_id, $status);
  }

}