<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  16 March 2018
 *
 * Description: Controller for Timetable.
 *
 * Requirements: PHP5 or above
 *
 */

class Timetables extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
      redirect('dashboard', 'refresh');
    }  
    $this->load->model('timetable/timetable');
    $this->load->model('class_master_model');
    $this->load->model('timetable/staff_tt');
    $this->load->model('timetable/room_tt');
    $this->load->model('timetable/sss_allocation_model');
    $this->load->library('building_cache');
    $this->load->library('settings');
  }
  
  //Landing function to initialize the TT and Staff availability
  public function init_tt($classId, $csId) {
    $this->__createEmptyTT($classId, $csId);
    $this->__initStaffTT();
    $this->__initRoomTT();
    redirect('timetable/timetable_master');
  }

  //Landing function to show the timetable menu
  public function menu() {
      $site_url = site_url();
      $data['tiles'] = array();
      if($this->authorization->isAuthorized('TIMETABLE.MASTER')) {
        $data['tiles'] = array(
          [
            'title' => 'Subjects',
            'sub_title' => 'Add the subjects list that make the timetable',
            'icon' => 'svg_icons/subjects.svg',
            'url' => $site_url.'subjects',
            'permission' => 1
          ],
          [
            'title' => 'Staff Workload',
            'sub_title' => 'Add Staff/Section/Subject mappings',
            'icon' => 'svg_icons/staffworkload.svg',
            'url' => $site_url.'timetable/sss_allocation',
            'permission' => 1
          ],
          [
            'title' => 'Construct Timetable',
            'sub_title' => 'Allocate staff workload to periods',
            'icon' => 'svg_icons/constructtimetable.svg',
            'url' => $site_url.'timetable/timetable_master',
            'permission' => 1
          ]
        );
        $data['tiles'] = checkTilePermissions($data['tiles']);
    }

    $data['staff_tiles'] = array(
      [
        'title' => 'View Staff Timetables',
        'sub_title' => 'View daily/weekly timetable of each staff with substitutions',
        'icon' => 'svg_icons/view.svg',
        'url' => $site_url.'timetable/staff_timetable/displayAllStaffIndex',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.STAFF_TIMETABLE_VIEW')
      ],
      [
        'title' => 'Print Staff Timetables',
        'sub_title' => 'View/print multiple staff timetables',
        'icon' => 'svg_icons/printstafftimetable.svg',
        'url' => $site_url.'timetable/staff_timetable/printStaffTimetables',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.STAFF_TIMETABLE_VIEW')
      ],
      // [
      //   'title' => 'My Timetable',
      //   'sub_title' => 'View your timetable (logged-in user)',
      //   'icon' => 'svg_icons/printstafftimetable.svg',
      //   'url' => $site_url.'timetable/staff_timetable',
      //   'permission' => $this->authorization->isAuthorized('TIMETABLE.MY_TIMETABLE')
      // ],
      [
        'title' => 'My Timetable',
        'sub_title' => 'View your timetable',
        'icon' => 'svg_icons/mytimetable.svg',
        'url' => $site_url.'timetablev2/template/staff_timetable_v2',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.MY_TIMETABLE')
      ],
      [
        'title' => 'Staff workload report',
        'sub_title' => 'View Staff workload report',
        'icon' => 'svg_icons/staffworkloadreport.svg',
        'url' => $site_url.'timetable/staff_timetable/staffWorkLoadReport',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.MASTER')
      ]
    );
    $data['staff_tiles'] = checkTilePermissions($data['staff_tiles']);

    $data['section_tiles'] = array(
      [
        'title' => 'View Section Day Timetables',
        'sub_title' => 'View daily/weekly timetable of each staff with substitutions',
        'icon' => 'svg_icons/viewsectiondaytimetable.svg',
        'url' => $site_url.'timetable/section_timetable/displayDaySectionTimetables',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.SECTION_TIMETABLE_VIEW')
      ],
      [
        'title' => 'Print Section/ Staff Timetables',
        'sub_title' => 'View/Print Section and Section\'s staff timetables',
        'icon' => 'svg_icons/printstafftimetable.svg',
        'url' => $site_url.'timetable/section_timetable/printSectionTimetables',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.SECTION_TIMETABLE_VIEW')
      ]
    );
    $data['section_tiles'] = checkTilePermissions($data['section_tiles']);

    $data['room_tiles'] = array(
      [
        'title' => 'Room Timetables',
        'sub_title' => 'View/Print Room Timetables',
        'icon' => 'svg_icons/viewprintroomtimetable.svg',
        'url' => $site_url.'timetable/room_timetable/printRoomTimetables',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.VIEW_ROOM_TT')
      ]
    );
    $data['room_tiles'] = checkTilePermissions($data['room_tiles']);

    $data['admin_tiles'] = array();
    if ($this->authorization->isSuperAdmin()) {
      $data['admin_tiles'] = array(
        [
          'title' => 'Construct TT Template',
          'sub_title' => 'Create timetable template; Mass-assign to section, staff and room',
          'icon' => 'svg_icons/constructtimetable.svg',
          'url' => $site_url.'timetable/timetable_template',
          'permission' => 1
        ],
        [
          'title' => 'Assign/Repair Staff TT',
          'sub_title' => 'Assign timetable template to Staff / Repair Staff timetable',
          'icon' => 'svg_icons/repairroomtt.svg',
          'url' => $site_url.'timetable/staff_timetable/showStaffTemplateInits',
          'permission' => 1
        ],
        [
          'title' => 'Auto-check Staff Cache',
          'sub_title' => 'Auto-check and repair Staff Cache',
          'icon' => 'svg_icons/staff.svg',
          'url' => $site_url.'timetable/staff_timetable/autoCheckStaffCache',
          'permission' => 1
        ],
        [
          'title' => 'Assign Room TT',
          'sub_title' => 'Assign a timetable template to Rooms',
          'icon' => 'svg_icons/assignroomtt.svg',
          'url' => $site_url.'timetable/timetable_template/init_single_room',
          'permission' => 1
        ],
        [
          'title' => 'Repair Room TT',
          'sub_title' => 'Assign a timetable template to Rooms',
          'icon' => 'svg_icons/repairroomtt.svg',
          'url' => $site_url.'View/Repair Room timetable cache',
          'permission' => 1
        ]
      );
      $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);
    }

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'timetable/menu/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'timetable/menu/index_mobile';
    }else{
      $data['main_content'] = 'timetable/menu/index';  	
    }

    
    $this->load->view('inc/template', $data);
  }

  //Landing function for constructing TT for this section
  public function construct_tt($csId) {
    $data['csObj']=$this->timetable->getClassSectionDetailsById ($csId);
    $data['pHeadArr'] = $this->timetable->getPeriodHeader($csId);
    $data['sssArr'] = $this->timetable->getSSSData ($csId);
    $data['main_content']    = 'timetable/construct/construct_tt';
    $data['csAllList'] = $this->sss_allocation_model->getAllClassSectionList();

    $ctObj = $this->sss_allocation_model->getClassTeacherObj($csId);
    $data['classTeacherName'] = (empty($ctObj->staffName)?'Not Assigned':$ctObj->staffName);;

    //echo '<pre>';print_r($data['csObj']);die();
    //echo '<pre>';print_r($data['sssArr']);die();
    $this->load->view('inc/template', $data);
  }
  
  //Landing function for constructing TT across sections
  public function construct_tt_across_classes($classId) {
    $data['classObj'] = $this->class_master_model->getClassDetailsById($classId);
    $data['csList'] = $this->class_master_model->getSectionListById($classId);
    //echo '<pre>';print_r($data['csList']);die();
    $data['main_content'] = 'timetable/construct_tt_across_sections';
    $this->load->view('inc/template', $data);
  }

  public function getTTForClass() {
    $classId = $_POST['classId'];
    $csId = $_POST['csId'];

    $data['numPeriods'] = $this->timetable->getNumPeriods($classId);
    $data['tta'] = $this->timetable->getTT($csId);
    echo json_encode($data);
  }

  public function getTTForClassSection() {

    $csId = $_POST['csId'];

    $data['tta'] = $this->timetable->getTT($csId);
    $data['is_admin'] = $this->authorization->isSuperAdmin();
    echo json_encode($data);
  }

  public function getTTForClassSectionWithHeader() {

    $csId = $_POST['csId'];

    $data['csName'] = $this->timetable->getSectionName($csId);
    $data['pHeadArr'] = $this->timetable->getPeriodHeader($csId);
    $data['tta'] = $this->timetable->getTT($csId);

    echo json_encode($data);
  }

  public function getDayTTForClassSectionWithHeader() {
    $date = date('Y/m/d');
    $weekDay = date("w", strtotime($date));

    $csId = $_POST['csId'];

    $data['csName'] = $this->timetable->getSectionName($csId);
    $data['pHeadArr'] = $this->timetable->getPeriodHeader($csId);
    $data['tta'] = $this->timetable->getDayTT($csId, $weekDay);

    echo json_encode($data);
  }

  public function getSSS () {
    if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
    $csId = $_POST['csId'];

    $data['sss_data'] = $this->timetable->getSSSData ($csId);
    $data['is_admin'] = $this->authorization->isSuperAdmin();

    echo json_encode($data);
  }

  public function allocS1Period () {
    $sssId = $_POST['sssId'];
    $periodId = $_POST['periodId'];
    $csName = $_POST['csName'];
    $deepCheck = $_POST['deepCheck'];

    $result = $this->timetable->allocS1Period($sssId, $periodId, $csName, $deepCheck);

    echo json_encode($result);
  }
  
  public function allocS2Period () {
    $sssId = $_POST['sssId'];
    $csName = $_POST['csName'];
    $periodIdPair = $_POST['periodIdPair']; //'56|65'
    $periodIdArr = explode('|', $periodIdPair);

    $result = $this->timetable->allocS2Period($sssId, $periodIdArr[0], $periodIdArr[1], $csName);

    echo json_encode($result);
  }

  public function allocSpecialCases () {
    $special_case = $_POST['special_case'];
    $special_period = $_POST['special_period'];
    $csId = $_POST['csId'];
    $csName = $_POST['csName'];

    $result = 0;

    switch ($special_case) {
      case 0: //Class teacher
        $result = $this->timetable->allocClassTeacher($csId, $special_period, true, $csName);
        break;
      case 1: //Non-class teacher
        $result = $this->timetable->allocClassTeacher($csId, $special_period, false, $csName);
        break;
      case 2: //Activity
        $result = $this->timetable->allocActivity($csId, $special_period, $csName);
        break;
      case 3: //Dummy period
      $result = $this->timetable->allocDummyPeriods($csId, $special_period, $csName);
      break;
  }
    echo $result;
  }

  public function allocDummyPeriodsToSaturdays () {
    $csId = $_POST['csId'];
    $csName = $_POST['csName'];
    $result = $this->timetable->allocDummyPeriodsToSaturdays($csId,$csName);
    return $result;
  }

  public function allocAllPeriods () {
    $csId = $_POST['csId'];
    $csName = $_POST['csName'];
    $result = $this->timetable->allocRemainingPeriods($csId,$csName);
    return $result;
  }

  public function swapPeriods () {
    $pLeftId = $_POST['pLeftId'];
    $pRightId = $_POST['pRightId'];
    $csName = $_POST['csName'];
    $deepCheck = $_POST['deepCheck'];

    $result = $this->timetable->swapPeriods($pLeftId,$pRightId,$csName, $deepCheck);
    // echo '<pre>';print_r($result);die();
    echo json_encode($result);
  }

  public function deallocPeriod() {
    $taId = $_POST['taId'];
    $csName = $_POST['csName'];
    $result = $this->timetable->deAllocPeriod($taId,$csName);
    echo json_encode($result);
  }

  public function deallocS2Period() {
    $deAllocIdString = $_POST['deAllocIdString'];
    $csName = $_POST['csName'];
    $result = $this->timetable->deAllocS2Period($deAllocIdString,$csName);
    echo json_encode($result);
  }

  public function allocS1ToDiffPeriod() {
    $originttaId = $_POST['originperiodId'];
    $originsssId = $_POST['originsssId'];
    $targetttaId = $_POST['targetperiodId'];
    $csName = $_POST['csName'];
    $deepCheck = $_POST['deepCheck'];
    
    //De-allocate the period
    $returnVal = $this->timetable->deAllocPeriod($originttaId,$csName);
    // echo '<pre>';print_r($returnVal);
    if ($returnVal->result != '1') {
      //Deallocate failed. Return error.
      echo json_encode($returnVal);
    }
    else {
      $returnVal2 = $this->timetable->allocS1Period($originsssId, $targetttaId, $csName, $deepCheck);
      // echo '<pre>';print_r($returnVal2);die();
      //echo 'here' . $result2;die();

      if ($returnVal2->result <= 0) {
        //Allocate to new ttaid failed. Hence allocated to old ttaid and return -1.
        $returnVal3 = $this->timetable->allocS1Period($originsssId, $originttaId, $csName, 0);
        if ($returnVal3->result != '1') {
          $returnVal4 = $this->timetable->__setReturnValue('-1','','','','Something went wrong. Contact your administrator');
          return $returnVal4;
        } else {
          //If successful, return the error we got when allocating the period.
          echo json_encode($returnVal2);
        }
      }
      else {
        echo 1;
      }
    }
  }

  public function resetTT () {
    $csId = $_POST['csId'];
    $origcsName = $_POST['csName'];
    $result = $this->timetable->resetTT($csId,$origcsName);
    echo $result;
  }

  public function getPeriodsByDay () {
    $day = $_POST['day'];
    $csId = $_POST['csId'];

    $periodData = $this->timetable->getPeriodsByDay($day, $csId);

    echo json_encode($periodData);
  }

  public function getS2PeriodsAvailability () {
    $csId = $_POST['csId'];

    //Get all span2 periods
    $periodS2Arr = $this->timetable->getSpan2PeriodAvailability($csId, '0');

    echo json_encode($periodS2Arr);
  }

  public function getS2AllocatedPeriods () {
    $csId = $_POST['csId'];

    //Get all span2 periods
    $periodS2Arr = $this->timetable->getS2AllocatedPeriods($csId);

    echo json_encode($periodS2Arr);
  }

  public function getAllocatedPeriods () {
    $csId = $_POST['csId'];

    //Get all span2 periods
    $allocatedPeriods = $this->timetable->getAllocatedPeriods($csId);

    echo json_encode($allocatedPeriods);
  }

  public function getAdditionalStaff() {
    $ttaId = $_POST['ttaId'];

    //Get all span2 periods
    $staff = $this->timetable->getAddionalStaff($ttaId);

    echo json_encode($staff);    
  }

  public function getStaffTT() {
    $csId = $_POST['csId'];

    echo json_encode($this->staff_tt->getStaffTT($csId));
  }

  public function getRoomTT() {
    echo json_encode($this->room_tt->getRoomTT());
  }

  public function getClassTeacher(){
    $csId = $_POST['csId'];
    $ctId = $this->timetable->getClassTeacher($csId);
    echo json_encode($ctId);
  }
  
  public function getScholasticStaff(){
    $csIds = $_POST['csIds'];
    $unStaffList = $this->timetable->getStaffList($csIds, 1);
    $result = $this->timetable->getOrderedStaffList($unStaffList);

    $staffList = array();
    foreach ($result as $t) {
      $staffList[] = $t->id;
    }

    //echo '<pre>';print_r($staffList);die();
    echo json_encode($staffList);
  }

  public function getNonScholasticStaff(){
    $csIds = $_POST['csIds'];
    $schStaffList = $this->timetable->getStaffList($csIds, 1);
    $nschStaffList = $this->timetable->getStaffList($csIds, 0);

    //Remove scholastic staff from non-scholastic staff
    foreach ($nschStaffList as $k => $n) {
      $found = 0;
      foreach ($schStaffList as $s) {
        if ($n == $s) {
          $found = 1;
          break;
        }
      }
      if ($found == 1) {
        unset($nschStaffList[$k]);
      }
    }

    $result = $this->timetable->getOrderedStaffList($nschStaffList);

    $staffList = array();
    foreach ($result as $t) {
      $staffList[] = $t->id;
    }

    echo json_encode($staffList);
  }
}