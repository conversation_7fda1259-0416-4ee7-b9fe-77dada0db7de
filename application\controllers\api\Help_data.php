<?php

  defined('BASEPATH') OR exit('No direct script access allowed');

  class Help_data extends CI_Controller {
    public function __construct() {
        parent::__construct();
        $this->acad_year->loadAcadYearDataToSession();
    }

    public function getHelpData() {
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $module = $data['module'];
      $helpData = array();
      if($module !== '') {
        $this->load->model('Helpdesk_model', 'helpdesk');
        $helpData = $this->helpdesk->getChatContent($module);
      }
      // return $helpData;
      echo json_encode($helpData);
    }

  }