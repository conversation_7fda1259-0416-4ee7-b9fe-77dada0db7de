<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON>
 *          <EMAIL>
 *
 * Created:  04 August 2022
 *
 * Description: Controller for Managing Donors.
 *
 * Requirements: PHP5 or above
 *
 */

class Create_donation extends CI_Controller
{
  function __construct()
  {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STAFF_TASKS_BASKET')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('donation/donation_model');
    $this->CI = &get_instance();
  }

  //Landing function to show non-compliance menu
  public function index()
  {
    $data['main_content']    = 'donation/create_donation/index_desktop.php';
    $this->load->view('inc/template', $data);
  }

 
  public function get_donors_data()
  {
    $data['donor_data'] = $this->donation_model->get_donor_data();
    echo json_encode($data);
  }

  
}
