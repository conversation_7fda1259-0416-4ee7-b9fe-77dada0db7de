<?php

/**
 * Description of Student_menu
 *
 * <AUTHOR>
 */
class Student_menu extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_MASTER')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT.MODULE')) {
      redirect('dashboard', 'refresh');
    }
  }

  function index() {
    /*$data['permitStudentModule'] = $this->authorization->isAuthorized('STUDENT.NEW_STUDENT_ADD');
    $data['permitStudentObservation'] = $this->authorization->isAuthorized('STUDENT_OBSERVATION.MODULE');
    $data['permitStudentLeave'] = $this->authorization->isAuthorized('STUDENT_LEAVE.MODULE');
    $data['permitStudentMassUpdate'] = $this->authorization->isAuthorized('STUDENT_MASSUPDATE.MODULE');
    $data['permit_student_report_view'] = $this->authorization->isAuthorized('STUDENT.VIEW_REPORT');
    $data['permit_observation_report_view'] = $this->authorization->isModuleEnabled("STUDENT_OBSERVATION") &&$this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW_ALL');
    $data['permitPromotion'] = $this->authorization->isAuthorized('STUDENT_PROMOTION.MODULE');
    $data['permitStudentAdd'] = $this->authorization->isAuthorized('STUDENT.NEW_STUDENT_ADD');
    $data['permitStudentView'] = $this->authorization->isAuthorized('STUDENT.BASIC_VIEW');
    $data['permit_leave_approval'] = $this->authorization->isAuthorized('STUDENT_LEAVE.APPROVE');
    $data['permit_add_certificates'] = $this->authorization->isAuthorized('STUDENT.ADD_CERTIFICATE');
    $data['permit_class_wise_report_view'] = $this->authorization->isAuthorized('STUDENT.VIEW_CLASS_REPORT');
    $data['permit_mass_assign_certificates'] = $this->authorization->isAuthorized('STUDENT.CERTIFICATE_GENERATE');
    $data['permit_student_admission_wise'] = $this->authorization->isAuthorized('STUDENT.VIEW_TOTAL_CLASS_WISE_STUDENT_REPORT');

    $data['permit_studnet_alumni_list'] = $this->authorization->isAuthorized('STUDENT.VIEW_ALUMNI_LIST');*/
    $currentAcadyearId =  $this->settings->getSetting('academic_year_id');
    $data['permitAddStudent'] = 0;
    if ($currentAcadyearId == $this->acad_year->getAcadYearId()) {
       $data['permitAddStudent'] = 1;
    }
    $data['studentCount'] = $this->db->select('count(sa.id) as stdCount')
    ->from('student_admission sa')
    ->join('student_year sy', "sy.student_admission_id=sa.id")
    ->where('sa.admission_status', 2)
    ->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
    ->where("sy.promotion_status!='JOINED'")
    ->where("sy.promotion_status!='4'")
    ->where("sy.promotion_status!='5'")
    ->get()->row()->stdCount;

    $site_url = site_url();
      $data['tiles'] = array(
          [
            'title' => 'Add Student',
            'sub_title' => 'Add a new student',
            'icon' =>'svg_icons/add.svg',
            'url' => $site_url.'student/Student_controller/add_student',
            'permission' => $this->authorization->isAuthorized('STUDENT.NEW_STUDENT_ADD') && $data['permitAddStudent']
          ],
          [
            'title' => 'View Students',
            'sub_title' => 'Perform operations on individual students',
            'icon' => 'svg_icons/view.svg',
            'url' => $site_url.'student/Student_controller',
            'permission' => $this->authorization->isAuthorized('STUDENT.BASIC_VIEW')
          ],
          // [
          //   'title' => 'Observations',
          //   'sub_title' => 'Manage observations of individual students',
          //   'icon' => 'svg_icons/maleprofile.svg',
          //   'url' => $site_url.'student_observation/observation/my_observation_mobile',
          //   'permission' => $this->authorization->isAuthorized('STUDENT_OBSERVATION.MODULE')
          // ],
          [
            'title' => 'Student Data Mass Update',
            'sub_title' => 'Perform operations for students',
            'icon' => 'svg_icons/student_mass_update.svg',
            'url' => $site_url.'student/student_massupdate',
            'permission' => $this->authorization->isAuthorized('STUDENT_MASSUPDATE.MODULE')
          ],
          [
            'title' => 'Student/Parent Address Mass Update',
            'sub_title' => 'Address Mass Update',
            'icon' => 'svg_icons/student_mass_update.svg',
            'url' => $site_url.'student/student_massupdate/address_mass_update',
            'permission' => $this->authorization->isAuthorized('STUDENT_MASSUPDATE.STUDENT_ADDRESS_MASSUPDATE')
          ],
          [
            'title' => 'Mass update Running Height and weight',
            'sub_title' => 'Perform operations for students',
            'icon' => 'svg_icons/massupdate.svg',
            'url' => $site_url.'student/student_massupdate/mass_update_height_weight',
            'permission' => $this->authorization->isAuthorized('STUDENT_MASSUPDATE.MODULE')
          ],
          [
            'title' => 'Mass Assign Section',
            'sub_title' => 'Assign section to students',
            'icon' => 'svg_icons/assignroomtt.svg',
            'url' => $site_url.'student/student_section',
            'permission' => $this->authorization->isAuthorized('STUDENT_MASSUPDATE.MODULE')
          ],
          [
            'title' => 'Mass Promote Students',
            'sub_title' => 'Promote Students',
            'icon' => 'svg_icons/rewards.svg',
            'url' => $site_url.'student/Student_promotion',
            'permission' => $this->authorization->isAuthorized('STUDENT_PROMOTION.MODULE')
          ],
          // [
          //   'title' => 'Manage Certificate Templates',
          //   'sub_title' => 'Add/Edit Certificates Templates',
          //   'icon' => 'svg_icons/certificate.svg',
          //   'url' => $site_url.'student/Certificates_controller/certificates',
          //   'permission' => $this->authorization->isAuthorized('STUDENT.ADD_CERTIFICATE')
          // ],
          // [
          //   'title' => 'Promote Students (Semester Wise)',
          //   'sub_title' => 'Promote Students',
          //   'icon' => 'svg_icons/rewards.svg',
          //   'url' => $site_url.'student/Student_promotion/semester_wise_promotion',
          //   'permission' => $this->authorization->isAuthorized('STUDENT_PROMOTION.MODULE')
          // ],
          [
            'title' => 'Mass Map RFID',
            'sub_title' => 'Mapping of students RFID to their ID card',
            'icon' => 'svg_icons/maprfid.svg',
            'url' => $site_url.'student/Student_controller/map_rfid_to_student',
            'permission' => $this->authorization->isAuthorized('STUDENT_PROMOTION.MODULE')
          ],  
          [
            'title' => 'Mass Documents And Photo Upload',
            'sub_title' => 'Mapping of parents photos',
            'icon' => 'svg_icons/mass_document_photo_upload.svg',
            'url' => $site_url.'student/Student_controller/mass_parent_photo_update',
            'permission' => $this->authorization->isAuthorized('STUDENT.DOCUMENTS_MASS_UPLOAD')
          ],
          [
            'title' => 'Student Exit Flow',
            'sub_title' => 'Mapping of parents photos',
            'icon' => 'svg_icons/student_exit_flow.svg',
            'url' => $site_url.'student/Student_controller/student_exitflow',
            'permission' => $this->authorization->isAuthorized('STUDENT_EXIT_FLOW.MODULE')
          ]
      );
      $data['tiles'] = checkTilePermissions($data['tiles']);

      $data['report_tiles'] = array(
          [
            'title' => 'Student Report',
            'sub_title' => 'View/Print Student Report',
            'icon' => 'svg_icons/student_report.svg',
            'url' => $site_url.'reports/student/student_report/reportIndex',
            'permission' => $this->authorization->isAuthorized('STUDENT.VIEW_REPORT')
          ],
          [
            'title' => 'Student Admission Report',
            'sub_title' => 'View Total Student count and Fee Report',
            'icon' => 'svg_icons/student_admission_report.svg',
            'url' => $site_url.'reports/student/student_report/student_count_admission_wise',
            'permission' => $this->authorization->isAuthorized('STUDENT.VIEW_TOTAL_CLASS_WISE_STUDENT_REPORT')
          ],
          [
            'title' => 'Student Previous School Report',
            'sub_title' => 'View student previous schools',
            'icon' => 'svg_icons/student_previous_school_report.svg',
            'url' => $site_url.'reports/student/student_report/student_prev_school_report',
            'permission' => $this->authorization->isAuthorized('STUDENT.STUDENT_PREVIOUS_SCHOOL_REPORT')
          ],
          [
            'title' => 'Class Section Report',
            'sub_title' => 'View/print # of student in class wise',
            'icon' => 'svg_icons/class_report.svg',
            'url' => $site_url.'reports/student/student_report/class_wise_report',
            'permission' => $this->authorization->isAuthorized('STUDENT.VIEW_CLASS_REPORT')
          ],
          [
            'title' => 'Class Health Report',
            'sub_title' => 'View Class wise Health Report',
            'icon' => 'svg_icons/class_health_report.svg',
            'url' => $site_url.'student/Health_controller/health_class_report',
            'permission' => $this->authorization->isAuthorized('STUDENT.VIEW_ALUMNI_LIST')  // To Do
          ],
          [
            'title' => 'Category-wise Student count',
            'sub_title' => 'Category-wise Student count',
            'icon' => 'svg_icons/category_wise_student_count.svg',
            'url' => $site_url.'reports/student/student_report/student_category_wise_count',
            'permission' => $this->authorization->isAuthorized('STUDENT.CATEGORY_WISE_STUDENT_REPORT')
          ],
          [
            'title' => 'Student Document Report',
            'sub_title' => 'Class and section wise document of students',
            'icon' => 'svg_icons/student_document_report.svg',
            'url' => $site_url.'reports/student/Student_docs_controller/student_wise_student_document_report',
            'permission' => $this->authorization->isAuthorized('STUDENT.STUDENT_DOCUMENT_REPORT')
          ],
          // [
          //   'title' => 'Vaccination Status',
          //   'sub_title' => 'Vaccination Status Verified',
          //   'icon' => 'svg_icons/reportcardnew.svg',
          //   'url' => $site_url.'student/Student_controller/vaccination_status',
          //   'permission' => $this->authorization->isAuthorized('STUDENT.VIEW_VACCINATION_STATUS')
          // ],
          [
            'title' => 'Aadhar Card Report',
            'sub_title' => 'Aadhar Card Report',
            'icon' => 'svg_icons/aadhar_card_report.svg',
            'url' => $site_url.'reports/student/Student_docs_controller/student_wise_aadhar_report',
            'permission' => $this->authorization->isAuthorized('STUDENT.STUDENT_AADHAR_REPORT')
          ],
          [
            'title' => 'PAN Card Report',
            'sub_title' => 'PAN Card Report',
            'icon' => 'svg_icons/pan_card_report.svg',
            'url' => $site_url.'reports/student/Student_docs_controller/student_wise_pancard_report',
            'permission' => $this->authorization->isAuthorized('STUDENT.STUDENT_PAN_REPORT')
          ],
          [
            'title' => 'Alumni Student List',
            'sub_title' => 'View Total Student Alumni',
            'icon' => 'svg_icons/alumni_student_list.svg',
            'url' => $site_url.'reports/student/student_report/student_alumni_list',
            'permission' => $this->authorization->isSuperAdmin()
          ],
          [
            'title' => 'Student Transport Report',
            'sub_title' => 'View  Student Transport Report',
            'icon' => 'svg_icons/student_transport_report.svg',
            'url' => $site_url.'reports/student/student_report/transport_reportIndex',
            'permission' => $this->authorization->isAuthorized('STUDENT.VIEW_TRANSPORT_REPORT')
          ],
          [
            'title' => 'Student Audit Log',
            'sub_title' => 'Student Audit Log report',
            'icon' => 'svg_icons/student_audit_log.svg',
            'url' => $site_url.'reports/student/student_report/student_audit_log',
            'permission' => $this->authorization->isSuperAdmin()
          ],
          [
            'title' => 'Student Data Missing Report',
            'sub_title' => 'Data Missing Report report',
            'icon' => 'svg_icons/student_data_missing_report.svg',
            'url' => $site_url.'reports/student/student_report/student_data_mission_report',
            'permission' => $this->authorization->isAuthorized('STUDENT.STUDENT_DATA_MISSING_REPORT')
          ],
          [
            'title' => 'Student Edit History Report',
            'sub_title' => 'Student Edit History Report',
            'icon' => 'svg_icons/student_edit_history_report.svg',
            'url' => $site_url.'reports/student/student_report/student_edit_history_report',
            'permission' => $this->authorization->isAuthorized('STUDENT.STUDENT_EDIT_HISTORY_REPORT')
          ], 
          [
            'title' => 'Single Window Report',
            'sub_title' => 'Student Window Report',
            'icon' => 'svg_icons/single_window_report.svg',
            'url' => $site_url.'reports/student/student_report/single_window_report',
            'permission' => $this->authorization->isAuthorized('STUDENT.SINGLE_WINDOW_REPORT')
          ], 
          [
            'title' => 'Sibling Report',
            'sub_title' => 'Sibling Report',
            'icon' => 'svg_icons/siblings_report.svg',
            'url' => $site_url.'reports/student/student_report/sibling_report',
            'permission' => $this->authorization->isAuthorized('STUDENT.SIBLING_REPORT')
          ] 
      );
      $data['report_tiles'] = checkTilePermissions($data['report_tiles']);
      $data['admin_tiles'] = array(
          [
            'title' => 'Configure Student Fields',
            'sub_title' => 'Configure Student Fields',
            'icon' => 'svg_icons/config_student_fields.svg',
            'url' => $site_url.'reports/student/Student_docs_controller/student_profile_display_fiedls',
            // 'permission' => $this->authorization->isAuthorized('STUDENT.STUDENT_PROFILE_DISPLAY_FIELDS')
            'permission' => $this->authorization->isSuperAdmin()
          ],
          [
            'title' => 'Configure Parent Edits',
            'sub_title' => 'Configure Parent Edits',
            'icon' => 'svg_icons/config_parent_edit_fields.svg',
            'url' => $site_url.'reports/student/Student_docs_controller/student_profile_edit_fiedls',
            // 'permission' => $this->authorization->isAuthorized('STUDENT.STUDENT_PROFILE_DISPLAY_FIELDS')
            'permission' => $this->authorization->isSuperAdmin()
          ],
          [
            'title' => 'Configure Required Fields',
            'sub_title' => 'Configure Required Fields',
            'icon' => 'svg_icons/config_required_fields.svg',
            'url' => $site_url . 'reports/student/Student_docs_controller/student_profile_display_required_fiedls',
            // 'permission' => $this->authorization->isAuthorized('STUDENT.STUDENT_PROFILE_DISPLAY_FIELDS')
            'permission' => $this->authorization->isSuperAdmin(),
          ],
          [
            'title' => 'Configure Health fields',
            'sub_title' => 'Configure Health fields',
            'icon' => 'svg_icons/config_health_fields.svg',
            'url' => $site_url.'staff/Staff_controller/manage_student_health_fields',
            'permission' => $this->authorization->isSuperAdmin()
          ],
          [
            'title' => 'Manage Student Document Types',
            'sub_title' => 'Manage Student Document Types',
            'icon' => 'svg_icons/student_document_types.svg',
            'url' => $site_url.'reports/student/Student_docs_controller/student_document_types',
            'permission' => $this->authorization->isSuperAdmin()
          ],
          [
            'title' => 'Configure Parent-side Fields',
            'sub_title' => 'Configure Parent-side Fields',
            'icon' => 'svg_icons/config_parent_side_fields.svg',
            'url' => $site_url.'reports/student/Student_docs_controller/configure_parent_fields',
            'permission' => $this->authorization->isAuthorized('STUDENT.CONFIGURE_PARENT_SIDE_FIELDS')
          ],
          [
            'title' => 'Lock/Unlock Profile Confirmation',
            'sub_title' => 'Manage Student Profile Edit',
            'icon' => 'svg_icons/lock_unlock_profile.svg',
            'url' => $site_url.'reports/student/Student_docs_controller/manage_profile_confirm',
            'permission' => $this->authorization->isAuthorized('STUDENT.LOCK_UNLOCK_PROFILE')
          ]    
      );
      $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);
      
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/menu/tablet_index.php';
    }else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/menu/mobile_index.php';
    }else {
      $data['main_content'] = 'student/menu/index.php';
    }
    $this->load->view('inc/template', $data);
  }
}