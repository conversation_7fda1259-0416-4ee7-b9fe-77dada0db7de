<?php

defined('BASEPATH') OR exit('No direct script access allowed');
class user_payment extends CI_Controller {
    public $is_hookable = TRUE;
	public function __construct() {
        parent::__construct();
        $this->load->model('user_payment_model');
  	}
    

    public function payment_link($student_id='') {
        
        $fee_data = $this->user_payment_model->get_fee_data_from_link($student_id);
        
        if(!empty($fee_data)){
            $fee_amount = $this->_split_without_enter_amount_componentwise($fee_data);

            $input = array();
            $input['payment_type'] = '10_0';
            $input['transaction_mode'] = 'ONLINE';
            $input['student_id'] = $student_id;
            $input['receipt_date'] = date('d-m-Y');
            $totalPaybleAmount= 0;
            $allocate_enter_amount = 0;
            $split_arr = [];
            foreach ($fee_amount as $key => $val) {
                $totalPaybleAmount += $val->allocate_enter_amount;
                if(!array_key_exists($val->blueprint_component_id, $split_arr)){
                    $split_arr[$val->blueprint_component_id] = 0;
                }
                $split_arr[$val->blueprint_component_id] += $val->allocate_enter_amount;
                $input['fine_amount'][$val->feev2_installments_id][$val->blueprint_component_id] = '0';
                $input['comp_id'] = [$val->blueprint_component_id];
                $input['fsicompId'][$val->feev2_installments_id][$val->blueprint_component_id] = $val->fsicompId;
                $input['fsInsId'][$val->feev2_installments_id][$val->blueprint_component_id] = $val->fsInsId;
                $input['conc_amount'][$val->feev2_installments_id][$val->blueprint_component_id] = $val->concession_amount;
                $input['adjustment_amount'][$val->feev2_installments_id][$val->blueprint_component_id] = $val->adjustment_amount;
                $input['pay_amount'][$val->feev2_installments_id][$val->blueprint_component_id] = $val->allocate_enter_amount;
                $input['split_amount'][$val->blueprint_component_id] = $split_arr[$val->blueprint_component_id];
                $input['fee_student_schedule_id'] = $val->fee_student_schedule_id;
                $input['cohort_student_id'] = $val->cohort_student_id;
            }
            $input['total_amount'] = $totalPaybleAmount;
            $this->userdata = $input;
            $this->hooks->call_hook('custom_post_controller');
        }else{
            $this->userdata = 'Failed';
            $this->hooks->call_hook('failed_post_controller');
        }
       
       
    }

    private function _split_without_enter_amount_componentwise($fee_amount)
	{
		foreach ($fee_amount as $insId => &$val) {
            $val->allocate_enter_amount = $val->component_amount - $val->component_amount_paid -  $val->concession_amount - $val->concession_amount_paid -  $val->adjustment_amount - $val->adjustment_amount_paid;
		}
		return $fee_amount;
	}

    public function success_user_payment(){
        $this->success_data = $_POST;
        $this->hooks->call_hook('success_post_controller');
    }





}