<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Template extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('timetablev2/template_model','template_model');
  }

  //Landing function to show the timetable menu
  public function index() {
    $data['templates'] = $this->template_model->get_template_list();
    $data['main_content']    = 'timetablev2/template/manage_templates.php';
    // echo '<pre>';print_r($data);die();
    $this->load->view('inc_v2/template', $data);
  }

  public function get_template_detail() {
    $data['template_detail'] = $this->template_model->get_template_by_id($_POST['template_id']);

    echo json_encode($data);
  }

  public function add_or_update() {
    $result = $this->template_model->add_or_update_template($_POST);

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
      $data['template'] = $this->template_model->get_template_by_id($result);
    }

    echo json_encode($data);
  }

  public function add_template_periods() {
    $result = $this->template_model->add_template_periods($_POST);
    
    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }
    
    echo json_encode($data);
  }
  
  public function get_section_template_status_list() {
    $data['sections'] = $this->template_model->get_section_template_status_list($_POST['template_id']);
    
    echo json_encode($data);
  }

  public function get_staff_template_status_list() {
    $data['staff'] = $this->template_model->get_staff_template_status_list($_POST['template_id']);
    
    echo json_encode($data);
  }
  
  public function get_raw_tt_template_periods() {
    // echo '<pre>';print_r($_POST);die();
    $data['tt_periods'] = $this->template_model->get_raw_template_periods($_POST['ttt_id']);
    $data['tt_metadata'] = $this->template_model->get_template_by_id($_POST['ttt_id']);

    echo json_encode($data);
  }

  public function get_section_template_periods() {
    // echo '<pre>';print_r($_POST);die();
    $data['section_tt_periods'] = $this->template_model->get_section_template_periods($_POST['ttt_id'], $_POST['section_id']);
    $data['tt_metadata'] = $this->template_model->get_template_by_id($_POST['ttt_id']);
    $data['is_admin'] = $this->authorization->isSuperAdmin();
    $result = $this->template_model->get_section_class_teacher($_POST['section_id']);
    // echo '<pre>';print_r($result);die();
    $data['class_teacher_id'] = $result->class_teacher_id;
    $data['class_teacher_name'] = $result->class_teacher_name;
    $data['ass_class_teacher_id'] = $result->ass_class_teacher_id;
    $data['ass_class_teacher_name'] = $result->ass_class_teacher_name;
    
    echo json_encode($data);
  }

  public function check_delete_section_template_periods() {
    //echo '<pre>';print_r($_POST);die();
    $data = $this->template_model->check_delete_section_template_periods($_POST['template_id'], $_POST['section_id']);

    echo json_encode($data);
  }
  public function delete_section_template_periods() {
    //echo '<pre>';print_r($_POST);die();
    $data = $this->template_model->delete_section_template_periods($_POST['template_id'], $_POST['section_id']);

    echo json_encode($data);
  }

  public function check_delete_staff_template_periods() {
    //echo '<pre>';print_r($_POST);die();
    $data = $this->template_model->check_delete_staff_template_periods($_POST['template_id'], $_POST['staff_id']);

    echo json_encode($data);
  }

  public function delete_staff_template_periods() {
    //echo '<pre>';print_r($_POST);die();
    $data = $this->template_model->delete_staff_template_periods($_POST['template_id'], $_POST['staff_id']);

    echo json_encode($data);
  }

  public function get_staff_template_periods() {
    // echo '<pre>';print_r($_POST);die();
    $data = $this->template_model->get_staff_template_periods($_POST['ttt_id'], $_POST['staff_id']);
    $data['tt_metadata'] = $this->template_model->get_template_by_id($_POST['ttt_id']);
    $data['is_admin'] = $this->authorization->isSuperAdmin();

    echo json_encode($data);
  }
    
  public function add_section_template() {
    $result = $this->template_model->add_section_template($_POST);
    
    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }
    
    echo json_encode($data);
  }

  public function add_staff_template() {
    // echo '<pre>';print_r($_POST);die();
    $result = $this->template_model->add_staff_template($_POST);
    
    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }
    
    echo json_encode($data);
  }

  public function clone_staff_template() {
    // echo '<pre>';print_r($_POST);die();
    $result = $this->template_model->clone_staff_template($_POST['template_id'], $_POST['base_staff_id'], $_POST['clone_staff_ids']);
    
    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }
    
    echo json_encode($data);
  }
  
  public function clone_section_template() {
    // echo '<pre>';print_r($_POST);die();
    $result = $this->template_model->clone_section_template($_POST['template_id'], $_POST['base_section_id'], $_POST['clone_section_ids']);
    
    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }
    
    echo json_encode($data);
  }

  public function get_room_template_status_list() {
    $data['room'] = $this->template_model->get_room_template_status_list($_POST['template_id']);
    
    echo json_encode($data);
  }

  public function get_room_template_periods() {
    // echo '<pre>';print_r($_POST);die();
    $data = $this->template_model->get_room_template_periods($_POST['ttt_id'], $_POST['room_id']);
    $data['tt_metadata'] = $this->template_model->get_template_by_id($_POST['ttt_id']);

    echo json_encode($data);
  }

  public function add_room_template() {
    // echo '<pre>';print_r($_POST);die();
    $result = $this->template_model->add_room_template($_POST);
    
    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }
    
    echo json_encode($data);
  }

  public function clone_room_template() {
    // echo '<pre>';print_r($_POST);die();
    $result = $this->template_model->clone_room_template($_POST['template_id'], $_POST['base_room_id'], $_POST['clone_room_ids']);
    
    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }
    
    echo json_encode($data);
  }

  public function staff_timetable_v2(){
    if (!$this->authorization->isAuthorized('TIMETABLE.MY_TIMETABLE')) {
      redirect('dashboard', 'refresh');
    }
    
		$templates = $this->template_model->get_template_list('1');
    if(count($templates)>0){
      $data['templates'] = $templates;
      $data['is_template_active'] = 1;
    }
    else{
      $data['is_template_active'] = 0;
    }
    
		$staff_id = $this->authorization->getAvatarStakeHolderId();
    $data['staff_obj'] = $this->template_model->get_staff_oc_data($staff_id);
    $data['timetable_attendance_link'] = $this->settings->getSetting('timetable_attendance_link');
    switch ($data['timetable_attendance_link']) {
      case 'subject':
        $attendance_url = site_url('attendance_v2/Attendance/take_attendance/');
        break;
      case 'none':
        $attendance_url = "";
        break;
      case 'day':
        $attendance_url = site_url('attendance/Attendance_controller/markAttendance/');
        break;
      default:
        $attendance_url = "";
        break;
    }
    $data['attendance_url'] = $attendance_url;
    //echo '<pre>';print_r($data['templates']);die();
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']   = 'timetablev2/staff_timetable_v2/index_tablet';
    }else if ($this->mobile_detect->isMobile()) {
      $data['main_content']    = 'timetablev2/staff_timetable_v2/index_mobile';
    } else {
      $data['main_content'] = 'timetablev2/staff_timetable_v2/index';
    }
    // echo '<pre>';print_r($data);die();
    $this->load->view('inc/template', $data);
	}

	public function get_staff_timtable_v2(){

		$staff_id = $this->authorization->getAvatarStakeHolderId();
		$template_id = $_POST['template_id'];
		$data = $this->template_model->get_staff_template_periods_subjects($template_id, $staff_id);
    $data['tt_metadata'] = $this->template_model->get_template_by_id($template_id);
    // echo '<pre>';print_r($data);die();
    echo json_encode($data);
	}

  public function update_classlink() {
    $result = $this->template_model->update_classlink($_POST);
    
    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }
    
    echo json_encode($data);
 }

}