<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  12 Feb 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_Master
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Fees_collection extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isAuthorized('FEESV2.COLLECT_FEES')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('feesv2/fees_collection_model');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_cohorts_model');
    $this->load->model('avatar');
    $this->load->library('Payment_settlement');
    $this->load->library('fee_library');
    $this->load->library('filemanager');
    $this->load->model('parent_model');
    $this->config->load('form_elements');
    $this->load->helper('fees_helper');
  }

  /**
   * Landing function for Fee Collection
   */
  public function index () {
    $data['selectedClassId'] = $this->input->post('classId');
    if (empty($classId)){
      $classId = $this->input->post('classId');
    }
    if (empty($classId)){
      $classId = 0;
    }
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['puc_combination'] = $this->settings->getSetting('puc_combination');
    $data['is_semester_scheme'] = $this->settings->getSetting('is_semester_scheme');
    $data['fee_collection_v1'] = $this->settings->getSetting('fee_collection_v1');
    $data['admissionStatusArr'] = $this->settings->getSetting('admission_status');
    $data['adSelected'] = ['1','2'];
    $data['main_content'] = 'feesv2/transaction/index';
    $this->load->view('inc/template', $data);
  }

  /**
   * Function to show all the Fee blueprints associated with the student. For every blueprint, assign the cohort if not already assigned to the student. 
   * 
   * Step 1: Get all the blueprints
   * Step 2: Get the Fee details for each blueprint.
   * Step 3: When getting the fee details, if cohort is not assigned, assign the cohort and get the details. If cohort doesn't exist, then display 'Cohort not defined'.
   * Step 4: If blueprint fee collection has not started, goto confirmation page. If started, goto collection page.
   * Step 5: Goto confirmation page only if enable_cohort_confirmation is true.
   */
  public function fee_student_blueprints($std_id) {
    //Get all blue prints.
    //Get the fee details for each blue print
    $data['std_id'] = $std_id;
    $fee_blueprints = $this->fees_student_model->get_blueprints($std_id);
    foreach ($fee_blueprints as &$fbp) {
      //Assign cohorts if not assigned yet
      $data['student'] = $this->fees_student_model->get_std_detailsbyId($std_id, $fbp->acad_year_id);
      $fbp->reconciliation = '';
      $fbp->concession_adjustment = '';
      $fbp->is_cohort_exists = '';
      if (!empty($data['student'])) {
          $fbp->is_cohort_exists = $this->fees_student_model->determine_cohort($fbp->id, $data['student']);
          $fbp->std_fee_details = $this->fees_student_model->get_std_fee_cohort($std_id, $fbp->id);        
          $fbp->reconciliation = $this->fees_collection_model->check_reconcilation_status($std_id, $fbp->id);
          $fbp->concession_adjustment = $this->fees_student_model->get_concession_adjustment_amount($std_id, $fbp->id);
          $fbp->payment_modes = json_decode($fbp->allowed_payment_modes);
        }
    }
    $data['previous_balance'] =  $this->fees_student_model->check_previous_balance_amount($std_id);
    $data['admission_form_remarks'] = $this->Student_Model->get_admission_form_remarksbyStudentId($std_id);
    $data['eqnuiry_form_remarks'] = $this->Student_Model->get_enquiry_form_remarksbyStudentId($std_id);
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['disabledCurrentFees'] = $this->settings->getSetting('disable_current_fees_if_previous_balance_is_present');
    $data['fee_blueprints'] = $fee_blueprints;
    $data['main_content'] = 'feesv2/transaction/fees_index';
    $this->load->view('inc/template', $data);
  }

  /**
   * Function to show the confirmation screen to confirm the fee structure for the student
   * 
   * Steps:
   * Step 1: Get the Student details
   * Step 2: Get the Blueprint details (with components, amounts and installment types)
   * Step 3: Allow user to select the installment type.
   * Step 4: Allow fee customization.
   * Step 5: After clicking on confirm, change fee_collect_Status to 'COHORT_CONFIRMED'. Also, insert the relevant tables.
   * 
   */

  public function confirm_cohort_fee(){  
    $input = $this->input->post();
    $data['blueprint_id'] = $input['blueprint_id'];
    $data['student_id'] = $input['student_id'];
    $data['cohort_id'] = $input['cohort_id'];
    $data['blue_print'] = $this->fees_collection_model->ge_blueprint_details_by_id($input['blueprint_id']);

    $data['student'] = $this->fees_student_model->get_std_detailsbyId($input['student_id'],$data['blue_print']->acad_year_id);
    $conc_obj = $this->fee_library->compute_concession($data['student'], $data['blue_print']->concession_algo);
    $data['concession_alog'] = $conc_obj;

    $filter_columns = json_decode($data['blue_print']->display_fields);
    $data['filters'] = [];
    if (!empty($filter_columns)) {
      $data['filters'] = $this->_student_filter_columns($filter_columns, $data['student']);
    }
    $data['cohort_details'] = $this->fees_collection_model->get_all_cohorts_filter($input['blueprint_id']);

    $installments_types = $this->fees_collection_model->get_installment_types($data['blueprint_id']);
    $default_ins = $this->fees_collection_model->get_default_ins_id($input['cohort_id']);
    if (!empty($installments_types)) {
      foreach ($installments_types as $key => $val) {
        if (!empty($default_ins)) {
          if ($default_ins->default_ins != 0) {
            if ($default_ins->default_ins == $val->typeId) {
              $blueprint_installment_types_id = $val->feev2_blueprint_installment_types_id;
              $selectedId = $val->feev2_blueprint_installment_types_id;
            }
          }else{
            $blueprint_installment_types_id = $val->feev2_blueprint_installment_types_id;
            $selectedId = $val->feev2_blueprint_installment_types_id;
          }
        }else{
          $blueprint_installment_types_id = $val->feev2_blueprint_installment_types_id;
          $selectedId = $val->feev2_blueprint_installment_types_id;
        }
      }
      $fee_amount = $this->fees_collection_model->fee_cohort_component_structure($input['cohort_id'], $blueprint_installment_types_id);
      $data['installments_types'] = $installments_types;
      $data['fee_component'] = $fee_amount;
      $data['selected_default'] = $blueprint_installment_types_id;
    }

    
    $data['main_content'] = 'feesv2/transaction/collection/confirm_fee_cohort';
    $this->load->view('inc/template', $data);  
  }

   public function get_all_installments_blueprint_wise(){
    $blueprint_id = $_POST['blueprint_id'];
    $result = $this->fees_student_model->get_installmenttypebyblueprintId($blueprint_id);
    echo json_encode($result);
  }

  /**
   * Confirm page change  installment type get data and construct fee cohort structure
   */
  public function get_installment_type_wisedata(){
    $instypes = $_POST['instypes'];
    $select = $_POST['select'];
    if ($select === 'CUSTOM') {
      $result = $this->fees_collection_model->fee_cohort_component_structure_custom($instypes);
    }else{
      $result = $this->fees_collection_model->fee_cohort_component_structure($select, $instypes); 
    }
    echo json_encode($result);
  }
  
  /**
   * When change the student details update cohort id to student cohort table redirect to confirm chorot fee
  */

  public function update_fee_student_cohort_data($std_id, $blueprint_id)
  {
    $data['student'] = $this->fees_student_model->get_std_detailsbyId($std_id);
    $cohort_id =  $this->fees_student_model->change_std_data_update_cohorts($std_id, $blueprint_id, $data['student']);
    $this->session->set_userdata('student_id', $std_id);
    $this->session->set_userdata('cohort_student_id',$cohort_id);
    if ($cohort_id) {
      $this->session->set_flashdata('flashSuccess', 'Student fee data change successfully');
      redirect('feesv2/fees_collection/confirm_cohort_fee');
    }else{
      $this->session->set_flashdata('flashError', 'Student fee data not updated. Contact administrator');
      redirect('feesv2/fees_collection');
    }
  }


private function _construct_fee_components($fee_component, $blueprint_installment_type_id){

  $comp_amount = array();
  $data = array();
   foreach ($fee_component as $ins_name => $component) {
    foreach ($component as $key => $comp) {
      $comp_amount[$comp->feev2_installment_id][$comp->feev2_blueprint_component_id] = $comp->compAmount;
      $concession_amount[$comp->feev2_installment_id][$comp->feev2_blueprint_component_id] = 0;
    }
   }
  $data['cohort_status'] = 'STANDARD';
  $data['blueprint_installment_type_id'] = $blueprint_installment_type_id;
  $data['comp_amount'] = $comp_amount;
  $data['concession_amount'] = $concession_amount;
  $data['concession_name'] = '';
  return $data;
}

/**
 * Function called after confirming the student cohort
 * 
 * Steps:
 * 1. If request from confirmation page, then insert the student fee structure. If request from student blueprint page, the confirmation gets skipped; get the cohort details and do the relevant inserts.
 * 2. After inserts, redirect to fee collection.
 *  
 */
public function cohor_data_insert_fee_table()
{
  $input = $this->input->post();

  if (empty($input['comp_amount'])) {

    $installments_types = $this->fees_collection_model->get_installment_types($input['blueprint_id']);
    $default_ins = $this->fees_collection_model->get_default_ins_id($input['cohort_id']);
    if (!empty($installments_types)) {
      foreach ($installments_types as $key => $val) {
        if (!empty($default_ins)) {
          if ($default_ins->default_ins != 0) {
            if ($default_ins->default_ins == $val->typeId) {
              $blueprint_installment_types_id = $val->feev2_blueprint_installment_types_id;
              $selectedId = $val->feev2_blueprint_installment_types_id;
            }
          }
        }
        $blueprint_installment_types_id = $val->feev2_blueprint_installment_types_id;
        $selectedId = $val->feev2_blueprint_installment_types_id;
      }
      $fee_amount = $this->fees_collection_model->fee_cohort_component_structure($input['cohort_id'], $blueprint_installment_types_id);
    }
    $input = $this->_construct_fee_components($fee_amount, $blueprint_installment_types_id);    
  }


  $input['cohort_id'] = $this->input->post('cohort_id');
  $input['student_id'] = $this->input->post('student_id');
  $input['blueprint_id'] = $this->input->post('blueprint_id');
    
  $rdata = $this->fees_student_model->insert_cohort_details($input['blueprint_id'], $input['cohort_id'], $input['cohort_status'], $input['blueprint_installment_type_id'], $input['comp_amount'], $input['concession_amount'], $input['student_id'], $input['concession_name'], $input['fine_amount']);
  $this->session->set_userdata('student_id', $input['student_id']);
  $this->session->set_userdata('cohort_student_id',$rdata['cohort_student_id']);
  $this->session->set_userdata('std_sch_id',$rdata['std_sch_id']);
  $this->session->set_userdata('blueprint_id',$input['blueprint_id']);

 
  if ($rdata) {
     redirect('feesv2/fees_collection/fee_collect');
  }else{
    $this->session->set_flashdata('flashError', 'Something went wrong.');
    redirect('feesv2/fees_collection/fee_student_blueprints/'.$input['student_id']);
  }
}

public function fee_collect(){
  $input = $this->input->post();
  if (empty($input)) {
    $input['student_id'] = $this->session->userdata('student_id');
    $input['cohort_student_id'] = $this->session->userdata('cohort_student_id');
    $input['std_sch_id'] = $this->session->userdata('std_sch_id');
    $input['blueprint_id'] = $this->session->userdata('blueprint_id');
    $this->session->unset_userdata('student_id');
    $this->session->unset_userdata('cohort_student_id');
    $this->session->unset_userdata('std_sch_id');
    $this->session->unset_userdata('blueprint_id');
  }

  if (empty($input['student_id'])) {
    redirect('feesv2/fees_collection');
  }
  $data['student_id'] = $input['student_id'];
  $data['std_sch_id'] = $input['std_sch_id'];
  $data['cohort_student_id'] = $input['cohort_student_id'];
  $data['blueprint_id'] = $input['blueprint_id'];
  //Get the student details and the fee filtercolumns
  $data['blue_print'] = $this->fees_collection_model->ge_blueprint_details_by_id($input['blueprint_id']);
  
  $data['std_cohort_status'] = $this->fees_collection_model->get_std_fee_cohort_status($input['cohort_student_id']);
 
  //Get the payment modes and sort them
  $data['payment_modes'] = json_decode($data['blue_print']->allowed_payment_modes);
  // usort($data['payment_modes'], function($a, $b) { return strcasecmp($a->name, $b->name); });

  $data['student'] = $this->fees_student_model->get_std_detailsbyId($input['student_id'],$data['blue_print']->acad_year_id);
  $filter_columns = json_decode($data['blue_print']->display_fields);
  $data['filters'] = $this->_student_filter_columns($filter_columns, $data['student']);
  $data['total_fee_amount'] = $this->fees_collection_model->get_std_total_fee_amount($input['std_sch_id']);
  //Get the blueprint, balance installment and fee details
  $installmentsType = $this->fees_collection_model->get_fee_installments_all($input['std_sch_id']);
  foreach ($installmentsType as $key => $types) {
    if ($types->status !='FULL') {
      $fee_amount = $this->fees_collection_model->get_std_fee_amount($input['std_sch_id'], $types->fiInsId);
      break;
    }
  }
  if (empty($fee_amount)) {
    redirect('feesv2/fees_collection/fee_student_blueprints/'.$data['student_id']);
  }
  $data['noOfComponents'] = count($fee_amount);

  $data['no_of_installments'] = count($installmentsType);
  // echo "<pre>"; print_r($data['no_of_installments']); die();
  
  $data['selectedIns'] = $fee_amount[0]->fsInsId;
 
  $data['fee_amount'] = $fee_amount;

  $data['installments'] = $installmentsType;
  $data['card_charge_amount'] = json_encode($this->settings->getSetting('fee_payment_card_charge_amount'));
  $data['selection_installment'] = $this->settings->getSetting('fee_installment_selection');
  $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
  //TODO: discount, fine amount, etc.
  $data['blueprint_alog'] = $this->fees_collection_model->get_fee_blueprint_alog($input['std_sch_id'], $data['student']->class);
  $data['main_content'] = 'feesv2/transaction/collection/fee_collect';
  $this->load->view('inc/template', $data);
}

  

  /**
   * Function to submit the collected fee (fee transaction)
   */
  public function submit_fee(){  
    $input = $this->input->post();

    $this->db->trans_begin();

    $fTrans = $this->fees_collection_model->insert_fee_transcation($input);
    if (empty($fTrans)) 
    {
      $this->db->trans_rollback();
    }
    else
    {

      // generate and update receipt number after transcation
      $this->fees_collection_model->update_receipt_transcation_wise($fTrans);
      // Check Reconcilication Status if 1 pending... not allowed updated student schedule table
      $rconStatus = $this->fees_collection_model->lastFeeTranscationReconCheck($fTrans);
      if ($rconStatus->reconciliation_status == 1) {
        $this->db->trans_commit();
        $this->session->set_flashdata('flashInfo', 'Fee collected Successfully. Reconcilication pending');
        redirect('feesv2/fees_collection/fee_reciept/'.$fTrans);
      }

      $result =  $this->fees_collection_model->update_student_schedule_all_table($fTrans);
      if (empty($result)) 
      {
        $this->db->trans_rollback();
      }
      if ($this->db->trans_status()) 
      {
        $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($input['blueprint_id']);

        $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);

        if ($blue_print->enable_sms == TRUE) {
          $this->__sms_fees_payment_counter($data['fee_trans']->amount_paid, $data['fee_trans']->student->stdName, $data['fee_trans']->student->stdId, $blue_print->name);  
        }
        $this->db->trans_commit();
        $this->session->set_flashdata('flashSuccess', 'Fee collected Successfully');
        redirect('feesv2/fees_collection/fee_reciept/'.$fTrans);
      }
      else
      {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
    } 
  }

  
  private function __sms_fees_payment_counter($amount_paid, $student_name, $student_admission_id, $blueprint_name){

    $stake_holder_id = [$student_admission_id];
    /*$sent_by = $this->authorization->getAvatarId();
    $ph_type = 'Both';
    $sent_to_str = 'Student Individual';
    $source = 'Fee';
    $sh_type = 'Student';*/
    $fee_payment_sms = $this->settings->getSetting('fee_payment_sms');
    if(!empty($fee_payment_sms)){
			if ($fee_payment_sms->sms_enabled == TRUE) {
        $message = $fee_payment_sms->message;
        $message = str_replace('%%student_name%%', $student_name, $message);
        $message = str_replace('%%amount%%', $amount_paid, $message);
			}
    }else{
      $message = $student_name . " Fee payment of Rs. ".$amount_paid." towards ".$blueprint_name." collected successfully. \n - Cashier, ". ucwords($this->settings->getSetting('school_short_name')).' -NXTSMS';
    }

    $input_arr = array();
    $this->load->helper('texting_helper');
    $input_arr['student_ids'] = $stake_holder_id;
    $input_arr['mode'] = 'sms';
    $input_arr['source'] = 'Fee Payment';
    $input_arr['send_to'] = 'Both';
    $input_arr['message'] = $message;
    return $response = sendText($input_arr);
    // return sendCommonSMS($stake_holder_id, $sh_type, $source, $message, $sent_by, $sent_to_str, $ph_type);
  }

  private function _student_filter_columns($filter_columns, $student_data)
  { 
    $stdFilterColumns = array();
    if (empty($filter_columns)) {
      return $stdFilterColumns;
    }
    foreach ($filter_columns as $key => $val) {
      $temp = new stdClass();
      switch ($val) {
        case 'academic_year_of_joining':
        if (!empty($student_data->{$val})) {
          $stdFilterColumns[$val] = $this->acad_year->getAcadYearById($student_data->{$val});
        }
        break;
        case 'donor':
        if (!empty($student_data->{$val})) {
          $stdFilterColumns[$val] = $student_data->{$val};
        }
        break;

        case 'father_name':
        if (!empty($student_data->{$val})) {
          $stdFilterColumns[$val] = $student_data->{$val};
        }
        break;

        case 'mother_name':
        if (!empty($student_data->{$val})) {
          $stdFilterColumns[$val] = $student_data->{$val};
        }
        break;

        case 'father_phone':
        if (!empty($student_data->{$val})) {
          $stdFilterColumns[$val] = $student_data->{$val};
        }
        break;

        case 'mother_phone':
        if (!empty($student_data->{$val})) {
          $stdFilterColumns[$val] = $student_data->{$val};
        }
        break;

        case 'father_email':
        if (!empty($student_data->{$val})) {
          $stdFilterColumns[$val] = $student_data->{$val};
        }
        break;

        case 'mother_email':
        if (!empty($student_data->{$val})) {
          $stdFilterColumns[$val] = $student_data->{$val};
        }
        break;

        case 'has_transport_km':
        if (!empty($student_data->{$val})) {
          $stdFilterColumns[$val] = $this->fee_library->__get_trans_km_value($student_data->std_year_id);
        }
        break;

        case 'stop':
        if (!empty($student_data->{$val})) {
          $stdFilterColumns[$val] = $this->fee_library->__get_trans_stop_value($student_data->std_year_id);
        }
        break;
          
        case 'quota':
        $stdFilterColumns[$val] = $this->fee_library->__get_quota_types_value($val, $student_data->{$val});
        break;

        case 'combination':
        $stdFilterColumns[$val] = strtoupper($student_data->{$val});
        break;

        case 'class_type':
        $type = 'classType';
        $stdFilterColumns[$val] = $this->fee_library->__get_class_types_value($type, $student_data->{$val});
        break;
        
        case 'category':
        $stdFilterColumns[$val] = $this->fee_library->__get_cateogry_value($val, $student_data->{$val});
        break;
        case 'is_rte':
        $type = 'rte';
        $stdFilterColumns[$val] = $this->fee_library->__get_cateogry_value($type, $student_data->{$val});
        break;
        case 'medium':
        case 'admission_type':
        case 'board':
        case 'boarding':
          $temp_settings = $this->settings->getSetting($val);
          if (!empty($temp_settings[$student_data->{$val}]))
            $stdFilterColumns[$val] = $temp_settings[$student_data->{$val}];  
          break;    
        case 'has_staff':
          if ($student_data->{$val} === '1')
            $stdFilterColumns['has_staff'] = 'Staff`s kid';
          else
            $stdFilterColumns['has_staff'] = 'Not a Staff`s kid';
          break;
        case 'has_sibling':
          if ($student_data->{$val} === '11')
            $stdFilterColumns['has_sibling'] = 'Doesn`t have a sibling';
          else
            $stdFilterColumns['has_sibling'] = 'Has a sibling';
          break;
        case 'has_transport':
          if ($student_data->{$val} === '1')
            $stdFilterColumns['has_transport'] = 'Yes';
          else
            $stdFilterColumns['has_transport'] = 'No';
          break;
        case 'is_lifetime_student':
         if ($student_data->{$val} === '1')
            $stdFilterColumns['is_lifetime_student'] = 'Yes';
          else
            $stdFilterColumns['is_lifetime_student'] = 'No';
        break;

      }
    }
    return $stdFilterColumns;
  }

 
  public function concession($student_id, $cohort_student_id, $std_sch_id){

    $data['student_id'] = $student_id;
    $data['cohort_student_id'] = $cohort_student_id;
    $data['std_sch_id'] = $std_sch_id;

    $data['blue_print'] = $this->fees_collection_model->ge_blueprint_by_id($cohort_student_id);
    $data['student'] = $this->fees_student_model->get_std_detailsbyId($student_id,$data['blue_print']->acad_year_id);

    $data['blueprint_id'] = $data['blue_print']->id;
    $filter_columns = json_decode($data['blue_print']->display_fields);
    $data['filters'] = $this->_student_filter_columns($filter_columns, $data['student']);

    $data['concession_amount'] = $this->fees_collection_model->fee_previous_concession_amount($std_sch_id);
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['addition_concession_amount'] = $this->settings->getSetting('addition_concession_amount');
    $data['addConAmount'] = 0;
    if ($data['addition_concession_amount']) {
      $data['addConAmount'] = $this->fees_collection_model->get_addition_concession_amount($data['blue_print']->id, $student_id);
    }
    $data['max_comp_count'] = max($data['concession_amount']);
    $data['main_content'] = 'feesv2/transaction/collection/concession';
    $this->load->view('inc/template', $data); 
  }

  public function concession_update(){
    $input = $this->input->post();
    if (isset($input['addition_concession_amount'])) {
      $this->fees_student_model->update_concession_amount_addition($input['addition_concession_amount'], $input['blueprint_id'],$input['student_id']);
    }

    $result = $this->fees_student_model->update_concession_amount($input['std_sch_id'],$input['concession_amount'],$input['cohort_student_id'],$input['feev2_blueprint_installment_types_id'],$input['concession_name'],$input['concession_amount_add']);
    $total_con = 0;
    foreach ($input['concession_amount'] as $key => $val) {
      $total_con += array_sum($val);
    }
    $auditDesc = 'Concession amount '.$total_con;
    $this->fees_student_model->insert_fee_audit($input['student_id'], 'Concession', $auditDesc, $this->authorization->getAvatarId(), $input['blueprint_id']);
    $this->session->set_userdata('student_id', $input['student_id']);
    $this->session->set_userdata('cohort_student_id',$input['cohort_student_id']);
    $this->session->set_userdata('std_sch_id',$input['std_sch_id']);
    $this->session->set_userdata('blueprint_id',$input['blueprint_id']);

    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Concession applied');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    }
    redirect('feesv2/fees_collection/fee_collect');
  }


  public function get_installmentdata(){
    $fee_student_schedule_id = $_POST['fee_student_schedule_id'];
    $fsiId = $_POST['fsiId'];    
    // $insCompCount = $_POST['insCompCount'];
    $fee_amount = $this->fees_collection_model->get_std_fee_amount_installmentwise($fee_student_schedule_id,$fsiId);
    $insCompCount = []; 
    foreach ($fee_amount as $key => $val) {
      if(!array_key_exists($val->fsInsId, $insCompCount)) {
        $insCompCount[$val->fsInsId] = 0;
      }
      $insCompCount[$val->fsInsId]++;
    }
    $noOfComponents = count($fee_amount);
    echo json_encode(array('fee_amount'=>$fee_amount, 'noOfComponents'=> $noOfComponents,'insCompCount'=>$insCompCount));
  }
  

  public function reconsilation_fee(){
      $feeId = $_POST['feeId'];
      $bank_date = $_POST['bank_date'];
      $cohort_student_id = $_POST['cohort_student_id'];
      $this->db->trans_start();

      $resultQuery = $this->fees_collection_model->update_bankdatefee_paymnet_trans($feeId, $bank_date);
      if ($resultQuery) {
        $this->fees_collection_model->update_student_schedule_all_table($feeId);
        $fee_trans = $this->fees_collection_model->get_fee_transcation_for_receipt($feeId);
        $parent_name = $this->fees_collection_model->get_created_online_parent_name($fee_trans->collected_by,$fee_trans->transaction_mode);
        $created_name = $this->avatar->getAvatarById($fee_trans->collected_by);
        $template = $this->fees_collection_model->get_fee_template_by_blueprint_id($fee_trans->no_of_ins->feev2_blueprint_id);
        $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($fee_trans->no_of_ins->feev2_blueprint_id);
        $payment_modes = json_decode($blue_print->allowed_payment_modes);
        $result = $this->_create_template_fee_counter_amount($fee_trans, $template, $payment_modes, $created_name, $parent_name);
        // echo "<pre>"; print_r($result); die();
        if(!empty($result)){
          $update =  $this->fees_collection_model->update_html_receipt($result, $feeId);
          if($update) {
            $this->__generatefee_pdf_receipt($result, $feeId, $blue_print->receipt_for, $blue_print->receipt_pdf_page_alignment);
          }
        }
        
      }else{
        return false;
      }
      $this->db->trans_complete();
      if ($this->db->trans_status() === true) {
        return true;
      }else{
        return false;
      }
  }

  public function history($std_id, $transvers_to){
    $data['refund'] = $this->authorization->isAuthorized('FEESV2.REFUND');
    $data['transvers_to'] = $transvers_to;
    $data['std_id'] = $std_id;
    $fee_blueprints = $this->fees_student_model->get_blueprints();
    foreach ($fee_blueprints as &$fbp) {
      //Assign cohorts if not assigned yet
      $data['student'] = $this->fees_student_model->get_std_detailsbyId($std_id, $fbp->acad_year_id);
      $fbp->std_fee = $this->fees_collection_model->fee_student_fee_details($std_id, $fbp->id);
      $fbp->concession_adjustment = $this->fees_student_model->get_concession_adjustment_amount($std_id, $fbp->id);
      $fbp->compCount = $this->fees_collection_model->fee_total_no_of_components($fbp->id);
      if (!empty($fbp->std_fee)) {
        $fbp->installments = $this->fees_collection_model->get_installments_all_history($fbp->std_fee->feev2_blueprint_installment_types_id);
        $fbp->history = $this->fees_collection_model->fee_student_fee_history($fbp->std_fee->stdSchId);
      }
    }
    $data['fee_history'] = $fee_blueprints;
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    // echo "<pre>"; print_r($data['fee_adjustment_amount']); die();
    $data['main_content'] = 'feesv2/transaction/history_new';
    $this->load->view('inc/template', $data);
  }

  public function history_ajax(){
    $std_id = $_POST['std_id'];
    // $transvers_to = $_POST['transvers_to'];
    // $data['transvers_to'] = $transvers_to;
    // $data['std_id'] = $std_id;
    // $data['refund'] = $this->authorization->isSuperAdmin();
    $data['refund'] = $this->authorization->isAuthorized('FEESV2.REFUND');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['fee_refund_amount'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['delete_authorization'] = $this->authorization->isAuthorized('FEESV2.SOFT_DELETE_RECEIPTS');
    
    $fee_blueprints = $this->fees_student_model->get_blueprints();
    // $acad_year_id = $this->acad_year->getAcadYearId();
    foreach ($fee_blueprints as &$fbp) {
      //Assign cohorts if not assigned yet
      // $acad_year_id = $fbp->acad_year_id;
      $fbp->std_fee = $this->fees_collection_model->fee_student_fee_details($std_id, $fbp->id);
      $fbp->concession_adjustment = $this->fees_student_model->get_concession_adjustment_amount($std_id, $fbp->id);
      $fbp->compCount = $this->fees_collection_model->fee_total_no_of_components($fbp->id);
      if (!empty($fbp->std_fee)) {
        $fbp->installments = $this->fees_collection_model->get_installments_all_history($fbp->std_fee->feev2_blueprint_installment_types_id);
        $fbp->history = $this->fees_collection_model->fee_student_fee_history($fbp->std_fee->stdSchId);
      }
    }
    // $data['student'] = $this->fees_student_model->get_std_detailsbyId($std_id, $acad_year_id);
    $data['fee_history'] = $fee_blueprints;
    
    echo json_encode($data);
  }

  public function fee_soft_delete(){
    $feeId = $_POST['feeId'];
    $remarks = $_POST['remarks'];
    $this->fees_collection_model->store_fees_edit_history($_POST['student_id'],'Fees Receipt Deleted Receipt No : '.$_POST['receipt_number']);
    echo $this->fees_collection_model->soft_delete_feereceipt($feeId, $remarks);
  }

  public function reset_confirm_data($student_id, $cohort_student_id, $blueprint_id){
    
    $result = $this->fees_collection_model->reset_confirm_student_cohort_data($cohort_student_id);
    if ($result) {

      $auditDesc = 'Reset Fee Structure';
      $this->fees_student_model->insert_fee_audit($student_id, 'Reset Fee Structure', $auditDesc, $this->authorization->getAvatarId(), $blueprint_id);

      $this->session->set_flashdata('flashSuccess', 'Fee data reset successfully');
      redirect('feesv2/fees_collection/fee_student_blueprints/'.$student_id);
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong');
      redirect('feesv2/fees_collection/fee_student_blueprints/'.$student_id);
    }
  }

  public function fee_reciept($fTrans){
    $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
    $data['created_name'] = $this->avatar->getAvatarById($data['fee_trans']->collected_by);
    $parent_name = $this->fees_collection_model->get_created_online_parent_name($data['fee_trans']->collected_by,$data['fee_trans']->transaction_mode); // get parent name
    $template = $this->fees_collection_model->get_fee_template_by_blueprint_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);

    $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);

    $payment_modes = json_decode($blue_print->allowed_payment_modes);
    if ($template) {
      $result = $this->_create_template_fee_counter_amount($data['fee_trans'],$template, $payment_modes, $data['created_name'],$parent_name);
    }
    if (!empty($result)) {
      $update =  $this->fees_collection_model->update_html_receipt($result, $fTrans);
      if($update) {
        $this->__generatefee_pdf_receipt($result, $fTrans, $blue_print->receipt_for, $blue_print->receipt_pdf_page_alignment);
      }
    }
  
    redirect('feesv2/fees_collection/fee_reciept_view/'.$fTrans); 
  }

  public function fee_reciept_view($fTrans, $canceled = ''){
    $data['cancel'] = $canceled;
    // $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
    $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
    $data['created_name'] = $this->avatar->getAvatarById($data['fee_trans']->collected_by);
    $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);

    $data['payment_modes'] = json_decode($blue_print->allowed_payment_modes);
    // echo "<pre>"; print_r($data['fee_trans']); die();

    // $receipt_for = $this->settings->getSetting('receipt_for');
    $data['main_content'] = 'feesv2/receipts/'.$blue_print->receipt_for;
    $this->load->view('inc/template_fee', $data);
  }

  private function __generatefee_pdf_receipt($html, $fTrans, $receipt_for, $pdf_page_mode) {
    $school = CONFIG_ENV['main_folder'];
    $path = $school.'/fee_reciepts/'.uniqid().'-'.time().".pdf";

    $bucket = $this->config->item('s3_bucket');

    $status = $this->fees_collection_model->updateFeePath($fTrans, $path);
    $page = $pdf_page_mode;
    $page_size = 'a4';
    if ($receipt_for === 'ourschool_academic') {
      $page_size = 'a5';
      $page = 'portrait';
    }
    if ($receipt_for === 'vinayaka') {
      $page = 'portrait';
    }
    if ($receipt_for === 'prarthana') {
      $page = 'portrait';
    }
    if ($receipt_for === 'transcendschool') {
      $page = 'portrait';
    }
    $curl = curl_init();
    $postData = urlencode($html);
    $username = CONFIG_ENV['job_server_username'];
    $password = CONFIG_ENV['job_server_password'];
    $return_url = site_url().'Callback_Controller/updateFeePdfLink';

    curl_setopt_array($curl, array(
        CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERPWD => $username . ":" . $password,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
        CURLOPT_HTTPHEADER => array(
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Content-Type: application/x-www-form-urlencoded",
            "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
        ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);
    return $response;
  }

  public function _create_template_fee_counter_amount($fee_trans, $template, $payment_modes, $created_name, $parent_name, $online_tranxId = 'NA')
  {
    $medium = $this->settings->getSetting('medium')[$fee_trans->student->medium];
    $concessionColumn = $this->settings->getSetting('fee_receipt_concession_column');
    $schoolName = $this->settings->getSetting('school_short_name');
    $class = $fee_trans->student->classSection;
    if ($fee_trans->student->is_placeholder == 1) {
      $class = $fee_trans->student->clsName;
    }
    if($schoolName =='transcenddegree'){
        switch ($fee_trans->student->clsName) {
          case '13':
            $class = '1st Year B.Com';
            break;
          case '14':
            $class = '2nd Year B.Com';
            break;
          case '14':
            $class = '3rd Year B.Com';
            break;
          default:
            $class = $fee_trans->student->clsName;
            break;
        }
    }

    $createdByParentName = '';
    if($fee_trans->transaction_mode =='ONLINE'){
      $createdByParentName.='<td style="padding-left: 20%;"><b>Parent:</b>'.$parent_name->parent_name.'</td>';
      $createdByParentName.='<td style="padding-left: 35%;"></td>';
    }else{
      $createdByParentName.='<td style="padding-left: 20%;"><b>Father Name:</b>'.$fee_trans->student->fName.'</td>';
      $createdByParentName.='<td style="padding-left: 35%;"><b>Mother Name:</b>'.$fee_trans->student->mName.'</td>';
    }
    $overAllFeeBalance = 0;
		if(!empty($fee_trans->overallFeeBal)){
			$overAllFeeBalance = $fee_trans->overallFeeBal->over_all_balance;
		}
    $template = str_replace('%%receipt_no%%',$fee_trans->receipt_number, $template);
    $template = str_replace('%%class%%',$class, $template);
    $template = str_replace('%%class_name%%',$fee_trans->student->clsName, $template);
    $template = str_replace('%%class_medium%%',$class.'/'.$medium, $template);
    $template = str_replace('%%transaction_id%%', $online_tranxId, $template);
    $template = str_replace('%%transaction_date%%', date('d-m-Y', strtotime($fee_trans->paid_datetime)), $template);
    $template = str_replace('%%f_number%%',$fee_trans->student->mobile_no, $template);
    $template = str_replace('%%student_name%%', $fee_trans->student->stdName, $template);
    $template = str_replace('%%father_name%%', $fee_trans->student->fName, $template);
    $template = str_replace('%%mother_name%%', $fee_trans->student->mName, $template);
    $template = str_replace('%%admission_no%%', $fee_trans->student->admission_no, $template);
    $template = str_replace('%%sts_number%%', $fee_trans->student->sts_number, $template);
    $template = str_replace('%%remarks%%', $fee_trans->remarks, $template);
    $template = str_replace('%%academic_year%%', $this->acad_year->getAcadYearById($fee_trans->acad_year_id), $template);
    $template = str_replace('%%enrollment_number%%', $fee_trans->student->enrollment_number, $template);
    $template = str_replace('%%roll_no%%', $fee_trans->student->roll_no, $template);
    $template = str_replace('%%combination%%', $fee_trans->student->combination, $template);
    $template = str_replace('%%f_mobile_no%%', $fee_trans->student->mobile_no, $template);
    $template = str_replace('%%semester%%', $fee_trans->student->semester, $template);
    $template = str_replace('%%createdByParentName%%', $createdByParentName, $template);
    $template = str_replace('%%overAllBalance%%', $overAllFeeBalance, $template);
    $template = str_replace('%%coupon_code%%', $fee_trans->student->custom1, $template);
    $template = str_replace('%%receipt_generated_time%%', $fee_trans->receipt_generated_time, $template);
    
    $style="";
    if ($schoolName == 'prarthana') {
      $style='text-align:right';
    }
    $con_remaarks = 'Concession (-)';
    if ($schoolName == 'divine') {
      $con_remaarks ='Concession (-)<br> (Deductions Covid - 19)';
    }
    $i=1;
    $cnt= 0; 
    $sl=0;
    $t=1;
    $totalAmount = 0;
    $totalAmount_sales =0;
    if ($fee_trans->no_of_ins->ins_count > 1) {
      $colspan=3;
    }else{
      $colspan=2;
    }
    $sales_part = '';
    $sales_part .= '<table>';
    $sales_part .= '<tr>';
    $sales_part .= '<th>S. No.</th>';
    $sales_part .= '<th>Category</th>';
    $sales_part .= '<th>HSN/SAC</th>';
    $sales_part .= '<th>Size</th>';
    $sales_part .= '<th>Quantity</th>';
    $sales_part .= '<th>Rate</th>';
    $sales_part .= '<th>Amount</th>';
    $sales_part .= '</tr>';
     foreach ($fee_trans->comp as $key => $trans) {      
      $totalAmount_sales += $trans->amount_paid + $trans->concession_amount;
      $sales_part .='<tr>';
      $sales_part .='<td style="vertical-align: middle;">'.$i++.'</td>';
      $sales_part .='<td>'.$trans->compName.'</td>';
      $sales_part .='<td></td>';
      $sales_part .='<td></td>';
      $sales_part .='<td></td>';
      $sales_part .='<td></td>';
      $sales_part .='<td>'.($trans->amount_paid + $trans->concession_amount).'</td>';
      $sales_part .='</tr>';
    }


    $sales_part .= '<tr>';
    $sales_part .='<td colspan="6" style="text-align: right;">Fee Paid</td>';
    $sales_part .='<td>'.$totalAmount_sales.'</td>'; 
    $sales_part .='</tr>';

    if($fee_trans->concession_amount != 0) {
      $sales_part .='<tr>';
      $sales_part .='<td colspan="6" style="text-align:right;">Concession (-)</td>';
      $sales_part .='<td>'.$fee_trans->concession_amount.'</td>';
      $sales_part .='</tr>';
    }

    if($fee_trans->discount_amount != 0) {
      $sales_part .='<tr>';
      $sales_part .='<td colspan="6" style="text-align:right;">Discount (-)</td>';
      $sales_part .='<td>'.$fee_trans->discount_amount.'</td>';
      $sales_part .='</tr>';
    }

    if($fee_trans->fine_amount != 0) {
      $sales_part .='<tr>';
      $sales_part .='<td colspan="6" style="text-align:right;">Late Fee</td>';
      $sales_part .='<td>'.$fee_trans->fine_amount.'</td>';
      $sales_part .='</tr>';
    }
    if($fee_trans->card_charge_amount != 0) {
      $sales_part .='<tr>';
      $sales_part .='<td colspan="6" style="text-align:right;">Card Charge Amount</td>';
      $sales_part .='<td>'.$fee_trans->card_charge_amount.'</td>';
      $sales_part .='</tr>';
    }

    $sales_part .='<tr>';
    $sales_part .='<td colspan="6" style="text-align:right;border: solid 1px #474747;"><strong>Total amount paid</strong></td>';
    $sales_part .='<td style="border: solid 1px #474747;">'.$fee_trans->amount_paid.'</td>';
    $sales_part .='</tr>';

    $sales_part .= '</table>';


    $header_part = '<table>';
    $header_part .= '<tr>';
    $header_part .= '<th width="10%">Sl.no</th>';
    if ($fee_trans->no_of_ins->ins_count > 1) {
    $header_part .= '<th width="25%">Installment</th>';
    }
    $header_part .= '<th>Particulars</th>';
    $header_part .= '<th style='.$style.'>Amount</th>';

    if ($concessionColumn && $fee_trans->concession_amount !=0) {
      $header_part .= '<th>Concession</th>';
      $header_part .= '<th style='.$style.'>Amount paid</th>';
    }
    $concessionTotal=0;
    $adjustmentTotal=0;
    $totalAmountPaid = 0;
    $header_part .= '</tr>';
    $rowspan = $fee_trans->no_of_comp->comp_count; 
    // if ($schoolName == 'vsips') {
    //   $rowspan= '0';
    // }
    
    // $component_part = '';
    // $f=1;
    // foreach ($fee_trans->comp as $key => $trans) {     
    //   $totalAmount += $trans->amount_paid + $trans->concession_amount + $trans->adjustment_amount;
    //   $totalAmountPaid += $trans->amount_paid;
    //   $concessionTotal += $trans->concession_amount;
    //   $adjustmentTotal += $trans->adjustment_amount;
    //   $component_part.='<tr>';
    //   if (!$sl) {
    //     $component_part.='<td style="vertical-align: middle;" rowspan="'.$rowspan .'">'.$f++.'</td>';
    //     $sl .= $rowspan;
    //   }
    //   $sl--;

    //   if($fee_trans->no_of_ins->ins_count >= 2){ 
    //   if(!$cnt) { 
    //     $component_part.= '<td style="vertical-align: middle;" rowspan="'.$rowspan.'" >'.$trans->insName.'</td>';
    //     $cnt = $rowspan; }
    //     $cnt--;     
    //   }

    //   $component_part.='<td>'.$trans->compName.'</td>';
    //   $component_part.='<td style='.$style.'>'.($trans->amount_paid + $trans->concession_amount + $trans->adjustment_amount).'</td>';
    //   if ($concessionColumn && $fee_trans->concession_amount !=0) {
    //     $component_part.='<td style='.$style.'>'.$trans->concession_amount.'</td>';
    //     $component_part.='<td style='.$style.'>'.$trans->amount_paid.'</td>';
    //   }
    //   $component_part.='</tr>';
    // }
    
    $m=1;
    $m1=1;
    $slN = 0;
    $slN1 = 0;
    $cntN = 0; 
    $cntN1 = 0; 
    $component_part_new = '';
    $component_part_new_paid = '';
    $footer_without_part = '';
    foreach ($fee_trans->transInsComp as $insName => $comp) {
      $compCount =  count($comp);
      foreach ($comp as $key => $trans) {  
        $totalAmount += $trans->amount_paid + $trans->concession_amount + $trans->adjustment_amount;
        $totalAmountPaid += $trans->amount_paid;
        $concessionTotal += $trans->concession_amount;
        $adjustmentTotal += $trans->adjustment_amount;
        $component_part_new.='<tr>';
        if (!$slN) {
          $component_part_new.='<td style="vertical-align: middle;" rowspan="'.$compCount .'">'.$m++.'</td>';
          $slN .= $compCount;
        }
        $slN--;
        if($fee_trans->no_of_ins->ins_count >= 2){
        if(!$cntN) { 
        $component_part_new.= '<td style="vertical-align: middle;" rowspan="'.$compCount.'" >'.$insName.'</td>';
        $cntN = $compCount; }
        $cntN--;   
        }
        $component_part_new.='<td>'.$trans->compName.'</td>';
        $component_part_new.='<td style='.$style.'>'.($trans->amount_paid + $trans->concession_amount + $trans->adjustment_amount).'</td>';
        if ($concessionColumn && $fee_trans->concession_amount !=0) {
          $component_part_new.='<td style='.$style.'>'.$trans->concession_amount.'</td>';
          $component_part_new.='<td style='.$style.'>'.$trans->amount_paid.'</td>';
        }
        $component_part_new.='</tr>';

        $component_part_new_paid.='<tr>';
        if (!$slN1) {
          $component_part_new_paid.='<td style="vertical-align: middle;" rowspan="'.$compCount .'">'.$m1++.'</td>';
          $slN1 .= $compCount;
        }
        $slN1--;
        if($fee_trans->no_of_ins->ins_count >= 2){
        if(!$cntN1) { 
        $component_part_new_paid.= '<td style="vertical-align: middle;" rowspan="'.$compCount.'" >'.$insName.'</td>';
        $cntN1 = $compCount; }
        $cntN1--;   
        }
        $component_part_new_paid.='<td>'.$trans->compName.'</td>';
        $component_part_new_paid.='<td style='.$style.'>'.$trans->amount_paid.'</td>';
        $component_part_new_paid.='</tr>';

      }
    }
    $without_comp ='<tr>';
    $without_comp.='<td>'.$i++.'</td>';     
    $without_comp.='<td>'.$fee_trans->no_of_comp->blueprint_name.'</td>';
    $without_comp.='<td>'.$totalAmount.'</td>';
    $without_comp.='</tr>';

    if ($schoolName !='achieve') {
    $footer_part = '<tr>';
    $footer_part.='<td colspan="'.$colspan.'" style="text-align: right;">Total Fee</td>';
    $footer_part.='<td style='.$style.'>'.$totalAmount.'</td>'; 
    
    if ($concessionColumn && $fee_trans->concession_amount !=0) {
      $footer_part.='<td style='.$style.'>'.'(-) '.$concessionTotal.'</td>'; 
      $footer_part.='<td style='.$style.'>'.$totalAmountPaid.'</td>'; 
    }

    $footer_part.='</tr>';
  }
    $cols=0;
     if ($fee_trans->concession_amount != 0) {
      $cols=3;
    } 
  
    if($fee_trans->concession_amount != 0 && $schoolName != 'jesps' && $schoolName != 'vts' && $schoolName != 'advitya') {
      $footer_part.='<tr>';
      $footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">'.$con_remaarks.'</td>';
      $footer_part.='<td style='.$style.' colspan='.$cols.'">'.$fee_trans->concession_amount.'</td>';
      $footer_part.='</tr>';
    }

    if($fee_trans->adjustment_amount != 0) {
      $footer_part.='<tr>';
      $footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">Adjustment (-)</td>';
      $footer_part.='<td style='.$style.'  colspan="'.$cols.'">'.$fee_trans->adjustment_amount.'</td>';
      $footer_part.='</tr>';
    }

    if($fee_trans->discount_amount != 0) {
      $footer_part.='<tr>';
      $footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">Discount (-)</td>';
      $footer_part.='<td style='.$style.'  colspan="'.$cols.'">'.$fee_trans->discount_amount.'</td>';
      $footer_part.='</tr>';
    }

    if($fee_trans->fine_amount != 0) {
      $footer_part.='<tr>';
      $footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">Late Fee</td>';
      $footer_part.='<td style='.$style.'  colspan="'.$cols.'">'.$fee_trans->fine_amount.'</td>';
      $footer_part.='</tr>';
    }
    if($schoolName == 'vinayaka') {
      $footer_part.='<tr>';
      $footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">Fine Amount</td>';
      $footer_part.='<td style='.$style.'  colspan="'.$cols.'">0</td>';
      $footer_part.='</tr>';
    }
    if($fee_trans->card_charge_amount != 0) {
      $footer_part.='<tr>';
      $footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">Card Charge Amount</td>';
      $footer_part.='<td style='.$style.'  colspan="'.$cols.'">'.$fee_trans->card_charge_amount.'</td>';
      $footer_part.='</tr>';
    }

    $footer_part.='<tr>';
    $footer_part.='<td colspan="'.$colspan.'" style="text-align:right;border: solid 1px #474747;"><strong>Fee Paid</strong></td>';
    if ($schoolName =='jesps') {
      $footer_part.='<td style='.$style.'  colspan="'.$cols.'" style="border: solid 1px #474747;">'.$totalAmount.'</td>';
    }else{
      $footer_part.='<td style='.$style.'  colspan="'.$cols.'" style="border: solid 1px #474747;">'.($fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount).'</td>';

    }
    $footer_part.='</tr>';

    $footer_without_part.='<tr>';
    $footer_without_part.='<td colspan="'.$colspan.'" style="text-align:right;border: solid 1px #474747;"><strong>Fee Paid</strong></td>';
    $footer_without_part.='<td style='.$style.'  colspan="'.$cols.'" style="border: solid 1px #474747;">'.($fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount).'</td>';
    $footer_without_part.='</tr>';
    $footer_without_part .= '</table>';

    $footer_part .= '</table>';
    $without_comp_dynamic_part = $header_part.$without_comp.$footer_part;
    $dynamic_part = $header_part.$component_part_new.$footer_part;
    $dynamic_partwithout_footer = $header_part . $component_part_new_paid . $footer_without_part;
    // $dynamic_part_new = $header_part.$component_part_new.$footer_part;

    $footer_yashasvi = '<table style="margin-bottom:0">';
    $footer_yashasvi .= '<tr>';
    $footer_yashasvi .= '<th>Balance Amount</th>';
    $footer_yashasvi .= '<th>Entered By</th>';
    $footer_yashasvi .= '</tr>';
    $footer_yashasvi.='<tr>';
    $footer_yashasvi.='<td>'.($fee_trans->no_of_ins->total_fee -  $fee_trans->no_of_ins->total_fee_paid -  $fee_trans->no_of_ins->total_concession_amount_paid - $fee_trans->no_of_ins->total_adjustment_amount_paid).'</td>';
    $footer_yashasvi.='<td>'.$created_name->fName.'</td>';
    $footer_yashasvi.='</tr>';
     $footer_yashasvi.='<tr>';
    $footer_yashasvi.='<td></td>';
    $footer_yashasvi.='<td>This is computer generated receipt. No signature required</td>';
    $footer_yashasvi.='</tr>';
    $footer_yashasvi .= '</table>';

    $next_due_list = '<tr>';
    $next_due_list .= '<th>Particulars</th>';
    $next_due_list .= '<th>Amount</th>';
    if ($this->settings->getSetting('school_short_name') != 'npsjnr') {
      $next_due_list .= '<th>Due Date</th>';
    }

    $next_due_list .= '</tr>';
    $fee_balance_total = 0;
    foreach($fee_trans->bal_ins as $bal){
      $fee_balance_total += $bal->ins_balance;
      $next_due_list.='<tr>';
      $next_due_list.='<td>'.$bal->installment_name.'</td>';
      $next_due_list.='<td>'.$bal->ins_balance.'</td>';
    if ($this->settings->getSetting('school_short_name') != 'npsjnr') {
      $next_due_list.='<td>'.$bal->due_date.'</td>';
    }
      $next_due_list.='</tr>';
    }

    $installment_details = '';
    $totalInstallment_amount = 0;
    $totalInstallment_amount_paid = 0;
    $totalTotal_concession = 0;
    $totalIns_balance = 0;
		foreach($fee_trans->bal_ins as $bal){
      $totalInstallment_amount += $bal->installment_amount;
			$totalInstallment_amount_paid += $bal->installment_amount_paid;
			$totalTotal_concession += $bal->total_concession;
			$totalIns_balance += $bal->ins_balance;
			$installment_details.='<tr>';
			$installment_details.='<td>'.$bal->installment_name.'</td>';
			$installment_details.='<td>'.$bal->installment_amount.'</td>';
			$installment_details.='<td>'.$bal->installment_amount_paid.'</td>';
			$installment_details.='<td>'.$bal->total_concession.'</td>';
			$installment_details.='<td>'.$bal->ins_balance.'</td>';
			$installment_details.='</tr>';
		}
    $installment_details.='<tr>';
		$installment_details.='<td>Total</th>';
		$installment_details.='<td>'.$totalInstallment_amount.'</td>';
		$installment_details.='<td>'.$totalInstallment_amount_paid.'</td>';
		$installment_details.='<td>'.$totalTotal_concession.'</td>';
		$installment_details.='<td>'.$totalIns_balance.'</td>';
		$installment_details.='</tr>';
    // $fee_balance = $fee_trans->no_of_ins->total_fee -  $fee_trans->no_of_ins->total_fee_paid -  $fee_trans->no_of_ins->total_concession_amount_paid;
    $fee_balance = $fee_balance_total;
    $amountInWords = $this->getIndianCurrency($fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount);
    $totalAmountString = $fee_trans->amount_paid + $fee_trans->fine_amount - $fee_trans->discount_amount;
    $paymentTypeString = '';
    $paymentTypeChequeNumberString = '';
    $paymentTypeChequeDateString = '';
    $paymentTypeBankDateString = '';
    $payment_mode = '<table style="margin:0">';
    if($fee_trans->payment_type == '10'){
      $payment_mode .='<tr>';
      $payment_mode .='<td><strong>Payment Type : </strong> Online Payment</td>';
      $payment_mode .='<td><strong>Payment Mode : </strong>'.$fee_trans->online_tx_mode.'</td>';
      $payment_mode .='</tr>';
      $paymentTypeString = 'Online Payment';
    }else if($fee_trans->payment_type == '777'){
      $payment_mode .='<tr>';
      $payment_mode .='<td><strong>Payment Type : </strong> Online Challan Payment</td>';
      $payment_mode .='</tr>';
      $paymentTypeString = 'Online Challan Payment';
    }else{
      foreach ($payment_modes as $key => $type) {
        if ($type->value == $fee_trans->payment_type ) {
          $paymentTypeString = strtoupper($type->name);
          $paymentTypeChequeNumberString = $fee_trans->cheque_dd_nb_cc_dd_number;
          $paymentTypeChequeDateString = date('d-m-Y',strtotime($fee_trans->cheque_or_dd_date));
          $paymentTypeBankDateString = $fee_trans->bank_name;
          if ($type->value == '1' || $type->value == '4') {
            $payment_mode .='<tr>';
            $payment_mode .='<td style="border:none" >Payment Type : '.strtoupper(str_replace('_',' ',$type->name)).'</td>';
            $payment_mode .='<td style="border:none">Date :'.date('d-m-Y',strtotime($fee_trans->cheque_or_dd_date)).'</td>';
            $payment_mode .='<td style="border:none">Drawn On :'.$fee_trans->bank_name.'</td>';
            $payment_mode .='<td style="border:none">Ref Number :'.$fee_trans->cheque_dd_nb_cc_dd_number.'</td>';
            $payment_mode .='</tr>';
          }else if($type->value == '8' || $type->value == '2' || $type->value == '3' || $type->value == '11'){
            $payment_mode .='<tr>';
            $payment_mode .='<td style="border:none" >Payment Type : '.strtoupper(str_replace('_',' ',$type->name)).'</td>';
            $payment_mode .='<td style="border:none">Date :'.date('d-m-Y',strtotime($fee_trans->cheque_or_dd_date)).'</td>';
            $payment_mode .='<td style="border:none">Ref Number :'.$fee_trans->cheque_dd_nb_cc_dd_number.'</td>';
            $payment_mode .='</tr>';

          }else{
            $payment_mode .='<tr>';
            $payment_mode .='<td>Payment Type '.strtoupper($type->name).'</td>';
            $payment_mode .='</tr>';
          }
        }
      }
    }
    
    $payment_mode .= '</table>';

    $classSectionName=$fee_trans->student->clsName;

    $note_npsknr = $this->_construct_not_for_npskr($classSectionName);

    $template = str_replace('%%installements%%',$dynamic_part, $template);
    $template = str_replace('%%paymentTypeString%%',$paymentTypeString, $template);
    $template = str_replace('%%totalAmountString%%',$totalAmountString, $template);
    $template = str_replace('%%paymentTypeChequeNumberString%%',$paymentTypeChequeNumberString, $template);
    $template = str_replace('%%paymentTypeChequeDateString%%',$paymentTypeChequeDateString, $template);
    $template = str_replace('%%paymentTypeBankDateString%%',$paymentTypeBankDateString, $template);
    // $template = str_replace('%%installements_new%%',$dynamic_part_new, $template);
    // $template = str_replace('%%installements_covind%%',$dynamic_part_covid, $template);
    $template = str_replace('%%installements_without_components%%',$without_comp_dynamic_part, $template);
    $template = str_replace('%%payment_modes%%',$payment_mode, $template);
    $template = str_replace('%%rupees_in_words%%',ucwords($amountInWords), $template);
    $template = str_replace('%%footer_yashasvi%%',$footer_yashasvi, $template);
    $template = str_replace('%%fee_balance%%',$fee_balance, $template);
    $template = str_replace('%%next_due_list%%',$next_due_list, $template);
    $template = str_replace('%%sales%%',$sales_part, $template);
    $template = str_replace('%%fees_total_amount%%',$totalAmount, $template);
    $template = str_replace('%%fees_paid_amount%%',$totalAmountPaid, $template);
    $template = str_replace('%%fees_concession_amount%%',$concessionTotal, $template);
    $template = str_replace('%%fees_adjustment_amount%%',$adjustmentTotal, $template);
    $template = str_replace('%%installements_without_footer%%', $dynamic_partwithout_footer, $template);
		$template = str_replace('%%installment_details%%', $installment_details, $template);
		$template = str_replace('%%class_wise_note%%', $note_npsknr, $template);
    return $template;
  }

  private function _construct_not_for_npskr($class){
     $nursery = ' <table class="no-border" style="width: 100%; text-align: left; ">
         <tr>
           <td><strong><h3>List of Books / Services Provided</h3></strong></td>
         </tr>
         
       </table>
<table class="table table-border" style="width: 100%; ">
    
         <tr>
          <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
          <td style="text-align: center;width: 30%;"><b>Book Type</b></td>
          <td style="text-align: center;width: 7%;"><b>Qty</b></td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">Tinker Advanced Primer - Nursery</td>
          <td style="text-align: center;">Text Book</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">Student Resource Kit</td>
          <td style="text-align: center;">Text Book</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">Diary</td>
          <td style="text-align: center;">Diary</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">SAPA Music Academy Program</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">School ERP / Parent App</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions in the the School Book Kit:</strong>
            <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
          </td>
        </tr>
       </table>'; 

 $lkg = '<table class="no-border" style="width: 100%; text-align: left; ">
         <tr>
           <td><strong><h3>List of Books / Services Provided</h3></strong></td>
         </tr>
         
       </table>
<table class="table table-border" style="width: 100%; ">
         
         <tr>
          <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
          <td style="text-align: center;width: 30%;"><b>Book Type</b></td>
          <td style="text-align: center;width: 8%;"><b>Qty</b></td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">Tinker Advanced Primer A - LKG</td>
          <td style="text-align: center;">Text Book</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">Student Resource Kit</td>
          <td style="text-align: center;">Text Book</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">50 Pg. Four line Ruled book</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">50 Pg. Big Square Ruled Book</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Diary</td>
          <td style="text-align: center;">Diary</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">SAPA Music Academy Program</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">School ERP / Parent App</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
            <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
          </td>
        </tr>
       </table>'; 

  $ukg = '<table class="no-border" style="width: 100%; text-align: left; ">
         <tr>
           <td><strong><h3>List of Books / Services Provided</h3></strong></td>
         </tr>
         
       </table>
<table class="table table-border" style="width: 100%; ">
       
         <tr>
          <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
          <td style="text-align: center;width: 30%;"><b>Book Type</b></td>
          <td style="text-align: center;width: 8%;"><b>Qty</b></td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">Tinker Advanced Primer B - UKG</td>
          <td style="text-align: center;">Text Book</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">Student Resource Kit</td>
          <td style="text-align: center;">Text Book</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">50 Pg. four Line Ruled Book</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">50 Pg. Big Square Ruled Book</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
 
<tr>
          <td style=" padding-left: 1%;">SAPA Music Academy Program</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">School ERP / Parent App</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
            <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
          </td>
        </tr>
       </table>'; 

  $g1 = '<table class="no-border" style="width: 100%; text-align: left; ">
         <tr>
           <td><strong><h3>List of Books / Services Provided</h3></strong></td>
         </tr>
         
       </table>
<table class="table table-border" style="width: 100%; ">
         
         <tr>
          <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
          <td style="text-align: center;width: 30%;">Type</td>
          <td style="text-align: center;width: 8%;"><b>Qty</b></td>
        </tr>
      
        <tr>
          <td style=" padding-left: 1%;">Gul Mohar – Reader 1</td>
          <td style="text-align: center;">Text Book</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">Enjoying Grammar - 1</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
         <tr>
          <td style=" padding-left: 1%;">Math Steps -1</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 
<tr>
          <td style=" padding-left: 1%;">Window on the World -1</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
       <tr>
          <td style=" padding-left: 1%;">Madhuban Saral - 1 </td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Kali Kannada - 1</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 1</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">The Knowledge Tree – a Book on GK</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Art Day - A Book of art and craft - 1</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg.Four ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Small Square</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">2</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Four ruled ( interleaf)</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">2</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Double ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Double ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">2</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Double ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">2</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg.Four ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg.Four ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">Diary</td>
          <td style="text-align: center;">Diary</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">File</td>
          <td style="text-align: center;">File</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">SAPA Music Academy Program</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">School ERP / Parent App.</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
            <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
          </td>
        </tr>
       </table>'; 

  $g2 = ' <table class="no-border" style="width: 100%; text-align: left; ">
         <tr>
           <td><strong><h3>List of Books / Services Provided</h3></strong></td>
         </tr>
         
       </table>
<table class="table table-border" style="width: 100%; ">
         
         <tr>
          <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
          <td style="text-align: center;width: 30%;">Type</td>
          <td style="text-align: center;width: 8%;"><b>Qty</b></td>
        </tr>
      
        <tr>
          <td style=" padding-left: 1%;">Gul Mohar – Reader 2</td>
          <td style="text-align: center;">Text Book</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">Enjoying Grammar - 2</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
         <tr>
          <td style=" padding-left: 1%;">Math Steps -2</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 
<tr>
          <td style=" padding-left: 1%;">Window on the World -2</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
       <tr>
          <td style=" padding-left: 1%;">Madhuban Saral - 2</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Kali Kannada - 2</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 2</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">The Knowledge Tree – a Book on GK</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Art Day - A Book of art and craft - 2</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg.Four ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">3</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Small Square</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">2</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Four ruled ( interleaf)</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">4</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Double ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Double ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">2</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Double ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">2</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg.Four ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg.Four ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">Diary</td>
          <td style="text-align: center;">Diary</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">File</td>
          <td style="text-align: center;">File</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">SAPA Book Kit</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;"></td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">Robotics & Coading</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;"></td>
        </tr>
        <tr>
          <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
            <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
          </td>
        </tr>
       </table>'; 

  $g3 = ' <table class="no-border" style="width: 100%; text-align: left; ">
         <tr>
           <td><strong><h3>List of Books / Services Provided</h3></strong></td>
         </tr>
         
       </table>
<table class="table table-border" style="width: 100%; ">
         
         <tr>
          <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
          <td style="text-align: center;width: 30%;">Type</td>
          <td style="text-align: center;width: 8%;"><b>Qty</b></td>
        </tr>
      
        <tr>
          <td style=" padding-left: 1%;">Gul Mohar – Reader 3</td>
          <td style="text-align: center;">Text Book</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">Enjoying Grammar - 3</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
         <tr>
          <td style=" padding-left: 1%;">Math Steps -3</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Eureka Plus -3</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Getting Ahead in Social Studies -3</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">Saral Hindi Pathamala - Book 3 </td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
       <tr>
          <td style=" padding-left: 1%;">Mai Aur Mera Vyakaran - Book 3</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Kali Kannada - 3</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 3</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">The Knowledge Tree – a Book on GK</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Art Day - A Book of art and craft - 2</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">5</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg.  Horizontal ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">6</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">2</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">3</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">3</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">3</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg.Single Ruled (Interleaf)</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">Diary</td>
          <td style="text-align: center;">Diary</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">File</td>
          <td style="text-align: center;">File</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">SAPA Book Kit</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;"></td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">Robotics & Coading</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;"></td>
        </tr>
        <tr>
          <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
            <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
          </td>
        </tr>
       </table>'; 

  $g4 = ' <table class="no-border" style="width: 100%; text-align: left; ">
         <tr>
           <td><strong><h3>List of Books / Services Provided</h3></strong></td>
         </tr>
         
       </table>
<table class="table table-border" style="width: 100%; ">
         
         <tr>
          <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
          <td style="text-align: center;width: 30%;">Type</td>
          <td style="text-align: center;width: 8%;"><b>Qty</b></td>
        </tr>
      
        <tr>
          <td style=" padding-left: 1%;">Gul Mohar – Reader 4</td>
          <td style="text-align: center;">Text Book</td>
          <td style="text-align: center;">1</td>
        </tr>
        <tr>
          <td style=" padding-left: 1%;">Enjoying Grammar - 4</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
         <tr>
          <td style=" padding-left: 1%;">Math Steps -3</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Eureka Plus -4</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Getting Ahead in Social Studies -4</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">Saral Hindi Pathamala - Book 4 </td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
       <tr>
          <td style=" padding-left: 1%;">Mai Aur Mera Vyakaran - Book 4</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Kali Kannada - 4</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 4</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">The Knowledge Tree – a Book on GK</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
 <tr>
          <td style=" padding-left: 1%;">Art Day - A Book of art and craft - 4</td>
          <td style="text-align: center;">Text book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">5</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg.  Horizontal ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">8</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">2</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">3</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">3</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">3</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">50 Pg.Single Ruled (Interleaf)</td>
          <td style="text-align: center;">Note book</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">Diary</td>
          <td style="text-align: center;">Diary</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">File</td>
          <td style="text-align: center;">File</td>
          <td style="text-align: center;">1</td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">SAPA Book Kit</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;"></td>
        </tr>
<tr>
          <td style=" padding-left: 1%;">Robotics & Coading</td>
          <td style="text-align: center;">NA</td>
          <td style="text-align: center;"></td>
        </tr>
        <tr>
          <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
            <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
          </td>
        </tr>
       </table>'; 

       $g5 = '  <table class="no-border" style="width: 100%; text-align: left; ">
     <tr>
       <td><strong><h3>List of Books / Services Provided</h3></strong></td>
     </tr>
     
   </table>
<table class="table table-border" style="width: 100%; ">
     
     <tr>
      <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
      <td style="text-align: center;width: 30%;">Type</td>
      <td style="text-align: center;width: 8%;"><b>Qty</b></td>
    </tr>
  
    <tr>
      <td style=" padding-left: 1%;">Gul Mohar – Reader 5</td>
      <td style="text-align: center;">Text Book</td>
      <td style="text-align: center;">1</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">Enjoying Grammar - 5</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
     <tr>
      <td style=" padding-left: 1%;">Math Steps -5</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Eureka Plus -5</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Getting Ahead in Social Studies -5</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Saral Hindi Pathamala - Book 5 </td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
   <tr>
      <td style=" padding-left: 1%;">Mai Aur Mera Vyakaran - Book 5</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Tili Kannada -5</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 5</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">The Knowledge Tree – a Book on GK</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Art Day - A Book of art and craft - 5</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">5</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg.  Horizontal ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">8</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg . Plain book</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">2</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">2</td>
    </tr>
    
<tr>
      <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">3</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">4</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">4</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Diary</td>
      <td style="text-align: center;">Diary</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">File</td>
      <td style="text-align: center;">File</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">SAPA Book Kit</td>
      <td style="text-align: center;">NA</td>
      <td style="text-align: center;"></td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Robotics & Coading</td>
      <td style="text-align: center;">NA</td>
      <td style="text-align: center;"></td>
    </tr>
    <tr>
      <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
        <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
      </td>
    </tr>
   </table>'; 

  $g6 = ' <table class="no-border" style="width: 100%; text-align: left; ">
     <tr>
       <td><strong><h3>List of Books / Services Provided</h3></strong></td>
     </tr>
     
   </table>
<table class="table table-border" style="width: 100%; ">
     
     <tr>
      <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
      <td style="text-align: center;width: 30%;">Type</td>
      <td style="text-align: center;width: 8%;"><b>Qty</b></td>
    </tr>
  
    <tr>
      <td style=" padding-left: 1%;">Gul Mohar – Reader 6</td>
      <td style="text-align: center;">Text Book</td>
      <td style="text-align: center;">1</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">Enjoying Grammar - 6</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
     <tr>
      <td style=" padding-left: 1%;">Mathematics – 6</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Learning Elementary Physics - 6</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Learning Elementary Chemistry- 6</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Learning Elementary Biology - 6</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
   <tr>
      <td style=" padding-left: 1%;">Getting Ahead in Social Studies -6</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">Saral Hindi Pathamala - Book 6</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">Mai Aur Mera Vyakaran - Book 6</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">Madhuban saral 3</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">Siri Kannada</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Tili Kannada </td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 6</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>


<tr>
      <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">5</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">4</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">4</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg.  Horizontal ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">10</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg . Plain book</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">3</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">8</td>
    </tr>
    
<tr>
      <td style=" padding-left: 1%;">50 Pg. . Single Ruled interleaf</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">2</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">2</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Diary</td>
      <td style="text-align: center;">Diary</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">File</td>
      <td style="text-align: center;">File</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">SAPA Book Kit</td>
      <td style="text-align: center;">NA</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Robotics & Coading</td>
      <td style="text-align: center;">NA</td>
      <td style="text-align: center;">1</td>
    </tr>
    <tr>
      <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
        <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
      </td>
    </tr>
   </table>'; 

  $g7 ='<table class="no-border" style="width: 100%; text-align: left; ">
     <tr>
       <td><strong><h3>List of Books / Services Provided</h3></strong></td>
     </tr>
     
   </table>
<table class="table table-border" style="width: 100%; ">
     
     <tr>
      <td style="padding-left: 1%;width: 60%;"><b>Name</b></td>
      <td style="text-align: center;width: 30%;">Type</td>
      <td style="text-align: center;width: 8%;"><b>Qty</b></td>
    </tr>
  
    <tr>
      <td style=" padding-left: 1%;">Gul Mohar – Reader 7</td>
      <td style="text-align: center;">Text Book</td>
      <td style="text-align: center;">1</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">Enjoying Grammar - 7</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
     <tr>
      <td style=" padding-left: 1%;">Mathematics – 7</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Learning Elementary Physics - 7</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Learning Elementary Chemistry - 7</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Learning Elementary Biology - 7</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
   <tr>
      <td style=" padding-left: 1%;">Getting Ahead in Social Studies -7</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">Saral Hindi Pathamala - Book 7</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">Mai Aur Mera Vyakaran - Book 7</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">Madhuban saral 4</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">Siri Kannada</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Tili Kannada </td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Happy Coder 2.0 – Level 7</td>
      <td style="text-align: center;">Text book</td>
      <td style="text-align: center;">1</td>
    </tr>


<tr>
      <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">5</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">4</td>
    </tr>
    <tr>
      <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">4</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg.  Horizontal ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">10</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg . Plain book</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">3</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">8</td>
    </tr>
    
<tr>
      <td style=" padding-left: 1%;">50 Pg. . Single Ruled interleaf</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">2</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg. Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">2</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">50 Pg.Single Ruled</td>
      <td style="text-align: center;">Note book</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Diary</td>
      <td style="text-align: center;">Diary</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">File</td>
      <td style="text-align: center;">File</td>
      <td style="text-align: center;">1</td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">SAPA Book Kit</td>
      <td style="text-align: center;">NA</td>
      <td style="text-align: center;"></td>
    </tr>
<tr>
      <td style=" padding-left: 1%;">Robotics & Coading</td>
      <td style="text-align: center;">NA</td>
      <td style="text-align: center;"></td>
    </tr>
    <tr>
      <td colspan="3" style=" padding-left: 1%;"><strong> Inclusions in the the School Book Kit:</strong> <br> • All required textbooks for each subject <br> • Classwork notebooks for every subject <br> • Worksheets for regular practice and assessments <br>  • Robotics & Coding materials (To applicable classes) <br> • Activity books (Applicable as per curriculum) <br> • School ERP & Parent App. <br> <strong> Exclusions from the School Book Kit:</strong>
        <br>• Personal stationery items (e.g., school bag, pencil box, etc.) <br> • Additional reference books (optional books for extra learning) <br> • School ID card
      </td>
    </tr>
   </table>';


         
switch ($class) {
    case 'Nursery':
        $html = $nursery;
        break;
    case 'LKG':
        $html = $lkg;
        break;
    case 'UKG':
        $html = $ukg;
        break;
    case 'Grade-1':
        $html = $g1;
        break;
    case 'Grade-2':
        $html = $g2;
        break;
    case 'Grade-3':
        $html = $g3;
        break;
    case 'Grade-4':
        $html = $g4;
        break;
    case 'Grade-5':
        $html = $g5;
        break;
    case 'Grade-6':
        $html = $g6;
        break;
    case 'Grade-7':
        $html = $g7;
        break;
    default:
        $html = '<p><strong>Note : </strong><span>Cheques accepted subject to realisation</span></p>'; 
        break;
}
return $html;

  }

  public function getIndianCurrency(float $number)
  {
    $schoolName = $this->settings->getSetting('school_short_name');
    $decimal = round($number - ($no = floor($number)), 2) * 100;
    $hundred = null;
    $digits_length = strlen($no);
    $i = 0;
    $str = array();
    $words = array(0 => '', 1 => 'one', 2 => 'two',
        3 => 'three', 4 => 'four', 5 => 'five', 6 => 'six',
        7 => 'seven', 8 => 'eight', 9 => 'nine',
        10 => 'ten', 11 => 'eleven', 12 => 'twelve',
        13 => 'thirteen', 14 => 'fourteen', 15 => 'fifteen',
        16 => 'sixteen', 17 => 'seventeen', 18 => 'eighteen',
        19 => 'nineteen', 20 => 'twenty', 30 => 'thirty',
        40 => 'forty', 50 => 'fifty', 60 => 'sixty',
        70 => 'seventy', 80 => 'eighty', 90 => 'ninety');
    $digits = array('', 'hundred','thousand','lakh', 'crore');
    while( $i < $digits_length ) {
        $divider = ($i == 2) ? 10 : 100;
        $number = floor($no % $divider);
        $no = floor($no / $divider);
        $i += $divider == 10 ? 1 : 2;
        if ($number) {
            $plural = (($counter = count($str)) && $number > 9) ? '' : null;
            $hundred = ($counter == 1 && $str[0]) ? ' and ' : null;
            $str [] = ($number < 21) ? $words[$number].' '. $digits[$counter]. $plural.' '.$hundred:$words[floor($number / 10) * 10].' '.$words[$number % 10]. ' '.$digits[$counter].$plural.' '.$hundred;
        } else $str[] = null;
    }
    $Rupees = implode('', array_reverse($str));
    $paise = ($decimal) ? "." . ($words[$decimal / 10] . " " . $words[$decimal % 10]) . ' Paise' : '';
    if ($schoolName === 'prarthana') {
      return 'Rupees ' . ( $Rupees ? $Rupees . 'Only ' : ' ') . $paise ;
    }
    return ($Rupees ? $Rupees . 'Rupees ' : '') . $paise ;
  }

 
  public function fee_receipt_template(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['main_content'] = 'feesv2/receipt_template';
    $this->load->view('inc/template', $data);
  }
  public function get_receipt_template(){
    $fee_type = $_POST['fee_type'];
    echo $this->fees_student_model->get_receipt_templatebyId($fee_type);
  }
  public function insert_fee_receipt_template(){
    $result = $this->fees_collection_model->fee_receipt_template_insert();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Update successfully');
      redirect('feesv2/fees_collection/fee_receipt_template');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong');
      redirect('feesv2/fees_collection/fee_receipt_template');
    }
  }


  public function mark_verified($date, $settlement_id_json){
    $date = date("Y-m-d", strtotime($date));
    $name = $this->authorization->getAvatarFriendlyName(); 
    $result = $this->fees_collection_model->mark_verified($date, $settlement_id_json, $name);
    if($result)
      $this->session->set_flashdata('flashSuccess', 'Settlement has been added to verified list.');
    redirect('feesv2/fees_collection/transactions_settlements', 'refresh');
  }

  public function receipt_pdf_download($id){ 
    $link = $this->parent_model->download_fee_receipt($id);
    $details = $this->parent_model->download_fee_receipt_file_name($id);
    if(!empty($details)){
      $file_name = $details->student_name . ' (' . $details->receipt_number . ') fee_reciept.pdf';
    } else {
      $file_name = 'fee_reciept.pdf';
    }
    $url = $this->filemanager->getFilePath($link);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download($file_name, $data, TRUE);
  }

  public function receipt_pdf_download_view_popup(){ 
    $transId  = $_POST['transId'];
    $link =$this->parent_model->download_fee_receipt($transId);
    echo $this->filemanager->getFilePath($link);
  }


  public function re_generate_pdf(){
    $data['fee_types'] = $this->fees_student_model->get_fee_types_all();
    $blueprint_id = $this->input->post('blueprint_id');
 
    if (empty($blueprint_id))
      $data['selectedBP'] = $data['fee_types'][0]->id;
    else
      $data['selectedBP'] = $blueprint_id;

    // $data['blueprint_id'] = $blueprint_id;  
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    // $data['studentNames'] = $this->Student_Model->getstudentNames();

    $data['main_content'] = 'feesv2/re_generate_pdf/index';
    $this->load->view('inc/template', $data);
  }

  public function student_fee_pdf_receipt(){
    $mode = $_POST['mode'];
    $stdData = $this->fees_collection_model->search_std_class_wise_fee_transcation($mode);
  
    echo json_encode($stdData);
  }

  public function generate_pdf_fee_receipt(){
    $checked_transids = $_POST['checked_transids'];   
    foreach ($checked_transids as $key => $transIds) {
      $fee_trans = $this->fees_collection_model->get_fee_transcation_for_receipt($transIds);
      $created_name = $this->avatar->getAvatarById($fee_trans->collected_by);
      $parent_name = $this->fees_collection_model->get_created_online_parent_name($fee_trans->collected_by,$fee_trans->transaction_mode);
      $template = $this->fees_collection_model->get_fee_template_by_blueprint_id($fee_trans->no_of_ins->feev2_blueprint_id);
      $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($fee_trans->no_of_ins->feev2_blueprint_id);
      $payment_modes = json_decode($blue_print->allowed_payment_modes);
      $online_tranxId = $this->fees_collection_model->get_online_tranxId($transIds);
      if($fee_trans->payment_type == 777){
        $online_tranxId = $this->fees_collection_model->get_online_challen_tranxId($transIds);
      }
      $result = $this->_create_template_fee_counter_amount($fee_trans, $template, $payment_modes, $created_name, $parent_name, $online_tranxId);
      if(!empty($result)){
        $update =  $this->fees_collection_model->update_html_receipt($result, $transIds);
        if($update) {
          $this->__generatefee_pdf_receipt($result, $transIds, $blue_print->receipt_for, $blue_print->receipt_pdf_page_alignment);
        }
      }
     
    }
    echo 1;
  }


  public function reconsilation_failed(){
    $feeId = $_POST['feeId'];
    $remarks = $_POST['remarks'];
    $cohort_student_id = $_POST['cohort_student_id'];
    echo $this->fees_collection_model->reconsilation_failed_update($feeId,$remarks, $cohort_student_id);
  }

  public function check_consolidate_pdf_status(){
    $stdSchId = $_POST['stdSchId'];
    echo $this->fees_collection_model->check_consolidate_pdf_statusbySchId($stdSchId);
  }
  public function get_full_receipt_pdf(){
    $stdSchId = $_POST['stdSchId'];
    $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_full_receipt($stdSchId);
    $template = $this->fees_collection_model->get_fee_consolidated_template_by_blueprint_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);
    if ($template) {
      $result = $this->_create_template_fee_consolidated($data['fee_trans'],$template, 0, 0);
    }
    $update =  $this->fees_collection_model->update_consolidate_html_receipt($result, $stdSchId);
    if($update) {
      return $this->__generatefee_pdf_consolidated_receipt($result, $stdSchId);
    }
    
  }

  public function _create_template_fee_consolidated($fee_trans, $template)
  {
    $medium = $this->settings->getSetting('medium')[$fee_trans->student->medium];
    $concessionColumn = $this->settings->getSetting('fee_receipt_concession_column');
    $class = $fee_trans->student->classSection;
    if ($fee_trans->student->is_placeholder == 1) {
      $class = $fee_trans->student->clsName;
    }
    $template = str_replace('%%receipt_no%%',$fee_trans->receipt_number, $template);
    $template = str_replace('%%class%%',$class, $template);
    $template = str_replace('%%class_medium%%',$class.'/'.$medium, $template);
    $template = str_replace('%%transaction_date%%', date('d-m-Y', strtotime($fee_trans->paid_datetime)), $template);
    $template = str_replace('%%f_number%%',$fee_trans->student->mobile_no, $template);
    $template = str_replace('%%student_name%%', $fee_trans->student->stdName, $template);
    $template = str_replace('%%father_name%%', $fee_trans->student->fName, $template);
    $template = str_replace('%%mother_name%%', $fee_trans->student->mName, $template);
    $template = str_replace('%%admission_no%%', $fee_trans->student->admission_no, $template);
    $template = str_replace('%%academic_year%%', $this->acad_year->getAcadYearById($fee_trans->no_of_comp->acad_year_id), $template);
    $i=1;
    $totalAmount = 0;
    $header_part = '<table>';
    $header_part .= '<tr>';
    $header_part .= '<th width="10%">#</th>';
    $header_part .= '<th>Particulars</th>';
    $header_part .= '<th>Amount</th>';
    if ($concessionColumn && $fee_trans->no_of_ins->total_concession_amount_paid !=0) {
      $header_part .= '<th>Concession</th>';
      $header_part .= '<th>Amount paid</th>';
    }
    $header_part .= '</tr>';
    $component_part = '';
    $concessionTotal = 0;
    $totalAmountPaid = 0;
    foreach ($fee_trans->comp as $key => $trans) {      
      $totalAmount += $trans->amount_paid + $trans->concession_amount;
      $totalAmountPaid += $trans->amount_paid;
      $concessionTotal += $trans->concession_amount;
      $component_part.='<tr>';
      $component_part.='<td>'.$i++.'</td>';     
      $component_part.='<td>'.$trans->compName.'</td>';
      $component_part.='<td>'.($trans->amount_paid + $trans->concession_amount).'</td>';
      if ($concessionColumn && $fee_trans->no_of_ins->total_concession_amount_paid !=0) {
        $component_part.='<td>'.$trans->concession_amount.'</td>';
        $component_part.='<td>'.$trans->amount_paid.'</td>';
      }
      $component_part.='</tr>';
    }

    $cols=0;
     if ($fee_trans->concession_amount != 0) {
      $cols=3;
    } 

    $footer_part = '<tr>';
    $footer_part.='<td colspan="2" style="text-align: right;">Total Fee</td>';
    $footer_part.='<td>'.$totalAmount.'</td>'; 
    if ($concessionColumn && $fee_trans->no_of_ins->total_concession_amount_paid !=0) {
      $footer_part.='<td>'.'(-) '.$concessionTotal.'</td>'; 
      $footer_part.='<td>'.$totalAmountPaid.'</td>'; 
    }
    $footer_part.='</tr>';
    
    if($concessionColumn == false){
      if($fee_trans->no_of_ins->total_concession_amount_paid != 0) {
        $footer_part.='<tr>';
        $footer_part.='<td colspan="2" style="text-align:right;">Concession (-)</td>';
        $footer_part.='<td>'.$fee_trans->no_of_ins->total_concession_amount_paid.'</td>';
        $footer_part.='</tr>';
      }
    }

    if($fee_trans->no_of_ins->discount != 0) {
      $footer_part.='<tr>';
      $footer_part.='<td colspan="2" style="text-align:right;">Discount (-)</td>';
      $footer_part.='<td colspan="'.$cols.'">'.$fee_trans->no_of_ins->discount.'</td>';
      $footer_part.='</tr>';
    }

    if($fee_trans->no_of_ins->total_fine_amount != 0) {
      $footer_part.='<tr>';
      $footer_part.='<td colspan="2" style="text-align:right;">Late Fee</td>';
      $footer_part.='<td colspan="'.$cols.'">'.$fee_trans->no_of_ins->total_fine_amount.'</td>';
      $footer_part.='</tr>';
    }

    if($fee_trans->no_of_ins->total_card_charge_amount != 0) {
      $footer_part.='<tr>';
      $footer_part.='<td colspan="2" style="text-align:right;">Card Charge Amount</td>';
      $footer_part.='<td colspan="'.$cols.'">'.$fee_trans->no_of_ins->total_card_charge_amount.'</td>';
      $footer_part.='</tr>';
    }

    $footer_part.='<tr>';
    $footer_part.='<td colspan="2" style="text-align:right;border: solid 1px #474747;"><strong>Fee Paid</strong></td>';
    $footer_part.='<td colspan="'.$cols.'" style="border: solid 1px #474747;">'.$fee_trans->no_of_ins->total_fee_paid.'</td>';
    $footer_part.='</tr>';

    $footer_part .= '</table>';
    $dynamic_part = $header_part.$component_part.$footer_part;

    $amountInWords = $this->getIndianCurrency($fee_trans->no_of_ins->total_fee_paid);

    $template = str_replace('%%installements%%',$dynamic_part, $template);
    $template = str_replace('%%rupees_in_words%%',ucwords($amountInWords), $template);
    return $template;
  }

  private function __generatefee_pdf_consolidated_receipt($html, $stdSchId) {
    $school = CONFIG_ENV['main_folder'];
    $path = $school.'/fee_reciepts/'.uniqid().'-'.time().".pdf";

    $bucket = $this->config->item('s3_bucket');

    $status = $this->fees_collection_model->updateConsolidateFeePath($stdSchId, $path);
    $page = 'portrait';
    $page_size = 'a4';
    $curl = curl_init();
    $postData = urlencode($html);
    $username = CONFIG_ENV['job_server_username'];
    $password = CONFIG_ENV['job_server_password'];
    $return_url = site_url().'Callback_Controller/updateConsolidatedFeePdfLink';

    curl_setopt_array($curl, array(
        CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERPWD => $username . ":" . $password,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
        CURLOPT_HTTPHEADER => array(
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Content-Type: application/x-www-form-urlencoded",
            "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
        ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);
  }

  public function get_full_receipt_nhis($stdSchId){
    $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_full_receipt($stdSchId);
    $data['main_content'] = 'feesv2/receipts/northhill_full';
    $this->load->view('inc/template', $data);
  }

  public function consolidated_receipt_pdf_download($stdSchId){ 
    $link = $this->fees_collection_model->download_consolidated_fee_receipt($stdSchId);
    $url = $this->filemanager->getFilePath($link);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download('fee_reciept.pdf', $data, TRUE);
  }

  public function receipt_view_history(){
    $transId =  $_POST['transId'];
    $result = $this->fees_collection_model->fee_receipt_view_by_trans_id($transId);
    echo json_encode($result);
  }

  public function adjust_amount($student_id, $cohort_student_id, $std_sch_id){

    $data['student_id'] = $student_id;
    $data['cohort_student_id'] = $cohort_student_id;
    $data['std_sch_id'] = $std_sch_id;

    $data['blue_print'] = $this->fees_collection_model->ge_blueprint_by_id($cohort_student_id);
    $data['student'] = $this->fees_student_model->get_std_detailsbyId($student_id,$data['blue_print']->acad_year_id);

    $data['blueprint_id'] = $data['blue_print']->id;
    $filter_columns = json_decode($data['blue_print']->display_fields);
    $data['filters'] = $this->_student_filter_columns($filter_columns, $data['student']);

    $data['adjust_amount'] = $this->fees_collection_model->fee_previous_adjust_amount($std_sch_id);
    // echo "<pre>"; print_r($data['concession_amount']); die();
    $data['main_content'] = 'feesv2/transaction/collection/adjust_amount';
    $this->load->view('inc/template', $data);

  }

  public function adjustment_update(){
    $input = $this->input->post();

    $result = $this->fees_student_model->update_adjustment_amount($input['std_sch_id'],$input['adjustment_amount'],$input['cohort_student_id'],$input['feev2_blueprint_installment_types_id'],$input['adjustment_name'],$input['adjustment_amount_add']);

    $total_adjust = 0;
    foreach ($input['adjustment_amount'] as $key => $val) {
      $total_adjust += array_sum($val);
    }
    $auditDesc = 'Adjustment amount '.$total_adjust;
    $this->fees_student_model->insert_fee_audit($input['student_id'], 'Adjustment', $auditDesc, $this->authorization->getAvatarId(), $input['blueprint_id']);

    $this->session->set_userdata('student_id', $input['student_id']);
    $this->session->set_userdata('cohort_student_id',$input['cohort_student_id']);
    $this->session->set_userdata('std_sch_id',$input['std_sch_id']);
    $this->session->set_userdata('blueprint_id',$input['blueprint_id']);

    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Adjustment Amount applied');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    }
    redirect('feesv2/fees_collection/fee_collect');
  }

  public function get_siblings_data(){
    $stdId = $_POST['stdId'];
    $result = $this->fees_student_model->get_siblings_data_by_id($stdId);
    echo json_encode($result);
  }

  public function get_fine_amount_details_byid(){
    $std_sch_id = $_POST['std_sch_id'];
    $result = $this->fees_student_model->get_fine_amount_details_byschId($std_sch_id);
    echo json_encode($result);
  }

  public function insert_fine_amount_counter(){
    $input = $this->input->post();
    
    $result = $this->fees_collection_model->insert_fine_amount_data();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Fine Amount applied');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    }

    $this->session->set_userdata('student_id', $input['student_id']);
    $this->session->set_userdata('cohort_student_id',$input['cohort_student_id']);
    $this->session->set_userdata('std_sch_id',$input['fine_sch_id']);
    $this->session->set_userdata('blueprint_id',$input['blueprint_id']);

    redirect('feesv2/fees_collection/fee_collect');
    
  }

  public function redrive_fees_online_transaction(){
    $this->__handle_redrive_online_response($_POST);
  }

  private function __handle_redrive_online_response($response){
    if ($response['transaction_status'] === 'SUCCESS') {
      if (strpos($response['source_id'], '[') === 0) {
          // If source_id is already in array format
        $sourceIds = json_decode($response['source_id'], true);
      } else {
          // If source_id is a single value, create an array with that value
        $sourceIds = [$response['source_id']];
      }     
      foreach ($sourceIds as $key => $source_id) {
        // if receipt is already generated, we have already updated the transaction and schedule table. No need to update again. Just show the already generated receipt.
				$is_receipt_generated = $this->fees_collection_model->is_receipt_generated($source_id);
        if ($is_receipt_generated){
          $this->fees_collection_model->update_transcation_status($source_id, 'SUCCESS');
          $this->session->set_flashdata('flashError', 'Fee receipt alreday generated');
          redirect('payment_controller/online_transaction_report');
        }
      // generate and update receipt number after transcation
        $result =  $this->fees_collection_model->update_redrive_trans_student_all_table($source_id);
			}

      if (!$result) {
        $this->session->set_flashdata('flashError', 'Something went wrong. Will get back to you shortly');
      } else {
        $this->session->set_flashdata('flashSuccess', 'Transcation successful');
        foreach ($sourceIds as $key => $source_id) {
          $this->_redrive_generate_pdf_receipt_template($source_id, $response['transaction_id'], $response['transaction_date'], $response['transaction_time']);
        }
      }
    } else {
      //Online payment failed
      foreach ($sourceIds as $key => $source_id) {
        $this->fees_collection_model->update_transcation_status($source_id, 'FAILED');
			}
      $this->session->set_flashdata('flashError', 'Something went wrong. Will get back to you shortly');
    }
    redirect('payment_controller/online_transaction_report');
  }

  private function _redrive_generate_pdf_receipt_template($fTrans, $transaction_id, $transaction_date, $transaction_time){
    $this->fees_collection_model->create_pdf_template_for_fee_receipts($fTrans, $transaction_id, $transaction_date, $transaction_time);
  }

  public function auto_redrive_handle_online_transaction($response){
    
    $is_receipt_generated = $this->fees_collection_model->is_receipt_generated($response['source_id']);

    if ($is_receipt_generated == false){
      $this->fees_collection_model->update_redrive_trans_student_all_table($response['source_id']);
      $this->_redrive_generate_pdf_receipt_template($response['source_id'], $response['transaction_id'], $response['transaction_date'], $response['transaction_time']);
    }

  }

  public function fee_student_blueprints_v1($std_id) {  
    $data['std_id'] = $std_id;
    $data['student'] = $this->fees_student_model->get_fees_std_detailsbyId($std_id,$this->acad_year->getAcadYearId());
    $data['previous_balance'] =  $this->fees_student_model->check_previous_balance_amount($std_id);
    
    $data['invoices_pdf_status'] = $this->fees_student_model->get_invoices_pdf_status($std_id,'Invoice');
    $data['invoices_statement_pdf_status'] = $this->fees_student_model->get_invoices_pdf_status($std_id,'Statement');
    // $data['invoice_email'] =$this->fees_student_model->check_invoice_email_send($std_id);
    $excess_amount = $this->fees_collection_model->get_over_all_additional_amount($std_id);
    $current_excess_amount = $this->fees_collection_model->get_current_additional_amount($std_id);

    $current_acad_year  = $this->acad_year->getAcadYearId();
    $acadyearSetting =  $this->settings->getSetting('academic_year_id');
    if($current_acad_year != $acadyearSetting){
      $data['excess_amount'] = $excess_amount;
    }else{
      $data['excess_amount'] = $current_excess_amount;
    }
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fast_fee_payment_modes'] = $this->settings->getSetting('fast_fee_collection_payment_modes');
    if($this->settings->getSetting('enable_indus_single_window_approval_process')){
      $data['single_window_data'] = $this->fees_student_model->get_single_window_data($std_id);
    }
    $data['delete'] = $this->settings->getSetting('fees_enable_online_payment_delete');
    $data['std_data'] = $this->fees_student_model->get_stud_data($std_id);
    $data['refund'] = $this->authorization->isAuthorized('FEESV2.REFUND');
    $data['main_content'] = 'feesv2/transaction/fees_indexv1';
    $this->load->view('inc/template', $data);
  }

  public function get_admission_enquiry_remarks_data(){
    $std_id = $_POST['std_id'];
    $admission = $this->Student_Model->get_admission_form_remarksbyStudentId($std_id);
    $enquiry = $this->Student_Model->get_enquiry_form_remarksbyStudentId($std_id);
    echo json_encode(array('admission_remarks'=>$admission, 'enquiry_remarks'=>$enquiry));
  }

  public function get_admission_offers_data(){
    $std_id = $_POST['std_id'];
    $admission_offers = $this->fees_student_model->get_admission_offer_byStudentId($std_id);
    echo json_encode($admission_offers);
  }

  public function get_inventory_expense_offers_data(){
    $std_id = $_POST['std_id'];
    $this->load->model('procurement/Sales_model_v2');

    $bookId = $this->db->select('pic.id as book_id')
		->from('procurement_itemmaster_category pic')
		->where('pic.category_name','Books')
		->get()->row();
    $total_books_amount = 0;
    if($bookId){
      $books = $this->Sales_model_v2->get_report_of_a_student_latest($std_id, $bookId->book_id, 0, 2);
      if(!empty($books) && $books['final_arr']){
        $final_arr = $books['final_arr'];
        foreach ($final_arr as $item) {
            $amount = isset($item->amount) ? (float)$item->amount : 0;
            $symbol = isset($item->symbol) ? $item->symbol : '';
            if ($symbol === '+') {
                $total_books_amount += $amount;
            } elseif ($symbol === '-') {
                $total_books_amount -= $amount;
            }
        }
      }
    }
    
    $UniformId = $this->db->select('pic.id as uniform_id')
		->from('procurement_itemmaster_category pic')
		->where('pic.category_name','Uniforms')
		->get()->row();
    $total_uniform_amount = 0;
    if($UniformId){
      $uniforms = $this->Sales_model_v2->get_report_of_a_student_latest($std_id, $UniformId->uniform_id, 0, 2);
      if(!empty($uniforms) && $uniforms['final_arr']){
        $final_arr = $uniforms['final_arr'];
        foreach ($final_arr as $item) {
            $amount = isset($item->amount) ? (float)$item->amount : 0;
            $symbol = isset($item->symbol) ? $item->symbol : '';
            if ($symbol === '+') {
              $total_uniform_amount += $amount;
            } elseif ($symbol === '-') {
              $total_uniform_amount -= $amount;
            }
        }
      }
    }
    $other_expenses = (array) $this->fees_collection_model->get_other_expenses_data($std_id, $total_books_amount, $total_uniform_amount);
    echo json_encode($other_expenses);
  }

  public function get_all_fee_blueprints(){
    $std_id= $_POST['student_id'];
    
    $fee_blueprints = $this->fees_student_model->get_all_blueprint_fees_details($std_id);
    // $fee_blueprints = $this->fees_student_model->get_blueprintsv1();
    // $isConcessionApprovedFlowTrue = $this->settings->getSetting('fees_concession_approval_flow');
    // foreach ($fee_blueprints as $key => $fbp) {
    //   $fbp->std_fee_details = $this->fees_student_model->get_assigned_student_fee_details($std_id, $fbp->id);   
    //   $fbp->reconciliation = $this->fees_collection_model->check_reconcilation_status($std_id, $fbp->id);
    //   $fbp->opening_balance = $this->fees_collection_model->fees_student_opening_bal($std_id, $fbp->id);
    //   $fbp->isConcessionPending = 0;
    //   if($isConcessionApprovedFlowTrue){
    //     $fbp->isConcessionPending = $this->fees_collection_model->check_isConcession_approved_pending($std_id, $fbp->id);
    //   }
    //   // $fbp->blueprint_alog = '';
    //   // if(!empty($fbp->std_fee_details->std_sch_id)){
    //   //   $fbp->blueprint_alog = $this->fees_collection_model->get_fee_blueprint_alog($fbp->std_fee_details->std_sch_id, $student->class);
    //   // }
    // }
    echo json_encode(array('fee_blueprints'=>$fee_blueprints));
  }

  function get_discount_amount_alog(){
    $student_id = $_POST['student_id'];
    $sch_id = $_POST['sch_id'];
    $student = $this->fees_student_model->get_fee_student_class_id($student_id);
    $result =array();
    if(!empty($student)){
      $result = $this->fees_collection_model->get_fee_blueprint_alog($sch_id, $student->class_id);
    }
    echo json_encode($result);
    
  }

  public function get_fee_cohort_confirm_data(){
    $bpId = $_POST['bpId'];
    $student_id = $_POST['student_id'];
    $student = $this->fees_student_model->get_std_detailsbyId($student_id,$this->acad_year->getAcadYearId());
    $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($bpId);
    $filter_columns = json_decode($blue_print->display_fields);
    $filters = [];
    if (!empty($filter_columns)) {
      $filters = $this->_student_filter_columns($filter_columns, $student);
    }
    $displayRemarks = 0;
    if(!empty($blue_print->enable_remarks)){
      $displayRemarks = 1;
    }
    $cohort_details = $this->fees_collection_model->get_all_cohorts_filter($bpId);

    $cohort_id = $this->fees_student_model->determine_cohort($bpId, $student);

    $installments_types = $this->fees_collection_model->get_installment_types($bpId);

    echo json_encode(array('student'=>$student,'filters'=>$filters,'cohort_details'=>$cohort_details,'installments_types'=>$installments_types,'cohort_id'=>$cohort_id,'displayRemarks'=>$displayRemarks));
  }

  public function fee_collect_v1(){
    $input = $this->input->post();
    if (empty($input['student_id'])) {
      redirect('feesv2/fees_collection');
    }
    $data['student_id'] = $input['student_id'];
    $data['std_sch_id'] = $input['std_sch_id'];
    $data['cohort_student_id'] = $input['cohort_student_id'];
    $data['blueprint_id'] = $input['blueprint_id'];
    //Get the student details and the fee filtercolumns
    $data['blue_print'] = $this->fees_collection_model->ge_blueprint_details_by_id($input['blueprint_id']);
    
    $data['std_cohort_status'] = $this->fees_collection_model->get_std_fee_cohort_status($input['cohort_student_id']);
   
    //Get the payment modes and sort them
    $data['payment_modes'] = json_decode($data['blue_print']->allowed_payment_modes);
    // usort($data['payment_modes'], function($a, $b) { return strcasecmp($a->name, $b->name); });

    $data['student'] = $this->fees_student_model->get_std_detailsbyId($input['student_id'],$data['blue_print']->acad_year_id);
    // echo "<pre>"; print_r($data['student']);die();
    $filter_columns = json_decode($data['blue_print']->display_fields);
    $data['filters'] = $this->_student_filter_columns($filter_columns, $data['student']);
    $data['total_fee_amount'] = $this->fees_collection_model->get_std_total_fee_amount($input['std_sch_id']);
    //Get the blueprint, balance installment and fee details
    $installmentsType = $this->fees_collection_model->get_fee_installments_all($input['std_sch_id']);
    foreach ($installmentsType as $key => $types) {
      if ($types->status !='FULL') {
        $fee_amount = $this->fees_collection_model->get_std_fee_amount($input['std_sch_id'], $types->fiInsId);
        break;
      }
    }
    if (empty($fee_amount)) {
      redirect('feesv2/fees_collection/fee_student_blueprints_v1/'.$data['student_id']);
    }
    $data['noOfComponents'] = count($fee_amount);

    $data['no_of_installments'] = count($installmentsType);
    // echo "<pre>"; print_r($data['no_of_installments']); die();
    
    $data['selectedIns'] = $fee_amount[0]->fsInsId;
   
    $data['fee_amount'] = $fee_amount;

    $data['installments'] = $installmentsType;
    $data['card_charge_amount'] = json_encode($this->settings->getSetting('fee_payment_card_charge_amount'));
    $data['selection_installment'] = $this->settings->getSetting('fee_installment_selection');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['discount_calucate_based_balance'] = $this->settings->getSetting('discount_calucate_based_balance');
    //TODO: discount, fine amount, etc.
    $data['blueprint_alog'] = $this->fees_collection_model->get_fee_blueprint_alog($input['std_sch_id'],  $data['student']->class);
    $data['additionalAmount'] = $this->fees_collection_model->get_current_additional_amount($input['student_id']);
    // echo "<pre>"; print_r($data['additionalAmount']); die();
    $data['main_content'] = 'feesv2/transaction/collectionv1/fee_collect';
    $this->load->view('inc/template', $data);
  }

  public function switch_fees_publish(){
    $stngId = $_POST['stngId'];
    $value = $_POST['value'];
    $this->fees_collection_model->store_fees_edit_history($_POST['student_id'],$_POST['blueprint_name'].' '.$value);
    echo $this->fees_collection_model->update_switch_fees_publish($stngId,$value); 
  }

  public function switch_fees_online_publish(){
    $stngId = $_POST['stngId'];
    $value = $_POST['value'];
    $this->fees_collection_model->store_fees_edit_history($_POST['student_id'],$_POST['blueprint_name'].' Online '.$value);
    echo $this->fees_collection_model->update_switch_fees_online_publish($stngId,$value); 
  }

  public function reset_confirm_data_v1(){
    $student_id = $_POST['student_id'];
    $cohort_student_id = $_POST['cohort_student_id'];
    $blueprint_id = $_POST['blueprint_id'];
    $this->fees_collection_model->store_fees_edit_history($student_id,$_POST['blueprint_name'].' Fees is Un-Assigned');
    $result = $this->fees_collection_model->reset_confirm_student_cohort_data($cohort_student_id);
    if ($result) {
      $auditDesc = 'Reset Fee Structure';
      $this->fees_student_model->insert_fee_audit($student_id, 'Reset Fee Structure', $auditDesc, $this->authorization->getAvatarId(), $blueprint_id);
    }
    echo $result;
  }


  public function submit_feev1(){  
    $input = $this->input->post();
    $this->fees_collection_model->store_fees_edit_history($input['student_id'],$input['total_component_amount_check'].'Rs.'.' Fees is Collected');
    $this->db->trans_begin();

    $fTrans = $this->fees_collection_model->insert_fee_transcation($input);
    if (empty($fTrans)) 
    {
      $this->db->trans_rollback();
    }
    else
    {
      // generate and update receipt number after transcation
      $this->fees_collection_model->update_receipt_transcation_wise($fTrans);
      // Check Reconcilication Status if 1 pending... not allowed updated student schedule table
      $rconStatus = $this->fees_collection_model->lastFeeTranscationReconCheck($fTrans);
      if ($rconStatus->reconciliation_status == 1) {
        $this->db->trans_commit();
        // echo json_encode(array('message'=>'Fee collected Successfully. Reconcilication pending','transId'=>$fTrans));
        // return false;
        $this->session->set_flashdata('flashInfo', 'Fee collected Successfully. Reconcilication pending');

        echo json_encode(array('message'=>'Fee collected Successfully. Reconcilication pending','transId'=>$fTrans));
        return false;
        // redirect('feesv2/fees_collection/fee_reciept/'.$fTrans);
      }

      $result =  $this->fees_collection_model->update_student_schedule_all_table($fTrans);
      if (empty($result)) 
      {
        $this->db->trans_rollback();
      }
      if ($this->db->trans_status()) 
      {
        $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($input['blueprint_id']);

        $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);

        if ($blue_print->enable_sms == TRUE) {
          $this->__sms_fees_payment_counter($data['fee_trans']->amount_paid, $data['fee_trans']->student->stdName, $data['fee_trans']->student->stdId, $blue_print->name);  
        }
        $this->db->trans_commit();
        echo json_encode(array('message'=>'Fee collected Successfully','transId'=>$fTrans));
        return false;
      }
      else
      {
        echo json_encode(array('message'=>'Something went wrong','transId'=>''));
        return false;
      }
    } 
  }

  public function fee_recieptv1($fTrans){
    $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
    $data['created_name'] = $this->avatar->getAvatarById($data['fee_trans']->collected_by);
    $parent_name = $this->fees_collection_model->get_created_online_parent_name($data['fee_trans']->collected_by,$data['fee_trans']->transaction_mode);
    $template = $this->fees_collection_model->get_fee_template_by_blueprint_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);

    $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);

    $payment_modes = json_decode($blue_print->allowed_payment_modes);
    if ($template) {
      $result = $this->_create_template_fee_counter_amount($data['fee_trans'],$template, $payment_modes, $data['created_name'],$parent_name);
    }
    if (!empty($result)) {
      $update =  $this->fees_collection_model->update_html_receipt($result, $fTrans);
      if($update) {
        $this->__generatefee_pdf_receipt($result, $fTrans, $blue_print->receipt_for, $blue_print->receipt_pdf_page_alignment);
      }
    }
    redirect('feesv2/fees_collection/fee_reciept_viewv1/'.$fTrans); 
  }

  public function fee_reciept_viewv1($fTrans, $canceled = ''){
    $data['cancel'] = $canceled;
    // $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
    $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
    $data['created_name'] = $this->avatar->getAvatarById($data['fee_trans']->collected_by); // Staff
    $data['parent_name'] = $this->fees_collection_model->get_created_online_parent_name($data['fee_trans']->collected_by,$data['fee_trans']->transaction_mode); // get parent name
    $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);

    $data['payment_modes'] = json_decode($blue_print->allowed_payment_modes);
    $data['receipt_for'] = $blue_print->receipt_for;
    // $receipt_for = $this->settings->getSetting('receipt_for');
    $data['main_content'] = 'feesv2/receiptv1/header';
    $this->load->view('inc/template_fee', $data);   
  }

  public function insert_fine_amount_counterv1(){
    $input = $this->input->post();
    $this->fees_collection_model->store_fees_edit_history($_POST['student_id'],$input['fine_amount'].'Rs.'.' Fine Amount is added');
    $result = $this->fees_collection_model->insert_fine_amount_data();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Fine Amount applied');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    }

    $this->session->set_userdata('student_id', $input['student_id']);
    $this->session->set_userdata('cohort_student_id',$input['cohort_student_id']);
    $this->session->set_userdata('std_sch_id',$input['fine_sch_id']);
    $this->session->set_userdata('blueprint_id',$input['blueprint_id']);

    redirect('feesv2/fees_collection/fee_collect');
    
  }

  public function add_additional_amount_details(){
    $student_id = $_POST['student_id'];
    $additional_amount = $_POST['additional_amount'];
    $additional_amount_remarks = $_POST['additional_amount_remarks'];
    $paymentmodes = $_POST['paymentmodes'];
    $excess_bank_name = $_POST['excess_bank_name'];
    $excess_branch = $_POST['excess_branch'];
    $excess_cheque_dd_nb_cc_dd_number = $_POST['excess_cheque_dd_nb_cc_dd_number'];
    $excess_bank_date = $_POST['excess_bank_date'];
    $excess_amount_created_date = $_POST['excess_amount_created_date'];
    if ($additional_amount !=0) {
      $last_insert_id =  $this->fees_collection_model->insert_additional_amount_details($student_id, $additional_amount, $additional_amount_remarks, $paymentmodes, $excess_bank_name, $excess_branch, $excess_cheque_dd_nb_cc_dd_number, $excess_bank_date, $excess_amount_created_date);
      if($last_insert_id != 0){
        $this->fees_collection_model->update_excess_amount_receipt_number($last_insert_id);
      }
      echo 1;
    }else{
      echo 0;
    }
  }

  public function get_additional_amount_details(){
    $student_id = $_POST['student_id'];
    $result = $this->fees_collection_model->get_additional_amount_detailsbyid($student_id);
    echo json_encode($result);
  }

  public function get_collection_additional_amount_details(){
    $student_id = $_POST['student_id'];
    $result = $this->fees_collection_model->get_collection_additional_amount_detailsbyid($student_id);
    echo json_encode($result);
  }

  public function send_fee_invoice_to_parent(){
    $student_admission_id= $_POST['student_admission_id'];
    $transverse= $_POST['transverse'];
    $invoice_type = $_POST['invoice_type'];
    $invoices = $this->fees_student_model->getFeeInvoiceStudentDetailsbyid($student_admission_id);
  
    $invoices_pdf_status = $this->fees_student_model->get_invoices_pdf_status($student_admission_id, $invoice_type);
    $filePath = $this->filemanager->getFilePath($invoices_pdf_status->invoice_path);
    $download ='<br><a target="_blank" rel="noopener noreferrer" href="'.$filePath.'">Fee receipts</a>';

    $email_content =$this->fees_collection_model->get_email_fee_invoice_pdf();
    if (empty($email_content)) {
      echo "Email template not found";
      exit();
    }
    $content = $email_content->content;
    $files_array = array();

    if($invoices_pdf_status->invoice_path != '') {
      array_push($files_array, array('name' => 'Fee Invoice.pdf', 'path' => $invoices_pdf_status->invoice_path));
    }

    if(!empty($files_array)) {
      $files_string = json_encode($files_array);
    }

    $memberEmail = [];
    $sender_list = [];
    foreach ($invoices as $key => $val) {
      $memberEmail[]['email'] = $val->email;
      $sender_list['students'] = [
        'send_to' => 'Both',
        'send_to_type' => $val->relation_type,
        'ids' => $val->std_id,
      ];
    }
    $email_master_data = array(
      'subject' => $email_content->email_subject,
      'body' => $content,
      'source' => 'Fee Invoice',
      'sent_by' => $this->authorization->getAvatarId(),
      'recievers' => 'Parents',
      'from_email' => $email_content->registered_email,
      'files' => ($files_string=='')?NULL:$files_string,
      'acad_year_id' => $this->acad_year->getAcadYearId(),
      'visible' => 1,
      'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
      'sending_status' => $transverse, // $transverse == 'Send Email' || 'Resend Email'
      'texting_master_id'=>$email_content->id
    );
    $this->load->model('communication/emails_model');
    $email_master_id = $this->emails_model->saveEmail($email_master_data);
    $this->fees_student_model->save_master_id_to_inovice($student_admission_id, $email_master_id, $invoices_pdf_status->std_invoice_id);

    $sent_data = array();
    $stakeholder_ids = (array)$student_admission_id;
    $sent_data = $this->fees_student_model->getStudents_for_email($stakeholder_ids, 'Both');
    $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
    $this->load->helper('email_helper');
    $email = $this->emails_model->getEmailInfo($email_master_id);
    $email_ids = [];
    foreach ($invoices as $key => $val) {
      if($val->email != '' && $val->email != null) {
        array_push($email_ids, $val->email);
      }
    }
    $members = [];
    if (!empty($email_content->members_email)) {
      $members = explode(',', $email_content->members_email);
    }
    $emailIds =  array_merge($email_ids, $members);
    echo sendEmail($email->body, $email->subject, $email_master_id, $emailIds, $email->from_email, json_decode($email->files));
  }

  public function excess_amount_refund_detail(){
    $bal_excess = $_POST['bal_excess'];
    $excess_id = $_POST['excess_id'];
    $remarks = $_POST['remarks'];
    echo $this->fees_collection_model->insert_excess_amount_refund_detail($bal_excess, $excess_id, $remarks);
  }

  public function excess_amount_delete(){
    $excess_id = $_POST['excess_id'];
    echo $this->fees_collection_model->delete_excess_amount_detail($excess_id);
  }

  public function fee_excess_reciept_view($excess_id){
    $data['excess_amount'] = $this->fees_collection_model->get_excess_amount_receipt_data($excess_id);
    $data['main_content'] = 'feesv2/receipts/excess_amount_receipts';
    $this->load->view('inc/template', $data);
  }

  public function send_fee_receipt_to_email(){
    $student_id = $_POST['student_id'];
    $trans_id = $_POST['trans_id'];
    $result = $this->fees_collection_model->send_fee_receipt_to_email_data($student_id);
    $email_template = $this->fees_collection_model->get_email_template_for_parent_receipt();
    $check_pdf_status = $this->fees_collection_model->check_pdf_file_path($trans_id);

    echo json_encode(array('parent_email'=>$result,'email_temlate'=>$email_template,'pdf_status'=>$check_pdf_status));
    
  }

  public function download_fee_receipt_fromEmail(){
    $link = $_POST['pdfFilepath'];
    $url = $this->filemanager->getFilePath($link);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download('fee_reciept.pdf', $data, TRUE);
  }

  public function send_fee_receipt_to_parent(){

    $pdf_file_pathID =$_POST['pdf_file_pathID'];
    $filepath = $this->fees_collection_model->check_pdf_file_path($pdf_file_pathID);
    $emailDetails = $this->fees_collection_model->getEmailTemplateforParent($filepath->student_id, 'fee_receipt_email_to_parent_counter');
    if(!empty($emailDetails)){
     $email_content = $this->_fee_counter_email_to_parent($emailDetails, (array) $filepath);
    }
    $files_array = array();
    array_push($files_array, array('name' => 'Fee Invoice.pdf', 'path' => $filepath->receipt_pdf_link));

    if(!empty($files_array)) {
      $files_string = json_encode($files_array);
    }
    $sent_data = [];
    // Add father's data
    $father_data = new stdClass();
    $father_data->id = $email_content['to_email']->f_id;
    $father_data->email = $email_content['to_email']->father_email;
    $father_data->avatar_type = 2;
    $sent_data[] = $father_data;
  
    // Add mother's data
    $mother_data = new stdClass();
    $mother_data->id = $email_content['to_email']->m_id;
    $mother_data->email = $email_content['to_email']->mother_email;
    $mother_data->avatar_type = 2;
    $sent_data[] = $mother_data;
    $sender_list = [];
    $sender_list['students'] = [[
      'send_to' => 'Both',
      'send_to_type' => 'Father',
      'ids' => $filepath->student_id
    ],
    [
      'send_to' => 'Both',
      'send_to_type' => 'Mother',
      'ids' =>  $filepath->student_id
    ]];
    
    $email_master_data = array(
      'subject' => $email_content['email_subject'],
      'body' =>  $email_content['template_content'],
      'source' => 'Fee Receipt',
      'sent_by' => $this->authorization->getAvatarId(),
      'recievers' => 'Parents',
      'from_email' => $email_content['registered_email'],
      'files' => ($files_string=='')?NULL:$files_string,
      'acad_year_id' => $this->acad_year->getAcadYearId(),
      'visible' => 1,
      'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
      'sending_status' => 'Completed',
      'texting_master_id' => $email_content['id']
    );
    $this->load->model('communication/emails_model');
    $email_master_id = $this->emails_model->saveEmail($email_master_data);
    $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
    $this->load->helper('email_helper');
    $email = $this->emails_model->getEmailInfo($email_master_id);
    $email_ids = $email_content['to_emails'];
    echo sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
  }

  private function _fee_counter_email_to_parent($input, $fee_details = []){
   
		$input['template_content'] = str_replace('%%father_name%%',$input['to_email']->father_name,$input['template_content']);
		$input['template_content'] = str_replace('%%mother_name%%',$input['to_email']->mother_name,$input['template_content']);
		$input['template_content'] = str_replace('%%student_name%%',$input['to_email']->student_name,$input['template_content']);
		$input['template_content'] = str_replace('%%student_grade%%',$input['to_email']->grade,$input['template_content']);

		if(!empty($fee_details)){
			$dateTimeObject = new DateTime($fee_details['paid_datetime']);
			$input['email_subject'] = str_replace('%%std_name%%',$input['to_email']->student_name,$input['email_subject']);
			$input['template_content'] = str_replace('%%std_name%%',$input['to_email']->student_name,$input['template_content']);
			$input['template_content'] = str_replace('%%grade_sec%%',$input['to_email']->grade,$input['template_content']);
			$input['template_content'] = str_replace('%%fee_paid_amount%%',$fee_details['amount_paid'],$input['template_content']);
			$input['template_content'] = str_replace('%%payment_date%%',$dateTimeObject->format('d-M-Y h:i A'),$input['template_content']);
			$input['template_content'] = str_replace('%%payment_method%%',$fee_details['payment_method'],$input['template_content']);
		}

		$boards = $this->settings->getSetting('board');
		$board = '';
		foreach($boards as $key => $val){
			if($key == $input['to_email']->board){
				$board = $val;
			}
		}
		$input['template_content'] = str_replace('%%curriculum_interested%%',$board,$input['template_content']);
    return $input;
	}

  public function get_online_challan_amount_details(){
    $online_challan_order_id = $_POST['online_challan_order_id'];
    $result = $this->fees_collection_model->get_online_challa_amount_detailsbyid($online_challan_order_id);
    echo json_encode($result);
  }

  public function get_installment_details_by_schid(){
    $schId = $_POST['schId'];
    $installments = $this->fees_collection_model->get_installment_data_by_schid($schId);
    $friendly_name = $this->fees_collection_model->get_fee_structure_friendly_name($schId);
    echo json_encode(array('installments'=>$installments,'friendly_name'=>$friendly_name));
  }

  public function get_fees_fast_collection_data(){
    $checked_std_sch_ids = $_POST['checked_std_sch_ids'];
    $result = $this->fees_collection_model->get_installment_wise_fee_data_for_fast_fee($checked_std_sch_ids);
    echo json_encode($result);
  }

  public function get_conession_discount_amount_alog(){
    $fees_sch_installment_id = $_POST['fees_sch_installment_id'];
    $result = $this->fees_collection_model->get_fee_installment_concession_alog($fees_sch_installment_id);
    echo json_encode($result);
    
  }

  public function get_expense_details_trans(){
    $exp_type = $_POST['exp_type'];
    $std_id = $_POST['std_id'];
    $result = $this->fees_collection_model->get_other_expenses_data_tx($exp_type, $std_id);
    echo json_encode($result);
  }

  public function get_fees_cohort_details_all(){
    $cohort_id = $_POST['cohort_id'];
    $bpId = $_POST['bpId'];
    $result = $this->fees_collection_model->student_assign_fee_cohort_component_structure($cohort_id);
    echo json_encode($result);
  }

  public function fees_approval_process(){
    $this->fees_collection_model->store_fees_edit_history($_POST['student_id'],'Single window '.$_POST['status'].'. Remarks : '.$_POST['fees_approval_remarks']);
    echo $this->fees_collection_model->fees_approval_process($_POST);
  }

  public function generate_expense_fees_component_wise_data(){
    $std_id = $this->input->post('std_id');
    $expense_name = $this->input->post('expense_name');
    $allocated_amount = $this->input->post('allocated_amount');
    $fee_total_amount = $this->input->post('fee_total_amount');
    $rdata = 0;
    if($fee_total_amount > $allocated_amount){

      $input = $this->fees_collection_model->get_expense_blueprint_details($expense_name, $fee_total_amount, $allocated_amount);
      if(empty($input)){
        $rdata = 0;
      }
      $checkIfAlreadyExits = $this->fees_collection_model->check_if_already_exits_in_cohortstudent($input['blueprint_id'], $std_id);
      if($checkIfAlreadyExits){
        $rdata = -1;
      }
      if(!empty($input) &&  isset($input['blue_print_name']) && $checkIfAlreadyExits == 0){
        $this->fees_collection_model->store_fees_edit_history($std_id,$input['blue_print_name'].' Expense Fees is Assigned');

        $rdata = $this->fees_student_model->insert_cohort_details_mass_assign($input['blueprint_id'], 0, 'CUSTOM', $input['blueprint_installment_type_id'], $input['comp_amount'], $input['conce_amount'], $std_id, $expense_name, $input['fine_amount'], 0, '','expense exit allocated amount');
      }
     
    }else{
      $additional_amount = $allocated_amount - $fee_total_amount;
      $additional_amount_remarks = '';
      if($expense_name == 'Books'){
        $additional_amount_remarks = 'TEXT & Note books <span style="position: absolute;left: 75%;">| ('.$allocated_amount.') | '.$fee_total_amount.'</span>';
      }
      if($expense_name == 'Uniforms'){
        $additional_amount_remarks = 'Uniforms <span style="position: absolute;left: 75%;">| ('.$allocated_amount.') | '.$fee_total_amount.'</span>';
      }
      if($expense_name == 'Pocket Money expenses'){
        $additional_amount_remarks = 'Balance in Pocket Money <span style="position: absolute;left: 75%;">| '.$allocated_amount.' | '.$fee_total_amount.'</span>';
      }
      $checkIfAlreadyExits = $this->fees_collection_model->check_if_already_exits_in_additional_amount($additional_amount_remarks, $std_id);
      if($checkIfAlreadyExits){
        $rdata = -1;
      }
      if(!empty($additional_amount_remarks) && $checkIfAlreadyExits == 0){
        $last_insert_id =  $this->fees_collection_model->insert_additional_amount_details($std_id, $additional_amount, $additional_amount_remarks, 9, '', '', '', '', date('Y-m-d'));
      }
    }
    echo json_encode($rdata);

  }

}