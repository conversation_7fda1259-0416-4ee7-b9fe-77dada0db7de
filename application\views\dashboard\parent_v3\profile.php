<!-- Profile Page - Mobile UI -->
<style>
   
    .profile-header {
        padding: 1rem 1rem 0.5rem 1rem;
        display: flex;
        align-items: center;
        position: relative;
    }
    .profile-header .back-btn {
        font-size: 1.5rem;
        color: #7b5cff;
        background: none;
        border: none;
        margin-right: 0.5rem;
        padding: 0;
        cursor: pointer;
    }
    .profile-header-title {
        flex: 1;
        text-align: center;
        font-weight: 700;
        font-size: 1.1rem;
    }
    .profile-alert {
        background: #fffbe6;
        color: #b26a00;
        border-radius: 10px;
        margin: 1rem 1rem 0.5rem 1rem;
        padding: 0.7rem 1rem;
        font-size: 0.98rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        border: 1px solid #ffe58f;
    }
    .profile-alert .fa {
        margin-right: 0.5rem;
    }
    .profile-alert .arrow {
        margin-left: auto;
        color: #b26a00;
    }
    .profile-success {
        background: #e6ffed;
        color: #237804;
        border-radius: 10px;
        margin: 1rem 1rem 0.5rem 1rem;
        padding: 0.7rem 1rem;
        font-size: 0.98rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        border: 1px solid #b7eb8f;
    }
    .profile-success .fa {
        margin-right: 0.5rem;
        color: #52c41a;
    }
    .profile-success .arrow {
        margin-left: auto;
        color: #237804;
    }
    .profile-avatar-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 1.2rem;
        margin-bottom: 1.2rem;
    }
    .profile-avatar {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #e0e0e0;
        margin-bottom: 0.7rem;
    }
    .profile-name {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 0.2rem;
    }
    .profile-class {
        color: #888;
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }
    .switch-profile-btn {
        background: #7b5cff;
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 0.7rem 1.5rem;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1.2rem;
        margin-top: 0.5rem;
        width: 90%;
        max-width: 320px;
        box-shadow: 0 2px 8px rgba(123,92,255,0.08);
        transition: background 0.2s;
    }
    .switch-profile-btn:active {
        background: #4e8cff;
    }
    .profile-list {
        background: #fff;
        border-top-left-radius: 22px;
        border-top-right-radius: 22px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.01);
        padding: 0.5rem 12px;

    }
    .profile-list-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.2rem;
        border-bottom: 1px solid #ececec;
        font-size: 1.05rem;
        color: #333;
        cursor: pointer;
        background: #f6f7fb;
        border-radius: 10px;
        margin: 0.5rem 0;
        transition: background 0.15s;
    }
    .profile-list-item:last-child {
        border-bottom: none;
    }
    .profile-list-item .fa, .profile-list-item .fa-solid {
        font-size: 1.2rem;
        margin-right: 1.1rem;
        color: #7b5cff;
        min-width: 22px;
        text-align: center;
    }
    .profile-list-item .fa-lock, .profile-list-item .fa-headset {
        color: #4e8cff;
    }
    .profile-list-item .fa-sign-out-alt {
        color: #fd5e53;
    }
    .profile-list-item .fa-trash {
        color: #ff3b30;
    }
    .profile-list-item:active {
        background: #f0f0ff;
    }
    .profile-list-item .right-arrow {
        margin-left: auto;
        color: #bbb;
        font-size: 1.1rem;
    }
    .profile-list-item.delete {
        color: #ff3b30;
        font-weight: 600;
        justify-content: center;
        background: #fff0f0;
        border: 1px solid #ffe0e0;
    }
    .profile-list-item.delete .fa-trash {
        margin-right: 0.7rem;
    }

    /* Switch Profile Modal Styles */
    .switch-profile-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: none;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .switch-profile-modal.show {
        display: flex;
        opacity: 1;
    }

    .switch-profile-modal-content {
        background: #fff;
        width: 100%;
        max-height: 70vh;
        margin-top: auto;
        border-radius: 20px 20px 0 0;
        transform: translateY(100%);
        transition: transform 0.3s ease;
        overflow-y: auto;
    }

    .switch-profile-modal.show .switch-profile-modal-content {
        transform: translateY(0);
    }

    .modal-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: sticky;
        top: 0;
        background: #fff;
        border-radius: 20px 20px 0 0;
    }

    .modal-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #888;
        cursor: pointer;
        padding: 0;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background 0.2s;
    }

    .modal-close:hover {
        background: #f0f0f0;
    }

    .modal-body {
        padding: 1rem 1.5rem 2rem 1.5rem;
    }

    .modal-profile-item {
        display: flex;
        align-items: center;
        background: #f8f9fa;
        border-radius: 14px;
        margin-bottom: 1rem;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        color: inherit;
    }

    .modal-profile-item:hover {
        background: #e9ecef;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .modal-profile-item:last-child {
        margin-bottom: 0;
    }

    .modal-profile-avatar {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 1rem;
        border: 2px solid #e0e0e0;
    }

    .modal-profile-info {
        flex: 1;
    }

    .modal-profile-name {
        font-size: 1.1rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.2rem;
    }

    .modal-profile-class {
        color: #888;
        font-size: 0.95rem;
    }

    @media (min-width: 600px) {
        .profile-header, .profile-alert, .profile-list, .profile-avatar-section {
            max-width: 440px;
            margin-left: auto;
            margin-right: auto;
        }

        .switch-profile-modal-content {
            max-width: 440px;
            margin: auto auto 0 auto;
        }
    }
</style>

<!-- FontAwesome CDN (for icons) -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />

<div class="profile-header">
    <button class="back-btn" onclick="window.history.back()">
        <i class="fa fa-chevron-left arrow"></i>
    </button>
    <div class="profile-header-title">Profile</div>
    <div style="width:32px;"></div>
</div>

<?php if($student_data['profile_confirmed'] == 0){ ?>
    <div class="profile-alert">
        <i class="fa fa-exclamation-triangle"></i>
        <span>Profile confirmation is pending</span>
        <i class="fa fa-chevron-right arrow"></i>
    </div>
<?php }else{ ?>
    <div class="profile-success">
        <i class="fa fa-check-circle"></i>
        <span>Profile confirmation is successfull</span>
        <i class="fa fa-chevron-right arrow"></i>
    </div>
<?php } ?>

<div class="profile-avatar-section">
    <img src="<?php echo $student_data['photoUrl'] ?>" class="profile-avatar" alt="User">
    <div class="profile-name">
        <?php echo $student_data['name']; ?>
    </div>
    <div class="profile-class">
        <?php echo $student_data['classSection']; ?>
    </div>
    <?php if($student_data['isSiblingConnected'] == 1){ ?>
        <button class="switch-profile-btn" onclick="openSwitchProfileModal('<?php echo $student_data['student_id'] ?>')">Switch Profile</button>
    <?php } ?>
</div>

<div class="profile-list">
    <div class="profile-list-item" onclick="onclickProfileList()" onclick="window.location.href='<?php echo site_url('parent/profile_detail/'.$student_data['student_id']) ?>'">
        <i class="fa fa-user"></i>
        Profile Detail
        <i class="fa fa-chevron-right right-arrow"></i>
    </div>
    <div class="profile-list-item" onclick="window.location.href='#'">
        <i class="fa fa-lock"></i>
        Change Password
        <i class="fa fa-chevron-right right-arrow"></i>
    </div>
    <div class="profile-list-item" onclick="window.location.href='#'">
        <i class="fa fa-headset"></i>
        Help & Support
        <i class="fa fa-chevron-right right-arrow"></i>
    </div>
    <div class="profile-list-item" onclick="window.location.href='<?php echo site_url('auth/logout') ?>'">
        <i class="fa fa-sign-out-alt"></i>
        Logout
        <i class="fa fa-chevron-right right-arrow"></i>
    </div>
    <div class="profile-list-item delete" onclick="if(confirm('Are you sure you want to delete your account?')) window.location.href='#'">
        <i class="fa fa-trash"></i>
        Delete Account
    </div>
</div>

<!-- Switch Profile Modal -->
<div class="switch-profile-modal" id="switchProfileModal">
    <div class="switch-profile-modal-content">
        <div class="modal-header">
            <div class="modal-title">Switch Profile</div>
            <button class="modal-close" onclick="closeSwitchProfileModal()">
                <i class="fa fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div id="profilesList">
              
            </div>
        </div>
    </div>
</div>

<script>
// Properly encode PHP array as JSON for use in JavaScript
let student_data = <?php echo json_encode($student_data); ?>;

function onclickProfileList(){   
    // Encode the data as a base64 JSON string
    var encodedData = btoa(unescape(encodeURIComponent(JSON.stringify(student_data))));
    // Redirect with encoded data as a single query param
    window.location.href = '<?php echo site_url('parent/profile_detail') ?>' + '?data=' + encodeURIComponent(encodedData);
}

// Switch Profile Modal Functions
function openSwitchProfileModal(currentStudentId) {
    const modal = document.getElementById('switchProfileModal');
    modal.style.display = 'flex';

    // Trigger animation after display is set
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);

    // Load profiles dynamically
    loadProfiles(currentStudentId);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function closeSwitchProfileModal() {
    const modal = document.getElementById('switchProfileModal');
    modal.classList.remove('show');

    // Hide modal after animation
    setTimeout(() => {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }, 300);
}

function loadProfiles(currentStudentId) {
    $.ajax({
        url: '<?php echo site_url('parent/dashboard_controller/getConnectedSiblingData') ?>',
        type: 'POST',
        data: {'currentStudentId':currentStudentId},
        success: function(response) {
            console.log('AJAX Success - sibiling Response received:', response);
            handleSiblingData(response);
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error, xhr, status);
        },
        complete: function() {
            // Re-enable search button
            $('#search').prop('disabled', false).html('Get Report');
            hideLoading();
        }
    });
}

function handleSiblingData(response){
    var profiles = JSON.parse(response);
    const profilesList = document.getElementById('profilesList');
    let html = '';
    profiles.forEach(profile => {
        html += `
            <a href="#" class="modal-profile-item" onclick="switchToProfile(${profile.id})">
                <img src="${profile.student_photo}" class="modal-profile-avatar" alt="${profile.student_name}">
                <div class="modal-profile-info">
                    <div class="modal-profile-name">${profile.student_name}</div>
                    <div class="modal-profile-class">${profile.class_section}</div>
                </div>
            </a>
        `;
    });
    profilesList.innerHTML = html;
}

function switchToProfile(student_id){
    window.location.href = '<?php echo site_url('parentdashboard') ?>?student_id=' + student_id;
}


// Close modal when clicking outside
document.getElementById('switchProfileModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSwitchProfileModal();
    }
});

// Handle escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modal = document.getElementById('switchProfileModal');
        if (modal.classList.contains('show')) {
            closeSwitchProfileModal();
        }
    }
});

// Prevent modal content click from closing modal
document.querySelector('.switch-profile-modal-content').addEventListener('click', function(e) {
    e.stopPropagation();
});
</script>