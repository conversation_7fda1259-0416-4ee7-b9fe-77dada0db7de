<?php

require APPPATH . 'libraries/REST_Controller.php';

class Online_schedule extends REST_Controller {
    public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
            $data = ['error' => 'User not logged in'];
            $this->response($data, REST_Controller::HTTP_UNAUTHORIZED);
			$this->output->_display();
			exit;
		}
        $this->load->model('parentv2/Online_schedule_model');
    }

    // public function index() {
    //     $data['main_content'] = 'online_class_v2/student/schedules.php';
    //     $this->load->view('inc/template', $data);
    // }

    public function schedules_get() {
		$student_id = $this->get('student_id');
		$schedule_from = date('Y-m-d', strtotime($this->get('schedule_from')));
        $schedule_to = $this->get('schedule_to');
        if(!empty($schedule_to)) {
            $schedule_to = date('Y-m-d', strtotime($schedule_to));
        } else {
            $schedule_to = $schedule_from;
        }


        // $this->load->model('parent_model');
        // $schedule_date = date('Y-m-d', strtotime($_POST['schedule_date']));
        // $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        
        $data['schedules'] = $this->Online_schedule_model->get_schedules($schedule_from, $schedule_to, $student_id);
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
        exit;
    }

}