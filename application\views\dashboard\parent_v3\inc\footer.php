
    <div style="height: 80px;"></div>

    <!-- Bottom Navigation Bar -->
    <div class="bottom-nav">
        <a href="<?php echo site_url('parentdashboard'); ?>" class="nav-item <?php echo (uri_string() == 'parentdashboard' || uri_string() == '') ? 'active' : ''; ?>">
            <i class="fa fa-home"></i><br>
            <span>Home</span>
        </a>
        <a href="<?php echo site_url('parent/explore'); ?>" class="nav-item <?php echo (strpos(uri_string(), 'explore') !== false) ? 'active' : ''; ?>">
            <i class="fa fa-compass"></i><br>
            <span>Explore</span>
        </a>
        <a href="<?php echo site_url('parent/notifications'); ?>" class="nav-item <?php echo (strpos(uri_string(), 'notifications') !== false) ? 'active' : ''; ?>" style="position:relative;">
            <i class="fa fa-bell"></i>
            <?php if (isset($notification_counts) && $notification_counts->total_notifications > 0): ?>
                <span class="badge-notify" style="top:2px;right:18px;"><?php echo $notification_counts->total_notifications; ?></span>
            <?php endif; ?>
            <br>
            <span>Notification</span>
        </a>
        <a href="javascript:void(0)" id="profileFooterOnclick" class="nav-item <?php echo (strpos(uri_string(), 'profile') !== false) ? 'active' : ''; ?>">
            <img id="student_profile_photo" src="<?php echo base_url('assets/img/icons/profile.png'); ?>" alt="Profile" class="dashboard-avatar-footer studentphoto" style="object-fit:cover; cursor:pointer;">
            <br>
            <span>Profile</span>
        </a>
    </div>

    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/moment.min.js"></script>
    <!-- DataTables (only load when needed) -->
    <?php if (isset($load_datatables) && $load_datatables): ?>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <?php endif; ?>

    <!-- Custom JavaScript -->
    <script>
        // Global app configuration
        const AppConfig = {
            baseUrl: '<?php echo base_url(); ?>',
            siteUrl: '<?php echo site_url(); ?>',
            currentPage: '<?php echo uri_string(); ?>',
            csrfToken: '<?php echo $this->security->get_csrf_hash(); ?>',
            csrfName: '<?php echo $this->security->get_csrf_token_name(); ?>'
        };

        // Loading spinner functions
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }

       
        // Handle AJAX requests with loading
        $(document).ajaxStart(function() {
            showLoading();
        }).ajaxStop(function() {
            hideLoading();
        });

        // Service Worker registration for PWA (optional)
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('<?php echo base_url("sw.js"); ?>')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // Handle offline/online status
        window.addEventListener('online', function() {
            console.log('App is online');
        });

        window.addEventListener('offline', function() {
            console.log('App is offline');
        });

        // Prevent zoom on iOS
        document.addEventListener('gesturestart', function (e) {
            e.preventDefault();
        });

        // Handle viewport height for mobile browsers
        function setViewportHeight() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }

        setViewportHeight();
        window.addEventListener('resize', setViewportHeight);


        $('#profileFooterOnclick').on('click', function() {
            var studentName = $('#studentName').text();
            var classSection = $('.welcome-sub').text();
            var studentPhoto = $('#student_profile_photo').attr('src');

            // Prepare data object
            var data = {
                name: studentName,
                classSection: classSection,
                student_id: student_id,
                photoUrl: studentPhoto,
                profile_confirmed: profile_confirmed,
                isSiblingConnected: isSiblingConnected,
            };

            // Encode the data as a base64 JSON string
            var encodedData = btoa(unescape(encodeURIComponent(JSON.stringify(data))));
            // Redirect with encoded data as a single query param
            window.location.href = '<?php echo site_url('profile') ?>' + '?data=' + encodeURIComponent(encodedData);
        });


    </script>

    

    <!-- Page-specific scripts -->
    <?php if (isset($page_scripts)): ?>
        <?php echo $page_scripts; ?>
    <?php endif; ?>

</body>
</html>
