<?php
class Ass_Entities_Controller extends CI_Controller {
    private $yearId;
    function __construct() {
        parent::__construct();
       if (!$this->ion_auth->logged_in()) {
         redirect('auth/login', 'refresh');
       }
       if (!$this->authorization->isModuleEnabled('STUDENT_MASTER')) {
        redirect('dashboard', 'refresh');
      }
      if (!$this->authorization->isAuthorized('STUDENT.BASIC_VIEW')) {
        redirect('dashboard', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('Ass_Entities_Model');
      
    }

    public function index($class_id=1){
        $data['ass_entities'] = $this->Ass_Entities_Model->filterAssessmentEntities($class_id);
        $data['classes'] = $this->Ass_Entities_Model->getsClassId($this->yearId);
        $data['class_id'] = $class_id;
        $data['yearId'] = $this->yearId;
        $data['years'] = $this->Ass_Entities_Model->getAcadYears();
        // echo "<pre>"; print_r($data['class_id']);die();
        $data['groups'] = $this->Ass_Entities_Model->getGroups($class_id);
        // echo "<pre>"; print_r($data['ass_entities']);die();
        $data['main_content'] = 'entities/index';
        $this->load->view('inc/template', $data);
    }

    public function getYearClasses() {
      $year = $_POST['yearId'];
      $classes = $this->Ass_Entities_Model->getsClassId($year);
      echo json_encode($classes);
    }

    public function addEntity(){
      $class_id = $this->input->post('cs_id');
      $status = $this->Ass_Entities_Model->addEntity();
      if($status)
        $this->session->set_flashdata('flashSuccess', 'Successfully Added');
      else 
        $this->session->set_flashdata('flashError', 'Something Went Wrong!');
      redirect('Ass_Entities_Controller/index/'.$class_id);
    }

    public function editEntityForm(){
        $entity_id = $_POST['id'];
        $entity = $this->Ass_Entities_Model->getAssessmentEntity($entity_id);
        $json = json_decode($entity->derived_formula);
        unset($entity->derived_formula);
        $data['entData'] = $entity;
        $data['entityIds'] = array();
        $data['formula'] = array();
        if($json != null) {
          $data['entityIds'] = $json->entityId;
          $data['formula'] = ($json->formula)->name;
        }
        echo json_encode($data);
       
    }

    public function editEntity(){
        $data['entity_id'] = $this->uri->segment(3);
        $status = $this->Ass_Entities_Model->updateEntity($data['entity_id']);
        if($status)
          $this->session->set_flashdata('flashSuccess', 'Successfully Updated');
        else 
          $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        redirect('Ass_Entities_Controller/index');
    }

    public function deleteEntity($entity_id){
        $status = $this->Ass_Entities_Model->deleteEntity($entity_id);
        if($status)
          $this->session->set_flashdata('flashSuccess', 'Successfully Deleted');
        else 
          $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        redirect('Ass_Entities_Controller/index');
    }

    public function filterEntities(){
      
        $entities = $this->Ass_Entities_Model->filterAssessmentEntities($_POST['class_id']);
        $groups = $this->Ass_Entities_Model->getGroups($_POST['class_id']);

        if(empty($entities)){
          $template = '<h4>No entities added</h4>';
        } else {
          $template = "<table id='customers1' class='table datatable'>
            <thead>
              <tr>
                <th>#</th>
                <th>Name</th>
                <th>Short Name</th>
                <th>Group</th>
                <th>Mapping String</th>
                <th>Class ID</th>
                <th>Type</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>";
               
            $i=1;     
              foreach ($entities as  $entity) { 
              
             $template .= "<tr>
                <td>".$i++."</td>
                <td>".$entity->name."</td>
                <td>".$entity->short_name."</td>
                <td>".$entity->entity_name."</td>
                <td>".$entity->mapping_string."</td>
                <td>".$entity->class_id."</td>
                <td>".$entity->ass_type."</td>
               
                <td>
                  <a href='javascript:void(0)' class='btn btn-warning ' data-placement='top'  data-toggle='tooltip' data-original-title='Edit Entity' onclick='edit(".$entity->id.")'><i class='fa fa-edit'></i></a>
                  <a href='".site_url('Ass_Entities_Controller/deleteEntity/'.$entity->id)."' class='btn btn-warning ' data-placement='top'  data-toggle='tooltip' data-original-title='Delete Entity'><i class='fa fa-trash-o'></i></a>
                </td>
                </tr>";
              }

           $template .= "</tbody>
          </table> ";
        }
        $data = array('table'=>$template, 'groups'=>$groups);
        
        echo json_encode($data);
        //echo 1;
       // print_r($data['entities']);
        //echo json_encode($data['entities']);
    }

    public function getEntitiesAndGroups(){
      $classId = $_POST['classId'];
      echo json_encode($this->Ass_Entities_Model->getEntGroupsByClass($classId));
    }

    public function addCloneEntities(){
      $status = $this->Ass_Entities_Model->addCloneEntAndGroups();
      if($status)
        $this->session->set_flashdata('flashSuccess', 'Successfully Added');
      else 
        $this->session->set_flashdata('flashError', 'Something Went Wrong!');
      redirect('Ass_Entities_Controller/index/'.$class_id);
    }

    public function getComponents(){
      $data = $this->Ass_Entities_Model->getComponents($_POST['classId'], $_POST['gId']);
      echo json_encode($data);
    }
}