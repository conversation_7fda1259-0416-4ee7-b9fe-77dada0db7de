<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

if(!function_exists('local_time')) {
	function local_time($datetime, $format="d-m-Y H:i:s") {
		if(!$datetime || $datetime == '') return '';
		$CI =& get_instance();
        $timezone = $CI->settings->getSetting('school_timezone');
		if(!$timezone) {
			$timezone = 'Asia/Kolkata';
		}
		$userTimezone = new DateTimeZone($timezone);
		$gmtTimezone = new DateTimeZone('GMT');
		$myDateTime = new DateTime($datetime, $gmtTimezone);
		$offset = $userTimezone->getOffset($myDateTime);
		return date($format, strtotime($datetime)+$offset);
	}
}

if(!function_exists('gmt_time')) {
	function gmt_time($datetime, $format="d-m-Y H:i:s") {
		if(!$datetime || $datetime == '') return '';
		$CI =& get_instance();
        $timezone = $CI->settings->getSetting('school_timezone');
		if(!$timezone) {
			$timezone = 'Asia/Kolkata';
		}
		$given = new DateTime($datetime, new DateTimeZone($timezone));
		$given->setTimezone(new DateTimeZone("GMT"));
		return $given->format($format); 
	}
}