<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

if (!function_exists('sendStudentNotifications')) {
	//$student_ids - array of student admission ids
	//$title - Title of the notification
	//$message - Notification content 
	//$send_to - Both, Father, Mother (Both by default)
    function sendStudentNotifications($student_ids, $title, $message, $url, $send_to='Both') {
        if (ENVIRONMENT !== 'production') {
            return 0;
        }
        $CI =& get_instance();
        $CI->load->model('student/Student_Model', 'student_model');
        $CI->load->library('pushnotification');
        $users = $CI->student_model->getParentUserTokens($student_ids, $send_to);
        $response = array();
        $response['request'] = $users;
        if(empty($users)) {
            // trigger_error("All the tokens are empty - Notifications not sent");
            $response['response'] = 'All the tokens are empty - Notifications not sent';
        } else {
            $response['response'] = $CI->pushnotification->sendNotification($users, $title, $message, $url);
        }
        return $response;
    }
}


if (!function_exists('sendStaffNotifications')) {
	//$staff_ids - array of staff ids
	//$title - Title of the notification
	//$message - Notification content 
    function sendStaffNotifications($staff_ids, $title, $message, $url) {
        if (ENVIRONMENT !== 'production') {
            return 0;
        }
        $CI =& get_instance();
        $CI->load->model('staff/Staff_Model', 'staff_model');
        $CI->load->library('pushnotification');
        $users = $CI->staff_model->getStaffUserTokens($staff_ids);
        $response = array();
        $response['request'] = $users;
        if(empty($users)) {
            // trigger_error("All the tokens are empty - Notifications not sent");
             $response['response'] = 'All the tokens are empty - Notifications not sent';
        } else {
            $response['response'] = $CI->pushnotification->sendNotification($users, $title, $message, $url);
        }
        return $response;
    }
}

function statusUpdate($user_tokens, $response) {
    // echo '<pre>tokens';print_r($user_tokens);
    // echo '<pre>res';print_r($response);die();
    $CI =& get_instance();
    $CI->load->model('communication/texting_model');
    $tokens = array();
    $text_ids = array();
    foreach ($user_tokens as $user) {
        array_push($tokens, $user['token']);
        array_push($text_ids, $user['text_sent_to_id']);
    }
    $res_message = json_decode($response['message']);
    $result = array();
    if($response['status']) {
        $result = $res_message->results;
    }
    $statusData = array();
    foreach ($tokens as $i => $token) {
        $status = 'Sent';
        if($response['status'] == 0) {
            $status = 'Failed';
        } else if(isset($result[$i]->error)) {
            $status = $result[$i]->error;
        }
        $statusData[] = array(
            'id' => $text_ids[$i],
            'status' => $status
        );
    }
    $CI->texting_model->updateNotificationStatus($statusData);
    return $response['status'];
}

if (!function_exists('studentNotifications')) {
    //$student_ids - array of student admission ids
    //$title - Title of the notification
    //$message - Notification content 
    //$send_to - Both, Father, Mother (Both by default)
    function studentNotifications($user_tokens, $title, $message, $url='') {
        if (ENVIRONMENT !== 'production') {
            statusUpdate($user_tokens, array('status' => 0, 'message' => 'Failed'));
            return -2;
        }
        $CI =& get_instance();
        $CI->load->library('pushnotification');
        $response = $CI->pushnotification->sendNotificationV2($user_tokens, $title, $message, $url);
        return statusUpdate($user_tokens, $response);
    }
}

if (!function_exists('staffNotifications')) {
    //$student_ids - array of student admission ids
    //$title - Title of the notification
    //$message - Notification content 
    //$send_to - Both, Father, Mother (Both by default)
    function staffNotifications($user_tokens, $title, $message, $url='') {
        if (ENVIRONMENT !== 'production') {
            statusUpdate($user_tokens, array('status' => 0, 'message' => 'Failed'));
            return -2;
        }
        $CI =& get_instance();
        $CI->load->library('pushnotification');
        $response = $CI->pushnotification->sendNotificationV2($user_tokens, $title, $message, $url);
        return statusUpdate($user_tokens, $response);
    }
}

if (!function_exists('uniqueStudentNotifications')) {
    //$student_ids - array of student admission ids
    //$title - Title of the notification
    //$message - Notification content 
    //$send_to - Both, Father, Mother (Both by default)
    function uniqueStudentNotifications($user_tokens, $title, $url='') {
        if (ENVIRONMENT !== 'production') {
            statusUpdate($user_tokens, array('status' => 0, 'message' => 'Failed'));
            return -2;
        }
        $CI =& get_instance();
        $CI->load->library('pushnotification');
        return  $CI->pushnotification->sendUniqueNotification($user_tokens, $title, $url);
    }
}

if (!function_exists('uniqueStaffNotifications')) {
    //$student_ids - array of student admission ids
    //$title - Title of the notification
    //$message - Notification content 
    //$send_to - Both, Father, Mother (Both by default)
    function uniqueStaffNotifications($user_tokens, $title, $url='') {
        if (ENVIRONMENT !== 'production') {
            statusUpdate($user_tokens, array('status' => 0, 'message' => 'Failed'));
            return -2;
        }
        $CI =& get_instance();
        $CI->load->library('pushnotification');
        return  $CI->pushnotification->sendUniqueNotification($user_tokens, $title, $url);
    }
}

if (!function_exists('sendNotificationsToCommity')) {
    //$student_ids - array of student admission ids
    //$title - Title of the notification
    //$message - Notification content 
    //$send_to - Both, Father, Mother (Both by default)
    function sendNotificationsToCommity($title, $message, $url) {
        if (ENVIRONMENT !== 'production') {
            return 0;
        }
        $CI =& get_instance();
        $CI->load->model('staff/Staff_Model', 'staff_model');
        $CI->load->library('pushnotification');
        $member_ids = $CI->settings->getSetting('commity_members_staff_ids');
        if($member_ids == '') {
            return 0;
        }
        $staff_ids = explode(",", $member_ids);
        $users = $CI->staff_model->getStaffUserTokens($staff_ids);
        if(empty($users)) {
            return 0;
        }
        return $CI->pushnotification->sendNotification($users, $title, $message, $url);
    }
}

if (!function_exists('sendTransportNotifications')) {
    //$user_tokens - array of user ids and tokens
    //$title - Title of the notification
    //$message - Notification content 
    //$url - page to open on notification click
    function sendTransportNotifications($user_tokens, $title, $message, $url) {
        $response = array();
        $response['request'] = $user_tokens;
        if (ENVIRONMENT !== 'production') {
            statusUpdate($user_tokens, array('status' => 0, 'message' => 'Failed'));
            $response['response'] = 'Cannot send notification from Localhost';
            return $response;
        }
        $CI =& get_instance();
        $CI->load->library('pushnotification');
        if(empty($user_tokens)) {
            $response['response'] = 'All the tokens are empty - Notifications not sent';
        } else {
            $res = $CI->pushnotification->sendNotification($user_tokens, $title, $message, $url);
            $response['response'] = $res['message'];
            statusUpdate($user_tokens, $res);
        }
        return $response;
    }
}

if (!function_exists('commonNotifications')) {
    //$student_ids - array of student admission ids
    //$title - Title of the notification
    //$message - Notification content 
    //$send_to - Both, Father, Mother (Both by default)
    function commonNotifications($user_tokens, $title, $message, $url='') {
        if (ENVIRONMENT !== 'production') {
            statusUpdate($user_tokens, array('status' => 0, 'message' => 'Failed'));
            return -2;
        }
        $CI =& get_instance();
        $CI->load->library('pushnotification');
        $response = $CI->pushnotification->sendNotificationV2($user_tokens, $title, $message, $url);
        return statusUpdate($user_tokens, $response);
    }
}