<?php

class Attendance extends CI_Controller
{
	function __construct()
	{
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->settings->isParentModuleEnabled('ATTENDANCE_V2')) {
			redirect('dashboard', 'refresh');
		}
		$this->load->model('parent/Attendance_model', 'attendance_model');
		$this->load->model('parent_model');
	}

	public function index() {
		if ($this->mobile_detect->isMobile()) {
			$data['main_content'] = 'parent/attendance_v2/index_mobile.php';
		} else {
			$data['main_content'] = 'parent/attendance_v2/index_desktop.php';
		}
		$this->load->view('inc/template', $data);
	}

	public function getAttendanceData() {
		$date = date('Y-m-d', strtotime($_POST['date']));
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['attendance'] = $this->attendance_model->getAttendanceData($date, $student_id);
		echo json_encode($data);
	}

	public function getAttendanceDataByDateRange() {
		$from_date = date('Y-m-d', strtotime($_POST['from_date']));
		$to_date = date('Y-m-d', strtotime($_POST['to_date']));
		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['attendance'] = $this->attendance_model->getAttendanceDataByDateRange($from_date, $to_date, $student_id);
		echo json_encode($data);
	}

}
?>