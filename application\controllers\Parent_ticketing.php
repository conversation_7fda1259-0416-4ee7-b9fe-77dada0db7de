<?php

class Parent_ticketing extends CI_Controller {

	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
			redirect('dashboard', 'refresh');
		}
		if (!$this->settings->isParentModuleEnabled('PARENT_TICKETING')) {
			redirect('dashboard', 'refresh');
		}
		$this->load->library('filemanager');
		$this->load->model('parent_ticketing_model', 'pt');
		$this->config->load('form_elements');
		$this->load->model('student/Student_Model');
		$this->load->model('school_model', 'sm');
	}

  	public function parent_index($type='all') {
		$id=$this->authorization->getAvatarStakeHolderId();
		$data['type'] = $type;

		switch ($type) {
			case 'all':
				$data['ticket_list'] = $this->pt->get_tickets_by_parent();
				break;
			case 'open':
				$data['ticket_list'] = $this->pt->get_open_tickets_by_id($id);
				break;
			case 'closed':
				$data['ticket_list'] = $this->pt->get_closed_tickets_by_id($id);
				break;
		}

		$data['ticketcount'] = count($data['ticket_list']);
		$data['main_content'] = 'ticketing/parent/parent_index';
		$this->load->view('inc/template', $data);
	}

	public function add_ticket() {
		$ticket_types = $this->settings->getSetting('parent_ticketing_types');
		$data['staff_list']=$this->pt->get_staff_list_all();
		$data['main_content'] = 'ticketing/parent/add';
		$data['category_list'] = $this->pt->get_categories();
		$data['ticket_types'] = [];
		if($ticket_types) {
			$data['ticket_types'] = json_decode($ticket_types);
		}
		$this->load->view('inc/template', $data);
	}

	private function __s3FileUpload($file) {
	    if($file['tmp_name'] == '' || $file['name'] == '') {
	      return ['status' => 'empty', 'file_name' => ''];
	    }       
	    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'circulars');
	}

	public function submit_ticket() {
		$files = $_FILES['attachments'];
		$files_array = array();
		$files_string = '';
		if(isset($_FILES['attachments'])) {
			foreach ($_FILES['attachments']['name'] as $key => $file_name) {
				$file = array(
					'name' => $file_name,
					'type' => $_FILES['attachments']['type'][$key],
					'tmp_name' => $_FILES['attachments']['tmp_name'][$key],
					'error' => $_FILES['attachments']['error'][$key],
					'size' => $_FILES['attachments']['size'][$key]
				);
				$path = $this->__s3FileUpload($file);
				if($path['file_name'] != '') {
					array_push($files_array, array('name' => $file_name, 'path' => $path['file_name']));
				}
			}
			// $files_string = implode(",", $files_array);
			if(!empty($files_array))
				$files_string = json_encode($files_array);
		}
		// echo '<pre>'; print_r($files_string); die();
		$result = $this->pt->submit_ticket($files_string);

		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully submited');
			redirect('parent_ticketing/parent_index');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
			redirect('parent_ticketing/parent_index');
		}
	}
	
	/*
	* 1: Get open tickets
	* 2: Get assigned tickets
	* 3: Get all tickets
	*/
	public function view_assigned_tickets($mode = 1) {
	//	echo 'mode';print_r($mode);die();
		if (!$this->authorization->isAuthorized('PARENT_TICKETING.MODULE')) {
			redirect('dashboard', 'refresh');
		}

		if ($this->authorization->isAuthorized('PARENT_TICKETING.TICKETING_ADMIN')) {
			//Do Nothing
		} else {
			//Show only their assigned tickets if *not* ticketing admin
			$mode = 2;
		}

		switch($mode){
			case 1:
				$data['list_issue'] = $this->pt->get_open_tickets();
				$data['line'] = " - Open Tickets (" . count($data['list_issue']) . ")";
				$data['opencount'] = count($data['list_issue']);
				break;
			case 2:
				$staff_id = $this->authorization->getAvatarStakeHolderId();
				$data['list_issue'] = $this->pt->get_assigned_tickets_by_id($staff_id);
				$data['line'] = " - Assigned to me (" . count($data['list_issue']) . ")";
				break;
			case 3:
				$data['list_issue'] = $this->pt->get_all_tickets();
				$data['line'] = " - All Tickets (" . count($data['list_issue']) . ")";
				break;
		}
		$data["ticket_mode"]=$mode;
		$data['ticketcount'] = count($data['list_issue']);
		$data['is_ticketing_admin'] = $this->authorization->isAuthorized('PARENT_TICKETING.TICKETING_ADMIN');
		// echo '<pre>';print_r($data);die();
		$data['ticketsummary'] = $this->pt->get_ticket_summary_count();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'ticketing/staff/view_all_tickets_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'ticketing/staff/view_all_tickets_mobile';
        }else{
          $data['main_content'] = 'ticketing/staff/view_all_tickets'; 	
        }
		$this->load->view('inc/template', $data);	
	
	}

	public function get_ticket_type_wise(){
		$from_date = $_POST['from_date']; 
		$to_date = $_POST['to_date']; 
		$ticket_type = $_POST['ticket_types'];
		
		if (!$this->authorization->isAuthorized('PARENT_TICKETING.MODULE')) {
			redirect('dashboard', 'refresh');
		}

		if ($this->authorization->isAuthorized('PARENT_TICKETING.TICKETING_ADMIN')) {
			//Do Nothing
		} else {
			//Show only their assigned tickets if *not* ticketing admin
			$ticket_type = 2;
		}
		
		switch($ticket_type){
			case 1:
				$result['list_issue'] = $this->pt->get_open_tickets_v2($from_date,$to_date);
				break;
			case 2:
				$staff_id = $this->authorization->getAvatarStakeHolderId();
				$result['list_issue'] = $this->pt->get_assigned_tickets_by_id_v2($staff_id,$from_date,$to_date);
				break;
			case 3:
				$result['list_issue'] = $this->pt->get_all_tickets_v2($from_date,$to_date);
				break;
		}

		echo json_encode($result);

		
	}

	public function view_ticket_details($id)	{
		if (!$this->authorization->isAuthorized('PARENT_TICKETING.MODULE')) {
			redirect('dashboard', 'refresh');
		}
		$data['ticket'] = $this->pt->get_ticket_by_id($id);
		$data['staff_list'] = $this->pt->build_assigned_to_list($data['ticket']->student_cs_id);
		$data['status_flags'] = $this->config->item('ticketing_status');
        if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'ticketing/staff/view_ticket_details_tablet';
		  }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'ticketing/staff/view_ticket_details_mobile';
		  }else{
			$data['main_content'] = 'ticketing/staff/view_ticket_details'; 	
		  }		
		$this->load->view('inc/template', $data);
	}

	public function submit_staff_response($id) {
		if (!$this->authorization->isAuthorized('PARENT_TICKETING.MODULE')) {
			redirect('dashboard', 'refresh');
		}
		
		$files = $_FILES['staff_attachments'];
		$files_array = array();
		$files_string = '';
		if(isset($_FILES['staff_attachments'])) {
			foreach ($_FILES['staff_attachments']['name'] as $key => $file_name) {
				$file = array(
					'name' => $file_name,
					'type' => $_FILES['staff_attachments']['type'][$key],
					'tmp_name' => $_FILES['staff_attachments']['tmp_name'][$key],
					'error' => $_FILES['staff_attachments']['error'][$key],
					'size' => $_FILES['staff_attachments']['size'][$key]
				);
				$path = $this->__s3FileUpload($file);
				if($path['file_name'] != '') {
					array_push($files_array, array('name' => $file_name, 'path' => $path['file_name']));
				}
			}
		}
		$result = $this->pt->update_staff_response($id,$files_array);
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully Updated');
			redirect('parent_ticketing/view_assigned_tickets');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
			redirect('parent_ticketing/view_assigned_tickets');
		}
	}

	public function submit_parent_response($id) {
		if (!$this->settings->isParentModuleEnabled('PARENT_TICKETING')) {
			redirect('dashboard', 'refresh');
		}
		// $files = $_FILES['attachments'];
		$files_array = array();
		$files_string = '';
		if(isset($_FILES['attachments'])) {
			foreach ($_FILES['attachments']['name'] as $key => $file_name) {
				
				$file = array(
					'name' => $file_name,
					'type' => $_FILES['attachments']['type'][$key],
					'tmp_name' => $_FILES['attachments']['tmp_name'][$key],
					'error' => $_FILES['attachments']['error'][$key],
					'size' => $_FILES['attachments']['size'][$key]
				);
				
				$path = $this->__s3FileUpload($file);
				
				if($path['file_name'] != '') {
					array_push($files_array, array('name' => $file_name, 'path' => $path['file_name']));
						
				}
			}
		}
		$result = $this->pt->update_parent_response($id,$files_array);
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully Updated');
			redirect('parent_ticketing/parent_index');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
			redirect('parent_ticketing/parent_index');
		}
	}

	public function parent_view_ticket($id = 1) {
		if (!$this->settings->isParentModuleEnabled('PARENT_TICKETING')) {
			redirect('dashboard', 'refresh');
		}
		$data['ticket'] = $this->pt->get_ticket_by_id($id);
		$data['main_content'] = 'ticketing/parent/edit';
		$this->load->view('inc/template', $data);
	}

	public function category_index() {
		$data['category_list'] = $this->pt->get_ticket_categories();
		$data['main_content'] = 'ticketing/staff/category_index';
		$this->load->view('inc/template', $data);
	}

	public function add_category () {
		$data['staff_list'] = $this->pt->get_staff_list();
		$data['main_content'] = 'ticketing/staff/add_category';
		$this->load->view('inc/template', $data);
	}

	public function edit_category($id){
		$data['edit_category'] = $this->pt->edit_categorybyId($id);
		// echo "<pre>"; print_r($data); die();
		$data['main_content'] = 'ticketing/staff/edit_category';
		$this->load->view('inc/template', $data);
	}

	public function update_category($id) {
		$update = $this->pt->update_category_byId($id);
		if ($update) {
			$category = $this->input->post('category_name');
			$description = $this->input->post('description');
			$default_assignee_type = $this->input->post('default_assignee_type');
		
				$this->session->set_flashdata('flashSuccess', 'Successfully updated.');
				redirect('parent_ticketing/category_index');
		} else {
				$this->session->set_flashdata('flashError', 'Something Wrong.');
				redirect('parent_ticketing/category_index');
		}
	}

	public function delete_category() {
		$delete = $this->pt->delete_category($_POST['id']);
		if ($delete) {
				$this->session->set_flashdata('flashSuccess', 'Successfully Deleted.');
				redirect('parent_ticketing/category_index');
		} else {
				$this->session->set_flashdata('flashError', 'Something Wrong.');
				redirect('parent_ticketing/category_index');
		}
	}
	
	public function submit_new_category () {
		if (!$this->settings->isParentModuleEnabled('PARENT_TICKETING')) {
			redirect('dashboard', 'refresh');
		}
		$result = $this->pt->add_new_category();
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Category Successfully Added');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong...');
		}
		redirect('parent_ticketing/category_index');
	}

	public function issue_raise_update($id)
	{
		if (!$this->settings->isParentModuleEnabled('PARENT_TICKETING')) {
			redirect('dashboard', 'refresh');
		}
		$result = $this->parent_model->update_parent_issue_raise($id);
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully Updated');
			redirect('parent_controller/issue_raise_parent');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
			redirect('parent_controller/issue_raise_parent');
		}
	}

	public function issue_raise_delete($id)
	{
		if (!$this->settings->isParentModuleEnabled('PARENT_TICKETING')) {
			redirect('dashboard', 'refresh');
		}
		$result = $this->parent_model->delete_parent_issue_raise($id);
		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully deleted');
			redirect('parent_controller/issue_raise_parent');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
			redirect('parent_controller/issue_raise_parent');
		}
	}

	public function view_remarks_issues($id) {
		if (!$this->settings->isParentModuleEnabled('PARENT_TICKETING')) {
			redirect('dashboard', 'refresh');
		}
		$data['view_issue'] = $this->parent_model->fetch_issue_raise_id($id);
		$data['main_content'] = 'parent/issue_raise/view_remarks';
		$this->load->view('inc/template', $data);
	}
	public function add_tickting() {
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['staff_list'] = $this->pt->get_staff_list_search_expense();
		$data['category_list'] = $this->pt->get_ticket_categories();
		$ticket_types = $this->settings->getSetting('parent_ticketing_types');
		$data['ticket_types'] = [];
		if($ticket_types) {
			$data['ticket_types'] = json_decode($ticket_types);
		}
		$data['main_content'] = 'ticketing/staff/add_tickets';
		$this->load->view('inc/template', $data);
	}

  public function get_class_section_wise_std_data(){
    $cls_section = $this->input->post('cls_section');
    list($class_id,$sectionId) = explode('_',$cls_section);
    $result = $this->pt->get_class_section_student_data($class_id,$sectionId);
    echo json_encode($result);
  }

  public function get_parent_detailsbystudentid(){
    $student_id = $_POST['student_id'];
    $result = $this->pt->get_parent_detailsbystudentid($student_id);
    echo json_encode($result);
  }

  public function submit_ticket_staff() {
		$student_id=$_POST["user_selection"];

		$files = $_FILES['attachments'];
		$files_array = array();
		$files_string = '';
		if(isset($_FILES['attachments'])) {
			foreach ($_FILES['attachments']['name'] as $key => $file_name) {
				$file = array(
					'name' => $file_name,
					'type' => $_FILES['attachments']['type'][$key],
					'tmp_name' => $_FILES['attachments']['tmp_name'][$key],
					'error' => $_FILES['attachments']['error'][$key],
					'size' => $_FILES['attachments']['size'][$key]
				);
				$path = $this->__s3FileUpload($file);
				if($path['file_name'] != '') {
					array_push($files_array, array('name' => $file_name, 'path' => $path['file_name']));
				}
			}
			// $files_string = implode(",", $files_array);
			if(!empty($files_array))
				$files_string = json_encode($files_array);
		}
		// echo '<pre>'; print_r($files_string); die();
		$result = $this->pt->submit_ticket_staff($files_string, $student_id);

		if ($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully submited');
			redirect('parent_ticketing/view_assigned_tickets');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
			redirect('parent_ticketing/view_assigned_tickets');
		}
	}

	public function parent_report($mode = 1) {
		if (!$this->authorization->isAuthorized('PARENT_TICKETING.MODULE')) {
			redirect('dashboard', 'refresh');
		}

		if ($this->authorization->isAuthorized('PARENT_TICKETING.TICKETING_ADMIN')) {
			//Do Nothing
		} else {
			//Show only their assigned tickets if *not* ticketing admin
			$mode = 2;
		}

		switch($mode){
			case 1:
				$data['list_issue'] = $this->pt->get_open_tickets();
				$data['line'] = " - Open Tickets (" . count($data['list_issue']) . ")";
				$data['opencount'] = count($data['list_issue']);
				break;
			case 2:
				$staff_id = $this->authorization->getAvatarStakeHolderId();
				$data['list_issue'] = $this->pt->get_assigned_tickets_by_id($staff_id);
				$data['line'] = " - Assigned to me (" . count($data['list_issue']) . ")";
				break;
			case 3:
				$data['list_issue'] = $this->pt->get_all_tickets();
				$data['line'] = " - All Tickets (" . count($data['list_issue']) . ")";
				break;
		}
		$data['ticketcount'] = count($data['list_issue']);
		$data['is_ticketing_admin'] = $this->authorization->isAuthorized('PARENT_TICKETING.TICKETING_ADMIN');
		// echo '<pre>';print_r($data);die();
		$data['ticketsummary'] = $this->pt->get_ticket_summary_count();
		$data['staff_list'] = $this->pt->get_staff_list_for_ticketing();
		$data['category_list'] = $this->pt->get_category_list_for_ticketing();
		// $from_date = '2023-02-22';
		// $to_date = '2023-01-11';
		// $aasa = $this->pt->get_parent_ticket_closed_summary($from_date,$to_date );
		// echo "<pre>"; print_r($aasa);die();

		 // echo "<pre>"; print_r($data['category_list']); die();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'ticketing/staff/parent_report';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'ticketing/staff/parent_report';
        }else{
          $data['main_content'] = 'ticketing/staff/parent_report'; 	
        }
		$this->load->view('inc/template', $data);	
	}

	public function parent_ticket_staffwise_report(){
		$staffid = $_POST['staffid'];
		$staffwisedata = $this->pt->parent_ticket_staffwise_report();
		echo json_encode($staffwisedata);
	}

	 public function get_parent_ticket_open_summary(){
        // $bpId = $_POST['bpId'];
        $fromDate = $_POST['fromDate'];
        $toDate = $_POST['toDate'];
        $result = $this->pt->get_parent_ticket_open_summary($fromDate, $toDate);
        echo json_encode($result);
    }

    public function get_parent_ticket_closed_summary(){
        // $bpId = $_POST['bpId'];
        $fromDate = $_POST['fromDate'];
        $toDate = $_POST['toDate'];
        $result = $this->pt->get_parent_ticket_closed_summary($fromDate, $toDate);
        echo json_encode($result);
    }

  public function ticketing_staff_report(){
		$data['main_content'] = 'ticketing/staff/parent_ticket_staff_report';
		$this->load->view('inc/template', $data);	
	}

	public function student_report(){
		$data['classList'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
		$data['main_content'] = 'ticketing/staff/parent_ticket_student_report';
		$this->load->view('inc/template', $data);	
	}

	public function get_parent_ticket_student_history()
	{
		$user_selection = $_POST['user_selection'];
		$result = $this->pt->get_student_history($user_selection);
		echo json_encode($result);
	}

	public function get_parent_ticket_staff_report(){
		$ticekt_status = $_POST['ticekt_status'];
		$list = $this->pt->get_pt_staff_report_status_wise_chunk($ticekt_status);	
		$pt_ids = array_chunk($list, 150);
    echo json_encode($pt_ids);
	}

	public function get_parent_ticket_staff_report_data(){
		$ticket_ids = $_POST['ticket_ids'];
		$reslist = $this->pt->get_pt_staff_report_status_wise($ticket_ids);	
    echo json_encode($reslist);
	}


}