<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/*
|-----------------------------
| General Settings
|-----------------------------
|
| 'settings'   => General settings
|               Can be overriden with the template settings
|               or when calling the send() method
|
| Following configs tested working on gmail account
*/
$config['settings'] = array( 

    'from_email'    => getenv('FROM_EMAIL'),
    'smtp_user'     => getenv('SMTP_USER'),
    'smtp_pass'     => getenv('SMTP_PASS'),
    'from_name'     => getenv('FROM_NAME'),
    'smtp_host'     => getenv('SMTP_HOST'),
    'smtp_port'     => getenv('SMTP_PORT'),
    'protocol'      => 'smtp',
    'smtp_crypto'   => 'ssl',
    'mailtype'      => 'html',
    'charset'       => 'utf-8',
    'newline'       => "\r\n",
);



/*
|-----------------------------
| Templates location
|-----------------------------
|
| 'templates' = Folder located @ application/views/{}
|
| Each template created must have a config in templates
*/

$config['templates'] = 'emails';

/*
|-----------------------------
| Dev Email controls
|-----------------------------
|
|
| Stop remove actual users from DB and sent it to specified 
*/

$config['dev_email'] = '<EMAIL>';

/*
|-----------------------------
| Email Templates
|-----------------------------
|
| 'mailtype'    = Mail type, if not set will use general type
| 'charset'     = Charset, if not set will use general charset
| 'from_email'  = From email, if not set will use general email
| 'from_name'   = From name, if not set will use general name
| 'subject'     = Email Subject
| 'send_email'  = If false, it will send the email to site owner instead of actual user
| 'bcc_admin'   = Add the site admin as a BCC to the email
*/

$config['templates'] = array(
    // Demo
    'demo'   => array(
        'subject'    => 'Test Demo',
        'send_email' => true,
        'bcc_admin'  => false,
    ),
    'forgot_password'   => array(
        'subject'    => 'Forgot Password Reset',
        'send_email' => true,
        'bcc_admin'  => false,
    ),
);