<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  17 August 2022
 *
 * Description: Controller for Classroom Dashboard Module.
 *
 * Requirements: PHP5 or above
 *
 */

class Student_analytics extends CI_Controller {
  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/reports_model');
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_collection_model');
    $this->load->model('student/Student_analytics_model');
    $this->load->library('filemanager');

  }

  //Landing function to show non-compliance menu
  public function index($id = '',$class_id ='') {
    $data['getclassinfo'] = $this->Student_Model->getClassSectionNames();
    $data['all_names'] = $this->Student_analytics_model->get_all_staff_student_names();
    $custom = $this->settings->getSetting('student_admission_custom_fields');
    $custom_sy = $this->settings->getSetting('student_year_custom_fields');
    // echo "<pre>"; print_r($custom); die();
    $display_fields = $this->Student_Model->get_config_display_fields();
    $dbEnabed = [];
      foreach ($display_fields as $key => $enabled) {
        $dbEnabed = json_decode($enabled->value);
      }
    $data['display_enabled_fields'] = (array) $dbEnabed;
    if(empty($data['display_enabled_fields']['personal_info'])){
      $data['display_enabled_fields']['personal_info'] = array();
    }
    if(empty($data['display_enabled_fields']['school_info'])){
      $data['display_enabled_fields']['school_info'] = array();
    }
    if(empty($data['display_enabled_fields']['parent_info'])){
      $data['display_enabled_fields']['parent_info'] = array();
    }

    if(empty($data['display_enabled_fields']['guardian_info'])){
      $data['display_enabled_fields']['guardian_info'] = array();
    }
    $data['student_id'] = $id;
    $data['class_id'] = $class_id;

    $tempKey = [];
    if (!empty($custom)) {
      $data['chunkData'] = array_chunk($custom, 2);
      foreach ($custom as $key => $val) {
        $tempKey[$val] = $key;
      }
    }
    $tempKey_sy = [];
    if (!empty($custom_sy)) {
      $data['chunkData_sy'] = array_chunk($custom_sy, 2);
      foreach ($custom_sy as $key => $val) {
        $tempKey_sy[$val] = $key;
      }
    }
    // echo "<pre>"; print_r($custom_sy); die();
    $data['acad_years'] = $this->acad_year->getAllYearData();
    $data['acad_year_id'] = $this->acad_year->getAcadYearId();
    $data['custom'] = $tempKey;
    $data['custom_sy'] = $tempKey_sy;
    // echo "<pre>"; print_r($data['enabled_fields']); die();
    $data['teams'] =  $this->settings->getSetting('indus_single_window_approval_teams'); 
    //$data['main_content']    = 'student_analytics/index';
    if ($this->mobile_detect->isMobile()) {
      $data['student_id'] = $id;
      $data['class_id'] = $class_id;
      $data['main_content']    = 'student_analytics/stud_mobile_index';
    } else {
      $data['main_content']    = 'student_analytics/index';
    }
    $this->load->view('inc/template', $data);
  }
  public function mobile_tab_wise_data($tabname,$student_id,$class_id) {
    if ($tabname == 'personal_details') {
        $custom = $this->settings->getSetting('student_admission_custom_fields');
        $custom_sy = $this->settings->getSetting('student_year_custom_fields');
        $tempKey = [];
        if (!empty($custom)) {
          foreach ($custom as $key => $val) {
            $tempKey[$val] = $key;
          }
        }
        $tempKey_sy = [];
        if (!empty($custom_sy)) {
          foreach ($custom_sy as $key => $val) {
            $tempKey_sy[$val] = $key;
          }
        }
        $data['custom'] = $tempKey;
        $data['custom_sy'] = $tempKey_sy;
        $data['class_id'] = $class_id;
        $data['student_id'] = $student_id;
        $data['result'] = $this->Student_analytics_model->get_student_analytics_databyid($student_id);
        $data['main_content']    = 'student_analytics/profile_index';
    } elseif ($tabname == 'school_details') {
        $data['class_id'] = $class_id;
        $data['student_id'] = $student_id;
        $data['result'] = $this->Student_analytics_model->get_school_details($student_id);
        $data['main_content'] = 'student_analytics/school_details';
    } elseif ($tabname == 'documents') {
        $data['class_id'] = $class_id;
        $data['student_id'] = $student_id;
        $data['result'] = $this->Student_analytics_model->get_student_documents($student_id);
        // echo "<pre>"; print_r($data['result']); die();

        $data['main_content'] = 'student_analytics/stud_mobile_doc';
    }elseif ($tabname == 'prev_school_details') {
        $data['class_id'] = $class_id;
        $data['student_id'] = $student_id;
        $data['result'] = $this->Student_analytics_model->get_prev_schooling_details($student_id);
        // echo "<pre>"; print_r($data['result']); die();
        $data['main_content'] = 'student_analytics/stud_mobile_pre_school';
    }elseif ($tabname == 'student_medical_forms') {
        $data['class_id'] = $class_id;
        $data['student_id'] = $student_id;
        $data['result'] = $this->Student_analytics_model->get_student_medical_forms_details($student_id);
        // echo "<pre>"; print_r($data['result']); die();
        $data['main_content'] = 'student_analytics/stud_mobile_medical_form_details';
    }elseif ($tabname == 'fees') {
        $data['class_id'] = $class_id;
        $data['student_id'] =$student_id;
        $data['academic_year'] = $this->acad_year->getAcadYearId();
        $data['result'] = $this->Student_analytics_model->get_fee_detailed_transaction($student_id, $data['academic_year']);
        $data['main_content'] =  'student_analytics/stud_mob_fees';
    }
    elseif ($tabname == 'examination') {
       $data['class_id'] = $class_id;
        $data['student_id'] =$student_id;
        $data['main_content'] =  'student_analytics/stud_mobile_examination';
    } elseif ($tabname == 'student_consent_forms') {
        $data['class_id'] = $class_id;
        $data['student_id'] =$student_id;
        $data['result'] = $this->Student_analytics_model->get_student_consent_forms_details($student_id);
        // echo "<pre>"; print_r($data['result']); die();
        $data['main_content'] =  'student_analytics/stud_consent_form';
    }elseif ($tabname == 'circular') {
        $data['class_id'] = $class_id;
        $data['student_id'] =$student_id;
        $data['result'] = $this->Student_analytics_model->get_student_circular($student_id);
        $data['main_content'] = 'student_analytics/stud_mobile_circular';
    } elseif ($tabname == 'sms') {
        $data['class_id'] = $class_id;
        $data['student_id'] = $student_id;
        $data['academic_year'] = $this->acad_year->getAcadYearId();
        $data['result'] = $this->Student_analytics_model->get_student_sms($student_id, $data['academic_year']);
        $data['main_content'] = 'student_analytics/stud_mobile_sms';
    }elseif ($tabname == 'observation') {
        $data['class_id'] = $class_id;
        $data['student_id'] = $student_id;
        $data['academic_year'] = $this->acad_year->getAcadYearId();
        $data['result'] = $this->Student_analytics_model->get_student_observation($student_id, $class_id, $data['academic_year']);
        $data['main_content'] = 'student_analytics/stud_mobile_observation';
    } elseif ($tabname == 'non_compliance') {
        $data['class_id'] = $class_id;
        $data['student_id'] = $student_id;
        $data['result'] = $this->Student_analytics_model->get_student_non_compliance($student_id);
        $data['main_content'] = 'student_analytics/stud_mobile_noncompliance';
    } elseif ($tabname == 'transportation') {
        $data['class_id'] = $class_id;
        $data['student_id'] = $student_id;
        $data['result'] = $this->Student_analytics_model->get_transposrtation_detailset($student_id);
        $data['main_content'] = 'student_analytics/stud_mobile_transportation';
    } elseif ($tabname == 'attendance') {
        $data['class_id'] = $class_id;
        $data['main_content'] = 'student_analytics/stud_mobile_attendance';
         $data['student_id'] = $student_id;
    } elseif ($tabname == 'academicanalysis') {
        $data['class_id'] = $class_id;
        $data['student_id'] = $student_id;
        $data['main_content'] = 'student_analytics/academic_analysis_mob';
    } elseif ($tabname == 'student_health') {
        $data['class_id'] = $class_id;
        $data['student_id'] = $student_id;
        $data['main_content'] = 'student_analytics/stud_mobile_health';
    }
    $this->load->view('inc/template',$data);
  }
  public function get_mobile_subject_wise_attendance() {
    $from_date =$_POST['from_date'];
    $to_date =$_POST['to_date'];
    $student_id =$_POST['student_id'];
    $result =$this->Student_analytics_model->get_subject_wise_attendance($from_date,$to_date,$student_id);
    echo json_encode($result);

  }
  public function get_student_by_class_section(){
    $classSectionId = $this->input->post('classSectionId');
    $result = $this->Student_Model->get_student_by_class_sectionbyid($classSectionId);
    echo json_encode($result);
  }

  public function get_student_databyid(){
    $selectStudent = $_POST['student_id'];
    $result = $this->Student_analytics_model->get_student_analytics_databyid($selectStudent);
    
    if (!empty($result->category)) {
      $categories = $this->settings->getSetting('category');
  
      if (!empty($categories) && isset($categories[$result->category])) {
          $result->category = $categories[$result->category];
      } else {
          $result->category = 'NA'; 
      }
  }
    if($result->father->dob == '01-Jan-1970'){
      $result->father->dob = '';
    }
    if($result->mother->dob == '01-Jan-1970'){
      $result->mother->dob = '';
    }
    // echo '<pre>';print_r($result);die();

    $display_fields = $this->Student_Model->get_config_display_fields();
    $dbEnabed = [];
    foreach ($display_fields as $key => $enabled) {
      $dbEnabed = json_decode($enabled->value);
    }
    $data['display_enabled_fields'] = (array) $dbEnabed;
    if(empty($data['display_enabled_fields']['personal_info'])){
      $data['display_enabled_fields']['personal_info'] = array();
    }
    if(empty($data['display_enabled_fields']['parent_info'])){
      $data['display_enabled_fields']['parent_info'] = array();
    }
    if(empty($data['display_enabled_fields']['guardian_info'])){
      $data['display_enabled_fields']['guardian_info'] = array();
    }
    if(empty($data['display_enabled_fields']['driver_info'])){
      $data['display_enabled_fields']['driver_info'] = array();
    }

    $permited_personal_information = ['aadhar_no','email','student_mobile_no','sibling1_mobile_num','mobile_no','annual_income','caste','student_sub_caste','category','student_whatsapp_num','alternate_email_id','whatsapp_num','pan_number','bank_account_num'];
    if(!$this->authorization->isAuthorized('STUDENT_360.PERSONAL_DETAILS_EXTENDED')){
      $data['display_enabled_fields']['personal_info'] = array_diff($data['display_enabled_fields']['personal_info'], $permited_personal_information);
      $data['display_enabled_fields']['parent_info'] = array_diff($data['display_enabled_fields']['parent_info'], $permited_personal_information);
      $data['display_enabled_fields']['guardian_info'] = array_diff($data['display_enabled_fields']['guardian_info'], $permited_personal_information);
      $data['display_enabled_fields']['driver_info'] = array_diff($data['display_enabled_fields']['driver_info'], $permited_personal_information);
    }
    $result->family_picture_url = '';
    if($result->family_picture_url !=''){
      $result->family_picture_url =$this->filemanager->getFilePath($result->family_picture_url);
    }
    if($result->father->picture_url !=''){
      $result->father->picture_url = $this->filemanager->getFilePath($result->father->picture_url);
    }
    if($result->mother->picture_url !=''){
      $result->mother->picture_url =$this->filemanager->getFilePath($result->mother->picture_url);
    }

    if(!empty($result->guardians->picture_url)){
      $result->guardians->picture_url =$this->filemanager->getFilePath($result->guardians->picture_url);
    }

    if(!empty($result->guardians2->picture_url)){
      $result->guardians2->picture_url =$this->filemanager->getFilePath($result->guardians2->picture_url);
    }

    if(!empty($result->driver->picture_url)){
      $result->driver->picture_url =$this->filemanager->getFilePath($result->driver->picture_url);
    }

    if(!empty($result->driver2->picture_url)){
      $result->driver2->picture_url =$this->filemanager->getFilePath($result->driver2->picture_url);
    }  

    if($result->picture_url !=''){
      $result->picture_url =$this->filemanager->getFilePath($result->picture_url);
    }else{
      $result->picture_url = '';
    }

    $chunkfield =[];
    $fatherfield =[];
    $guardianfield = [];
    $driverfield = [];
    if(!empty($data['display_enabled_fields']['personal_info'])){
      $chunkfield = array_chunk($data['display_enabled_fields']['personal_info'], 2);
    }

    if(!empty($data['display_enabled_fields']['parent_info'])){
      $fatherfield = array_chunk($data['display_enabled_fields']['parent_info'], 2);
    }

    if(!empty($data['display_enabled_fields']['guardian_info'])){
      $guardianfield = array_chunk($data['display_enabled_fields']['guardian_info'], 2);
    }
    if(!empty($data['display_enabled_fields']['driver_info'])){
      $driverfield = array_chunk($data['display_enabled_fields']['driver_info'],2);
    }

    echo json_encode(array('student_data'=>$result, 'enabled_field'=>$chunkfield, 'f_enabled_field'=>$fatherfield, 'g_enabled_field'=>$guardianfield, 'd_enabled_field'=>$driverfield));

  }
  public function get_school_details()
  {
    $studentid = $_POST['student_id'];
    $result = $this->Student_analytics_model->get_school_details($studentid);
    if(isset($result->admission_acad_year_id)){
      $result->admission_acad_year_id = $this->acad_year->getAcadYearById($result->admission_acad_year_id);
    }
    if($result->date_of_joining == '01-Jan-1970'){
      $result->date_of_joining = '-';
    }
    if(isset($result->student_house) && empty($result->student_house)){
      $result->student_house = '-';
    }

    // echo json_encode($result);
    $display_fields = $this->Student_Model->get_config_display_fields();
    $dbEnabed = [];
    foreach ($display_fields as $key => $enabled) {
      $dbEnabed = json_decode($enabled->value);
    }
    $data['display_enabled_fields'] = (array) $dbEnabed;
    if(empty($data['display_enabled_fields']['school_info'])){
      $data['display_enabled_fields']['school_info'] = array();
    }else {
      $data['display_enabled_fields']['school_info'] = array_diff(
          $data['display_enabled_fields']['school_info'],
          ['combination_id']
      );
    }
    $schoolfield = array_chunk($data['display_enabled_fields']['school_info'], 2);

    echo json_encode(array('student_data'=>$result, 'enabled_field'=>$schoolfield));

  }
  
  public function get_student_documents()
  {
    $student_id = $_POST['student_id'];
    $result = $this->Student_analytics_model->get_student_documents($student_id);
    
    echo json_encode($result);
  }

  public function get_prev_schooling_details() {
    $student_id = $_POST['student_id'];
    $result = $this->Student_analytics_model->get_prev_schooling_details($student_id);

    echo json_encode($result);
  }

  public function student_documents_download($student_doc_id, $student_id)
  {
    $document_obj = $this->Student_analytics_model->get_document_row($student_doc_id);
    $document_url = $this->filemanager->getFilePath($document_obj->document_url);
    $document_data = file_get_contents($document_url);
    $pdf_name = $this->Student_analytics_model->get_student_name_by_id($student_id) . "_" . $document_obj->document_type;
    $this->load->helper('download');
    force_download($pdf_name . '.pdf', $document_data, TRUE);
  }
  public function get_student_sms()
  {
    $student_id = $_POST['student_id'];
    $data['academic_year'] = $this->acad_year->getAcadYearId();
    //$parentId = $this->authorization->getAvatarStakeHolderId();
    //
    $result = $this->Student_analytics_model->get_student_sms($student_id, $data['academic_year']);
    echo json_encode($result);
  }
  public function get_circular()
  {
    $student_id = $_POST['student_id'];
    $result = $this->Student_analytics_model->get_student_circular($student_id);
    echo json_encode($result);
  }
  public function get_student_observation()
  {
    $student_id = $_POST['student_id'];
    $class_id =$_POST['class_id'];
    $data['academic_year'] = $this->acad_year->getAcadYearId();
    $result = $this->Student_analytics_model->get_student_observation($student_id, $class_id, $data['academic_year']);
    echo json_encode($result);

  }
 
  public function get_student_non_compliance()
  {
    $student_id = $_POST['student_id'];
    $result= $this->Student_analytics_model->get_student_non_compliance($student_id);
    // echo $students_id;
    echo json_encode($result);
  }

  public function fee_detailed_summary() {
    $student_id = $_POST['student_id'];
    //$stdId = $_POST['stdId'];
    //$acadyearId = $_POST['acadyearId'];
    $data['academic_year'] = $this->acad_year->getAcadYearId();
    $result = $this->Student_analytics_model->get_fee_detailed_transaction($student_id, $data['academic_year']);
    echo json_encode($result);

  }

  public function download_fee_recipt_by_rowid(){
    $student_id = $_POST['student_id'];
    $data['excess_amount'] = $this->fees_collection_model->get_over_all_additional_amount($student_id);
    $data['refund'] = $this->authorization->isAuthorized('FEESV2.REFUND');

    $data['fee_data'] = $this->reports_model->fee_student_history_data_by_stdbyId($student_id);

    $fee_blueprints = $this->fees_student_model->get_blueprintsv1();
    // $acad_year_id = $this->acad_year->getAcadYearId();
    foreach ($fee_blueprints as &$fbp) {
      //Assign cohorts if not assigned yet
      // $acad_year_id = $fbp->acad_year_id;
      $fbp->std_fee = $this->fees_collection_model->fee_student_fee_details($student_id, $fbp->id);
      $fbp->compCount = $this->fees_collection_model->fee_total_no_of_components($fbp->id);
      $fbp->concession_adjustment = $this->fees_student_model->get_concession_adjustment_amount($student_id, $fbp->id);
      if (!empty($fbp->std_fee)) {
        $fbp->installments = $this->fees_collection_model->get_installments_all_history($fbp->std_fee->feev2_blueprint_installment_types_id);
        $fbp->history = $this->fees_collection_model->fee_student_fee_history($fbp->std_fee->stdSchId);
      }
    }
    $data['refund'] = $this->authorization->isAuthorized('FEESV2.REFUND');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['fee_refund_amount'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['delete_authorization'] = $this->authorization->isAuthorized('FEESV2.SOFT_DELETE_RECEIPTS');
    // $data['student'] = $this->fees_student_model->get_std_detailsbyId($std_id, $acad_year_id);
    $data['fee_history'] = $fee_blueprints;
    echo json_encode($data);
  }   

  public function get_transposrtation_details()
  {
    $student_id = $_POST['student_id'];
    $result = $this->Student_analytics_model->get_transposrtation_detailset($student_id);
    echo json_encode($result);
  }

  public function get_student_health_details()
  {
    $student_id = $_POST['student_id'];
    $disabled_fields = $this->Student_analytics_model->get_health_disabled_fields();
    $result=$this->Student_analytics_model->whole_medical_history_360($student_id);
    // $healthfields = array_chunk($result, 2);
    // echo "<pre>"; print_r($disabled_fields);
    // echo "<pre>"; print_r($result);
    //  die();
    echo json_encode(array('result'=>$result, 'enabled'=>$disabled_fields));
  }

  public function get_student_consent_forms_details() {
    $student_id = $_POST['student_id'];
    $result=$this->Student_analytics_model->get_student_consent_forms_details($student_id);
    echo json_encode($result);
  }


  public function get_student_medical_forms_details() {
    $student_id = $_POST['student_id'];
    $result=$this->Student_analytics_model->get_student_medical_forms_details($student_id);
    echo json_encode($result);
  }

  public function student_medical_full_details() {
    $id = $_POST['id'];
    $student_id = $_POST['student_id'];
    $result = $this->Student_analytics_model->student_medical_full_details($student_id,$id);
    echo json_encode($result);
  }

  public function get_subject_wise_attendance() {
    $from_date =$_POST['from_date'];
    $to_date =$_POST['to_date'];
    $student_id =$_POST['student_id'];
    $result =$this->Student_analytics_model->get_subject_wise_attendance($from_date,$to_date,$student_id);
    echo json_encode($result);
  }

  public function getStudentPerformanceData() {
    $acad_year_id = $_POST['acad_year'];
    
    $studentId = $_POST['student_id'];

    $stdData = $this->Student_Model->get_std_class_section_id($studentId, $acad_year_id);
    if($stdData != '') {
      $classId = $stdData[0]->class_id;
      $sectionId = $stdData[0]->class_section_id; 
    } else {
      $classId = 0;
      $sectionId = 0;
    }
    $assessments = $this->Student_Model->getAssessments($classId, $sectionId, $acad_year_id);
    $assIds = array();
    foreach ($assessments as $key => $value) {
      array_push($assIds, $value->assId);
    }
    $data['subWiseStatus'] = array();
    $data['assessments'] = array();
    
    $subWiseData = $this->Student_Model->getSubWiseReport($classId, $studentId, $assIds);
    if(!empty($subWiseData)) {
      $subWiseAvgData = $this->Student_Model->getSubWiseAvgReport($classId, $studentId, $assIds);

      $subWise = array();
      foreach ($subWiseData as $key => $value) {
        foreach ($subWiseAvgData as $k => $avg) {
          if($value->gId == $avg->gId && $value->assId == $avg->assId) {
            // echo "<pre>"; print_r($value);print_r($avg);die();
            $percentage = ($value->tMarks > 0) ? round(($value->marks/$value->tMarks)*100) : 0;
            $avgPercent = ($avg->tMarks > 0) ? round(($avg->marks/$avg->tMarks)*100) : 0;
            if(!array_key_exists($value->entity_name, $subWise)) {
              $subWise[$value->entity_name] = array();
              $subWise[$value->entity_name]['name'] = $value->entity_name;
              $subWise[$value->entity_name]['subId'] = $value->gId;
              $subWise[$value->entity_name]['total'] = 0;
              $subWise[$value->entity_name]['aboveAvg'] = 0;
              $subWise[$value->entity_name]['belowAvg'] = 0;
              $subWise[$value->entity_name]['Average'] = 0;
            }
            $subWise[$value->entity_name]['total']++;
            /*if($value->entity_name == 'HPE')
              echo $percentage.' - '.$avgPercent.'<br>';*/
            if($percentage == $avgPercent) {
              $subWise[$value->entity_name]['Average']++;
            } else if($percentage > $avgPercent) {
              $subWise[$value->entity_name]['aboveAvg']++;
            } else if($percentage < $avgPercent) {
              $subWise[$value->entity_name]['belowAvg']++;
            }
          }
        }
      }
      $statusWise = array();
      foreach ($subWise as $key => &$value) {
        if($value['total'] == $value['aboveAvg']) {
          $value['status'] = 'Above Average';
        }
        // echo "<pre>";
        // print_r(&$value);
        // die();
        else if($value['total'] == $value['belowAvg']) {
          $value['status'] = 'Below Average';
        }
        else if($value['total'] == $value['Average']) {
          $value['status'] = 'Average';
        }
        else if(($value['belowAvg'] + $value['Average']) >= $value['aboveAvg']) {
          if($value['Average'] > $value['belowAvg'])
            $value['status'] = 'Average';
          else 
            $value['status'] = 'Below Average';
        }
        else if(($value['belowAvg'] + $value['Average']) < $value['aboveAvg']) {
            $value['status'] = 'Above Average';
        }

        $statusWise[$value['status']][] = $value;
      }
      ksort($statusWise);
      $data['subWiseStatus'] = $statusWise;
      $data['assessments'] = $assessments;
    }

    echo json_encode($data);
  }

  public function getMarksAnalysis()
  {
    
    $assIds = $_POST['assIds'];
    $subject = $_POST['subject'];
    $stdId = $_POST['stdId'];
    // $section_id = $_POST['section_id'];

    $acad_year_id = $_POST['acad_year'];
    $stdData = $this->Student_Model->get_std_class_section_id($stdId, $acad_year_id);
    if($stdData != '') {
      $section_id = $stdData[0]->class_section_id; 
    } else {
      return;
    }

    $data = $this->Student_Model->getMarksDataBySubject($assIds, $subject, $stdId);
    $avgData = $this->Student_Model->getClassAvgBySubject($assIds, $subject, $section_id);
    $marksData = array();
    foreach ($data as $key => $value) {
      $percentage = round(($value->marks / $value->tMarks) * 100, 2);
      $total_without_absenties = $avgData[$key]->tMarks - ($avgData[$key]->total_marks * $avgData[$key]->absent);
      $avgPercent = round(($avgData[$key]->marks / $total_without_absenties) * 100, 2);
      $marksData[] = array('assName' => $value->short_name, $value->entity_name => $percentage, 'Average' => $avgPercent);
    }
    // echo 'sssss'. $subject."ddddddddddddd<br><pre>";print_r($marksData); die();
    echo json_encode($marksData);
  }

  public function student_vaccination_history_details() {
     $student_id = $_POST['student_id'];
    $result=$this->Student_analytics_model->student_vaccination_history_details($student_id);


    echo json_encode($result);
    
  }

  public function get_student_attendance_current_month_calendar($student_biometric_code){
    $result = $this->Student_analytics_model->get_attendence_data( $student_biometric_code);
    $data = array();
    foreach ($result as $key => $val){
      $month  = $val->month;
      $year   = $val->year;
        $timezone= local_time($val->punch_time);
        $text = date('h:i A', strtotime($timezone));
        $date=$val->date;
        $data[] = array('title'=>$text,'start'=>$year.'-'.$month.'-'.$date, 'end'=>$year.'-'.$month.'-'.$date,'friendly_name'=>$val->device_friendly_name);
    }
// echo "<pre>"; print_r($data); die();

    echo json_encode($data);
  }

  public function student_details_by_name($name) {
        $data['students']= $this->Student_analytics_model->get_students_by_name($name);
        // echo "<pre>"; print_r($data['students']); die();
        if($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'student_analytics/students_name_list';
        }
        $this->load->view('inc/template', $data);
    }

     public function get_ids_from_rfid(){
        $student_ids = $_POST['student_ids'];
        $res = $this->Student_analytics_model->get_student_ids_from_rfid($student_ids);
        echo json_encode($res);
    }

    public function rfid_student_display($rfid_number){
      $res = $this->Student_analytics_model->get_ids_from_rfid($rfid_number);
      $data['std_ids'] = $res;
      // echo "<pre>"; print_r($data['std_ids']); die();
      if(count($res) > 1){
        $data['main_content']    = 'student_analytics/student_rfid_list';
        $this->load->view('inc/template', $data);
      }else{
        $result = $this->Student_analytics_model->get_ids_from_rfid_single($res[0]);
        redirect('student_analytics/student_analytics/index/'.$result->id.'/'.$result->class_section_id);
      }
      
    }

    public function student_rfid(){
      $data['main_content']    = 'student_analytics/student_rfid_mobile';
      $this->load->view('inc/template', $data);
    }

    public function submit_approval_process(){
      // $this->fees_collection_model->store_fees_edit_history($_POST['student_id'],'Single window '.$_POST['status'].'. Remarks : '.$_POST['fees_approval_remarks']);
      echo $this->Student_analytics_model->single_window_approval($_POST);
    }

    public function get_approval_process_details(){
      $result = $this->Student_analytics_model->get_approval_process_details($_POST['student_id']);
      echo json_encode($result);
    }

    public function student_qr_code_scan(){
        $data['main_content'] = 'student_analytics/scan_qr_code';
        $this->load->view('inc/template', $data);
    }

    public function student_qr_code_scan_get_rfidnumber(){
        $input_qr_code = $_POST['content'];
        echo json_encode($this->Student_analytics_model->get_student_details_from_qr_code($input_qr_code));
    }

}
?>