<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  12 March 2018
 *
 * Description: Model class for Class Master table
 *
 * Requirements: PHP5 or above
 *
 */

class class_master extends CI_Controller {
	function __construct() {
		  parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      //This should come up only for super admins
      if (!$this->authorization->isSuperAdmin()) {
        redirect('dashboard', 'refresh');
      }
      $this->load->model('class_master_model');
      $this->config->load('form_elements');
	}

  //Landing function to display classes
  public function index() {
    $data['classlist']=$this->class_master_model->get_list_class_sections();
    $data['placeHolderAdded'] = 0;
    foreach ($data['classlist'] as $key => $value) {
      if($value->is_placeholder == 1) {
        $data['placeHolderAdded'] = 1;
      }
    }
    // echo $data['placeHolderAdded'];die();
    $promotionAcadYearId = $this->acad_year->getPromotionAcadYearId();
    $data['promotionClasses'] = $this->class_master_model->get_classes($promotionAcadYearId);
    $data['main_content']    = 'master/class/index';
    $this->load->view('inc/template', $data);
    
  }
  public function indexv2() {
    $data['classlist']=$this->class_master_model->get_list_class_sections();
    $data['placeHolderAdded'] = 0;
    foreach ($data['classlist'] as $key => $value) {
      if($value->is_placeholder == 1) {
        $data['placeHolderAdded'] = 1;
      }
    }
    // echo $data['placeHolderAdded'];die();
    $promotionAcadYearId = $this->acad_year->getPromotionAcadYearId();
    $data['promotionClasses'] = $this->class_master_model->get_classes($promotionAcadYearId);
    $data['main_content']    = 'school/class/class_section';
    $this->load->view('inc/template', $data);
    
  }

  public function add_class() { 
    $data['board']       =  $this->settings->getSetting('board');
    $data['medium']       =  $this->settings->getSetting('medium');
    $data['classTypes']       =  $this->settings->getSetting('classType');
    $data['main_content'] = 'master/class/add_class';
    $this->load->view('inc/template', $data);
  }

  public function submit_class() {
    $result=$this->class_master_model->submit_data_class();
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Submit Class.');
      redirect('class_master');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('class_master');
    }
  }

  public function add_section($id) { 
    $data['class_details'] = $this->class_master_model->getClassDetailsById($id);
    $data['sections'] = $this->class_master_model->get_sections();
    $data['main_content']    = 'master/class/add_sections';
    $this->load->view('inc/template', $data);
  }

  public function submit_section($class_id) {
    $result=$this->class_master_model->submit_data_section($class_id);
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Submit Class.');
      redirect('class_master');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('class_master');
    }
  }

  public function add_periods($id) { 
    $data['classDetail'] = $this->class_master_model->getClassDetailsById($id);
    $data['main_content'] = 'master/class/add_periods';
    $this->load->view('inc/template', $data);
  }

  public function submit_periods($class_id) {
    $result=$this->class_master_model->submit_periods($class_id);
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Submit Class.');
      redirect('class_master');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('class_master');
    }
  }

  public function clone_year_cls(){
    $yClass = $_POST['yClass'];
    $result = $this->class_master_model->get_year_wise_classData($yClass);
    echo json_encode($result);
  }
  public function clone_year_clsv2(){
    $yClass = $_POST['yClass'];
    $result = $this->class_master_model->get_year_wise_classDatav2($yClass);
    echo json_encode($result);
  }


  public function clone_class_master(){
    $result = $this->class_master_model->insert_clone_class_data();
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Clone Class data Successfully.');
      redirect('class_master');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('class_master');
    }
  }
  public function clone_class_masterv2(){
    $result = $this->class_master_model->insert_clone_class_datav2();
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Clone Class data Successfully.');
      redirect('class_master/indexv2');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('class_master/indexv2');
    }
  }

  public function addPlaceholderSection() {
    echo $this->class_master_model->addPlaceholderSection();
  }

  public function addPromotionClass() {
    $promoClass = $_POST['promo_class_id'];
    $classId = $_POST['classId'];
    echo $this->class_master_model->addPromoClass($classId, $promoClass);
  }
}
?>
