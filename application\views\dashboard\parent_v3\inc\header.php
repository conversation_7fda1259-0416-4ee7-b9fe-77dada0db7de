<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title><?php echo isset($page_title) ? $page_title : 'Parent Dashboard'; ?> - School Management</title>
      <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#7b5cff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Parent Dashboard">
    <!-- Latest Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Latest Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #7b5cff;
            --secondary-color: #4e8cff;
            --success-color: #00b894;
            --info-color: #36b9cc;
            --warning-color: #fdcb6e;
            --danger-color: #e74c3c;
            --light-bg: #f7f8fa;
            --white: #ffffff;
            --text-dark: #333333;
            --text-muted: #888888;
            --border-light: #e0e0e0;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Nunito', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: var(--light-bg);
            margin: 0;
            padding: 0;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Mobile-first responsive design */
        .container-mobile {
            max-width: 100%;
            margin: 0 auto;
            padding: 0;
        }

        /* Remove default Bootstrap margins and paddings for mobile */
        .container-fluid {
            padding: 0;
        }

        /* Hide desktop elements on mobile */
        .desktop-only {
            display: none;
        }

        /* Mobile navigation styles */
        .mobile-nav {
            display: block;
        }

        /* Responsive breakpoints */
        @media (min-width: 600px) {
            .container-mobile {
                max-width: 440px;
            }
        }

        @media (min-width: 768px) {
            .desktop-only {
                display: block;
            }

            .mobile-nav {
                display: none;
            }
        }

        /* Loading spinner */
        .loading-spinner {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
            display: none;
        }

        .spinner-border-custom {
            width: 3rem;
            height: 3rem;
            border: 0.3em solid rgba(123, 92, 255, 0.2);
            border-top: 0.3em solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        /* Utility classes */
        .text-primary-custom {
            color: var(--primary-color) !important;
        }

        .bg-primary-custom {
            background-color: var(--primary-color) !important;
        }

        .border-primary-custom {
            border-color: var(--primary-color) !important;
        }

        /* Safe area for iOS devices */
        @supports (padding: max(0px)) {
            body {
                padding-bottom: max(0px, env(safe-area-inset-bottom));
            }
        }
    </style>

    <style>
    body {
        background: #F9F7FE;
        font-family: 'Inter', 'Nunito', Arial, sans-serif;
        margin: 0;
        padding: 0;
    }
    .dashboard-header {
        padding: 1rem 1rem 0.5rem 1rem;
        border-bottom-left-radius: 18px;
        border-bottom-right-radius: 18px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: sticky;
        top: 0;
        z-index: 100;
    }
    .dashboard-header .school-logo {
        height: 48px;
        width: auto;
    }
    .dashboard-avatar {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #e0e0e0;
        cursor: pointer;
    }
    .dashboard-avatar-footer {
       width: 24px;
        height: 24px;
        flex-shrink: 0;
        aspect-ratio: 2/3;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #e0e0e0;
        cursor: pointer;
    }
    .school-name {
        font-size: 1rem;
        font-weight: 600;
        color: #333;
        flex: 1;
        margin-left:1rem;
    }
    .welcome-section {
        padding: 1rem 1rem 0 1rem;
    }
    .welcome-title {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 0.2rem;
        color: #333;
    }
    .welcome-sub {
        color: #888;
        font-size: 1rem;
    }
    .fee-card {
        background: linear-gradient(90deg, #7b5cff 0%, #4e8cff 100%);
        color: #fff;
        border-radius: 16px;
        padding: 1rem;
        margin: 1rem;
        box-shadow: 0 4px 16px rgba(123,92,255,0.08);
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        transition: transform 0.2s;
    }
    .fee-card:hover {
        transform: translateY(-2px);
    }
    .fee-card .fa {
        font-size: 2rem;
        opacity: 0.2;
    }
    .fee-card.no-due {
        background: linear-gradient(90deg, #00b894 0%, #00cec9 100%);
    }
    .fee-card.overdue {
        background: linear-gradient(90deg, #e74c3c 0%, #fd79a8 100%);
    }
    .feature-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1.1rem 1rem;
        padding: 0.5rem 1rem 1rem 1rem;
    }
   .feature-tile {
        background: #fff;
        border-radius: 14px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 1.5rem 1rem 1.5rem 1rem;
        position: relative;
        min-width: 70px;
        min-height: 90px;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        color: inherit;
    }
    .feature-tile:hover {
        box-shadow: 0 4px 16px rgba(123,92,255,0.10);
        transform: translateY(-2px);
    }
    .feature-tile .fa, .feature-tile img {
        font-size: 1.7rem;
        margin-bottom: 0.3rem;
    }
    .feature-grid a{
        text-decoration: none;
    }
    .feature-tile svg{
        width:48px !important;
        height:48px !important;
    }
    .feature-label {
        color: #000;
        text-align: center;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        letter-spacing: -0.28px;
    }

.feature-icon {
    display: flex;
    justify-content: center;
    align-items: center;
}

   
    .badge-notify {
        position: absolute;
        top: 8px;
        right: 18px;
        background: #ff3b30;
        color: #fff;
        font-size: 0.75rem;
        border-radius: 50%;
        padding: 0.18em 0.5em;
        font-weight: 700;
        min-width: 22px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(255,59,48,0.15);
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    .bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100vw;
        background: #fff;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 62px;
        z-index: 100;
        box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
    }
    .bottom-nav .nav-item {
        flex: 1;
        text-align: center;
        color: #888;
        font-size: 12px;
        padding-top: 0.2rem;
        text-decoration: none;
    }
    .bottom-nav .nav-item.active {
        color: #7b5cff;
        font-weight: 700;
    }
    .bottom-nav .fa, .bottom-nav img {
        font-size: 1rem;
        margin-bottom: 0.1rem;
    }
    .bottom-nav .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #e0e0e0;
    }
    @media (min-width: 600px) {
        .dashboard-header, .welcome-section, .fee-card, .feature-grid {
            max-width: 440px;
            margin-left: auto;
            margin-right: auto;
        }
        .bottom-nav {
            max-width: 440px;
            left: 50%;
            transform: translateX(-50%);
        }
    }
</style>
</head>
<body>
    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner-border-custom"></div>
    </div>

    <!-- Main Container -->
    <div class="container-mobile">

