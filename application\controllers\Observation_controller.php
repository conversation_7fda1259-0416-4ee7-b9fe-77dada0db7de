<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Observation_controller extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_MASTER')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_OBSERVATION')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT_OBSERVATION.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('observation_model');
    $this->load->model('avatar');
    $this->load->model('student/Student_Model');
  }

  public function index(){
    if (!$this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW') && !$this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW_ALL') ) {
      redirect('dashboard');
    }
    $data['viewPrivilege'] = $this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW_ALL');
    $staff = $this->staffcache->getStaffCache();
    if($data['viewPrivilege'] == 0){
      $data['observed'] = $this->observation_model->getObservedData($staff->staffId);
    } else {
      $data['observed'] = $this->observation_model->getObservedData(0);
    }

    $data['staffId'] = 0;
    if(!empty($staff)){
      $data['staffId'] = $staff->staffId;
    }
    $Date = date('Y-m-d');
    $data['from_date'] = date('Y-m-d', strtotime($Date. ' - 7 day'));
    $data['to_date'] = date("Y-m-d");
    $data['main_content']    = 'student/observation/index';
    $this->load->view('inc/template', $data);
  }

  public function addObservations($classId = 1){
    if (!$this->authorization->isAuthorized('STUDENT_OBSERVATION.CREATE') ) {
      redirect('dashboard');
    }  
    $data['selectedClassId'] = $classId;
    $data['sectionList'] = $this->Student_Model->getClassSectionNames(FALSE);
    $data['main_content']    = 'student/observation/add';
    $this->load->view('inc/template', $data);
  }

  public function get_section(){
		$class_id =$this->input->post('className');
		$section = $this->observation_model->get_classwiseSection($class_id);
		echo json_encode(array('section'=>$section));
  }
  
  public function get_student(){
		$section_id = $this->input->post('section_id');
		$stdName = $this->observation_model->get_studentclassSectionwise($section_id);
		echo json_encode(array('stdname'=>$stdName));
  }
  
  public function submitObservation(){
    if (!$this->authorization->isAuthorized('STUDENT_OBSERVATION.CREATE') ) {
      redirect('dashboard');
    }  

    $status = (int) $this->observation_model->submitObservation();
    if($status){
      $this->session->set_flashdata('flashSuccess', 'Observation added successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    }

    redirect('observation_controller/index/');
  }

  public function deleteObservation($id,$date){
    if (!$this->authorization->isAuthorized('STUDENT_OBSERVATION.CREATE') ) {
      redirect('dashboard');
    }  
    $data['selectedDate'] = $date;
    $status = (int) $this->observation_model->deleteObservation($id);
    if($status){
      $this->session->set_flashdata('flashSuccess', 'Observation deleted successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    }
    redirect('observation_controller/index/'.$date);
  }

  public function editObservation($id, $date){
    if (!$this->authorization->isAuthorized('STUDENT_OBSERVATION.CREATE') ) {
      redirect('dashboard');
    }  
    $data['selectedDate'] = $date;
    $data['editData'] = $this->observation_model->getObservationData($id);
    // echo "<pre>"; print_r($data['editData']); die();
    $data['main_content']    = 'student/observation/edit';
    $this->load->view('inc/template', $data);
  }

  public function UpdateObservation($id, $date){
    if (!$this->authorization->isAuthorized('STUDENT_OBSERVATION.CREATE') ) {
      redirect('dashboard');
    }  
    $data['selectedDate'] = $date;
    $status = (int) $this->observation_model->updateObservation($id);
    if($status){
      $this->session->set_flashdata('flashSuccess', 'Successfully Updated Observation.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('observation_controller/index/'.$date);
  }

  public function observationReport(){
    if (!$this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW_ALL') ) {
      redirect('dashboard');
    }  
    $classId = $this->input->post('classId');
    if(empty($classId)){
      $classId = "1_1";
    }
    $data['selectedSection'] = $classId;
    list($class, $section) = explode("_", $classId);
    $data['classList'] = $this->Student_Model->getClassSectionNames();
    $viewPrivilege = $this->authorization->isAuthorized('OBSERVATION.ALL');
    $staff = $this->staffcache->getStaffCache();
    if($viewPrivilege == 0){
      $data['observed'] = $this->observation_model->getObservedDataByClass($staff->staffId, $class, $section);
    } else {
      $data['observed'] = $this->observation_model->getObservedDataByClass(0, $class, $section);
    }

    $data['staff'] = 0;
    if(!empty($staff)){
      $data['staff'] = $staff->staffId;
    }
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
    $data['main_content']    = 'student/observation/report';
    $this->load->view('inc/template', $data);
  }

  public function observations(){
    if (!$this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW_SUMMARY')) {
      redirect('dashboard', 'refresh');
    }
    $data['student_uid'] = $this->uri->segment(3);
    $data['stdData'] = $this->Student_Model->getStdData($data['student_uid']);
    $observed = $this->observation_model->observations($data['student_uid']);
    $obData = array();
    foreach ($observed as $value){
      $obData[$value->staffName][] = array('date'=>date('d-m-Y', strtotime($value->created_on)), 'obs' => $value->observation);
    }
    $data['obData'] = $obData;
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
    $data['main_content'] = 'student/student_registration/more/observations';
    $this->load->view('inc/template', $data);
  }

  public function filterObservations(){
    $viewPrivilege = $this->input->get('otype');
    $data['otype'] = $viewPrivilege;
    $staff = $this->staffcache->getStaffCache();
    $data['viewPrivilege'] = $this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW_ALL');
    $data['staffId'] = 0;
    if(!empty($staff))
      $data['staffId'] = $staff->staffId;
    if($viewPrivilege == 0){
      //Show only the logged in users' observations. 
      if (!$this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW')) {
        redirect('observation_controller/index');
      }  
      $data['observed'] = $this->observation_model->filterObservedData($data['staffId']);
    } else {
      //Show all observations. 
      if (!$this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW_ALL')) {
        redirect('observation_controller/index');
      }  
      $data['observed'] = $this->observation_model->filterObservedData(0);
    }
    
    // echo "<pre>"; print_r($data['observed']); die();

    $data['staff'] = 0;
    if(!empty($staff)){
      $data['staff'] = $staff->staffId;
    }
    $data['from_date'] = $this->input->get('from_date');
    $data['to_date'] = $this->input->get('to_date');
    $data['main_content']    = 'student/observation/index';
    $this->load->view('inc/template', $data);
  }
}