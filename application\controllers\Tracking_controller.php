<?php
/**
 * Name:    Tracking Controller
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  29 August 2018
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

class Tracking_controller extends CI_Controller {

	public function __construct() {

    parent::__construct();

    $this->load->model('tracking/Tracking_model','tracking');
  }

  public function index() {
    $data['trackingList'] = $this->tracking->getTrackingMasterData();
    $data['main_content'] = 'tracking/index';
    $this->load->view('inc/template', $data);
  }

  public function add() {
    $data['main_content'] = 'tracking/add';
    $this->load->view('inc/template', $data);
  }

  public function submit_tracking_data() {
    //echo '<pre>';print_r($this->input->post());die();
    $trackingData = $this->input->post('tracking_data');
    $status = $this->tracking->submitTackingData($trackingData);

    if($status) {

      $this->session->set_flashdata('flashSuccess', 'Tracking added successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('tracking_controller/index/');
  }

  public function insertTrackingData($data) {
    //echo '<pre>';print_r($this->input->post());die();
    $status = $this->tracking->submitTackingData($data);
    if($status){

      $packetCollection = [];
      $trackingUnits = explode('##', $trackingData);

      foreach ($trackingUnits as $packetData) {

        $packet = explode('|', $packetData);

        if(!empty($packet[0])) {

          $data['schoolId'] = $packet[0];
          $data['readerId'] = 1; //isset($packet[1]) ? $packet[1] : '';
          $data['time'] = isset($packet[2]) ? date("Y-m-d H:i:s", $packet[2]) : '';
          $data['cardId'] = isset($packet[3]) ? $packet[3] : '';
          $data['temp'] = isset($packet[4]) ? $packet[4] : '';
          $data['calories'] = isset($packet[5]) ? $packet[5] : '';
          $data['steps'] = isset($packet[6]) ? $packet[6] : '';
          $data['batteryVoltage'] = isset($packet[7]) ? $packet[7] : '';
          $data['signalStrength'] = isset($packet[8]) ? $packet[8] : '';

          $packetCollection[] = $data;
        }
      }

      $trackingData = $this->tracking->insertPacketData($packetCollection);

      echo 1;
    } else {
      echo 0;
    }
  }

  public function postTrackingData() {
    $trackingData = $this->input->post('tracking_data');
    $status = $this->tracking->submitTackingData($trackingData);
    if($status){

      $packetCollection = [];
      $trackingUnits = explode('##', $trackingData);
      $readerId = 0;

      foreach ($trackingUnits as $packetData) {

        $packet = explode('|', $packetData);

        if(!empty($packet[0])) {

          $data['schoolId'] = $packet[0];
          $readerId = $data['readerId'] = isset($packet[1]) ? $packet[1] : '';
          $data['time'] = isset($packet[2]) ? date("Y-m-d H:i:s", $packet[2]) : '';
          $data['cardId'] = isset($packet[3]) ? $packet[3] : '';
          $data['temp'] = isset($packet[4]) ? $packet[4] : '';
          $data['calories'] = isset($packet[5]) ? $packet[5] : '';
          $data['steps'] = isset($packet[6]) ? $packet[6] : '';
          $data['batteryVoltage'] = isset($packet[7]) ? $packet[7] : '';
          $data['signalStrength'] = isset($packet[8]) ? $packet[8] : '';

          $packetCollection[] = $data;
        }
      }

      $trackingData = $this->tracking->insertPacketData($packetCollection, $readerId);

      echo 1;
    } else {
      echo 0;
    }
  }

  private function prepareData($trackingData) {

    $flag_arr = [];
    $reader_data = [];
    $p = 0; 

    foreach ($trackingData as $key => $value) {

      //if(!in_array($value['readerId'], $flag_arr)) {

        //$c = $value['readerId'];

        $reader_data[$value['readerId']][] = $value;
      
      //}
    }

    return $reader_data;
  }

  public function viewTrackingInfo() {
    $trackingData = $this->tracking->getTrackingData();

    // echo '<pre>'; print_r($trackingData); die();
    $data['reader_data'] = $this->prepareData($trackingData);
    
   // echo '<pre>'; print_r($data); die();
    
    $data['main_content'] = 'tracking/view_tracking_info';
    $this->load->view('inc/template', $data);
  }
}