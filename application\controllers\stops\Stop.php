<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of route
 *
 * <AUTHOR>
 */
class Stop extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->load->model('stop/Stop_model');
    }

    public function index() {
        $class_id = 1;
        if (!empty($_POST['class_name']))
            $class_id = $_POST['class_name'];

        $class_section_id = 1;
        if (!empty($_POST['class_section_name']))
            $class_section_id = $_POST['class_section_name'];

        if (!empty($class_id)) {
            $data['sectionList'] = $this->Stop_model->getSectionList($class_id);
            $data['stopList'] = $this->Stop_model->getStopList();
            $data['studentList'] = $this->Stop_model->getAsginedStudentList($class_id, $class_section_id);
        }

        $data['classList'] = $this->Stop_model->getClassList();
        $data['selectedClassId'] = $class_id;
        $data['selectedSectionId'] = $class_section_id;        
        $data['main_content'] = 'stop/assign_stop';
        $this->load->view('inc/template', $data);
    }

    public function insert_stop() {
        $data['allStopInfo'] = $this->Stop_model->getStopList();
        //  echo "<pre>";print_r($data['allStopInfo']);die();
        $data['main_content'] = 'stop/add_stops';
        $this->load->view('inc/template', $data);
    }

    public function add_stop() {
        $stopadded = $this->Stop_model->add_stops();
        if ($stopadded) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
        } else {
            $this->session->set_flashdata('flashError', 'Failed To Insert .');
        }
        redirect('stops/stop/insert_stop/');
    }

    public function getClassesSections() {
        if (isset($_POST['classid'])) {
            $classid = $_POST['classid'];
            $getclassectioninfo = $this->Stop_model->getsectionList($classid);
            echo json_encode($getclassectioninfo);
        }
    }

    public function routeInfo() {
        $routeList = $this->Stop_model->getrouteList();
        echo json_encode($routeList);
    }
 
    public function deleteStop($id) {
        $deleteInfo = $this->Stop_model->deleteStopData($id);
        if ($deleteInfo) {
            $this->session->set_flashdata('flashSuccess', 'Deleted..');
        } else {
            $this->session->set_flashdata('flashError', 'Failed To Delete .');
        }
        redirect('stops/Stop/insert_stop/');
    }
    
    public function deleteStudentStop() {
        $id = $_POST['id'];
        $deleteStudentStopInfo = $this->Stop_model->deleteStudent($id);
        if ($deleteStudentStopInfo)
            echo " Student with Stop Deleted Successfully";
        else
            echo "something went wrong";
    }

    public function addtAsginedStudent() {
        $studentid=$_POST['studentid']; $edit_stop=$_POST['edit_stop']; 
       
        $stop_assign = $this->Stop_model->addtAsginedStudentList($studentid, $edit_stop);
        if($stop_assign)
            echo " Stop Added Successfully";
        else
            echo "something went wrong";
    }
    
    public function editAsginedStudent() {
        $studentid=$_POST['studentid']; $edit_stop=$_POST['edit_stop'];
        $stop_updated = $this->Stop_model->editAsginedStudentList($studentid, $edit_stop);
        if($stop_updated)
            echo " Stop upated Successfully";
        else
            echo "something went wrong";
    }
}
