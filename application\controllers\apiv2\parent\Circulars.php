<?php

require APPPATH . 'libraries/REST_Controller.php';

class Circulars extends REST_Controller
{
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
            $data = ['error' => 'User not logged in'];
            $this->response($data, REST_Controller::HTTP_UNAUTHORIZED);
			$this->output->_display();
			exit;
		}
		$this->load->model('parentv2/Circular_model', 'circular');
	}

	public function index_get($parent_id = 0) {
		//TBD: Validate that Parent ID belongs to this User

		$filter_name = $this->get('filter_name');
		if (empty($filter_name)) {
			$filter_name = '-1';
		}

		$data = $this->circular->getCircularsAndEmails($parent_id, $filter_name);

        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function categories_get() {
		$data = $this->circular->getCircularCategories();

        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function circular_detail_get() {
		//TBD: Validate that this parent can actually view this circular

		$circular_id = $this->get('circular_id');
		if (empty($circular_id) || $circular_id <= 0) {
            $data = ['error' => 'INVALID_INPUT'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}

		$data = $this->circular->getCircularDetail($circular_id);

        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function mark_read_unread_post() {
		//TBD: Validate that this parent can actually view this circular

		$circular_id = $this->post('circular_id');
		$is_read = $this->post('is_read');
		$parent_id = $this->post('parent_id');

		if (empty($circular_id) || $circular_id <= 0) {
			$data = ['error' => 'INVALID_INPUT'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}

		if (empty($parent_id) || $parent_id <= 0) {
			$data = ['error' => 'INVALID_INPUT'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}

		$is_read = ($is_read == '1' || $is_read == 1) ? '1' : '0';

		$status = $this->circular->markAsReadUnRead($parent_id, $circular_id, $is_read);

		switch ($status) {
			case -1:
				$data = ['message' => 'PARENT_NOT_AUTHORIZED'];
				break;
			case 0:
				$data = ['message' => 'UPDATE_FAILED'];
				break;
			case 1:
				$data = ['message' => 'SUCCESS'];
				break;
			default:
				$data = ['message' => 'UNKNOWN_ERROR'];
				break;
		}

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

}