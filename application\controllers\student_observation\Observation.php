<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Observation extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_OBSERVATION')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('observation_model', 'observation_model');
  }

  public function my_observations() {
    $data['sectionList'] = $this->observation_model->getClassNames();
    $data['category_types'] = $this->observation_model->get_category_types();
    $data['capacity_types'] = $this->observation_model->get_capacity_types();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student_observation/my_observation_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student_observation/my_observation_mobile';
    }else{
      $data['main_content'] = 'student_observation/my_observations';    	
    }
    $this->load->view('inc/template', $data);
  }



  public function add_observation_mobile_view(){
    $data['sectionList'] = $this->observation_model->getClassNames();
    $data['category_types'] = $this->observation_model->get_category_types();
    $data['capacity_types'] = $this->observation_model->get_capacity_types();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student_observation/add_observation_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student_observation/add_observation_mobile';
    }   
    
    $this->load->view('inc/template', $data);
  }

  public function deactivate_observations() {
    $data['sectionList'] = $this->observation_model->getClassNames();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student_observation/deactivate_observations_Tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student_observation/deactivate_observations_mobile';
    }else{
      $data['main_content'] = 'student_observation/deactivate_observations';   	
    }
    $this->load->view('inc/template', $data);
  }

  public function observation_report() {
    $data['category_types'] = $this->observation_model->get_category_types();
    $data['sectionList'] = $this->observation_model->getClassNames();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student_observation/observation_report_Tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student_observation/observation_report_mobile';
    }else{
      $data['main_content'] = 'student_observation/observation_report';  	
    }
    $this->load->view('inc/template', $data);
  }

  public function student_observation_report() {
    $data['sectionList'] = $this->observation_model->getClassNames();
    $data['studentList'] = $this->observation_model->getAllStudentNames();
    $data['acadList'] = $this->observation_model->getAcadYearList();
    $data['main_content'] = 'student_observation/student_observation_report';
    $this->load->view('inc/template', $data);
  }

  public function section_observation_summary() {
    $data['main_content'] = 'student_observation/section_observation_summary';
    $this->load->view('inc/template', $data);
  }

  public function deactived_observations_report() {
    $data['sectionList'] = $this->observation_model->getClassNames();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student_observation/deactivated_observations_report_Tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student_observation/deactivated_observations_report_mobile';
    }else{
      $data['main_content'] = 'student_observation/deactivated_observations_report';   	
    }
    $this->load->view('inc/template', $data);
  }

  public function manage_categories() {
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student_observation/manage_categories_Tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student_observation/manage_categories_mobile';
    }else{
      $data['main_content'] = 'student_observation/manage_categories';  	
    }
    $this->load->view('inc/template', $data);
  }

  public function manage_capacity() {
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student_observation/manage_capacity_mobile';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student_observation/manage_capacity_mobile';
    }else{
      $data['main_content'] = 'student_observation/manage_capacity';   	
    }
    $this->load->view('inc/template', $data);
  }

  public function add_capacity(){
    $capacity_name = $_POST['capacity_name'];
    $success = $this->observation_model->add_capacity($capacity_name);
    echo json_encode($success);
  }

  public function get_capacity_types(){
    $data['capacity_types'] = $this->observation_model->get_capacity_types();
    echo json_encode($data);
  }
  
  public function delete_capacity(){
    $capacity_id = $_POST['capacity_id'];
    $success = $this->observation_model->delete_capacity($capacity_id);
    echo json_encode($success);

  }

  public function add_category(){
    $category_name = $_POST['category_name'];
    $category_color = $_POST['category_color'];
    $remarks = $_POST['remarks'];
    $success = $this->observation_model->add_category($category_name, $category_color,$remarks);
    echo json_encode($success);
  }

  public function edit_category(){
    $category_name = $_POST['category_name'];
    $category_color = $_POST['category_color'];
    $remarks_edit = $_POST['remarks_edit'];
    $category_id = $_POST['category_id'];
    $success = $this->observation_model->edit_category($category_name, $category_color,$category_id,$remarks_edit);
    echo json_encode($success);
  }

  public function get_category_types(){
    $data['category_types'] = $this->observation_model->get_category_types();
    echo json_encode($data);
  }
  
  public function delete_category(){
    $category_id = $_POST['category_id'];
    $success = $this->observation_model->delete_category($category_id);
    echo json_encode($success);

  }
  
  public function add_observations_view(){
    $data['sectionList'] = $this->observation_model->getClassNames();
    $data['category_types'] = $this->observation_model->get_category_types();
    $data['capacity_types'] = $this->observation_model->get_capacity_types();
    $data['main_content']    = 'student_observation/add_student_observation';
    $this->load->view('inc/template', $data);
  }

  public function get_student(){
		$section_id = $this->input->post('section_id');
		$stdName = $this->observation_model->get_studentclassSectionwise($section_id);
		echo json_encode(array('stdname'=>$stdName));
  }
  public function s3FileUpload($file,$folder_name = 'student_observation') {
    if ($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }
    $this->load->library('filemanager');
    return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
  }
  
  public function add_single_student_observation(){
    $file_input_url = $this->s3FileUpload($_FILES['file_input']);
    $class_section_id = $_POST['section_id'];
    $student_id = $_POST['student_name'];
    $category_id = $_POST['category_id'];
    $staff_capacity_id = $_POST['staff_capacity_id'];
    $observation = $_POST['observation'];
    // echo "<pre>"; print_r($observation); die();
    $action_taken = $_POST['action_taken'];
    $observation_date = date('Y-m-d H:i:s', strtotime($_POST['date']));
    $success = $this->observation_model->add_single_student_observation($class_section_id,$student_id,$category_id,$staff_capacity_id,$observation,$observation_date, $action_taken,$file_input_url);

    echo json_encode($success);
  }
  public function get_single_staff_observation_data(){
    $class_section_id = $_POST['class_section_id'];
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));

    $data['observation_data'] = $this->observation_model->get_single_staff_observation_data($class_section_id,$from_date,$to_date);

    $this->load->library('filemanager');

    
    foreach($data['observation_data'] as $k => $file_input) {
      if($file_input->file_input){
        $file_input->file_input = $this->filemanager->getFilePath($file_input->file_input);
      }
    }
    echo json_encode($data);
  }

  public function get_observation_report(){
    $class_section_id = $_POST['class_section_id'];
    $category_id = $_POST['category_id'];
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));

    $data['observation_data'] = $this->observation_model->get_observation_report($class_section_id,$from_date,$to_date,$category_id);
    echo json_encode($data);
  }

  public function get_student_observation_report(){
    $student_id = $_POST['student_id'];
    $acad_year = $_POST['acadYear'];
    $class_section_id = $_POST['class_section_id'];
    $data['observation_data'] = $this->observation_model->get_student_observation_report($student_id,$acad_year,$class_section_id);
    echo json_encode($data);
  }

  public function get_section_observation_report(){
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));
    // echo print($to_date);die();
    $data['categories'] = $this->observation_model->get_observation_categories();
    $data['observation_data'] = $this->observation_model->get_section_observation_report($from_date,$to_date);
    echo json_encode($data);
  }

  public function deactivate_observation(){
    $observation_id = $_POST['observation_id'];
    $reason = $_POST['reason'];
    $deactivate = $this->observation_model->deactivate_observation($observation_id,$reason);
    echo json_encode($deactivate);
  }

  public function get_all_observation(){
    $class_section_id = $_POST['class_section_id'];
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));

    $data['observation_data'] = $this->observation_model->get_all_observation($class_section_id,$from_date,$to_date);
    echo json_encode($data);
  }

  public function get_deactivated_observation_report(){
    $class_section_id = $_POST['class_section_id'];
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));

    $data['observation_data'] = $this->observation_model->get_deactivated_observation_report($class_section_id,$from_date,$to_date);
    echo json_encode($data);
  }

  public function get_section_category_observation(){
    $class_section_id = $_POST['class_section_id'];
    $category_id = $_POST['category_id'];
    $from_date=$_POST['from_date'];
    $to_date=$_POST['to_date'];

    $data['observation_data'] = $this->observation_model->get_section_category_observation($class_section_id,$category_id,$from_date,$to_date);
    echo json_encode($data);
  }

  public function get_staff_observation_data(){
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));

    $data['observation_data'] = $this->observation_model->get_staff_observation_data($from_date,$to_date);
    echo json_encode($data);
  }

  public function fill_remarks(){
    $data= $this->observation_model->fill_remarks($_POST);
    echo json_encode($data);
  }

  public function predefined_remarks(){
    $data['category_types'] = $this->observation_model->get_category_types();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student_observation/predefined_remarks';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student_observation/predefined_remarks';
    }else{
      $data['main_content'] = 'student_observation/predefined_remarks';   	
    }
    $this->load->view('inc/template', $data);
  }

  public function add_predefined_remarks(){
    $remarks = $_POST['remarks'];
    $category_id = $_POST['category_id'];
    $success = $this->observation_model->add_predefined_remarks($remarks,$category_id);
    echo json_encode($success);
  }

  public function get_predefined_remakes(){
    $data = $this->observation_model->get_predefined_remakes();
    echo json_encode($data);
  }

  public function delete_remarks(){
    $remarks_id = $_POST['remarks_id'];
    $success = $this->observation_model->delete_remarks($remarks_id);
    echo json_encode($success);

  }

  public function mass_observation() {
    $data['sectionList'] = $this->observation_model->getClassNames();
    $data['category_types'] = $this->observation_model->get_category_types();
    $data['capacity_types'] = $this->observation_model->get_capacity_types();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student_observation/mass_observation_view_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student_observation/mass_observation_view_mobile';
    }else{
      $data['main_content'] = 'student_observation/mass_observation_view';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function get_mass_observation_data(){
    $data = $this->observation_model->get_mass_observation_data($_POST);
    echo json_encode($data);
  }

  public function add_mass_observation_data(){
    $input_data = json_decode($this->input->raw_input_stream, true);
    $success = $this->observation_model->add_mass_observation_data($input_data);

    echo json_encode($success);
  }

}