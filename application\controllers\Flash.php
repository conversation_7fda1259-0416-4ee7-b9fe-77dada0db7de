	<?php

	class Flash extends CI_Controller {
	               
		function __construct()
		{
	      parent::__construct();
	      if (!$this->ion_auth->logged_in()) {
	        redirect('auth/login', 'refresh');
	      }
	      $this->load->model('flash_model');
	      $this->load->model('parent_model');
	      $this->load->model('class_section');
		}

		public function index(){
			$data['class_section'] = $this->class_section->getAllClassSections();
			$data['main_content']    = 'flash/index';
	        $this->load->view('inc/template', $data);
		}

		public function get_section(){
			$class_id =$this->input->post('className');
			$section = $this->class_section->getSectionsByClassId($class_id);
			$stdName = $this->class_section->get_studentclassSectionwise($class_id, 0);
			echo json_encode(array('section'=>$section,'stdname'=>$stdName));
		}
		public function get_student(){
			$class_id =$this->input->post('className');
			$section_id =$this->input->post('section');
			$result = $this->class_section->get_studentclassSectionwise($class_id,$section_id);
			echo json_encode($result);
		}
		
		public function flash(){
	        $data['class_section'] = $this->class_section->getAllClassSections();
			if ($this->mobile_detect->isTablet()) {
				$data['main_content'] = 'flash/index_tablet';
			}else if($this->mobile_detect->isMobile()){
				$data['main_content'] = 'flash/index_mobile';
			}else{
				$data['main_content'] = 'flash/index';     	
			}

	        
	        $this->load->view('inc/template', $data);
	   	}

	  public function flash_edit($flashId) {
			$data['flash_data'] = $this->flash_model->getFlashNewsById($flashId);
			$data['class_section'] = $this->class_section->getAllClassSections();
			$json = json_decode($data['flash_data'][0]->send_to);
			if(!empty($json)) {
				foreach ($json as $key => $value) {
					foreach ($data['class_section'] as $k => $val) {
						if($value->id == $val->id) {
							$value->classId = $val->class_id;
							$value->class_name = $val->class_name;
							$value->section_name = $val->section_name;
							$value->sectionId = $val->id;
							unset($data['class_section'][$k]);
						}
					}
				}
			}
			$data['selected_section'] = $json;
	        $data['main_content'] = 'flash/index';
	        $this->load->view('inc/template', $data);
		}

		public function showflashnewsBySection()
		{
			$sectionId = $this->parent_model->getSectionIdOfLoggedInParent();
			$data['flash_news'] = $this->flash_model->getAllFlashNewsForSection($sectionId);
	        $data['main_content'] = 'flash/showflashnews';
	        $this->load->view('inc/template', $data);	
		}
	   	
	   	public function showAllFlashNewsForSection()
	   	{
	   		$data['flash_news'] = $this->flash_model->get_AllFlashdata();
	        $data['main_content'] = 'flash/showflashnews';
	        $this->load->view('inc/template', $data);
	  	}

	   	public function showflashnews()
	   	{
	   		$data['flash_news'] = $this->flash_model->get_AllFlashdata();
			if ($this->mobile_detect->isTablet()) {
				$data['main_content'] = 'flash/showflashnews_tablet';
			}else if($this->mobile_detect->isMobile()){
				$data['main_content'] = 'flash/showflashnews_mobile';
			}else{
				$data['main_content'] = 'flash/showflashnews';     	
			} 
	        $this->load->view('inc/template', $data);
	  	}
		
		public function getPreviewData(){
			$msg = $_POST['msg'];
			$type = $_POST['type'];
			$membersData = $this->flash_model->getPreview();
			$html_str = '<thead><tr><th>#</th><th style="width:20%;">Name</th><th>Number</th><th>Message</th></tr></thead><tbody>';
			$i = 1;
			if(!empty($membersData)) {
				foreach ($membersData as $key => $member){
					$html_str .= '<tr>';
					$html_str .= '<td>'.$i.'</td>';
					if($member->id == -1)
						$html_str .= '<td>'.$member->Name.'</td>';
					else if($type == "Student" || $type == "Class")
						$html_str .= '<td>'.$member->Name.'(Parent of '. $member->stdName .')</td>';
					else 
						$html_str .= '<td>'.$member->Name.'</td>';
					$mobile_no = $member->mobile_no;
					if(empty($mobile_no)) $mobile_no = 'No Number';
					$html_str .= '<td>'.$mobile_no.'</td>';
					$html_str .= '<td>'.$msg.'</td>';
					$html_str .= '</tr>';
					$i++;
				}
			}
			$html_str .= "</tbody>";
			echo json_encode($html_str);
		}
		
		public function flash_insert(){
			$retVal = $this->flash_model->insert_flash_content();
			if($retVal)
			{
				$this->session->set_flashdata('flashSuccess', "Flash News added");
			}
			else
			{
				$this->session->set_flashdata('flashError', "Flash News add Failed");
			}
			redirect('flash/showflashnews');
		}

		public function flash_update($flash_id) {
			$retVal = $this->flash_model->update_flash_content($flash_id);
			if($retVal)
			{
				$this->session->set_flashdata('flashSuccess', "Flash News updated");
			}
			else
			{
				$this->session->set_flashdata('flashError', "Flash News update Failed");
			}
			redirect('flash/showflashnews');
		}

		public function flash_delete($flashId){
			$retVal = $this->flash_model->delete_flash_content($flashId);
			if($retVal)
			{
				$this->session->set_flashdata('flashSuccess', "Flash News deleted");
			}
			else
			{
				$this->session->set_flashdata('flashError', "Flash News delete Failed");
			}

				redirect('flash/showflashnews');     	
			
		}
	}