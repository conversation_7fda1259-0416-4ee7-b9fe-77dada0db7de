<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Staff_view extends CI_Controller {

	public function __construct() {
        parent::__construct();
		if (!$this->ion_auth->logged_in()) {
     		redirect('auth/login', 'refresh');
    	}
        $this->load->model('staff/staff_view_model');
  	}

  	public function transport()
	{
		$staffId = $this->authorization->getAvatarStakeHolderId();
		$data['journeys'] = $this->staff_view_model->getTodaysJourney($staffId);
		$data['main_content'] = 'staff/transport/index';
		$this->load->view('inc/template', $data);
	}

  	public function track_bus_noti($thingId, $stopId=0)
	{
		$url = $this->staff_view_model->getTrackingUrlByThing($thingId);
		if($stopId != 0) {
			$url .= '&stopId='.$stopId;
		}
		$data['tracking_url'] = $url;
		$data['main_content'] = 'staff/transport/track_bus';
		$this->load->view('inc/template', $data);
	}

	public function track_bus()
	{
		$thingId = $_POST['thing_id'];
		$stopId = $_POST['stop_id'];
		$url = $this->staff_view_model->getTrackingUrlByThing($thingId);
		$data['tracking_url'] = $url.'&stopId='.$stopId;
		$data['main_content'] = 'staff/transport/track_bus';
		$this->load->view('inc/template', $data);
	}

}