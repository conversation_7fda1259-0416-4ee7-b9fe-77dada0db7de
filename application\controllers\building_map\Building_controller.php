<?php

class Building_controller extends CI_Controller {

    function __construct(){
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('BUILDING_MASTER')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('building_model');
    }

    public function index(){
        $data['buildingData'] = $this->building_model->get_building_details();
        $data['main_content'] = 'building_map/index';
        $this->load->view('inc/template', $data);
    }

    public function addBuildingInfo(){
        $data['buildingData'] = $this->building_model->get_building_details(0,'room_type');
        $data['main_content'] = 'building_map/addRoom';
        $this->load->view('inc/template', $data);
    }

    public function submitRoom(){
        $this->db->trans_begin();

        $status = (int) $this->building_model->submitRoomDetails(); 

        switch ($status){
            case -1:
                $this->db->trans_rollback();
                $this->session->set_flashdata('flashError', 'Room number already exists, Choose different!');
                break;
            case 1:
                $this->db->trans_commit();
                $this->session->set_flashdata('flashSuccess', 'Room Details Successfully Inserted.');
                break;
            default:
                $this->db->trans_rollback();
                $this->session->set_flashdata('flashError', 'Something Went Wrong!');
                break;
        }
        redirect('building_map/building_controller');
    }

    public function EditRoomInfo($id){
        $data['buildingData'] = $this->building_model->get_building_details($id);
        $data['roomType'] = $this->building_model->get_building_details(0,'room_type');
        $data['main_content'] = 'building_map/editRoom';
        $this->load->view('inc/template', $data);
    }

    public function updateRoomInfo(){
        $this->db->trans_begin();
        $status = $this->building_model->updateRoom();

        if ($status != 0) {
            $this->db->trans_commit();
            $this->session->set_flashdata('flashSuccess', 'Room Details Successfully Updated.');
            redirect('building_map/building_controller');
        } else {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Unable to Update Room Details.');
            redirect('building_map/building_controller');
        }

    }

    public function deleteRoomInfo($room_id){
        $status = (int) $this->building_model->deleteRoom($room_id);

        switch ($status){
            case -1:
                $this->db->trans_rollback();
                $this->session->set_flashdata('flashError', 'This room has some assets, cannot delete.');
                break;
            case 1:
                $this->db->trans_commit();
                $this->session->set_flashdata('flashSuccess', 'Room Details Successfully deleted.');
                break;
            default:
                $this->db->trans_rollback();
                $this->session->set_flashdata('flashError', 'Something Went Wrong!');
                break;
        }
        
        redirect('building_map/building_controller');
    }

    public function allocate_rooms(){
        $data['roomsAllocated'] = $this->building_model->getallocatedRooms(null);
        // echo '<pre>';
        // print_r($data['roomsAllocated']);
        $data['main_content'] = 'building_map/allocate_rooms/index';
        $this->load->view('inc/template', $data);
    }

    public function allocateNewRoom(){
        $data['getclassinfo'] = $this->building_model->getclass();
        $data['blocksList'] = $this->building_model->get_building(FALSE,FALSE,FALSE,'block');
        $data['main_content'] = 'building_map/allocate_rooms/allocateRoom';
        $this->load->view('inc/template', $data);
    }

    public function getFloorsByBlock(){
        $block = $_POST['block'];
        $floorData = $this->building_model->get_building(FALSE,$block,FALSE,'floor');
    
        echo json_encode($floorData);
    }

    public function getRoomsByFloor(){
        $floor = $_POST['floor'];
        $block = $_POST['block'];
     
        $roomData = $this->building_model->get_building($floor, $block,FALSE,'room_id,room_no,room_name,section_id');
        echo json_encode($roomData);
    }

    public function getClassess() {
        if(isset($_POST['classid'])) {
          $classid = $_POST['classid'];
          $getclassectioninfo = $this->building_model->getclassection($classid);
          echo json_encode($getclassectioninfo);
        } 
    }

    public function allocRooms(){
        $input = $this->input->post();
        $room_id = $this->building_model->getRoomId($input);
        $status =(int) $this->building_model->allocateRoom($room_id->room_id);

        if ($status) {
            $this->db->trans_commit();
            $this->session->set_flashdata('flashSuccess', 'Allocation Done Successfully');
            redirect('building_map/building_controller/allocate_rooms');
        } else {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something went wrong!');
            redirect('building_map/building_controller/allocate_rooms');
        }
    }

    public function editAllocation($room_id){
        $data['allocData'] = $this->building_model->getallocatedRooms($room_id);
        $data['sections'] = $this->building_model->getclassection($data['allocData']->class_id);
        $data['getclassinfo'] = $this->building_model->getclass();
        $data['main_content'] = 'building_map/allocate_rooms/editAllocation';
        $this->load->view('inc/template', $data);
    }

    public function updateAllocation($room_id){
        $status = (int)$this->building_model->updateAllocation($room_id);
        if ($status) {
            $this->db->trans_commit();
            $this->session->set_flashdata('flashSuccess', 'Allocation Updated Successfully');
            redirect('building_map/building_controller/allocate_rooms');
        } else {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something went wrong!');
            redirect('building_map/building_controller/allocate_rooms');
        }
    }

    public function deleteAllocation($room_id){
        $status = (int) $this->building_model->deleteAllocation($room_id);

        if ($status) {
            $this->db->trans_commit();
            $this->session->set_flashdata('flashSuccess', 'Deleted Successfully');
            redirect('building_map/building_controller/allocate_rooms');
        } else {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something went wrong!');
            redirect('building_map/building_controller/allocate_rooms');
        }
    }
}

?>