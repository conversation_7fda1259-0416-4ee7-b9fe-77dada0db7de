<?php

require APPPATH . 'libraries/REST_Controller.php';

class Api extends REST_Controller
{
	function __construct() {
		parent::__construct();
		$this->yearId = $this->acad_year->setAcadYearID($this->settings->getSetting('academic_year_id'));
		$this->load->model('msm/fees_model','fees');
	}



	public function daily_fee_collection_post() {
        $from_date = $this->post('from_date');
        $to_date = $this->post('to_date');

		$data['fee_data'] = $this->fees->get_total_collection_by_date($from_date, $to_date);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function fee_management_summary_post() {
		$this->load->model('feesv2/reports_model');

		$data['fee_management'] = $this->reports_model->get_fee_management_summary();
		$data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
		$data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
		$data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_blueprint_for_year_post(){
		$this->load->model('msm/fees_model','fees');
		$acad_year = $this->post('acad_year');

		$data = $this->fees->get_blueprint_for_year($acad_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_student_fee_pending_data_post(){
		$this->load->model('msm/fees_model','fees');
		$acad_year = $this->post('acad_year');
		$blueprint_id = $this->post('blueprint_id');
		$data = $this->fees->get_student_fee_pending_data($acad_year, $blueprint_id);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_monthwise_statistics_post() {
		$this->load->model('msm/fees_model','fees');
		$current_year = $this->post('current_year');
		$prev_year = $this->post('prev_year');

		$data['previous'] = $this->fees->get_fee_monthwise_statistics($prev_year);
		$data['current'] = $this->fees->get_fee_monthwise_statistics($current_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_prediction_collection_statistics_post() {
		$this->load->model('msm/fees_model','fees');
		$current_year = $this->post('current_year');
		$data['current'] = $this->fees->get_fee_monthwise_statistics($current_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}
	
	public function get_fee_7day_statistics_post() {
		$this->load->model('msm/fees_model','fees');
		$no_of_days = $this->post('no_of_days');

		$to_date =  date('Y-m-d');
		if ($no_of_days == '7')
			$from_date = date('Y-m-d', strtotime('-6 days'));
		else
			$from_date = date('Y-m-d', strtotime('-30 days'));

		$data = $this->fees->get_total_collection_by_date($from_date, $to_date);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_paytrend_statistics_post() {
		$this->load->model('msm/fees_model','fees');
		$acad_year = $this->post('acad_year');
		$data = $this->fees->get_fee_paytrend_statistics($acad_year);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_id_for_guest_users_post(){
		$this->load->model('msm/Msm_model','msm_model');
		$user_name = $this->post('user_name');
		$data = $this->msm_model->get_guest_user_ids($user_name);
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_overall_statistics_post() {
		$this->load->model('msm/fees_model','fees');
		$acad_year = $this->post('acad_year');
		$data = $this->fees->get_fee_overall_statistics($acad_year);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_collection_bpwise_post() {
		$this->load->model('msm/fees_model','fees');
		$bp_id = $this->post('bp_id');
		$data = $this->fees->get_fee_collection_bpwise($bp_id);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function student_attendance_summary_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');

        $attendance_date = $this->post('attendance_date');

		$data['student_attendance'] = $this->student_analytics_model->get_student_attendance_summary($attendance_date);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function student_compliance_summary_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_student_compliance_summary();

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function student_counselling_summary_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_student_counselling_summary();

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function student_counselling_statuswise_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_counselling_statuswise();

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function student_boarding_summary_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_student_boarding_individual_summary();
		$boarding_type = $this->settings->getSetting('boarding');
		$responseData = array(
			'data' => $data,
			'boarding_type' => $boarding_type
		);
		
		$this->response($responseData, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_student_boarding_summary_genderwise_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_student_boarding_summary_genderwise();
		$boarding_type = $this->settings->getSetting('boarding');
		$boarding_type = array_slice($boarding_type, 1);
		$responseData = array(
			'data' => $data,
			'boarding_type' => $boarding_type
		);
		
		$this->response($responseData, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_boarding_overall_statistics_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_student_boarding_summary_v2();
	
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function boarding_nationalitywise_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_nationalitywise_bording_data();
		
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function student_category_summary_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_student_category_individual_summary();
		$category_type = $this->settings->getSetting('category');
		$responseData = array(
			'data' => $data,
			'category_type' => $category_type
		);
		
		$this->response($responseData, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function student_observation_summary_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_student_observation_summary();

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_student_observation_individual_data_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_student_observation_individual_summary();

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_student_counselling_individual_data_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_student_counselling_individual_summary();

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_student_compliance_individual_data_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_student_compliance_individual_summary();

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function staff_attendance_summary_post() {
		$this->load->model('msm/staff_attendance_model','staff_attendance_model');

        $attendance_date = $this->post('attendance_date');

		$data['staff_attendance'] = $this->staff_attendance_model->get_staff_attendance_summary($attendance_date);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function student_class_summary_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');

		$data['student_analytics'] = $this->student_analytics_model->get_student_class_summary();


		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function student_summary_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$acad_year = $this->post('acad_year');
		$data['student_summary'] = $this->student_analytics_model->get_student_summary($acad_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function student_data_across_years_post() {
		$this->load->model('msm/student_analytics_model','student_analytics_model');
		$data = $this->student_analytics_model->get_student_data_across_years();

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	
	
	public function get_lead_overall_statistics_post() {
		$this->load->model('msm/lead_management_model','lead_management');

		$acad_year = $this->post('acad_year');
		$data = $this->lead_management->_get_overall_status($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	
	
	public function get_admission_target_statistics_post() {
		$this->load->model('msm/admission_management_model','admission_management');

		$acad_year = $this->post('acad_year');
		$data = $this->admission_management->get_admission_target_statistics($acad_year, $this->post('school_code'));
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function admission_statuswise_post() {
		$this->load->model('msm/admission_management_model','admission_management');
		$acad_year = $this->post('acad_year');
		$data = $this->admission_management->admission_statuswise($acad_year);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}
	
	public function get_student_overall_statistics_post() {
		$this->load->model('msm/student_analytics_model','student_analytics');
		$acad_year = $this->post('acad_year');
		$data = $this->student_analytics->get_student_summary($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function get_student_attendance_data_post() {
		$this->load->model('msm/student_analytics_model','student_analytics');
		$from_date = $this->post('from_date');
		$data = $this->student_analytics->getAttendanceReportByDate($from_date);
		// print_r($data);die();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_staff_overall_statistics_post() {
		$this->load->model('msm/staff_analytics_model','staff_analytics');

		$data = $this->staff_analytics->get_staff_counts();

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_staff_qualification_data_post() {
		$this->load->model('msm/staff_analytics_model','staff_analytics');

		$data = $this->staff_analytics->get_staff_qualification_counts();

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}
	
	public function get_staff_attendance_data_post() {
		$this->load->model('msm/Staff_attendance_model_v2','staff_attendance');
		$from_date = $this->post('from_date');
		$data = $this->staff_attendance->getAttendanceReportByDate($from_date);
		// print_r($data);die();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_staff_attrition_data_post() {
		$this->load->model('msm/Staff_attrition_model','staff_attrition');
		$acad_year = $this->post('acad_year');
		$data = $this->staff_attrition->getAttritionReport($acad_year);
		// print_r($data);die();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_staff_age_data_post() {
		$this->load->model('msm/Staff_age_model','staff_age');
		$acad_year = $this->post('acad_year');
		$data = $this->staff_age->getageReport($acad_year);
		// print_r($data);die();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_staff_tenure_data_post() {
		$this->load->model('msm/Staff_tenure_model','staff_tenure');
		$acad_year = $this->post('acad_year');
		$data = $this->staff_tenure->gettenureReport($acad_year);
		// print_r($data);die();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_infirmary_data_for_dashboard_post() {
		$this->load->model('msm/Infirmary_statistics_model','infirmary_model');
		if($this->post('date_type') == 'week'){
			$to_date =  date('Y-m-d');
			$from_date = date('Y-m-d', strtotime('-6 days'));
		}
		else if($this->post('date_type') == 'month'){
			$to_date =  date('Y-m-d');
			$from_date = date('Y-m-d', strtotime('-1 month', strtotime($to_date)));
		}
		else if($this->post('date_type') == 'year'){
			$to_date =  date('Y-m-d');
			$from_date = date('Y-m-d', strtotime('-1 year', strtotime($to_date)));
		}
		else if($this->post('date_type') == 'fiveyear'){
			$to_date =  date('Y-m-d');
			$from_date = date('Y-m-d', strtotime('-5 year', strtotime($to_date)));
		}
		$data = $this->infirmary_model->get_infirmary_data_dashboard($from_date, $to_date);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_infirmary_statistics_post() {
		$this->load->model('msm/Infirmary_statistics_model','infirmary_model');
		$data = $this->infirmary_model->get_infirmary_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_infirmary_monthwise_statistics_post() {
		$this->load->model('msm/Infirmary_statistics_model','infirmary_model');
		$data = $this->infirmary_model->get_fee_monthwise_statistics();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_individual_infirmary_monthwise_statistics_post() {
		$this->load->model('msm/Infirmary_statistics_model','infirmary_model');
		$data = $this->infirmary_model->get_fee_monthwise_statistics();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function parent_ticketing_overall_statistics_post() {
		$this->load->model('msm/parent_ticketing_model','parent_ticketing');

		$data = $this->parent_ticketing->get_overall_statistics();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	
	public function parent_ticketing_overall_status_post() {
		$this->load->model('msm/parent_ticketing_model','parent_ticketing');

		$data = $this->parent_ticketing->_get_overall_status();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	
	
	public function parent_ticketing_get_activity_details_post() {
		$this->load->model('msm/parent_ticketing_model','parent_ticketing');

		$data = $this->parent_ticketing->ticketing_get_activity_details();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	
	
	public function staff_attendance_trend_get_sevenday_activity_post() {
		$this->load->model('msm/staff_attendance_model','staff_attendance_model');

		$data = $this->staff_attendance_model->get_last_seven_day_attendance_trend();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function parent_ticketing_get_sevenday_activity_post() {
		$this->load->model('msm/parent_ticketing_model','parent_ticketing');

		$data = $this->parent_ticketing->_get_ticketing_activity();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function parent_ticketing_ticket_details_post() {
		$this->load->model('msm/parent_ticketing_model','parent_ticketing');

		$data = $this->parent_ticketing->parent_ticketing_ticket_details();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function internal_ticketing_get_sevenday_activity_post() {
		$this->load->model('msm/internal_ticketing_model','internal_ticketing');

		$data = $this->internal_ticketing->_get_ticketing_activity($this->post('school_code'));
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function lead_management_post() {
		$this->load->model('msm/lead_management_model','lead_management');

		$acad_year = $this->post('acad_year');
		$data = $this->lead_management->get_lead_management($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function lead_reporting_status_post() {
		$this->load->model('msm/lead_management_model','lead_management');

		$data = $this->lead_management->getReportingStatus();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function lead_statuswise_post() {
		$this->load->model('msm/lead_management_model','lead_management');
		$acad_year = $this->post('acad_year');
		$data = $this->lead_management->lead_statuswise($acad_year);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function lead_counselor_post() {
		$this->load->model('msm/lead_management_model','lead_management');

		$data = $this->lead_management->getLead_counselor_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function lead_management_get_activity_post() {
		$this->load->model('msm/lead_management_model','lead_management');

		$acad_year = $this->post('acad_year');
		$data = $this->lead_management->get_lead_management_activity($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	
	
	public function lead_management_get_enquiry_post() {
		$this->load->model('msm/lead_management_model','lead_management');

		$acad_year = $this->post('acad_year');
		$data = $this->lead_management->get_lead_management_enquiry($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	
	
	public function admission_management_get_activity_post() {
		$this->load->model('msm/admission_management_model','admission_management');

		$acad_year = $this->post('acad_year');
		$data = $this->admission_management->admission_management_get_activity($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function admission_management_get_admission_post() {
		$this->load->model('msm/admission_management_model','admission_management');

		$acad_year = $this->post('acad_year');
		$data = $this->admission_management->admission_management_get_admission($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function admission_management_post() {
		$this->load->model('msm/admission_management_model','admission_management');
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');

		$data = $this->admission_management->get_admission_management($school_code, $acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function admission_reporting_status_post() {
		$this->load->model('msm/admission_management_model','admission_management_model');

		$data = $this->admission_management_model->get_admissions_basedOn_reporting_status();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function admission_user_status_post() {
		$this->load->model('msm/admission_management_model','admission_management_model');

		$data = $this->admission_management_model->get_admissions_basedOn_user_status();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function admission_counselor_post() {
		$this->load->model('msm/admission_management_model','admission_management_model');

		$data = $this->admission_management_model->getAdmission_counselor_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function admission_genderwise_post() {
		$this->load->model('msm/admission_management_model','admission_management_model');

		$data = $this->admission_management_model->getAdmission_genderwise_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function admission_gradewise_post() {
		$this->load->model('msm/admission_management_model','admission_management_model');

		$data = $this->admission_management_model->getAdmission_gradewise_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function admission_closure_reason_post() {
		$this->load->model('msm/admission_management_model','admission_management_model');

		$data = $this->admission_management_model->getAdmission_closure_reason_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function admission_pipeline_post() {
		$this->load->model('msm/pipeline_model','pipeline_model');
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');

		$data = $this->pipeline_model->pipeline($school_code, $acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function activity_tracker_post() {
		$this->load->model('msm/activity_tracker_model','activity_tracker_model');

		$data['activity_tracker'] = $this->activity_tracker_model->get_activity();

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	function get_payroll_individual_month_wise_data_post(){
		$this->load->model('msm/payroll_model','payroll_model');
		$schedule_id = $this->post('schedule_id');
		$data = $this->payroll_model->get_payroll_individual_month_wise_data($schedule_id);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	function get_comp_data_post(){
		$this->load->model('msm/payroll_model','payroll_model');
		$comp_name = $this->post('comp_name');
		$data = $this->payroll_model->get_comp_data($comp_name);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_staff_report_post(){
		$this->load->model('report/Staffreport_model','staffreport_model');
		$columnListJSON = $this->post('columnListJSON');
		$selectedIndex = $this->post('selectedColumns');
		$staff_status_selected_id= $this->post('staff_status_selected_id');
		$columnList = json_decode($columnListJSON);

		$selectedColumns = array();
		$displayColumns = array();
		$addressColumns = array();
		foreach($selectedIndex as $fIndex) {
			foreach ($columnList as $col) {
			if ($col->index == $fIndex) {
				if ($col->table == 'address_info') {
				$temp = new stdClass();
				$temp->addressOf = $col->addressOf;
				$temp->addressType = $col->addressType;
				$addressColumns[] = $temp;
				}
				else
				$selectedColumns[] = (array)$col;
				$displayColumns[] = $col;
				break;
			}
			}
		}

		$displayColumns = $this->staffreport_model->get_display_columns($displayColumns);
		$staffData = $this->staffreport_model->getStaffData_msm($selectedColumns, $staff_status_selected_id);
		foreach ($addressColumns as $addCol) {
			$staffData = $this->staffreport_model->getAndMergeAddresses($staffData,$addCol->addressOf,$addCol->addressType);
		}

		foreach ($staffData as $key => &$val) {
			if (isset($val->staff_type)) {
				foreach ($this->settings->getSetting('staff_type') as $value => $name) {
					if ($val->staff_type == $value) {
					$val->staff_type = $name;
					}
				}
			}
		
			if (isset($val->gender)) {
				if ($val->gender == 'F') {
					$val->gender ='Female';
				}else{
					$val->gender ='Male';
				}
			} 
			if (isset($val->marital_status)) {
				if ($val->marital_status == '1') {
					$val->marital_status ='Married';
				}else{
					$val->marital_status ='Single';
				}
			}
		}
		$data['exportData'] = $staffData;
		$data['selectedColumns'] = $displayColumns;
		// echo '<pre>';print_r($data);die();
		echo json_encode($data);
	}

	public function get_dashboard_school_summary_post() {
		//We need to provide the following
		//a. Student count
		//b. Staff count
		//c. Student Attendance
		//d. Staff attendance
		//e. Fees collection
		
		$this->load->model('msm/msm_statistics_model','msms');

        $statistic_name = $this->post('statistic_name');

		$data['student_count_obj'] = $this->msms->get_latest_single_statistics('student_count_obj');
		$data['staff_count_obj'] = $this->msms->get_latest_single_statistics('staff_count_obj');
		$data['staff_attendance_obj'] = $this->msms->get_latest_single_statistics_per_recorded_date('staff_attendance_obj', '2023-01-22');
		// $data['student_attendance'] = $this->msms->get_latest_single_statistics_per_recorded_date('student_attendance', '2023-01-14');

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}
	// public function get_statistics_get() {
	// 	$this->load->model('msm/msm_statistics_model','msms');

    //     // $statistic_name = $this->post('statistic_name');
    //     $statistic_name = 'student_count';

	// 	$data['statistic_value'] = $this->msms->get_latest_single_statistics($statistic_name);

	// 	$this->response($data, REST_Controller::HTTP_OK);
	// 	$this->output->_display();
	// 	exit;
	// }
}
?>
