<?php

class Learning extends CI_Controller {
    function __construct(){
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->load->model('helium/Learning_model');
        $this->load->model('parent_model');
        $this->load->library('filemanager');
    }

    public function bridge() {
        $end_point = CONFIG_ENV['helium'].$_POST['end_point'];
        $data = $_POST;
        if($data['end_point'] == 'Course/receipt') {
            $data['receipt_template'] = HELIUM_RECEIPT_TEMPLATE;
        }
        unset($data['end_point']);

        $curl_request = [
            CURLOPT_URL => $end_point,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                "content-type: application/json"
            ]
        ];

        $curl = curl_init();
        curl_setopt_array($curl, $curl_request);
        $response = curl_exec($curl);
        // echo "<pre>"; print_r($db_response); die();
        curl_close($curl);
        echo $response;
    }

    private function __generate_token($student_id, $additional=[]) {
        $parent_id = $this->authorization->getAvatarStakeHolderId();
        $student = $this->Learning_model->getStudentInfo($student_id, $parent_id);
        $school = $this->settings->getSetting('school_short_name');
        $student = [
            "id" => $student->student_id,
            "school" => $school
        ];
        if(!empty($additional)) {
            $student = array_merge($student, $additional);
        }
        // echo '<pre>'; print_r($student); die();
        $jwt = $this->generateJWT($student);
        return $jwt;
    }

    public function index() {
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $parent_id = $this->authorization->getAvatarStakeHolderId();
        $student = $this->Learning_model->getStudentInfo($student_id, $parent_id);
        $data['student_id'] = $student_id;
        $school = $this->settings->getSetting('school_short_name');
        $data['student'] = $student;
        if($student->joined_helium) {
            $student = [
                "id" => $student->student_id,
                "school" => $school
            ];
            $data['jwt'] = $this->generateJWT($student);
            if ($this->mobile_detect->isTablet()) {
               $data['main_content']   = 'helium/student/dashboard';
            }else if($this->mobile_detect->isMobile()){
              $data['main_content']    = 'helium/student/dashboard';
              // $this->load->view('helium/student/redirecting_page', $data);
            }else{
              $data['main_content']    = 'helium/student/desktop/dashboard';
            }
            // $data['main_content'] = 'helium/student/dashboard';
            $this->load->view('helium/inc/template', $data);
        } else {
            $student = [
                "id" => $student->student_id,
                "name" => $student->student_name,
                "school" => $school,
                "mobile_no" => $student->mobile_no,
                "email" => $student->email,
                "dob" => $student->dob
            ];
            //email, phone_no, dob 
            $data['jwt'] = $this->generateJWT($student);
            if ($this->mobile_detect->isTablet()) {
               $data['main_content']   = 'helium/student/starter';
            }else if($this->mobile_detect->isMobile()){
              $data['main_content']    = 'helium/student/starter';
            }else{
              $data['main_content']    = 'helium/student/desktop/starter';
            }
            $this->load->view($data['main_content'], $data);
        }
    }

    public function courses() {
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['jwt'] = $this->__generate_token($student_id);
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/student/courses';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/student/courses';
        }else{
          $data['main_content']    = 'helium/student/desktop/courses';
        }
        $this->load->view('helium/inc/template', $data);
    }

    public function course() {
        $data['course_id'] = $_POST['course_id'];
        // echo $_POST['course_id'];
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['jwt'] = $this->__generate_token($student_id, ['course_id' => $data['course_id']]);

        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/student/course';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/student/course';
        }else{
          $data['main_content']    = 'helium/student/desktop/course';
        }
        $this->load->view('helium/inc/template', $data);
    }

    public function my_learnings() {
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['jwt'] = $this->__generate_token($student_id);
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/student/my_learnings';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/student/my_learnings';
        }else{
          $data['main_content']    = 'helium/student/desktop/my_learnings';
        }
        $this->load->view('helium/inc/template', $data);
    }

    public function my_course() {
        $data['batch_id'] = $_POST['batch_id'];
        $data['course_id'] = $_POST['course_id'];
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['jwt'] = $this->__generate_token($student_id, ['batch_id' => $data['batch_id'], 'course_id' => $data['course_id']]);
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/student/my_course';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/student/my_course';
        }else{
          $data['main_content']    = 'helium/student/desktop/my_course';
        }
        $this->load->view('helium/inc/template', $data);
    }

    public function batch_registration() {
        $data['batch_id'] = $_POST['batch_id'];
        $data['course_id'] = $_POST['course_id'];
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['jwt'] = $this->__generate_token($student_id, ['batch_id' => $data['batch_id'], 'course_id' => $data['course_id']]);
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/student/batch_registration';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/student/batch_registration';
        }else{
          $data['main_content']    = 'helium/student/desktop/batch_registration';
        }
        $this->load->view('helium/inc/template', $data);
    }

    public function index_old() {
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $parent_id = $this->authorization->getAvatarStakeHolderId();
        $student = $this->Learning_model->getStudentInfo($student_id, $parent_id);
        $data['student_id'] = $student_id;
        $school = $this->settings->getSetting('school_short_name');
        $data['student'] = $student;
        if($student->joined_helium) {
            $student = [
                "id" => $student->student_id,
                "school" => $school
            ];
            $data['jwt'] = $this->generateJWT($student);
            $this->load->view('helium/index', $data);
        } else {
            $student = [
                "id" => $student->student_id,
                "name" => $student->student_name,
                "school" => $school
            ];
            //email, phone_no, dob 
            $data['jwt'] = $this->generateJWT($student);
            $this->load->view('helium/joiner', $data);
        }
    }

    private function generateJWT($data) {
      $this->load->library('JWT');
      $secret = "bmsjman09rsasaac1ezin_tx";

      $payload = [
            "user" => $data,
            "iss" => "nextelement"
      ];

      // echo '<pre>';print_r($payload);die();
      $jwt = JWT::encode($payload, $secret, 'HS256');
      return $jwt;
    }

    public function updateStudentAsJoined() {
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $status = $this->Learning_model->updateStudentAsJoined($student_id);
        echo $status;
    }

    public function send_email() {
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $parent_id = $this->authorization->getAvatarStakeHolderId();
        $student = $this->Learning_model->getStudentInfo($student_id, $parent_id);
        $subject = $_POST['subject'];
        $message = $_POST['message'];
        $status = $this->__email_sender($student, $subject, $message);
        echo $status;
    }

    private function __email_sender($student, $subject, $message) {
        $from_email = $this->settings->getSetting('circularv2_from_email');
        $email_master_data = array(
            'subject' => $subject,
            'body' => $message,
            'source' => 'Helium',
            'sent_by' => 1,
            'recievers' => 'Students',
            'from_email' => $from_email,
            'files' => NULL,
            'acad_year_id' => $this->acad_year->getAcadYearId(),
            'visible' => 1,
            'sender_list' => NULL,
            'sending_status' => 'Completed'
        );

        $this->db->trans_start();
        $this->db->insert('email_master', $email_master_data);
        $email_master_id = $this->db->insert_id();
        $sent_to = array(
          'email_master_id' => $email_master_id,
          'stakeholder_id' => $parent_id,
          'email' => $student->email,
          'avatar_type' => 2, //parent
          'status' => ($student->email)?'Awaited':'No Email'
        );
        $this->db->insert('email_sent_to', $sent_to);
        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        }
        $this->load->helper('email_helper');
        $this->db->trans_commit();
        sendEmail($message, $subject, $email_master_id, [$student->email], $from_email);
        return 1;
    }

    public function about_us() {
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/student/about_us';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/student/about_us';
        }else{
          $data['main_content']    = 'helium/student/desktop/about_us';
        }
        $this->load->view('helium/inc/template', $data);
    }

    public function help() {
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/student/help';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/student/help';
        }else{
          $data['main_content']    = 'helium/student/desktop/help';
        }
        $this->load->view('helium/inc/template', $data);
    }

    public function contact_us() {
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/student/contact_us';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/student/contact_us';
        }else{
          $data['main_content']    = 'helium/student/desktop/contact_us';
        }
        $this->load->view('helium/inc/template', $data);
    }

    public function download_file() {
        $link = $_POST['path'];
        $file_name = $_POST['name'];
        $type = $_POST['type'];
        
        $file = explode("/", $link);
        $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
        $data = file_get_contents($link);
        $this->load->helper('download');
        force_download($fname, $data, TRUE);
    }

    /*public function change_password() {
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/student/change_password';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/student/change_password';
        }else{
          $data['main_content']    = 'helium/student/desktop/change_password';
        }
        $this->load->view('helium/inc/template', $data);
    }*/

    
}