<?php

/**
 * Description of School_menu
 *
 * <AUTHOR>
 */
class Class_controller extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('SCHOOL')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('SCHOOL.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('School_model','sm');
    $this->load->library('filemanager');
    $this->load->model('academics/ManageSubject_model');
  }

  function index() {
    $data['class_list'] = $this->sm->get_class_data();

    $data['main_content'] = 'school/class/index.php';
    $this->load->view('inc/template', $data);
  }

  function edit_class($id) {
    $data['class_data'] = $this->sm->get_class_data_by_id($id);
    $data['staff_list'] = $this->sm->get_staff_list();

    $data['main_content'] = 'school/class/edit.php';
    $this->load->view('inc/template', $data);
  }

  function get_class_details(){
    $classId = $_POST['classId'];
    $result = $this->sm-> get_class_data_by_classid($classId );
    echo json_encode($result);
  }
  function change_class_name(){
    $class_name = $_POST['class_name'];
    $class_id = $_POST['class_id'];
    $result = $this->sm-> change_class_name($class_name, $class_id);
    echo $result;
  }
  function change_sec_name(){
    $new_sec_name = $_POST['new_sec_name'];
    $sec_id = $_POST['sec_id'];
    $result = $this->sm->change_sec_name($new_sec_name, $sec_id);
    echo $result;
  }
  function update_sectionv2() {
    //$class_id = $_POST['classId'];
    $data = $this->sm->update_sectionv2();
    
   /* if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Class Successfully Added');
    } else {
        $this->session->set_flashdata('flashError', 'Something Wrong...');
    }*/
    echo ($data);
  }

   function update_class($id) {
    $result = $this->sm->update_class($id);
    
    if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Class Successfully Added');
  } else {
        $this->session->set_flashdata('flashError', 'Something Wrong...');
     }
     redirect('school/class_controller/');
   }
  function update_classv2() {
    //$class_id = $_POST['classId'];
    $data = $this->sm->update_classv2($this->s3FileUpload($_FILES['edit_fee_template']));
    $is_template_set=$this->ManageSubject_model->assign_period_template_to_class(["classId"=>$_POST["classId"],"periodTemplateId"=>$_POST["period_template_id"]]);
   /* if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Class Successfully Added');
    } else {
        $this->session->set_flashdata('flashError', 'Something Wrong...');
    }*/
    echo ($data && $is_template_set);
  }

  public function s3FileUpload($file,$folder_name='fees_structure_template') {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],$folder_name);
  }
}