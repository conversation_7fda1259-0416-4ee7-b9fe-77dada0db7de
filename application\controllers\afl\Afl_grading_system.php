<?php
class Afl_grading_system extends CI_Controller {
    private $yearId;
    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
       redirect('auth/login', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('afl/Afl_grading_model','grading_model');
      $this->load->model('Class_section', 'class_section');
    }

  public function index(){
    $data['grades'] = $this->grading_model->getGradingSystems();
    $data['main_content'] = 'afl/grading_system/index';
    $this->load->view('inc/template', $data);
  }

  public function add_grading_system() {
    $data['main_content'] = 'afl/grading_system/add';
    $this->load->view('inc/template', $data);
  }

  public function saveGradingSystem() {
    $input = $this->input->post();
    $status = $this->grading_model->saveGradingSystem();
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Grading Added Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_grading_system');
  }

  public function editGradingSystem($scaleId) {
    $data['grading_system'] = $this->grading_model->getGradingScale($scaleId);
    $data['grading_system_values'] = $this->grading_model->getGradingScaleValues($scaleId);
    $data['main_content'] = 'afl/grading_system/edit';
    $this->load->view('inc/template', $data);
  }

  public function updateGradingSystem() {
    $input = $this->input->post();
    $status = $this->grading_model->updateGradingSystem();
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Grading Updated Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_grading_system');
    // echo "<pre>"; print_r($input); die();
  }
}