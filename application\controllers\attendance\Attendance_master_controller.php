<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Attendance_controller
 *
 * <AUTHOR>
 */
class Attendance_master_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('STAFF_MASTER')) {
            redirect('dashboard', 'refresh');
        }    

        $this->load->model('attendance/Attendance_master_model','attendanceMaster');
        $this->load->model('student/Student_Model','studentModel');
        //$this->load->model('attendance/Attendance_model');
    }

    public function index() {
      $data['attendanceMaster'] = $this->attendanceMaster->getAttendanceMaster();
      $data['main_content'] = 'attendance/master/master_index';
      $this->load->view('inc/template', $data);
    }

    public function show($id) {
      $data['details'] = $this->attendanceMaster->getAttendanceTemplateMaster($id);
      $data['main_content'] = 'attendance/master/show_master_template';
      $this->load->view('inc/template', $data);
    }

    public function getAttendenceTemplate() {
      $classid = $this->input->post('classid');
      $periodsByClass = $this->attendanceMaster->getPeriodsByClass($classid);
      echo json_encode($periodsByClass);
    }

    public function add_template() {
      
      $data['getclassinfo'] = $this->studentModel->getclass();
      $data['main_content'] = 'attendance/master/master_template';
      $this->load->view('inc/template', $data);
    }

    public function alias_name() {

      $classid = $this->input->post('classid');
      $periodinfo = $this->attendanceMaster->getPeriodsByClass($classid);

      $weekday_period = $this->input->post('weekday_period');
      $saturday_period = $this->input->post('saturday_period');

      $wsection = [];
      $section_index = 0;
      $ref_index = 0;

      if (!empty($weekday_period)) {
        foreach ($periodinfo['weekday'] as $pkey => $pvalue) {

          if(isset($weekday_period[$ref_index])) {
            if($pvalue->id.'_'.$pvalue->short_name == $weekday_period[$ref_index]) {
              $ref_index++;
              $section_index++;
            }
          }
  
        $wsection['section'.$section_index][] = $pvalue;        
        }
      }

      $ssection = [];
      $section_index = 0;
      $ref_index = 0;

      if(!empty($saturday_period)) {

        foreach ($periodinfo['saturday'] as $pkey => $pvalue) {

          if(isset($saturday_period[$ref_index])) {
            if($pvalue->id.'_'.$pvalue->short_name == $saturday_period[$ref_index]) {
              $ref_index++;
              $section_index++;
            }
          }          
          $ssection['section'.$section_index][] = $pvalue;        
        }
      }
      $data['weekday_period'] = $wsection;
      $data['saturday_period'] = $ssection;
      $data['classid'] = $this->input->post('classid');
      $data['main_content'] = 'attendance/master/master_alias_name';
      $this->load->view('inc/template', $data);
    }

    public function submit_template() {

      $input = $this->input->post();
      $insert_status = false;

      $this->db->trans_begin();

      foreach ($input as $k => $v) {
        if (strpos($k, '_weekday_') !== false) {
          $insert_status = $this->attendanceMaster->save_attendance_master($k, $v, 1, $input['classid']);
        } elseif (strpos($k, '_sat_') !== false) {
          $insert_status = $this->attendanceMaster->save_attendance_master($k, $v, 2, $input['classid']);
        }
      }

      if ($insert_status) {
        $this->db->trans_commit();
        $this->session->set_flashdata('flashSuccess', 'Attendance Template Successfully Inserted.');
      } else {
        $this->db->trans_rollback();
        $this->session->set_flashdata('flashError', 'Unable to Insert Attendance Template.');
      }

      redirect('attendance/Attendance_master_controller');
    }

       
    
}
