<?php

class Staff_raw_attendance extends CI_Controller {

    function __construct() {
      parent::__construct();
      $this->load->model('staff/Staff_attendance_model', 'staff_attendance');
    }

    public function updateAttendanceRawData () {
        // trigger_error('In UpdateAttendance');
        // trigger_error(json_encode($_POST));
        $input = (array)$_POST;

        // Test input
        // $input['LogDate'] = '2022-09-03 22:56:40';
        // $input['UserId'] = 'SDNT204888';
        // $input['Direction'] = 0;
        // $input['DeviceId'] = 4;
        // $input['C5'] = '1';
        // $input['C4'] = '5';

        //We get biometric time in local. Converting to GMT for further processing.
        $offset = 5.5 * 60 * 60;
        $input['LogDate'] = date('Y-m-d H:i:s', strtotime($input['LogDate'])- $offset);

        //Determine if the biometric input coming in is for student or staff. Student will contain 'SDNT' in their user id.
        // echo 'userid: ' . strpos($input['UserId'], 'SDNT');die();
        if (strpos($input['UserId'], 'SDNT') !== false) {
            return $this->__handle_student_biometric ($input);
        } else if (strpos($input['UserId'], 'APSPSSD') !== false) {
            return $this->__handle_student_biometric ($input);
        } else if (strpos($input['UserId'], 'IISBSD') !== false) {
            return $this->__handle_student_biometric ($input);
        }
        
        $direction = 0;
        if($input['Direction'] == 'out') {
        	$direction = 1;
        }
        $data = array(
        	'device_id' => $input['DeviceId'],
        	'staff_code' => $input['UserId'],
        	'punch_time' => $input['LogDate'],
        	'access_type' => $input['C5'],
        	'direction' => $direction,
        	'timezone_code' => $input['C4']
        );

        $log_id = $this->staff_attendance->update_attendance_log($data);
        if($log_id>0) {
	        $this->_updateAttendanceFromLogs($data, $log_id);
	        return 1;
	    } else if($log_id == -1) {
            return 1;
        } else {
	        trigger_error("Failed to add attendance logs");
	        return 0;
	    }
    }

    private function __handle_student_biometric($input) {
        //Update Student attendance log
        $data = array(
        	'device_id' => $input['DeviceId'],
        	'student_biometric_code' => $input ['UserId'],
        	'punch_time' => $input['LogDate'],
        	'access_type' => $input['C5'],
        	'direction' => $input['Direction'],
        	'timezone_code' => $input['C4']
        );
        $this->db->insert('student_attendance_logs', $data);
        return 1;
    }

    private function _updateAttendanceFromLogs($log_data, $log_id) {
    	// $att_data = $this->staff_attendance->getAttendanceLogs();
    	// $date = date('Y-m-d', strtotime($log_data['punch_time']));
        
        //commenting the code for updating biometric data to old staff attendance tables
        // $att_session_id = $this->staff_attendance->addAttendanceSession($date);
        // $status = $this->staff_attendance->updateAttendanceFromLog($log_data, $att_session_id, $log_id);

        //this is the code for updating biometric data to new staff attendance table (st_attendance, st_attendance_transaction)
        
        $status = $this->staff_attendance->update_biomtric_data($log_id, $log_data);

	    return $status;
    }
}
  
  