<?php

class Competition extends CI_Controller 
{
	function __construct()
	{
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    } 
    if (!$this->authorization->isModuleEnabled('COMPETITION')) {
      redirect('dashboard', 'refresh');
    }
	  $this->load->model('competition_model');
    $this->load->model('avatar');
	}

// Competition 
	public function index(){
    //Set privileges
    $permitCRUD = $this->authorization->isAuthorized('COMPETITION.ADD_EDIT_DELETE');
    $permitApproval = $this->authorization->isAuthorized('COMPETITION.APPROVE');

    //Is the faculty a default incharge so she gets rights even if she is not assigned to competition
    $isDefaultIncharge = $this->authorization->isAuthorized('COMPETITION.DEFAULT_STAFF_INCHARGE');
    $loggedInStaffId = $this->authorization->getAvatarStakeHolderId();
    
    //Get Competition Data
    $competitionData = $this->competition_model->getlistof_competition();
    foreach ($competitionData['comp'] as &$comp) {
      //Get If the logged in staff is assigned to this competition
      $staffList = json_decode($comp->staff_assigned);
      $found = 0;
      foreach ($staffList as $staff) {
        if ($staff->teachers_id == $loggedInStaffId) {
          $found = 1;
          break;
        }
      }
      $comp->isStaffAssigned = $found || $isDefaultIncharge;

      //Get the dates of the competition
      $dates = '';
      foreach ($competitionData['com_1'] as $dateObj) {
        if ($comp->id == $dateObj->competition_id) {
          if (!empty($dates)) {
            $dates .= '<br>';
          }
          $dates .= date('d-m-Y',strtotime($dateObj->competition_date));
        }
        $comp->dates = $dates;
      }
    }
    $data['permitCRUD'] = $permitCRUD;
    $data['permitApproval'] = $permitApproval;
    $data['list_competition'] = $competitionData;
    $data['competitionAttendance'] = $this->settings->getSetting('competition_attendance');
    $data['staff_details'] = $this->competition_model->get_staff_detils();
    $data['main_content']    = 'competition/index';

     //echo '<pre>'; print_r($data); die();

    $this->load->view('inc/template', $data);      
  }
  
	public function add_competition(){
    if (!$this->authorization->isAuthorized('COMPETITION.ADD_EDIT_DELETE')) {
      redirect('competition', 'refresh');
    }
    $data['competition_code'] = $this->competition_model->getLastInserted(); 
    $data['list_competition'] = $this->competition_model->getlistof_competition();
		$data['staff_details'] = $this->competition_model->get_staff_detils();
    //echo "<pre>"; print_r($data['staff_details']); die();
		$data['main_content']    = 'competition/competition_add';
   	$this->load->view('inc/template', $data);
	}

  public function search_competitionwiseforclone(){
    $data['list_competition'] = $this->competition_model->getlistof_competition();
    $closeData = $this->input->post('competition_clone');
    if (empty($closeData))
      redirect('competition/add_competition');
    $data['selectedData'] = $closeData;
    $data['clone_data'] = $this->competition_model->get_clone_data($closeData);
    $data['staff_details'] = $this->competition_model->get_staff_detils();
    $data['main_content']    = 'competition/competition_add';
    $this->load->view('inc/template', $data);

  }

	public function submit_competition(){
    if (!$this->authorization->isAuthorized('COMPETITION.ADD_EDIT_DELETE')) {
      redirect('competition', 'refresh');
    }

	 $data['competition'] = $this->competition_model->insert_competitionDetails();
	 if ($data['competition']) {
      $this->session->set_flashdata('flashSuccess', 'Submit Successfully');
 		redirect('competition');
	 }else{
    $this->session->set_flashdata('flashError', 'Something went wrong..');
	 	redirect('competition/add_competition');
	 }	
	}

	public function edit_competition($id){
    if (!$this->authorization->isAuthorized('COMPETITION.ADD_EDIT_DELETE')) {
      redirect('competition', 'refresh');
    }
		$data['edit_competition'] = $this->competition_model->getlistof_competitionbyId($id);
		$data['staff_details'] = $this->competition_model->get_staff_detils();
		$data['main_content']    = 'competition/competition_edit';
   	$this->load->view('inc/template', $data);
	}	

  public function update_competition($id){
 		 $update_competition = $this->competition_model->udpate_competition($id);
		 if ($update_competition) {
	 	 	$this->session->set_flashdata('flashSuccess', 'Update Successfully');
	 		redirect('competition');
		 }else{
		 	$this->session->set_flashdata('flashError', 'Something went wrong..');
		 	redirect('competition/edit_competition');
		 }	
   }

  public function delete_competition($id){
    if (!$this->authorization->isAuthorized('COMPETITION.ADD_EDIT_DELETE')) {
      redirect('competition', 'refresh');
    }
    $delete_competition = $this->competition_model->delete_competition($id);
    if ($delete_competition) {
   	 	$this->session->set_flashdata('flashSuccess', 'Delete Successfully');
   		redirect('competition');
  	 }else{
  	 	$this->session->set_flashdata('flashError', 'Something went wrong..');
  	 	redirect('competition');
  	 }	
  }
// End Competition

//Assing student for staff  

  public function insert_competition($id){
    $competition_insert = $this->competition_model->insert_competitionbystudent($id);
     if ($competition_insert) {      
       $this->session->set_flashdata('flashSuccess', 'Update Successfully');
        redirect('competition/fetch_competition_wise/'.$id);
     }else{
       $this->session->set_flashdata('flashError', 'Something went wrong..');
        redirect('competition/fetch_competition_wise/'.$id);
     }
   }

   private function __checkStaffAccess ($competitionData, $id) {
    //Check if the staff has access
    if ($this->authorization->isAuthorized('COMPETITION.DEFAULT_STAFF_INCHARGE'))
      return 1;
    
    $loggedInStaffId = $this->authorization->getAvatarStakeHolderId();
    $staffAssigned = json_decode($competitionData->staff_assigned);
    $found = 0;
    foreach ($staffAssigned as $staff) {
      if ($staff->teachers_id == $loggedInStaffId) {
        $found = 1;
        break;
      }
    }
    return $found;
   }

  public function fetch_competition_wise($id){
    $competition = $this->competition_model->fetch_competitionwiseby($id);
    if (!$this->__checkStaffAccess($competition, $id))
      redirect('competition', 'refresh');

    $data['fetch_competition']  = $competition;
    $data['list_competition'] = $this->competition_model->getlistof_competition();
    $data['staff_details'] = $this->competition_model->get_staff_detils();
    $data['classes'] = $this->competition_model->get_classandsection_detils(); 
    $result_std_show = $this->competition_model->get_resultcompeition_studentlist($id);
    $resultStd = array();
    foreach ($result_std_show as $key => $val) {
        $resultStd[$val->classSection][] = $val;
    }
    $data['result_std_wise'] = $resultStd;
    $data['competition_id'] = $id;
    $data['main_content']    = 'competition/participation/fetch_class_data';
    $this->load->view('inc/template', $data);
  
   }

  public function get_studentClassSectionwise(){
      $class_section = explode(',', $this->input->post('cls_name')); 
      $get_student = $this->competition_model->get_studentclassSection($class_section);
      echo json_encode($get_student);
  }

  public function delete_participation($competition_id, $id){
    $delete = $this->competition_model->delete_participationStudent($id);
    if($delete) {
        $this->session->set_flashdata('flashSuccess', 'Delete Successfully');
        redirect('competition/fetch_competition_wise/'.$competition_id);
       }else{
        $this->session->set_flashdata('flashError', 'Something went wrong..');
         redirect('competition/fetch_competition_wise/'.$competition_id);
       }  

   }
  //End 

   // participation_report

  public function participation_report($id){
    $competition = $this->competition_model->fetch_competitionwiseby($id);
    if (!$this->__checkStaffAccess($competition, $id))
      redirect('competition', 'refresh');
    $data['fetch_competition']  = $competition;
    $data['list_competition'] = $this->competition_model->getlistof_competition();;
    $data['staff_details'] = $this->competition_model->get_staff_detils();
    $data['result_std_wise'] = $this->competition_model->get_resultcompeition_studentlist($id);
    $data['main_content']    = 'competition/participation/report';
    $this->load->view('inc/template', $data);
  }

    public function update_particition_result($id){

     $insert = $this->competition_model->insert_prizeParticipationbyStudent($id);
      if ($insert) {
        $this->session->set_flashdata('flashSuccess', 'Update Successfully');
        redirect('competition/');
       }else{
        $this->session->set_flashdata('flashError', 'No Changes Done');
        redirect('competition/');
       }  
   }
// End

    // Principal Submit 
    public function submit_to_principal($id){
      $competition = $this->competition_model->fetch_competitionwiseby($id);
      if (!$this->__checkStaffAccess($competition, $id))
        redirect('competition', 'refresh');

      $insert = $this->competition_model->staffsubmittoprincipal($id);
      if ($insert) {
        $this->session->set_flashdata('flashSuccess', 'Submitted Successfully');
        redirect('competition');
      }else{
        $this->session->set_flashdata('flashError', 'Something went wrong..');
        redirect('competition');
      }  
    }
// End

  // Pricipal Report //
    public function view_competition($id){
      if (!$this->authorization->isAuthorized('COMPETITION.APPROVE'))
        redirect('competition');

      $data['fetch_competition']  = $this->competition_model->getlistof_competitionbyId($id);
      $data['staff_details'] = $this->competition_model->get_staff_detils();
      $data['result_std_wise'] = $this->competition_model->get_resultcompeition_studentlist($id);
      $data['main_content']    = 'competition/participation/view_competition';
      $this->load->view('inc/template', $data);
     } 

     public function insert_principal_comments($id){
      $insert = $this->competition_model->insert_pricipalcomments($id);
      if ($insert) {
        $this->session->set_flashdata('flashSuccess', 'Update Successfully');
        redirect('competition');
       }else{
        $this->session->set_flashdata('flashError', 'Something went wrong..');
        redirect('competition');
       }  
     }

     public function print_competition($id){
      $data['fetch_competition']  = $this->competition_model->getlistof_competitionbyId($id);
      $data['staff_details'] = $this->competition_model->get_staff_detils();
      $data['result_std_wise'] = $this->competition_model->get_resultcompeition_studentlist($id);
      $data['main_content']    = 'competition/participation/print_competition';
      $this->load->view('inc/template', $data);
     }


     public function take_admin_attendance() { 

      $competition_id =  $data['competition_id']  = $this->input->post('competition');
      $data['today'] = date('Y-m-d', strtotime($this->input->post('competition_timing')));

      $data['competition'] = $this->competition_model->get_competition_by_id($competition_id);

      //check if attendance taken  
      $details = $this->competition_model->checkCompetitionAttendanceTaken($competition_id, $data['today']);

      //echo '<pre>'; print_r($details); die();
      if(empty($details)) {
        $getStudents = $this->competition_model->get_resultcompeition_studentlist($competition_id);
        $data['main_content']    = 'competition/attendance/takeAttendance';
      } else {
        $getStudents = $details;
        $data['main_content']    = 'competition/attendance/editAttendance';
      }

      $data['redirect'] = 'admin';
      $data['getStudents'] = $getStudents;    

      // echo '<pre>'; print_r($data); die();
      $this->load->view('inc/template', $data);

     }

      
     

     public function take_attendance($competition_id) {     
      $competition = $this->competition_model->fetch_competitionwiseby($competition_id);

      if (!$this->__checkStaffAccess($competition, $competition_id))
        redirect('competition', 'refresh');

      $data['competition_id']  = $competition_id;
      $data['today'] = date('Y-m-d');

      //check if attendance taken  
      $details = $this->competition_model->checkCompetitionAttendanceTaken($competition_id, $data['today']);

      $data['competition'] = $this->competition_model->get_competition_by_id($competition_id);

      //echo '<pre>'; print_r($details); die();
      if(empty($details)) {
        $getStudents = $this->competition_model->get_resultcompeition_studentlist($competition_id);
        $data['main_content']    = 'competition/attendance/takeAttendance';
      } else {
        $getStudents = $details;
        $data['main_content']    = 'competition/attendance/editAttendance';
      }

      $data['redirect'] = 'regular';
      $data['getStudents'] = $getStudents;    

      // echo '<pre>'; print_r($data); die();
      $this->load->view('inc/template', $data);

     }

     public function submitAttendance() {
       $input = $this->input->post();
       $stuClassSection = [];

       foreach ($input as $key => $value) {
          if(!in_array($key, ['date','competition_id'])) {
            list($sid, $class, $section) = explode('_', $key);
            $stuClassSection[$class.'_'.$section][] = ['student_id' => $sid, 'status' => $value];
          }
       }

       foreach ($stuClassSection as $cs_key => $cs_value) {

          list($class, $class_section) = explode('_', $cs_key);
          $checkOne = $this->competition_model->checkIfAttendanceTaken($class, $class_section, $input['date']);
          if($checkOne != 0) {
            foreach ($cs_value as $stu_att_details) {
              $this->competition_model->edit_class_attendance_ids($stu_att_details['student_id'],$checkOne, $stu_att_details['status'], $input['competition_id'], 6);
            }
            
          }
       }

       $this->competition_model->saveCompetitionAttendance($input);
       $this->session->set_flashdata('flashSuccess', 'Attendance Taken Successfully');

      if($input['redirect'] == 'admin')
        redirect('competition/showEditAttendance');
      else
        redirect('competition'); 
     }


    public function showEditAttendance() {

      $data['competition'] = $this->competition_model->get_competition();
      $data['main_content']    = 'competition/attendance/showEditAttendance';
      $this->load->view('inc/template', $data);
    }


    public function getAttendanceTime() {

      if(isset($_POST['competitionid'])) {
        $competitionid = $_POST['competitionid'];
        $competitiontime = $this->competition_model->get_competition_time($competitionid);
        echo json_encode($competitiontime);
      } 

    }

    public function editCompetitionAttendance() {

        $id = $_POST['dataId'];
        $status = $_POST['status'];

        if($status == 0) {
          $status = 1;
        } else {
          $status = 0;
        }

        $session_id     = $this->competition_model->edit_attendance_ids($id, $status);
        $attendanceInfo = $this->competition_model->getInfoAttendanceById($id);
        $studentInfo    = $this->competition_model->getStudentInfo($attendanceInfo[0]->student_id, $attendanceInfo[0]->student_admission_id);

        $checkOne       = $this->competition_model->checkIfAttendanceTaken($studentInfo[0]->class_id, $studentInfo[0]->class_section, $attendanceInfo[0]->day);
        
        if($checkOne != 0) { 
          $this->competition_model->edit_class_attendance_ids($attendanceInfo[0]->student_id, $attendanceInfo[0]->student_admission_id,$checkOne, $status, $attendanceInfo[0]->competition_id, 7);
        }
        return true;
    }

  // Clone competition wise serach student name 

    public function get_studentNameCloneCompetition(){
      $cCompetition = $this->input->post('cCompetition');
      $result = $this->competition_model->get_competition_assigned_student($cCompetition);
      echo json_encode($result);
    }

    public function insert_cloneCompetitionstd($competitioncloneId){
     $competition_insert = $this->competition_model->insert_competitionbystudent($competitioncloneId);
     if ($competition_insert) {      
       $this->session->set_flashdata('flashSuccess', 'Update Successfully');
        redirect('competition/fetch_competition_wise/'.$competitioncloneId);
     }else{
       $this->session->set_flashdata('flashError', 'Something went wrong..');
        redirect('competition/fetch_competition_wise/'.$competitioncloneId);
     } 
    }


    /*-------------------- Deletion Script----------------------------*/

    // Competition 
  public function indexd(){
    //Set privileges
    $permitCRUD = $this->authorization->isAuthorized('COMPETITION.ADD_EDIT_DELETE');
    $permitApproval = $this->authorization->isAuthorized('COMPETITION.APPROVE');

    //Is the faculty a default incharge so she gets rights even if she is not assigned to competition
    $isDefaultIncharge = $this->authorization->isAuthorized('COMPETITION.DEFAULT_STAFF_INCHARGE');
    $loggedInStaffId = $this->authorization->getAvatarStakeHolderId();
    
    //Get Competition Data
    $competitionData = $this->competition_model->getlistof_competition();
    foreach ($competitionData['comp'] as &$comp) {
      //Get If the logged in staff is assigned to this competition
      $staffList = json_decode($comp->staff_assigned);
      $found = 0;
      foreach ($staffList as $staff) {
        if ($staff->teachers_id == $loggedInStaffId) {
          $found = 1;
          break;
        }
      }
      $comp->isStaffAssigned = $found || $isDefaultIncharge;

      //Get the dates of the competition
      $dates = '';
      foreach ($competitionData['com_1'] as $dateObj) {
        if ($comp->id == $dateObj->competition_id) {
          if (!empty($dates)) {
            $dates .= '<br>';
          }
          $dates .= date('d-m-Y',strtotime($dateObj->competition_date));
        }
        $comp->dates = $dates;
      }
    }
    $data['permitCRUD'] = $permitCRUD;
    $data['permitApproval'] = $permitApproval;
    $data['list_competition'] = $competitionData;
    $data['competitionAttendance'] = $this->settings->getSetting('competition_attendance');
    $data['staff_details'] = $this->competition_model->get_staff_detils();
    $data['main_content']    = 'competition/indexd';

     //echo '<pre>'; print_r($data); die();

    $this->load->view('inc/template', $data);      
  }

  private function _prepareAttendanceData($details) {

    $preresult = [];
    $day = [];

    foreach ($details as $k => $v) {


      $day[$v['day']] = $v['day'];

      $data = ['id' => $v['id'],
                    'status' => $v['status'],
                    'day' => $v['day']
                    ];

      if(isset($preresult[$v['student_id']])) {
        $preresult[$v['student_id']][$data['day']][] = $data;
      } else {
        $preresult[$v['student_id']]['student'] = 
          ['student_id' => $v['student_id'],
            'std_name' => $v['std_name'],
                    'roll_no' => $v['roll_no'],
                    'classSection' => $v['classSection']
                    ];
                $preresult[$v['student_id']][$data['day']][] = $data;
      }
    }

    foreach ($preresult as $key => $value) {

      $temp_arr = $day; 
      $only_keys = array_keys($value);

      foreach ($temp_arr as $key1 => $value1) {
        if(in_array($key1, $only_keys)) {
          unset($temp_arr[$key1]);
        }
      }

      if(!empty($temp_arr)) {
        foreach ($temp_arr as $key3 => $value3) {
          $preresult[$key][$key3] = [];
        }

        foreach ($day as $key8 => $value) {
          $new_arr[$key8] = $preresult[$key][$key8];
          unset($preresult[$key][$key8]);
        }

        $preresult[$key] = array_merge($new_arr, $preresult[$key]);
      }   

    }

    // based on timestamp
    function compareByTimeStamp($time1, $time2)
    {
        if (strtotime($time1) > strtotime($time2))
            return 1;
        else if (strtotime($time1) < strtotime($time2)) 
            return -1;
        else
            return 0;
    }
   
  
    // sort array with given user-defined function
    usort($day, "compareByTimeStamp");

    // echo '<pre>'; print_r($day); die();

    return ['result' => $preresult, 'days' => $day];

  }


  public function print_competitiond($id){
      $data['fetch_competition']  = $this->competition_model->getlistof_competitionbyId($id);
      $data['staff_details'] = $this->competition_model->get_staff_detils();
      $data['result_std_wise'] = $this->competition_model->get_resultcompeition_studentlist($id);
      $competitionAttendance = $this->competition_model->competitionAttendance($id);
      $data['data'] = $this->_prepareAttendanceData($competitionAttendance);
      $data['main_content']    = 'competition/participation/print_competitiond';

      $this->load->view('inc/template', $data);
  }

  
  public function delete_competitiond($id, $day) {

    $competitionAttendance = $this->competition_model->competitionAttendanceByDay($id, $day);

    $checkDuplicates = [];
    $csession = [];
    $competitionAttendanceIds = [];

    foreach ($competitionAttendance as $key => $value) {

      $checkVal = $value['class_id'].'_'.$value['class_section_id'];

      if(in_array($checkVal, $checkDuplicates)) {
        $competitionAttendance[$key]['attendance_session'] = $csession[$checkVal];

      } else {
        $checkOne = $this->competition_model->checkIfAttendanceTaken($value['class_id'], $value['class_section_id'], $value['day']);

        if($checkOne != 0) {
          $checkDuplicates[] = $checkVal;
          $csession[$checkVal] = $checkOne;
          $competitionAttendance[$key]['attendance_session'] = $checkOne;
        }
      }

      $competitionAttendanceIds[] = $value['id'];      
    }

    foreach ($competitionAttendance as $key1 => $value1) {
      $this->competition_model->edit_attendance_student($value1['student_id'],$value1['attendance_session']);
    }

    $this->competition_model->deleteCompetitionAttendanceById($competitionAttendanceIds);

    $this->session->set_flashdata('flashSuccess', 'Competition Attendance removed Successfully');
    redirect('competition/indexd');

  }



}