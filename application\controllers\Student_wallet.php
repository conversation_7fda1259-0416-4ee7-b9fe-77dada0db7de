<?php

class Student_wallet extends CI_Controller 
{
	function __construct()
	{
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    } 
    if (!$this->authorization->isModuleEnabled('WALLET') && !$this->authorization->isAuthorized('WALLET.MODULE')) {
      redirect('dashboard', 'refresh');
    }
	  $this->load->model('student_wallet_model');
    $this->load->model('student/Student_Model');
    $this->load->library('filemanager');
    
	}


	public function index(){
    $site_url = site_url();
    $data['student_wallet'] = array(
        [
          'title' => 'Load Money',
          'sub_title' => 'Load money for student',
          'icon' => 'svg_icons/loadmoeny.svg',
          'url' => $site_url.'student_wallet/student_load_money',
          'permission' => $this->authorization->isAuthorized('WALLET.LOAD_MONEY')
        ],
        [
          'title' => 'Add Transaction',
          'sub_title' => 'Load money for student',
          'icon' => 'svg_icons/loadmoeny.svg',
          'url' => $site_url.'student_wallet/transaction_money',
          'permission' => $this->authorization->isAuthorized('WALLET.ADD_TRANSACTION')
        ],
        [
          'title' => 'Delete Transaction',
          'sub_title' => 'Load money for student',
          'icon' => 'svg_icons/loadmoeny.svg',
          'url' => $site_url.'student_wallet/delete_transaction_money',
          'permission' => $this->authorization->isAuthorized('WALLET.DELETE_TRANSACTION')
        ],
        [
          'title' => 'Mass Transaction Update',
          'sub_title' => 'Mass Transaction Update',
          'icon' => 'svg_icons/loadmoeny.svg',
          'url' => $site_url.'student_wallet/mass_transaction_update',
          'permission' => $this->authorization->isAuthorized('WALLET.DELETE_TRANSACTION')
        ]
    );
    $data['student_wallet'] = checkTilePermissions($data['student_wallet']);

    $data['wallet_reports'] = array(
        [
          'title' => 'Transaction per Student',
          'sub_title' => 'Student wise Transaction',
          'icon' => 'svg_icons/loadmoeny.svg',
          'url' => $site_url.'student_wallet/student_wallet_report',
          'permission' => $this->authorization->isAuthorized('WALLET.VIEW_WALLET_DETAILS')
        ],
        [
          'title' => 'Wallet Transaction',
          'sub_title' => 'Wallet Transaction',
          'icon' => 'svg_icons/loadmoeny.svg',
          'url' => $site_url.'student_wallet/wallet_report',
          'permission' => 1
        ],
        [
          'title' => 'Balance Report',
          'sub_title' => 'Wallet Balance Report',
          'icon' => 'svg_icons/loadmoeny.svg',
          'url' => $site_url.'student_wallet/wallet_balance_report',
          'permission' => 1
        ],
        [
          'title' => 'Delete Transaction Report',
          'sub_title' => 'Delete Transaction Report',
          'icon' => 'svg_icons/loadmoeny.svg',
          'url' => $site_url.'student_wallet/transaction_delete_report',
          'permission' => 1
        ]

    );
    $data['wallet_reports'] = checkTilePermissions($data['wallet_reports']);

    $data['wallet_administration'] = array(
        [
          'title' => 'Wallet Purpose',
          'sub_title' => 'Student Wallet Purpose',
          'icon' => 'svg_icons/loadmoeny.svg',
          'url' => $site_url.'student_wallet/wallet_purpose',
          'permission' => $this->authorization->isAuthorized('WALLET.VIEW_WALLET_DETAILS')
        ],
        [
          'title' => 'Sync with Fees Transactions',
          'sub_title' => 'Sync with Fees Transactions',
          'icon' => 'svg_icons/loadmoeny.svg',
          'url' => $site_url.'student_wallet/sync_fee_transactions',
          'permission' => $this->authorization->isAuthorized('WALLET.SYNC_FEE_TRANSACTIONS')
        ]
    );
    $data['wallet_administration'] = checkTilePermissions($data['wallet_administration']);
    $data['main_content']    = 'student_wallet/index';
    $this->load->view('inc/template', $data);      
  }

  public function student_load_money(){
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['main_content']    = 'student_wallet/load_money';
    $this->load->view('inc/template', $data);
  }

  public function class_wise_student_wallet_data(){
    $classId = $_POST['classId'];
    $studentIds = $this->student_wallet_model->class_wise_student_wallet_databyId($classId);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function get_student_wallet_report(){
    $student_ids = $_POST['student_ids'];
    $result = $this->student_wallet_model->get_student_wallet_report_list($student_ids);
    echo json_encode($result);
  }

  public function get_checked_student_wallet_preview(){
    $stdIds = $_POST['stdIds'];
    $result = $this->student_wallet_model->get_student_wallet_report_list($stdIds);
    echo json_encode($result);
  }

  public function insert_student_wise_wallet_amount(){
    $wallet_amount = $this->input->post('student_wallet_confrim');
    $balance_amount = $this->input->post('student_balance_wallet_confrim');
    $remarks = $this->input->post('remarks');
    $wallet_tx_data = [];
    foreach ($wallet_amount as $std_id => $amount) {
      $wallet_tx_data[] = array(
        'amount' =>  $amount, 
        'student_id' =>  $std_id, 
        'transaction_mode' => 'offline', 
        'transaction_type' => 'Load', 
        'created_by' => $this->authorization->getAvatarId(),
        'status' => 'active',
        'running_balance_amount' =>$amount + $balance_amount[$std_id],
        'remarks'=>$remarks,
        'source_type'=>'Wallet',
        'acad_year_id'=>$this->acad_year->getAcadYearId()
      );
    }
    $wallet_data = [];
    $update_walletData = [];
    foreach ($wallet_amount as $std_id => $amount) {
      $this->db->where('student_id', $std_id);
      $query = $this->db->get('student_wallet');
      $this->db->reset_query();

      if ($query->num_rows() > 0) {
        $update_walletData[] = array(
          'student_id' =>  $std_id, 
          'wallet_amount' =>  $amount + $balance_amount[$std_id],
        );
      }else{
        $wallet_data[] = array(
          'student_id' =>  $std_id, 
          'wallet_amount' =>  $amount,
        );
      }
    }

    $this->db->trans_begin();
    $result = $this->student_wallet_model->inert_wallet_data($wallet_tx_data,'student_wallet_transactions');
    if ($result) {
      if (!empty($wallet_data)) {
        $this->student_wallet_model->inert_wallet_data($wallet_data,'student_wallet');
      }
      if (!empty($update_walletData)) {
        $this->student_wallet_model->update_wallet_data($update_walletData,'student_wallet');
      }

      $this->db->trans_complete();
      if ($this->db->trans_status()) {
        echo 1;
      }else{
        echo 0;
      }
    }else{
      echo 0;
    }

  }
  public function wallet_report(){
    $data['acadList'] = $this->student_wallet_model->getAcadYearList();
    $data['srouceType'] = $this->student_wallet_model->get_sourcetype();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['wallet_purpose'] = $this->student_wallet_model->getpurposeList_createpage();
    $data['main_content']    = 'student_wallet/wallet_report';
    $this->load->view('inc/template', $data);
  }

  public function get_wallet_tx_report(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $classId = $_POST['classId'];
    $source_type = $_POST['source_type'];
    $transaction_type = $_POST['transaction_type'];
    $purpose = $_POST['purpose'];
    if(!  $this->settings->getSetting('disable_acad_year_student_wallet_transcend') ){
    $acadYear = $_POST['acadYear'];
    } else {
      $acadYear = null;
    }
    $StudentIds = $this->student_wallet_model->get_wallet_transaction_report($from_date, $to_date,$classId,$source_type,$transaction_type, $purpose,$acadYear);
    $StudentIds = array_chunk($StudentIds, 100);
    echo json_encode($StudentIds);
  }

  public function get_wallet_tx_report_student_wise(){
    $walletStudentIds = $_POST['walletStudentIds'];
    $purpose = $_POST['purpose'];
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    if(!  $this->settings->getSetting('disable_acad_year_student_wallet_transcend') ){
    $acadYear = $_POST['acadYear'];
  } else {
    $acadYear = null;
  }
    $classId = $_POST['classId'];
    $result = $this->student_wallet_model->get_wallet_tx_report_student_wise_data($walletStudentIds, $purpose, $from_date, $to_date,$acadYear,$classId);
    echo json_encode($result);
  }
  public function student_wallet_report(){
    $data['classSection'] = $this->Student_Model->getClassSectionNames();
    $data['SourceType'] = $this->student_wallet_model->get_sourcetype();
    $data['all_names'] = $this->student_wallet_model->get_all_staff_student_names();
    $data['acadList'] = $this->student_wallet_model->getAcadYearList();

    $data['main_content']    = 'student_wallet/wallet_student_report';
    $this->load->view('inc/template', $data);
  }

  public function get_student_wallet_history(){
    $studentId = $_POST['studentId'];
    if(!  $this->settings->getSetting('disable_acad_year_student_wallet_transcend') ){
      $acadYear = $_POST['acadYear'];
    } else {
      $acadYear = null;
    }
    $result = $this->student_wallet_model->get_student_wallet_history_by_id($studentId,$acadYear);
    echo json_encode($result);
  }

  public function get_student_wallet_history_without_load_money(){
    $studentId = $_POST['studentId'];
    if(!  $this->settings->getSetting('disable_acad_year_student_wallet_transcend') ){
    $acadYear = $_POST['acadYear'];
    } else {
      $acadYear = null;
    }
    $result = $this->student_wallet_model->get_student_wallet_history_by_id_without_load_money($studentId,$acadYear);
    echo json_encode($result);
  }

  public function wallet_balance_report(){
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['main_content']    = 'student_wallet/balance_wallet_report';
    $this->load->view('inc/template', $data);
  }

  public function get_wallet_balance_report(){
    $classId = $_POST['classId'];
    $StudentIds = $this->student_wallet_model->get_wallet_balance_report_studentId($classId);
    $StudentIds = array_chunk($StudentIds, 150);
    echo json_encode($StudentIds);
  }

  public function get_wallet_balance_report_student_wise(){
    $walletStudentIds = $_POST['walletStudentIds'];
    $result = $this->student_wallet_model->get_wallet_balance_report_student_wise_data($walletStudentIds);
    echo json_encode($result);
  }

  public function transaction_money(){
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['all_names'] = $this->student_wallet_model->get_all_staff_student_names_approved_only();
    $data['wallet_purpose'] = $this->student_wallet_model->getpurposeList_createpage();
    $data['main_content']    = 'student_wallet/transaction_money';
    $this->load->view('inc/template', $data);
  }

  public function get_student_transcation_report(){
    $student_ids = $_POST['student_ids'];
    $result = $this->student_wallet_model->get_student_wallet_transcation_list($student_ids);
    echo json_encode($result);
  }

  public function search_by_name_submit_and_stay(){
    $studentId = $_POST['studentId'];
    $result = $this->student_wallet_model->search_by_name_submit_and_stay($studentId);
    $result->new_wallet_balance = $this->student_wallet_model->get_student_wallet_balance($studentId,null);
    echo json_encode($result);

  }

  public function search_by_name(){
    $studentId = $_POST['studentId'];
    $result = $this->student_wallet_model->get_search_by_name_ById($studentId);
    $splitStudent =  explode('_', $studentId);
    $staff_student =  explode(',', $splitStudent[1]);
    $useSelection = $staff_student[0];
    $result->new_wallet_balance = $this->student_wallet_model->get_student_wallet_balance($useSelection,null);
    echo json_encode($result);

  }

  public function search_by_name_with_out_load_money(){
    $studentId = $_POST['studentId'];
    $result = $this->student_wallet_model->get_search_by_name_ById_with_out_load_money($studentId);
    echo json_encode($result);

  }

  public function get_admission_student_data() {
      $admin_no = $_POST['admin_no'];
      $result = $this->student_wallet_model->get_admission_student_data($admin_no);
      echo json_encode($result);
  }

  public function get_admission_student_data_without_load_money() {
      $admin_no = $_POST['admin_no'];
      // echo "<pre>"; print_r($admin_no); die();
      $result = $this->student_wallet_model->get_admission_student_data_without_load_money($admin_no);
      echo json_encode($result);
  }

  public function save_transaction() {
    $transaction_status= $this->student_wallet_model->save_transaction($this->s3FileUpload($_FILES['certificate_name'], 'student_wallet_document'));
    
    if($transaction_status){
      $parents=$this->student_wallet_model->get_parent_details($_POST['studentId']);
      $this->load->model('communication/texting_model', 'texting_model');
      $school_name = $this->settings->getSetting('school_name');
      $notification = "Dear Parent, the transaction has been successfully completed.";
      
      $text_master = array(
        'title' => $school_name,
        'message' => $notification,
        'sent_by' => $this->authorization->getAvatarId(),
        'reciever' => 'Students',
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'source' => 'Wallet Transaction',
        'text_count' => 0,
        'visible' => 1,
        'mode' => 'notification',
        'sms_credits' => 0,
        'is_unicode' => 0,
        'sender_list' => NULL,
        'sending_status' => 'Initiated'
      );
      $texting_master_id = $this->texting_model->save_texts($text_master);
   
      $url = site_url('parent_controller/wallet');
      $textingData = [];
      foreach($parents as $parent){
       $textingData[] = array(
         'texting_master_id' => $texting_master_id,
         'stakeholder_id' => $parent->parent_id,
         'mobile_no' => $parent->parent_mobile_no,
         'mode' => 1,
         'status' => ($parent->tokenState == 0 )? 'No Token' : 'Sent',
         'avatar_type' => 2,
         'is_read' => 0,
         'user_id' => $parent->user_id,
         'token' => $parent->user_token,
       );
     }
    $token_data = $this->texting_model->save_notifications($textingData);
    $this->load->helper('notification_helper');
    $notification_status = commonNotifications($token_data, $school_name, $notification, $url);

    // mail
    $this->load->model('email_model');
    $this->load->helper('email_helper');
    $sender_list = [];
    $email_ids = [];
    foreach ($parents as $key => $val) {
        array_push($email_ids, $val->parent_email);
        $sender_list['parents'][] = [
            'send_to' => '',
            'send_to_type' => $val->relation_type,
            'ids' => $val->parent_id,
        ];
    }
    $email_template = $this->email_model->get_email_template_to_send_visitor('student_wallet_transaction_parent_mail');
    if(!empty($email_template)){
      $emailBody =  $email_template->content;    
      $emailBody = str_replace('%%transaction_type%%', 'Debit', $emailBody);
      $emailBody = str_replace('%%amount%%', $_POST['used_money'], $emailBody);
      $email_master_data = array(
        'subject' => $email_template->email_subject,
        'body' => $emailBody,
        'source' => 'Wallet Transaction',
        'sent_by' => $this->authorization->getAvatarId(),
        'recievers' => 'Parents',
        'from_email' => $email_template->registered_email,
        'files' => NULL,
        'acad_year_id' => $this->acad_year->getAcadYearID(),
        'visible' => 1,
        'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
        'sending_status' => 'Completed'
      );
      $this->load->model('communication/emails_model');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      $email_data = [];
      foreach ($parents as $key => $val) {
        if(empty($val)){
          continue;
        }
        $email_obj = new stdClass();
        $email_obj->stakeholder_id = $val->parent_id;
        $email_obj->avatar_type = 2;
        $email_obj->email = $val->parent_email;
        $email_data[] = $email_obj;
      }
      $this->emails_model->save_sending_email_data($email_data,$email_master_id);  
      $email = $this->emails_model->getEmailInfo($email_master_id);
      sendEmail($emailBody, $email_template->email_subject,0,$email_ids, $email_template->registered_email);
    }
    }
    
    echo $transaction_status;
  }

  private function s3FileUpload($file, $folder_name = 'wallet')
  {
    if ($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }
    return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
  }

  public function wallet_purpose(){
    $data['main_content']    = 'student_wallet/student_wallet_purpose';
    $this->load->view('inc/template', $data);
  }

  public function savewallet_purpose() {
   $result = $this->student_wallet_model->insertwallet_purpose();
   if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Added Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    redirect('student_wallet/wallet_purpose');
  }

  public function getpurposelist(){
    $result = $this->student_wallet_model->getpurposelist();
    echo json_encode($result);
  }

  public function update_purpose(){
    $id = $_POST['id'];
    $wallet_purpose = $_POST['wallet_purpose'];
    echo $this->student_wallet_model->update_purpose($id,$wallet_purpose);
  }

  public function activate_purpose() {
    $purposeid = $_POST['purposeid'];
    $success = $this->student_wallet_model->activate_purpose($purposeid);
    echo json_encode($success);
  }

  public function deactivate_purpose() {
    $purposeid = $_POST['purposeid'];
    $success = $this->student_wallet_model->deactivate_purpose($purposeid);
    echo json_encode($success);
  }

  public function delete_transaction_money(){
    $data['all_names'] = $this->student_wallet_model->get_all_staff_student_names();
    $data['main_content']    = 'student_wallet/delete_transaction_money';
    $this->load->view('inc/template', $data);
  }

  public function delete_transaction_list(){
    $studentId = $_POST['studentId'];
    $success = $this->student_wallet_model->delete_transaction_list($studentId);
    echo json_encode($success);
  }

  public function delete_transaction_list_with_out_load_money(){
    $studentId = $_POST['studentId'];
    $success = $this->student_wallet_model->delete_transaction_list_with_out_load_money($studentId);
    echo json_encode($success);
  }

  public function delete_student_transaction_id(){
    $id = $_POST['id'];
    $amount = $_POST['amount'];
    $stdId = $_POST['stdId'];
    $remarks = $_POST['remarks'];
    $success = $this->student_wallet_model->delete_student_transaction_id($id, $amount, $stdId, $remarks);
    echo json_encode($success);
  }

  public function transaction_delete_report(){
    $data['main_content']    = 'student_wallet/delete_transaction_report';
    $this->load->view('inc/template', $data);
  }

  public function getdelete_report(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $success = $this->student_wallet_model->delete_transaction_report($from_date, $to_date);
    echo json_encode($success);
  }

  public function delete_transaction_by_id_without_load_money(){
    $id = $_POST['id'];
    $amount = $_POST['amount'];
    $stdId = $_POST['stdId'];
    $remarks = $_POST['remarks'];
    $success = $this->student_wallet_model->delete_transaction_by_id_without_load_money($id, $amount, $stdId, $remarks);
    echo json_encode($success);
  }

  public function get_class_section_wise_wallet_std_data(){
    $cls_section = $this->input->post('cls_section');
    list($class_id,$sectionId) = explode('_',$cls_section);
    $result = $this->student_wallet_model->get_class_section_studetn_wallet_data($class_id,$sectionId);
    echo json_encode($result);
  }

  public function mass_transaction_update(){
    $data['main_content']    = 'student_wallet/mass_transaction_update';
    $this->load->view('inc/template', $data);
  }

  public function insert_data_into_student_wallet(){
    echo $this->student_wallet_model->insert_data_into_student_wallet($_POST); 
  }

  public function sync_fee_transactions(){
    $data['acadList'] = $this->student_wallet_model->getAcadYearList();
    $data['main_content']    = 'student_wallet/sync_fee_transactions_view';
    $this->load->view('inc/template', $data);
  }

  public function sync_fees_transactions_into_db(){
    $acad_year_id=$_POST['acad_year'];
    $result = $this->student_wallet_model->sync_fees_transactions_into_db($acad_year_id);
    echo json_encode($result);
  }
  
}