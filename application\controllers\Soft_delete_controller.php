<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Soft_delete_controller extends CI_Controller{
	function __construct(){
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      	redirect('auth/login', 'refresh');
    	} 
     	if (!$this->authorization->isSuperAdmin()) {
      		redirect('dashboard', 'refresh');
    	}
		$this->load->model('soft_delete_model');
	}

  	public function index() {

	    $data['getFeePaidStudents'] = $this->soft_delete_model->getAllFeePaidStudentDetails();  
	    //echo "<pre>"; print_r($data['getFeePaidStudents']); die();
	    //$data['selectedClassId'] = $classId;      
	    //echo '<pre>';print_r($data['getstudents']);die();
	    $data['main_content'] = 'soft_delete/index';
	    $this->load->view('inc/template', $data);

    }

    public function delete(){
    	$result=$this->soft_delete_model->soft_deleteFeesTables();
	    if($result){
	      $this->session->set_flashdata('flashSuccess', 'Delete Successfully.');
	      redirect('soft_delete_controller');
	    }else{
	       $this->session->set_flashdata('flashError', 'Some Thing Wrong.');
	      redirect('soft_delete_controller');
	    }
    }
}