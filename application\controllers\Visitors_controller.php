<?php
//updated code
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Visitors_controller
 *
 * <AUTHOR>
 */
class Visitors_controller extends CI_Controller{
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('VISITOR')) {
            redirect('dashboard', 'refresh');
        }
       
        $this->load->model('Visitor_model');
        $this->load->library('upload');
        $this->load->library('filemanager');
        $this->config->load('form_elements');
        $this->load->helper('sms_helper');
    }
    /* testew code addd */
    public function visitor_index($cloneId=''){
      $data['cloneData'] = array();
      $data['clone'] = 0;
      if($cloneId != ''){
        $data['cloneData'] =$this->Visitor_model->getCloneData($cloneId);
        $data['clone'] = 1;
      }

      $data['toMeet'] = $this->Visitor_model->getToMeet();
      $data['main_content'] = 'visitors/add';
      $this->load->view('inc/template', $data);
    }

    public function visitor_dashboard() {
      $data['register_permit'] = $this->authorization->isAuthorized('VISITOR.REGISTER');
      $data['vistor_view_permit'] = $this->authorization->isAuthorized('VISITOR.VIEW');
      $site_url = site_url();
      $data['tiles'] = array(
        [
          'title' => 'Register',
          'sub_title' => 'Register a vistor',
          'icon' => 'svg_icons/reportcard.svg',
          'url' => $site_url.'Visitors_controller/outgoing_visitors',
          'permission' => $data['register_permit']
        ],
        [
          'title' => 'Visitor Report',
          'sub_title' => 'View Visitors',
          'icon' => 'svg_icons/visitorreport.svg',
          'url' => $site_url.'Visitors_controller/visitor_detail',
          'permission' => $data['vistor_view_permit']
        ]
      );
      $data['tiles'] = checkTilePermissions($data['tiles']);

      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'visitors/dashboard_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'visitors/dashboard_mobile';
      }else{
        $data['main_content'] = 'visitors/dashboard';     	
      }

      
      $this->load->view('inc/template', $data);
    }

 public function staff_registration_details()
	{ 
	   $staff_details = $this->Visitor_model->staff_details();
	    echo json_encode($staff_details);
	   
	}
 public function class_list()
	{ 
	   $class_details = $this->Visitor_model->get_allclass_dp();
	    echo json_encode($class_details);
	 
	}
  public function student_list()   
  {
    $class_info=$_POST['class_info'];
    list($classId, $sectionId) = explode("_", $class_info);
    $student_details = $this->Visitor_model->get_allstudent($classId, $sectionId);
    echo json_encode($student_details);
  }
   
    public function s3FileUpload($file) {
        if($_FILES['webcam']['tmp_name'] == '' || $_FILES['webcam']['name'] == '') {
        return ['status' => 'empty', 'file_name' => ''];
        }        
        return $this->filemanager->uploadFile($_FILES['webcam']['tmp_name'],$_FILES['webcam']['name'],'profile');
    }

   public function add_visitor_info() {
     $visitors_details = $this->Visitor_model->add_visitor_list();
     $to_meet = $this->input->post('to_meet');
     $numbers = $this->config->item('numbers');
     if(!empty($numbers[$to_meet])) {
      $sms = "Hi, you got a visitor ".$this->input->post('visitor_name')." today.";
      $insId = sendToCustomNumbers(array($numbers[$to_meet]),'Visitor', $sms);
      if($insId == -1) {
        $this->session->set_flashdata('flashError', 'Sms not sent. Credits unavailable');
        redirect('Visitors_controller/outgoing_visitors');
      }
     }
     if($visitors_details){
        $this->session->set_flashdata('flashSuccess', 'Visitor Info added successfully');
        redirect('Visitors_controller/outgoing_visitors');
     }else{
        $this->session->set_flashdata('flashError', 'Something Wrong..');
        redirect('Visitors_controller/outgoing_visitors');
     }
   }  
    public function outgoing_visitors() {
        $date = $this->input->post('date');
        if(empty($date)){
          $date = date('Y-m-d');
        }
        $data['selected'] = $date;
        $date = date('Y-m-d', strtotime($date));
        $data['visitors_list'] = $this->Visitor_model->visitorDataByDate($date);
        $data['total'] = $this->Visitor_model->getCounts($date, 1);
        $data['checkOut'] = $this->Visitor_model->getCounts($date, 0);
        // echo "<pre>"; print_r($data);die();
        $data['main_content']    = 'visitors/index';
       $this->load->view('inc/template', $data);
    } 

   public function checkOutVistor(){
      $id = $_POST['id'];
      $date = $_POST['date'];
      $status = $this->Visitor_model->vistor_check_out_insert($id, $date);
      echo $status;
   }
    public function visitor_detail() {
        $fdate = $this->input->post('fdate');
        $todate = $this->input->post('todate');
        if(empty($fdate)){
          $fdate = date('Y-m-d');
          $todate = date('Y-m-d');
        }
        $data['selectedFrom'] = $fdate;
        $data['selectedTo'] = $todate;
        $data['visitors_det'] = $this->Visitor_model->vistor_details($fdate, $todate);
       // echo "<pre>";print_r($data['visitors_det']) ;die();
         $data['main_content']    = 'visitors/detail';
     $this->load->view('inc/template', $data);
     //  echo "<ore>";print_r($data['visitors_det']) ;die();
   } 
   public function captureImage(){
        $filename = '';
        if(isset($_FILES['webcam']['name'])){
            $filepath = $this->s3FileUpload($_FILES['webcam']['name']);
            echo $filepath['file_name'];
        }
   }

   public function getData(){
    $number = $_POST['mobile'];
    $data = $this->Visitor_model->getDataByNumber($number);
    echo json_encode($data);
   }
}
