<?php
class Afl_rubrics extends CI_Controller {
    private $yearId;
    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
       redirect('auth/login', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('afl/Afl_rubrics_model','rubric_model');
      $this->load->model('afl/Afl_grading_model','grading_model');
      $this->load->model('Class_section', 'class_section');
    }

    public function index(){
      $data['rubrics'] = $this->rubric_model->getRubrics();
      $data['main_content'] = 'afl/rubrics/index';
      $this->load->view('inc/template', $data);
    }

    public function add_rubric() {
      $data['grades'] = $this->grading_model->getGradingScales();
      $data['main_content'] = 'afl/rubrics/add_rubric';
      $this->load->view('inc/template', $data);
    }

    public function saveRubric() {
      // $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $status = $this->rubric_model->saveRubric();
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Rubric Added Successfully.');
      } else {
        $this->session->set_flashdata('flashError', 'Something Went Wrong.');
      }
      redirect('afl/afl_rubrics');
    }

    public function rubricDetails($rubricId) {
      $data['grades'] = $this->grading_model->getGradingScales();
      $data['rubric'] = $this->rubric_model->getRubric($rubricId);
      $data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($data['rubric']->afl_grading_scale_id);
      $data['rubric_parameters'] = $this->rubric_model->getRubricParameters($rubricId);
      $data['main_content'] = 'afl/rubrics/rubric_details';
      $this->load->view('inc/template', $data);
    }

    public function updateRubric() {
      $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $status = $this->rubric_model->updateRubric();
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Rubric Updated Successfully.');
      } else {
        $this->session->set_flashdata('flashError', 'Something Went Wrong.');
      }
      redirect('afl/afl_rubrics/rubricDetails/'.$input['rubric_id']);
    }

    public function updateParameters() {
      $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $status = $this->rubric_model->updateParameters();
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Parameters Updated Successfully.');
      } else {
        $this->session->set_flashdata('flashError', 'Something Went Wrong.');
      }
      redirect('afl/afl_rubrics/rubricDetails/'.$input['rubric_id']);
    }
}