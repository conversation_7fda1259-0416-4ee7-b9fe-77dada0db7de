<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN"
   "http://www.w3.org/TR/REC-html40/strict.dtd">
<html lang=en>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>HTML 4.0 Entities for Symbols and Greek Letters</title>
<link rev=Made href="mailto:<EMAIL>">
<link rel=Start href="../index.html">
<link rel=Prev href="latin1.html">
<link rel=Next href="special.html">
<style>
body {
  background: white;
  color: black;
  font-family: "DejaVu Sans", sans-serif;
}

h1 {
  color: #c33;
  background: none;
  font-weight: bold;
  text-align: center;
}

h2 {
  color: #00008b;
  background: none;
  font-weight: bold;
}

h3 {
  color: #006400;
  background: none;
  margin-left: 4%;
  margin-right: 4%;
  font-weight: bold;
}

h4 {
  margin-left: 6%;
  margin-right: 6%;
  font-weight: bold;
}

h5 {
  margin-left: 6%;
  margin-right: 6%;
  font-weight: bold;
}

ul, ol, dl, p {
  margin-left: 6%;
  margin-right: 6%;
}

ul ul, table ol, table ul, dl ol, dl ul {
  margin-left: 1.2em;
  margin-right: 1%;
  padding-left: 0;
}

pre {
  margin-left: 10%;
  white-space: pre;
}

table caption {
  font-size: larger;
  font-weight: bolder;
}

table p, table dl, ol p, ul p, dl p, blockquote p, .note p, .note ul, .note ol, .note dl, li pre, dd pre {
  margin-left: 0;
  margin-right: 0;
}

p.top {
  margin-left: 1%;
  margin-right: 1%;
}

blockquote {
  margin-left: 8%;
  margin-right: 8%;
  border: thin ridge #dc143c;
}

blockquote pre {
  margin-left: 1%;
  margin-right: 1%;
}

dt a {
  font-weight: bold;
  margin-top: .8em;
}

a:link {
  color: #00f;
  background: none;;
}

a:visited {
  color: #800080;
  background: none;;
}

a:active {
  color: green;
  background: #FFD700;
}

.html {
  color: #000080;
  background: none;
}

.css {
  color: #800000;
  background: none;
}

.javascript {
  color: #008000;
  background: none;
}

.example { margin-left: 10% }

dfn {
  font-style: normal;
  font-weight: bolder;
}

var sub { font-style: normal }

.note {
  font-size: 85%;
  margin-left: 10%;
}

.SMA {
  color: fuchsia;
  background: none;
  font-family: Kids, "Comic Sans MS", Jester;
}

.oops {
  font-family: Jester, "Comic Sans MS";
}

.author {
  font-style: italic;
}

.copyright {
  font-size: smaller;
  text-align: right;
  clear: right;
}

.toolbar {
  text-align: center;
}

.toolbar IMG {
  float: right;
}

.error {
  color: #DC143C;
  background: none;
  text-decoration: none;
}

.warning {
  color: #FF4500;
  background: none;
  text-decoration: none;
}

.error strong {
  color: #DC143C;
  background: #FFD700;
  text-decoration: none;
}

.warning strong {
  color: #FF4500;
  background: #FFD700;
  text-decoration: none;
}

.warning a:link, .warning a:visited, .warning a:active {
  color: #FF4500;
  background: none;
  text-decoration: underline;
}

.error a:link, .error a:visited, .error a:active {
  color: #DC143C;
  background: none;
  text-decoration: underline;
}

.error strong a:link, .error strong a:visited, .error strong a:active {
  color: #DC143C;
  background: #FFD700;
}

.warning strong a:link, .warning strong a:visited, .warning strong a:active {
  color: #FF4500;
  background: #FFD700;
}

colgroup.entity { text-align: center }

.default { text-decoration: underline; font-style: normal }
.required { font-weight: bold }
td li.transitional, .elements li.transitional {
  font-weight: lighter;
  color: #696969;
  background: none;
}
td li.frameset, .elements li.frameset {
  font-weight: lighter;
  color: #808080;
  background: none;
}

.footer, .checkedDocument {
  margin-top: 2em;
  padding-top: 1em;
  border-top: solid thin black;
}

strong.legal {
  font-weight: normal;
  text-transform: uppercase;
}

@media print {
  input#toggler, .toolbar { display: none }
}


table { border-collapse: collapse; width: 100%; }
td { border: 0.5pt solid black; }

</style>
<meta name="author" content="Liam Quinn">
<meta name="description" content="A table of the HTML 4.0 entities for symbols and Greek letters.">
<meta name="keywords" content="symbols, mathematical symbols, math, Greek letters, alpha, beta, Unicode, entities, characters, character set, HTML, HyperText Markup Language, HTML 4.0, HTML4, character entity reference, decimal, hexadecimal, hex, browser test, WDG, Web Design Group">
<body>
<h1>Entities for Symbols and Greek Letters</h1>
<p>The following table gives the character entity reference, decimal character reference, and hexadecimal character reference for symbols and Greek letters, as well as the rendering of each in your browser. <a href="http://www.unicode.org/charts/">Glyphs</a> of the characters are available at the <a href="http://www.unicode.org/">Unicode Consortium</a>.</p>
<p>Browser support for these entities is generally quite poor, but recent browsers support some of the character entity references and decimal character references.</p>
<table>
  <thead>
    <tr>
      <th scope=col rowspan=2>Character</th>
      <th scope=col rowspan=2>Entity</th>
      <th scope=col rowspan=2>Decimal</th>
      <th scope=col rowspan=2>Hex</th>
      <th scope=colgroup colspan=3>Rendering in Your Browser</th>
    </tr>
    <tr>
      <th scope=col>Entity</th>
      <th scope=col>Decimal</th>
      <th scope=col>Hex</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td scope=row>Latin small f with hook = function = florin</td>
      <td>&amp;fnof;</td>
      <td>&amp;#402;</td>
      <td>&amp;#x192;</td>
      <td>&fnof;</td>
      <td>&#402;</td>
      <td>&#x192;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter alpha</td>
      <td>&amp;Alpha;</td>
      <td>&amp;#913;</td>
      <td>&amp;#x391;</td>
      <td>&Alpha;</td>
      <td>&#913;</td>
      <td>&#x391;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter beta</td>
      <td>&amp;Beta;</td>
      <td>&amp;#914;</td>
      <td>&amp;#x392;</td>
      <td>&Beta;</td>
      <td>&#914;</td>
      <td>&#x392;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter gamma</td>
      <td>&amp;Gamma;</td>
      <td>&amp;#915;</td>
      <td>&amp;#x393;</td>
      <td>&Gamma;</td>
      <td>&#915;</td>
      <td>&#x393;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter delta</td>
      <td>&amp;Delta;</td>
      <td>&amp;#916;</td>
      <td>&amp;#x394;</td>
      <td>&Delta;</td>
      <td>&#916;</td>
      <td>&#x394;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter epsilon</td>
      <td>&amp;Epsilon;</td>
      <td>&amp;#917;</td>
      <td>&amp;#x395;</td>
      <td>&Epsilon;</td>
      <td>&#917;</td>
      <td>&#x395;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter zeta</td>
      <td>&amp;Zeta;</td>
      <td>&amp;#918;</td>
      <td>&amp;#x396;</td>
      <td>&Zeta;</td>
      <td>&#918;</td>
      <td>&#x396;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter eta</td>
      <td>&amp;Eta;</td>
      <td>&amp;#919;</td>
      <td>&amp;#x397;</td>
      <td>&Eta;</td>
      <td>&#919;</td>
      <td>&#x397;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter theta</td>
      <td>&amp;Theta;</td>
      <td>&amp;#920;</td>
      <td>&amp;#x398;</td>
      <td>&Theta;</td>
      <td>&#920;</td>
      <td>&#x398;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter iota</td>
      <td>&amp;Iota;</td>
      <td>&amp;#921;</td>
      <td>&amp;#x399;</td>
      <td>&Iota;</td>
      <td>&#921;</td>
      <td>&#x399;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter kappa</td>
      <td>&amp;Kappa;</td>
      <td>&amp;#922;</td>
      <td>&amp;#x39A;</td>
      <td>&Kappa;</td>
      <td>&#922;</td>
      <td>&#x39A;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter lambda</td>
      <td>&amp;Lambda;</td>
      <td>&amp;#923;</td>
      <td>&amp;#x39B;</td>
      <td>&Lambda;</td>
      <td>&#923;</td>
      <td>&#x39B;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter mu</td>
      <td>&amp;Mu;</td>
      <td>&amp;#924;</td>
      <td>&amp;#x39C;</td>
      <td>&Mu;</td>
      <td>&#924;</td>
      <td>&#x39C;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter nu</td>
      <td>&amp;Nu;</td>
      <td>&amp;#925;</td>
      <td>&amp;#x39D;</td>
      <td>&Nu;</td>
      <td>&#925;</td>
      <td>&#x39D;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter xi</td>
      <td>&amp;Xi;</td>
      <td>&amp;#926;</td>
      <td>&amp;#x39E;</td>
      <td>&Xi;</td>
      <td>&#926;</td>
      <td>&#x39E;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter omicron</td>
      <td>&amp;Omicron;</td>
      <td>&amp;#927;</td>
      <td>&amp;#x39F;</td>
      <td>&Omicron;</td>
      <td>&#927;</td>
      <td>&#x39F;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter pi</td>
      <td>&amp;Pi;</td>
      <td>&amp;#928;</td>
      <td>&amp;#x3A0;</td>
      <td>&Pi;</td>
      <td>&#928;</td>
      <td>&#x3A0;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter rho</td>
      <td>&amp;Rho;</td>
      <td>&amp;#929;</td>
      <td>&amp;#x3A1;</td>
      <td>&Rho;</td>
      <td>&#929;</td>
      <td>&#x3A1;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter sigma</td>
      <td>&amp;Sigma;</td>
      <td>&amp;#931;</td>
      <td>&amp;#x3A3;</td>
      <td>&Sigma;</td>
      <td>&#931;</td>
      <td>&#x3A3;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter tau</td>
      <td>&amp;Tau;</td>
      <td>&amp;#932;</td>
      <td>&amp;#x3A4;</td>
      <td>&Tau;</td>
      <td>&#932;</td>
      <td>&#x3A4;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter upsilon</td>
      <td>&amp;Upsilon;</td>
      <td>&amp;#933;</td>
      <td>&amp;#x3A5;</td>
      <td>&Upsilon;</td>
      <td>&#933;</td>
      <td>&#x3A5;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter phi</td>
      <td>&amp;Phi;</td>
      <td>&amp;#934;</td>
      <td>&amp;#x3A6;</td>
      <td>&Phi;</td>
      <td>&#934;</td>
      <td>&#x3A6;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter chi</td>
      <td>&amp;Chi;</td>
      <td>&amp;#935;</td>
      <td>&amp;#x3A7;</td>
      <td>&Chi;</td>
      <td>&#935;</td>
      <td>&#x3A7;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter psi</td>
      <td>&amp;Psi;</td>
      <td>&amp;#936;</td>
      <td>&amp;#x3A8;</td>
      <td>&Psi;</td>
      <td>&#936;</td>
      <td>&#x3A8;</td>
    </tr>
    <tr>
      <td scope=row>Greek capital letter omega</td>
      <td>&amp;Omega;</td>
      <td>&amp;#937;</td>
      <td>&amp;#x3A9;</td>
      <td>&Omega;</td>
      <td>&#937;</td>
      <td>&#x3A9;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter alpha</td>
      <td>&amp;alpha;</td>
      <td>&amp;#945;</td>
      <td>&amp;#x3B1;</td>
      <td>&alpha;</td>
      <td>&#945;</td>
      <td>&#x3B1;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter beta</td>
      <td>&amp;beta;</td>
      <td>&amp;#946;</td>
      <td>&amp;#x3B2;</td>
      <td>&beta;</td>
      <td>&#946;</td>
      <td>&#x3B2;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter gamma</td>
      <td>&amp;gamma;</td>
      <td>&amp;#947;</td>
      <td>&amp;#x3B3;</td>
      <td>&gamma;</td>
      <td>&#947;</td>
      <td>&#x3B3;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter delta</td>
      <td>&amp;delta;</td>
      <td>&amp;#948;</td>
      <td>&amp;#x3B4;</td>
      <td>&delta;</td>
      <td>&#948;</td>
      <td>&#x3B4;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter epsilon</td>
      <td>&amp;epsilon;</td>
      <td>&amp;#949;</td>
      <td>&amp;#x3B5;</td>
      <td>&epsilon;</td>
      <td>&#949;</td>
      <td>&#x3B5;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter zeta</td>
      <td>&amp;zeta;</td>
      <td>&amp;#950;</td>
      <td>&amp;#x3B6;</td>
      <td>&zeta;</td>
      <td>&#950;</td>
      <td>&#x3B6;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter eta</td>
      <td>&amp;eta;</td>
      <td>&amp;#951;</td>
      <td>&amp;#x3B7;</td>
      <td>&eta;</td>
      <td>&#951;</td>
      <td>&#x3B7;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter theta</td>
      <td>&amp;theta;</td>
      <td>&amp;#952;</td>
      <td>&amp;#x3B8;</td>
      <td>&theta;</td>
      <td>&#952;</td>
      <td>&#x3B8;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter iota</td>
      <td>&amp;iota;</td>
      <td>&amp;#953;</td>
      <td>&amp;#x3B9;</td>
      <td>&iota;</td>
      <td>&#953;</td>
      <td>&#x3B9;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter kappa</td>
      <td>&amp;kappa;</td>
      <td>&amp;#954;</td>
      <td>&amp;#x3BA;</td>
      <td>&kappa;</td>
      <td>&#954;</td>
      <td>&#x3BA;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter lambda</td>
      <td>&amp;lambda;</td>
      <td>&amp;#955;</td>
      <td>&amp;#x3BB;</td>
      <td>&lambda;</td>
      <td>&#955;</td>
      <td>&#x3BB;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter mu</td>
      <td>&amp;mu;</td>
      <td>&amp;#956;</td>
      <td>&amp;#x3BC;</td>
      <td>&mu;</td>
      <td>&#956;</td>
      <td>&#x3BC;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter nu</td>
      <td>&amp;nu;</td>
      <td>&amp;#957;</td>
      <td>&amp;#x3BD;</td>
      <td>&nu;</td>
      <td>&#957;</td>
      <td>&#x3BD;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter xi</td>
      <td>&amp;xi;</td>
      <td>&amp;#958;</td>
      <td>&amp;#x3BE;</td>
      <td>&xi;</td>
      <td>&#958;</td>
      <td>&#x3BE;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter omicron</td>
      <td>&amp;omicron;</td>
      <td>&amp;#959;</td>
      <td>&amp;#x3BF;</td>
      <td>&omicron;</td>
      <td>&#959;</td>
      <td>&#x3BF;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter pi</td>
      <td>&amp;pi;</td>
      <td>&amp;#960;</td>
      <td>&amp;#x3C0;</td>
      <td>&pi;</td>
      <td>&#960;</td>
      <td>&#x3C0;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter rho</td>
      <td>&amp;rho;</td>
      <td>&amp;#961;</td>
      <td>&amp;#x3C1;</td>
      <td>&rho;</td>
      <td>&#961;</td>
      <td>&#x3C1;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter final sigma</td>
      <td>&amp;sigmaf;</td>
      <td>&amp;#962;</td>
      <td>&amp;#x3C2;</td>
      <td>&sigmaf;</td>
      <td>&#962;</td>
      <td>&#x3C2;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter sigma</td>
      <td>&amp;sigma;</td>
      <td>&amp;#963;</td>
      <td>&amp;#x3C3;</td>
      <td>&sigma;</td>
      <td>&#963;</td>
      <td>&#x3C3;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter tau</td>
      <td>&amp;tau;</td>
      <td>&amp;#964;</td>
      <td>&amp;#x3C4;</td>
      <td>&tau;</td>
      <td>&#964;</td>
      <td>&#x3C4;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter upsilon</td>
      <td>&amp;upsilon;</td>
      <td>&amp;#965;</td>
      <td>&amp;#x3C5;</td>
      <td>&upsilon;</td>
      <td>&#965;</td>
      <td>&#x3C5;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter phi</td>
      <td>&amp;phi;</td>
      <td>&amp;#966;</td>
      <td>&amp;#x3C6;</td>
      <td>&phi;</td>
      <td>&#966;</td>
      <td>&#x3C6;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter chi</td>
      <td>&amp;chi;</td>
      <td>&amp;#967;</td>
      <td>&amp;#x3C7;</td>
      <td>&chi;</td>
      <td>&#967;</td>
      <td>&#x3C7;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter psi</td>
      <td>&amp;psi;</td>
      <td>&amp;#968;</td>
      <td>&amp;#x3C8;</td>
      <td>&psi;</td>
      <td>&#968;</td>
      <td>&#x3C8;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter omega</td>
      <td>&amp;omega;</td>
      <td>&amp;#969;</td>
      <td>&amp;#x3C9;</td>
      <td>&omega;</td>
      <td>&#969;</td>
      <td>&#x3C9;</td>
    </tr>
    <tr>
      <td scope=row>Greek small letter theta symbol</td>
      <td>&amp;thetasym;</td>
      <td>&amp;#977;</td>
      <td>&amp;#x3D1;</td>
      <td>&thetasym;</td>
      <td>&#977;</td>
      <td>&#x3D1;</td>
    </tr>
    <tr>
      <td scope=row>Greek upsilon with hook symbol</td>
      <td>&amp;upsih;</td>
      <td>&amp;#978;</td>
      <td>&amp;#x3D2;</td>
      <td>&upsih;</td>
      <td>&#978;</td>
      <td>&#x3D2;</td>
    </tr>
    <tr>
      <td scope=row>Greek pi symbol</td>
      <td>&amp;piv;</td>
      <td>&amp;#982;</td>
      <td>&amp;#x3D6;</td>
      <td>&piv;</td>
      <td>&#982;</td>
      <td>&#x3D6;</td>
    </tr>
    <tr>
      <td scope=row>bullet = black small circle</td>
      <td>&amp;bull;</td>
      <td>&amp;#8226;</td>
      <td>&amp;#x2022;</td>
      <td>&bull;</td>
      <td>&#8226;</td>
      <td>&#x2022;</td>
    </tr>
    <tr>
      <td scope=row>horizontal ellipsis = three dot leader</td>
      <td>&amp;hellip;</td>
      <td>&amp;#8230;</td>
      <td>&amp;#x2026;</td>
      <td>&hellip;</td>
      <td>&#8230;</td>
      <td>&#x2026;</td>
    </tr>
    <tr>
      <td scope=row>prime = minutes = feet</td>
      <td>&amp;prime;</td>
      <td>&amp;#8242;</td>
      <td>&amp;#x2032;</td>
      <td>&prime;</td>
      <td>&#8242;</td>
      <td>&#x2032;</td>
    </tr>
    <tr>
      <td scope=row>double prime = seconds = inches</td>
      <td>&amp;Prime;</td>
      <td>&amp;#8243;</td>
      <td>&amp;#x2033;</td>
      <td>&Prime;</td>
      <td>&#8243;</td>
      <td>&#x2033;</td>
    </tr>
    <tr>
      <td scope=row>overline = spacing overscore</td>
      <td>&amp;oline;</td>
      <td>&amp;#8254;</td>
      <td>&amp;#x203E;</td>
      <td>&oline;</td>
      <td>&#8254;</td>
      <td>&#x203E;</td>
    </tr>
    <tr>
      <td scope=row>fraction slash</td>
      <td>&amp;frasl;</td>
      <td>&amp;#8260;</td>
      <td>&amp;#x2044;</td>
      <td>&frasl;</td>
      <td>&#8260;</td>
      <td>&#x2044;</td>
    </tr>
    <tr>
      <td scope=row>script capital P = power set = Weierstrass p</td>
      <td>&amp;weierp;</td>
      <td>&amp;#8472;</td>
      <td>&amp;#x2118;</td>
      <td>&weierp;</td>
      <td>&#8472;</td>
      <td>&#x2118;</td>
    </tr>
    <tr>
      <td scope=row>blackletter capital I = imaginary part</td>
      <td>&amp;image;</td>
      <td>&amp;#8465;</td>
      <td>&amp;#x2111;</td>
      <td>&image;</td>
      <td>&#8465;</td>
      <td>&#x2111;</td>
    </tr>
    <tr>
      <td scope=row>blackletter capital R = real part symbol</td>
      <td>&amp;real;</td>
      <td>&amp;#8476;</td>
      <td>&amp;#x211C;</td>
      <td>&real;</td>
      <td>&#8476;</td>
      <td>&#x211C;</td>
    </tr>
    <tr>
      <td scope=row>trade mark sign</td>
      <td>&amp;trade;</td>
      <td>&amp;#8482;</td>
      <td>&amp;#x2122;</td>
      <td>&trade;</td>
      <td>&#8482;</td>
      <td>&#x2122;</td>
    </tr>
    <tr>
      <td scope=row>alef symbol = first transfinite cardinal</td>
      <td>&amp;alefsym;</td>
      <td>&amp;#8501;</td>
      <td>&amp;#x2135;</td>
      <td>&alefsym;</td>
      <td>&#8501;</td>
      <td>&#x2135;</td>
    </tr>
    <tr>
      <td scope=row>leftwards arrow</td>
      <td>&amp;larr;</td>
      <td>&amp;#8592;</td>
      <td>&amp;#x2190;</td>
      <td>&larr;</td>
      <td>&#8592;</td>
      <td>&#x2190;</td>
    </tr>
    <tr>
      <td scope=row>upwards arrow</td>
      <td>&amp;uarr;</td>
      <td>&amp;#8593;</td>
      <td>&amp;#x2191;</td>
      <td>&uarr;</td>
      <td>&#8593;</td>
      <td>&#x2191;</td>
    </tr>
    <tr>
      <td scope=row>rightwards arrow</td>
      <td>&amp;rarr;</td>
      <td>&amp;#8594;</td>
      <td>&amp;#x2192;</td>
      <td>&rarr;</td>
      <td>&#8594;</td>
      <td>&#x2192;</td>
    </tr>
    <tr>
      <td scope=row>downwards arrow</td>
      <td>&amp;darr;</td>
      <td>&amp;#8595;</td>
      <td>&amp;#x2193;</td>
      <td>&darr;</td>
      <td>&#8595;</td>
      <td>&#x2193;</td>
    </tr>
    <tr>
      <td scope=row>left right arrow</td>
      <td>&amp;harr;</td>
      <td>&amp;#8596;</td>
      <td>&amp;#x2194;</td>
      <td>&harr;</td>
      <td>&#8596;</td>
      <td>&#x2194;</td>
    </tr>
    <tr>
      <td scope=row>downwards arrow with corner leftwards = carriage return</td>
      <td>&amp;crarr;</td>
      <td>&amp;#8629;</td>
      <td>&amp;#x21B5;</td>
      <td>&crarr;</td>
      <td>&#8629;</td>
      <td>&#x21B5;</td>
    </tr>
    <tr>
      <td scope=row>leftwards double arrow</td>
      <td>&amp;lArr;</td>
      <td>&amp;#8656;</td>
      <td>&amp;#x21D0;</td>
      <td>&lArr;</td>
      <td>&#8656;</td>
      <td>&#x21D0;</td>
    </tr>
    <tr>
      <td scope=row>upwards double arrow</td>
      <td>&amp;uArr;</td>
      <td>&amp;#8657;</td>
      <td>&amp;#x21D1;</td>
      <td>&uArr;</td>
      <td>&#8657;</td>
      <td>&#x21D1;</td>
    </tr>
    <tr>
      <td scope=row>rightwards double arrow</td>
      <td>&amp;rArr;</td>
      <td>&amp;#8658;</td>
      <td>&amp;#x21D2;</td>
      <td>&rArr;</td>
      <td>&#8658;</td>
      <td>&#x21D2;</td>
    </tr>
    <tr>
      <td scope=row>downwards double arrow</td>
      <td>&amp;dArr;</td>
      <td>&amp;#8659;</td>
      <td>&amp;#x21D3;</td>
      <td>&dArr;</td>
      <td>&#8659;</td>
      <td>&#x21D3;</td>
    </tr>
    <tr>
      <td scope=row>left right double arrow</td>
      <td>&amp;hArr;</td>
      <td>&amp;#8660;</td>
      <td>&amp;#x21D4;</td>
      <td>&hArr;</td>
      <td>&#8660;</td>
      <td>&#x21D4;</td>
    </tr>
    <tr>
      <td scope=row>for all</td>
      <td>&amp;forall;</td>
      <td>&amp;#8704;</td>
      <td>&amp;#x2200;</td>
      <td>&forall;</td>
      <td>&#8704;</td>
      <td>&#x2200;</td>
    </tr>
    <tr>
      <td scope=row>partial differential</td>
      <td>&amp;part;</td>
      <td>&amp;#8706;</td>
      <td>&amp;#x2202;</td>
      <td>&part;</td>
      <td>&#8706;</td>
      <td>&#x2202;</td>
    </tr>
    <tr>
      <td scope=row>there exists</td>
      <td>&amp;exist;</td>
      <td>&amp;#8707;</td>
      <td>&amp;#x2203;</td>
      <td>&exist;</td>
      <td>&#8707;</td>
      <td>&#x2203;</td>
    </tr>
    <tr>
      <td scope=row>empty set = null set = diameter</td>
      <td>&amp;empty;</td>
      <td>&amp;#8709;</td>
      <td>&amp;#x2205;</td>
      <td>&empty;</td>
      <td>&#8709;</td>
      <td>&#x2205;</td>
    </tr>
    <tr>
      <td scope=row>nabla = backward difference</td>
      <td>&amp;nabla;</td>
      <td>&amp;#8711;</td>
      <td>&amp;#x2207;</td>
      <td>&nabla;</td>
      <td>&#8711;</td>
      <td>&#x2207;</td>
    </tr>
    <tr>
      <td scope=row>element of</td>
      <td>&amp;isin;</td>
      <td>&amp;#8712;</td>
      <td>&amp;#x2208;</td>
      <td>&isin;</td>
      <td>&#8712;</td>
      <td>&#x2208;</td>
    </tr>
    <tr>
      <td scope=row>not an element of</td>
      <td>&amp;notin;</td>
      <td>&amp;#8713;</td>
      <td>&amp;#x2209;</td>
      <td>&notin;</td>
      <td>&#8713;</td>
      <td>&#x2209;</td>
    </tr>
    <tr>
      <td scope=row>contains as member</td>
      <td>&amp;ni;</td>
      <td>&amp;#8715;</td>
      <td>&amp;#x220B;</td>
      <td>&ni;</td>
      <td>&#8715;</td>
      <td>&#x220B;</td>
    </tr>
    <tr>
      <td scope=row>n-ary product = product sign</td>
      <td>&amp;prod;</td>
      <td>&amp;#8719;</td>
      <td>&amp;#x220F;</td>
      <td>&prod;</td>
      <td>&#8719;</td>
      <td>&#x220F;</td>
    </tr>
    <tr>
      <td scope=row>n-ary sumation</td>
      <td>&amp;sum;</td>
      <td>&amp;#8721;</td>
      <td>&amp;#x2211;</td>
      <td>&sum;</td>
      <td>&#8721;</td>
      <td>&#x2211;</td>
    </tr>
    <tr>
      <td scope=row>minus sign</td>
      <td>&amp;minus;</td>
      <td>&amp;#8722;</td>
      <td>&amp;#x2212;</td>
      <td>&minus;</td>
      <td>&#8722;</td>
      <td>&#x2212;</td>
    </tr>
    <tr>
      <td scope=row>asterisk operator</td>
      <td>&amp;lowast;</td>
      <td>&amp;#8727;</td>
      <td>&amp;#x2217;</td>
      <td>&lowast;</td>
      <td>&#8727;</td>
      <td>&#x2217;</td>
    </tr>
    <tr>
      <td scope=row>square root = radical sign</td>
      <td>&amp;radic;</td>
      <td>&amp;#8730;</td>
      <td>&amp;#x221A;</td>
      <td>&radic;</td>
      <td>&#8730;</td>
      <td>&#x221A;</td>
    </tr>
    <tr>
      <td scope=row>proportional to</td>
      <td>&amp;prop;</td>
      <td>&amp;#8733;</td>
      <td>&amp;#x221D;</td>
      <td>&prop;</td>
      <td>&#8733;</td>
      <td>&#x221D;</td>
    </tr>
    <tr>
      <td scope=row>infinity</td>
      <td>&amp;infin;</td>
      <td>&amp;#8734;</td>
      <td>&amp;#x221E;</td>
      <td>&infin;</td>
      <td>&#8734;</td>
      <td>&#x221E;</td>
    </tr>
    <tr>
      <td scope=row>angle</td>
      <td>&amp;ang;</td>
      <td>&amp;#8736;</td>
      <td>&amp;#x2220;</td>
      <td>&ang;</td>
      <td>&#8736;</td>
      <td>&#x2220;</td>
    </tr>
    <tr>
      <td scope=row>logical and = wedge</td>
      <td>&amp;and;</td>
      <td>&amp;#8743;</td>
      <td>&amp;#x2227;</td>
      <td>&and;</td>
      <td>&#8743;</td>
      <td>&#x2227;</td>
    </tr>
    <tr>
      <td scope=row>logical or = vee</td>
      <td>&amp;or;</td>
      <td>&amp;#8744;</td>
      <td>&amp;#x2228;</td>
      <td>&or;</td>
      <td>&#8744;</td>
      <td>&#x2228;</td>
    </tr>
    <tr>
      <td scope=row>intersection = cap</td>
      <td>&amp;cap;</td>
      <td>&amp;#8745;</td>
      <td>&amp;#x2229;</td>
      <td>&cap;</td>
      <td>&#8745;</td>
      <td>&#x2229;</td>
    </tr>
    <tr>
      <td scope=row>union = cup</td>
      <td>&amp;cup;</td>
      <td>&amp;#8746;</td>
      <td>&amp;#x222A;</td>
      <td>&cup;</td>
      <td>&#8746;</td>
      <td>&#x222A;</td>
    </tr>
    <tr>
      <td scope=row>integral</td>
      <td>&amp;int;</td>
      <td>&amp;#8747;</td>
      <td>&amp;#x222B;</td>
      <td>&int;</td>
      <td>&#8747;</td>
      <td>&#x222B;</td>
    </tr>
    <tr>
      <td scope=row>therefore</td>
      <td>&amp;there4;</td>
      <td>&amp;#8756;</td>
      <td>&amp;#x2234;</td>
      <td>&there4;</td>
      <td>&#8756;</td>
      <td>&#x2234;</td>
    </tr>
    <tr>
      <td scope=row>tilde operator = varies with = similar to</td>
      <td>&amp;sim;</td>
      <td>&amp;#8764;</td>
      <td>&amp;#x223C;</td>
      <td>&sim;</td>
      <td>&#8764;</td>
      <td>&#x223C;</td>
    </tr>
    <tr>
      <td scope=row>approximately equal to</td>
      <td>&amp;cong;</td>
      <td>&amp;#8773;</td>
      <td>&amp;#x2245;</td>
      <td>&cong;</td>
      <td>&#8773;</td>
      <td>&#x2245;</td>
    </tr>
    <tr>
      <td scope=row>almost equal to = asymptotic to</td>
      <td>&amp;asymp;</td>
      <td>&amp;#8776;</td>
      <td>&amp;#x2248;</td>
      <td>&asymp;</td>
      <td>&#8776;</td>
      <td>&#x2248;</td>
    </tr>
    <tr>
      <td scope=row>not equal to</td>
      <td>&amp;ne;</td>
      <td>&amp;#8800;</td>
      <td>&amp;#x2260;</td>
      <td>&ne;</td>
      <td>&#8800;</td>
      <td>&#x2260;</td>
    </tr>
    <tr>
      <td scope=row>identical to</td>
      <td>&amp;equiv;</td>
      <td>&amp;#8801;</td>
      <td>&amp;#x2261;</td>
      <td>&equiv;</td>
      <td>&#8801;</td>
      <td>&#x2261;</td>
    </tr>
    <tr>
      <td scope=row>less-than or equal to</td>
      <td>&amp;le;</td>
      <td>&amp;#8804;</td>
      <td>&amp;#x2264;</td>
      <td>&le;</td>
      <td>&#8804;</td>
      <td>&#x2264;</td>
    </tr>
    <tr>
      <td scope=row>greater-than or equal to</td>
      <td>&amp;ge;</td>
      <td>&amp;#8805;</td>
      <td>&amp;#x2265;</td>
      <td>&ge;</td>
      <td>&#8805;</td>
      <td>&#x2265;</td>
    </tr>
    <tr>
      <td scope=row>subset of</td>
      <td>&amp;sub;</td>
      <td>&amp;#8834;</td>
      <td>&amp;#x2282;</td>
      <td>&sub;</td>
      <td>&#8834;</td>
      <td>&#x2282;</td>
    </tr>
    <tr>
      <td scope=row>superset of</td>
      <td>&amp;sup;</td>
      <td>&amp;#8835;</td>
      <td>&amp;#x2283;</td>
      <td>&sup;</td>
      <td>&#8835;</td>
      <td>&#x2283;</td>
    </tr>
    <tr>
      <td scope=row>not a subset of</td>
      <td>&amp;nsub;</td>
      <td>&amp;#8836;</td>
      <td>&amp;#x2284;</td>
      <td>&nsub;</td>
      <td>&#8836;</td>
      <td>&#x2284;</td>
    </tr>
    <tr>
      <td scope=row>subset of or equal to</td>
      <td>&amp;sube;</td>
      <td>&amp;#8838;</td>
      <td>&amp;#x2286;</td>
      <td>&sube;</td>
      <td>&#8838;</td>
      <td>&#x2286;</td>
    </tr>
    <tr>
      <td scope=row>superset of or equal to</td>
      <td>&amp;supe;</td>
      <td>&amp;#8839;</td>
      <td>&amp;#x2287;</td>
      <td>&supe;</td>
      <td>&#8839;</td>
      <td>&#x2287;</td>
    </tr>
    <tr>
      <td scope=row>circled plus = direct sum</td>
      <td>&amp;oplus;</td>
      <td>&amp;#8853;</td>
      <td>&amp;#x2295;</td>
      <td>&oplus;</td>
      <td>&#8853;</td>
      <td>&#x2295;</td>
    </tr>
    <tr>
      <td scope=row>circled times = vector product</td>
      <td>&amp;otimes;</td>
      <td>&amp;#8855;</td>
      <td>&amp;#x2297;</td>
      <td>&otimes;</td>
      <td>&#8855;</td>
      <td>&#x2297;</td>
    </tr>
    <tr>
      <td scope=row>up tack = orthogonal to = perpendicular</td>
      <td>&amp;perp;</td>
      <td>&amp;#8869;</td>
      <td>&amp;#x22A5;</td>
      <td>&perp;</td>
      <td>&#8869;</td>
      <td>&#x22A5;</td>
    </tr>
    <tr>
      <td scope=row>dot operator</td>
      <td>&amp;sdot;</td>
      <td>&amp;#8901;</td>
      <td>&amp;#x22C5;</td>
      <td>&sdot;</td>
      <td>&#8901;</td>
      <td>&#x22C5;</td>
    </tr>
    <tr>
      <td scope=row>left ceiling = APL upstile</td>
      <td>&amp;lceil;</td>
      <td>&amp;#8968;</td>
      <td>&amp;#x2308;</td>
      <td>&lceil;</td>
      <td>&#8968;</td>
      <td>&#x2308;</td>
    </tr>
    <tr>
      <td scope=row>right ceiling</td>
      <td>&amp;rceil;</td>
      <td>&amp;#8969;</td>
      <td>&amp;#x2309;</td>
      <td>&rceil;</td>
      <td>&#8969;</td>
      <td>&#x2309;</td>
    </tr>
    <tr>
      <td scope=row>left floor = APL downstile</td>
      <td>&amp;lfloor;</td>
      <td>&amp;#8970;</td>
      <td>&amp;#x230A;</td>
      <td>&lfloor;</td>
      <td>&#8970;</td>
      <td>&#x230A;</td>
    </tr>
    <tr>
      <td scope=row>right floor</td>
      <td>&amp;rfloor;</td>
      <td>&amp;#8971;</td>
      <td>&amp;#x230B;</td>
      <td>&rfloor;</td>
      <td>&#8971;</td>
      <td>&#x230B;</td>
    </tr>
    <tr>
      <td scope=row>left-pointing angle bracket = bra</td>
      <td>&amp;lang;</td>
      <td>&amp;#9001;</td>
      <td>&amp;#x2329;</td>
      <td>&lang;</td>
      <td>&#9001;</td>
      <td>&#x2329;</td>
    </tr>
    <tr>
      <td scope=row>right-pointing angle bracket = ket</td>
      <td>&amp;rang;</td>
      <td>&amp;#9002;</td>
      <td>&amp;#x232A;</td>
      <td>&rang;</td>
      <td>&#9002;</td>
      <td>&#x232A;</td>
    </tr>
    <tr>
      <td scope=row>lozenge</td>
      <td>&amp;loz;</td>
      <td>&amp;#9674;</td>
      <td>&amp;#x25CA;</td>
      <td>&loz;</td>
      <td>&#9674;</td>
      <td>&#x25CA;</td>
    </tr>
    <tr>
      <td scope=row>black spade suit</td>
      <td>&amp;spades;</td>
      <td>&amp;#9824;</td>
      <td>&amp;#x2660;</td>
      <td>&spades;</td>
      <td>&#9824;</td>
      <td>&#x2660;</td>
    </tr>
    <tr>
      <td scope=row>black club suit = shamrock</td>
      <td>&amp;clubs;</td>
      <td>&amp;#9827;</td>
      <td>&amp;#x2663;</td>
      <td>&clubs;</td>
      <td>&#9827;</td>
      <td>&#x2663;</td>
    </tr>
    <tr>
      <td scope=row>black heart suit = valentine</td>
      <td>&amp;hearts;</td>
      <td>&amp;#9829;</td>
      <td>&amp;#x2665;</td>
      <td>&hearts;</td>
      <td>&#9829;</td>
      <td>&#x2665;</td>
    </tr>
    <tr>
      <td scope=row>black diamond suit</td>
      <td>&amp;diams;</td>
      <td>&amp;#9830;</td>
      <td>&amp;#x2666;</td>
      <td>&diams;</td>
      <td>&#9830;</td>
      <td>&#x2666;</td>
    </tr>
  </tbody>
</table>

<div class=footer>
<address>Maintained by <a href="http://www.htmlhelp.com/%7Eliam/">Liam Quinn</a> &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</address>
<p class=toolbar><a href="../index.html" rel=Start>HTML&#160;4.0&#160;Reference</a>&#160;~ <a href="latin1.html" rel=Prev>Latin-1&#160;Characters</a>&#160;~ <a href="special.html" rel=Next>Other&#160;Special&#160;Characters</a></p>
<p class=copyright>Copyright &copy; 1998 by <a href="http://www.htmlhelp.com/%7Eliam/">Liam Quinn</a>. This material may be distributed only subject to the terms and conditions set forth in the Open Publication License, v1.0 or later (the latest version is presently available at <a href="http://www.opencontent.org/openpub/">http://www.opencontent.org/openpub/</a>).</p>
<p class=copyright>Modfications made by Benj Carson <a><EMAIL></a> for dompdf, Jan 5, 2006.</p>
</div>
</body>
</html>
