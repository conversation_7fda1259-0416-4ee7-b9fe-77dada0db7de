<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('staff/leaves/dashboard'); ?>">Leave Master</a></li>
    <li class="active">Staff leave approval</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('staff/leaves/dashboard'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Staff Leave Approval
                    </h3>
                </div>
            </div>
        </div>
        <div class="card-body pt-1 pb-3">
            <div class="d-flex">
                <div class="form-group mr-2" style="width:20%">
                    <select class="form-control" name="" id="staff_id" style="font-weight: 600;">
                        <option value="0">All Staff</option>
                        <?php if ($staff_list) { ?>
                            <?php foreach ($staff_list as $staff) { ?>
                                <?php if ($staff->status != 2) continue; ?>
                                <?php $staffLoggedInId = $this->authorization->getAvatarStakeHolderId(); ?>

                                <?php if ($applyLeave == 2) { ?>
                                    <?php if ($staffLoggedInId == $staff->reporting_manager_id) { ?>
                                        <?php if($selected_staff==$staff->id){
                                            $selecetd="Selected";
                                        }else{
                                            $selecetd="";
                                        } ?>
                                        <option <?php echo $selecetd; ?> value="<?php echo $staff->id ?>"><?php echo $staff->first_name ?> <?php echo $staff->last_name ?></option>
                                    <?php } ?>
                                <?php } else if ($applyLeave == 1) { ?>
                                    <?php if ($selected_staff == $staff->id) {
                                        $selecetd = "Selected";
                                    } else {
                                        $selecetd = "";
                                    } ?>
                                    <option <?php echo $selecetd; ?> value="<?php echo $staff->id ?>"><?php echo $staff->first_name ?> <?php echo $staff->last_name ?></option>
                                <?php } ?>
                            <?php } ?>
                        <?php } ?>
                    </select>
                </div>

                <div class="form-group" style="width:20%">
                    <select class="form-control select" name="" id="status_id" style="font-weight: 600;" multiple>
                        <?php if ($status_list) { ?>
                            <?php foreach ($status_list as $status) { ?>
                                <?php if ($status->status == 0) { ?>
                                    <?php $status_name = "Pending" ?>
                                <?php } else if ($status->status == 1) { ?>
                                    <?php $status_name = "Approved" ?>
                                <?php } else if ($status->status == 2) { ?>
                                    <?php $status_name = "Auto Approved" ?>
                                <?php } else if ($status->status == 3) { ?>
                                    <?php $status_name = "Rejected" ?>
                                <?php } else if ($status->status == 4) { ?>
                                    <?php $status_name = "Cancelled" ?>
                                <?php } ?>
                                <option value="<?php echo $status->status ?>"><?php echo $status_name ?></option>
                            <?php } ?>
                        <?php } ?>
                    </select>
                </div>

                <div class="form-group">
                    <button class="click_event btn btn-primary ml-2" type="button">Get leaves</button>
                </div>
            </div>

            <!-- <?php if ($applyLeave) { ?> -->
            <!-- <div class="form-group">
                    <label class="control-label">Show &nbsp;&nbsp;</label>
                    <label class="control-label radio-inline" for="all"><input checked type="radio" name="leaves" id="all" value="3">All leaves</label>
                    <label class="control-label radio-inline" for="me"><input type="radio" name="leaves" id="me" value="2">Leaves filed by me</label>
                    <label class="control-label radio-inline" for="my"><input type="radio" name="leaves" id="my" value="1">My leaves</label>
                </div> -->

            <!-- <div class="form-group">
                    <select class="form-control" name="leaves" id="leaves">
                        <option value="3">All Leaves</option>
                        <option value="2">My Filed Leaves</option>
                        <option value="1">My Leaves</option>
                    </select>
                </div> -->
            <!-- <?php } ?> -->

            <div id="loader-icon" style="display:none;">
                <div class="d-flex justify-content-center align-items-center" style="">
                    <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
                </div>
            </div>

            <div class="mt-3 leaveData" style="opacity:0;">

            </div>
        </div>

        <div class="modal fade" id="showLeaveDocModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Modal title</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                <div id="external-files">
                    <iframe src="" frameborder="0" width="500px" height="500px"></iframe>
                </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js" integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>
<script>
    var approveLeave = 0;
    var staffId = 0;
    const isSuperAdmin="<?php echo $this->authorization->isSuperAdmin(); ?>"
    const enableStaffLeaveConversion = "<?php echo $enableStaffLeaveConversion; ?>";

    var original_available_leave_categories_with_balance_quota;
    var available_leave_categories;

    $(document).ready(function() {
        getLeaves();

    $('#showLeaveDocModal').on('shown.bs.modal', function (e) {
        const {doc_url,name}=e.relatedTarget.dataset;
        $("#exampleModalLabel").text(name);
        $("iframe").attr("src",doc_url);
        $("iframe").css("width","100%");
    })
    });

    $(".click_event").click(() => {
        getLeaves();
    })

    async function getLeaves(statusId=0) {
        const staff_id = $("#staff_id").val();
        const status_id = $("#status_id").val() || statusId;
        await $.ajax({
            url: '<?php echo site_url('staff/leaves/staffLeaves'); ?>',
            type: "POST",
            beforeSend: function() {
                $(".leaveData").css("opacity", "0");
                $("#loader-icon").show();
            },
            data: {
                'staff_id': staff_id,
                'status_id': status_id
            },
            type: "post",
            success: function(data) {
                $("#loader-icon").hide();
                $(".leaveData").css("opacity", "1");

                var leaves = JSON.parse(data);
                if (leaves.length == 0) {
                    $(".leaveData").html('<h4>Leaves not filed.</h4>');
                    return false;
                }
                var html = '<table class="table table-bordered">';
                html += '<thead><tr><th data-toggle="tooltip" onclick="clearStatusFilters()" data-original-title="Clear Filters" style="width:4%;cursor: pointer;color: #e04b4a;vertical-align: middle;font-size: 1.5rem;"><i class="fa fa-times"></i></th><th width="10%"><input onkeyup="filterStatusList()" type="text" class="form-control" id="staffname-filter" placeholder="Select"/> </th> <th width="10%"><input class="form-control fromdatefilter" onselect="filterStatusList()" id="requestdate-filter" placeholder="Request Date"/></th> <th width="10%"><input class="form-control fromdatefilter" onselect="filterStatusList()" id="fromdate-filter" placeholder="Select Date"/></th><th width="10%"><input onselect="filterStatusList()" class="form-control todatefilter" id="todate-filter" placeholder="Select Date"/></th><th width="6%"></th><th width="10%"><input onkeyup="filterStatusList()" type="text" class="form-control" id="leavetype-filter" placeholder="Search"/></th><th width="20%"></th><th width="8%"></th><th></th><th width="10%"></th></tr></thead>';

                html += '<thead style="white-space: nowrap;"><tr><th width="4%">#</th><th width="10%">Staff Name</th><th width="6%">Request Date and Time</th><th width="6%">From Date</th><th width="10%">To Date</th><th width="10%">No of Days</th><th width="10%">Leave Type</th><th width="20%">Reason</th><th width="8%">Status</th><th>Comment</th><th width="10%">Action</th></tr></thead>';
                html += '<tbody>';
                for (var i = 0; i < leaves.length; i++) {
                    html += '<tr id="leave_' + leaves[i].id + '" class="staff-filters" data-leavetype="' + leaves[i].leave_type.toLowerCase() + '" data-staffname="' + leaves[i].staff_name.toLowerCase() + '" data-requestdate="' + leaves[i].request_date + '" data-fromdate="' + leaves[i].from_date + '" data-todate="' + leaves[i].to_date + '">';
                    html += '<td>' + (i + 1) + '</td>';
                    html += constructLeave(leaves[i]);
                    html += '</tr>';
                }
                html += '</tbody>';
                html += '</table>';
                $(".leaveData").html(html);

                $('#fromdate-filter').datepicker({
                        format: 'd-m-yyyy',
                        "autoclose": true
                    })
                    .on('changeDate', function(selected) {
                        startDate = new Date(selected.date.valueOf());
                        var fromdateformat = moment(startDate).format('DD-MM-YYYY');
                        $('#fromdate-filter').val(fromdateformat)
                        filterStatusList();
                    });

                    $('#requestdate-filter').datepicker({
                        format: 'd-m-yyyy',
                        "autoclose": true
                    })
                    .on('changeDate', function(selected) {
                        startDate = new Date(selected.date.valueOf());
                        var requestdateformat = moment(startDate).format('DD-MM-YYYY');
                        $('#requestdate-filter').val(requestdateformat)
                        filterStatusList();
                    });

                $('#todate-filter').datepicker({
                        format: 'd-m-yyyy',
                        "autoclose": true
                    })
                    .on('changeDate', function(selected) {
                        endDate = new Date(selected.date.valueOf());
                        var todateformat = moment(endDate).format('DD-MM-YYYY');
                        $('#todate-filter').val(todateformat)
                        filterStatusList();
                    });


            },
            error: function(err) {
                console.log(err);
            }
        });
    }

    function constructLeave(leave) {
        var html = '<td>' + leave.staff_name + '</td>';
        html += '<td>' + leave.request_date + ' ' + leave.request_time + '</td>';
        html += '<td>' + leave.from_date + '</td>';
        html += '<td>' + leave.to_date + '</td>';
        html += '<td>' + parseFloat(leave.noofdays) + '</td>';
        html += '<td>' + leave.leave_type + '</td>';
        html += '<td>' + leave.reason + '</td>';
        html += '<td>' + getStatus(leave.status) + '</td>';
        html += '<td>' + ((leave.description) ? leave.description : '') + '</td>';
        var action = '';
        if (staffId == leave.staff_id) {
            if ((leave.date_passed == 0 && leave.status != '4') || (leave.date_passed == 1 && leave.status == '0' || leave.status == '3')) {
                action = '<button class="btn btn-sm btn-warning" onclick="cancelLeave(' + leave.id + ', \'' + leave.staff_name + '\',' + leave.staff_id + ')">Cancel Leave</button>';
            }
        } else {
            if (leave.status == '0' && approveLeave) {
                action = '<button class="btn btn-sm btn-danger" style="margin-bottom: 10px;" id="approveReject-btn-id-"'+leave.id+'" onclick="updateLeaveStatus(' + leave.id + ', \'' + leave.staff_name + '\',' + leave.staff_id + ', 1)">Approve/Reject</button>';
                
                if(enableStaffLeaveConversion==1){
                    if (leave.status != 3 && leave.status != 4) {
                        action += `<button class="btn btn-sm btn-primary" id="convert-btn-id-${leave.id}" onclick="convertLeaveToOtherType(${leave.id},'${leave.staff_name}','${leave.name}')">Convert Leave</button>`;
                    }else{
                        action += `<button class="btn btn-sm btn-primary" disabled>Convert Leave</button>`;
                    }
                }
            } else if (leave.status == '3') {
                //Cannot approve after rejection
                //action = '<button class="btn btn-sm btn-secondary" style="border-radius: 8px;" onclick="updateLeaveStatus('+leave.id+', \''+leave.staff_name+'\','+leave.staff_id+', 1)">Approve</button>';
                action = "None";
            } else if (leave.status != '4') {
                action = '<button class="btn btn-sm btn-danger" style="margin-bottom: 10px;" id="approveReject-btn-id-"'+leave.id+'" onclick="updateLeaveStatus(' + leave.id + ', \'' + leave.staff_name + '\',' + leave.staff_id + ', 0)">Approve/Reject</button>';

                if(enableStaffLeaveConversion==1){
                    if (leave.status != 3 && leave.status != 4) {
                        action += `<button class="btn btn-sm btn-primary" id="convert-btn-id-${leave.id}" onclick="convertLeaveToOtherType(${leave.id},'${leave.staff_name}','${leave.name}')">Convert Leave</button>`;
                    }else{
                        action += `<button class="btn btn-sm btn-primary" disabled>Convert Leave</button>`;
                    }
                }
            } else {
                action = "None";
            }
        }
        html += '<td>' + action + '</td>';
        return html;
    }

    function getFilteredOptions(oldLeaveCategoryId,oldLeaveCategoryName){
        const allLeaveCategoryAvailableOptions=document.querySelectorAll(".leave_category_options");

        let choosedNewOptionCategories={};
        allLeaveCategoryAvailableOptions.forEach(opt=>{
            const newLeaveCategory=opt.value;
            if(newLeaveCategory!=oldLeaveCategoryId){
                choosedNewOptionCategories[newLeaveCategory] = choosedNewOptionCategories[newLeaveCategory] ?  choosedNewOptionCategories[newLeaveCategory]+1 : 1;
            }
        })

        if(!Object.entries(choosedNewOptionCategories)?.length){
            return;
        }

        const tempLeaveCategoryOptions={};
        for(let [categoryId,remainingCount] of Object.entries(original_available_leave_categories_with_balance_quota)){
            if(categoryId in choosedNewOptionCategories){
                const balance=Number(original_available_leave_categories_with_balance_quota[categoryId])-Number(choosedNewOptionCategories[categoryId]);
                if(balance>0){
                    tempLeaveCategoryOptions[categoryId]=balance;
                }
            }else{
                tempLeaveCategoryOptions[categoryId]=original_available_leave_categories_with_balance_quota[categoryId];
            }
        }

        const allLeaveCategoryNames=[]
        tempLeaveCategoryOptions[oldLeaveCategoryId]=`Keep same as ${oldLeaveCategoryName}`;
        for(let {has_quota, leave_category_id, name} of Object.values(available_leave_categories)){
            allLeaveCategoryNames[leave_category_id]=name;

            if(+has_quota>0){
                if(leave_category_id in tempLeaveCategoryOptions){
                    tempLeaveCategoryOptions[leave_category_id]=name;
                }
            }else{
                tempLeaveCategoryOptions[leave_category_id]=name;
            }
        }

        allLeaveCategoryAvailableOptions.forEach((opt,i)=>{
            const selectedLeaveCategoryId=opt.value;
            const newLeaveCategoryName=allLeaveCategoryNames[selectedLeaveCategoryId];

            let options="";
            
            if(Object.entries(tempLeaveCategoryOptions)?.length){
                for([cateId,catName] of Object.entries(tempLeaveCategoryOptions)){
                    if(cateId!=selectedLeaveCategoryId){
                        options+=`<option value="${cateId}">${catName}</option>`;
                    }
                }
            }

            if(!newLeaveCategoryName){
                options+=`<option selected value="${oldLeaveCategoryId}">Keep same as ${oldLeaveCategoryName}</option>`;
            }else{
                options+=`<option selected value="${selectedLeaveCategoryId}">${newLeaveCategoryName}</option>`;
            }

            opt.innerHTML=options;
        })
    }
   
    function convertLeaveToOtherType(leaveId, staffName, currentLeaveCategory){
        $(`#convert-btn-id-${leaveId}`).prop("disabled",true).text("Converting...");
        // 1. Bring all the leave 'Dates'(Verify dates should match with Leave taken no. of days) and 'Leave type categories to choose from'
        // hint:// If no. of days taken and Total no. of 'from_date' and 'to_date' matches then it may be holiday scenario else It is non-holiday scenario
        $.ajax({
            url:"<?php echo site_url('staff/leaves/get_staff_leave_info_to_convert_leave_from_one_type_to_another') ?>",
            type:"POST",
            data:{"leaveId":leaveId},
            success:function(response){
                let data=JSON.parse(response);
                const available_leave_dates=data.available_leave_dates;
                const available_no_of_days_for_each_date=data.available_no_of_days_for_each_date;
                available_leave_categories=data.available_leave_categories;
                const leave_info=data.leave_info;
                
                const leaveCategories=Object.values(available_leave_categories);
                const leaveDates=Object.values(available_leave_dates);
                // Generate html table for the above info

                // Original available leave categories
                original_available_leave_categories_with_balance_quota=data.original_available_leave_categories_with_balance_quota;
                // console.log(original_available_leave_categories_with_balance_quota)

                let html=`
                <table class="table table-bordered" id="convert-leaves-table">
                <thead>
                <tr>
                <th>#</th>
                <th>Leave date</th>
                <th>Leave days</th>
                <th>Present leave category</th>
                <th>Leave category</th>
                </tr>
                </thead>
                <tbody>
                `;
                                
                if(leaveDates.length){
                    leaveDates[1].forEach((date,i)=>{
                        let index=i;
                        const availableNoOfDay=available_no_of_days_for_each_date[i];
                        html+=` <tr>
                                    <td>${++index}</td>
                                    <td>${date}</td>
                                    <td>${availableNoOfDay}</td>
                                    <td>${currentLeaveCategory}</td>
                                    <td>`;

                        html+=`<select name="leave-category-${index}" class="form-control leave_category_options" id="leave-category-id-${index}" onchange="getFilteredOptions(${leave_info["leave_v2_category_id"]},'${leave_info["leave_category_name"]}')">`
                        if(leaveCategories?.length){
                            leaveCategories.forEach((cate,index)=>{
                                html+=`<option value="${cate.leave_category_id}">${cate.name}</option>`;
                            });
                        }

                        // Do not show this option if there is only 1 leave
                        // if(leaveDates[1]?.length>1){
                        html+=`<option selected value="${leave_info["leave_v2_category_id"]}">Keep same as ${leave_info["leave_category_name"]}</option>`;
                        // }

                        html+=`         </select>
                                    </td>

                                    </tr>`;
                                })
                }

                html+=`
                    </tbody>
                    </table>
                `;

                Swal.fire({
                    title: `<strong>Convert applied leaves for <u>${staffName}</u></strong>`,
                    html: `${html}`,
                    showCloseButton: true,
                    showCancelButton: true,
                    focusConfirm: false,
                    confirmButtonText: `Convert Leaves!`,
                    cancelButtonText: `Close`,
                }).then(e=>{
                    if(e.isConfirmed){
                        const oldLeaveCategory=leave_info["leave_v2_category_id"];
                        let isLeaveConverted=false;
                        // convert above leaves
                        // 1. Extract all the leave changes from the HTML/UI
                        const leavesToConvert={};
                        leaveDates[1].forEach((leaveDate,i)=>{
                            let index=i;
                            const leaveNoOfDays=available_no_of_days_for_each_date[i];
                            const leaveCategoryId=$(`#leave-category-id-${index+1}`).val();

                            if(oldLeaveCategory!=leaveCategoryId){
                                isLeaveConverted=true;
                            }
                            // change request date for curent leave convert date
                            leavesToConvert[index]={
                                leave_v2_year_id:leave_info["leave_year_id"],
                                is_application_from_manage_attendance:1,
                                staff_id:leave_info["staff_id"],
                                leave_category:leaveCategoryId,
                                selection_type:leaveNoOfDays==0.5 ? "morning" : "fullday",
                                from_date:leaveDate,
                                to_date:leaveDate,
                                noofdays:leaveNoOfDays,
                                reason:"Leave being applied from the admin due to change in leave convert",
                                approver_1:leave_info["leave_approved_by"],
                                approver_2:leave_info["approved_by_2"],
                                approver_3:leave_info["approved_by_3"],
                                approver_mode:leave_info["approval_mode"],
                            }
                        })
                        
                        if(isLeaveConverted){
                            $.ajax({
                                url:"<?php echo site_url('staff/leaves/convert_staff_leaves_from_one_leave_type_to_another') ?>",
                                type:"POST",
                                data:{leavesToConvert,"leaveInfo":leave_info},
                                success:function(res){
                                    try {
                                        const {status, message}=JSON.parse(res);
    
                                        if(+status===0){
                                            Swal.fire({
                                                icon: "error",
                                                title: "Oops...",
                                                html: `<strong>${message}</strong>`,
                                            }).then(e=>{
                                                $(`#convert-btn-id-${leaveId}`).prop("disabled",false).text("Convert Leave");
                                            })
                                        }else if(+status===1){
                                            Swal.fire({
                                                icon: "success",
                                                title: "Successful",
                                                html: `<strong>Leave Converted successfully for <u>${staffName}</u>!</strong>`,
                                            }).then(e=>{
                                               getLeaves();
                                            })
                                        }else{
                                            Swal.fire({
                                                icon: "error",
                                                title: "Oops...",
                                                text: "Something went wrong!",
                                            }).then(e=>{
                                                $(`#convert-btn-id-${leaveId}`).prop("disabled",false).text("Convert Leave");
                                            })
                                        }
                                    } catch (error) {
                                        console.log(err.message)
                                    }
                                }
                            })
                        }else{
                            Swal.fire({
                                icon: "warning",
                                title: "Oops...",
                                text: "Found no changes in the leave, Please try again!",
                                confirmButtonText: `Go back!`
                            }).then(e=>{
                                convertLeaveToOtherType(leaveId, staffName, currentLeaveCategory);
                            });
                        }
                    }else{
                        $(`#convert-btn-id-${leaveId}`).prop("disabled",false).text("Convert Leave");
                    }
                })
            }
        })
    }

    function clearStatusFilters() {
        $(`#fromdate-filter`).val('');
        $(`#requestdate-filter`).val('');
        $(`#todate-filter`).val('');
        $(`#leavetype-filter`).val('');
        $(`#staffname-filter`).val('');
        filterStatusList();
    }


    function filterStatusList() {
        var leavetype_filter = $(`#leavetype-filter`).val().toLowerCase();
        var staffname_filter = $(`#staffname-filter`).val().toLowerCase();
        var fromdate_filter = $(`#fromdate-filter`).val();
        var requestdate_filter = $(`#requestdate-filter`).val();
        
        var todate_filter = $(`#todate-filter`).val();

        var find_string = '';
        if (leavetype_filter != '') {
            find_string += `[data-leavetype*='${leavetype_filter}']`;
        }
        if (staffname_filter != '') {
            find_string += `[data-staffname*='${staffname_filter}']`;
        }
        if (fromdate_filter != '') {
            find_string += `[data-fromdate*='${fromdate_filter}']`;
        }

        if (requestdate_filter != '') {
            find_string += `[data-requestdate*='${requestdate_filter}']`;
        }
        if (todate_filter != '') {
            find_string += `[data-todate*='${todate_filter}']`;
        }
        // console.log(find_string);
        if (find_string === '') {
            $(".staff-filters").show();
        } else {
            $(".staff-filters").hide();
            $(`.staff-filters${find_string}`).show();
        }
    }

    function cancelLeave(leave_id, staff_name, staff_id, status) {
        var html = '<textarea placeholder="Enter reason here..." class="form-control" id="cancel_reason" rows="5"></textarea>';
        Swal.fire({
            title: staff_name,
            html: html,
            confirmButtonText: 'Confirm',
            showCancelButton: true,
            showLoaderOnConfirm: true,
            allowOutsideClick: false,
            preConfirm: function() {}
        }).then((result) => {
            if (result.isConfirmed) {
                var reason = $("#cancel_reason").val().trim();
                var input = {
                    'leave_id': leave_id,
                    'reason': reason
                };
                $.ajax({
                    url: '<?php echo site_url('staff/leaves/cancelLeave'); ?>',
                    type: 'post',
                    data: input,
                    success: function(data) {
                        var status = parseInt(data);
                        if (status == 0) {
                            Swal.fire({
                                title: "Error",
                                text: "Failed to cancel leave",
                                icon: "error",
                            });
                        } else {
                            Swal.fire({
                                title: "Success",
                                text: "Leave cancelled successfully",
                                icon: "success",
                            });
                            getLeaves();
                        }
                    }
                });
            }
        });
    }

    function getStatus(status_val) {
        var status = "Pending";
        if (status_val == '1') {
            status = "<span class='text-success'>Approved</span>";
        } else if (status_val == '2') {
            status = "<span class='text-success'>Auto Approved</span>";
        } else if (status_val == '3') {
            status = "<span class='text-danger'>Rejected</span>";
        } else if (status_val == '4') {
            status = "<span class='text-warning'>Cancelled</span>";
        }
        return status;
    }

    function updateLeaveStatus(leave_id, staff_name, staff_id, status) {
        $.ajax({
            url: '<?php echo site_url('staff/leaves/staffLeave'); ?>',
            data: {
                'leave_id': leave_id
            },
            type: "post",
            success: function(data) {
                var leave = JSON.parse(data);

                if(leave==0){
                    Swal.fire({
                        title: "Error",
                        text: "Leave cannot be Approved or Rejected. It has been Cancelled already.",
                        icon: "error",
                    });

                    getLeaves();
                }else{
                    leave.staff_name = staff_name;
                    var html = '<table class="table table-bordered" style="text-align:left;">';
                    html += '<tr><th style="width: 20%;">Staff</th><td>' + staff_name + '</td><tr>';
                    html += '<tr><th>Leave type</th><td>' + leave.leave_type + '</td><tr>';
                    html += '<tr><th>From date</th><td>' + leave.from_date + '</td><tr>';
                    html += '<tr><th>To date</th><td>' + leave.to_date + '</td><tr>';
                    html += '<tr><th>No. of days</th><td>' + leave.noofdays + '</td><tr>';
                    html += '<tr><th>Reason</th><td>' + leave.reason + '</td><tr>';

                    if(leave.leave_evidence_url){
                        html += `<tr><th>Leave Evidence</th><td><button type="button" class="btn btn-primary" data-doc_url='${leave.leave_evidence_url}' data-toggle="modal" data-target="#showLeaveDocModal" data-name="Leave Evidence">View Doc</button></td><tr>`;
                    }else{
                        html+=`<tr><th>Leave Evidence</th><td>No attachment found</td>`;
                    }

                    if(leave.leave_plan_url){
                        html += `<tr><th>Leave Plan</th><td><button type="button" class="btn btn-primary" data-doc_url='${leave.leave_plan_url}' data-toggle="modal" data-target="#showLeaveDocModal" data-name="Leave Plan">View Doc</button></td><tr>`;
                    }else{
                        html+=`<tr><th>Leave Plan</th><td>No attachment found</td>`;
                    }

                    html += '<tr><th>Status</th><td>';
                    html += '<label class="radio-inline"><input value="1" type="radio" ' + (leave.status == 1 ? 'checked' : '') + ' name="status">Approve</label>';
                    html += '<label class="radio-inline"><input value="3" type="radio" ' + (leave.status == 0 ? 'checked' : '') + ' name="status">Reject</label>';
                    html += '</td><tr>';
                    html += '<tr><th>Remarks</th><td><textarea id="description" class="form-control" id="description"></textarea></td><tr>';
                    html += '<tr><td colspan="2" style="text-align: right;">Check the history of leaves taken: <button onclick="viewHistory(' + staff_id + ', ' + leave_id + ', \'' + staff_name + '\')" class="btn btn-primary btn-sm">View History</button><button style="margin-left: 1rem;" onclick="viewLeaveStatistics(' + staff_id + ', ' + leave_id + ', \'' + staff_name + '\')" class="btn btn-primary btn-sm">View Leave Statistics</button></td></tr>';
                    html += '</table>';
                    Swal.fire({
                        title: 'Approve/Reject Leave',
                        html: html,
                        width: '40%',
                        confirmButtonText: 'Save',
                        showCancelButton: true,
                        showLoaderOnConfirm: true,
                        preConfirm: () => {
                            var status = $("input[name='status']:checked").val();
                            var description = $("#description").val();
                            var data = {
                                'id': leave_id,
                                'status': status,
                                'description': description,
                                'staff_name': staff_name
                            };
                            saveLeaveStatus(data, leave);
                        }
                    });
                }
            },
            error: function(err) {
                console.log(err);
            }
        });
    }

    function constructLeaveStatisticsDetails(leaves){
        let html = '<div class="table-responsive">';
        html += '<table class="table table-bordered">';
        html += '<thead>';
        html += '<tr>';
        html += '<th>#</th>';
        html += '<th>Category name</th>';
        html += '<th>Short name</th>';
        html += '<th>Total Quota</th>';
        html += '<th>Used Quota</th>';
        html += '<th>Remaining Quota</th>';
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';
        leaves.forEach((l,index)=>{
            html+=`
                <tr>
                    <td>${++index}</td>
                    <td>${l.category_name}</td>
                    <td>${l.short_name}</td>
                    <td>${l.total_quota}</td>
                    <td>${l.used_quota}</td>
                    <td>${l.remaining_quota}</td>
                </tr>
            `;
        })

        html += '</tbody>';
        html += '</table></div>';
        return html;
    }

    function viewLeaveStatistics(staff_id, leave_id, staff_name){
        $.ajax({
            url: '<?php echo site_url('staff/leaves/getLeavesStatistics'); ?>',
            data: {
                'staff_id': staff_id
            },
            type: "post",
            success: function (data) {
                const leaves = JSON.parse(data);

                if(!leaves.length){
                    // handle empty check
                    return Swal.fire({
                        icon: "success",
                        title: "Leaves categories",
                        text: "No leave categories to show!",
                    }).then(s=>{
                        updateLeaveStatus(leave_id, staff_name, staff_id);
                    })
                }

                const html = constructLeaveStatisticsDetails(leaves);
                Swal.fire({
                    title: staff_name,
                    html: html,
                    width: '70%',
                    customClass: 'view-custom-swal',
                    allowOutsideClick: false,
                    confirmButtonText: 'Okay',
                    showConfirmButton: true,
                }).then((result) => {
                    updateLeaveStatus(leave_id, staff_name, staff_id);
                });
            }
        })
    }

    function viewHistory(staff_id, leave_id, staff_name) {
        $.ajax({
            url: '<?php echo site_url('staff/leaves/getLeavesHistory'); ?>',
            data: {
                'staff_id': staff_id
            },
            type: "post",
            success: function(data) {
                var leaves = JSON.parse(data);
                var html = constructDetails(leaves, staff_id, leave_id);
                Swal.fire({
                    title: staff_name,
                    html: html,
                    width: '40%',
                    allowOutsideClick: false,
                    confirmButtonText: 'Okay',
                    showConfirmButton: true,
                }).then((result) => {
                    updateLeaveStatus(leave_id, staff_name, staff_id);
                });
            },
            error: function(err) {
                console.log(err);
            }
        });
    }

    function constructDetails(leaves, staff_id, leave_id) {
        var html = '';
        html += '<table class="table table-bordered">';
        html += '<thead>';
        html += '<tr>';
        html += '<th>#</th>';
        html += '<th>From</th>';
        html += '<th>To</th>';
        html += '<th>Type</th>';
        html += '<th>Days</th>';
        html += '<th>Status</th>';
        html += '<th>Applied By</th>';
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';
        var j = 1;
        for (var i in leaves) {
            if (leave_id == leaves[i].leave_id) continue;
            var status = (leaves[i].status == 0) ? 'Pending' : ((leaves[i].status == 3) ? '<span class="text-danger">Rejected</span>' : '<span class="text-success">Approved</span>');
            var filed_by = (leaves[i].leave_filed_by == 0) ? 'Admin' : leaves[i].staff_name;
            html += '<tr>';
            html += '<td>' + (j++) + '</td>';
            html += '<td>' + leaves[i].from_date + '</td>';
            html += '<td>' + leaves[i].to_date + '</td>';
            html += '<td>' + leaves[i].short_name + '</td>';
            html += '<td>' + parseFloat(leaves[i].noofdays) + '</td>';
            html += '<td>' + status + '</td>';
            html += '<td>' + filed_by + '</td>';
            html += '</tr>';
        }
        html += '</tbody>';
        html += '</table>';
        return html;
    }

    function saveLeaveStatus(input, leave) {
        $.ajax({
            url: '<?php echo site_url('staff/leaves/saveLeaveStatus'); ?>',
            data: input,
            type: "post",
            success: function(data) {
                if(data==0){
                    Swal.fire({
                        title: "Error",
                        text: "Leave cannot be Approved or Rejected. It has been Cancelled already.",
                        icon: "error",
                    });

                    getLeaves();
                }else{ 
                    if (parseInt(data)) {
                        Swal.fire({
                            title: "successful",
                            text: "Updated status successfully",
                            icon: "success",
                        });
                        leave.status = input.status;
                        leave.description = input.description;
                    } else {
                        Swal.fire({
                            title: "Error",
                            text: "Failed to update status",
                            icon: "error",
                        });
                    }
                    var sl = $("#leave_" + input.id + " td:first").html();
                    var html = '<td>' + sl + '</td>';
                    leave.id = input.id;
                    html += constructLeave(leave);
                    $("#leave_" + input.id).html(html);
                }
            },
            error: function(err) {
                console.log(err);
            }
        });
    }
</script>
<style>
    .btn_align {
        margin-bottom: 4px;
        width: 32px;
    }

    ul.panel-controls>li>a {
        border-radius: 50%;
    }

    .card {
        margin-bottom: 1rem;
        border-radius: 0.75rem;
    }

    .modal{
        z-index: 1061;
    }

    .new_circleShape_res {
        padding: 8px;
        border-radius: 50% !important;
        color: white !important;
        font-size: 22px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        float: left;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
    }

    .swal2-popup{
        width:50%;
    }
</style>