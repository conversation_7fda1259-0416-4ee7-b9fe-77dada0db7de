<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<style>
div {
  border: 2px solid #000;
  width: 50px;
  height: 50px;
  margin: 10px;
	background: green;
}
</style>
</head>
<body>

<h1>Nested block elements</h1>

<div style="opacity: 0.8;">
  <div style="opacity: 0.8;">
    <div style="opacity: 0.8;">
      <div style="opacity: 0.8;">
        <div style="opacity: 0.8;">
          <div style="opacity: 0.8;">
            <div style="opacity: 0.8;">
              <div style="opacity: 0.8;">
                <div style="opacity: 0.8;">
                  <div style="opacity: 0.8;">
                    <div style="opacity: 0.8;">
                      <div style="opacity: 0.8;">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<br />
<br />
<br />
<br />
<br />
<br />
<br />
<br />
<br />

<h1>Inline elements</h1>

<span style="width: 100%;">
  <span style="opacity: 0.1">ab</span>
  <span style="opacity: 0.2">cb</span>
  <span style="opacity: 0.3">ef</span>
  <span style="opacity: 0.4">gh</span>
  <span style="opacity: 0.5">ij</span>
  <span style="opacity: 0.6">kl</span>
  <span style="opacity: 0.7">mn</span>
  <span style="opacity: 0.8">op</span>
  <span style="opacity: 0.9">qr</span>
  <span style="opacity: 1.0">st</span>
</span>

<br/>

<span style="width: 100%; opacity: 0.5;">
  <span style="opacity: 0.1">ab</span>
  <span style="opacity: 0.2">cb</span>
  <span style="opacity: 0.3">ef</span>
  <span style="opacity: 0.4">gh</span>
  <span style="opacity: 0.5">ij</span>
  <span style="opacity: 0.6">kl</span>
  <span style="opacity: 0.7">mn</span>
  <span style="opacity: 0.8">op</span>
  <span style="opacity: 0.9">qr</span>
  <span style="opacity: 1.0">st</span>
</span>

<div style="opacity: 0.1;">0.1</div>
<div style="opacity: 0.2;">0.2</div>
<div style="opacity: 0.3;">0.3</div>
<div style="opacity: 0.4;">0.4</div>
<div style="opacity: 0.5;">0.5</div>
<div style="opacity: 0.6;">0.6</div>
<div style="opacity: 0.7;">0.7</div>
<div style="opacity: 0.8;">0.8</div>
<div style="opacity: 0.9;">0.9</div>
<div style="opacity: 1.0;">1.0</div>

<div style="opacity: 1.0;">1.0 opacity</div>
<div>No opacity</div>

<div style="opacity: 0.5; height: auto;">
	<div style="opacity: 0.5;">
    <div style="opacity: 1.0;"></div>
    <div style="opacity: 0.5;"></div>
  </div>
	<div style="opacity: 0.5;"></div>
</div>

</body> </html>
