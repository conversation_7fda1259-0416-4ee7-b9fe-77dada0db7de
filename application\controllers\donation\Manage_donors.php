<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON>
 *          <EMAIL>
 *
 * Created:  04 August 2022
 *
 * Description: Controller for Managing Donors.
 *
 * Requirements: PHP5 or above
 *
 */

class Manage_donors extends CI_Controller
{
  function __construct()
  {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('DONATION')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('DONATION.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('class_section');
    $this->load->model('donation/donation_model');
    $this->config->load('form_elements');
  }

  //Landing function to show non-compliance menu
  public function index()
  {
    $data['main_content']    = 'donation/manage_donors/index_desktop.php';
    $this->load->view('inc/template', $data);
  }

  public function add_donor()
  {
    echo $this->donation_model->add_donor($_POST);
  }

  public function get_donor_list()
  {

    $data['donor_data'] = $this->donation_model->get_donor_data();
    echo json_encode($data);
  }

  public function donor_profile($id)
  {
    $data["id"] = $id;
    $data['sections'] = $this->class_section->getAllClassSections();
    $data['main_content']    = 'donation/manage_donors/donor_profile.php';
    $this->load->view('inc/template', $data);
  }

  public function add_commitment_data()
  {
    // echo "<pre>"; print_r($_POST); die();

    $result = $this->donation_model->add_commitment_data($_POST);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully inserted.');
    }
    // else{
    //   $this->session->set_flashdata('flashError', 'Something went wrong.');
    // }
    redirect("/donation/manage_donors/donor_profile/" . $_POST['donor_id']);
  }

  public function get_donor_list_committment_data()
  {
    $donor_id = $_POST['donor_id'];
    $result = $this->donation_model->get_donor_list_committment_databyid($donor_id);
    echo json_encode($result);
  }
  public function get_committed_data()
  {
    $donor_id = $_POST['donor_id'];
    $result = $this->donation_model->get_committed_data($donor_id);
    echo json_encode($result);
  }

  public function insert_donation_data()
  {
    $lastInsert_id = $this->donation_model->add_donation_data($_POST);

    if ($lastInsert_id) {
      $this->donation_model->update_receipt_number_for_donation($lastInsert_id);
      $this->session->set_flashdata('flashSuccess', 'Successfully inserted.');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    }
    redirect("/donation/manage_donors/donor_profile/" . $_POST['collection_donor_id']);
  }

  public function get_donor_collection_data()
  {
    $donor_id = $_POST['donor_id'];
    $result = $this->donation_model->get_donor_collection_data_by_donor_id($donor_id);
    echo json_encode($result);
  }

  public function donor_receipt($id)
  {
    $data['receipts'] = $this->donation_model->get_donor_collection_data_by_id($id);
    // echo "<pre>"; print_r($data['receipts']); die();
    $data['main_content']    = 'donation/manage_donors/donation_receipt';
    $this->load->view('inc/template', $data);
  }

  public function donor_summary_report()
  {
    $data['main_content']    = 'donation/manage_donors/donor_summary_report';
    $this->load->view('inc/template', $data);
  }

  public function get_donor_summary_report()
  {
    $result = $this->donation_model->get_donor_summary_report();
    echo json_encode($result);
  }

  public function pledge_summary_report()
  {
    $data['main_content']    = 'donation/manage_donors/pledge_summary_report';
    $this->load->view('inc/template', $data);
  }

  public function pledge_for()
  {
    $data['main_content']    = 'donation/manage_donors/pledge_for';
    $this->load->view('inc/template', $data);
  }

  public function add_pledge_for_name()
  {
    // echo "<pre>"; print_r($_POST); die();
    $pledge_name = $this->donation_model->add_pledge_for_name($_POST);
    if ($pledge_name) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Added New Pledge Category.');
    } else {
      $this->session->set_flashdata('flashError', 'Pledge Not Added.');
    }
    redirect("/donation/manage_donors/pledge_for");
  }

  public function get_pledge_for_name()
  {
    $result = $this->donation_model->get_pledge_for_name();
    // echo "<prep>"; print_r($result); die();
    echo json_encode($result);
  }

  public function get_pledge_collection_data()
  {
    $donor_id = $_POST['donor_id'];
    $pledge_for_id = $_POST['pledge_for_id'];
    $result = $this->donation_model->get_pledge_summary_report($donor_id, $pledge_for_id);
    echo json_encode($result);
    // echo "hello"; die();
  }

  public function donation_collections()
  {
    $data['main_content'] = 'donation/manage_donors/collections';
    $this->load->view('inc/template', $data);
  }

  public function get_donation_collections()
  {
    // echo "<prep>";print_r($_POST); die();
    $result = $this->donation_model->get_donation_collections($_POST);
    echo json_encode($result);
  }

  public function get_pledge_categories()
  {
    $result = $this->donation_model->get_pledge_categories();
    echo json_encode($result);
    // echo '<pre>'; print_r($_POST); die();
  }
  public function get_sectionwise_studentnames()
  {
    $section_id = $_POST['section_id'];
    $data['students'] = $this->donation_model->getSectionStudentNames($section_id);
    echo json_encode($data);
  }

  public function get_student_fee_trans_details()
  {
    $studentId = $_POST['studentId'];
    $result = $this->donation_model->get_student_fee_trans_detailsbyid($studentId);
    echo json_encode($result);
  }

  public function submit_donor_commitment_amount()
  {
    $donor_id=$_POST['donor_id'];

    $result = $this->donation_model->insert_donation_usage_details();
    // redirect("donation/manage_donors/donor_profile/".$donor_id);

    // if($result){
    // }
  }

  public function get_donation_usage_data(){
    $donor_committment_id = $_POST['donor_committment_id'];
    $result = $this->donation_model->get_donation_usage_data_donor_comt_id($donor_committment_id);
    echo json_encode($result);
  }

  public function get_beneficiary_data()
  {
    $result = $this->donation_model->get_beneficiary_data();
    echo json_encode($result);
    // echo '<pre>'; print_r($_POST); die();
  }
}
