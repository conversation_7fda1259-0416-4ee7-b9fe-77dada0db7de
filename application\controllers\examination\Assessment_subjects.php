<?php
class Assessment_subjects extends CI_Controller {
    private $yearId;
    function __construct() {
        parent::__construct();
       if (!$this->ion_auth->logged_in()) {
         redirect('auth/login', 'refresh');
       }
       if (!$this->authorization->isModuleEnabled('EXAMINATION')) {
        redirect('dashboard', 'refresh');
      }
      if (!$this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN')) {
        redirect('dashboard', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('examination/Assessment_model','assessment_model');
      $this->load->model('examination/Assessment_subjects_model', 'assessment_subjects');
    }

    public function index($class_id=0){
        $data['classes'] = $this->assessment_subjects->getsClassId($this->yearId);
        $data['class_id'] = $class_id;
        if($class_id == 0) {
          $data['class_id'] = $data['classes'][0]->id;
        }
        $data['ass_entities'] = $this->assessment_subjects->filterAssessmentEntities($data['class_id']);
        $data['yearId'] = $this->yearId;
        $data['years'] = $this->assessment_subjects->getAcadYears();
        $groups = $this->assessment_subjects->getGroups($data['class_id']);
        $data['groups'] = array();
        foreach ($groups as $key => $group) {
          $data['groups'][$group->id] = $group;
        }
        // echo "<pre>"; print_r($data['groups']); die();
        // $data['main_content'] = 'entities/index';
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'entities/subjects_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'entities/subjects_mobile';
        }else{
          $data['main_content'] = 'entities/subjects';    	
        }
        $this->load->view('inc/template', $data);
    }

    public function getYearClasses() {
      $year = $_POST['yearId'];
      $classes = $this->assessment_subjects->getsClassId($year);
      echo json_encode($classes);
    }

    public function addEntity(){
      $class_id = $this->input->post('cs_id');
      $status = $this->assessment_subjects->addEntity();
      if($status)
        $this->session->set_flashdata('flashSuccess', 'Successfully Added');
      else 
        $this->session->set_flashdata('flashError', 'Something Went Wrong!');
      redirect('examination/Assessment_subjects/index/'.$class_id);
    }

    public function addSubject() {
      $class_id = $this->input->post('cs_id');
      $status = $this->assessment_subjects->addSubject();
      if($status)
        $this->session->set_flashdata('flashSuccess', 'Successfully Added');
      else 
        $this->session->set_flashdata('flashError', 'Something Went Wrong!');
      redirect('examination/Assessment_subjects/index/'.$class_id);
    }

    public function checkClassMappingString() {
      $class_id = $_POST['class_id'];
      $mapping_string = $_POST['mapping_string'];
      $table = $_POST['table'];
      $status = $this->assessment_subjects->checkMappingString($class_id, $mapping_string, $table);
      echo $status;
    }

    public function editEntityForm(){
        $entity_id = $_POST['id'];
        $entity = $this->assessment_subjects->getAssessmentEntity($entity_id);
        $json = json_decode($entity->derived_formula);
        unset($entity->derived_formula);
        $data['entData'] = $entity;
        $data['entityIds'] = array();
        $data['formula'] = array();
        if($json != null) {
          $data['entityIds'] = $json->entityId;
          $data['formula'] = ($json->formula)->name;
        }
        echo json_encode($data);
       
    }

    public function editSubject($entityId){
        $data['entity_id'] = $entityId;
        $class_id = $this->input->post('class_id');
        $status = $this->assessment_subjects->updateSubject($data['entity_id']);
        if($status)
          $this->session->set_flashdata('flashSuccess', 'Successfully Updated');
        else 
          $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        redirect('examination/Assessment_subjects/index/'.$class_id);
    }

    public function editEntity($entityId){
        $data['entity_id'] = $entityId;
        $status = $this->assessment_subjects->updateEntity($data['entity_id']);
        if($status)
          $this->session->set_flashdata('flashSuccess', 'Successfully Updated');
        else 
          $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        redirect('examination/Assessment_subjects/index');
    }

    public function deleteEntity($entity_id){
        $status = $this->assessment_subjects->deleteEntity($entity_id);
        if($status)
          $this->session->set_flashdata('flashSuccess', 'Successfully Deleted');
        else 
          $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        redirect('examination/Assessment_subjects/index');
    }

    public function filterEntities(){
      
        $entities = $this->assessment_subjects->filterAssessmentEntities($_POST['class_id']);
        $subjects = $this->assessment_subjects->getGroups($_POST['class_id']);
        $groups = array();
        foreach ($subjects as $key => $subject) {
          $groups[$subject->id] = $subject;
        }

        if(empty($entities)){
          $template = '<h4>No entities added</h4>';
        } else {
          $template = "<table id='customers1' class='table datatable'>
            <thead>
              <tr>
                <th>#</th>
                <th>Name</th>
                <th>Short Name</th>
                <th>Group</th>
                <th>Mapping String</th>
                <th>Class ID</th>
                <th>Type</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>";
               
            $i=1;     
              foreach ($entities as  $entity) { 
              
             $template .= "<tr>
                <td>".$i++."</td>
                <td>".$entity->name."</td>
                <td>".$entity->short_name."</td>
                <td>".$entity->entity_name."</td>
                <td>".$entity->mapping_string."</td>
                <td>".$entity->class_id."</td>
                <td>".$entity->ass_type."</td>
               
                <td>
                  <a href='javascript:void(0)' class='btn btn-warning ' data-placement='top'  data-toggle='tooltip' data-original-title='Edit Entity' onclick='edit(".$entity->id.")'><i class='fa fa-edit'></i></a>
                  <a href='".site_url('examination/Assessment_subjects/deleteEntity/'.$entity->id)."' class='btn btn-warning ' data-placement='top'  data-toggle='tooltip' data-original-title='Delete Entity'><i class='fa fa-trash-o'></i></a>
                </td>
                </tr>";
              }

           $template .= "</tbody>
          </table> ";
        }
        $data = array('table'=>$template, 'groups'=>$groups);
        
        echo json_encode($data);
        //echo 1;
       // print_r($data['entities']);
        //echo json_encode($data['entities']);
    }

    public function getEntitiesAndGroups(){
      $classId = $_POST['classId'];
      echo json_encode($this->assessment_subjects->getEntGroupsByClass($classId));
    }

    public function addCloneEntities(){
    $class_id = $this->input->post('toClass');
      $status = $this->assessment_subjects->addCloneEntAndGroups();
      if($status)
        $this->session->set_flashdata('flashSuccess', 'Successfully Added');
      else 
        $this->session->set_flashdata('flashError', 'Something Went Wrong!');
      redirect('examination/Assessment_subjects/index/'.$class_id);
    }

    public function getComponents(){
      $data = $this->assessment_subjects->getComponents($_POST['classId'], $_POST['gId']);
      echo json_encode($data);
    }

    public function ordering(){
        $data['classes'] = $this->assessment_model->getClassess();
        $data['main_content'] = 'examination/subjects/ordering';
        $this->load->view('inc/template', $data);
    }

    public function sorting(){
        $data['classes'] = $this->assessment_model->getClassess();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'examination/subjects/sorting_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'examination/subjects/sorting_mobile';
        }else{
          $data['main_content'] = 'examination/subjects/sorting';     	
        }
        $this->load->view('inc/template', $data);
    }

    public function getEntitiesAssmtForSorting(){
    $classId = $_POST['classId'];
    $type = $_POST['type'];

    switch ($type) {
      case 'assessments':
        $data = $this->assessment_subjects->getClsAssessments($classId, 'both');
        foreach ($data as $key => $value) {
          $value->subName = $value->short_name;
          unset($value->short_name);
        }
        break;
      case 'groups':
        $data = $this->assessment_model->getAssEntitiesOfClass($classId, 'group');
        break;
      case 'components':
        $data = $this->assessment_model->getAssEntitiesOfClass($classId, 'component');
        break;
      default:
        $data = array();
        break;
    }
    echo json_encode($data);
  }

  public function get_subjects_assessments() {
    $class_id = $_POST['class_id'];
    $data['assessments'] = $this->assessment_subjects->getAssessmentsForSorting($class_id);
    $data['subjects'] = $this->assessment_subjects->getSubjectsForSorting($class_id);
    $data['computed'] = $this->assessment_subjects->getComputedSorting($class_id);
    echo json_encode($data);
  }

  public function save_sorting_order() {
    $type = $_POST['type'];
    $sorted_items = $_POST['sorted_items'];
    // echo "<pre>"; print_r($sorted_items); die();
    if($type == 'components') {
      $this->assessment_subjects->save_component_sorting($sorted_items);
    } else if($type == 'groups') {
      $this->assessment_subjects->save_group_sorting($sorted_items);
    } else if($type == 'computed') {
      $this->assessment_subjects->save_computed_sorting($sorted_items);
    } else {
      $this->assessment_subjects->save_assessment_sorting($sorted_items);
    }
    echo 1;
  }

  public function getComponentsForSorting(){
    $classId = $_POST['classId'];
    $data['groups'] = $this->assessment_model->getAssEntitiesOfClass($classId, 'group');
    $gId = array();
    if(!empty($data['groups']))
      $gId[] = $data['groups'][0]->id;

    $data['components'] = $this->assessment_subjects->getEntityIdsByGroupId($gId);
    echo json_encode($data);
  }

  public function getEntitiesOfGroup(){
    $gId[] = $_POST['gId'];
    $data['components'] = $this->assessment_subjects->getEntityIdsByGroupId($gId);
    echo json_encode($data);
  }

  public function addSoringOrder(){
    $status = $this->assessment_subjects->addSoringOrder();
    echo $status;
  }

  public function manage_subjects(){
    $data['classes'] = $this->assessment_subjects->getsClassId($this->yearId);
    // $data['class_id'] = $class_id;
    // if($class_id == 0) {
    //   $data['class_id'] = $data['classes'][0]->id;
    // }
    $data['yearId'] = $this->yearId;
    $data['years'] = $this->assessment_subjects->getAcadYears();
    $data['groups'] = $this->assessment_subjects->get_groups_all();
    // echo "<pre>"; print_r($data['groups']); die();
    // $groups = $this->assessment_subjects->getGroups($data['class_id']);
    // $data['groups'] = array();
    // foreach ($groups as $key => $group) {
    //   $data['groups'][$group->id] = $group;
    // }
    // $data['main_content'] = 'examination/subjects_v2/index';
    $data['grades'] = $this->assessment_subjects->get_grading_systems();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/subjects_v2/manage_subjects_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/subjects_v2/manage_subjects_mobile'; 
    }else{
      $data['main_content'] = 'examination/subjects_v2/manage_subjects'; 	
    }      
    
    // $data['main_content'] = 'entities/subjects_new';
    $this->load->view('inc/template', $data);
  }

  public function get_group_maping_string(){
    $groupId = $_POST['groupId'];
    $result =  $this->assessment_subjects->get_group_maping($groupId);
    echo json_encode($result);
  }

  public function get_entity_maping_string(){
    $entity_name = $_POST['entity_name'];
    echo  $this->assessment_subjects->get_entity_maping($entity_name);
  }

  public function assessments_entities(){
    $classId = $_POST['classId'];
    $data['ass_entities'] = $this->assessment_subjects->filterAssessmentEntities($classId);
    $groups = $this->assessment_subjects->getGroups($classId);
    $data['groups'] = array();
    foreach ($groups as $key => $group) {
      $data['groups'][$group->id] = $group;
    }
    echo json_encode($data);
  }

  public function addSubject_new() {
    echo $this->assessment_subjects->addSubject();
  }

  public function deleteEntity_new(){   
    $assId = $_POST['assId'];
    echo $this->assessment_subjects->deleteEntity($assId);
  }

  public function updateSubjectbyId(){
    $entity_id = $this->input->post('entity_id');
    echo $this->assessment_subjects->updateSubject($entity_id);
  }

  public function getEntitiesAndGroups_new(){
    $classId = $_POST['classId'];
    $data['ass_entities'] = $this->assessment_subjects->getEntGroupsByClass($classId);
    $groups = $this->assessment_subjects->getGroups($classId);
    $data['groups'] = array();
    foreach ($groups as $key => $group) {
      $data['groups'][$group->id] = $group;
    }
    echo json_encode($data);
  }

  public function addCloneEntities_new(){
    $status = $this->assessment_subjects->addCloneEntAndGroups_new();
    if($status)
      $this->session->set_flashdata('flashSuccess', 'Successfully Added');
    else 
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    redirect('examination/Assessment_subjects/manage_subjects');
  }

  public function create_subjects(){
    $data['privileges'] = $this->_compareJsonAndDbGroupSubejct();
    if($this->mobile_detect->isMobile() || $this->mobile_detect->isTablet()) { 
      $data['main_content'] = 'entities/created_subject_mobile';
    } else {
      $data['main_content'] = 'entities/created_subject';
    }
    $this->load->view('inc/template', $data);
  }

  private function _compareJsonAndDbGroupSubejct() {
    $dbPriv = $this->_getPrivilegesInDB();
    $configPriv = $this->_getPrivilegesInJson();
    $privileges = $dbPriv['privileges'];
    $sub_privileges = $dbPriv['sub_privileges'];
    $finalData = $this->_makeData($configPriv, $privileges, $sub_privileges);
    return $finalData;
  }

  private function _getPrivilegesInDB() {
    $prData = $this->assessment_subjects->get_group_subject_All();
    $privs = $prData['prev_data'];
    $sub_privs = $prData['sub_prev_data'];
    $privileges = array();
    $sub_privileges = array();
    foreach ($privs as $k => $val) {
        $privileges[$val->pName] = $val->pId;
        $sub_privileges[$val->pId] = array();
    }
    
    foreach ($sub_privs as $k => $val) {
        $sub_privileges[$val->privilege_id][] = array('subId' => $val->psId, 'subName' => $val->spName,'subMaping' => $val->subject_maping);
    }
    return array('privileges' => $privileges, 'sub_privileges' => $sub_privileges);
  }

  private function _getPrivilegesInJson() {
    $jsonData = json_decode(file_get_contents('application/config/subject_master.json'));
    $json_privileges = array();
    foreach ($jsonData as $key => $val) {
      $json_privileges[$val->group][$val->group_maping] = $val->subject;
    }
    return $json_privileges;
  }

  private function _makeData($config, $privileges, $sub_privileges) {
    $finalData = array();
    foreach ($config as $groupName => $groups) {
      foreach ($groups as $group_maping => $subs) {        
        $finalData[$groupName] = array();
        $finalData[$groupName]['groupId'] = null;
        $finalData[$groupName]['groupName'] = $groupName;
        $finalData[$groupName]['groupMaping'] = $group_maping;
        $finalData[$groupName]['in_db'] = 0;
        $finalData[$groupName]['in_json'] = 1;
        $finalData[$groupName]['subs'] = array();    
        foreach ($subs as $key => $sub) {          
          $finalData[$groupName]['subs'][] = array(
            'subId' => null,
            'subName' => $sub->name,
            'subMaping' => $sub->mapping,
            'in_db' => 0,
            'in_json' => 1
          );
        }
      } 
    }    
    foreach ($privileges as $privName => $privId) {
      if(array_key_exists($privName, $finalData)) {
        $finalData[$privName]['privId'] = $privId;
        $finalData[$privName]['in_db'] = 1;
        foreach ($sub_privileges as $prId => $subs) {
          foreach ($subs as $k => $sub) {
            if($prId != $privId) {
              continue;
            }
            if(array_key_exists($sub['subName'], $finalData[$privName]['subs'])) {
              $finalData[$privName]['subs'][$sub['subName']]['subId'] = $sub['subId']; 
              $finalData[$privName]['subs'][$sub['subName']]['in_db'] = 1; 
            } else {
              $finalData[$privName]['subs'][$sub['subName']] = array();
              $finalData[$privName]['subs'][$sub['subName']]['subName'] = $sub['subName']; 
              $finalData[$privName]['subs'][$sub['subName']]['subMaping'] = $sub['subMaping']; 
              $finalData[$privName]['subs'][$sub['subName']]['subId'] = $sub['subId']; 
              $finalData[$privName]['subs'][$sub['subName']]['in_db'] = 1;
              $finalData[$privName]['subs'][$sub['subName']]['in_json'] = 0;
            }
          }
        }
      } else {
        $finalData[$privName] = array();
        $finalData[$privName]['privId'] = $privId;
        $finalData[$privName]['privName'] = $privName;
        $finalData[$privName]['in_db'] = 1;
        $finalData[$privName]['in_json'] = 0;
        $finalData[$privName]['subs'] = array();
        foreach ($sub_privileges as $prId => $subs) {
          foreach ($subs as $k => $sub) {
            if($prId != $privId) {
              continue;
            }
            $finalData[$privName]['subs'][$sub['subName']] = array();
            $finalData[$privName]['subs'][$sub['subName']]['subName'] = $sub['subName']; 
            $finalData[$privName]['subs'][$sub['subName']]['subMaping'] = $sub['subMaping']; 
            $finalData[$privName]['subs'][$sub['subName']]['subId'] = $sub['subId']; 
            $finalData[$privName]['subs'][$sub['subName']]['in_db'] = 1;
            $finalData[$privName]['subs'][$sub['subName']]['in_json'] = 0;
          }
        }
      }
    }
    return $finalData;
  }

  public function addGroup() {
    $name = trim($_POST['name']);
    $maping = trim($_POST['maping']);
    $status = $this->assessment_subjects->addGroupName($name, $maping);
    echo $status;
  }

  public function addSubeject() {
    $sub_name = trim($_POST['sub_name']);
    $group_id = $_POST['group_id'];
    $maping = $_POST['maping'];
    $status = $this->assessment_subjects->addSubectName($sub_name, $group_id, $maping);
    echo $status;
  }

  public function removeSubject() {
    $subjectId = $_POST['subjectId'];
    $status = $this->assessment_subjects->removeSubjectname($subjectId);
    echo $status;
  }

  public function add_subject_groups() {
    $data['main_content'] = 'examination/subjects_v2';
    $this->load->view('inc/template', $data);
  }

  public function getSubjectsList() {
    $class_id = $_POST['class_id'];
    $data['subjects'] = $this->assessment_subjects->getSubjectsList($class_id);
    // $data['subjects'] = $this->assessment_subjects->getSubjectsList($class_id);
    echo json_encode($data);
  }

  public function getSubjectComponentList() {
    $subject_id = $_POST['subject_id'];
    $data['subject'] = $this->assessment_subjects->getSubjectData($subject_id);
    $data['components'] = $this->assessment_subjects->getSubjectComponentList($subject_id);
    echo json_encode($data);
  }

  public function getExistingComponentData() {
    $class_id = $_POST['class_id'];
    $data = $this->assessment_subjects->getExistingComponentData($class_id);
    echo json_encode($data);
  }

  public function save_subject() {
    echo $this->assessment_subjects->save_subject($_POST);
  }

  public function save_subject_component() {
    // echo "<pre>"; print_r($_POST); die();
    echo $this->assessment_subjects->save_subject_component($_POST);
  }

  public function delete_component() {
    $component_id = $_POST['component_id'];
    $is_used = $this->assessment_subjects->check_component_used($component_id);
    if($is_used != 'not_used') {
      $x= ['status' => '-1', 'value' => $is_used]; //component is used for assessments
    }
    else {
      $y= $this->assessment_subjects->delete_component($component_id);
      $x= ['status' => $y, 'value' => 'Deleted'];
    }
    echo json_encode($x);
  }

  public function delete_subject() {
    $subject_id = $_POST['subject_id'];
    echo $this->assessment_subjects->delete_subject($subject_id);
  }

  public function subject_cloning() {
    $data['classes'] = $this->assessment_subjects->getsClassId($this->yearId);
    $data['yearId'] = $this->yearId;
    $data['years'] = $this->assessment_subjects->getAcadYears();
    $data['current_year'] = $this->acad_year->getAcadYear();
    $data['main_content'] = 'examination/subjects_v2/cloning';
    $this->load->view('inc/template', $data);
  }

  public function get_class_subjects() {
    $class_id = $_POST['class_id'];
    $data = $this->assessment_subjects->get_class_subjects($class_id);
    echo json_encode($data);
  }

  public function save_subjects_cloning() {
    $status= $this->assessment_subjects->save_subjects_cloning();
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Subjects cloning completed.');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    }
    redirect('examination/assessment_subjects/manage_subjects');
  }

  public function save_subjects_cloning_v2() {
    // echo '<pre>'; print_r($this->input->post()); die();
    $status= $this->assessment_subjects->save_subjects_cloning();
    echo json_encode($status);
    // if($status) {
    //   $this->session->set_flashdata('flashSuccess', 'Subjects cloning completed.');
    // } else {
    //   $this->session->set_flashdata('flashError', 'Something went wrong.');
    // }
    // redirect('examination/assessment_subjects/manage_subjects');
  }

  // public function check_for_duplicate_entry() {
  //   $data = $this->assessment_subjects->check_for_duplicate_entry();
  //   echo json_encode($data);
  // }

}