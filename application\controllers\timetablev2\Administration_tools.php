<?php

class Administration_tools extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('timetablev2/section_workload_model','section_workload_model');
    $this->load->model('timetablev2/administration_tools_model','administration_tools_model');
  }

  public function staff_names(){
    $data['main_content']    = 'timetablev2/settings/edit_staff_names.php';
    $this->load->view('inc/template', $data);
  }

  public function refresh_staff_names(){
    $data = $this->administration_tools_model->refresh_staff_names();
    echo json_encode($data);
  }

  public function class_names(){
      
    $data['main_content']    = 'timetablev2/settings/edit_class_names.php';
    $this->load->view('inc/template', $data);
  }

  public function refresh_class_names(){
    $data = $this->administration_tools_model->refresh_class_names();
    echo json_encode($data);
  }

  public function move_staff_to_other_staff() {
    $template_details = $this->administration_tools_model->get_template_details();
    $data['template_details']    = $template_details;

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'timetablev2/settings/move_staff_to_other_staff_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'timetablev2/settings/move_staff_to_other_staff_mobile';
    }else {
      $data['main_content']    = 'timetablev2/settings/move_staff_to_other_staff';
    }
    $this->load->view('inc/template', $data);
  }

  public function get_staff_details_for_move_staff() {
    $template_id = $this->input->post('template_id');
    $data['template_staff'] = $this->administration_tools_model->get_staff_by_template($template_id);
    echo json_encode($data);
  }

  public function move_periods_to_new_staff() {
    $template_id = $this->input->post('template_id');
    $new_staff_id = $this->input->post('new_staff_id');
    $move_staff_id = $this->input->post('move_staff_id');
    $data['result'] = $this->administration_tools_model->move_periods_to_new_staff($template_id, $new_staff_id, $move_staff_id);
    echo json_encode($data);
  }

  public function check_ttv2(){
    $template_details = $this->administration_tools_model->get_template_details();
    $data['template_details']    = $template_details;
    $data['main_content']    = 'timetablev2/settings/check_ttv2.php';
    $this->load->view('inc/template', $data);
  }

  public function get_staff_details(){
    $template_id = $_POST['template_id'];

    $staff_details = $this->administration_tools_model->get_staff_details($template_id);

    echo json_encode( array('staff_ids'=>$staff_details));
  }

  public function get_sec_details(){
    $template_id = $_POST['template_id'];
    $section_details = $this->administration_tools_model->get_section_details($template_id);

    echo json_encode( array('section_details'=>$section_details));
  }

  public function check_staff_data(){
    
    $staff_template_id = $_POST['staff_template_id'];
    $template_id = $_POST['template_id'];
    $data = $this->administration_tools_model->check_staff_data($staff_template_id, $template_id);
    echo json_encode($data);
  }

  public function check_section_data(){
    
    $section_template_id = $_POST['section_template_id'];
    $template_id = $_POST['template_id'];
    $data = $this->administration_tools_model->check_section_data($section_template_id, $template_id);
    echo json_encode($data);
  }

  public function delete_template_view(){
    $template_details = $this->administration_tools_model->get_template_details();
    $data['template_details']    = $template_details;
    $data['main_content']    = 'timetablev2/settings/delete_template_view.php';
    $this->load->view('inc/template', $data);
  }

  public function delete_timetable(){
    $template_id = $_POST['template_id'];
    $delete_template = $this->administration_tools_model->delete_timetable($template_id);

    echo json_encode($delete_template);
  }

  public function check_timetable_allocation_view(){
    $template_details = $this->administration_tools_model->get_template_details();
    $data['template_details']    = $template_details;

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'timetablev2/settings/check_timetable_allocation_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'timetablev2/settings/check_timetable_allocation_mobile';
    }else {
      $data['main_content']    = 'timetablev2/settings/check_timetable_allocation';
    }

    $this->load->view('inc/template', $data);
  }

  public function check_timetable_allocation(){
    $template_id = $_POST['template_id'];
    $timetable_allocation = $this->administration_tools_model->check_timetable_allocation($template_id);

    echo json_encode($timetable_allocation);
  }
  
  public function change_timings(){
    $template_id = $_POST['template_id'];
    $timetable_allocation = $this->administration_tools_model->check_timetable_allocation($template_id);

    echo json_encode($timetable_allocation);
  }

  public function update_timings() {
    $ttv2_period_id = $_POST['ttv2_period_id'];
    $period_name = $_POST['period_name'];
    $short_period_name = $_POST['short_period_name'];
    $ttv2_start_time = $_POST['start_time'];
    $ttv2_end_time = $_POST['end_time'];

    echo $this->administration_tools_model->update_timings($ttv2_period_id, $ttv2_start_time, $ttv2_end_time, $period_name, $short_period_name);
  }
  
  public function update_section_period_type() {
    $ttv2_section_period_id = $_POST['ttv2_section_period_id'];
    $new_period_type = $_POST['new_period_type'];
    $period_name = $_POST['period_name'];
    $short_period_name = $_POST['short_period_name'];

    echo $this->administration_tools_model->update_section_period_type($ttv2_section_period_id, $new_period_type, $period_name, $short_period_name);
  }
  
  public function update_staff_period_type() {
    $ttv2_staff_period_id = $_POST['ttv2_staffedit_period_id'];
    $new_period_type = $_POST['new_period_type'];

    echo $this->administration_tools_model->update_staff_period_type($ttv2_staff_period_id, $new_period_type);
  }

}