<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Suy_user extends CI_Controller {

    public function __construct() {		
        parent::__construct();
        $this->load->model('suy/Suy_model');
        $this->load->model('student/Student_Model');
        $this->load->model('feesv2/fees_student_model');
        $this->load->model('feesv2/fees_cohorts_model');
        $this->load->model('feesv2/fees_collection_model');
        $this->load->library('payment');
        $this->load->model('Admission_model');
        $this->load->model('user_provisioning_model');
        $this->load->model('parent_activation_model');
    }

    public function suy_handle_payment_callback() {
      //Call the payment callback handler
      $result = $this->payment->payment_callback_school_handler_suy($_POST);
      $data = $result;
      $this->load->view('suy/payment_done', $data);
    }

    public function payment_done_suy(){
        if ($_POST['response_type'] === 'IMMEDIATE') {
            $this->__handle_immediate_op_response($_POST);
         } elseif ($_POST['response_type'] === 'DELAYED') {
            // Code Delete because handleing delayed response like an immediate response
            $this->__handle_immediate_op_response($_POST);
        } else {
            $this->__handle_recon_op_response($_POST);
        }
    }

    private function __handle_immediate_op_response($response){
        if ($response['transaction_status'] === 'SUCCESS') {
          $is_receipt_generated = $this->fees_collection_model->is_receipt_generated($response['source_id']);

          // if receipt is already generated, we have already updated the transaction and schedule table. No need to update again. Just show the already generated receipt.
          if ($is_receipt_generated) {
            return;
          }
          // generate and update receipt number after transcation
            $result =  $this->fees_collection_model->update_trans_student_all_table($response['source_id']);
            $suy_update = $this->Suy_model->update_payment_fee_trans_id_suy_enquiry($response['enquiry_id'],$response['source_id'],'Fee Paid');
          if (!$result) {
            $this->session->set_flashdata('flashError', 'Something went wrong. Will get back to you shortly');
          } else {
            $this->session->set_flashdata('flashSuccess', 'Transcation successful');
          }
          
        } else {
          //Online payment failed
            $this->fees_collection_model->update_transcation_status($response['source_id'], 'FAILED');
            $suy_update = $this->Suy_model->update_payment_fee_trans_id_suy_enquiry($response['enquiry_id'],$response['source_id'],'Fee Not Paid');
        }

        redirect('suy/Suy_user/receipt_user/' .  $response['transaction_id'] . '/' . $response['transaction_date'] . '/' . $response['transaction_time'].'/'.$response['transaction_status']);
    }

    public function receipt_user($transaction_id, $transaction_date, $transaction_time, $transaction_status){
        $data['transactiondetails'] = $this->suy_model->get_suy_transaction_details($transaction_id);
        $data['transaction_id'] = $transaction_id;
        $data['transaction_date'] = $transaction_date;
        $data['transaction_time'] = $transaction_time;
        $data['transaction_status'] = $transaction_status;
        $this->load->view('suy/receipt_view', $data);
    }

}