<?php

require APPPATH . 'libraries/REST_Controller.php';

class Assessment extends REST_Controller
{
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
            $data = ['error' => 'User not logged in'];
            $this->response($data, REST_Controller::HTTP_UNAUTHORIZED);
			$this->output->_display();
			exit;
		}
		$this->load->model('parentv2/Assessment_model');
	}

	public function assessment_get() {
        $class_id = $this->get('class_id');

		$data['assessments'] = $this->Assessment_model->getPublishedAssessmentsOfStudent($class_id);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

}
?>