<?php defined('BASEPATH') OR exit('No direct script access allowed');

class PushNotification_controller extends CI_Controller {

	public function __construct()	{
        parent::__construct();
        $this->load->model('Ion_auth_model');      
        $this->load->library('pushnotification');
    }

    public function addFcmToken() {
        $inputJSON = file_get_contents('php://input');
        // trigger_error('In addFcmToken');
        // trigger_error($inputJSON);
        $input = json_decode($inputJSON, TRUE);

        if(empty($input)) {
            // trigger_error('Fields cannot be empty');
            echo json_encode(['status' => -1000, 'message' => 'Fields cannot be empty']);
        } elseif(empty($input['username'])) {
            // trigger_error('Username cannot be empty');
            echo json_encode(['status' => -1000, 'message' => 'Username cannot be empty']);
        } elseif(empty($input['token'])) {
            // trigger_error('Token cannot be empty');
            echo json_encode(['status' => -1000, 'message' => 'Token cannot be empty']);
        } else {
            //Check Username exits
            if($this->Ion_auth_model->checkifUsernameExists($input['username'])) {
                //Insert/Update Token
                $this->Ion_auth_model->updateToken($input['username'],$input['token']); 
                // trigger_error('Added token successfully');
                echo json_encode(['status' => 200, 'message' => 'token added successfully']);
            } else {
                // trigger_error('Username does not exists');
                echo json_encode(['status' => -1000, 'message' => 'Username does not exists']);
            }
        
        }
    }

    public function prepareNotification() {

        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }

        $data['users'] = $this->Ion_auth_model->getUserWithNotificationToken();
        $data['main_content'] = 'pushNotification/index';
        $this->load->view('inc/template', $data);
    }

    public function sendNotification() {

        $input = $this->input->post();
        $token_arr = explode('_', $input['user_token']);

        $user[] = ['user_id' => $token_arr[0], 'token' => $token_arr[1]];

        $this->pushnotification->sendNotification($user, $input['title'], $input['content'], $input['url']);

        echo 'Sent Successfully';
    }


    public function pushNotificationLog() {
        $this->load->model('PushNotification_model');
        $allnotifications = $this->PushNotification_model->getData();

        $data['notifications'] = $allnotifications;
        if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'pushNotification/list_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'pushNotification/list_mobile';
        }else{   	
            $data['main_content']  = 'pushNotification/list';
        }
        $this->load->view('inc/template', $data);
    }

    public function notifications() {
        $this->load->model('PushNotification_model');
        $data['class_section'] = $this->PushNotification_model->getClassSections();
        $data['main_content'] = 'pushNotification/send_notification';
        $this->load->view('inc/template', $data);
    }

    public function submitNotification() {
        $input = $this->input->post();
        $this->load->helper('notification_helper');
        sendStudentNotifications($input['students'], $input['title'], $input['content'], $input['url'], $input['send_to']);
        $this->session->set_flashdata('flashSuccess', 'Successfully sent notification.');
        redirect('PushNotification_controller/notifications');
    }

    public function getSectionStudents() {
        $this->load->model('PushNotification_model');
        $section_id = $_POST['section_id'];
        $students = $this->PushNotification_model->getSectionStudents($section_id);
        echo json_encode($students);
    }
}