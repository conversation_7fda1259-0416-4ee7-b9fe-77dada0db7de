DROP TRIGGER IF EXISTS admission_form_audit;
DELIMITER $$
CREATE TRIGGER admission_form_audit
AFTER UPDATE ON admission_forms FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.academic_year_applied_for, NEW.academic_year_applied_for, 'academic_year_applied_for');
    CALL merge_object(@old<PERSON>son, @newJson, OLD.grade_applied_for, NEW.grade_applied_for, 'grade_applied_for');
    CALL merge_object(@oldJson, @newJson, OLD.std_name, NEW.std_name, 'std_name');
    CALL merge_object(@oldJson, @newJson, OLD.student_middle_name, NEW.student_middle_name, 'student_middle_name');
    CALL merge_object(@oldJson, @newJson, OLD.student_last_name, NEW.student_last_name, 'student_last_name');
    CALL merge_object(@old<PERSON><PERSON>, @new<PERSON><PERSON>, OLD.dob, NEW.dob, 'dob');
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.gender, NEW.gender, 'gender');
    CALL merge_object(@oldJson, @newJson, OLD.birth_taluk, NEW.birth_taluk, 'birth_taluk');
    CALL merge_object(@oldJson, @newJson, OLD.birth_district, NEW.birth_district, 'birth_district');
    CALL merge_object(@oldJson, @newJson, OLD.nationality, NEW.nationality, 'nationality');
    CALL merge_object(@oldJson, @newJson, OLD.religion, NEW.religion, 'religion');
    CALL merge_object(@oldJson, @newJson, OLD.std_mother_tongue, NEW.std_mother_tongue, 'std_mother_tongue');
    CALL merge_object(@oldJson, @newJson, OLD.curriculum_currently_studying, NEW.curriculum_currently_studying, 'curriculum_currently_studying');
    CALL merge_object(@oldJson, @newJson, OLD.boarding, NEW.boarding, "boarding");
    CALL merge_object(@oldJson, @newJson, OLD.second_language_currently_studying, NEW.second_language_currently_studying, 'second_language_currently_studying');
    CALL merge_object(@oldJson, @newJson, OLD.esl_english_as_second_language, NEW.esl_english_as_second_language, 'esl_english_as_second_language');
    CALL merge_object(@oldJson, @newJson, OLD.physical_disability, NEW.physical_disability, 'physical_disability');
    CALL merge_object(@oldJson, @newJson, OLD.learning_disability, NEW.learning_disability, 'learning_disability');
    CALL merge_object(@oldJson, @newJson, OLD.special_needs_description, NEW.special_needs_description, 'special_needs_description');
    CALL merge_object(@oldJson, @newJson, OLD.primary_language_spoken, NEW.primary_language_spoken, 'primary_language_spoken');
    CALL merge_object(@oldJson, @newJson, OLD.transport, NEW.transport, 'transport');
    CALL merge_object(@oldJson, @newJson, OLD.joining_period, NEW.joining_period, 'joining_period');
    CALL merge_object(@oldJson, @newJson, OLD.f_name, NEW.f_name, 'f_name');
    CALL merge_object(@oldJson, @newJson, OLD.f_last_name, NEW.f_last_name, 'f_last_name');
    CALL merge_object(@oldJson, @newJson, OLD.m_last_name, NEW.m_last_name, 'm_last_name');
    CALL merge_object(@oldJson, @newJson, OLD.f_mobile_no, NEW.f_mobile_no, 'f_mobile_no');
    CALL merge_object(@oldJson, @newJson, OLD.f_email_id, NEW.f_email_id, 'f_email_id');
    CALL merge_object(@oldJson, @newJson, OLD.father_aadhar, NEW.father_aadhar, 'father_aadhar');
    CALL merge_object(@oldJson, @newJson, OLD.f_pan_number, NEW.f_pan_number, 'f_pan_number');
    CALL merge_object(@oldJson, @newJson, OLD.f_addr, NEW.f_addr, 'f_addr');
    CALL merge_object(@oldJson, @newJson, OLD.f_district, NEW.f_district, 'f_district');
    CALL merge_object(@oldJson, @newJson, OLD.f_state, NEW.f_state, 'f_state');
    CALL merge_object(@oldJson, @newJson, OLD.f_county, NEW.f_county, 'f_county');
    CALL merge_object(@oldJson, @newJson, OLD.f_pincode, NEW.f_pincode, 'f_pincode');
    CALL merge_object(@oldJson, @newJson, OLD.f_position, NEW.f_position, 'f_position');
    CALL merge_object(@oldJson, @newJson, OLD.f_profession, NEW.f_profession, 'f_profession');
    CALL merge_object(@oldJson, @newJson, OLD.f_qualification, NEW.f_qualification, 'f_qualification');
    CALL merge_object(@oldJson, @newJson, OLD.f_company_name, NEW.f_company_name, 'f_company_name');
    CALL merge_object(@oldJson, @newJson, OLD.f_company_addr, NEW.f_company_addr, 'f_company_addr');
    CALL merge_object(@oldJson, @newJson, OLD.f_company_district, NEW.f_company_district, 'f_company_district');
    CALL merge_object(@oldJson, @newJson, OLD.f_company_state, NEW.f_company_state, 'f_company_state');
    CALL merge_object(@oldJson, @newJson, OLD.f_company_county, NEW.f_company_county, 'f_company_county');
    CALL merge_object(@oldJson, @newJson, OLD.f_company_pincode, NEW.f_company_pincode, 'f_company_pincode');
    CALL merge_object(@oldJson, @newJson, OLD.f_annual_gross_income, NEW.f_annual_gross_income, 'f_annual_gross_income');
    CALL merge_object(@oldJson, @newJson, OLD.f_office_ph, NEW.f_office_ph, 'f_office_ph');
    CALL merge_object(@oldJson, @newJson, OLD.f_res_ph, NEW.f_res_ph, 'f_res_ph');
    CALL merge_object(@oldJson, @newJson, OLD.m_name, NEW.m_name, 'm_name');
    CALL merge_object(@oldJson, @newJson, OLD.m_mobile_no, NEW.m_mobile_no, 'm_mobile_no');
    CALL merge_object(@oldJson, @newJson, OLD.m_email_id, NEW.m_email_id, 'm_email_id');
    CALL merge_object(@oldJson, @newJson, OLD.mother_aadhar, NEW.mother_aadhar, 'mother_aadhar');
    CALL merge_object(@oldJson, @newJson, OLD.m_pan_number, NEW.m_pan_number, 'm_pan_number');
    CALL merge_object(@oldJson, @newJson, OLD.m_addr, NEW.m_addr, 'm_addr');
    CALL merge_object(@oldJson, @newJson, OLD.m_district, NEW.m_district, 'm_district');
    CALL merge_object(@oldJson, @newJson, OLD.m_state, NEW.m_state, 'm_state');
    CALL merge_object(@oldJson, @newJson, OLD.m_county, NEW.m_county, 'm_county');
    CALL merge_object(@oldJson, @newJson, OLD.m_position, NEW.m_position, 'm_position');
    CALL merge_object(@oldJson, @newJson, OLD.m_qualification, NEW.m_qualification, 'm_qualification');
    CALL merge_object(@oldJson, @newJson, OLD.m_profession, NEW.m_profession, 'm_profession');
    CALL merge_object(@oldJson, @newJson, OLD.m_company_name, NEW.m_company_name, 'm_company_name');
    CALL merge_object(@oldJson, @newJson, OLD.m_company_addr, NEW.m_company_addr, 'm_company_addr');
    CALL merge_object(@oldJson, @newJson, OLD.m_company_district, NEW.m_company_district, 'm_company_district');
    CALL merge_object(@oldJson, @newJson, OLD.m_company_state, NEW.m_company_state, 'm_company_state');
    CALL merge_object(@oldJson, @newJson, OLD.m_company_county, NEW.m_company_county, 'm_company_county');
    CALL merge_object(@oldJson, @newJson, OLD.m_company_pincode, NEW.m_company_pincode, 'm_company_pincode');
    CALL merge_object(@oldJson, @newJson, OLD.m_pincode, NEW.m_pincode, 'm_pincode');
    CALL merge_object(@oldJson, @newJson, OLD.m_annual_gross_income, NEW.m_annual_gross_income, 'm_annual_gross_income');
    CALL merge_object(@oldJson, @newJson, OLD.m_office_ph, NEW.m_office_ph, 'm_office_ph');
    CALL merge_object(@oldJson, @newJson, OLD.m_res_ph, NEW.m_res_ph, 'm_res_ph');
    CALL merge_object(@oldJson, @newJson, OLD.std_photo_uri, NEW.std_photo_uri, 'std_photo_uri');
     CALL merge_object(@oldJson, @newJson, OLD.created_on, NEW.created_on, 'created_on');
    CALL merge_object(@oldJson, @newJson, OLD.nationality_other, NEW.nationality_other, 'nationality_other');
    CALL merge_object(@oldJson, @newJson, OLD.religion_other, NEW.religion_other, 'religion_other');
    CALL merge_object(@oldJson, @newJson, OLD.mother_tongue_other, NEW.mother_tongue_other, 'mother_tongue_other');
    CALL merge_object(@oldJson, @newJson, OLD.application_no, NEW.application_no, "application_no");
    CALL merge_object(@oldJson, @newJson, OLD.sibling_student_name, NEW.sibling_student_name, 'sibling_student_name');
    CALL merge_object(@oldJson, @newJson, OLD.sibling_school_name, NEW.sibling_school_name, 'sibling_school_name');
    CALL merge_object(@oldJson, @newJson, OLD.sibling_student_class, NEW.sibling_student_class, 'sibling_student_class');
    CALL merge_object(@oldJson, @newJson, OLD.f_area, NEW.f_area, 'f_area');
    CALL merge_object(@oldJson, @newJson, OLD.m_area, NEW.m_area, 'm_area');
    CALL merge_object(@oldJson, @newJson, OLD.f_company_area, NEW.f_company_area, 'f_company_area');
     CALL merge_object(@oldJson, @newJson, OLD.m_company_area, NEW.m_company_area, 'm_company_area');
    CALL merge_object(@oldJson, @newJson, OLD.instruction_file, NEW.instruction_file, 'instruction_file');
    CALL merge_object(@oldJson, @newJson, OLD.modified_on, NEW.modified_on, 'modified_on');
    CALL merge_object(@oldJson, @newJson, OLD.father_mother_tongue, NEW.father_mother_tongue, 'father_mother_tongue');
    CALL merge_object(@oldJson, @newJson, OLD.father_mother_tongue_other, NEW.father_mother_tongue_other, 'father_mother_tongue_other');
    CALL merge_object(@oldJson, @newJson, OLD.mother_mother_tongue, NEW.mother_mother_tongue, 'mother_mother_tongue');
    CALL merge_object(@oldJson, @newJson, OLD.mother_mother_tongue_other, NEW.mother_mother_tongue_other, 'mother_mother_tongue_other');
    CALL merge_object(@oldJson, @newJson, OLD.student_aadhar, NEW.student_aadhar, 'student_aadhar');
    CALL merge_object(@oldJson, @newJson, OLD.student_email_id, NEW.student_email_id, 'student_email_id');
    CALL merge_object(@oldJson, @newJson, OLD.student_mobile_no, NEW.student_mobile_no, 'student_mobile_no');
    CALL merge_object(@oldJson, @newJson, OLD.student_caste, NEW.student_caste, 'student_caste');
    CALL merge_object(@oldJson, @newJson, OLD.student_sub_caste, NEW.student_sub_caste, 'student_sub_caste');
    CALL merge_object(@oldJson, @newJson, OLD.student_blood_group, NEW.student_blood_group, 'student_blood_group');
    CALL merge_object(@oldJson, @newJson, OLD.g_name, NEW.g_name, 'g_name');
    CALL merge_object(@oldJson, @newJson, OLD.g_mobile_no, NEW.g_mobile_no, 'g_mobile_no');
    CALL merge_object(@oldJson, @newJson, OLD.g_email_id, NEW.g_email_id, 'g_email_id');
    CALL merge_object(@oldJson, @newJson, OLD.guardian_aadhar, NEW.guardian_aadhar, 'guardian_aadhar');
    CALL merge_object(@oldJson, @newJson, OLD.g_addr, NEW.g_addr, 'g_addr');
    CALL merge_object(@oldJson, @newJson, OLD.g_area, NEW.g_area, 'g_area');
    CALL merge_object(@oldJson, @newJson, OLD.g_district, NEW.g_district, 'g_district');
    CALL merge_object(@oldJson, @newJson, OLD.g_state, NEW.g_state, 'g_state');
    CALL merge_object(@oldJson, @newJson, OLD.g_county, NEW.g_county, 'g_county');
    CALL merge_object(@oldJson, @newJson, OLD.g_pincode, NEW.g_pincode, 'g_pincode');
    CALL merge_object(@oldJson, @newJson, OLD.g_office_ph, NEW.g_office_ph, 'g_office_ph');
    CALL merge_object(@oldJson, @newJson, OLD.g_annual_gross_income, NEW.g_annual_gross_income, 'g_annual_gross_income');
    CALL merge_object(@oldJson, @newJson, OLD.g_res_ph, NEW.g_res_ph, 'g_res_ph');
    CALL merge_object(@oldJson, @newJson, OLD.g_position, NEW.g_position, 'g_position');
    CALL merge_object(@oldJson, @newJson, OLD.g_company_name, NEW.g_company_name, 'g_company_name');
    CALL merge_object(@oldJson, @newJson, OLD.g_company_addr, NEW.g_company_addr, 'g_company_addr');
    CALL merge_object(@oldJson, @newJson, OLD.g_company_area, NEW.g_company_area, 'g_company_area');
    CALL merge_object(@oldJson, @newJson, OLD.g_company_district, NEW.g_company_district, 'g_company_district');
    CALL merge_object(@oldJson, @newJson, OLD.g_company_state, NEW.g_company_state, 'g_company_state');
    CALL merge_object(@oldJson, @newJson, OLD.g_company_county, NEW.g_company_county, 'g_company_county');
    CALL merge_object(@oldJson, @newJson, OLD.g_company_pincode, NEW.g_company_pincode, 'g_company_pincode');
    CALL merge_object(@oldJson, @newJson, OLD.guardian_mother_tongue_other, NEW.guardian_mother_tongue_other, 'guardian_mother_tongue_other');
    CALL merge_object(@oldJson, @newJson, OLD.g_profession, NEW.g_profession, 'g_profession');
    CALL merge_object(@oldJson, @newJson, OLD.guardian_mother_tongue, NEW.guardian_mother_tongue, 'guardian_mother_tongue');
    CALL merge_object(@oldJson, @newJson, OLD.g_qualification, NEW.g_qualification, 'g_qualification');
    CALL merge_object(@oldJson, @newJson, OLD.filled_by, NEW.filled_by, 'filled_by');
    CALL merge_object(@oldJson, @newJson, OLD.category, NEW.category, 'category');
    CALL merge_object(@oldJson, @newJson, OLD.enquiry_id, NEW.enquiry_id, 'enquiry_id');
    CALL merge_object(@oldJson, @newJson, OLD.receipt_html_path, NEW.receipt_html_path, "receipt_html_path");
    CALL merge_object(@oldJson, @newJson, OLD.receipt_number, NEW.receipt_number, "receipt_number");
    CALL merge_object(@oldJson, @newJson, OLD.s_present_addr, NEW.s_present_addr, 's_present_addr');
    CALL merge_object(@oldJson, @newJson, OLD.s_present_area, NEW.s_present_area, 's_present_area');
    CALL merge_object(@oldJson, @newJson, OLD.s_present_district, NEW.s_present_district, 's_present_district');
    CALL merge_object(@oldJson, @newJson, OLD.s_present_state, NEW.s_present_state, 's_present_state');
    CALL merge_object(@oldJson, @newJson, OLD.s_present_country, NEW.s_present_country, 's_present_country');
    CALL merge_object(@oldJson, @newJson, OLD.s_present_pincode, NEW.s_present_pincode, 's_present_pincode');
    CALL merge_object(@oldJson, @newJson, OLD.s_permanent_addr, NEW.s_permanent_addr, 's_permanent_addr');
    CALL merge_object(@oldJson, @newJson, OLD.s_permanent_area, NEW.s_permanent_area, 's_permanent_area');
    CALL merge_object(@oldJson, @newJson, OLD.s_permanent_district, NEW.s_permanent_district, 's_permanent_district');
    CALL merge_object(@oldJson, @newJson, OLD.s_permanent_state, NEW.s_permanent_state, 's_permanent_state');
    CALL merge_object(@oldJson, @newJson, OLD.s_permanent_country, NEW.s_permanent_country, 's_permanent_country');
    CALL merge_object(@oldJson, @newJson, OLD.s_permanent_pincode, NEW.s_permanent_pincode, 's_permanent_pincode');
    CALL merge_object(@oldJson, @newJson, OLD.ration_card_number, NEW.ration_card_number, 'ration_card_number');
    CALL merge_object(@oldJson, @newJson, OLD.ration_card_type, NEW.ration_card_type, 'ration_card_type');
    CALL merge_object(@oldJson, @newJson, OLD.caste_income_certificate_number, NEW.caste_income_certificate_number, 'caste_income_certificate_number');
    CALL merge_object(@oldJson, @newJson, OLD.extracurricular_activities, NEW.extracurricular_activities, 'extracurricular_activities');
    CALL merge_object(@oldJson, @newJson, OLD.lang_1_choice, NEW.lang_1_choice, 'lang_1_choice');
    CALL merge_object(@oldJson, @newJson, OLD.lang_2_choice, NEW.lang_2_choice, 'lang_2_choice');
    CALL merge_object(@oldJson, @newJson, OLD.lang_3_choice, NEW.lang_3_choice, 'lang_3_choice');
    CALL merge_object(@oldJson, @newJson, OLD.family_annual_income, NEW.family_annual_income, 'family_annual_income');
    CALL merge_object(@oldJson, @newJson, OLD.student_quota, NEW.student_quota, 'student_quota');
    CALL merge_object(@oldJson, @newJson, OLD.template_pdf_path, NEW.template_pdf_path, "template_pdf_path");
    CALL merge_object(@oldJson, @newJson, OLD.seat_allotment_pdf_path, NEW.seat_allotment_pdf_path, "seat_allotment_pdf_path");
    CALL merge_object(@oldJson, @newJson, OLD.seat_allotment_no, NEW.seat_allotment_no, "seat_allotment_no");
    CALL merge_object(@oldJson, @newJson, OLD.seat_allotment_date, NEW.seat_allotment_date, 'seat_allotment_date');
    CALL merge_object(@oldJson, @newJson, OLD.pdf_status, NEW.pdf_status, 'pdf_status');
    CALL merge_object(@oldJson, @newJson, OLD.sats_number, NEW.sats_number, 'sats_number');
    CALL merge_object(@oldJson, @newJson, OLD.passport_number, NEW.passport_number, 'passport_number');
    CALL merge_object(@oldJson, @newJson, OLD.passport_issued_place, NEW.passport_issued_place, 'passport_issued_place');
    CALL merge_object(@oldJson, @newJson, OLD.passport_expiry_date, NEW.passport_expiry_date, 'passport_expiry_date');
    CALL merge_object(@oldJson, @newJson, OLD.m_signature, NEW.m_signature, 'm_signature');
    CALL merge_object(@oldJson, @newJson, OLD.f_signature, NEW.f_signature, 'f_signature');
    CALL merge_object(@oldJson, @newJson, OLD.g_photo_uri, NEW.g_photo_uri, 'g_photo_uri');
    CALL merge_object(@oldJson, @newJson, OLD.custom_field, NEW.custom_field, 'custom_field');
    CALL merge_object(@oldJson, @newJson, OLD.medical_concerns, NEW.medical_concerns, 'medical_concerns');
    CALL merge_object(@oldJson, @newJson, OLD.assigned_to, NEW.assigned_to, 'assigned_to');
    CALL merge_object(@oldJson, @newJson, OLD.has_sibling, NEW.has_sibling, 'has_sibling');
    CALL merge_object(@oldJson, @newJson, OLD.sibling_inschool_other, NEW.sibling_inschool_other, 'sibling_inschool_other');
    CALL merge_object(@oldJson, @newJson, OLD.f_nationality, NEW.f_nationality, 'f_nationality');
    CALL merge_object(@oldJson, @newJson, OLD.m_nationality, NEW.m_nationality, 'm_nationality');
    CALL merge_object(@oldJson, @newJson, OLD.s_country_code, NEW.s_country_code, 's_country_code');
    CALL merge_object(@oldJson, @newJson, OLD.f_country_code, NEW.f_country_code, 'f_country_code');
    CALL merge_object(@oldJson, @newJson, OLD.m_country_code, NEW.m_country_code, 'm_country_code');
    CALL merge_object(@oldJson, @newJson, OLD.g_country_code, NEW.g_country_code, 'g_country_code');
    CALL merge_object(@oldJson, @newJson, OLD.physically_challenged_discription, NEW.physically_challenged_discription, 'physically_challenged_discription');
    CALL merge_object(@oldJson, @newJson, OLD.know_about_us, NEW.know_about_us, 'know_about_us');
    CALL merge_object(@oldJson, @newJson, OLD.father_photo, NEW.father_photo, 'father_photo');
    CALL merge_object(@oldJson, @newJson, OLD.mother_photo, NEW.mother_photo, 'mother_photo');
    CALL merge_object(@oldJson, @newJson, OLD.emergency_contact, NEW.emergency_contact, 'emergency_contact');
    CALL merge_object(@oldJson, @newJson, OLD.father_interest, NEW.father_interest, 'father_interest');
    CALL merge_object(@oldJson, @newJson, OLD.mother_interest, NEW.mother_interest, 'mother_interest');
    CALL merge_object(@oldJson, @newJson, OLD.family_photo, NEW.family_photo, 'family_photo');
    CALL merge_object(@oldJson, @newJson, OLD.std_photo_uri_resize, NEW.std_photo_uri_resize, "std_photo_uri_resize");
    CALL merge_object(@oldJson, @newJson, OLD.is_ready_to_take_proficiency_test, NEW.is_ready_to_take_proficiency_test, 'is_ready_to_take_proficiency_test');
    CALL merge_object(@oldJson, @newJson, OLD.f_dob, NEW.f_dob, 'f_dob');
    CALL merge_object(@oldJson, @newJson, OLD.m_dob, NEW.m_dob, 'm_dob');
    
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'admission_forms',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        NEW.last_modified_by
    );
    END IF;
END;
$$
DELIMITER ;