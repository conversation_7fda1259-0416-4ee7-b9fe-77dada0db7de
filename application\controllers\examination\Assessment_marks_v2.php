<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Assessment_marks_v2 extends CI_Controller {

  private $yearId;
	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isAuthorized('EXAMINATION.MODULE')) {
      redirect('dashboard', 'refresh');
    } 
    $this->yearId = $this->acad_year->getAcadYearId();
    $this->load->library('filemanager');
    $this->load->model('examination/Assessment_model','assessment_model');
    $this->load->model('examination/Assessment_marks_model','assessment_marks');
  }

  public function index($class_id=1) {
    $class = $this->input->post('classSectionId');
    if(!empty($class)) {
      $class_id = $class;
    }
    if($class == 0 && $class != '') {
      $class_id = $class;
    }
    $data['classSelected'] = $class_id;
    $data['is_exam_admin'] = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $staffId = $this->authorization->getAvatarStakeHolderId();
    // $data['classList'] = $this->assessment_model->getClassess();
    $data['classList'] = $this->assessment_marks->getClassess($data['is_exam_admin'], $staffId);
    if($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'examination/marks_v2/assessment_marks_mobile';
    } else {
      $data['main_content'] = 'examination/marks_v2/assessment_marks';
    }
    $this->load->view('inc/template', $data);
  }

  public function getAssessmentDataForMarksEntry() {
    $classId = $_POST['classId'];
    $selectedRadio = $_POST['selectedRadio'];
    $show_derived = $this->authorization->isSuperAdmin(); //Always show derived assessments for Admin!
    // $data['assessments'] = $this->assessment_model->getAssessments($classId, $show_derived);
    $data['assessments'] = $this->assessment_model->getAssessmentsGenerationTypeWise($classId, $selectedRadio);
    $staff = $this->staffcache->getStaffCache();
    $staffId = 0;
    if(!empty($staff)) $staffId = $staff->staffId;
    $showAll = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    if($showAll) {
      $staffId = 0;
    }
    $permitted = $this->assessment_marks->permittedAssessments($classId, $staffId);
    $assessments = array();
    foreach ($data['assessments'] as $key => $assessment) {
      if(in_array($assessment->id, $permitted)) {
        $sub = $this->assessment_model->getsubAdded($assessment->id);
        $assessments[] = array(
          'id' => $assessment->id,
          'short_name' => $assessment->short_name,
          'long_name' => $assessment->long_name,
          'ass_type' => $assessment->ass_type,
          'subAdded' => $sub,
          'enable_subject_remarks' => $assessment->enable_subject_remarks,
          'add_marks_enability' => $assessment->add_marks_enability,
          'generation_type' => $assessment->generation_type
        );
      }
    }
    echo json_encode($assessments);
  }

  public function get_portions() {
    $assessment_id = $_POST['assessment_id'];
    $portion = $this->assessment_model->getPortion($assessment_id);
    echo json_encode($portion);
  }

  public function marks_entry_subjects($assessment_id, $class_id, $section_id = '') {
    $data['selected_section_id'] = $section_id; 
    $data['class_id'] = $class_id;
    $data['assessment_id'] = $assessment_id;
    $data['is_exam_admin'] = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $data['permit_for_unlock'] = $this->authorization->isAuthorized('EXAMINATION.UNLOCK_MARKS_ENTRY');
    $data['sections'] = $this->assessment_marks->getPermittedSections($assessment_id, $data['is_exam_admin']);
    $data['max_entity'] = 5;
    $max_entity = $this->settings->getSetting('marks_entry_components_count');
    if($max_entity) {
      $data['max_entity'] = $max_entity;
    }
    $data['assessment'] = $this->assessment_model->getAssessmentName($assessment_id);

    $data['main_content'] = 'examination/marks_v2/marks_entry';
    $this->load->view('inc/template', $data);
  }

  public function subject_remarks_entry_asssubjects_view($assessment_id, $class_id, $section_id = '') {
    $data['selected_section_id'] = $section_id; 
    $data['class_id'] = $class_id;
    $data['assessment_id'] = $assessment_id;
    $data['is_exam_admin'] = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $data['sections'] = $this->assessment_marks->getPermittedSections($assessment_id, $data['is_exam_admin']);
    $data['max_entity'] = 5;
    $data['permit_unlock'] = $this->authorization->isAuthorized('EXAMINATION.UNLOCK_MARKS_ENTRY') || $data['is_exam_admin'];
    $max_entity = $this->settings->getSetting('marks_entry_components_count');
    if($max_entity) {
      $data['max_entity'] = $max_entity;
    }
    $data['assessment'] = $this->assessment_model->getAssessmentName($assessment_id);

    $data['main_content'] = 'examination/marks_v2/remarks_entry_asssubjects_view';
    $this->load->view('inc/template', $data);
  }

  //Marks entry fro Desktop
  public function add_subject_marks() {
    $input = $this->input->post();

    if(!isset($input['assessment_id']) || !isset($input['section_id']) || !isset($input['selected_entities']) ) {
      $this->session->set_flashdata('flashError', 'Something went wrong. Please go to marks entry and try again');
      redirect('examination/Assessments/index'); 
    }
    if($input['assessment_id'] <= 0 || $input['assessment_id'] == '' || $input['section_id'] <= 0 || $input['section_id'] == '' || empty($input['selected_entities'])) {
      $this->session->set_flashdata('flashError', 'Something went wrong. Please go to marks entry and try again');
      redirect('examination/Assessments/index');
    }
    //Store metadata of multiple required data
    $data['selected_entities'] = $input['selected_entities'];
    $data['is_exam_admin'] = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $data['assessment'] = $this->assessment_model->getAssessmentName($input['assessment_id']);
    $data['section'] = $this->assessment_marks->getSectionDetails($input['section_id']);
    $data['staffList'] = $this->assessment_model->getStaff();

    if(empty($data['assessment']) || empty($data['section'])) {
      $this->session->set_flashdata('flashError', 'Something went wrong. Please go to marks entry and try again');
      redirect('examination/Assessments/index');
    }

    // echo '<pre>';print_r($data);die();

    //Get selected Entities meta data
    $entities = $this->assessment_marks->getMarksEntities($input['assessment_id'], $input['selected_entities'], $input['section_id'], $data['is_exam_admin']);
    if (!empty($entities)) {

      //dedupe and marshall entities (required only for exam_admin)
      $temp_entities = [];
      foreach ($entities as $ent) {
        $temp_entities[$ent->aeId] = $ent;
      }
      $entities = $temp_entities;

      $aeIds = array();
      foreach ($entities as $key => $entity) {
        $aeIds[] = $entity->aeId;
        $entity->grades = json_decode($entity->grades);
        $data['entities'][$entity->aeId] = $entity;
      }
      // echo '<pre>';print_r($data['entities']);die();
      $list = $this->assessment_marks->getStudentMarksData($input['selected_entities'], $input['section_id'], $aeIds);
      $data['students'] = $list['list'];
      $data['isFreshEntry'] = $list['isFreshEntry'];
    }
    // echo '<pre>';print_r($data);die();

    if($input['save_type'] == 'manual') {
      $data['main_content'] = 'examination/marks_v2/add_marks';
    } else { 
      $data['main_content'] = 'examination/marks_v2/add_marks_auto_save_page';
    }
    $this->load->view('inc/template', $data);
  }

  public function onblur_auto_save() {
    echo json_encode($this->assessment_marks->onblur_auto_save());
  }

  public function upload_students_marks_csv(){
    $file_path = $_FILES['upload_csv']['tmp_name'];
    
    $csv_marks_arr = [];
    $this->load->library('csvimport');
    if ($this->csvimport->get_array($file_path)) {
      $csv_marks_arr = $this->csvimport->get_array($file_path);
      // echo "<pre>"; print_r($csv_marks_arr); die();
    }

    
    $csv_marks=[];
    if(!empty($csv_marks_arr)) { //[JSPUC] | [2025-01-28 10:34:25] | unknown | [error_statistics_string] | Undefined index: Student (/home/<USER>/oxygenv2/application/controllers/examination/Assessment_marks_v2.php:189) | (Notice:8) | 10.11.141.103:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36 
      foreach($csv_marks_arr as $key => $value) {
        if(array_key_exists('Student', $value)) { // [JSPUC] | [2025-01-28 10:34:25] | unknown | [error_statistics_string] | Undefined index: Student (/home/<USER>/oxygenv2/application/controllers/examination/Assessment_marks_v2.php:189) | (Notice:8) | 10.11.141.103:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36 
          $csv_marks[$value["Student"]]["name"] = $value["Student"];
          $csv_marks[$value["Student"]]['subjects'][] = $value;
        }
      }
    }
    // 
    echo json_encode($csv_marks);
  }

  public function upload_students_remarks_csv(){
    $file_path = $_FILES['upload_csv']['tmp_name'];
    
    $csv_marks_arr = [];
    $this->load->library('csvimport');
    if ($this->csvimport->get_array($file_path)) {
      $csv_marks_arr = $this->csvimport->get_array($file_path);
    }
    
    $csv_marks = [];
    foreach ($csv_marks_arr as $key => $value) {
        
        $studentName = $value["Student"];
    
        // Check if the student is already in $csv_marks; if not, initialize it
        if (!array_key_exists($studentName, $csv_marks)) {
            $csv_marks[$studentName] = [
                "Student" => $studentName,
                "subject_remarks" => []
            ];
        }
        
        // Remove unnecessary keys
        unset($value["#"]);
        unset($value["Student"]);
    
        // Add the remaining subjects and remarks to the student's subject_remarks array
        $csv_marks[$studentName]["subject_remarks"] = $value;
    }
    echo json_encode($csv_marks);
  }

  //For Desktop Save marks
  public function save_subject_marks() {
    $input = $this->input->post();
    // echo '<pre>'; print_r($input); die();
    $status = $this->assessment_marks->insertMarksV2($is_refresh_derived_subject_required=0);
    $this->session->set_flashdata('flashSuccess', 'Marks added successfully');
    $this->session->set_userdata('subject_id', $input["subject_id"]);
    redirect('examination/assessment_marks_v2/marks_entry_subjects/'.$input["ass_id"].'/'.$input["class_id"]);
  }

  public function subject_remarks_entry() {
    $data = $this->input->post();
    $data['subject_id'] = explode(",", $data['subject_id']);
    $data['group'] = $this->assessment_marks->getGroupData($data['subject_id']);
    $data['assessment'] = $this->assessment_model->getAssessmentName($data['assessment_id']);
    $data['section'] = $this->assessment_marks->getSectionDetails($data['section_id']);
    $data['students'] = $this->assessment_marks->getSectionStudents($data['section_id']);
    $subject_remarks = $this->assessment_marks->getSubjectRemarks($data['assessment_id'], $data['subject_id']);
    // echo "<pre>"; print_r($subject_remarks);
    foreach ($data['students'] as $key => $std) {
      $data['students'][$key]->remarks = array();
      foreach ($data['subject_id'] as $subId) {
        $data['students'][$key]->remarks[$subId] = '';
        // $data['students'][$key]->subject_remarks = '';
        if(array_key_exists($std->student_id.'_'.$subId, $subject_remarks)) {
          $data['students'][$key]->remarks[$subId] = $subject_remarks[$std->student_id.'_'.$subId];
        }
      }
    }

    $data['remarks_list'] = array();
    if($data['assessment']->remarks_group_id) {
      $data['remarks_list'] = $this->assessment_marks->getRemarksList($data['assessment']->remarks_group_id);
    }
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'examination/marks_v2/subject_remarks';
    $this->load->view('inc/template', $data);
  }
  

  public function get_permitted_subjects_for_remarks() {
    $assessment_id = $_POST['assessment_id'];
    $section_id = $_POST['section_id'];
    $groups = $this->assessment_marks->get_permitted_subjects_for_remarks($assessment_id, $section_id);
    // echo '<pre>';print_r($groups);die();
    echo json_encode($groups);
  }

  public function save_subject_remarks() {
    $data = $this->input->post();
    // echo "<pre>"; print_r($data); die();
    $status = $this->assessment_marks->saveRemarks();
    $this->session->set_userdata('subject_id', $data["subject_id"]);
    redirect('examination/assessment_marks_v2/subject_remarks_entry_asssubjects_view/'.$data['assessment_id'].'/'.$data['class_id']);
  }

  public function get_permitted_subjects() {
    $is_exam_admin = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $assessment_id = $_POST['assessment_id'];
    $section_id = $_POST['section_id'];
    $groups = $this->assessment_marks->get_permitted_subjects($assessment_id, $is_exam_admin, $section_id);
    // echo '<pre>';print_r($groups);die();
    echo json_encode($groups);
  }

  public function get_student_marks_grade() {
    $input = $_POST;
    $entity_ids[] = $input['entity_id'];
    $is_exam_admin = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $entities = $this->assessment_marks->getMarksEntities($input['assessment_id'], $entity_ids, $input['section_id'], $is_exam_admin);

    $aeIds = array();
    foreach ($entities as $key => $entity) {
      $aeIds[] = $entity->aeId;
      $entity->formula = '';
      $entity->grades = json_decode($entity->grades);
      // echo '<pre>';print_r($entity);die();
      if($entity->ass_type == 'Derived') {
        //Sometimes, derived formula may not exist if the component is switched from 'Manual' to 'Derived'.
        if ($entity->derived_formula != '') {
          $derived = json_decode($entity->derived_formula);
          $entity->formula = $derived->formula->name;
        }
      }
      unset($entity->derived_formula);
      $data['entities'][$entity->aeId] = $entity;
    }
    $list = $this->assessment_marks->getStudentMarksData([$input['group_id']], $input['section_id'], $aeIds);
    $data['students'] = $list['list'];
    echo json_encode($data);
    // echo "<pre>"; print_r($data); die();
  }

  public function get_student_marks_grade_for_derived_entity() {
    //Step1: Get the entity object of the given derived subject and get all the related entities
    $entity_ids = $this->assessment_marks->get_related_entities_of_derived_entity($_POST['entity_id']);

    $is_exam_admin = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');

    //Step2: Get the objects for all the related entities
    $entities = $this->assessment_marks->getMarksEntities($_POST['assessment_id'], $entity_ids, $_POST['section_id'], $is_exam_admin);
    $aeIds = array();
    foreach ($entities as $key => $entity) {
      $aeIds[] = $entity->aeId;
      $entity->formula = '';
      $entity->grades = json_decode($entity->grades);
      if($entity->ass_type == 'Derived') {
        $derived = json_decode($entity->derived_formula);
        $entity->formula = $derived->formula->name;
      }
      unset($entity->derived_formula);
      $data['entities'][$entity->aeId] = $entity;
    }

    //Step3: Get the students data
    $list = $this->assessment_marks->getStudentMarksData([$_POST['group_id']], $_POST['section_id'], $aeIds);
    $data['students'] = $list['list'];

    echo json_encode($data);
  }

  public function generate_derived_entity_marks() {
    // echo '<pre>';print_r($_POST);die();
    echo $this->assessment_marks->generate_derived_entity_marks($_POST['section_id'], $_POST['entity_id'], $_POST['assessment_id'], $_POST['derived_ae_id']);
  }
  
  public function generate_derived_entity_marks_without_derivedae() {
    $assessment_id = $_POST['assessment_id'];
    $section_id = $_POST['section_id'];
    $entity_id = $_POST['entity_id'];
    
    $data['status'] = $this->assessment_marks->generate_derived_entity_marks_without_derivedae($section_id, $entity_id, $assessment_id);
    echo json_encode($data);
  }

  public function generate_ranks() {
    // echo '<pre>';print_r($_POST);die();
    echo $this->assessment_marks->generate_ranks($_POST['section_id'], $_POST['entity_id'], $_POST['assessment_id'], $_POST['ae_id']);
  }

  //Marks entry for Mobile
  public function marks_entry($assessment_id, $class_id, $subject_id=0, $section_id=0) {
    $data['class_id'] = $class_id;
    $data['assessment_id'] = $assessment_id;
    $data['subject_id'] = $subject_id;
    $data['section_id'] = $section_id;
    $current_section = $section_id;
    if(isset($_POST['subject_id'])) {
      $data['subject_id'] = $_POST['subject_id'];
    } else {
      $data['subject_id'] = $this->session->userdata('subject_id');
      $this->session->unset_userdata('subject_id');
    }
    if($data['subject_id'] == null || $data['subject_id'] == 0) {
      $data['subject_id'] = [];
    }
    if(isset($_POST['section_id'])) {
      $data['section_id'] = $_POST['section_id'];
      $current_section = $_POST['section_id'];
    }
    $data['assessment'] = $this->assessment_model->getAssessmentName($assessment_id);
    $data['is_exam_admin'] = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $data['sections'] = $this->assessment_marks->getPermittedSections($assessment_id, $data['is_exam_admin']);
    if($current_section == 0) {
      $current_section = (empty($data['sections']))?0:$data['sections'][0]->id;
    }
    $data['groups'] = $this->assessment_marks->getPermittedSubjects($assessment_id, $data['is_exam_admin'], $current_section);
    $data['com_count'] = 5;
    $com_count = $this->settings->getSetting('marks_entry_components_count');
    if($com_count) {
      $data['com_count'] = $com_count;
    }
    $data['class'] = $this->assessment_marks->getClassData($class_id);
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'examination/marks_v2/marks_entry_mobile';
    $this->load->view('inc/template', $data);
  }

  public function getPermittedGroups() {
    $is_exam_admin = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $assessment_id = $_POST['assessment_id'];
    $section_id = $_POST['section_id'];
    $groups = $this->assessment_marks->getPermittedSubjects($assessment_id, $is_exam_admin, $section_id);
    echo json_encode($groups);
  }

  public function getPermittedEntities() {
    $group_id = $_POST['group_id'];
    $assessment_id = $_POST['assessment_id'];
    $section_id = $_POST['section_id'];
    $is_exam_admin = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $data['entities'] = $this->assessment_marks->getPermittedEntitiesList($assessment_id, $section_id, $group_id, $is_exam_admin);
    $elective_group_id = 0;
    $data['status'] = $this->assessment_marks->getMarksEntryStatus($assessment_id, [$group_id], $section_id);
    $data['remarks_status'] = $this->assessment_marks->getRemarksEntryStatus($assessment_id, [$group_id], $elective_group_id, $section_id);
    echo json_encode($data);
  }

  public function unlockEntityMarksEntry() {
    $entities_id = $_POST['entities_id'];
    echo ($this->assessment_marks->unlockEntityMarksEntry($entities_id));
  }

  //Marks Entry for Mobile
  public function add_marks() {
    $input = $this->input->post();
    if(!$this->mobile_detect->isMobile()) {
      //Is this the right link??
      redirect('examination/assessment_marks_v2/marks_entry/'.$input['assessment_id'].'/'.$input['class_id']);
    }

    
    $data['is_exam_admin'] = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $data['assessment'] = $this->assessment_model->getAssessmentName($input['assessment_id']);
    $data['group'] = $this->assessment_marks->getGroupData($input['group_id']);
    $entities = $this->assessment_marks->getMarksEntities($input['assessment_id'], $input['entities'], $input['section_id'], $data['is_exam_admin']);
    $data['section'] = $this->assessment_marks->getSectionDetails($input['section_id']);
    $data['staffList'] = $this->assessment_model->getStaff();
    $data['group_id'] = isset($input['group_id']) ? $input['group_id'] : 0;
    $aeIds = array();
    foreach ($entities as $key => $entity) {
      $aeIds[] = $entity->aeId;
      $entity->grades = json_decode($entity->grades);
      $data['entities'][$entity->aeId] = $entity;
    }
    $list = $this->assessment_marks->getStudentMarksData([$data['group_id']], $input['section_id'], $aeIds);
    // echo "<pre>"; print_r($data); die();
    
    $data['students'] = $list['list'];
    $data['isFreshEntry'] = $list['isFreshEntry'];
    if($input['save_type'] == 'manual') {
      $data['main_content'] = 'examination/marks_v2/add_marks_mobile';
    } else {
      $data['main_content'] = 'examination/marks_v2/add_marks_mobile_auto_save';
    }
    $this->load->view('inc/template', $data);
  }

  public function add_subject_remarks() {
    $data = $this->input->post();
    $data['subject_id'] = explode(",", $data['subject_id']);
    $data['group'] = $this->assessment_marks->getGroupData($data['subject_id']);
    $data['assessment'] = $this->assessment_model->getAssessmentName($data['assessment_id']);
    $data['section'] = $this->assessment_marks->getSectionDetails($data['section_id']);
    $data['students'] = $this->assessment_marks->getSectionStudents($data['section_id']);
    $subject_remarks = $this->assessment_marks->getSubjectRemarks($data['assessment_id'], $data['subject_id']);
    // echo "<pre>"; print_r($subject_remarks);
    foreach ($data['students'] as $key => $std) {
      $data['students'][$key]->remarks = array();
      foreach ($data['subject_id'] as $subId) {
        $data['students'][$key]->remarks[$subId] = '';
        // $data['students'][$key]->subject_remarks = '';
        if(array_key_exists($std->student_id.'_'.$subId, $subject_remarks)) {
          $data['students'][$key]->remarks[$subId] = $subject_remarks[$std->student_id.'_'.$subId];
        }
      }
    }

    $data['remarks_list'] = array();
    if($data['assessment']->remarks_group_id) {
      $data['remarks_list'] = $this->assessment_marks->getRemarksList($data['assessment']->remarks_group_id);
    }
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'examination/marks_v2/subject_remarks_mobile';
    $this->load->view('inc/template', $data);
  }

  public function insert_marks() {
    $input = $this->input->post();
    // echo '<pre>'; print_r($input); die();
    $status = $this->assessment_marks->insertMarksV2();
    $this->session->set_flashdata('flashSuccess', 'Marks added successfully');

    // Solution for: [VSIPS] | [2024-08-14 07:31:29] | unknown | Undefined index: ass_id (/home/<USER>/oxygenv2/application/controllers/examination/Assessment_marks_v2.php:444) | (Notice:8) | *************:Mozilla/5.0 (Linux; Android 10; SM-A305F Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.186 Mobile Safari/537.36
    if(!isset($input["ass_id"])) {
      $this->session->set_flashdata('flashError', 'Something went wrong. Please go to marks entry and try again');
      redirect('examination/assessments');      
    }
    if(!isset($input["class_id"])) {
      $this->session->set_flashdata('flashError', 'Something went wrong. Please go to marks entry and try again');
      redirect('examination/assessments');      
    }
    if(!isset($input["section_id"])) {
      $this->session->set_flashdata('flashError', 'Something went wrong. Please go to marks entry and try again');
      redirect('examination/assessments');      
    }
    // if(!isset($input["subject_id"])) {
    //   $this->session->set_flashdata('flashError', 'Something went wrong. Please go to marks entry and try again');
    //   redirect('examination/assessments');      
    // }

    $sub= isset($input["subject_id"]) ? $input["subject_id"] : 0;
    $this->session->set_userdata('subject_id', $sub);
    redirect('examination/assessment_marks_v2/marks_entry/'.$input["ass_id"].'/'.$input["class_id"].'/0/'.$input["section_id"]);
  }

  public function unlockMarksStatus() {
    $aeIds = $_POST['aeIds'];
    echo ($this->assessment_marks->unlockMarksStatus($aeIds));
  }

  public function lockUnlockMarksStatusForSection() {
    $ae_id = $_POST['ae_id'];
    $section_id = $_POST['section_id'];
    $lock_status = $_POST['lock_status'];
    echo ($this->assessment_marks->lockUnlockMarksStatusForSection($ae_id, $section_id, $lock_status));
  }  

  public function saveRemarks() {
    $data = $this->input->post();
    // echo "<pre>"; print_r($data); die();
    $status = $this->assessment_marks->saveRemarks();
    $this->session->set_userdata('subject_id', $data["subject_id"]);
    redirect('examination/assessment_marks_v2/marks_entry/'.$data['assessment_id'].'/'.$data['class_id'].'/0/'.$data['section_id']);
  }

  public function generate_average_high_rank_marks() {
    $assessment_id = $_POST['assessment_id'];
    $section_id = $_POST['section_id'];
    $entity_id = $_POST['entity_id'];
    $class_id = $_POST['class_id'];

    
    $status= $this->assessment_marks->generate_average_high_rank_marks($section_id, $entity_id, $assessment_id, $class_id);
    // echo '<pre>'; print_r($data['status']); die();
    echo json_encode($status);
  }

  public function checkIfTotalMarksIsAdded() {
    echo json_encode( $this->assessment_marks->checkIfTotalMarksIsAdded() );
  }

  public function add_update_total_marks() {
    echo json_encode( $this->assessment_marks->add_update_total_marks() );
  }

  public function generate_average_high_rank_marks_version_2() {
    $status= $this->assessment_marks->generate_average_high_rank_marks_version_2();
    // echo '<pre>'; print_r($data['status']); die();
    echo json_encode($status);
  }

  function get_student_marks_grade_for_derived_entity_v2() {
    //Step1: Get the entity object of the given derived subject and get all the related entities
    $entity_ids = $this->assessment_marks->get_related_entities_of_derived_entity($_POST['entity_id']);

    $is_exam_admin = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');

    //Step2: Get the objects for all the related entities
    $entities = $this->assessment_marks->getMarksEntities($_POST['assessment_id'], $entity_ids, $_POST['section_id'], $is_exam_admin);
    $aeIds = array();
    foreach ($entities as $key => $entity) {
      $aeIds[] = $entity->aeId;
      $entity->formula = '';
      $entity->grades = json_decode($entity->grades);
      if($entity->ass_type == 'Derived') {
        $derived = json_decode($entity->derived_formula);
        $entity->formula = $derived->formula->name;
      }
      unset($entity->derived_formula);
      $data['entities'][$entity->aeId] = $entity;
    }

    //Step3: Get the students data
    $list = $this->assessment_marks->getStudentMarksData([$_POST['group_id']], $_POST['section_id'], $aeIds);
    $data['students'] = $list['list'];

    echo json_encode($data);
  }

}

?>