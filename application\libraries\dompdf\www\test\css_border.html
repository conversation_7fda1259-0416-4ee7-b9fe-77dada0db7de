<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
<style>
table {
  border-spacing: 5px;
  margin: 2em auto;
}
td {
  border-width: 5px;
  padding: 1em;
}

td.dotted { border-style: dotted; color: maroon;}
td.dashed { border-style: dashed; color: orange; }
td.solid  { border-style: solid; color: yellow; }
td.double { border-style: double; color: olive; }
td.groove { border-style: groove; color: green; }
td.ridge  { border-style: ridge; color: lime; }
td.inset  { border-style: inset; color: teal; }
td.outset { border-style: outset; color: blue; }

div.full { border:thin blue solid; margin:2pt;}

div.partialthinsolid,
div.partial1,
div.partial2 { border:thin solid; margin:2pt;}

div.partial1 { border-color:green; }
div.partial2 { border:blue;}

div.partial3 { border-style:dashed; }
div.partial4 { border-width:thick; }
div.partial5 { border-width:medium; }
div.partial6 { border-width:3pt; }

div.partial1top,
div.partial2top { border-top:thin solid; margin:2pt;}

div.partial1top {border-top-color:green; }
div.partial2top {border-top:blue;}

.d { width: 1in; height: 1in; margin: 5pt; }

.b1 {
  border: 1px solid red;
}

.b2 {
  border: solid thick blue;
}

.b3 {
  border: #369 thin dashed;
}

.b4 {
  border: dotted green 2px;
}

.b5 {
  border: 0.1em #0033DD solid;
}

.b6 {
  border: orange inset 4pt;
}

hr {
	page-break-after: always;
	visibility: hidden;
}

</style>
</head>

<body>
<table>
  <tr>
    <td class="dotted">dotted</td>
    <td class="dashed">dashed</td>
    <td class="solid">solid</td>
    <td class="double">double</td>
  </tr>
  <tr>
    <td class="groove">groove</td>
    <td class="ridge">ridge</td>
    <td class="inset">inset</td>
    <td class="outset">outset</td>
  </tr>
</table>

<h2>partial attributes merged</h2>

<div style="border:thin solid red;">border:thin solid red;</div>
<div style="border:red thin solid;">border:red thin solid;</div>
<div class="partialthinsolid partial2">{ border:thin solid; }{border:blue; } (merged, reset all - color has no effect)</div>
<div class="partialthinsolid partial1">{ border:thin solid; }{border-color:green; } (merged, overwrite only color)</div>
<div class="partial2">{ border:thin solid; }{border:blue; } (merged, reset all - color has no effect)</div>
<div class="partial1">{ border:thin solid; }{border-color:green; } (merged, overwrite only color)</div>
<div class="full partial1">{ border:thin blue solid; }{border-color:green; } (merged, overwrite only color)</div>
<div class="full partial3">{ border:thin blue solid; }{border-style:dashed; } (merged, overwrite only style)</div>
<div class="full partial4">{ border:thin blue solid; }{border-width:thick; } (merged, overwrite only width)</div>
<div class="full partial5">{ border:thin blue solid; }{border-width:medium; } (merged, overwrite only width)</div>
<div class="full partial6">{ border:thin blue solid; }{border-width:3pt; } (merged, overwrite only width)</div>

<h2>top:</h2>

<div style="border-top:thin solid red;">border-top:thin solid red;</div>
<div style="border-top:red thin solid;">border-top:red thin solid;</div>
<div class="partial1top">{ border-top:thin solid; }{border-top-color:green; } (merged, overwrite only color)</div>
<div class="partial2top">{ border-top:thin solid; }{border-top:blue; } (merged, reset all - color has no effect)</div>

<h2>right left bottom:</h2>

<div style="border-right:thin solid red;">border-right:thin solid red;</div>
<div style="border-left:thin solid red;">border-left:thin solid red;</div>
<div style="border-bottom:thin solid red;">border-bottom:thin solid red;</div>

<h2>Individual Attributes</h2>
<div class="full" style="border-top-color:red;">{border:thin blue solid;}{border-top-color:red;}</div>
<div class="full" style="border-right-color:red;">{border:thin blue solid;}{border-right-color:red;}</div>
<div class="full" style="border-bottom-color:red;">{border:thin blue solid;}{border-bottom-color:red;}</div>
<div class="full" style="border-left-color:red;">{border:thin blue solid;}{border-left-color:red;}</div>

<div class="full" style="border-top-style:dashed;">{border:thin blue solid;}{border-top-style:dashed;}</div>
<div class="full" style="border-right-style:dashed;">{border:thin blue solid;}{border-right-style:dashed;}</div>
<div class="full" style="border-bottom-style:dashed;">{border:thin blue solid;}{border-bottom-style:dashed;}</div>
<div class="full" style="border-left-style:dashed;">{border:thin blue solid;}{border-left-style:dashed;}</div>

<div class="full" style="border-top-width:medium;">{border:thin blue solid;}{border-top-width:medium;}</div>
<div class="full" style="border-right-width:medium;">{border:thin blue solid;}{border-right-width:medium;}</div>
<div class="full" style="border-bottom-width:medium;">{border:thin blue solid;}{border-bottom-width:medium;}</div>
<div class="full" style="border-left-width:medium;">{border:thin blue solid;}{border-left-width:medium;}</div>

<h2>Individual side specific Attributes</h2>

<div class="full" style="border-color:red;">{border:thin blue solid;}{border-color:red;}</div>
<div class="full" style="border-color:red green;">{border:thin blue solid;}{border-color:red green;}</div>
<div class="full" style="border-color:red green blue;">{border:thin blue solid;}{border-color:red green blue;}</div>
<div class="full" style="border-color:red green blue gray;">{border:thin blue solid;}{border-color:red green blue gray;}</div>

<div class="full" style="border-style:dashed;">{border:thin blue solid;}{border-style:dashed;}</div>
<div class="full" style="border-style:dashed dotted;">{border:thin blue solid;}{border-style:dashed dotted;}</div>
<div class="full" style="border-style:dashed dotted double;">{border:thin blue solid;}{border-style:dashed dotted double;}</div>
<div class="full" style="border-style:dashed dotted double groove;">{border:thin blue solid;}{border-style:dashed dotted double groove;}</div>

<div class="full" style="border-width:1pt;">{border:thin blue solid;}{border-width:1pt;}</div>
<div class="full" style="border-width:1pt 2pt;">{border:thin blue solid;}{border-width:1pt 2pt;}</div>
<div class="full" style="border-width:1pt 2pt 3pt;">{border:thin blue solid;}{border-width:1pt 2pt 3pt;}</div>
<div class="full" style="border-width:1pt 2pt 3pt 4pt;">{border:thin blue solid;}{border-width:1pt 2pt 3pt 4pt;}</div>

<hr />
<h2>Misc. values</h2>

<div class="d b1"> </div>
<div class="d b2"> </div>
<div class="d b3"> </div>
<div class="d b4"> </div>
<div class="d b5"> </div>
<div class="d b6"> </div>
</body> </html>
