<?php

require APPPATH . 'libraries/REST_Controller.php';

class Timetable extends REST_Controller
{
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
            $data = ['error' => 'User not logged in'];
            $this->response($data, REST_Controller::HTTP_UNAUTHORIZED);
			$this->output->_display();
			exit;
		}
		$this->load->model('parentv2/Timetable_model');
	}

	public function student_Weektimetable_get() {
        $section_id = $this->get('section_id');
		$data['sectionTT'] = $this->Timetable_model->getTTWithStaff($section_id);
		$data['headerTT'] = $this->Timetable_model->getPeriodHeader($section_id);
		$data['wdCount'] = $this->Timetable_model->getNumberOfWeekDays($section_id);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

}
?>