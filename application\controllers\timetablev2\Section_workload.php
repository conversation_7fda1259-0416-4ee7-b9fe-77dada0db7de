<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Section_workload extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('timetablev2/section_workload_model','section_workload_model');
    $this->load->model('timetablev2/template_model','template_model');
  }

  //Landing function to show the timetable menu
  public function index($temp_id) {
    $data['template_obj'] = $this->template_model->get_template_by_id($temp_id);
    //echo "<pre>"; print_r("c_entry");die();
    $data['main_content']    = 'timetablev2/section_workload/section_workload.php';
    $this->load->view('inc/template', $data);
  }

  public function get_section_workload(){
    $temp_id = $_POST['temp_id'];
    //echo "<pre>"; print_r("c_entry");
    $subWorkload = $this->section_workload_model->get_workload_staff_ttsectionwise($temp_id);
    //echo "<pre>"; print_r($subWorkload);
  
    $class = $this->section_workload_model->class_names($temp_id);
    $subjects = $this->section_workload_model->get_subs();
    //echo "<pre>"; print_r($class);
    //echo "<pre>"; print_r($subjects);
    foreach($class as &$cl){
      if(array_key_exists($cl->id, $subWorkload)){
        $cl->is_allocated = 0;
        $cl->subject = array();
        
        foreach($subWorkload[$cl->id] as $sub){
          $cl->subject[] = array('subname'=>$sub->long_name, 'workload'=>$sub->workload) ;
          if($sub->is_allotted == 1){
            $cl->is_allocated = 1;
          }
          
        }
      }
    }

    // echo "<pre>"; print_r($class);die();
    $data['section_workload'] = $class;
    $data['subjects'] = $subjects;

    echo json_encode($data);
  
  }

  public function manage_workload(){

    $sec_id = $_POST['sec_id'];
    //echo "<pre>"; print_r($sec_id);
    $temp_id = $_POST['temp_id'];
    $subWorkload = $this->section_workload_model->single_sec_workload($temp_id,$sec_id);
 

    $subjects = $this->section_workload_model->get_subs();
    //echo "<pre>"; print_r($subjects);
    //echo "<pre>"; print_r($subWorkload);die();
    $data['subjects'] = $subjects;

    foreach($subjects as $s){
      $s->workload = 0;
    }
    
    foreach($subjects as $s){
      foreach($subWorkload as $w){
        if($s->long_name==$w->long_name){
          $s->workload=$w->workload;
          break;
        }
      }
    }
    //echo "<pre>"; print_r($subjects);

    echo json_encode($data);

  }

  public function add_single_sec_workload(){
    $subArry = $_POST['subArry'];
    $sub_ids = $_POST['sub_ids'];
    $temp_id = $_POST['temp_id'];
    $sec_id = $_POST['sec_id'];
    //echo "<pre>"; print_r($subArry);
    //echo "<pre>"; print_r($sub_ids);
    //echo "<pre>"; print_r($sec_id);
    
    $data = $this->section_workload_model->insert_workload_data($temp_id,$sec_id, $sub_ids, $subArry);
    //echo "<pre>"; print_r($data);die();

    echo json_encode($data);
  }

  public function edit_single_sec_workload(){
    $subArry = $_POST['subArry'];
    $sub_ids = $_POST['sub_ids'];
    $temp_id = $_POST['temp_id'];
    $sec_id = $_POST['sec_id'];
    //echo "<pre>"; print_r($subArry);
    //echo "<pre>"; print_r($sub_ids);
    //echo "<pre>"; print_r($sec_id);


    $delete = $this->section_workload_model->delete_sec_workload($temp_id,$sec_id);
    $data = $this->section_workload_model->insert_workload_data($temp_id,$sec_id, $sub_ids, $subArry);
    //echo "<pre>"; print_r($data);die();

    

    echo json_encode($data);
  }

  public function delete_single_sec_workload(){
    
    $sec_id = $_POST['sec_id'];
    $temp_id = $_POST['temp_id'];
    $delete = $this->section_workload_model->delete_sec_workload($temp_id,$sec_id);
  
    echo json_encode($delete);
  }

  public function get_clone_details(){
    $temp_id = $_POST['temp_id'];
    $sec_names = $this->section_workload_model->sec_names($temp_id);
    $data['sec_names']=$sec_names;
    //echo "<pre>"; print_r($sec_names);die();
    echo json_encode($data);
  }

  public function clone_single_sec_workload(){
    $clone_id = $_POST['clone_id'];
    $sec_id = $_POST['sec_id'];
    $temp_id = $_POST['temp_id'];
    //echo "<pre>"; print_r($sec_id);
    $single_workload = $this->section_workload_model->get_single_workload($temp_id,$clone_id);
    //echo "<pre>"; print_r($single_workload);
    $data = $this->section_workload_model->insert_clone_workload($temp_id,$single_workload, $sec_id);
    echo "<pre>"; print_r($data);
    echo json_encode('1');
  }

  public function get_sub_list_to_add_from(){

    $sec_id = $_POST['sec_id'];
    $temp_id = $_POST['temp_id'];
    $subject_list = $this->section_workload_model->get_sub_list_to_add_from($temp_id,$sec_id);
    $class_name = $this->section_workload_model->get_class_name($sec_id);
    $data['subject_list'] = $subject_list;
    $data['class_name'] = $class_name;

    echo json_encode($data);

  }

  public function get_sub_list_to_delete_from(){

    $sec_id = $_POST['sec_id'];
    $temp_id = $_POST['temp_id'];
    $subject_list = $this->section_workload_model->get_sub_list_to_delete_from($temp_id,$sec_id);
    $class_name = $this->section_workload_model->get_class_name($sec_id);
    $data['subject_list'] = $subject_list;
    $data['class_name'] = $class_name;

    echo json_encode($data);

  }

  public function add_sub_to_sec(){
    $sec_id = $_POST['sec_id'];
    $temp_id = $_POST['temp_id'];
    $subject_load = $_POST['subject_load'];
    $subject_id = $_POST['subject_id'];
    $insert = $this->section_workload_model->add_single_subject_workload($sec_id,$temp_id,$subject_load,$subject_id);

    echo json_encode($insert);
  }

  public function delete_sub_from_section(){
    $sec_id = $_POST['sec_id'];
    $temp_id = $_POST['temp_id'];
    $subject_id = $_POST['subject_id'];
    $delete = $this->section_workload_model->delete_sub_from_sec($sec_id,$temp_id,$subject_id);

    echo json_encode($delete);
  }

  public function get_single_sub_workload(){
    $sec_id = $_POST['sec_id'];
    $temp_id = $_POST['temp_id'];
    $subject_id = $_POST['subject_id'];
    $data = $this->section_workload_model->get_single_sub_workload($sec_id,$temp_id,$subject_id);

    echo json_encode($data);

  }

}