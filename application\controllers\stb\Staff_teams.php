<?php
class Staff_teams extends CI_Controller
{
  function __construct()
  {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STAFF_TASKS_BASKET')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('stb/Staff_teams_model', 'teams_model');
    $this->CI = &get_instance();
    $this->load->library('filemanager');
  }

  //Landing function to show non-compliance menu
  public function index()
  {
    $data['staff_list'] = $this->teams_model->getStaffList();
    $data['is_task_admin'] = $this->authorization->isAuthorized('STAFF_TASKS_BASKET.TASKS_ADMIN');
    $data['enable_add'] = $this->authorization->isAuthorized('STAFF_TASKS_BASKET.ADD');
    $data['enable_add'] = '1';
    $data['logged_in_staff_id'] = $this->authorization->getAvatarStakeHolderId();

    $data['main_content']    = 'stb/manage_staff_teams/index_desktop.php';
    $this->load->view('inc_v2/template', $data);
  }

  public function getStaffTeamsData(){
    $result = $this->teams_model->getStaffTeamsData($_POST);
    echo json_encode($result);
  }

  public function addStaffTeam(){
    $result = $this->teams_model->addStaffTeam($_POST);
    echo json_encode($result);
  }

  public function status_update()
  {
      $result = $this->teams_model->status_update($_POST);
      echo $result;
  }

  public function updateStaffTeam(){
    $result = $this->teams_model->updateStaffTeam($_POST);
    echo json_encode($result);
  }
}