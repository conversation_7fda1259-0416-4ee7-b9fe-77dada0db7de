<?php 
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Documentation pages
 */
include (APPPATH.'libraries/docx_reader.php');

class Docs extends CI_Controller {
               
	function __construct(){
		parent::__construct();
        if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
        }
	}

    private function callDocxReader($file) {
        $doc = new Docx_reader();
        $doc->setFile($file);
        $html = '';
        if(!$doc->get_errors()) {
            $html = $doc->to_html();
            $plain_text = $doc->to_plain_text();
        } else {
            $html = implode(', ',$doc->get_errors());
            return null;
        }
        return $html."\n";
    }

    public function setup() {
        $file = FCPATH.'assets/docs/setup.docx';
        $data['doc'] = $this->callDocxReader($file);
        if($data['doc'] == null) {
            $data['doc'] = 'Document file not found';
        }
        $data['main_content'] = 'docs/setup';
        $this->load->view('inc/template', $data);
    }
}