<?php

class Assessment extends CI_Controller {

  function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
      }
       $this->load->model('academics/assessment_model');
  }

  public function index(){
    $data['grades'] = $this->assessment_model->getAllClasses();
    $data['main_content'] = 'academics/assessment/create_assessment';
    $this->load->view('inc/template', $data);
  }

  public function getSubjects(){
    $data['subjectsList'] = $this->assessment_model->getSubjectsList();
    echo json_encode($data);
  }

  public function submit_group(){
    $data = $this->assessment_model->submit_group();
    echo ($data);
  }

  public function getSubjectName(){
    $data = $this->assessment_model->getSubjectName();
    echo json_encode($data);
  }

  public function getGroups(){
    $data['groupList'] = $this->assessment_model->getGroups();
    echo json_encode($data);
  }

  public function getQuestions(){
    $data['ready'] = $this->assessment_model->getReadyStatus();
    $data['questions'] = $this->assessment_model->getQuestions();
    $data['group_name'] = $this->assessment_model->getGroupName();
    $data['total_points'] = $this->assessment_model->points();
    // echo "<pre>";print_r($data);die();
    echo json_encode($data);
  }

  public function getFilters(){
    $data['lesson'] = $this->assessment_model->getLessons();
    $data['name'] = $this->assessment_model->getAssessmentName();
    // $data['lesson'] = $this->assessment_model->getLessons();
    // $data['questions'] = $this->assessment_model->getQuestionsToAdd();
    echo json_encode($data);
  }

  public function getSubTopics(){
    $data['topic'] = $this->assessment_model->getSubTopics();
    echo json_encode($data);

  }

  public function getQuestionsToAdd(){
    $data['questions'] = $this->assessment_model->getQuestionsToAdd();
    echo json_encode($data);

  }

  public function submitQuestions(){
    $group_id = $_POST['group_id'];
    $question_ids_string = $_POST['question_ids_string'];
    $question_ids = json_decode($question_ids_string);
    $data = $this->assessment_model->submitQuestions($question_ids, $group_id);
    echo ($data);
  }

  public function finalizeQuestions(){
    $group_id = $_POST['group_id'];
    $data = $this->assessment_model->finalizeQuestions($group_id);
    echo ($data);
  }

  public function getRemainingQuestions(){
    // echo 'here';die();
    $data['questions'] = $this->assessment_model->getRemainingQuestions();
    echo json_encode($data);
  }

  public function deleteQuestion(){
    $data = $this->assessment_model->deleteQuestion();
    echo $data;
  }
 

}