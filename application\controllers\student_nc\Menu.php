<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  10 December 2021
 *
 * Description: Controller for Student Non-compliance.
 *
 * Requirements: PHP5 or above
 *
 */

class Menu extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_NONCOMPLIANCE')) {
      redirect('dashboard', 'refresh');
    }
  }

  //Landing function to show non-compliance menu
  public function index() {
    $site_url = site_url();

    $data['tiles'] = array(
      [
        'title' => 'Manage Non-compliance',
        'sub_title' => 'View and add Non-compliance',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'student_nc/noncompliance/manage_nc',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_NONCOMPLIANCE.ADD')
      ],
      [
        'title' => 'Resolve Non-compliance',
        'sub_title' => 'Resolve Non-compliance',
        'icon' => 'svg_icons/assestdiscardreport.svg',
        'url' => $site_url.'student_nc/noncompliance/resolve',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_NONCOMPLIANCE.RESOLVE_ANY_NON_COMPLIANCE')
      ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
      [
        'title' => 'Balance NC Report',
        'sub_title' => 'View balance non-compliances',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'student_nc/nc_reports/balance_nc_report_view',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_NONCOMPLIANCE.NONCOMPLIANCE_REPORT')
      ],
      [
        'title' => 'Student Non-compliance Report',
        'sub_title' => 'View Non-compliance of a specific student',
        'icon' => 'svg_icons/student.svg',
        'url' => $site_url.'student_nc/nc_reports/student_non_complaince_report',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_NONCOMPLIANCE.STUDENT_NONCOMPLIANCE_REPORT')
      ],
      // Need to complete implementation
      // [
      //   'title' => 'Non-compliance Analytics',
      //   'sub_title' => 'View Section level Analytics',
      //   'icon' => 'svg_icons/circularreport.svg',
      //   'url' => $site_url.'student_nc/nc_reports/non_compliance_analytics',
      //   'permission' =>  $this->authorization->isAuthorized('STUDENT_NONCOMPLIANCE.SECTION_SUMMARY_REPORT')
      // ],
      [
        'title' => 'Class-wise Analytics',
        'sub_title' => 'View Class-wise Analytics',
        'icon' => 'svg_icons/circularreport.svg',
        'url' => $site_url.'student_nc/nc_reports/class_wise_analytics',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_NONCOMPLIANCE.SECTION_SUMMARY_REPORT')
      ],
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['admin_tiles'] = array(
      [
        'title' => 'Manage NC Categories',
        'sub_title' => 'View, add and modify categories',
        'icon' => 'svg_icons/assestcategory.svg',
        'url' => $site_url.'student_nc/noncompliance/manage_categories',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_NONCOMPLIANCE.MANAGE_CATEGORIES')
      ],
      [
        'title' => 'Manage Penalty',
        'sub_title' => 'View, add and modify penalty',
        'icon' => 'svg_icons/assestcategory.svg',
        'url' => $site_url.'student_nc/noncompliance/manage_penalty',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_NONCOMPLIANCE.MANAGE_PENALTY')
      ],
    );
    $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);

    $data['main_content']    = 'student_nc/menu';
    $this->load->view('inc/template', $data);
  }

}