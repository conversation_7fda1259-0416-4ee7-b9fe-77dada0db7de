<?php
defined('BASEPATH') or exit('No direct script access allowed');
class S3_storage extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		// $this->load->library('session');
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isModuleEnabled('GALLERY')) {  // To Do
			redirect('dashboard', 'refresh');
		}
		$this->load->model('s3_storage_model');
        $this->config->load('s3');

		
	}

	public function index(){
		$data['storage'] = $this->s3_storage_model->get_storage();
		// echo '<pre>'; print_r($data); die();
		$data['main_content'] = 'management/s3_storage';
        $this->load->view('inc/template', $data);
	}


	public function update_storage(){
		// s3 credentials
		//  echo "update; "; die();

		

		// an array containing the sub folders hard coded
		$folders = ["Admission_form_document", "admissions", "certificates", "circular", "circulars", "class_files", "emails",  "events", "fee_reciepts", "gallary_images", "helpdesk","home_work", "marks_card_pictures", "marks_cards", "payslips", "profile", "questions", "recordings", "resources", "staff_avatar", "student_avatar", "student_documents", "task_submissions", "tasks", "Voucher"];


		// in for loop call FolderSize($folder)
		$final_result = array();
		foreach($folders as $f){
			$folder_size = $this->__FolderSize($f);
			array_push($final_result, [$f, $folder_size]);
		}


		//  call a model function and store that details in s3_storage table 
		$stored_result = $this->s3_storage_model->storesizes($final_result);

	}

	private function __FolderSize($folder) {
		
		$s3 = S3Client::factory(array(
			'key' => $this->CI->config->item('access_key'),
			'secret' => $this->CI->config->item('secret_key'),
		));


		$size = 0;
		$bucket = $this->CI->config->item('s3_bucket');
		$objects = $s3->getIterator('ListObjects', array(
			"Bucket" => $bucket, // or env('AWS_BUCKET')
			"Prefix" => $folder
		));
		foreach ($objects as $object) {
			$size = $size+$object['Size'];
		}
	
		return $size;
		// return $this->formatSizeUnits($size);
		 
		
	}
	// public function formatSizeUnits($bytes) {
	// 	if ($bytes >= 1073741824) {
	// 		$bytes = number_format($bytes / 1073741824, 2) . ' GB';
	// 	} elseif ($bytes >= 1048576) {
	// 		$bytes = number_format($bytes / 1048576, 2) . ' MB';
	// 	} elseif ($bytes >= 1024) {
	// 		$bytes = number_format($bytes / 1024, 2) . ' KB';
	// 	} elseif ($bytes > 1) {
	// 		$bytes = $bytes . ' bytes';
	// 	} elseif ($bytes == 1) {
	// 		$bytes = $bytes . ' byte';
	// 	} else {
	// 		$bytes = '0 bytes';
	// 	}
	// 	return $bytes;
	// }

}

?>
