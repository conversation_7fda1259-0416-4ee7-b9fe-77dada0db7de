<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('ParentTicket_dashboard');?>">Parent Ticketing</a></li>
    <li>Parent Tickets</li>
</ul>

<div class="col-md-12">

<div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
        <div class="row" style="margin: 0px;">
            <div class="d-flex justify-content-between" style="width:100%;">
                <h3 class="card-title panel_title_new_style_staff">
                    <a class="back_anchor" href="<?php echo site_url('ParentTicket_dashboard'); ?>">
                    <span class="fa fa-arrow-left"></span>
                    </a> 
                    Parent Tickets
                </h3>
            </div>
        </div>
    </div>


<?php
    //Show summary only if the person is a ticketing admin
    // if ($is_ticketing_admin == 1) {
?>
<div class="col-md-12">
        <div class="card-header panel_heading_new_style_staff_border">
            <form method="post" id="form_data"  data-parsley-validate="" class="form-inline">
                
               
                
            <div class="form-grou col-md-2 pl-0">
                        <div id="reportrange" class="dtrange" style="width: 100%"><span></span></div>
                        <input type="hidden" id="from_date" name="from_date" />
                        <input type="hidden" id="to_date" name="to_date" />
                    </div>

                <div class="form-inline mr-2" >
                    
                   
                    <select name="ticket_types" class="form-control" id="ticket_types">
                        <?php if($is_ticketing_admin == 1){ ?>
                            <option value="1" selected <?php //echo $ticket_mode == 1 ? "selected" : ""; ?>>Open Tickets</option>
                            <option value="2" <?php //echo $ticket_mode == 2 ? "selected" : ""; ?>>Assigned to me</option>
                            <option value="3" <?php //echo $ticket_mode == 3 ? "selected" : ""; ?>>All Tickets</option>
                       <?php }else{ ?>
                            <option value="2" <?php //echo $ticket_mode == 2 ? "selected" : ""; ?>>Assigned to me</option>
                       <?php } ?>
                        
                    </select>
                </div>

                <button type="button" class="btn btn-primary my-2" onclick="get_ticket_data()" style="margin:0px !important">Get Report</button>
            </form>
        </div>

            </div>
            <div class="card-body pt-1 my-4 mx-4">
                <div class="" style="width: 41%;">
                    <h3 class="panel-title"><strong>Ticket Summary</strong></h3>
                    <div class="table-responsive my-3">
                    <table class="table table-bordered">
                        <tbody>
                            <tr><td>Total Open tickets</td><td><?= $ticketsummary->open_tickets; ?></td>
                            </tr>
                            <tr><td>Assigned to me</td><td><?= $ticketsummary->assigned; ?> </td>
                            </tr>
                            <tr><td>All tickets</td><td><?= ($ticketsummary->all >=1000) ?  $ticketsummary->all.'+' : $ticketsummary->all ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
         
        </div> <!--Panel body-->
        <div id="dislay_data" class="px-2">
        <div class="col-md-14 text-center d-flex justify-content-center align-items-center" style="height: 10%; width: 100%;display:none !important" id="list_data">
                    <i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i>
                </div>
        </div>
    </div> <!--panel-->
</div> <!--col-md-12-->
<?php        
    // }
?>
<br>
<div class="col-md-12" style="padding-bottom: 1rem;">
    <div class="card cd_border">

        
        

       
             
            <!-- only mobile -->
            <div class="card-body visible-xs leaveData" style="padding:7px;">
                <?php 
                    if(empty($list_issue))
                        echo 'No data available';
                ?>
                <?php $i=1; foreach ($list_issue as $key => $list) { ?>
                    <?php if($list->status=='Open'){
                        $bg='#D93E39';
                    }
                    else{
                        $bg='#3DA755';
                    }?>
                <div class="card-body" style="color: <?= $bg ?>">
                    <p><strong><?= $list->ticket_number ?></strong><strong> : </strong><?= $list->title ?></p>
                    <p><strong>Description : </strong>
                        <?php
                            if (!empty($list->description))
                                echo substr($list->description, 0,75).'...';
                            else 
                                echo 'No Description';
                        ?>
                    </p>
                    <p><strong>Assigned to : </strong><?= $list->assigned_to ?></p> 
                    <p><strong>Status : </strong><?= $list->status; ?>
                    <a  href="<?php echo site_url('parent_ticketing/view_ticket_details/' . $list->id); ?>" class='btn btn-primary  pull-right' style="margin:-5px; margin-right: 3px;" data-placement='top'  data-toggle='tooltip' data-original-title='View and Action'>View</i></a></p>
                    <!--<a style="margin-right:2px" href="<?php // echo site_url('parent_controller/issue_raise_delete'); ?>" class='btn btn-warning  pull-right' data-placement='top'  data-toggle='tooltip' data-original-title='Delete'><i class='fa fa-trash-o'></i></a>
                    <a style="margin-right:2px" href="<?php // echo site_url('parent_controller/issue_raise_edit'); ?>" class='btn btn-warning  pull-right' data-placement='top'  data-toggle='tooltip' data-original-title='Edit'><i class='fa fa-edit'></i></a> </p>-->
                </div>
                <div class="col-xs-12 line" style="border:2px solid #a29696"></div> 
                <?php } ?> 

                <a href="<?php echo site_url('dashboard');?>" id="backBtn"><span class="fa fa-mail-reply"></span></a>

            </div> 
        </div>
    </div>
</div>
</div>
</div>
<style>
ul.panel-controls > li > a.control-primary {
    color: white;
    border-color: white;
}
.btn-small {
    padding: 5px 10px;
}
.btn-small.btn-primary {
    margin: 0px !important;
    margin-right:20px !important;
}

</style>
        

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<!-- <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script> -->
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script type="text/javascript">
   $(document).ready(function () {
    var from_date = _get_cookie('from_date');
    var to_date = _get_cookie('to_date');
    var ticket_types = _get_cookie('ticket_types');

    if (from_date !== null && to_date !== null && ticket_types !== null) {
        $('#from_date').val(from_date);
        $('#to_date').val(to_date);
        $('#ticket_types').val(ticket_types);

        // Check if the date range corresponds to "All"
        if (from_date === '01-01-0001' && to_date === '31-12-9999') {
            $('#reportrange span').html('All');
            $('#reportrange').data('daterangepicker').setStartDate(moment('0001-01-01'));
        $('#reportrange').data('daterangepicker').setEndDate(moment('9999-12-31'));
        } else {
            var fromDateMoment = moment(from_date, 'DD-MM-YYYY');
            var toDateMoment = moment(to_date, 'DD-MM-YYYY');
            $('#reportrange span').html(fromDateMoment.format('MMM D, YYYY') + ' - ' + toDateMoment.format('MMM D, YYYY'));
        }

        get_ticket_data();  
    } else {
        get_ticket_data();
    }
});


    $("#reportrange").daterangepicker({
        ranges: {
        'All': ['All'],
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
        
        },
        opens: 'right',
        buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small btn-primary pull-right',
        format: 'MM.DD.YYYY',
        separator: 'to',
        // startDate: moment(),
        // endDate: moment()
        startDate: moment('0001-01-01'), 
        endDate: moment('9999-12-31')             
    },function(start, end, label) {
        if (label === 'All') {
            $('#reportrange span').html('All'); 
            $('#from_date').val('01-01-0001');
            $('#to_date').val('31-12-9999');
            $('#reportrange').data('daterangepicker').setStartDate(moment('0001-01-01'));
            $('#reportrange').data('daterangepicker').setEndDate(moment('9999-12-31'));
        } else {
            $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
            $('#from_date').val(start.format('DD-MM-YYYY'));
            $('#to_date').val(end.format('DD-MM-YYYY'));
        }
    });

            $("#reportrange span").html("All");
            $('#from_date').val('01-01-0001');
            $('#to_date').val('31-12-9999');

  function get_ticket_data() {
    $('#list_data').show();
    var from = $('#form_data')[0];
    var formData = new FormData(from);
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    var ticket_types = $('#ticket_types').val();
    console.log(ticket_types);
    _set_cookie('from_date', from_date);
    _set_cookie('to_date', to_date);
    _set_cookie('ticket_types', ticket_types);

    $.ajax({
        url: '<?php echo site_url('parent_ticketing/get_ticket_type_wise'); ?>',
        type: 'post',
        data: formData, 
        processData: false, 
        contentType: false, 
        success: function(data) {
            $('#list_data').hide();
            var result = $.parseJSON(data);
            var html = '';
            if (result.list_issue.length) {
                
                html += "<div><a onclick='exportToExcel()' class='btn btn-primary pull-right' style='margin: 10px !important;'><span class='fa fa-file-text-o'></span> Export</a></div>";
                html += "<table class='table table-bordered'>";
                html += "<thead><tr>";
                html += "<th data-toggle='tooltip' onclick='clearStatusFilters()' data-original-title='Clear Filters' style='width:5%;cursor: pointer;color: #e04b4a;vertical-align: middle;font-size: 1.5rem;'><i class='fa fa-times'></i></th>";
                html += "<th><input onkeyup='filterStatusList()' type='text' class='form-control' id='std-filter' placeholder='Search'/></th>";
                html += "<th><input onkeyup='filterStatusList()' type='text' class='form-control' id='section_name-filter' placeholder='Search'/></th>";
                html += "<th><input onkeyup='filterStatusList()' type='text' class='form-control' id='assigned_to-filter' placeholder='Search'/></th>";
                html += "<th><input onkeyup='filterStatusList()' type='text' class='form-control' id='category-filter' placeholder='Search'/></th><th></th>";
                html += "<th></th><th><input onkeyup='filterStatusList()' type='text' class='form-control' id='title-filter' placeholder='Search'/></th>";
                html += "<th><select onchange='filterStatusList()' class='form-control' id='status-filter'><option value=''>All</option><option value='Open'>Open</option><option value='Response_from_parent'>Response from parent</option><option value='Closed'>Closed</option></select></th>";
                html += "<th></th></tr></thead>";
                
                html += "<thead><th width='7%'>Ticket #</th>";
                html += "<th width='15%'>Student</th>";
                html += "<th width='7%'>Section</th>";
                html += "<th width='12%'>Assigned To</th>";
                html += "<th width=''>Category</th>";
                html += "<th width='12%'>Opened on</th>";
                html += "<th width='7%'>Closed on</th>";
                html += "<th width='16%'>Title</th>";
                html += "<th width='7%'>Status</th>";
                html += "<th width='5%'>Action</th></thead>";
                
                html += "<tbody>";

                for (var s = 0; s < result.list_issue.length; s++) {
                    var item = result.list_issue[s];
                    var color_code = '';

                    if (item.status === "Open") {
                        color_code = '#D93E39';
                    } else if (item.status === "Closed") {
                        color_code = '#3DA755';
                    } else if (item.status === "Response_from_parent") {
                        color_code = '#e2d668';
                    }

                    html += `<tr 
                        data-std-filter="${item.sName?.toLowerCase()}" 
                        data-section_name-filter="${item.section_name}" 
                        data-assigned_to-filter="${item?.assigned_to?.toLowerCase()}" 
                        data-category-filter="${item.name?.toLowerCase()}" 
                        data-title-filter="${item.title?.toLowerCase()}" 
                        data-status-filter="${item?.status}" 
                        class="tickets-filters">`;
                    
                    html += "<td style='background:" + color_code + "'>" + item.ticket_number + "</td>";
                    html += "<td>" + item.sName + "</td>";
                    html += "<td>" + item.section_name + "</td>";
                    html += "<td>" + item.assigned_to + "</td>";
                    html += "<td>" + item.name + "</td>";

                    var dateRegex = /\b(\d{1,2})-(\d{1,2})-(\d{4})\s(\d{1,2}):(\d{1,2})\b/;
                    var match = item.created_on.match(dateRegex);
                    if (match) {
                        var createdOn = new Date(match[3], match[2] - 1, match[1], match[4], match[5]);
                        var formattedDate = createdOn.toLocaleString('en-GB', {
                            day: '2-digit',
                            month: 'short',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                        });
                        html += "<td>" + formattedDate + " (Opened for " + item.days_opened_for + " days)</td>";
                    }
                    
                    var closed_date='';
                    if(item.status == "Open"){
                        closed_date="-";
                    }else{
                         closed_date = item.closed_date;
                    }

                    html += "<td>" + closed_date + "</td>";
                    html += "<td>" + item.title + "</td>";
                    html += "<td style='background:" + color_code + ";text-transform: capitalize;'>" + item.status + "</td>";
                    html += "<td>";
                    var siteurl = "<?php echo site_url('parent_ticketing/view_ticket_details/'); ?>" + item.id;
                    html += "<a href='" + siteurl + "' class='btn btn-warning' data-placement='top' data-toggle='tooltip' data-original-title='View and Action'>";
                    html += "<i class='fa fa-eye'></i>";
                    html += "</a></td>";
                    html += "</tr>";
                }

                html += "</tbody></table>";
            } else {
                html += "<div class='no-data-display my-4 mx-4'>No Data Found</div>";
            }

            $('#dislay_data').html(html);
        }
    });
}


    function exportToExcel(){
        var htmls = "";
        var uri = 'data:application/vnd.ms-excel;base64,';
        var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
        var base64 = function(s) {
            return window.btoa(unescape(encodeURIComponent(s)))
        };

        var format = function(s, c) {
            return s.replace(/{(\w+)}/g, function(m, p) {
                return c[p];
            })
        };

        var summaryTable = $("#dislay_data").html();

        var ctx = {
        worksheet : 'Ticket',
        table : summaryTable
        }

        var link = document.createElement("a");
        link.download = "export.xls";
        link.href = uri + base64(format(template, ctx));
        link.click();

    }

    function clearStatusFilters() {
        $(`#std-filter`).val('');
        $(`#section_name-filter`).val('');
        $(`#assigned_to-filter`).val('');
        $(`#title-filter`).val('');
        $(`#status-filter`).val('');
        filterStatusList();
    }

    function filterStatusList() {
    var std_filter = $(`#std-filter`).val().toLowerCase();
    var section_name_filter = $(`#section_name-filter`).val();
    var assigned_filter = $(`#assigned_to-filter`).val().toLowerCase();
    var title_filter = $(`#title-filter`).val().toLowerCase(); 
    var status_filter = $(`#status-filter`).val();
    var category_filter = $(`#category-filter`).val().toLowerCase(); 
    var find_string = '';


    if (std_filter != '') {
        find_string += `[data-std-filter*="${std_filter}"]`;
    }
    if (section_name_filter != '') {
        find_string += `[data-section_name-filter*="${section_name_filter}"]`;
    }
    if (assigned_filter != '') {
        find_string += `[data-assigned_to-filter*="${assigned_filter}"]`;
    }
    if (title_filter != '') {
        find_string += `[data-title-filter*="${title_filter}"]`;
    }
    if (status_filter != '') {
        find_string += `[data-status-filter*="${status_filter}"]`;
    }
    if (category_filter != '') {
        find_string += `[data-category-filter*="${category_filter}"]`;
    }

    if (find_string === '') {
        $(".tickets-filters").show(); 
    } else {
        $(".tickets-filters").hide();
        $(`.tickets-filters${find_string}`).show(); 
    }
}


    // $("#ticket_types").change(e=>{
    //     const ticketType=$("#ticket_types").val();
    //     const name="<?php echo site_url('parent_ticketing/view_assigned_tickets') ?>"
    //     window.location.href=`${name}/${ticketType}`;
    // })
</script>