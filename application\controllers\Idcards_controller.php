<?php
//echo "<pre>";print_r($data);die();
defined('BASEPATH') or exit('No direct script access allowed');

class Idcards_controller extends CI_Controller{
	public function __construct(){
		parent::__construct();
		$this->load->model('idcards_model');
		$this->load->library('filemanager');
	}

	public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }
	public function index(){
		$site_url = site_url();
	    $data['tiles'] = array(
			[
	            'title' => 'Manage ID Card Templates',
	            'sub_title' => 'View the Template and Add, Edit Template',
	          	'icon' => 'svg_icons/view.svg',
	            'url' => $site_url.'Idcards_controller/manageIDTemplate',
	            'permission' => 1
	          ],

	          [
	            'title' => 'Create Order',
	            'sub_title' => 'Create a new order',
	          	'icon' => 'svg_icons/freshentry.svg',
	            'url' => $site_url.'Idcards_controller/create_order',
	            'permission' => 1
	          ],
	          [
	            'title' => 'Create Order new',
	            'sub_title' => 'Create a new order new',
	          	'icon' => 'svg_icons/freshentry.svg',
	            'url' => $site_url.'Idcards_controller/create_order_v2',
	            'permission' => 1
	          ],
	          [
	            'title' => 'View Orders',
	            'sub_title' => 'View the created orders here',
	          	'icon' => 'svg_icons/view.svg',
	            'url' => $site_url.'Idcards_controller/view_orders',
	            'permission' => 1
			  ],
			  [
	            'title' => 'Create template',
	            'sub_title' => 'View the Creat Template',
	          	'icon' => 'svg_icons/view.svg',
	            'url' => $site_url.'Idcards_controller/createTemplate',
	            'permission' => 1
	          ],
			  [
	            'title' => 'Verify data',
	            'sub_title' => 'Verify data',
	          	'icon' => 'svg_icons/view.svg',
	            'url' => $site_url.'Idcards_controller/verify_data',
	            'permission' => 1
	          ]  
	    );
	    $data['tiles'] = checkTilePermissions($data['tiles']);

		$data['main_content'] = 'idcards/index';
		$this->load->view('inc/template',$data);
	}

	public function create_order(){
		$data['classSectionList'] = $this->idcards_model->getClassSectionNames();
		$data['staffDetails']=$this->idcards_model->getStaffDetails();
		// echo "<pre>";print_r($data);die();		
		$data['main_content'] = 'idcards/create_order';
		$this->load->view('inc/template',$data);
	}
	public function create_order_v2(){
		$data['classSectionList'] = $this->idcards_model->getClassSectionNames();
		$data['staffDetails']=$this->idcards_model->getStaffDetails();
		// echo "<pre>";print_r($data);die();		
		$data['main_content'] = 'idcards/create_order_v2';
		$this->load->view('inc/template',$data);
	}


	public function createNewIDCardOrder(){
		$data['main_content']='idcards/createNewIDCardOrder';
		$this->load->view('inc/template',$data);
	}

	public function orderSummaryIDcard(){
		$data['main_content']='idcards/orderSummaryIDcard';
		$this->load->view('inc/template',$data);
	}

	public function getStudentDetails(){
		$sectionId=$_POST['sectionId'];
		$stdData = $this->idcards_model->getstdDataByClassSection($sectionId);
		echo json_encode($stdData);
	}
	public function submit_order(){
		$idcard_type=$_POST['idcard_type'];
			if($idcard_type=='staff_selection')
				$type='staff';
			else if($idcard_type=='student_selection')
				$type='student';
			else if($idcard_type=='parent_selection')
				$type='parent';
		$result=$this->idcards_model->insertorder();
		// echo "<pre>";print_r($result);die();
      	redirect('Idcards_controller/ordercreated_view/'.$result);
	}
	public function id_card_templates_view(){
		$file = FCPATH.'assets/id_card_template';
		$filenames=array_diff(scandir($file), array('..', '.'));
		foreach($filenames as $key =>$value){
			$data_templates[]=array(
				'template_id' => $value,
				'template_html' =>	file_get_contents($file.'/'.$value)
			);
		}
		$data['templates']=$data_templates;
		// echo "<pre>";print_r($data);die();
		$data['main_content'] = 'idcards/templates_view';
		$this->load->view('inc/template',$data);
	}
	// public function id_card_templates($last_order_id,$type){
	// 	$file = FCPATH.'assets/id_card_template';

	// 	$filenames=array_diff(scandir($file), array('..', '.'));
	// 	foreach($filenames as $key =>$value){
	// 		$data_templates[]=array(
	// 			'template_id' => $value,
	// 			'template_html' =>	file_get_contents($file.'/'.$value)
	// 		);
	// 		// echo "<pre>";print_r($value);
	// 	}
	// 	$data['last_order_id']=$last_order_id;
	// 	$data['templates']=$data_templates;
	// 	$data['idcard_type']=$type;
	// 	$data['main_content'] = 'idcards/templates';
	// 	$this->load->view('inc/template',$data);
	// }
 //   public function select_template($template_id,$template_idwithext,$last_order_id,$idcard_type){
	// 	$result=$this->idcards_model->select_template($template_id,$template_idwithext,$last_order_id,$idcard_type);
	// 	if ($result) {
 //      		$this->session->set_flashdata('flashSuccess', 'Order Created Successfully');
 //    	} else {
 //      		$this->session->set_flashdata('flashError', 'Unable to Create Order');
 //    	}
 //      	redirect('Idcards_controller/ordercreated_view/'.$last_order_id);
	// }
	public function ordercreated_view($last_order_id){
		$data['idcards_data'] = $this->idcards_model->order_view_idcardData($last_order_id);
		$data['stakeholders_data'] = $this->idcards_model->order_view_stakeholderData($last_order_id);
		// $data['idcard_template'] = $this->idcards_model->order_view_template($last_order_id);
		// echo "<pre>";print_r($data);die();
		$data['main_content'] = 'idcards/createorder_view';
    	$this->load->view('inc/template',$data);
	}
	public function view_orders(){
		$data['order_details']=$this->idcards_model->view_orders();
		$data['main_content']='idcards/view_orders';
		$this->load->view('inc/template',$data);
	}
	public function approve_idcards($id,$idcard_type){
		$section_id='';
		if($idcard_type=='student'){
			$data['classSectionList']=$this->idcards_model->listOfAllClasses($id);
			$data['idcard_type']=$idcard_type;
			$data['order_name']=$this->idcards_model->getOrderName($id);
			$data['order_id']=$id;
			$data['attributes'] = $this->idcards_model->getIdcardAttributes($id);
			// echo "<pre>";print_r($data);die();
			$data['main_content']='idcards/approve_idcards';
			$this->load->view('inc/template',$data);	
		}
		else if($idcard_type=='staff'){
			$data['approve_details']=$this->idcards_model->approve_idcards($id,$idcard_type,$section_id);
			$data['idcard_type']=$idcard_type;
			$data['order_name']=$this->idcards_model->getOrderName($id);
			$data['attributes'] = $this->idcards_model->getIdcardAttributes($id);
			$data['order_id']=$id;
			// echo "<pre>";print_r($data);die();
			$data['main_content']='idcards/approve_idcards_proceed';
			$this->load->view('inc/template',$data);
		}
	}
	public function approve_idcards_proceed($section_id,$stu_count,$order_id,$idcard_type){
		// $data['template_details']=$this->idcards_model->getTemplateDetails($template_id);
		$data['attributes'] = $this->idcards_model->getIdcardAttributes($order_id);
		$data['approve_details']=$this->idcards_model->approve_idcards($order_id,$idcard_type,$section_id);
		$data['order_id']=$order_id;
		$data['idcard_type']=$idcard_type;
		$data['stu_count']=$stu_count;
		$data['section_id']=$section_id;
		// echo "<pre>";print_r($data);die();

		$data['main_content']='idcards/approve_idcards_proceed';
		$this->load->view('inc/template',$data);
	}
	public function approve_singleIdcard(){
		$id=$_POST['id'];
		$this->idcards_model->approve_singleIdcard($id);
	}

	public function idcard_tohold(){
		$result = $this->idcards_model->idcard_tohold();
		echo $result;
	}

	public function place_order($id){
		$result = $this->idcards_model->place_order($id);
		if ($result) {
        	$this->session->set_flashdata('flashSuccess', 'Order Placed Successfully');
      	} else {
        	$this->session->set_flashdata('flashError', 'Unable to place Order');
      	}
      	redirect('Idcards_controller/view_orders');
	}

	public function order_name_check(){
		$data=$this->idcards_model->order_name_check();
		echo json_encode($data);
	}

	public function order_excel_report(){
		$order_id = $_POST['order_id'];
		$idcard_type = $_POST['idcard_type'];
		$data['approve_details']=$this->idcards_model->order_excel_report($order_id,$idcard_type);
		$data['attributes'] = json_decode($this->idcards_model->getIdcardAttributes($order_id));
		// echo "<pre>";print_r($data);die();
		echo json_encode($data);
	}

	public function idCardsZip(){
		$order_id = $_POST['order_id'];
		$idcard_type = $_POST['idcard_type'];
		$order_name = $_POST['order_name'];
		$images = $this->idcards_model->order_excel_report($order_id,$idcard_type);
		$attributes = json_decode($this->idcards_model->getIdcardAttributes($order_id));
      	$this->load->library('zip');
      	if($idcard_type=='staff'){
      		if(!empty($images)){
      			if(in_array("STAFF_PHOTO",$attributes)){
	      			foreach ($images as $key) {
	      				if ($key->STAFF_PHOTO !='') {
	      					$url = $this->filemanager->getFilePath($key->STAFF_PHOTO);
				            $ext = pathinfo($url, PATHINFO_EXTENSION);
				            $name = $key->STAFF_NAME.'_'.$key->od.'.'.$ext;
				            $data = file_get_contents($url);
				            $this->zip->add_data($name, $data);
				            $this->zip->archive(FCPATH.'assets/marks_card_zip/'.$order_name.'staff.zip');
	      				}
	      			}
	      			$this->zip->download($order_name.'staff.zip');
      			}
      		}
      		else {
		        $this->session->set_flashdata('flashError', 'No Images were uploaded yet!!!');
		    }
      	}
      	if ($idcard_type=='student') {
      		if(!empty($images)){
      			if(in_array("STUDENT_PHOTO",$attributes)){
      				foreach ($images as $key) {
	      				if ($key->STAFF_PHOTO !='') {
	      					$url = $this->filemanager->getFilePath($key->STUDENT_PHOTO);
				            $ext = pathinfo($url, PATHINFO_EXTENSION);
				            $name = $key->STAFF_NAME.'_'.$key->od.'.'.$ext;
				            $data = file_get_contents($url);
				            $this->zip->add_data($name, $data);
				            $this->zip->archive(FCPATH.'assets/marks_card_zip/'.$order_name.'student.zip');
	      				}
	      			}
	      			$this->zip->download($order_name.'student.zip');
      			}
      			if(in_array("FATHER_PHOTO",$attributes)){
      				foreach ($images as $key) {
	      				if ($key->STAFF_PHOTO !='') {
	      					$url = $this->filemanager->getFilePath($key->FATHER_PHOTO);
				            $ext = pathinfo($url, PATHINFO_EXTENSION);
				            $name = $key->STUDENT_NAME.'_'.$key->CLASS.'_'.$key->SECTION.'.'.$ext;
				            $data = file_get_contents($url);
				            $this->zip->add_data($name, $data);
				            $this->zip->archive(FCPATH.'assets/marks_card_zip/'.$order_name.'father.zip');
	      				}
	      			}
	      			$this->zip->download($order_name.'father.zip');
      			}
      			if(in_array("MOTHER_PHOTO",$attributes)){
      				foreach ($images as $key) {
	      				if ($key->STAFF_PHOTO !='') {
	      					$url = $this->filemanager->getFilePath($key->MOTHER_PHOTO);
				            $ext = pathinfo($url, PATHINFO_EXTENSION);
				            $name = $key->STUDENT_NAME.'_'.$key->CLASS.'_'.$key->SECTION.'.'.$ext;
				            $data = file_get_contents($url);
				            $this->zip->add_data($name, $data);
				            $this->zip->archive(FCPATH.'assets/marks_card_zip/'.$order_name.'mother.zip');
	      				}
	      			}
	      			$this->zip->download($order_name.'mother.zip');
      			}
      		}
      	}
	}

	public function delete_idcard(){
		$id=$_POST['id'];
		$data = $this->idcards_model->delete_idcard($id);
		echo $data;
	}

	public function createTemplate(){
		// $data['main_content']='idcards/id_card_template_create';
		// $data['main_content']='idcards/idCard_template';
		$data['main_content']='template/tpl_default';
		$this->load->view('inc/template',$data);
	}

	public function save_template(){
        $data = json_decode(file_get_contents('php://input'), true);
        $json = $data['json'];
        $html = $data['html'];
        $this->idcards_model->save_template($json, $html);
        echo json_encode(['status' => 'success']);
    }

	public function view_template($id){
        $template = $this->idcards_model->get_template($id);
        $data['template_html'] = $template->html;
		$data['main_content']='idcards/templates_view';
		$this->load->view('inc/template',$data);
    }


	// public function save_tsemplate(){
	// 	$data = json_decode(file_get_contents('php://input'), true);
	// 	$imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $data['image']));
	// 	file_put_contents(FCPATH . 'uploads/design_' . time() . '.png', $imageData);
	// 	echo json_encode(['status' => 'success']);
	// }

	public function verify_data(){
		$data['template'] = $this->idcards_model->get_id_card_template();
		$data['main_content']='idcards/verify_data';
		$this->load->view('inc/template',$data);
	}
	public function view_id_cards() {
		// $data['students'] = $this->idcards_model->get_all_students();
		$data['staffs'] = $this->idcards_model->get_all_staffs();
		// echo "<pre>";print_r($data['students']);die();
		// $this->load->view('idcards/view_id_cards', $data);
		$students = $this->idcards_model->get_all_students();
        $template_id = 10; // Set template ID dynamically

        $id_cards = [];
        foreach ($data['staffs'] as $staff) {
            $id_cards[] = $this->render_idcard($template_id, $staff['id']); 
        }
		// echo "<pre>";print_r($id_cards);die();
        $data['id_cards'] = $id_cards;
        $this->load->view('idcards/view_id_cards', $data);
    }

	public function generate_pdf() {
        $this->load->library('pdf');
        $data['students'] = $this->idcards_model->get_all_students();
        $html = $this->load->view('idcards/view_id_cards', $data, true);
        $this->pdf->createPDF($html, 'student_id_cards', false);
    }

	// New custom template generator
    public function custom_template(){
        $data['main_content'] = 'idcards/idcard_custom_template';
        $this->load->view('inc/template', $data);
    }

	// New Console Start

	public function manageIDTemplate(){	
		$data['IdTemplates'] = $this->idcards_model->getIDCardTemplates();
		$data['main_content'] = 'idcards/manageIDTemplate';
		$this->load->view('inc/template',$data);
	}

	public function createNewIdCardTemplate($id =''){
		$data['editIdTemplates'] = $this->idcards_model->editIDCardTemplates($id);
		// echo "<pre>";print_r($data['editIdTemplates']);die();
		$data['main_content'] = 'idcards/createNewIdCardTemplate';
		$this->load->view('inc/template',$data);
	}

	public function saveIdCarttemplate() {
		$template_id = $this->input->post('template_id');
		$data = array(
			'template_name' => $this->input->post('name'),
            'template_content' => json_encode([
                'height' => $this->input->post('height'),
                'width' => $this->input->post('width'),
                'font_color' => $this->input->post('font_color'),
                'font_style' => $this->input->post('font_style'),
                'font_weight' => $this->input->post('font_weight'),
                'font_size' => $this->input->post('font_size'),
                'text_align' => $this->input->post('text_align'),
                'background_color' => $this->input->post('background_color'),
                'border' => $this->input->post('border'),
                'border_thickness' => $this->input->post('border_thickness'),
                'border_color' => $this->input->post('border_color'),
                'border_padding' => $this->input->post('border_padding'),
                'position' => $this->input->post('position'),
                'border_radius' => $this->input->post('border_radius'),
                'position' => $this->input->post('position'),
                'text_value' => $this->input->post('text_value'),
                'size' => $this->input->post('size'),
                'image_radius' => $this->input->post('image_radius'),
                'filename' => $this->input->post('filename'),
            ]),
            'template_code' => $this->cleanTemplateCode($this->input->post('template_code')),
            'template_image' => $this->input->post('template_image'),
			'created_on' => $this->Kolkata_datetime(),
        );
		// echo "<pre>";print_r($data);die();
        $result = $this->idcards_model->saveTemplate($data, $template_id);
        echo json_encode(['status' => $result ? 'success' : 'failed']);
    }

    private function cleanTemplateCode($html) {
        // Remove draggable classes and unnecessary attributes
        $html = preg_replace('/\s*ui-draggable\s*/', '', $html);
        $html = preg_replace('/\s*ui-draggable-handle\s*/', '', $html);
        return $html;
    }

	private function render_idcard($template_id, $student_id) {
        $template = $this->idcards_model->getTemplateDetails($template_id);
        // $student = $this->student_model->getStudent($student_id);
        $staff = $this->idcards_model->getStaff($student_id);
		
        if (!$template || !$staff) {
			return "<div class='error'>Template or Student Not Found</div>";
        }
		
        // Get the template HTML
        $html = $template->template_code;
		
        // Replace placeholders with student data
        $replacements = [
			'[[EMPLOYEE_NAME]]' => $staff->staff_name,
            '[[EMPLOYEE_CODE]]' => $staff->employee_code,
            '[[DESIGNATION]]' => $staff->designation,
            'src=""' => 'src="' . $this->filemanager->getFilePath($staff->staff_photo) . '"'
        ];
		$html = str_replace(array_keys($replacements), array_values($replacements), $html);
		$html = preg_replace(
			'/src="data:image\/[^"]+"/', 
			'src="' . $this->filemanager->getFilePath($staff->staff_photo) . '"', 
			$html
		);
		return $html;
    }

    // public function render_idcard($template_id, $student_id) {
	// 	echo "<pre>";print_r($template_id, $student_id);die();
    //     $template = $this->idcards_model->getTemplate($template_id);
    //     $student = $this->student_model->getStudent($student_id);
        
    //     // Get the template HTML
    //     $html = $template->template_code;
        
    //     // Replace placeholders with student data
    //     $replacements = [
    //         '[[EMPLOYEE_ID]]' => $student['student_name'],
    //         '[[ADMISSION_NO]]' => $student['admission_no'],
    //         'src=""' => 'src="' . $this->filemanager->getFilePath($student['student_photo']) . '"'
    //     ];
        
    //     $html = str_replace(array_keys($replacements), array_values($replacements), $html);
        
    //     return $html;
    // }
}

?>