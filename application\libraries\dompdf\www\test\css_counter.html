

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd"> 
<html>
<head>
<title>12.5b counter-increment </title>
<meta http-equiv="Content-Style-Type" content="text/css">
<style type="text/css">
li:before {content: counter(ct1) "|" counter(ct2) "|" counter(ct3) "... ";}
li {counter-increment: ct1;}
li li {counter-increment: ct2 2;}
li li li {counter-increment: ct3 -3;}
</style>
</head>

<body>
 <h2>CSS2 Test Suite: 12.5 counter-increment </h2>
[<A HREF="index.html">index page</A>] [<I>section</I>] [<A HREF="sec12-05a.htm">Previous</A>] [<A HREF="sec12-06-01.htm">Next</A>] [<A HREF="http://www.w3.org/TR/CSS2/generate.html#propdef-counter-increment">Specification</A>]
<HR>
<p>The style declarations contained within this page:</p>
<pre>span:before {content: counter(ct1) "|" counter(ct2) "|" counter(ct3) "... ";}
li span:before {counter-increment: ct1;}
li li span:before {counter-increment: ct2 2;}
li li li span:before {counter-increment: ct3 -3;}

</pre>
<hr>
<p>
The list items in the following set of lists all have 'span' elements, and each 'span' should be preceded with generated content in the form "a|b|c... " where the letters are incremented numbers.  "a" numbers increment in ones, "b" in twos, and "c" in negative threes.  Note that counters are <strong>not</strong> reset for each sublist, or at any point, and so should use the default '0' as their base point.
</p>
<ul>
<li><span>list item</span></li>
<li><span>list item</span></li>
<li><span>list item</span>
<ul>
<li><span>list item</span></li>
<li><span>list item</span></li>
<li><span>list item</span>
<ul>
<li><span>list item</span></li>
</ul>
</li>
<li><span>list item</span></li>
<li><span>list item</span>
<ul>
<li><span>list item</span></li>
<li><span>list item</span></li>
<li><span>list item</span></li>
<li><span>list item</span></li>
</ul>
</li>
<li><span>list item</span></li>
<li><span>list item</span></li>
</ul>
</li>
<li><span>list item</span></li>
<li><span>list item</span></li>
</ul>
<hr>[<A HREF="index.html">index page</A>] [<I>section</I>] [<A HREF="sec12-05a.htm">Previous</A>] [<A HREF="sec12-06-01.htm">Next</A>] [<A HREF="http://www.w3.org/TR/CSS2/generate.html#propdef-counter-increment">Specification</A>]
<HR>
</body>
</html>
