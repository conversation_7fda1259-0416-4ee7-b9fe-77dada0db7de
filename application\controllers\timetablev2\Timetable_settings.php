<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>chhipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Timetable_settings extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE')  || (!$this->authorization->isAuthorized('TIMETABLE.SETTINGS'))) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('timetablev2/timetable_settings_model','timetable_settings_model');  
  }

  public function index() {
    $site_url = site_url();

    $data['tiles'] = array();
    $data['tiles'] = array(
      [
        'title' => 'Settings',
        'sub_title' => 'Activate or inactivate Timetable',
        'icon' => 'svg_icons/settings.svg',
        'url' => $site_url.'timetablev2/timetable_settings/general_settings',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.SETTINGS')
      ],
      [
        'title' => 'Staff Online class links',
        'sub_title' => 'Add staff online class links to staff',
        'icon' => 'svg_icons/staffonlineclasslinks.svg',
        'url' => $site_url.'timetablev2/timetable_settings/staff_class_links',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.SETTINGS')
      ],
      [
        'title' => 'Section Online class links',
        'sub_title' => 'Add section online class links to staff',
        'icon' => 'svg_icons/sectiononlineclasslink.svg',
        'url' => $site_url.'timetablev2/timetable_settings/section_class_links',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.SETTINGS')
      ],
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'timetablev2/settings/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'timetablev2/settings/index_mobile';
    }else{
      $data['main_content']    = 'timetablev2/settings/index';
    }

    $this->load->view('inc/template', $data);
  }



  //Landing function to show the timetable menu
  public function staff_class_links() {

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'timetablev2/settings/staff_class_link_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'timetablev2/settings/staff_class_link_mobile';
    }else{
      $data['main_content']    = 'timetablev2/settings/staff_class_link';
    }    
    
    $this->load->view('inc/template', $data);
  }

  public function fetch_staff_class_links(){
    $staff_list = $this->timetable_settings_model->get_class_links();
    $data['staff_list'] = $staff_list;
    //echo "<pre>"; print_r($staff_list);die();
    echo json_encode($data);
  }

  public function save_staff_class_links(){
    $staff_id = $_POST['staff_id'];
    $platform = $_POST['platform'];
    $class_link = $_POST['class_link'];
    $info = $_POST['info'];
    $data = $this->timetable_settings_model->save_class_links($staff_id,$class_link,$platform,$info);
    //echo "<pre>"; print_r($staff_workload);die();
    echo json_encode($data);
  }

  public function general_settings() {

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'timetablev2/settings/template_selection_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'timetablev2/settings/template_selection_mobile';
    }else{
      $data['main_content']    = 'timetablev2/settings/template_selection';
    }

    $this->load->view('inc/template', $data);
  }

  public function fetch_templates_data(){
    $template_list = $this->timetable_settings_model->get_template_details();
    $data['template_list'] = $template_list;
    //echo "<pre>"; print_r($staff_workload);die();
    echo json_encode($data);
  }

  public function update_show_weekday() {
    $data = $this->timetable_settings_model->update_show_weekday($_POST['template_id'], $_POST['week_day'], $_POST['status']);
    echo json_encode($data);
  }

  public function update_show_class_link() {
    $data = $this->timetable_settings_model->update_show_class_link($_POST['template_id'], $_POST['status']);
    echo json_encode($data);
  }

  public function update_show_online_class_info() {
    $data = $this->timetable_settings_model->update_show_online_class_info($_POST['template_id'], $_POST['status']);
    echo json_encode($data);
  }

  public function update_show_section_links_instead() {
    $data = $this->timetable_settings_model->update_show_section_links_instead($_POST['template_id'], $_POST['status']);
    echo json_encode($data);
  }
    
  public function update_enable_intime_oc_button() {
    $data = $this->timetable_settings_model->update_enable_intime_oc_button($_POST['template_id'], $_POST['status']);
    echo json_encode($data);
  }
    
  public function update_show_staff_in_parent_tt() {
    $data = $this->timetable_settings_model->update_show_staff_in_parent_tt($_POST['template_id'], $_POST['status']);
    echo json_encode($data);
  }

  public function update_show_staff_in_parent_tt_button_checkbox() {
    $data = $this->timetable_settings_model->update_show_staff_in_parent_tt_button_checkbox($_POST['template_id'], $_POST['status']);
    echo json_encode($data);
  }
  
  public function inactivate_template(){
    $template_id = $_POST['template_id'];
    $data = $this->timetable_settings_model->inactivate_template($template_id);
    //echo "<pre>"; print_r($staff_workload);die();
    echo json_encode($data);
  }

  public function activate_template(){
    $template_id = $_POST['template_id'];
    $data['class_names'] = $this->timetable_settings_model->activate_template($template_id);
    //echo "<pre>"; print_r($data);die();
    echo json_encode($data);
  }

  public function section_class_links() {
    $acad_year_id = $this->acad_year->getAcadYearId();
    //echo "<pre>"; print_r($acad_year_id);die();

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'timetablev2/settings/section_class_link_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'timetablev2/settings/section_class_link_mobile';
    }else{
      $data['main_content']    = 'timetablev2/settings/section_class_link';
    }    

    
    $this->load->view('inc/template', $data);
  }

  public function fetch_section_class_links(){
    $acad_year_id = $this->acad_year->getAcadYearId();
    $section_list = $this->timetable_settings_model->get_section_links($acad_year_id);
    $data['section_list'] = $section_list;
    //echo "<pre>"; print_r($staff_list);die();
    echo json_encode($data);
  }

  public function save_section_class_links(){
    $class_section_id = $_POST['class_section_id'];
    $platform = $_POST['platform'];
    $class_link = $_POST['class_link'];
    $info = $_POST['info'];
    $data = $this->timetable_settings_model->save_section_class_links($class_section_id,$class_link,$platform,$info);
    //echo "<pre>"; print_r($staff_workload);die();
    echo json_encode($data);
  }


}