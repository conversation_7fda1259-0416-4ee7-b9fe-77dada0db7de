<?php

class Student_tracking_controller extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_TRACKING')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT_TRACKING.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('student_tracking/Student_tracking_model','Student_tracking_model');
  }

  public function index() {
    $site_url = site_url();

    $data['track_students_tiles'] = array(
      [
        'title' => 'Check-in Student/Parent ',
        'sub_title' => 'Check-in students and parents',
        'icon' => 'svg_icons/vendormaster.svg',
        'url' => $site_url.'escort_management/escort_controller/check_in__student',
        'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.MANAGE_ESCORTS')
      ],
      [
        'title' => 'Check-out Student/Parent',
        'sub_title' => 'Escort Student with Parent',
        'icon' => 'svg_icons/vendormaster.svg',
        'url' => $site_url.'escort_management/escort_controller/escort_student',
        'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.MANAGE_ESCORTS')
      ],
      // [
      //   'title' => 'Check-out Approvals',
      //   'sub_title' => 'Check in students with Parent',
      //   'icon' => 'svg_icons/vendormaster.svg',
      //   'url' => $site_url.'escort_management/escort_controller/pending_students',
      //   'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.MANAGE_ESCORTS')
      //   // 'permission' => $this->authorization->isSuperAdmin()
      // ],
      [
        'title' => 'Check Out Visitor',
        'sub_title' => 'Fast Checkout Visitors',
        'icon' => 'svg_icons/vendormaster.svg',
        'url' => $site_url.'escort_management/escort_controller/escort_temp_rfid_person',
        'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.MANAGE_ESCORTS')
        // 'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Facility Entry/Exit',
        'sub_title' => 'Track Students at Facility Entry/Exit',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_tracking/student_tracking_controller/display_device_roles',  
        'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.FACILITY_ENTRY')
        // 'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Requested Status (Today)',
        'sub_title' => 'Pending/Rejected/Approved Status',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_tracking/student_tracking_controller/display_status',  
        'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.MANAGE_ESCORTS')
        // 'permission' => $this->authorization->isSuperAdmin()
      ]
    );
    $data['tiles'] = checkTilePermissions($data['track_students_tiles']);

    $data['report_tiles']= array(
      [
        'title' => 'Escort Report',
        'sub_title' => 'Escort Report',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'escort_management/escort_controller/escort_report_v2',
        'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.VIEW_REPORTS')
      ],
      [
        'title' => 'Student Check-in Report',
        'sub_title' => 'Student Check-in Report',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_tracking/student_tracking_controller/student_check_in_report',  
        'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.VIEW_REPORTS')
      ],
      [
        'title' => 'Individual Student Report',
        'sub_title' => 'Individual Student Report',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_tracking/student_tracking_controller/student_report',  
        // 'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.VIEW_REPORTS')
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Student Tracking Analysis Report',
        'sub_title' => 'Student Tracking Analysis Report',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_tracking/student_tracking_controller/student_tracking_analysis_report',  
        'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.VIEW_REPORTS')
        // 'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Student Device Report',
        'sub_title' => 'Student Device Report',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_tracking/student_tracking_controller/student_device_report',  
        'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.VIEW_REPORTS')
        // 'permission' => $this->authorization->isSuperAdmin()
      ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['admin_tiles'] = array(
      [
        'title' => 'Manage Biometric Devices',
        'sub_title' => 'Manage Devices',
        'icon' =>'svg_icons/configmanagement.svg',
        'url' => $site_url.'student_tracking/student_tracking_controller/manage_devices',  
        'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.TRACKING_ADMIN')
      ],
      [
        'title' => 'Manage RFID Devices',
        'sub_title' => 'Manage RFID Devices',
        'icon' =>'svg_icons/configmanagement.svg',
        'url' => $site_url.'student_tracking/student_tracking_controller/manage_rfid_devices',  
        'permission' => $this->authorization->isAuthorized('STUDENT_TRACKING.TRACKING_ADMIN')
      ]
    );
    $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'student_tracking/student_tracking_menu_mobile';
    }else if ($this->mobile_detect->isMobile()) {
      $data['main_content']    = 'student_tracking/student_tracking_menu_mobile';
    }else {
      $data['main_content']    = 'student_tracking/student_tracking_menu';
    }
    $this->load->view('inc/template', $data);
  }

  public function student_check_in_report(){
    $data['class_section'] = $this->Student_tracking_model->get_class_names();
    // echo '<pre>';print_r($data);die();
    $data['main_content']    = 'student_tracking/student_check_in_report';
    $this->load->view('inc/template', $data);
  }

  public function student_report(){
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'student_tracking/student_report_mobile';
    }else if ($this->mobile_detect->isMobile()) {
      $data['main_content']    = 'student_tracking/student_report_mobile';
    }else {
      $data['main_content']    = 'student_tracking/student_report';
    }

    $this->load->view('inc/template', $data);
  }

  public function student_tracking_analysis_report(){
    $data['main_content']    = 'student_tracking/student_tracking_analysis_report';
    $this->load->view('inc/template', $data);
  }

   public function student_device_report(){
    $data['main_content']    = 'student_tracking/student_device_report';
    $this->load->view('inc/template', $data);
  }

  public function grade_wise_student_tracking_count(){
    $result = $this->Student_tracking_model->grade_wise_student_tracking_count();
    echo json_encode($result);
  }

  public function get_device_report(){
    $result = $this->Student_tracking_model->get_device_report($_POST);
    echo json_encode($result);
  }

  public function student_wise_student_tracking_count(){
    $result = $this->Student_tracking_model->student_wise_student_tracking_count();
    echo json_encode($result);
  }

  public function get_student_tracking_details(){
    // echo "<pre>"; print_r($_POST); die();
    $recordType=$_POST["recordType"];

    if($recordType=="Transport"){
      $result=$this->Student_tracking_model->get_student_tracking_details_from_transport($_POST);
      // echo '<pre>result ';print_r($result); die();
    }else if($recordType=="Escort"){
      $result=$this->Student_tracking_model->get_student_tracking_details_from_escort($_POST);
    }else if($recordType=="RFID"){
      $result=$this->Student_tracking_model->get_student_tracking_details_from_rfid($_POST);
    }else if($recordType=="Biometric" || $recordType=="Boys Hostel" || $recordType=="Girls Hostel"){
      $result=$this->Student_tracking_model->get_student_tracking_details_from_biometric($_POST);
      // echo "<pre>"; print_r($result); die();
    } 
    else if($recordType=="Not Checked In") {
      $result= new stdClass();
    }

    //

    echo json_encode($result);
  }

  public function manage_devices(){
    $data['main_content']    = 'student_tracking/manage_devices';
    $this->load->view('inc/template', $data);
  }

  public function display_device_roles(){
    $data['devices'] = $this->Student_tracking_model->display_devices();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'student/student_registration/tablet_index';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] ='student_tracking/display_device_roles_mobile';
    } else {
      $data['main_content'] ='student_tracking/display_device_roles';
    }
    $this->load->view('inc/template', $data);
  }
  
  public function get_Student_trackingData(){
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));
    $class_id = $_POST['class_id'];
    $section_id = $_POST['section_id'];
    $punch_type = $_POST['punch_type'];
   
    $data['dates'] = $this->_getDatesByRange($from_date, $to_date);
    $student = $this->Student_tracking_model->getStudentData();
    $student_names = array();
    foreach ($student as $stu) {
      $student_names[$stu->id] = $stu->student_name;
    }
    $student_ids = $this->Student_tracking_model->getStudentId();

    $attendance = $this->Student_tracking_model->getAttendance_ReportByDate($from_date, $to_date,$class_id, $section_id,$punch_type,$student_ids);
    
    $student_attendance = [];
      foreach ($attendance as $i => $att) {
        if(!array_key_exists($att->student_id, $student_attendance)) {
          $student_attendance[$att->student_id] = array();
          $student_attendance[$att->student_id]['student_name'] = $student_names[$att->student_id];
          $student_attendance[$att->student_id]['class_section'] = $att->class_section;
          $student_attendance[$att->student_id]['student_id'] = $att->student_id;
          $student_attendance[$att->student_id][$att->punch_date] = $att;
        }
        $student_attendance[$att->student_id][$att->punch_date] = $att;
      }
      
      $data['attendance'] = [];
      foreach ($student_attendance as $key => $value) {
        $data['attendance'][] = $value;
      }
    // echo '<pre>';print_r($data);die();
    echo json_encode( $data);
  }

  private function _getDatesByRange($from_date, $to_date, $format="d-m-Y") {
    $dates = [];
    $from = $from_date;
    while($from != $to_date) {
      $dates[] = array(
        'format1' => date('d-m-Y', strtotime($from)),
        'format2' => date('d M Y', strtotime($from))
      );
      $from = date('Y-m-d', strtotime("$from +1 days"));
    }
    $dates[] = array(
      'format1' => date('d-m-Y', strtotime($from)),
      'format2' => date('d M Y', strtotime($from))
    );
    return $dates;
  }

  public function get_class_section_names(){
    $result = $this->Student_tracking_model->get_class_names();
    echo json_encode($result);
   
  }

  public function get_class_section_student_data(){
    $cls_section = $this->input->post('cls_section');
    list($class_id,$sectionId) = explode('_',$cls_section);
    $result = $this->Student_tracking_model->get_class_section_student_data($class_id,$sectionId);
    echo json_encode($result);
  }

  public function get_student_attendance_current_month_data(){
    $student_biometric_code=$_POST["studentBiometricCode"];

    $transport_data_in= $this->Student_tracking_model->get_transport_checkin( $student_biometric_code);
    $transport_data_out= $this->Student_tracking_model->get_transport_checkout( $student_biometric_code);
    $escort_in_data= $this->Student_tracking_model->get_escorts_data_checkin( $student_biometric_code);
    $escort_out_data= $this->Student_tracking_model->get_escorts_data_checkout( $student_biometric_code);
    $rfid_data= $this->Student_tracking_model->get_rfid_punch( $student_biometric_code);
    $biometric_data = $this->Student_tracking_model->get_attendence_data( $student_biometric_code);

    
    $students_data=[];
    foreach($transport_data_in as $key => $val){
      if($val->date){
        $students_data[$val->date][local_time($val->time)]=$val;
        $students_data[$val->date][local_time($val->time)]->time=date('H:i A',strtotime(local_time($val->time)));
      }
    }

    foreach($transport_data_out as $key => $val){
      if($val->date){
        $students_data[$val->date][local_time($val->time)]=$val;
        $students_data[$val->date][local_time($val->time)]->time=date('H:i A',strtotime(local_time($val->time)));
      }
    }

    foreach($escort_in_data as $key => $val){
      if($val->date){
        $students_data[$val->date][local_time($val->time)]=$val;
        $students_data[$val->date][local_time($val->time)]->time=date('H:i A',strtotime(local_time($val->time)));
      }
    }

    foreach($escort_out_data as $key => $val){
      if($val->date){
        $students_data[$val->date][local_time($val->time)]=$val;
        $students_data[$val->date][local_time($val->time)]->time=date('H:i A',strtotime(local_time($val->time)));
      }
    }

    foreach($rfid_data as $key => $val){
      if($val->date){
        $students_data[$val->date][local_time($val->time)]=$val;
        $students_data[$val->date][local_time($val->time)]->time=date('H:i A',strtotime(local_time($val->time)));
      }
    }

    foreach($biometric_data as $key => $val){
      if($val->date){
        $students_data[$val->date][local_time($val->time)]=$val;
        $students_data[$val->date][local_time($val->time)]->time=date('H:i A',strtotime(local_time($val->time)));
      }
    }
    
    echo json_encode($students_data);
  }

  public function add_device(){
    echo $this->Student_tracking_model->add_device($_POST);
  }

  public function get_device_types(){
    $result = $this->Student_tracking_model->get_device_types();
    echo json_encode($result);
  }

  public function deactivate_device(){
    $id = $_POST['id'];
    echo $this->Student_tracking_model->deactivate_device($id);
  }

  public function activate_device(){
    $id = $_POST['id'];
    echo $this->Student_tracking_model->activate_device($id);
  }

  public function update_device_data(){
    echo $this->Student_tracking_model->update_device_data($_POST);
  }

  public function get_section_names(){
    // echo '<pre>';print_r($_POST);die();
    $class_id = $_POST['class_id'];
    $result = $this->Student_tracking_model->get_section_names($class_id);
    // echo '<pre>';print_r($result);die();
    echo json_encode($result);
  }

  public function open_page_to_track($dev_id='',$purpose=''){
    $data['dev_id'] = $dev_id;
    $data['purpose'] = $purpose;
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'student/student_registration/tablet_index';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] ='student_tracking/track_student_via_rfid_mobile';
    } else {
      $data['main_content'] ='student_tracking/track_student_via_rfid';
    }
    $this->load->view('inc/template', $data);
  }
  public function get_student_det_from_rfid(){
    $rfid_number = $_POST['rfid_number'];
    $res = $this->Student_tracking_model->get_student_det_from_rfid($rfid_number);
    echo json_encode($res);
  }
  public function store_rfid(){
    $rfid_number = $_POST['rfid_number'];
    $source = $_POST['source'];
    $source_id = $_POST['source_id'];
    $res = $this->Student_tracking_model->store_rfid($rfid_number, $source, $source_id);
    echo json_encode($res);
  }

  public function manage_rfid_devices() {
    $data['devices']= $this->Student_tracking_model->get_all_rfid_evices();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'student_tracking/manage_rfid_devices_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] ='student_tracking/manage_rfid_devices_mobile';
    } else {
      $data['main_content'] ='student_tracking/manage_rfid_devices';
    }
    
    $this->load->view('inc/template', $data);
  }

  public function add_rfid_device() {
    $name = $_POST['name'];
    $purpose = $_POST['purpose'];
    $description = $_POST['description'];
    $display_color = $_POST['display_color'];
    $res = $this->Student_tracking_model->add_rfid_device($name, $purpose, $description, $display_color);
    echo json_encode($res);
  }

  public function change_status_rfid() {
    $status = $_POST['status'];
    $id = $_POST['id'];
    $res = $this->Student_tracking_model->change_status_rfid($status, $id);
    echo json_encode($res);
  }
  public function delete_device_rfid() {
    $id = $_POST['id'];
    $res = $this->Student_tracking_model->delete_device_rfid($id);
    echo json_encode($res);
  }

  public function take_person_to_all_devices($rfid_number) {
    $data['student_id']= $this->Student_tracking_model->take_person_to_all_devices($rfid_number);
    if($data['student_id'] != '-1') {
      $data['devices']= $this->Student_tracking_model->get_active_rfid_devices();
    }

    // if ($this->mobile_detect->isTablet()) {
    //   $data['main_content']   = 'student_tracking/take_person_to_all_devices_tablet';
    // } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] ='student_tracking/take_person_to_all_devices_mobile';
    // } else {
    //   $data['main_content'] ='student_tracking/take_person_to_all_devices';
    // }
    
    $this->load->view('inc/template', $data);

  }

  public function take_person_to_select_device($student_id) {
    $data['student_id']= $this->Student_tracking_model->take_person_to_all_devices_by_id($student_id);
    if($data['student_id'] != '-1') {
      $data['devices']= $this->Student_tracking_model->get_active_rfid_devices();
    }

    // if ($this->mobile_detect->isTablet()) {
    //   $data['main_content']   = 'student_tracking/take_person_to_all_devices_tablet';
    // } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] ='student_tracking/take_person_to_all_devices_mobile';
    // } else {
    //   $data['main_content'] ='student_tracking/take_person_to_all_devices';
    // }
    
    $this->load->view('inc/template', $data);

  }

  public function entry_exit_person() {
    $res = $this->Student_tracking_model->entry_exit_person();
    echo json_encode($res);
  }

  public function take_person_to_all_devices_by_name($input_name) {
    $data['student_id']= $this->Student_tracking_model->take_person_to_all_devices_by_name($input_name);

    // echo '<pre>'; print_r($data['student_id']); die();
    

    // if ($this->mobile_detect->isTablet()) {
    //   $data['main_content']   = 'student_tracking/take_person_to_all_devices_by_name_tablet';
    // } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] ='student_tracking/take_person_to_all_devices_by_name_mobile';
    // } else {
    //   $data['main_content'] ='student_tracking/take_person_to_all_devices_by_name';
    // }
    
    $this->load->view('inc/template', $data);

  }

  public function display_status() {
    $data['todays_requests']= $this->Student_tracking_model->get_todays_requests();

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student_tracking/display_status';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student_tracking/display_status';
    } else {
      $data['main_content'] = 'student_tracking/display_status_desktop';
    }
	// echo "<pre>"; print_r($data['todays_requests']);
  //   die();

	$this->load->view('inc/template', $data);
  }

  public function approve_by_staff() {
    $res = $this->Student_tracking_model->approve_by_staff();
    echo json_encode($res);
  }

  public function get_device_color_information() {
    $res = $this->Student_tracking_model->get_device_color_information();
    echo json_encode($res);
  }

}