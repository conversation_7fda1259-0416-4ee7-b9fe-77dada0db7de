<?php

class Outgoing_student_controller extends CI_Controller {
	function __construct() {
		parent::__construct();
	    $this->load->model('outgoing_register_model');  
	}

    public function index() {
        $data['results']=$this->outgoing_register_model->get_list_info_outgoing_student();
        $data['main_content']    = 'outgoing/index';
        $this->load->view('inc/template', $data);
    }

    public function add() {  
        $data['listOfClass']=$this->outgoing_register_model->getSectionList();
        $data['main_content']    = 'outgoing/add';
        $this->load->view('inc/template', $data);
    }

    public function submit() {
        $result=$this->outgoing_register_model->submit();
        if($result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Submitted');
            redirect('outgoing_student_controller/outgoing_registration');
        }
    }

    public function studentList() {
        $selectclsId=$_POST['selectclsId'];
        $studentList=$this->outgoing_register_model->getStudentList($selectclsId);
        echo json_encode($studentList);
    }

    public function getParentInfo() {
        $selectclsId=$_POST['selectclsId'];
        $student_name_id=$_POST['student_name_id'];
        $parrnt=$_POST['parent'];
        $result=$this->outgoing_register_model->getParentInfo($selectclsId,$student_name_id,$parrnt);
        echo json_encode($result);
    }

    public function vistor_info() {
        $visitors_detail=$this->outgoing_register_model->vistor_details();
        echo json_encode($visitors_detail);  
    }

    public function vistor_img() {
        $other_name=$_POST["other_name"];
        $visitors_img=$this->outgoing_register_model->vistor_img( $other_name);
        echo json_encode($visitors_img);  
    }
}
?>