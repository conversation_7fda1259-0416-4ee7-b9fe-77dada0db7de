<?php

/**
 * Description of Student_menu
 *
 * <AUTHOR>
 */
class Staff_menu extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STAFF_MASTER')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STAFF.MODULE')) {
      redirect('dashboard', 'refresh');
    }
  }

  function index() {
    $data['permitStaffAdd'] = $this->authorization->isAuthorized('STAFF.ADD_EDIT_DELETE');
    $data['permitStaffObservation'] = $this->authorization->isAuthorized('STAFF_OBSERVATION.MODULE');
    $data['permitStaffView'] = $this->authorization->isAuthorized('STAFF.VIEW');
    $data['permit_staff_report_view'] = $this->authorization->isAuthorized('STAFF.VIEW_REPORT');
    $data['leave_permit'] = $this->authorization->isAuthorized('LEAVE.LEAVE_REPORT');
    $data['staff_initiative_permit'] = $this->authorization->isAuthorized('STAFF_INITIATIVE.MODULE');
    $data['leave_approval'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPROVE');
    $data['permitStaffMassupdate'] = $this->authorization->isAuthorized('STAFF_MASSUPDATE.MODULE');
    $data['permitStaffRM'] = $this->authorization->isAuthorized('STAFF.REPORTING_MANAGER_ADMIN');
    $data['permitShiftMaster'] = $this->authorization->isAuthorized('STAFF.SHIFTS');
    $data['permitStaffShift'] = $this->authorization->isAuthorized('STAFF.STAFF_SHIFTS');
    $data['permitStaffDocument'] = $this->authorization->isAuthorized('STAFF.STAFF_DOCUMENT_REPORT');
    $staff_attendance_type = $this->settings->getSetting('staff_attendance_type');
    $data['permitStaffAttendance'] = 0;
    $data['permitStaffCodesMissing'] = 0;
    $data['permitStaffManualAttendance'] = 0;
    if($staff_attendance_type == 'biometric') {
      $data['permitStaffAttendance'] = $this->authorization->isAuthorized('STAFF.ATTENDANCE');
      $data['permitStaffCodesMissing'] = $this->authorization->isAuthorized('STAFF.ATTENDANCE_MISSING_CODES');
    } else {
      $data['permitStaffManualAttendance'] = $this->authorization->isAuthorized('STAFF.ATTENDANCE');
    }
    $data['staffCount'] = $this->db->select('count(s.id) as staffCount')->from('staff_master s')->where('s.status', 2)->get()->row()->staffCount;

    $site_url = site_url();
    $data['tiles'] = array(
        [
          'title' => 'Add Staff',
          'sub_title' => 'Add a new staff',
          'icon' => 'svg_icons/add.svg',
          'url' => $site_url.'staff/Staff_controller/addStaff',
          'permission' => $this->authorization->isAuthorized('STAFF.ADD_EDIT_DELETE')
        ],
        [
          'title' => 'View Staff',
          'sub_title' => 'Perform operations on individual staff',
          'icon' => 'svg_icons/view.svg',
          'url' => $site_url.'staff/Staff_controller',
          'permission' => $this->authorization->isAuthorized('STAFF.VIEW')
        ],
        [
          'title' => 'Observations',
          'sub_title' => 'Staff Observations',
          'icon' => 'svg_icons/observation.svg',
          'url' => $site_url.'staff/Observation',
          'permission' => $this->authorization->isAuthorized('STAFF_OBSERVATION.MODULE')
        ],
        [
          'title' => 'Staff Mass Update',
          'sub_title' => 'Staff Mass Update',
          'icon' => 'svg_icons/massupdate.svg',
          'url' => $site_url.'staff/staff_massupdate',
          'permission' => $this->authorization->isAuthorized('STAFF_MASSUPDATE.MODULE')
        ],
        [
          'title' => 'Biometric Attendance',
          'sub_title' => 'Staff Biometric Attendance',
          'icon' => 'svg_icons/attendance.svg',
          'url' => $site_url.'staff/staff_attendance',
          'permission' => $data['permitStaffAttendance']
        ],
        [
          'title' => 'Missing Attendance',
          'sub_title' => 'View & Update Missing Staff Attendance Codes',
          'icon' => 'svg_icons/faq.svg',
          'url' => $site_url.'staff/staff_attendance/missingAttendance',
          'permission' => $data['permitStaffCodesMissing']
        ],
        [
          'title' => 'Manual Attendance',
          'sub_title' => 'View & Update Staff Attendance',
          'icon' => 'svg_icons/attendance.svg',
          'url' => $site_url.'staff/staff_attendance/manualAttendance',
          'permission' => $data['permitStaffManualAttendance']
        ],
        [
          'title' => 'Manage Reporting Managers',
          'sub_title' => 'View & update staff reporting managers',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'staff/Staff_controller/staff_reportings',
          'permission' => $data['permitStaffRM']
        ],
        [
          'title' => 'Assign Staff Code',
          'sub_title' => 'Assign staff code to the staff',
          'icon' => 'svg_icons/staffqrcode.svg',
          'url' => $site_url.'staff/Staff_controller/staffCodePage',
          'permission' => $this->authorization->isSuperAdmin()
        ],
        [
          'title' => 'Mass Document Upload',
          'sub_title' => 'Mass Document Upload',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'staff/Staff_controller/mass_document_upload',
          'permission' => $this->authorization->isAuthorized('STAFF_MASSUPDATE.MASS_DOCUMENT_UPLOAD')
        ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

     $data['admin_tiles'] = array(
      [
        'title' => 'Configure Staff Fields for Display (Staff Side)',
        'sub_title' => 'Staff Profile Display Fields',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url . 'staff/Staff_profile_view_controller/staff_profile_display_fiedls',
        'permission' => $this->authorization->isAuthorized('STAFF.MANAGE_PROFILE_DISPLAY')
      ],
      [
        'title' => 'Configure Staff Fields for Edit (Staff Side)',
        'sub_title' => 'Staff Profile Enabled Edit Fields',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url.'staff/Staff_profile_view_controller/staff_profile_edit_enabled_fiedls',
        'permission' => $this->authorization->isAuthorized('STAFF.MANAGE_PROFILE_EDITS')
      ],
      [
        'title' => 'Configure Staff Fields (Admin Side)',
        'sub_title' => 'Staff Profile Display Required Fields',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url . 'staff/Staff_profile_view_controller/staff_profile_display_required_fields',
        'permission' => $this->authorization->isAuthorized('STAFF.MANAGE_PROFILE_DISPLAY'),
      ],
      
      [
        'title' => 'Approve Attribute Updates',
        'sub_title' => 'Staff Approved Status',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url.'staff/Staff_controller/get_staff_edit_approved_list',
        'permission' => $this->authorization->isAuthorized('STAFF.APPROVE_ATTRIBUTE_UPDATES')
      ],
      [
        'title' => 'Manage Staff Designations',
        'sub_title' => 'Add / Update Designations',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url.'staff/Staff_controller/manage_staff_designations',
        'permission' => $this->authorization->isAuthorized('STAFF.MANAGE_DESIGNATIONS')
      ],
      [
        'title' => 'Manage Staff Departments',
        'sub_title' => 'Add / Update Staff Departments',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url.'staff/Staff_controller/manage_staff_departments',
        'permission' => $this->authorization->isAuthorized('STAFF.MANAGE_DEPARTMENTS')
      ],
      [
        'title' => 'Configure Staff-side Fields',
        'sub_title' => 'Configure Staff-side Fields',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url.'staff/Staff_profile_view_controller/configure_staff_side_fields',
        'permission' => $this->authorization->isAuthorized('STAFF.CONFIGURE_STAFF_PROFILE_FIELDS')
      ],
      [
        'title' => 'Lock/Unlock Staff Profile Confirmation',
        'sub_title' => 'Lock/Unlock Staff Profile Confirmation',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url.'staff/Staff_profile_view_controller/configure_lock_unlock_staff_profile',
        'permission' => $this->authorization->isAuthorized('STAFF.CONFIGURE_LOCK_UNLOCK_STAFF_PROFILE')
      ]
    );
    $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);

    $data['report_tiles'] = array(
        [
          'title' => 'Staff Report',
          'sub_title' => 'View/Print Staff Data',
          'icon' => 'svg_icons/staffreport.svg',
          'url' => $site_url.'reports/staff/staffreport_controller/staff_report',
          'permission' => $this->authorization->isAuthorized('STAFF.VIEW_REPORT')
        ],
        [
          'title' => 'Attendance Report',
          'sub_title' => 'View Staff Attendance',
          'icon' => 'svg_icons/staffsummaryreport.svg',
          'url' => $site_url.'staff/staff_attendance/report',
          'permission' => $data['permitStaffManualAttendance']
        ],
         [
          'title' => 'Staff Documents Report',
          'sub_title' => 'View Staff All Documents',
          'icon' => 'svg_icons/staffsummaryreport.svg',
          'url' => $site_url.'staff/staff_controller/staff_docs_report',
          'permission' => $data['permitStaffDocument']
         ],
        [
        'title' => 'Staff Audit Log  Report',
        'sub_title' => 'View Staff Audit Log Report',
        'icon' => 'svg_icons/staffsummaryreport.svg',
        'url' => $site_url . 'reports/staff/staffreport_controller/staff_audit_log_report',
        'permission' => $this->authorization->isAuthorized('STAFF.VIEW_AUDIT_REPORT')
        ],
        [
        'title' => 'Manage Staff Document Types',
        'sub_title' => 'Manage Staff Document Types',
        'icon' => 'svg_icons/staffsummaryreport.svg',
        'url' => $site_url . 'reports/staff/staffreport_controller/manage_staff_documents_types',
        'permission' => $this->authorization->isSuperAdmin()
        ],
        [
          'title' => 'Staff Data Missing Report',
          'sub_title' => 'Staff Data Missing Report',
          'icon' => 'svg_icons/staffqrcode.svg',
          'url' => $site_url.'reports/staff/staffreport_controller/staff_data_missing_report',
          'permission' => $this->authorization->isAuthorized('STAFF.STAFF_DATA_MISSING_REPORT')
        ],
        [
          'title' => 'Staff Edit History Report',
          'sub_title' => 'Staff Edit History Report',
          'icon' => 'svg_icons/staffqrcode.svg',
          'url' => $site_url.'reports/staff/staffreport_controller/staff_edit_history_report',
          'permission' => $this->authorization->isAuthorized('STAFF.STAFF_EDIT_HISTORY_REPORT')
        ]      
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/menu/index_tablet.php';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/menu/index_mobile.php';
    }else{
      $data['main_content'] = 'staff/menu/index.php';   	
    }
    
    $this->load->view('inc/template', $data);
  }

}