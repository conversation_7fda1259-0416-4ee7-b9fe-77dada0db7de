<?php

class Ptm_controller extends CI_Controller 
{

	function __construct()
	{
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isModuleEnabled('PTM')) {
        redirect('dashboard', 'refresh');
      }	
      $this->load->model('ptm_model');
      $this->load->model('parent_model');
	}

  public function ptm(){
    $data['ptm_list'] = $this->ptm_model->get_ptmdetailsAll();
    $data['main_content']    = 'ptm/index';
    $this->load->view('inc/template', $data);
  }
  public function add(){
    $data['class_section'] = $this->ptm_model->get_AllClassandSection();
    $data['main_content']    = 'ptm/add';
    $this->load->view('inc/template', $data);
  }
  public function ptm_creation()
  {
    $result = $this->ptm_model->insert_ptm_details();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Insert Successfully');
      redirect('ptm_controller/ptm');
    }else{
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('ptm_controller/ptm');
    }
  }

  public function edit($id)
  {
    $data['edit'] = $this->ptm_model->editdatabyId($id);
    //echo "<pre>"; print_r($data['edit']); die();
    $data['class_section'] = $this->ptm_model->get_AllClassandSection();
    $data['main_content']    = 'ptm/edit';
    $this->load->view('inc/template', $data);
  }

  public function ptm_update($id)
  {
    $update = $this->ptm_model->update_ptm_details($id);
    if ($update) {
      $this->session->set_flashdata('flashSuccess', 'Update Successfully');
      redirect('ptm_controller/ptm');
    }else{
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('ptm_controller/ptm');
    }
  }

  public function delete($id){
    $delete = $this->ptm_model->delete_ptm_details($id);
    if ($delete) {
      $this->session->set_flashdata('flashSuccess', 'Update Successfully');
      redirect('ptm_controller/ptm');
    }else{
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('ptm_controller/ptm');
    }
  }

  public function details_view($id){
    $data['view'] = $this->ptm_model->editdatabyId($id);
    $data['main_content']    = 'ptm/view';
    $this->load->view('inc/template', $data);
  }

  public function ptm_approved($id){
    $approved = $this->ptm_model->approved_ptm_details($id);
    if ($approved) {
      $this->session->set_flashdata('flashSuccess', 'Approved');
      redirect('ptm_controller/ptm');
    }else{
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('ptm_controller/ptm');
    }
  }

  public function ptm_rejected($id){
    $ptm_rejected = $this->ptm_model->rejected_ptm_details($id);
    if ($ptm_rejected) {
      $this->session->set_flashdata('flashSuccess', 'Rejected');
      redirect('ptm_controller/ptm');
    }else{
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('ptm_controller/ptm');
    }
  }

  public function report_ptm($id){
      $data['view'] = $this->ptm_model->editdatabyId($id);
      $data['history'] = $this->ptm_model->get_historyreport($id);
      $data['main_content']    = 'ptm/reports';
      $this->load->view('inc/template', $data);
  }

  public function ptm_report_closed($id){
    $report_closed = $this->ptm_model->closed_ptm_details($id);
    if ($report_closed) {
      $this->session->set_flashdata('flashSuccess', 'Successfully updated');
      redirect('ptm_controller/ptm');
    }else{
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('ptm_controller/ptm');
    }
  }

  public function submit_to_principal($id){
    $to_principal = $this->ptm_model->submit_to_principal($id);
    if ($to_principal) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Submitted to principal');
      redirect('ptm_controller/ptm');
    }else{
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('ptm_controller/ptm');
    }
  }

   // Parent Iniative

  public function parent_initiative_view(){
    $data['initiative_details'] = $this->ptm_model->get_initiativeDetailsAll();
    $data['title'] = 'Parent Initiative Report';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
    }else{
      $data['main_content'] = 'parent_initiative/index';
    }
    $this->load->view('inc/template', $data);
  }

  public function approved_parent_initiative_view($id){
    $data['initiative_details'] =$this->ptm_model->initiative_submitteddetailsALl($id);
    $studentId = $this->ptm_model->get_initivatedatabyAvatarParendId($data['initiative_details']->created_by);
    $data['studentData'] = $this->parent_model->getStudentDataById($studentId);
    $data['fatherData'] = $this->parent_model->getFatherDetails($studentId);
    $data['motherData'] = $this->parent_model->getMotherDetails($studentId);
    $data['main_content']    = 'parent_initiative/view';
    $this->load->view('inc/template', $data);
  }

  public function parent_initiative_submit_approved($id){
    $result = $this->ptm_model->approved_parent_initative_details($id);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully submited');
    }else{
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('ptm_controller/parent_initiative_view');
  }

}
?>