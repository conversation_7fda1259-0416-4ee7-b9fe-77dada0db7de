<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  16 March 2018
 *
 * Description: Controller for Timetable.
 *
 * Requirements: PHP5 or above
 *
 */

class Staff_timetable extends CI_Controller {
	function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
        redirect('dashboard', 'refresh');
      }
      $this->load->model('timetable/staff_tt');
      $this->load->model('timetable/timetable');
      $this->load->model('timetablev2/template_model','template_model');
  }
  // staff wise only can see time table 

  //Landing function for get my timetable
  public function index(){
    $avatarId =  $this->authorization->getAvatarId();
    // $data['pHeadArr'] = $this->timetable->getPeriodHeader('40');
    $data['staffObj'] = $this->staff_tt->get_staffIdbyAvatarId($avatarId);
    $data['csId'] = $this->staff_tt->getClassSectionIfClassTeacher();
    if ($this->mobile_detect->isMobile()) {
      $data['main_content']    = 'timetable/staff_timetable/index_mobile';
    } else {
      $data['main_content']    = 'timetable/staff_timetable/index';
    }

    //$data['main_content'] = 'timetable/staff_timetable/index';
    $this->load->view('inc/template', $data);
  }

  //Landing function to get all timetables
  public function displayAllStaffIndex(){
    // $data['pHeadArr'] = $this->timetable->getPeriodHeader('40');
    $data['staffAll'] = $this->staff_tt->get_allStaff();
    $data['main_content'] = 'timetable/staff_timetable/index';
    $this->load->view('inc/template', $data);
  }

  //Landing function to print all timetables
  public function printStaffTimetables(){

    $selectedStaffId = $this->input->post('selectedStaffId');

    // $data['pHeadArr'] = $this->timetable->getPeriodHeader('40');
    $data['staffAll'] = $this->staff_tt->get_allStaff();
    $data['selectedStaffIds'] = $selectedStaffId;
    $data['main_content'] = 'timetable/staff_timetable/print_staff_tt';
    $this->load->view('inc/template_fee', $data);
  }

  //Landing function for staff workload report
  public function staffWorkLoadReport() {
    $data['staffWorkload'] = $this->staff_tt->getStaffWorkload();
    //echo '<pre>';print_r($data['staffWorkload']);die();
    $data['main_content'] = 'reports/timetable/staff_workload_report';
    $this->load->view('inc/template_fee', $data);
  }

  public function getStaffTTById(){
   $staffId = $this->input->post('staffId');
   $result = $this->staff_tt->getStaffTTById($staffId);
   echo json_encode($result);
  }

  private function __getHeaderCSConfig () {
    $headerCS = $this->settings->getSetting('timetable_header_class_section_id');
    if ($headerCS == 0)
      $headerCS = 4;  //Initialize to a default class section.

    // $ttSetting = $this->settings->getSetting('timetable');
    // if (isset($ttSetting))
    //   $headerCS = $ttSetting['header_class_section_id'];
    // else
    //   $headerCS = 4; //Initialize to a default class section.

    return $headerCS;
  }

  public function getFullTTForStaff(){
    $staffId = $this->input->post('staffId');
    $staffTT = $this->staff_tt->getFullTTForStaff($staffId);
    $staffObj = $this->staff_tt->getStaffDetails($staffId);

    $headerTT = $this->timetable->getPeriodHeader($this->__getHeaderCSConfig());

    echo json_encode(array("staffObj"=>$staffObj, "staffTT" =>$staffTT, "headerTT" => $headerTT));
   }

   public function get_active_templates_details(){
    //echo '<pre>';print_r('entry');die();
    $data = $this->staff_tt->get_active_templates_details();
    //echo '<pre>';print_r($data);die();
    
    echo json_encode($data);
   }

   public function get_day_staff_timtable_v2(){

    $staffId = $this->input->post('staffId');
    $templateId = $this->input->post('templateId');
    $data = $this->staff_tt->get_day_staff_timtable_v2($staffId,$templateId);
    
    echo json_encode($data);
   }
   
   public function getPeriodHeader(){
    $result = $this->timetable->getPeriodHeader($this->__getHeaderCSConfig());
    echo json_encode($result);
   }

   public function getStaffTTSubstituteStaffById(){
    $staffId = $_POST['staffId'];
    $ttDate = $_POST['ttDate'];
    //$todayDate = date('Y-m-d');
    $date = date('Y-m-d',strtotime($ttDate));
    $weekDay = date("w", strtotime($date));
    $headerTT = $this->timetable->getPeriodHeader($this->__getHeaderCSConfig());
    //$weekNum = $date->format("W");
//    $staffAllo = $this->staff_tt->getStaffTTById($staffId);
    $staffObj = $this->staff_tt->getStaffDetails($staffId);
    $staffAllo = $this->staff_tt->getFullTTForStaff($staffId, $weekDay);
    // echo '<pre>';print_r($date);
    // echo '<pre>';print_r($weekDay);
    // $staffsub = $this->staff_tt->getCSStaffTTByWeekDaySusbstitute($staffId,$weekDay,$weekNum);
    $staffsub = $this->staff_tt->getCSStaffTTByWeekDaySusbstitute($staffId,$date);
    $old_staffId = $this->staff_tt->getData_isAssignStafftoOthers($staffId,$date);

    //echo '<pre>';print_r($old_staffId);die();

    echo json_encode(array("headerTT"=>$headerTT, "staffAlloc" =>$staffAllo, "subPeriodIn" => $staffsub, "subPeriodOut" => $old_staffId, "weekDay" => $weekDay, "staffObj" => $staffObj ));
    //echo 'here';
  }

  public function validateStaffTTIndex($staffId = '') {
    if (empty($staffId))
      $staffId = $this->input->post('selectedStaffId');

    $data['staffAll'] = $this->staff_tt->get_allStaff();
    $data['selectedStaffId'] = $staffId;
    $data['main_content'] = 'timetable/staff_timetable/validate_staff_tt_index';
    $this->load->view('inc/template', $data);
  }

  public function getStaffJSON () {
    $staffId = $_POST['staffId'];

    $data['staffTTJSON'] = $this->staff_tt->getStaffTTById($staffId, '1');
    // echo '<pre>';print_r($data['staffTTJSON']);die();
    $data['genStaffTTJSON'] = $this->generateJSONFromTT($staffId);

    echo json_encode($data);
  }

  public function generateJSONFromTT ($staffId) {
    $ttSetting = $this->settings->getSetting('timetable');
    //$newStaffJSON = $this->staff_tt->initStaffJSON($ttSetting['timetable_template_for_staff']);

    $staffTT = $this->staff_tt->getFullTTForStaff($staffId);
    //echo '<pre>';print_r($staffTT);die();
    
    //Construct the timetable
    $prevWd = 0;
    $isVisited = 0;
    $allocationData = array();
    $numPeriods = 0;
    foreach ($staffTT as $p) {
      //TODO: Consider allocation string
      if ($p->weekDay != $prevWd) {
        if ($prevWd != '0') {
          $allocationWeekDay['numPeriods'] = $numPeriods; 
          $allocationWeekDay['allocationStr'] = $allocationStr; 
          $allocationWeekDay['allocationDisplay'] = $allocationDisplay; 

          $allocationData[$prevWd] = $allocationWeekDay;  
        }

        $numPeriods = 0;
        $allocationStr = array();
        $allocationDisplay = array();
        $prevWd = $p->weekDay;
      }
      
      if ($p->pSeq != '0') {
        $numPeriods ++;
        if (isset($p->allocStaff))
          $allocationStr[$numPeriods] = $this->__getAllocString ($p->csName, $p->csCount, $p->allocStaff);
        else
          $allocationStr[$numPeriods] = 0;
        $allocationDisplay[$numPeriods] = (empty($p->csName)?'-':$p->csName);
      }
    }

    if ($numPeriods != 0) {
      $allocationWeekDay['numPeriods'] = $numPeriods; 
      $allocationWeekDay['allocationStr'] = $allocationStr; 
      $allocationWeekDay['allocationDisplay'] = $allocationDisplay; 
      $allocationData[$prevWd] = $allocationWeekDay;  
    }

    return $allocationData;
  }

  private function __getAllocString($csName, $csCount, $allocType) {
    switch ($allocType) {
      case '1':
        switch ($csCount) {
          case '0':
            return '0';
          case '1':
            return '1';
          default:
            return 'E';
        }
        break;
      case '2':
        switch ($csCount) {
          case '0':
            return '0';
          case '1':
            return '21';
          case '2':
            return '22';
          default:
            return '2E';
        }
        break;
      case '3':
        switch ($csCount) {
          case '0':
            return '0';
          case '1':
            return '31';
          case '2':
            return '32';
          case '3':
            return '33';
          default:
            return '3E';
        }
        break;
      case '4':
        switch ($csCount) {
          case '0':
            return '0';
          case '1':
            return '41';
          case '2':
            return '42';
          case '3':
            return '43';
          case '4':
            return '44';
          default:
            return '4E';
        }
      case 5:
        //Multiple allows to combine 50 classes
        //First allocation, $allocated will be 0
        //Subsequent allocations, $allocated will be between 51 and 100
        if ($csCount == 0)
          return '0';
        else if ($csCount == 1)
          return '51';
        else if ($csCount > 1 && $csCount <= 100)
          return '' . ($csCount + 50); //To convert to string
        break;
      // case '5':
        
      //   return '0';
      //   break;
    }
  }

  public function repairStaffTT() {
    //echo '<pre>';print_r($this->input->post());

    $allocData = $this->input->post('allocData');
    $staffId = $this->input->post('staffId');

    $rAllocData = json_decode($allocData);
    //echo '<pre>';print_r($rAllocData);

    $oldJSON = $rAllocData->staffTTJSON;
    $newJSON = json_encode($rAllocData->genStaffTTJSON);

    //echo '<pre>';print_r($newJSON);die();

    $result = $this->staff_tt->upsertStaffTTJSON($oldJSON, $newJSON, $staffId);

    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Repair Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong...');
	  }	
    redirect('timetable/staff_timetable/validateStaffTTIndex');
  }

  public function autoCheckStaffCache() {
    $data['main_content'] = 'timetable/staff_timetable/autocheck_staff_cache';
    $this->load->view('inc/template', $data);
  }

  //Ajax call
  public function cacheCheck() {
    $staffList = $this->staff_tt->get_allStaff();
    $checkArray = array();
    foreach ($staffList as $staffObj) {
      $temp = new stdClass();
      $temp->staffName = $staffObj->staffName;
      $temp->checkStatus = 1;
      $temp->checkMessage = 'Problem with something';
      
      $staffTTJSON = $this->staff_tt->getStaffTTById($staffObj->staffId, '1');
      $genStaffTTJSON = $this->generateJSONFromTT($staffObj->staffId);

      $output = $this->__matchJSON($staffTTJSON, $genStaffTTJSON);

      $temp->checkStatus = $output->result;
      $temp->checkMessage = $output->mismatch;

      $checkArray[] = $temp;
    }
    echo json_encode($checkArray);
  }

  private function __matchJSON($cache, $actual) {
    $cacheArray = json_decode($cache->allocation_data);

    $mismatch = '';
    $result = 1;
    foreach ($actual as $weekDay => $actData) {
      if (!isset($cacheArray->$weekDay)) {
        $returnVal = new stdClass();
        $returnVal->result = '';
        $returnVal->mismatch = '';
        return $returnVal;
      }
      $cacheObj = $cacheArray->$weekDay;
      $output = $this->__matchAllocStr($cacheObj->allocationStr, $actData['allocationStr'], $cacheObj->allocationDisplay, $actData['allocationDisplay']);

      $result = $result && $output->result;
      if (!empty($mismatch))
        $mismatch .= ', ';
      $mismatch .= $weekDay . ': ' . $output->mismatch;
    }

    $returnVal = new stdClass();
    $returnVal->result = $result;
    $returnVal->mismatch = $mismatch;

    return $returnVal;
  }

  private function __matchAllocStr($cacheAllocStr, $actAllocStr, $cacheAllocDisplay, $actAllocDisplay) {
    $mismatch = '';
    foreach ($actAllocStr as $key => $actObj) {
      if ($actObj != $cacheAllocStr->$key) {
        $periodString = 'P' . $key;
        $actString = $actObj . '/' . $actAllocDisplay[$key];
        $cacheString = $cacheAllocStr->$key . '|' . $cacheAllocDisplay->$key;
        $mismatch .= $periodString . ': A:' . $actString . ' C: ' . $cacheString; 
      }
    }
    if (empty($mismatch)) {
      $returnVal = new stdClass();
      $returnVal->result = 1;
      $returnVal->mismatch = 'None';
    } else {
      $returnVal = new stdClass();
      $returnVal->result = 0;
      $returnVal->mismatch = $mismatch;
    }
    return $returnVal;
  }

  public function getOverrideTTForStaff() {
    $staffId = $_POST['staffId'];
    $ttDate = $_POST['ttDate'];

    $data['oTT'] = $this->staff_tt->getOverrideTTForStaff($staffId, $ttDate);
    $data['staffObj'] = $this->staff_tt->getStaffDetails($staffId);
    $data['headerTT'] = $this->timetable->getPeriodHeader($this->__getHeaderCSConfig());

    echo json_encode($data);
  }

  //Landing function to initialize staff timetable template
  public function showStaffTemplateInits() {
    $data['staffTTTemplates'] = $this->staff_tt->getStaffTTTemplates();
    //echo '<pre>';print_r($data['classSectionList']);die();
    $data['main_content']    = 'timetable/template/staff_timetable_template';
    $this->load->view('inc/template', $data); 
  }

  public function getTimetableTemplates() {
    $templates = $this->staff_tt->get_tt_templates();
    echo json_encode($templates);
  }

  public function assignTimetableTemplate() {
    $staffId = $_POST['staffId'];
    $tttId = $_POST['tttId'];

    echo json_encode($this->staff_tt->setTimetableTemplateToStaff($staffId, $tttId));
  }
  
}