DROP PROCEDURE IF EXISTS merge_object;
DELIMITER $$
CREATE PROCEDURE merge_object (INOUT oldJson JSON, INOUT newJson JSON, IN oldValue TEXT, IN newValue TEXT, IN keyName TEXT)
BEGIN
IF (oldValue IS NOT NULL AND newValue IS NOT NULL AND oldValue != newValue)
    OR (oldValue IS NULL AND newValue IS NOT NULL)
    OR (oldValue IS NOT NULL AND newValue IS NULL) THEN
        IF (newJson IS NULL) THEN
            SELECT JSON_OBJECT(keyName, newValue) INTO newJson;
            SELECT JSON_OBJECT(keyName, oldValue) INTO oldJson;
        ELSE
            SELECT JSON_MERGE(newJson, JSON_OBJECT(keyName, newValue)) INTO newJson;
            SELECT  JSON_MERGE(oldJson, JSON_OBJECT(keyName, oldValue)) INTO oldJson;
         END IF;
    END IF;
END;
$$
DELIMITER ;