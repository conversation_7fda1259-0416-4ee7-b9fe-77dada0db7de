<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  12 Jan 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fees_structure
 */
class Fees_cohorts extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    // if (!$this->authorization->isSuperAdmin()) {
    //   redirect('dashboard', 'refresh');
    // }
    $this->load->model('feesv2/fees_cohorts_model');
    $this->load->model('feesv2/fees_blueprint_model','fbm');
    $this->load->library('fee_library');
  }

  public function index () {
    $selectedBP = $this->input->post('blueprintId');
    $feeBlueprints = $this->fbm->getFeeBluePrints(false);
    if (empty($selectedBP))
       $data['selectedBP'] = $feeBlueprints[0]->id;
    else
      $data['selectedBP'] = $selectedBP;

    if (!empty($feeBlueprints)) {
      $data['fee_structures'] = $this->fees_cohorts_model->get_fee_structures($data['selectedBP']);
    }
    $data['feeBlueprints'] = $feeBlueprints;
    $data['main_content'] = 'feesv2/cohorts/index';
    $this->load->view('inc/template', $data);
  }

  //Landing function for add a new Fee Structure
  public function add () {
    $selectedBP = $this->input->post('blueprintId');
    $data['selectedBP'] = $selectedBP;
    $selectedBPName = $this->input->post('blueprintName');

    $data['selectedBPName'] = $selectedBPName;
    // Filter type component display 
    $data['bluprint_components'] = $this->fees_cohorts_model->get_blueprint_components($selectedBP);
    // echo "<pre>"; print_r($data['bluprint_components']); die();
    $installment_types = [];
    foreach ($data['bluprint_components'] as $key => $value) {
      $installment_types[$value->feev2_installment_type_id] = $value->type_name;
    }
    // echo "<pre>"; print_r($installment_types); die();
    $data['ins_types'] = $installment_types;
    $data['get_filters'] = $this->fees_cohorts_model->get_filtersbyblueprintId($selectedBP);
    //echo "<pre>"; print_r($data['get_filters']); die();
     // Filter type construct fee structure and get Fee blueprint
    $fcArr = $this->fees_cohorts_model->get_filter_blueprint($selectedBP);
    if ($fcArr[0] === 'none') {
      $display_filters = FALSE;
    } else {
      $display_filters = TRUE;
      foreach ($fcArr as $fc) {
        switch ($fc) {
          case 'academic_year_of_joining':
            $data['showAcadJoining'] = TRUE;
            $data['acadJoiningLabel'] = 'Joining Academic year';
            $data['acadColumnName'] = $fc;
            $data['acadJoiningOptions'] = $this->fees_cohorts_model->getAcadJoiningOptions();
            break;
          case 'is_rte':
            $data['showRTE'] = TRUE;
            $data['rteLabel'] = 'RTE';
            $data['rteColumnName'] = $fc;
            $data['rteOptions'] = $this->fees_cohorts_model->getRTEOptions();
            break;
          case 'class':
            $data['showClass'] = TRUE;
            $data['classLabel'] = 'Class';
            $data['classColumnName'] = $fc;
            $data['classData'] = $this->fees_cohorts_model->getClassList();
          //echo "<pre>";print_r($data['classData']);die();
            break;
          case 'medium':
            $data['showMedium'] = TRUE;
            $data['mediumLabel'] = 'Medium';
            $data['mediumColumnName'] = $fc;
            $data['mediumOptions'] = $this->fees_cohorts_model->getMediumOptions();
            break;
          case 'category':
            $data['showCategory'] = TRUE;
            $data['categoryLabel'] = 'Category';
            $data['categoryColumnName'] = $fc;
            $data['categoryOptions'] = $this->fees_cohorts_model->getCategoryOptions();
            break;
          case 'admission_type':
            $data['showAdmissionType'] = TRUE;
            $data['admissionTypeLabel'] = 'Admission';
            $data['admissionTypeColumnName'] = $fc;
            $data['admissionTypeOptions'] = $this->fees_cohorts_model->getAdmissionTypeOptions();
            break;
          case 'board':
            $data['showBoards'] = TRUE;
            $data['boardsLabel'] = 'Boards';
            $data['boardsColumnName'] = $fc;
            $data['boardList'] = $this->fees_cohorts_model->getBoardsList();
            break;
          case 'boarding':
            $data['showBoarding'] = TRUE;
            $data['boardingLabel'] = 'Boarding';
            $data['boardingColumnName'] = $fc;
            $data['boardingOptions'] = $this->fees_cohorts_model->getBoardingOptions();
            break;
          case 'class_type':
            $data['showClassType'] = TRUE;
            $data['classTypeLabel'] = 'Class Type';
            $data['classTypeName'] = $fc;
            $data['classTypeOptions'] = $this->fees_cohorts_model->getClassTypeOptions();
          break;
          case 'has_staff':
            $data['showStaff'] = TRUE;
            $data['staffLabel'] = 'Has Staff';
            $data['staffColumnName'] = $fc;
            $data['staffOptions'] = ['Not a Staff kid' => '0', 'Staff kid' => '1'];
            break;
          case 'has_sibling':
            $data['showSibling'] = TRUE;
            $data['siblingLabel'] = 'Has Sibling';
            $data['siblingColumnName'] = $fc;
            $data['siblingOptions'] = ['No Sibling' => '0', 'Sibling' => '1'];
            break;
          case 'has_transport':
            $data['showTransport'] = TRUE;
            $data['transportLabel'] = 'Has Transport';
            $data['transportColumnName'] = $fc;
            $data['transportOptions'] = ['No' => '0', 'Yes' => '1'];
            break;
          case 'has_transport_km':
            $data['showTransportKm'] = TRUE;
            $data['TransportKmLabel'] = 'Transport Km';
            $data['TransportKmName'] = $fc;
            $data['TransportKmOptions'] = $this->fees_cohorts_model->get_fee_km_list();
            break;
          case 'stop':
            $data['showTransportStop'] = TRUE;
            $data['TransportStopLabel'] = 'Stop';
            $data['TransportStopName'] = $fc;
            $data['TransportStopOptions'] = $this->fees_cohorts_model->get_fee_Stop_list();
            break;
          case 'pickup_mode':
            $data['showTransportPM'] = TRUE;
            $data['TransportPMLabel'] = 'Pickup Mode';
            $data['TransportPMName'] = $fc;
            $data['TransportPMOptions'] = $this->fees_cohorts_model->get_fee_pickup_mode_list();
            break;
          case 'gender':
            $data['showGenderPM'] = TRUE;
            $data['GenderPMLabel'] = 'Gender';
            $data['GenderPMName'] = $fc;
            $data['GenderPMOptions'] = ['M' => 'Male', 'F' => 'Female'];
            break;
          case 'physical_disability':
            $data['showPhysicalDisabilityPM'] = TRUE;
            $data['PhysicalDisabilityPMLabel'] = 'Physical Disability';
            $data['PhysicalDisabilityPMName'] = $fc;
            $data['PhysicalDisabilityPMOptions'] = ['0' => 'No', '1' => 'Yes'];
            break;
         case 'is_lifetime_student':
            $data['showIsLifeTimeFeePM'] = TRUE;
            $data['IsLifeTimeFeePMLabel'] = 'Is Life Time Student';
            $data['IsLifeTimeFeePMName'] = $fc;
            $data['IsLifeTimeFeePMOptions'] = ['0' => 'No', '1' => 'Yes'];
            break;
          case 'combination':
            $data['showcombination'] = TRUE;
            $data['combinationLabel'] = 'Combination';
            $data['combinationColumnName'] = $fc;
            $data['combinationData'] = $this->fees_cohorts_model->getCombinationOptions();
            break;
          case 'quota':
            $data['showquota'] = TRUE;
            $data['quotaLabel'] = 'Quota';
            $data['quotaColumnName'] = $fc;
            $data['quotaOptions'] = $this->fees_cohorts_model->getQuotaOptions();
            break;
          case 'attempt':
            $data['showattempt'] = TRUE;
            $data['attemptLabel'] = 'Attempt';
            $data['attemptColumnName'] = $fc;
            $data['attemptOptions'] = $this->fees_cohorts_model->getAttemptOptions();
          }
      }
    }
    $data['display_filters'] = $display_filters;
    $data['main_content'] = 'feesv2/cohorts/add_fee_cohorts';
    $this->load->view('inc/template', $data);
  }

  public function get_installment_wise_component(){
    $fsiId = $_POST['fsiId'];
    //echo "<pre>"; print_r($fsiId); die();
    $blueprint_id = $_POST['blueprint_id'];
    $result = $this->fees_cohorts_model->get_installment_id_component($fsiId, $blueprint_id);
    echo json_encode($result);
  }
 //Add the Fee Master Record
  public function submit_fee_structure () {
    //Construct the filter     
    $selectedBP = $this->input->post('blueprint_id');
    $filter = $this->fee_library->construct_filter($selectedBP, $this->input->post());    
    $result = $this->fees_cohorts_model->insertFeeStructure($filter);
    //echo "<pre>"; print_r($result); die();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Submit Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }

    redirect('feesv2/fees_cohorts');
  }

  public function clone_data_from_id(){
    $cloneId = $_POST['cloneId'];
    $result = $this->fees_cohorts_model->clone_data_from_cohorId($cloneId);
    echo json_encode($result);
  }

  public function edit_fee_cohort_structre($cohort_id,$selectedBP){
    //echo "<pre>"; print_r($blueprint_id); die();
    $data['cohort_id'] = $cohort_id;
    $data['selectedBP'] = $selectedBP;
    $data['edit_fee_cohort'] = $this->fees_cohorts_model->get_fee_cohort_structure_details($cohort_id,$selectedBP);
      //echo "<pre>"; print_r($data['edit_fee_cohort']); die();
    // echo "<pre>"; print_r($data);die();
    $data['main_content'] = 'feesv2/cohorts/edit_fee_cohorts';
    $this->load->view('inc/template', $data);
  }

  public function save_fee_cohort_structrebyid(){
    $cohort_id = $_POST['cohort_id'];
    $total_fee = $_POST['total_fee'];
    $friendly_name = $_POST['friendly_name'];
    $result = $this->fees_cohorts_model->save_fee_cohort_structrebyid($cohort_id, $total_fee, $friendly_name);
    echo json_encode($result);
  }

  public function save_fee_cohort_structre_compId(){
    $cohort_comp_id = $_POST['cohort_comp_id'];
    $compAmount = $_POST['compAmount'];
    $result = $this->fees_cohorts_model->save_fee_cohort_structre_compId($cohort_comp_id, $compAmount);
    echo json_encode($result);
  }
  
  public function edit_comp_cohort(){
    $inst_type = $_POST['inst_type'];
    $editcomp_name = $_POST['editcomp_name'];
    $editcomp_amount = $_POST['editcomp_amount'];
    $cohort_id = $_POST['cohort_id'];

    $result = $this->fees_cohorts_model->insert_edit_comp_cohort($inst_type,$editcomp_name,$editcomp_amount,$cohort_id);
    echo json_encode($result);
  }

  public function get_installment_wise_comp_data_cohort_by_id(){
    $cohort_ins_id = $_POST['cohort_ins_id'];
    $cohort_id = $_POST['cohort_id'];
    $selectedBP = $_POST['selectedBP'];
    
    $result = $this->fees_cohorts_model->get_installment_wise_comp_data_cohort_by_id($cohort_ins_id,$cohort_id, $selectedBP);
    echo json_encode($result);

  }

  public function delete_fee_cohort_structre_comp(){
    $cohort_comp_id = $_POST['cohort_comp_id'];
    $result = $this->fees_cohorts_model->delete_fee_cohort_structre_comp($cohort_comp_id);
    echo json_encode($result);
  }

  public function delete_fee_cohort_structure($cohort_id, $selectedBP) {
    $result=$this->fees_cohorts_model->delete_fee_cohort_structure_details($cohort_id, $selectedBP);
    
    return $result; 
}


  

}