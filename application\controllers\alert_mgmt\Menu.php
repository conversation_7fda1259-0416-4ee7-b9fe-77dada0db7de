<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  17 August 2022
 *
 * Description: Controller for Alert Management Module. Entry point for Alert Management Module
 *
 * Requirements: PHP5 or above
 *
 */

class Menu extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
  }

  //Landing function to show non-compliance menu
  public function index() {
    $site_url = site_url();

    $data['tiles'] = array(
      [
        'title' => 'Manage Alerts',
        'sub_title' => 'View and add Alerts',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'',
        'permission' => $this->authorization->isAuthorized('DONATION.MODULE')
      ],
      [
        'title' => 'My Alerts',
        'sub_title' => 'View and add Alerts',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'',
        'permission' => $this->authorization->isAuthorized('DONATION.MODULE')
      ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
      [
        'title' => 'Alerts Summary',
        'sub_title' => 'Donor Summary',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'',
        'permission' =>  $this->authorization->isAuthorized('DONATION.VIEW_REPORTS')
      ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['main_content']    = 'alert_mgmt/menu';
    $this->load->view('inc/template', $data);
  }

}