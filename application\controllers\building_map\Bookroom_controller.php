<?php

class Bookroom_controller extends CI_Controller {

    function __construct(){
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('ROOM_BOOKING')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('bookroom_model');
        $this->load->model('building_model');
        $this->load->model('avatar');
    }

    function _remap($method,$args)
    {
        if (method_exists($this, $method)){
            $this->$method($args);
        }
        else{
            $this->index($method,$args);
        }
    }

    public function index($id=null){
        $data['room_id'] = $id;
        $AvatarId = $this->authorization->getAvatarId();
        $avatar = $this->avatar->getAvatarById($AvatarId);
        $data['sectionList'] = $this->bookroom_model->getSectionList();
        $data['staffId'] = $avatar->stakeholderId;//staff Id
        $AllowAllRooms = $this->authorization->isAuthorized('ROOM_BOOKING.ALLOW_ALL_ROOMS');
        if($AllowAllRooms)
            $data['roomNames'] = $this->bookroom_model->get_Rooms(1);
        else 
            $data['roomNames'] = $this->bookroom_model->get_Rooms(0);

        $data['main_content'] = 'building_map/bookrooms/index';
        $this->load->view('inc/template', $data);
    }

    public function getPeriods(){
        //$ttt = $this->bookroom_model->getTTTId();
        $tttId = $this->settings->getSetting('online_room_booking_template_id');

        $room_id = $this->input->post('room_id');
        $events = $this->bookroom_model->get_events_all();
        $holidays = array();
        foreach($events as $event){
            $holidays[] = array(
                'event_name'=>$event->event_name,
                'event_type'=>$event->event_type,
                'from_date'=>$event->from_date,
                'to_date'=>$event->to_date
            );
        }
        $date = date('Y-m-d');
        $data['Sun'] = $this->bookroom_model->getAllPeriods($tttId,1,$room_id,$date);
        $data['Mon'] = $this->bookroom_model->getAllPeriods($tttId,1,$room_id,$date);
        $data['Tue'] = $this->bookroom_model->getAllPeriods($tttId,2,$room_id,$date);
        $data['Wed'] = $this->bookroom_model->getAllPeriods($tttId,3,$room_id,$date);
        $data['Thu'] = $this->bookroom_model->getAllPeriods($tttId,4,$room_id,$date);
        $data['Fri'] = $this->bookroom_model->getAllPeriods($tttId,5,$room_id,$date);
        $data['Sat'] = $this->bookroom_model->getAllPeriods($tttId,6,$room_id,$date);
        $periods1 = array(
            "Sun"=>$data['Sun'],
            "Mon"=>$data['Mon'],
            "Tue"=>$data['Tue'],
            "Wed"=>$data['Wed'],
            "Thu"=>$data['Thu'],
            "Fri"=>$data['Fri'],
            "Sat"=>$data['Sat'],
            "holidays"=>$holidays
        );
        $date = date('Y-m-d', strtotime("next Sunday"));
        $data['Sun'] = $this->bookroom_model->getAllPeriods($tttId,1,$room_id,$date);
        $data['Mon'] = $this->bookroom_model->getAllPeriods($tttId,1,$room_id,$date);
        $data['Tue'] = $this->bookroom_model->getAllPeriods($tttId,2,$room_id,$date);
        $data['Wed'] = $this->bookroom_model->getAllPeriods($tttId,3,$room_id,$date);
        $data['Thu'] = $this->bookroom_model->getAllPeriods($tttId,4,$room_id,$date);
        $data['Fri'] = $this->bookroom_model->getAllPeriods($tttId,5,$room_id,$date);
        $data['Sat'] = $this->bookroom_model->getAllPeriods($tttId,6,$room_id,$date);
        $periods2 = array(
            "Sun"=>$data['Sun'],
            "Mon"=>$data['Mon'],
            "Tue"=>$data['Tue'],
            "Wed"=>$data['Wed'],
            "Thu"=>$data['Thu'],
            "Fri"=>$data['Fri'],
            "Sat"=>$data['Sat'],
            "holidays"=>$holidays
        );
        $periods = array(
            'thisWeek' => $periods1,
            'nextWeek' => $periods2
        );
        // echo "<pre>"; print_r($periods2);die();
        echo json_encode($periods);
    }


    public function roomBooking(){
        $input = $this->input->post();
        if (array_key_exists('periods', $input)) {
            $this->bookRoom($input);
        }
        else if(array_key_exists('cancel', $input)){
            $this->cancelBooking($input);
        }
    }
    public function bookRoom($input){
        $AvatarId = $this->authorization->getAvatarId();
        $avatar = $this->avatar->getAvatarById($AvatarId);
        $check = $this->bookroom_model->checkAvailability($input);
        $roomId = $this->input->post('room_id');
        if($check == 0) {
            $this->session->set_flashdata('flashInfo', 'Sorry this room got booked by someone else.');
            redirect('building_map/bookroom_controller/'.$roomId);
        } 
        $status = (int)$this->bookroom_model->addBooking($input,$avatar->stakeholderId);
        if ($status) {
            $this->session->set_flashdata('flashSuccess', 'Room Booked Successfully');
            redirect('building_map/bookroom_controller/'.$roomId);
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong!');
            redirect('building_map/bookroom_controller/'.$roomId);
        }
    }

    public function cancelBooking($input){
        //print_r($input);
        $status = (int)$this->bookroom_model->cancelBooked($input);
        $roomId = $this->input->post('room_id');
        if ($status) {
            $this->session->set_flashdata('flashSuccess', 'Booking Cancelled Successfully');
            redirect('building_map/bookroom_controller/'.$roomId);
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong!');
            redirect('building_map/bookroom_controller/'.$roomId);
        }
    }

}
