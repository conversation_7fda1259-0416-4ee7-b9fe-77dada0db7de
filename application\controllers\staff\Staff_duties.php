<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  30 March 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Staff Duties
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Staff_duties extends CI_Controller {

	public function __construct() {
		parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('SUBSTITUTION')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('staff/staff_duties_model');
    $this->load->model('staff/staff_request_model');
  }

  public function index(){
    $data['staff_duties'] = $this->staff_duties_model->get_allInsetedDetails();
    //echo "<pre>"; print_r($data['staff_duties']); die();
  	$data['main_content'] = 'staff/staff_duties/index';
    $this->load->view('inc/template', $data);
  }

  public function add(){
    $data['staff_list'] = $this->staff_request_model->getAll_Staff();
	  $data['main_content'] = 'staff/staff_duties/add';
    $this->load->view('inc/template', $data);
  }
 
  public function get_datewisePeriods(){
    $date = $this->input->post('date');
    $result = $this->staff_duties_model->get_datewisePeriods($date);
    echo json_encode($result);
  }
  public function submit_staffduties(){

   $result = $this->staff_duties_model->insert_staff_duties();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Submit Successfully');
      redirect('staff/staff_duties');
    }else{
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('staff/add');
    }  
    
  }

  public function deleteDates($datesId,$sdId){
    $status =$this->staff_duties_model->delete_DutiesDates($datesId,$sdId);
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Delete Successfully');
      redirect('staff/staff_duties');
    }else{
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('staff/staff_duties');
    }  
    
  }

  public function get_staffDetailsforremove(){
    $sdId = $this->input->post('sdId');
    $result = $this->staff_duties_model->get_staffDutiesbyId($sdId);
    $html_str = '<thead><tr><th>#</th><th>Name</th><th>Delete ?</th></tr></thead><tbody>';
    $i = 1;
    if(!empty($result)) {
      foreach ($result as $type => $res){
        $html_str .= '<tr>';
        $html_str .= '<td>'.$i.'</td>';
        $html_str .= '<td>'.$res->staff_name.'</td>';         
        $html_str .= '<td><input type="checkbox"  class="resId" value="'.$res->staff_id.'" name="staffId"></td>';         
        $html_str .= '<td><input type="hidden"  id="sdId" value="'.$sdId.'"></td>';         
        $html_str .= '</tr>';
        $i++;
      }
    }
    $html_str .= "</tbody>";
    echo json_encode($html_str);
  }
  public function delete_staffDetailsforremove(){
     $staffId = $this->input->post('staffId');
     $sdId = $this->input->post('sdId');
     $result = $this->staff_duties_model->deleteStaffFromStaffDuties($staffId,$sdId);
     if ($result) {
      echo 1;
     }else{
      echo 0;
     }
  }


}