<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  12 Jan 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_Master
 */
class Fees_inst_types extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isSuperAdmin()) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('feesv2/Fees_inst_model','finst_model');
    $this->load->library('fee_library');
  }

  public function index(){
    $data['installmentTypes'] = $this->finst_model->getInstallmentTypes();
    // echo '<pre>';print_r($data['installmentGroups']);die();
    $data['main_content'] = 'feesv2/installment_types/index';
    $this->load->view('inc/template', $data);
  }

  public function add_installment_type() {
    $data['main_content'] = 'feesv2/installment_types/add';
    $this->load->view('inc/template', $data);
  }
  public function edit_installment_types($id = '') {
    $data['fee_list'] = $this->finst_model->get_particular_installment($id);
    $data['main_content'] = 'feesv2/installment_types/edit';
    $this->load->view('inc/template', $data);
  }
  public function update_installemnt_type($id ='') {
    $result = $this->finst_model->update_installemnt_type($id);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Installment Updated Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('feesv2/Fees_inst_types/');
  }
  

  public function submit_installment_type(){
    // echo '<pre>';print_r($this->input->post());die();
    $result = $this->finst_model->insert_installment_type();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Installment Type created Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('feesv2/Fees_inst_types/');
  }

}