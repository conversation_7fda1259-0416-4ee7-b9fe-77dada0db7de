<?php

class Itari_user extends CI_Controller {
  function __construct() {
    parent::__construct();
    $this->load->model('feesv2/fees_collection_model');
    $this->load->model('itari/Itari_Model');
  }

  public function itari_enquiry_page($className ='') {
    $data['className'] = $className;
    $data['main_content']    = 'itari/itari_enquiry_page/index';
    $this->load->view('itari/inc/template', $data); 
  }

  public function itari_common_enquiry_page() {
    $data['main_content']    = 'itari/itari_common_enquiry_page/index';
    $this->load->view('itari/inc/template', $data); 
  }

  public function itari_success($className) {
    $data['className'] = $className;
    $data['main_content']    = 'itari/itari_enquiry_page/itari_success';
    $this->load->view('itari/inc/template', $data);
  }

  public function insert_enquiry_data(){
    $className = $_POST['class_name'];
    $source = $_POST['source'];
    $this->load->model('itari/Itari_Model');
    $result = $this->Itari_Model->insert_enquiry_data();
    if ($source == 'common landing page') {
      redirect("itari-common-success/$className");
    } else {
      redirect("itari-success/$className");
    }
  }

  public function itari_common_success($className) {
    $data['className'] = $className;
    $data['main_content']    = 'itari/itari_common_enquiry_page/itari_common_success';
    $this->load->view('itari/inc/template', $data);
  }
}