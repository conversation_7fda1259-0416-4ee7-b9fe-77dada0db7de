<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Super_admin_controller extends CI_Controller {

  private $yearId;
	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->yearId = $this->acad_year->getAcadYearId();
    $this->load->library('filemanager');
    $this->load->model('super_admin_folder/Super_admin_model');
  }

  public function super_admin_dashboard() {
    $data['main_content'] = 'super_admin_folder/super_admin_dashboard';     	
    $this->load->view('inc/template', $data);
  }

  function assessment_wise_report_filter() {
    $data['classse']= $this->Super_admin_model->get_all_classse();
    $data['main_content'] = 'super_admin_folder/super_admin_assessment_wise_report_filter';     	
    $this->load->view('inc/template', $data);
  }

  public function get_sections_and_assessments() {
    $class_id= $this->input->post('selected_class');
    $objects['sections']= $this->Super_admin_model->get_sections($class_id);
    $objects['assessments']= $this->Super_admin_model->get_assessments($class_id);
    $objects['computedFields']= $this->Super_admin_model->get_computed_fields($class_id);
    echo json_encode($objects);
  }

  public function onclick_get_subjects() {
    $class_id= $this->input->post('class_id');
    $assessments= $this->input->post('assessments');
    
    $objects['groups']= $this->Super_admin_model->get_assessment_wise_groups($class_id, $assessments);
    $objects['components']= $this->Super_admin_model->get_assessment_wise_components($class_id, $assessments);
    $objects['electives']= $this->Super_admin_model->get_assessment_wise_electives($class_id, $assessments);

    // echo "<pre>"; print_r($objects); die();

    echo json_encode($objects);
  }

  public function assessment_wise_report_data() {
    $inputs= $this->input->post();

    $class= isset($inputs['class']) ? $inputs['class'] : 0;
    $sections= isset($inputs['sections']) ? $inputs['sections'] : [0];
    $assessments= isset($inputs['assessments']) ? $inputs['assessments'] : [0];
    $summary_assessment= isset($inputs['summary_assessment']) ? $inputs['summary_assessment'] : [0];
    $computed_fields= isset($inputs['computed_fields']) ? $inputs['computed_fields'] : [];
    $display_marks_computed_fields= isset($inputs['display_marks_computed_fields']) ? $inputs['display_marks_computed_fields'] : [];
    $display_percentage_computed_fields= isset($inputs['display_percentage_computed_fields']) ? $inputs['display_percentage_computed_fields'] : [];
    $display_grade_computed_fields= isset($inputs['display_grade_computed_fields']) ? $inputs['display_grade_computed_fields'] : [];
    $display_students= isset($inputs['display_students']) ? $inputs['display_students'] : [];
    $display_rounded= isset($inputs['display_rounded']) ? $inputs['display_rounded'] : 'no_rounded';
    $display_class_teacher= isset($inputs['display_class_teacher']) ? $inputs['display_class_teacher'] : 'no';
    $display_section_teacher= isset($inputs['display_section_teacher']) ? $inputs['display_section_teacher'] : 'no';
    $display_after_computed_fields= isset($inputs['display_after_computed_fields']) ? $inputs['display_after_computed_fields'] : [];
    $groups= isset($inputs['groups']) ? $inputs['groups'] : [];
    $display_marks_groups= isset($inputs['display_marks_groups']) ? $inputs['display_marks_groups'] : [];
    $display_percentage_groups= isset($inputs['display_percentage_groups']) ? $inputs['display_percentage_groups'] : [];
    $display_grade_groups= isset($inputs['display_grade_groups']) ? $inputs['display_grade_groups'] : [];
    $components= isset($inputs['components']) ? $inputs['components'] : [];
    $display_marks_components= isset($inputs['display_marks_components']) ? $inputs['display_marks_components'] : [];
    $display_percentage_components= isset($inputs['display_percentage_components']) ? $inputs['display_percentage_components'] : [];
    $display_grade_components= isset($inputs['display_grade_components']) ? $inputs['display_grade_components'] : [];
    $electives= isset($inputs['electives']) ? $inputs['electives'] : [];
    $display_marks_electives= isset($inputs['display_marks_electives']) ? $inputs['display_marks_electives'] : [];
    $display_percentage_electives= isset($inputs['display_percentage_electives']) ? $inputs['display_percentage_electives'] : [];
    $display_grade_electives= isset($inputs['display_grade_electives']) ? $inputs['display_grade_electives'] : [];
    $report_title= isset($inputs['report_title']) ? $inputs['report_title'] : '';

    $student_details= $this->Super_admin_model->get_students_details($class, $sections);
    $students= $student_details['student_details'];
    $student_arr= $student_details['student_arr'];
    // Component marks
    $component_marks= [];
    if(!empty($components) && !empty($student_arr)) {
      $component_marks= $this->Super_admin_model->get_component_wise_marks($components, $assessments, $sections, $student_arr);
    }
    // Groups marks
    $group_marks= [];
    if(!empty($groups) && !empty($student_arr)) {
      $group_marks= $this->Super_admin_model->get_group_wise_marks($groups, $assessments, $sections, $student_arr);
    }
    // Elective marks
    $elective_marks= [];
    if(!empty($electives) && !empty($student_arr)) {
      $elective_marks= $this->Super_admin_model->get_elective_wise_marks($electives, $assessments, $sections, $student_arr);
    }
    echo '<pre>'; print_r($elective_marks); die();
  }

}

?>