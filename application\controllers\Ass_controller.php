<?php
class Ass_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
       if (!$this->ion_auth->logged_in()) {
         redirect('auth/login', 'refresh');
       }
       if (!$this->authorization->isModuleEnabled('STUDENT_MASTER')) {
        redirect('dashboard', 'refresh');
      }
      if (!$this->authorization->isAuthorized('STUDENT.BASIC_VIEW')) {
        redirect('dashboard', 'refresh');
      }
      $this->load->model('ass_model');
      
    }

    public function index(){
        $data['staffList'] = $this->ass_model->getStaff();
        $data['main_content'] = 'assessment/addPermissions';
        $this->load->view('inc/template', $data);
    }
}