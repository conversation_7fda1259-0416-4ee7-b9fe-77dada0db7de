<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Student_controller
 *
 * <AUTHOR>
 */
class Student_info_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
       if (!$this->ion_auth->logged_in()) {
         redirect('auth/login', 'refresh');
       }
       if (!$this->authorization->isModuleEnabled('GATHER_STUDENT_INFO')) {
        redirect('dashboard', 'refresh');
      }
      if (!$this->authorization->isAuthorized('STUDENT.VIEW')) {
        redirect('dashboard', 'refresh');
      }
      $this->load->model('student/student_info_model');
      $this->load->model('student/Student_Model');
    }

    public function index($classId = '') {
      if (empty($classId)){
        $classId = $this->input->post('classId');
      }
      if (empty($classId)){
        $classId = 1; 
      }
      $data['classList'] = $this->Student_Model->getClassList();
      $data['getstudents'] = $this->student_info_model->getstudentDetails($classId);  
      $data['selectedClassId'] = $classId;      
      //echo '<pre>';print_r($data['getstudents']);die();
      $data['main_content'] = 'student/student_info/profileReport';
      $this->load->view('inc/template', $data);
    }

    public function aggregateReport(){
      $profileData = $this->student_info_model->getAggregateReport();
      
      $data['stdUpdates'] = 0;
      $data['hUpdates'] = 0;
      $data['fUpdates'] = 0;
      $data['mUpdates'] = 0;
      $data['completed'] = 0;
      $data['active'] = 0;
      $data['total'] = count($profileData);

      foreach($profileData as $key => $val){
        if($val->sStatus) $data['stdUpdates']++;
        if($val->hStatus) $data['hUpdates']++;
        if($val->fStatus) $data['fUpdates']++;
        if($val->mStatus) $data['mUpdates']++;
        if($val->completed) $data['completed']++;
        if($val->active == 0) $data['active']++;
      }

      //echo '<pre>';print_r($data);die();
      $data['main_content'] = 'student/student_info/aggregateReport';
      $this->load->view('inc/template', $data);
    }

}