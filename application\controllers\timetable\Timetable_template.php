<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  28 March 2018
 *
 * Description: Controller for Timetable Template
 *
 * Requirements: PHP5 or above
 *
 */

class Timetable_template extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
      redirect('dashboard', 'refresh'); 
    }
    $this->load->model('timetable/timetable_template_model');
    $this->load->model('timetable/staff_tt');
    $this->load->model('timetable/room_tt');
	}

  //Landing function to display templates
  public function index() {
    $data['tt_templates']=$this->timetable_template_model->get_tt_templates();
    //echo '<pre>';print_r($data['classSectionList']);die();
    $data['main_content']    = 'timetable/template/timetable_template';
    $this->load->view('inc/template', $data); 
  }

  public function add_template() {
    $data['main_content']    = 'timetable/template/add_tt_template';
    $this->load->view('inc/template', $data); 
  }

  public function submit_template() {
    $result=$this->timetable_template_model->submit_template();
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Template created successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('timetable/timetable_template','refresh');
  }

  public function assign_template($tttId) {
    $data['target'] = "Sections";
    $data['templateList']=$this->timetable_template_model->get_tt_templates();
    $data['assignedTemplate'] = $this->timetable_template_model->assigned_template();
    $data['templateId'] = $tttId;
    $data['templateName'] = $this->timetable_template_model->getTemplateName($data['templateId']);
    $data['main_content']    = 'timetable/template/assign_template';
    $this->load->view('inc/template', $data);  
  }

  public function init_staff ($tttId) {
    $data['target'] = "Staff";
    $data['templateList']=$this->timetable_template_model->get_tt_templates();
    $data['assignedTemplate'] = $this->timetable_template_model->assigned_template_staff();
    $data['templateId'] = $tttId;
    $data['templateName'] = $this->timetable_template_model->getTemplateName($data['templateId']);
    $data['main_content']    = 'timetable/template/assign_template';
    $this->load->view('inc/template', $data);
    }

  public function init_rooms ($tttId) {
    $data['target'] = "Rooms";
    $data['templateList']=$this->timetable_template_model->get_tt_templates();
    $data['assignedTemplate'] = $this->timetable_template_model->assigned_template_rooms();
    $data['templateId'] = $tttId;
    $data['templateName'] = $this->timetable_template_model->getTemplateName($data['templateId']);
    $data['main_content']    = 'timetable/template/assign_template';
    $this->load->view('inc/template', $data);
  }

  public function init_single_room() {
    $data['roomList'] = $this->room_tt->get_allRoom();
    $data['templates'] = $this->timetable_template_model->get_tt_templates();
    $data['main_content'] = 'timetable/init_timetable/init_room_tt';
    $this->load->view('inc/template', $data);
  }

  public function submit_single_room() {
    $tttId = $this->input->post('tttId');
    $roomId = $this->input->post('roomId');
    $result = $this->room_tt->initSingleRoomTT($tttId, $roomId);

    if ($result) {
      if ($result == '1') {
        $this->session->set_flashdata('flashSuccess', 'Room Timetable initialized succesfully');
      } else {
        $this->session->set_flashdata('flashInfo', 'Room Timetable is already initialized');
      }
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong while initializing room timetable. Contact your administrator');
    }
    redirect('timetable/timetable_template/init_single_room');
  }

  public function submit_assign_template() {
    $result=$this->timetable_template_model->assign_template();
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Template assigned successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    switch ($_POST['target']){
      case 'Sections': 
        redirect('timetable/timetable_template/assign_template/' . $_POST['templateId']);
        break;
      case 'Staff':
        redirect('timetable/timetable_template/init_staff/' . $_POST['templateId']);
        break;
      case 'Rooms':
        redirect('timetable/timetable_template/init_rooms/' . $_POST['templateId']);
        break;           
    }
  }

  public function submit_unassign_template() {
    $result=$this->timetable_template_model->unassign_template();
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Template un-assigned successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    switch ($_POST['target']){
      case 'Sections': 
        redirect('timetable/timetable_template/assign_template/' . $_POST['templateId']);
        break;
      case 'Staff':
        redirect('timetable/timetable_template/init_staff/' . $_POST['templateId']);
        break;
      case 'Rooms':
        redirect('timetable/timetable_template/init_rooms/' . $_POST['templateId']);
        break;           
    }
  }

}
?>
