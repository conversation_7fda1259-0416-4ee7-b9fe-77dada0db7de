<?php
class Afl_performance_pointers extends CI_Controller {
    private $yearId;
    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
       redirect('auth/login', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('afl/Afl_perf_pointer_model','pointer_model');
    }

  public function index(){
    $data['pointers'] = $this->pointer_model->getPointers();
    $data['main_content'] = 'afl/performnace_pointers/index';
    $this->load->view('inc/template', $data);
  }

  public function addPerformancePointer() {
    $pointers = $this->input->post('pointers');
    $status = $this->pointer_model->savePerfPointer($pointers);
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Performance Pointers Added Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_performance_pointers');
  }

  public function updatePerformancePointer() {
    $status = $this->pointer_model->updatePerformancePointer();
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Performance Pointers Updated Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_performance_pointers');
  }

}