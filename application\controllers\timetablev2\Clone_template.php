<?php
class Clone_template extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE') || (!$this->authorization->isAuthorized('TIMETABLE.CLONE_TIMETABLE'))) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('timetablev2/clone_template_model','clone_template_model');
  }

  public function fetch_all_template_details(){
    $data['template_list'] = $this->clone_template_model->get_template_details();
    $data['acad_year_list'] = $this->clone_template_model->get_acad_year_details();
    $data['main_content']    = 'timetablev2/clone_template.php';
    $this->load->view('inc/template', $data);
  }

  public function get_template_data(){
    $template_id = $_POST['template_id'];
    $data['template_details'] = $this->clone_template_model->get_single_temp_data($template_id);
    echo json_encode($data);
  }

  public function check_template_requirements(){
    
    $template_name = $_POST['template_name'];
    $acad_year_id = $_POST['acad_year_id'];
    $description = $_POST['description'];
    $old_template_id = $_POST['template_id'];

    $temp_name = $this->clone_template_model->check_template_name($template_name);
    if($temp_name==0){
      echo json_encode(3);
      exit();
    }

    //Allow cloning to previous years too.
    // $acad_year = $this->clone_template_model->get_acad_year($old_template_id,$acad_year_id);
    // if($acad_year==0){
    //   echo json_encode(2);
    //   exit();
    // }
    $acad_year_name = $this->clone_template_model->get_acad_year_name($acad_year_id);
    $classes_exist=$this->clone_template_model->check_classes($old_template_id,$acad_year_id);
    
    $data['acad_year_name']=$acad_year_name;
    $data['class_exist']=$classes_exist;
    if($classes_exist==0){
      echo json_encode($data);
      exit();
    }
    
    $same_acad_year = $this->clone_template_model->check_acad_year($old_template_id,$acad_year_id);
    $data['same_acad_year']=$same_acad_year;
    
    echo json_encode($data);

  }

  public function clone_template_periods(){

    $template_name = $_POST['template_name'];
    $acad_year_id = $_POST['acad_year_id'];
    $description = $_POST['description'];
    $old_template_id = $_POST['template_id'];
    $same_acad_year = $_POST['same_acad_year'];

    $new_template_id = $this->clone_template_model->create_template($template_name,$acad_year_id,$description);
    $template_periods = $this->clone_template_model->clone_template_periods($old_template_id,$new_template_id);

    $data['new_template_id']=$new_template_id;
    echo json_encode($data);
  }

  public function clone_section_template(){

    $template_name = $_POST['template_name'];
    $acad_year_id = $_POST['acad_year_id'];
    $description = $_POST['description'];
    $old_template_id = $_POST['template_id'];
    $same_acad_year = $_POST['same_acad_year'];
    $new_template_id = $_POST['new_template_id'];

    $section_template = $this->clone_template_model->clone_section_template($old_template_id,$new_template_id,$acad_year_id,$same_acad_year);

    echo json_encode($section_template);
  }

  public function clone_staff_template(){

    $template_name = $_POST['template_name'];
    $acad_year_id = $_POST['acad_year_id'];
    $description = $_POST['description'];
    $old_template_id = $_POST['template_id'];
    $same_acad_year = $_POST['same_acad_year'];
    $new_template_id = $_POST['new_template_id'];

    $staff_template = $this->clone_template_model->clone_staff_template($old_template_id,$new_template_id,$acad_year_id, $template_name);

    echo json_encode($staff_template);
  }

  public function clone_section_workload(){

    $template_name = $_POST['template_name'];
    $acad_year_id = $_POST['acad_year_id'];
    $description = $_POST['description'];
    $old_template_id = $_POST['template_id'];
    $same_acad_year = $_POST['same_acad_year'];
    $new_template_id = $_POST['new_template_id'];

    $section_workload = $this->clone_template_model->clone_section_workload($old_template_id,$new_template_id,$acad_year_id, $template_name,$same_acad_year);

    echo json_encode($section_workload);
  }

  public function clone_staff_workload(){

    $template_name = $_POST['template_name'];
    $acad_year_id = $_POST['acad_year_id'];
    $description = $_POST['description'];
    $old_template_id = $_POST['template_id'];
    $same_acad_year = $_POST['same_acad_year'];
    $new_template_id = $_POST['new_template_id'];

    $staff_workload = $this->clone_template_model->clone_staff_workload($old_template_id,$new_template_id,$acad_year_id, $template_name);

    echo json_encode($staff_workload);
  }

  public function clone_construct_timetable(){

    $template_name = $_POST['template_name'];
    $acad_year_id = $_POST['acad_year_id'];
    $description = $_POST['description'];
    $old_template_id = $_POST['template_id'];
    $same_acad_year = $_POST['same_acad_year'];
    $new_template_id = $_POST['new_template_id'];

    $construct_timetable = $this->clone_template_model->clone_construct_timetable($old_template_id,$new_template_id,$acad_year_id, $template_name,$same_acad_year);

    echo json_encode($construct_timetable);
  }

  public function delete_clone_template(){

    $new_template_id = $_POST['new_template_id'];

    $delete_template = $this->clone_template_model->delete_clone_template($new_template_id);

    echo json_encode($delete_template);
  }

}