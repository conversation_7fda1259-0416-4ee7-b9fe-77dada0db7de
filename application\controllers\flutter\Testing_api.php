<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Testing_api extends CI_Controller {

	public function __construct() {
      parent::__construct();
		  if (CONFIG_ENV['school_sub_domain'] != 'demoschool') {
     		redirect('auth/login', 'refresh');
    	}
      $this->load->model('flutter/testing_api_model');
  	}

  	public function getSms() {
  		$text = $this->testing_api_model->getAllTexts();
      echo json_encode(array('status' => 'success', 'data' => $text));
  		// return json_encode(array('status' => 'success', 'data' => $text));
  	}

}