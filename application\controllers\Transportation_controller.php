<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  01 June 2018
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Transportation_controller extends CI_Controller {

	public function __construct() {
        parent::__construct();
		if (!$this->ion_auth->logged_in()) {
     		redirect('auth/login', 'refresh');
    	}
        $this->load->model('transportation_model');
        $this->load->model('student/Student_Model');
  	}

  // Stop Entry
  	public function stop_add(){
 		$data['allStopInfo'] = $this->transportation_model->getStopList();
    	$data['main_content'] = 'transportation/stops/index';
    	$this->load->view('inc/template', $data);
  	}

  	public function insert_stop() {
        $result = $this->transportation_model->add_stops();
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
        }else{
            $this->session->set_flashdata('flashError', 'Failed to Insert .');
        }
        redirect('transportation_controller/stop_add');
    }

    public function edit($id){
    	$data['allStopInfo'] = $this->transportation_model->getStopList();
    	$data['edit_stop'] = $this->transportation_model->editStopById($id);
    	$data['main_content'] = 'transportation/stops/index';
    	$this->load->view('inc/template', $data);
    }

    public function update_stop($id) {
        $result = $this->transportation_model->updateStop($id);
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Updated.');
        } else {
            $this->session->set_flashdata('flashError', 'Failed to update .');
        }
        redirect('transportation_controller/stop_add');
    }

    public function delete_stop($id){
	 	$result = $this->transportation_model->deleteStop($id);
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Deleted.');
        } else {
            $this->session->set_flashdata('flashError', 'Failed to delete .');
        }
        redirect('transportation_controller/stop_add');
    }


// Fee Structure

    public function fee_structure(){
        $data['isGPRS'] = $this->settings->getSetting('fees')['transport_gprs'];
        $data['fee_index'] = $this->transportation_model->get_fee_masterDetails();
        $data['main_content']='transportation/fee_structure/index';
        $this->load->view('inc/template',$data);
    }

     public function fee_structure_add(){
        $data['stopNames'] = $this->transportation_model->get_stopnamesAll();
        $data['isGPRS'] = $this->settings->getSetting('fees')['transport_gprs'];
        $data['main_content']='transportation/fee_structure/add';
        $this->load->view('inc/template',$data);
    }

    public function fee_structure_submit(){
        $result = $this->transportation_model->submit_fee_structureData();
        if ($result) {
          $this->session->set_flashdata('flashSuccess', ' Submit successfully');
        }else{
          $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('transportation_controller/fee_structure');
    }


    // Vechile Details

    public function vehicle(){
        $data['result']=$this->transportation_model->get_info_trans();
        $data['main_content']='transportation/vehicle/index';
        $this->load->view('inc/template',$data);
    }

    public function vehicle_data_add(){
      $data['main_content']='transportation/vehicle/add';
      $this->load->view('inc/template',$data);
    }

    public function submit_vehicle_data(){
        $result=$this->transportation_model->submitvehicle_details();
        if($result){
           $this->session->set_flashdata('flashSuccess', 'Succesfully Add.');
           redirect('transportation_controller/vehicle');
          }else{
           $this->session->set_flashdata('flashError', 'Some Thing Wrong.');
           redirect('transportation_controller/vehicle');
         }
    }    

    public function vehicle_data_edit($id){
      $data['vehicle_edit']=$this->transportation_model->vehicledataEditbyId($id);
      $data['main_content']='transportation/vehicle/edit';
      $this->load->view('inc/template',$data);
     }

    public function update_vehicle_data($id){
        $result=$this->transportation_model->updatevehicle_details($id);
        if($result){
            $this->session->set_flashdata('flashSuccess', 'Update Succesfully.');
            redirect('transportation_controller/vehicle');
        }else{
            $this->session->set_flashdata('flashError', 'Some Thing Wrong.');
            redirect('transportation_controller/vehicle_data_edit/'.$id);
        }
    }

    public function delete_trans($id){
        $result=$this->transportation_model->delete_trans_tab_1($id);
        if($result){
          $this->session->set_flashdata('flashSuccess', 'Delete Succesfully.');
          redirect('transportation_controller/vehicle');
        }
    }

    // Route Allocation

    public function route_allocation(){
        $data['route'] = $this->transportation_model->get_allroute();
        $data['stopNames'] = $this->transportation_model->get_stopAll();
        $data['main_content']    = 'transportation/route_allocate/index';
        $this->load->view('inc/template', $data);
    }

    public function route_allocation_submit(){
      $result = $this->transportation_model->insert_stopnamebyroute();
      if ($result) {
        $this->session->set_flashdata('flashSuccess', ' Submit successfully');
      }else{
          $this->session->set_flashdata('flashError', 'Something went wrong...');
      }
      redirect('transportation_controller/route_allocation');
    }

    public function get_routwiseallottedStop(){
         $route_name = $this->input->post('route_name');
         $result = $this->transportation_model->get_routwiseAllocatedStopnames($route_name);
         echo json_encode($result);
    }

    // Student Assign 
    public function assingStudent_transport($stdId,$traverse_to=""){

        $data['traverse_to'] = $traverse_to;
        $data['student_uid'] = $stdId;
        $data['stopList'] = $this->transportation_model->get_stopAll();
        $data['stdData'] = $this->Student_Model->getStdDataById($stdId);
        $data['edit_assignStop'] = $this->transportation_model->get_assignStop($stdId);
        //echo "<pre>"; print_r($data['stopList']); die();
        $data['main_content'] = 'transportation/assign_student/index';
        $this->load->view('inc/template', $data);
    }

    public function get_pickup_pointWiseRoute(){
        $pickup_point = $this->input->post('pickup_point');
        $result = $this->transportation_model->get_pickup_pointStopWiseRoute($pickup_point);
        echo json_encode($result);
    }

  	public function insert_student_assign($stdId,$traverse_to){

  		$result = $this->transportation_model->assignStudentStop($stdId);
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Updated.');
        }else{
            $this->session->set_flashdata('flashError', 'Failed to update .');
        }
        if ($traverse_to == '1') {
           redirect('fees/fee_concorde/feeTransportation/'.$stdId);
        }else{
          redirect('student/Student_controller/addMoreStudentInfo/'.$stdId);
        } 
  	}
    public function update_student_assign($stdId,$traverse_to){
        $result = $this->transportation_model->updateAssignStudentStop($stdId);
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Updated.');
        }else{
            $this->session->set_flashdata('flashError', 'Failed to update .');
        }
        if ($traverse_to == '1') {
           redirect('fees/fee_concorde/feeTransportation/'.$stdId);
        }else{
            redirect('student/Student_controller/addMoreStudentInfo/'.$stdId);
        }
    }
    //
    public function stop_price(){
        $data['priceList'] = $this->transportation_model->getAllStopPriseList();
        //echo "<pre>"; print_r($data['priceList']); die();
        $data['stopList'] = $this->transportation_model->getStopList();
        $data['main_content'] = 'transportation/stop_price/index';
        $this->load->view('inc/template', $data);
    }

    public function stopwise_price_insert(){
       $result = $this->transportation_model->insert_priceStopwise();
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Inserted.');
        } else {
            $this->session->set_flashdata('flashError', 'Failed to insert .');
        }
        redirect('transportation_controller/stop_price');
    }

    public function edit_price($id){
        $data['edit_stop'] = $this->transportation_model->edit_stopId($id);
        $data['stopList'] = $this->transportation_model->getStopList();
        $data['priceList'] = $this->transportation_model->getAllStopPriseList();
        $data['main_content'] = 'transportation/stop_price/index';
        $this->load->view('inc/template', $data);
    }

    public function stopwise_price_update($id){
       $result = $this->transportation_model->update_priceStopwise($id);
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Inserted.');
        } else {
            $this->session->set_flashdata('flashError', 'Failed to insert .');
        }
        redirect('transportation_controller/stop_price');
    }

    public function delete_price($id){
        $result = $this->transportation_model->deletePriceStopwise($id);
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Deleted.');
        } else {
            $this->session->set_flashdata('flashError', 'Failed to delete .');
        }
        redirect('transportation_controller/stop_price');
    }

}   