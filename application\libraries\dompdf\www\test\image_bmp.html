<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
 "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />
  <title>BMP image test suite</title>
	
	<style type="text/css">
		body {
			font-family: 'trebuchet ms', verdana, sans-serif;
			font-size: 12px;
		}
		table {
			border-collapse: collapse;
		}
		td {
			border: 1px solid #999;
			text-align: center;
		}
	</style>
</head>
<body>

<p>
	This test suite was grabbed from <a href="http://wvnvaxa.wvnet.edu/vmswww/bmp.html">http://wvnvaxa.wvnet.edu/vmswww/bmp.html</a>.
</p>

<table>
  <tr>
    <th></th>
    <th>BMP</th>
    <th>PNG</th>
  </tr>
	<tr>
		<td>1 bit (2 color)</td>
    <td><img src="images/bmp/test1.bmp" /></td>
    <td><img src="images/bmp/test1.png" /></td>
	</tr>
  <tr>
    <td>4 bit</td>
    <td><img src="images/bmp/test4.bmp" /></td>
    <td><img src="images/bmp/test4.png" /></td>
  </tr>
  <tr>
    <td>8 bit</td>
    <td><img src="images/bmp/test8.bmp" /></td>
    <td><img src="images/bmp/test8.png" /></td>
  </tr>
  <tr>
    <td>16 bit</td>
    <td><img src="images/bmp/test16.bmp" /></td>
    <td><img src="images/bmp/test16.png" /></td>
  </tr>
  <tr>
    <td>24 bit</td>
    <td><img src="images/bmp/test24.bmp" /></td>
    <td><img src="images/bmp/test24.png" /></td>
  </tr>
  <tr>
    <td>32 bit</td>
    <td><img src="images/bmp/test32.bmp" /></td>
    <td><img src="images/bmp/test32.png" /></td>
  </tr>
  <tr>
    <td>4 bit compressed</td>
    <td><img src="images/bmp/testcompress4.bmp" /></td>
    <td><img src="images/bmp/testcompress4.png" /></td>
  </tr>
	
  <tr>
    <td>8 bit compressed</td>
    <td><img src="images/bmp/testcompress8.bmp" /></td>
    <td><img src="images/bmp/testcompress8.png" /></td>
  </tr>
	<!-- The two bitmap formats below make an fatal error -->
  <!--<tr>
    <td>8 bit OS/2 version 1</td>
    <td><img src="images/bmp/test8os2.bmp" /></td>
    <td><img src="images/bmp/test8os2.png" /></td>
  </tr>
  <tr>
    <td>4 bit OS/2 version 2</td>
    <td><img src="images/bmp/test4os2v2.bmp" /></td>
    <td><img src="images/bmp/test4os2v2.png" /></td>
  </tr>-->
  <tr>
    <td>16 bit 555 bitfield</td>
    <td><img src="images/bmp/test16bf555.bmp" /></td>
    <td><img src="images/bmp/test16bf555.png" /></td>
  </tr>
  <tr>
    <td>16 bit 565 bitfield</td>
    <td><img src="images/bmp/test16bf565.bmp" /></td>
    <td><img src="images/bmp/test16bf565.png" /></td>
  </tr>
  <tr>
    <td>32 bit 888 bitfield</td>
    <td><img src="images/bmp/test32bf.bmp" /></td>
    <td><img src="images/bmp/test32bf.png" /></td>
  </tr>
  <tr>
    <td>32 bit 888 bitfield version 4</td>
    <td><img src="images/bmp/test32bfv4.bmp" width="300" /></td>
    <td><img src="images/bmp/test32bfv4.png" width="300" /></td>
  </tr>
  <tr>
    <td>32 bit version 5</td>
    <td><img src="images/bmp/test32v5.bmp" width="300" /></td>
    <td><img src="images/bmp/test32v5.png" width="300" /></td>
  </tr>
  <tr>
    <td>32 bit transparent version 4</td>
    <td><img src="images/bmp/trans.bmp" width="300" /></td>
    <td><img src="images/bmp/trans.png" width="300" /></td>
  </tr>
</table>

<p>
Note that as of December 2005, Mozilla and Internet Explorer
do not support transparent BMP images.
</p>

<p>
The images (except for the last three and the OS/2 version 2 image)
are from Jason Summer's <a href="http://entropymine.com/jason/bmpsuite/">BMP Suite</a>.
</p>
</body>
</html>