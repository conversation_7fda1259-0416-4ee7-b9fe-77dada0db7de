<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

if ( ! function_exists('prepare_attendance_count'))
{
    function prepare_attendance_count($data, $ma_periods)
    {

        $sliceData = [];
        $isSaturday = false;

        if(!empty($data)) {
            $day = $data[0]['day'];

            $timestamp = strtotime($day);

            $cnt = 1;
            $data_m = [];
            $data_n = [];

            if(date('l', $timestamp) == 'Saturday') {

                $isSaturday = true;

                foreach ($data as $key => $value) {
                    if($ma_periods['saturday']['m'] >= $cnt) {
                        $data_m[] = $value;
                    } else {
                        $data_n[] = $value;
                    }
                    $cnt++;
                }
                
                $sliceData['m'] = $data_m;
                $sliceData['a'] = $data_n;     
            } else {
                foreach ($data as $key => $value) {
                    if($ma_periods['weekday']['m'] >= $cnt) {
                        $data_m[] = $value;
                    } else {
                        $data_n[] = $value;
                    }
                    $cnt++;
                }

                $sliceData['m'] = $data_m;
                $sliceData['a'] = $data_n;                
            }
        }        
    	
    	$count = 0;
    	$total_count = count($data);
    	$morning = 0;
    	$noon = 0;
        $morning_ref = 0;
        $noon_ref = 0;

    	$loop_trace = 0;
    	$part = ceil($total_count/2);

        $competation_state = 0;


    	foreach ( $sliceData  as $sessdata) {

            if(empty($sessdata) && $loop_trace == 1) {
                $noon = $morning;
            } else {

                foreach ($sessdata as $onesess) {
                    if($onesess['status'] == 1 && $loop_trace == 0)
                        $morning = 1;
                    if($onesess['status'] == 1 && $loop_trace == 1)
                        $noon = 1;


                    if($onesess['reference_status'] == 1 && $loop_trace == 0)
                        $morning_ref = 1;
                    if($onesess['reference_status'] == 1 && $loop_trace == 1)
                        $noon_ref = 1;

                    if($onesess['reference_type'] == 1) {
                        $competation_state = 1;                
                    }


                }
                $loop_trace = 1;
            }
        }
        
        if($competation_state == 1 && (empty($sliceData['a']))) {
	        $noon_ref = $morning_ref;
        }

    	if($morning == 1 && $noon == 1) {
    		$return = ['status' => 1, 'type' => ''];
    	} else if($morning == 1 && $noon == 0) {
    		$return = ['status' => 0.5, 'type' => 'M'];
    	} else if($morning == 0 && $noon == 1) {
    		$return = ['status' => 0.5, 'type' => 'A'];
    	} else {
    		$return = ['status' => 0, 'type' => ''];
    	}

        if($morning_ref == 1 && $noon_ref == 1) {
            $return1 = ['status' => 1];
        } else if($morning_ref == 1 && $noon_ref == 0) {
            $return1 = ['status' => 0.5];
        } else if($morning_ref == 0 && $noon_ref == 1) {
            $return1 = ['status' => 0.5];
        } else {
            $return1 = ['status' => 0];
        }

        $return1 = array_merge($return1,['competation_state' => $competation_state]);

        return ['day' => $return, 'competation' => $return1];
    } 
}

