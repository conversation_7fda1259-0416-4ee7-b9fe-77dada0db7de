<?php

require APPPATH . 'libraries/REST_Controller.php';

class Calendar extends REST_Controller
{
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
            $data = ['error' => 'User not logged in'];
            $this->response($data, REST_Controller::HTTP_UNAUTHORIZED);
			$this->output->_display();
			exit;
		}
		$this->load->model('parentv2/Calendar_model');
	}

	public function month_events_get() {
		//TBD: Validate that Parent ID belongs to this User

		$applicable_to = $this->get('applicable_to');
		$month = $this->get('month');
		// $state = $this->get('state');
		if (empty($month)) {
			$month = date('m');
		}
        // trigger_error($state);
        // if($state == 'next') {
        //     $month = date('Y-m', strtotime('+1 month', strtotime($month)));
        // } else if($state == 'prev') {
        //     $month = date('Y-m', strtotime('-1 month', strtotime($month)));
        // }

		// $data = $this->Calendar_model->get_month_events($month, $applicable_to);
		$data = $this->Calendar_model->get_events_all($applicable_to,$month);

        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

}

?>