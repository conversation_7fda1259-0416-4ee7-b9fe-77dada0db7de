<?php

class Group extends CI_Controller {
    private $yearId;
    private $smsStatusCodes;
	function __construct() {
      	parent::__construct();
      	if (!$this->ion_auth->logged_in()) {
        	redirect('auth/login', 'refresh');
		}
		$this->load->model('communication/group_model');
      	$this->load->model('class_section');
      	$this->load->model('communication/texting_model');
	}

	public function group_creation(){
		$data['templates'] = $this->texting_model->getApprovedTextingTemplates();
		$data['credits'] = $this->texting_model->getRemainingSMSCredits();
		$data['classes'] = $this->class_section->getAllClassess();
		$data['batches'] = $this->class_section->get_batches();
		$data['class_section'] = $this->class_section->getAllClassSections();
		$this->load->model('staff/Staff_Model');
		$data['staff_details'] = $this->Staff_Model->getAll_Staff();
		$data['groups'] = $this->texting_model->get_groups_details();
        if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'communication/group/group_creation_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'communication/group/group_creation_mobile';
        }else{
			$data['main_content'] = 'communication/group/group_creation';    	
        }
	    $this->load->view('inc/template', $data);
	}

	public function create_group(){
		$data['templates'] = $this->texting_model->getApprovedTextingTemplates();
		$data['credits'] = $this->texting_model->getRemainingSMSCredits();
		$data['classes'] = $this->class_section->getAllClassess();
		$data['batches'] = $this->class_section->get_batches();
		$data['class_section'] = $this->class_section->getAllClassSections();
		$this->load->model('staff/Staff_Model');
		$data['staff_details'] = $this->Staff_Model->getAll_Staff();
		$data['staff_departments'] = $this->Staff_Model->getStaffDepartmentList();
		$data['staff_designations'] = $this->Staff_Model->getStaffDesignationList();
        if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'communication/group/create_group_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'communication/group/create_group_mobile';
        }else{
			$data['main_content'] = 'communication/group/create_group';      	
        }
	    $this->load->view('inc/template', $data);
	}

	public function group_insert() {
		$input = $this->input->post();
		$group_data = array();
		$group_data['students'] = array();
		$group_data['class_section'] = array();
		$group_data['staff'] = array();
		$group_data['custom'] = array();

		
		if(isset($input['student_ids'])) {
			$group_data['students'] = $input['student_ids'];
		}

		if(isset($input['section_ids'])) {
			$group_data['class_section'] = $input['section_ids'];
		}

		if(isset($input['staff_ids'])) {
			$group_data['staff'] = $input['staff_ids'];
		}
		if($input['group_name'] != '') {
			$group_json = json_encode($group_data);

			$result = $this->group_model->saveGroup($input['group_name'], $group_json);
			if($result) {
				$this->session->set_flashdata('flashSuccess', 'New group successfully created');
			} else {
				$this->session->set_flashdata('flashError', 'Something went wrong!');
			}
		}
		redirect('communication/group/group_creation');
	}

	public function group_update(){
		$input = $this->input->post();
		$group_data = array();
		$group_data['students'] = array();
		$group_data['class_section'] = array();
		$group_data['staff'] = array();

		
		if(isset($input['student_ids'])) {
			$group_data['students'] = $input['student_ids'];
		}

		if(isset($input['section_ids'])) {
			$group_data['class_section'] = $input['section_ids'];
		}

		if(isset($input['staff_ids'])) {
			$group_data['staff'] = $input['staff_ids'];
		}
		$group_json = json_encode($group_data);

		$result = $this->group_model->update_group($input['group_id'], $group_json);
		if($result) {
			$this->session->set_flashdata('flashSuccess', 'Successfully Update');
		} else {
			$this->session->set_flashdata('flashError', 'Something went wrong!');
		}
		redirect('communication/group/group_creation');
	}
	public function get_group_members() {
		$group_id = $_POST['group_id'];
		$group_data = $this->group_model->getGroupMembers_by_group_id($group_id);
		echo json_encode($group_data);
	}

	public function get_student(){
		$class_id =$this->input->post('className');
		$section_id =$this->input->post('section');
		$acad_year =$this->input->post('acad_year');
		$batch =$this->input->post('batch');
		$group_id =$this->input->post('group_id');
		$result = $this->group_model->get_studentclassSectionwise($class_id,$section_id, $acad_year, $batch, $group_id);
		echo json_encode($result);
	}

	public function get_staff(){
		$group_id =$this->input->post('group_id');
		$result = $this->group_model->get_staff_group($group_id);
		echo json_encode($result);
	}

	public function get_class(){
		$group_id =$this->input->post('group_id');
		$result = $this->group_model->get_class_group($group_id);
		echo json_encode($result);
	}

	public function check_group_name_exits(){
	 	$group = $_POST['group'];
      	if($this->group_model->check_group_name_exits_in_db($group))
	        echo 'exists';
     	else
	        echo 'not found';
	}

	public function group_switch_status(){
	 	$stngId = $_POST['stngId'];
	    $value = $_POST['value'];
	    echo $this->group_model->update_group_status($stngId,$value); 
	}
	
}