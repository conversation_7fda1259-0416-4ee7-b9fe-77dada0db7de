<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  02 June 2018
 *
 * Description: Model class for Transportation report view
 *
 * Requirements: PHP5 or above
 *
 */

class Transportation_report extends CI_Controller {
	function __construct() {
		  parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      $this->load->model('report/fee_report_model');
      $this->load->model('report/transportation_report_model');
    }
    
    public function index($filterMode=''){
      $data['classes'] =  $this->fee_report_model->get_Allclasses();
      $data['stops'] =  $this->transportation_report_model->getAllStops();
      $stop = $this->input->post('stop');
      $from_date = $this->input->post('from_date');
      $to_date = $this->input->post('to_date');
      if ($filterMode == '1')  {
        $data['SelectedClass'] = '';
        $data['SelectedStop'] = '';
        $data['SelectedFrom_date'] = '';
        $data['SelectedTo_date'] = '';
        $transport = $this->transportation_report_model->ChallanDate();
      }else{
        $classId = $this->input->post('class_name');
        if (empty($classId)) {
          $classId = 0; //0 signifies show all classes
        } else {
          if ($classId == 'All') {
            $classId = 0; //0 signifies show all classes
          }
        }
        $transport = $this->transportation_report_model->paidBetween($classId,$stop,$from_date,$to_date);
        $stop = $this->input->post('stop');
        $from_date = $this->input->post('from_date');
        $to_date = $this->input->post('to_date');   
        $data['SelectedClass'] = $classId;
        $data['SelectedStop'] = $stop;
        $data['SelectedFrom_date'] = $from_date;
        $data['SelectedTo_date'] = $to_date;
      }
      $data['result_Data'] = $transport;
      $data['main_content'] = 'reports/transportation/index';
      $this->load->view('inc/template', $data);
    }

}