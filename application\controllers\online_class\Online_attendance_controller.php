<?php

  defined('BASEPATH') OR exit('No direct script access allowed');

  class Online_attendance_controller extends CI_Controller {

    private $year_id;

    public function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      // if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
      //   redirect('dashboard', 'refresh');
      // }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('parent_model');
      $this->load->model('online_class/Schedule_model', 'schedule');
      $this->load->model('online_class/Online_attendance_model','oam');
      $this->load->library('openvidu');
      $this->load->library('filemanager');
      // $this->load->library('mailer', 'mailer');
      $this->load->helper('texting_helper');
    }

    public function markAttendance(){
      $data = $this->oam->markAttendance();
      echo json_encode($data);
    }

    public function markAttendance_LeaveTime(){
      $data = $this->oam->updateLeaveTime();
      echo json_encode($data);
    }

    public function hostJoining() {
      $data = $this->oam->updateHostJoining();
      echo json_encode($data);
    }

    public function viewScheduleReport(){
      $data['total'] = $this->oam->getTotalSchedulesCount();
      $data['previous'] = $this->oam->getPreviousScheduleCount();
      // echo "<pre>";print_r($data);die();
      if ($this->mobile_detect->isMobile()) {
        $data['main_content'] = 'online_class/attendance/scheduleReport_mobile.php';
      } else {
        $data['main_content']    = 'online_class/attendance/view_scheduleReport.php';
      }
      // $data['main_content'] = 'online_class/attendance/view_scheduleReport.php';
      $this->load->view('inc/template', $data);
    }

    public function getScheculeDetails(){
      $data = $this->oam->getScheculeDetails();
      echo json_encode($data);
    }

    public function getInvitedStudents(){
      $schedule_id = $_POST['schedule_id'];
      $data['invited']= $this->oam->getInvitedStudents($this->yearId,$schedule_id);
      $data['not_participated'] = $this->oam->getNotParticipatedStudents($this->yearId,$schedule_id);
      $data['participated']= $this->oam->getParticipatedStudents($this->yearId,$schedule_id);
      echo json_encode($data);
    }

    public function getNotParticipatedStudents(){
      $schedule_id = $_POST['schedule_id'];
      $data = $this->oam->getNotParticipatedStudents($this->yearId,$schedule_id);
      echo json_encode($data);
    }

    public function getParticipatedStudents(){
      $schedule_id = $_POST['schedule_id'];
      $data['time'] = $this->oam->getScheduleTime($this->yearId,$schedule_id);
      $data['participated'] = $this->oam->getParticipatedStudents($this->yearId,$schedule_id);
      echo json_encode($data);
    }

    public function dashboard(){
      $data['main_content'] = 'online_class/attendance/menu.php';
      $this->load->view('inc/template', $data);
    }
  
    public function onlineClassReport(){
        $data['schedules'] = $this->oam->getSchedules($this->yearId);
        // echo "<pre>";print_r($data);die();
        $data['main_content'] = 'online_class/attendance/onlineClassReport.php';
        $this->load->view('inc/template', $data);
    }

    public function generateOnlineClassReport(){
      $data  = $this->oam->generateOnlineClassReport();
      echo json_encode($data);
    }

    public function studentWiseReport(){
      $data['is_mobile'] = $this->mobile_detect->isMobile();
      $data['sections'] = $this->oam->getAllClassSections();
      if($this->mobile_detect->isMobile()) {
        $data['main_content'] = 'online_class/attendance/studentWiseReportMobile.php';
      } else {
        $data['main_content'] = 'online_class/attendance/studentWiseReport.php';
      }
      $this->load->view('inc/template', $data);
    }

    public function getSectionStudents(){
      $section_id = $_POST['section_id'];
      $data['students'] = $this->oam->getSectionStudents($section_id, $this->yearId);
      echo json_encode($data);
    }

    public function getSingleStudentDetails(){
      $data = $this->oam->getSingleStudentDetails($this->yearId);
      echo json_encode($data);
    }

    public function getStudentAttendanceData() {
      $student_id = $_POST['student_id'];
      $from_date = date('Y-m-d', strtotime($_POST['from_date']));
      $to_date = date('Y-m-d', strtotime($_POST['to_date']));
      $data = $this->oam->getStudentAttendanceSummary($student_id, $from_date, $to_date);
      echo json_encode($data);
    }

    public function getManagementReport() {
      $staff_id = $_POST['staff_id'];
      $room_id = $_POST['room_id'];
      $from_date = date('Y-m-d', strtotime($_POST['from_date']));
      $to_date = date('Y-m-d', strtotime($_POST['to_date']));
      $data = $this->oam->getManagementReportSummary($staff_id, $room_id, $from_date, $to_date);
      echo json_encode($data);
    }

    public function getConnectionDetails(){
      $data['connection'] = $this->oam->getConnectionDetails();
      $data['schedule'] = $this->oam->getScheduleDetails();
      echo json_encode($data);
    }

    public function managementReport(){
      $data['staff'] = $this->schedule->getAllStaff();
      $data['sections'] = $this->oam->getAllClassSections();
      $data['rooms'] = $this->schedule->getClassRooms();
      // echo "<pre>";print_r($data);die();
      if($this->mobile_detect->isMobile()) {
        $data['main_content'] = 'online_class/attendance/managementReportMobile.php';
      } else {
        $data['main_content'] = 'online_class/attendance/managementReport.php';
      }
      $this->load->view('inc/template', $data);
    }

    public function generateManagementReport(){
      $data['schedules'] = $this->oam->generateManagementReport($this->yearId);
      $data['presentees'] = $this->oam->getPresenteesCount();
      // echo "<pre>";print_r($data);die();
      echo json_encode($data);
    }

    public function getInvitedStudentsDetails(){
      $data = $this->oam->getInvitedStudentsDetails($this->yearId);
      echo json_encode($data);
    }

    public function getDataforSMS(){
      $max_percentage = $this->settings->getSetting('online_attendance_percentage');
      if(empty($max_percentage)){
        $max_percentage = 0;
      }
      $data['max_percentage'] = $max_percentage;
      $data['schedule'] = $this->oam->getScheduleDetails();
      $data['no_participation'] = $this->oam->getNoParticipationStudents($this->yearId);
      $data['participation'] = $this->oam->getParticipationStudents($this->yearId);
      echo json_encode($data);
    }
    public function sendAbsentSMS(){
      $message = $this->settings->getSetting('online_absent_message');
      $schedule = $this->oam->getScheduleDetails();
      $sendTo = $_POST['sendTo'];
      if(isset($_POST['student_ids']) && count($_POST['student_ids'])!=0){
        $no_participation = $this->oam->getStudentsDetailsSMS($this->yearId,$_POST['student_ids']);
      }
      if(isset($_POST['student_ids_part']) && count($_POST['student_ids_part'])!=0){
        $participation = $this->oam->getStudentsDetailsSMS($this->yearId,$_POST['student_ids_part']);
      }
      $mesages_to_send = array();
      $mesages_to_send_part = array();
      $result2=0;
      $result1=0;
      if(isset($_POST['student_ids']) && count($_POST['student_ids'])){
        foreach ($no_participation as $key => $value) {
            $temp=$message;
            $temp=str_replace("%%student_name%%", $value->std_name, $temp);
            $temp=str_replace("%%class_name%%", $value->class_name, $temp);
            $temp=str_replace("%%section_name%%", $value->section_name, $temp);
            $temp=str_replace("%%schedule_name%%", $schedule[0]->name, $temp);
            $mesages_to_send[$value->student_id] = $temp;
        }
        $result1 = $this->__sendSMS($mesages_to_send,$_POST['sendTo']);
      }
      if(isset($_POST['student_ids_part']) && count($_POST['student_ids_part'])){
        foreach ($participation as $key => $value) {
            $temp=$message;
            $temp=str_replace("%%student_name%%", $value->std_name, $temp);
            $temp=str_replace("%%class_name%%", $value->class_name, $temp);
            $temp=str_replace("%%section_name%%", $value->section_name, $temp);
            $temp=str_replace("%%schedule_name%%", $schedule[0]->name, $temp);
            $mesages_to_send_part[$value->student_id] = $temp;
        }
        $result2 = $this->__sendSMS($mesages_to_send_part,$_POST['sendTo']);
      }      
      $result = $result1+$result2;
      echo json_encode($result);
      // echo "<pre>";print_r($mesages_to_send);die();
    }

    private function __sendSMS ($mesages_to_send,$sendTo) {
        $sent_by = 1;
        $input_arr = array();
        $input_arr['student_id_messages'] = $mesages_to_send;
        $input_arr['source'] = 'Online Class';
        $text_send_to = $sendTo;
        $input_arr['mode'] = 'sms';
        $input_arr['send_to'] = $text_send_to;
        $success = sendUniqueText($input_arr);
        if($success['success'] != ''){
            $insId1 = 1;
        } else {
            $insId1 = 0;
        }
        return $insId1;
    }

    public function class_wise_report() {
      $data['sections'] = $this->oam->getAllClassSections();
      $data['main_content'] = 'online_class/attendance/class_wise.php';
      $this->load->view('inc/template', $data);
    }

    public function getClassWiseData() {
      $section_id = $_POST['section_id'];
      $date = date('Y-m-d', strtotime($_POST['date']));
      echo json_encode($this->oam->getClassWiseData($section_id, $date));
    }
  }
?>