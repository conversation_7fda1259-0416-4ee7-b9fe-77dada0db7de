<?php

class Reports extends CI_Controller
{

  function __construct()
  {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_ATTENDANCE_V2')) {
      redirect('dashboard', 'refresh');
    }

    if (!$this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('class_section');
    $this->load->model('attendance_v2/attendance_model');
  }

  public function section_wise()
  {
    $data['attendance_type'] = 'subject_wise';
    $att_type = $this->settings->getSetting('student_attendance_type');
    if ($att_type) {
      $data['attendance_type'] = $att_type;
    }
    $data['is_semester_scheme'] = 0;
    if ($this->settings->getSetting('is_semester_scheme')) {
      $data['is_semester_scheme'] = 1;
    }
    $data['sections'] = $this->class_section->getAllClassSections();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance_v2/reports/section_wise_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'attendance_v2/reports/section_wise_mobile';
    } else {
      $data['main_content'] = 'attendance_v2/reports/section_wise';
    }
    $this->load->view('inc/template', $data);
  }

  public function getSectionWise()
  {
    $data = $this->attendance_model->getSectionWise();
    echo json_encode($data);
  }

  public function section_subject()
  {
    $data['attendance_type'] = 'subject_wise';
    $att_type = $this->settings->getSetting('student_attendance_type');
    if ($att_type) {
      $data['attendance_type'] = $att_type;
    }
    $data['sections'] = $this->class_section->getAllClassSections();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance_v2/reports/section_subject_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'attendance_v2/reports/section_subject_mobile';
    } else {
      $data['main_content'] = 'attendance_v2/reports/section_subject';
    }
    $this->load->view('inc/template', $data);
  }

  public function month_wise(){
    $data['attendance_type'] = 'subject_wise';
    $att_type = $this->settings->getSetting('student_attendance_type');
    if ($att_type) {
      $data['attendance_type'] = $att_type;
    }

    $data['sections'] = $this->class_section->getAllClassSections();
    
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance_v2/reports/subject_month_wise_mobile';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'attendance_v2/reports/subject_month_wise_mobile';
    } else {
      $data['main_content'] = 'attendance_v2/reports/subject_month_wise';
    }
    $this->load->view('inc/template', $data);
  }

  public function get_subject_month_wise_attendance(){
    $data = $this->attendance_model->get_subject_month_wise_attendance($_POST);
    echo json_encode($data);
  }

  public function get_subjects_by_class_section(){
    $data = $this->attendance_model->get_subjects_by_class_section($_POST);
    echo json_encode($data);
  }

  public function section_subject_with_periods()
  {
    $data['attendance_type'] = 'subject_wise';
    $att_type = $this->settings->getSetting('student_attendance_type');
    if ($att_type) {
      $data['attendance_type'] = $att_type;
    }
    $data['sections'] = $this->class_section->getAllClassSections();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance_v2/reports/section_subject_with_periods_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'attendance_v2/reports/section_subject_with_periods_mobile';
    } else {
      $data['main_content'] = 'attendance_v2/reports/section_subject_with_periods';
    }
    $this->load->view('inc/template', $data);
  }

   public function overall_period_wise_attendance_report(){
    $data['attendance_type'] = 'subject_wise';
    $att_type = $this->settings->getSetting('student_attendance_type');
    if ($att_type) {
      $data['attendance_type'] = $att_type;
    }
    
    $data['sections'] = $this->class_section->getAllClassSections();

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance_v2/reports/overall_period_wise_attendance_report/index';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'attendance_v2/reports/overall_period_wise_attendance_report/index';
    } else {
      $data['main_content'] = 'attendance_v2/reports/overall_period_wise_attendance_report/index';
    }
    $this->load->view('inc/template', $data);
  }

  public function period_wise_attendance_report(){
    $data['attendance_type'] = 'subject_wise';
    $att_type = $this->settings->getSetting('student_attendance_type');
    if ($att_type) {
      $data['attendance_type'] = $att_type;
    }

    $data['sections'] = $this->class_section->getAllClassSections();

    $data['main_content'] = 'attendance_v2/reports/period_wise_attendance_report/index';
    $this->load->view('inc/template', $data);
  }

  public function get_period_wise_attendance_report(){
    $attendance_data=$this->attendance_model->get_period_wise_attendance_report($_POST);
    echo json_encode($attendance_data);
  }

  public function getSectionSubjectData()
  {
    $data = $this->attendance_model->getSectionSubjectData();
    echo json_encode($data);
  }

  public function getSectionSubjectDataWithPeriods()
  {
    $data = $this->attendance_model->getSectionSubjectData();
    $data['actual_period_lists'] = $this->attendance_model->getActualPeriodLists($_POST);
    echo json_encode($data);
  }

  public function get_overall_period_wise_attendance(){
    $result = $this->attendance_model->get_overall_period_wise_attendance($data);
    echo json_encode($result);
  }

  public function student_wise()
  {
    $data['attendance_type'] = 'subject_wise';
    $att_type = $this->settings->getSetting('student_attendance_type');
    if ($att_type) {
      $data['attendance_type'] = $att_type;
    }
    $data['sections'] = $this->class_section->getAllClassSections();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance_v2/reports/student_wise_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'attendance_v2/reports/student_wise_mobile';
    } else {
      $data['main_content'] = 'attendance_v2/reports/student_wise';
    }
    $this->load->view('inc/template', $data);
  }

  public function getSectionStudentNames()
  {
    $section_id = $_POST['section_id'];
    $data['students'] = $this->attendance_model->getSectionStudentNames($section_id);
    echo json_encode($data);
  }

  public function getStudentWise()
  {
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));
    $student_id = $_POST['student_id'];
    $data['attendance'] = $this->attendance_model->getAttendanceDataByDateRange($from_date, $to_date, $student_id);
    echo json_encode($data);
  }

  public function section_summary()
  {
    $data['sections'] = $this->class_section->getAllClassSections();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance_v2/reports/section_summary_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'attendance_v2/reports/section_summary_mobile';
    } else {
      $data['main_content'] = 'attendance_v2/reports/section_summary';
    }
    $this->load->view('inc/template', $data);
  }

  public function getSectionWiseSummary()
  {
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));
    $section_id = $_POST['section_id'];
    $data = $this->attendance_model->getSectionSummaryByDateRange($from_date, $to_date, $section_id);
    echo json_encode($data);
  }

  public function summary_report()
  {
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance_v2/reports/summary_report_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'attendance_v2/reports/summary_report_mobile';
    } else {
      $data['main_content'] = 'attendance_v2/reports/summary_report';
    }
    $this->load->view('inc/template', $data);
  }

  public function getSummaryReport()
  {
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));
    $data = $this->attendance_model->getSummaryReport($from_date, $to_date);
    echo json_encode($data);
  }

  public function day_wise_report()
  {
    $data['sections'] = $this->class_section->getAllClassSections();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance_v2/reports/day_wise_report_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'attendance_v2/reports/day_wise_report_mobile';
    } else {
      $data['main_content'] = 'attendance_v2/reports/day_wise_report';
    }
    $this->load->view('inc/template', $data);
  }

  public function getDayWiseReport()
  {
    $data = $this->attendance_model->getDayWise();
    echo json_encode($data);
  }

  public function getSectionSubjectsORSessions()
  {
    $section_id = $_POST['section_id'];
    $data['attendance_type'] = $_POST['attendance_type'];
    if ($data['attendance_type'] == 'subject_wise') {
      $data['subjects'] = $this->attendance_model->getSectionSemesterSubjets($section_id);
    } else if ($data['attendance_type'] == 'session_wise') {
      $data['sessions'] = [];
    }
    echo json_encode($data);
  }

  public function notify_absentees()
  {
    $data['classes'] = $this->class_section->getAllClassess();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance_v2/reports/notify_absentees_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'attendance_v2/reports/notify_absentees_mobile';
    } else {
      $data['main_content'] = 'attendance_v2/reports/notify_absentees';
    }
    $this->load->view('inc/template', $data);
  }

  public function notifications_for_late_absentees(){
    $data['classes'] = $this->class_section->getAllClassess();
    $data['main_content'] = 'attendance_v2/reports/notifications_for_late_absentees/index_desktop.php';
    $this->load->view('inc/template', $data);
  }

  public function get_absent_late_data()
  {
    $students = $this->attendance_model->get_absent_late_data();
    echo json_encode($students);
  }

  public function send_messages()
  {
    // echo "<pre>"; print_r($_POST); die();
    $this->load->helper('texting_helper');
    $input = array();
    $input['source'] = 'Attendance';
    // $text_send_to = $this->settings->getSetting('text_send_to');
    $text_send_to = $_POST['text_send_to'];
    $input['mode'] = $_POST['communication_mode'];
    $input['student_id_messages'] = $_POST['student_messages'];
    $status = 0;
    $error = 0;
    $warning = 0;
    if ($input['mode'] == 'notification') {
      $input['send_to'] = 'Both';
      $res = sendUniqueText($input);
      if ($res['success'] != '') $status = 1;
      $error = $res['error'];
      $warning = $res['warning'];
    } else {
      if ($text_send_to == 'preferred') {
        $input['send_to'] = 'preferred';
        $res = sendUniqueText($input);
        if ($res['success'] != '') $status = 1;
        $error = $res['error'];
        $warning = $res['warning'];
      } else if ($text_send_to == 'preferred_parent') {
        $input['send_to'] = 'preferred';
        $res = sendUniqueText($input);
        if ($res['success'] != '') $status = 1;
        $error = $res['error'];
        $warning = $res['warning'];
      } else if ($text_send_to == '' || $text_send_to == 'Both') {
        $input['send_to'] = 'Both';
        $res = sendUniqueText($input);
        if ($res['success'] != '') $status = 1;
        $error = $res['error'];
        $warning = $res['warning'];
      } else if ($text_send_to == 'Father') {
        //sending to father
        $input['send_to'] = 'Father';
        $res = sendUniqueText($input);
        if ($res['success'] != '') $status = 1;
        $error = $res['error'];
        $warning = $res['warning'];
      } else if ($text_send_to == 'Mother') {
        //sending to mother
        $input['send_to'] = 'Mother';
        $res = sendUniqueText($input);
        if ($res['success'] != '') $status = 1;
        $error = $res['error'];
        $warning = $res['warning'];
      }
    }
    echo json_encode(['status' => $status, 'error' => $error, 'warning' => $warning]);
  }
  public function get_late_absentees_students(){
    $students = $this->attendance_model->get_late_absentees_students($_POST);
    echo json_encode($students);
  }
}
