<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Student_controller
 *
 * <AUTHOR>
 */
class Student_profile_controller extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_MASTER')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT.BASIC_VIEW')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT_PROFILE.VIEW')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('student/Student_Model');
    $this->load->model('Observation_model');
    $this->config->load('form_elements');
    $this->load->library('filemanager');
  }
  public function Kolkata_datetime(){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $date->setTimezone($timezone );
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
  }
  //Landing function for Student Profile
  public function index($student_id =''){

    $data['canViewPD'] = $this->authorization->isAuthorized('STUDENT_PROFILE.VIEW_PERSONAL_DETAILS');
    $data['canViewSD'] = $this->authorization->isAuthorized('STUDENT_PROFILE.VIEW_SCHOOL_DETAILS');

    $data['canViewTrans'] = $this->authorization->isAuthorized('STUDENT_PROFILE.VIEW_TRANSPORT_DETAILS');
    $data['canViewHealth'] = $this->authorization->isAuthorized('STUDENT_PROFILE.VIEW_HEALTH_DETAILS');
    $data['canViewFees'] = $this->authorization->isModuleEnabled('FEEV2') && $this->authorization->isAuthorized('STUDENT_PROFILE.VIEW_FEES_DETAILS');
    $data['canViewEmerInfo'] = $this->authorization->isAuthorized('STUDENT_PROFILE.VIEW_EMERGENCY_INFO');
    $data['canViewAD'] = $this->authorization->isAuthorized('STUDENT_PROFILE.VIEW_ACTIVITY_DETAILS');
    $data['canSchoolDetails'] = $this->authorization->isAuthorized('STUDENT.SCHOOL_DETAILS');
    $id = $this->uri->segment(4);
    $data['stdata'] = $this->Student_Model->getFullStudentDataById($id);
    $data['sDetails'] = $this->Student_Model->get_SchoolDetailsbyStdId($id);
    // echo "<pre>"; print_r($data['stdata']); die();
    $data['boarding'] = $this->settings->getSetting('boarding');
    $data['rte'] = $this->settings->getSetting('rte');
    $data['admis_type'] = $this->settings->getSetting('admission_type');
    $data['boards'] = $this->settings->getSetting('board');
    $data['medium'] = $this->settings->getSetting('medium');
    $data['category'] = $this->settings->getSetting('category');
    $data['stdFatherData'] = $this->Student_Model->getParentDataById($id,"Father");
    $data['stdMotherData'] = $this->Student_Model->getParentDataById($id,"Mother");
    $data['std_address'] = $this->Student_Model->getStudent_addressDataById($id);
    $data['student_uid'] = $this->uri->segment(4);
    $data['stdData'] = $this->Student_Model->getStdData($data['student_uid']);
    $data['acad_years'] = $this->acad_year->getAllYearData();
    $data['acad_year_id'] = $this->acad_year->getAcadYearId();
    $data['profile_edit'] = $this->settings->getSetting('profile_edit_enabled',1,1);
    $data['transData'] = $this->Student_Model->getTransDataById($id);
    // echo "<pre>"; print_r($data['transData']); die();
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 

    $data['is_semester_scheme'] = $this->settings->getSetting('is_semester_scheme');
    $data['custom_field'] = $this->settings->getSetting('student_admission_custom_fields');
    //Observations --
    $data['canViewOb'] = $this->authorization->isModuleEnabled('STUDENT_OBSERVATION') && $this->authorization->isAuthorized('STUDENT_OBSERVATION.STUDENT_OBSERVATION_REPORT');
    $obData = array();
    if ($data['canViewOb'] == 1) {
      $observed = $this->Observation_model->get_student_observation_report($data['student_uid'], $data['acad_year_id'],$data['stdata']->csId);
      foreach ($observed as $value){
        $obData[$value->staff_name][] = array('date'=>date('d-m-Y', strtotime($value->created_on)), 'obs' => $value->observation);
      }
      $data['obData'] = $obData;
    }
    $data['electives'] = $this->Student_Model->getElectivesStudentwise($id);
    // echo "<pre>"; print_r($data['electives']); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'student/student_profile_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'student/student_profile_mobile';
    }else{
      $data['main_content']    = 'student/student_profile';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function downloadImage() {
    $url = $_POST['url'];
    $name = $_POST['name'];
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download($name.'.jpeg', $data, TRUE);
  }

  public function getStudentPerformanceData() {
    $acad_year_id = $_POST['acad_year'];
    
    $studentId = $_POST['student_id'];
    $stdData = $this->Student_Model->getStdDataByAcadYear($studentId, $acad_year_id);
    $classId = $stdData->class_id;
    $sectionId = $stdData->section_id;
    $assessments = $this->Student_Model->getAssessments($classId, $sectionId, $acad_year_id);
    $assIds = array();
    foreach ($assessments as $key => $value) {
      array_push($assIds, $value->assId);
    }
    $data['subWiseStatus'] = array();
    $data['assessments'] = array();
    
    $subWiseData = $this->Student_Model->getSubWiseReport($classId, $studentId, $assIds);
    if(!empty($subWiseData)) {
      $subWiseAvgData = $this->Student_Model->getSubWiseAvgReport($classId, $studentId, $assIds);

      $subWise = array();
      foreach ($subWiseData as $key => $value) {
        foreach ($subWiseAvgData as $k => $avg) {
          if($value->gId == $avg->gId && $value->assId == $avg->assId) {
            // echo "<pre>"; print_r($value);print_r($avg);die();
            $percentage = ($value->tMarks > 0) ? round(($value->marks/$value->tMarks)*100) : 0;
            $avgPercent = ($avg->tMarks > 0) ? round(($avg->marks/$avg->tMarks)*100) : 0;
            if(!array_key_exists($value->entity_name, $subWise)) {
              $subWise[$value->entity_name] = array();
              $subWise[$value->entity_name]['name'] = $value->entity_name;
              $subWise[$value->entity_name]['subId'] = $value->gId;
              $subWise[$value->entity_name]['total'] = 0;
              $subWise[$value->entity_name]['aboveAvg'] = 0;
              $subWise[$value->entity_name]['belowAvg'] = 0;
              $subWise[$value->entity_name]['Average'] = 0;
            }
            $subWise[$value->entity_name]['total']++;
            /*if($value->entity_name == 'HPE')
              echo $percentage.' - '.$avgPercent.'<br>';*/
            if($percentage == $avgPercent) {
              $subWise[$value->entity_name]['Average']++;
            } else if($percentage > $avgPercent) {
              $subWise[$value->entity_name]['aboveAvg']++;
            } else if($percentage < $avgPercent) {
              $subWise[$value->entity_name]['belowAvg']++;
            }
          }
        }
      }
      $statusWise = array();
      foreach ($subWise as $key => &$value) {
        if($value['total'] == $value['aboveAvg']) {
          $value['status'] = 'Above Average';
        }
        else if($value['total'] == $value['belowAvg']) {
          $value['status'] = 'Below Average';
        }
        else if($value['total'] == $value['Average']) {
          $value['status'] = 'Average';
        }
        else if(($value['belowAvg'] + $value['Average']) >= $value['aboveAvg']) {
          if($value['Average'] > $value['belowAvg'])
            $value['status'] = 'Average';
          else 
            $value['status'] = 'Below Average';
        }
        else if(($value['belowAvg'] + $value['Average']) < $value['aboveAvg']) {
            $value['status'] = 'Above Average';
        }

        $statusWise[$value['status']][] = $value;
      }
      ksort($statusWise);
      $data['subWiseStatus'] = $statusWise;
      $data['assessments'] = $assessments;
    }

    echo json_encode($data);
  }

  public function get_compeition_databystd_acad_yearwise(){
    $acad_year = $_POST['acad_year'];
    $student_id = $_POST['student_id'];
    $result = $this->Student_Model->get_compeitionstudentwise_acadyear($acad_year, $student_id);
    echo json_encode($result);
  }

  public function unlock_profile_confirmed($value=''){
    $this->db->where('id',$_POST['stdYearId']);
    echo $this->db->update('student_year',array('profile_confirmed'=>'No'));
  }
}