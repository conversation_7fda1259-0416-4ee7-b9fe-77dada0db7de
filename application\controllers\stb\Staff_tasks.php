<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>chhipulusu
 *          <EMAIL>
 *
 * Created:  16 January 2022
 *
 * Description: Controller for Staff Tasks Basket.
 *
 * Requirements: PHP5 or above
 *
 */

class Staff_tasks extends CI_Controller
{
  function __construct()
  {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STAFF_TASKS_BASKET')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('stb/Staff_tasks_model', 'task_model');
    $this->CI = &get_instance();
    $this->load->library('filemanager');
  }

  //Landing function to show non-compliance menu
  public function index()
  {
    $data['staff_list'] = $this->task_model->getStaffList();
    $data['basket_list'] = $this->task_model->getBasketList();
    $data['is_task_admin'] = $this->authorization->isAuthorized('STAFF_TASKS_BASKET.TASKS_ADMIN');
    $data['enable_add'] = $this->authorization->isAuthorized('STAFF_TASKS_BASKET.ADD');
    $data['enable_add'] = '1';
    $data['logged_in_staff_id'] = $this->authorization->getAvatarStakeHolderId();

    $data['main_content']    = 'stb/manage_task/index_desktop.php';
    $this->load->view('inc_v2/template', $data);
  }

  public function add_task()
  {
    $this->load->helper('texting_helper');

    //Step 1: Add the task
    $result = $this->task_model->add_task($_POST);

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;

      // Step 2: Send notifications that they are assigned a new task
      $staff_ids = isset($_POST['secondary_staff_list']) ? $_POST['secondary_staff_list'] : [];
      $staff_ids[] = $_POST['primary_staff_id'];
      $title = $_POST['task_title'];
      $this->_send_attendance_notification($staff_ids, "A new task is assigned to you in Staff task basket - $title");
    }

    echo json_encode($data);
  }

  public function clone_task()
  {
    echo $this->task_model->add_task($_POST);
  }

  private function _send_attendance_notification($staff_ids_to_send, $message)
  {
    $this->load->helper('texting_helper');

    $input_arr = array();
    $input_arr['staff_ids'] = $staff_ids_to_send;
    $input_arr['mode'] = 'notification';
    $input_arr['source'] = 'STB';
    $input_arr['message'] = $message;
    $response = sendText($input_arr);
  }

  public function get_task_list()
  {
    $task_filter = $_POST['task_filter'];
    $basket_id = $_POST['basket_id'];

    $data['task_list'] = $this->task_model->get_task_list($task_filter, $basket_id);
    $data['status_list'] = $this->task_model->get_priority_list();
    echo json_encode($data);
  }

  public function get_single_task_details()
  {
    // echo '<pre>';print_r($_POST);
    $task_id = $_POST['task_id'];

    $data['task_details'] = $this->task_model->get_single_task_details($task_id);

    echo json_encode($data);
  }

  public function get_single_task_details_points()
  {
    $task_id = $_POST['task_id'];

    $data['task_details'] = $this->task_model->get_single_task_details_points($task_id);
    echo json_encode($data);
  }

  public function get_single_task_resources()
  {
    // echo '<pre>';print_r($_POST);
    $task_id = $_POST['task_id'];
    $s3_url = $this->CI->config->item('s3_base_url');

    $data['task_resources'] = $this->task_model->get_single_task_resources($s3_url, $task_id);
    echo json_encode($data);
  }

  public function add_comment_and_status()
  {
    //Step 1: Add comment or change status
    $result = $this->task_model->add_comment_and_status($_POST);

    //Step 2: Send notification
    $task_details = $this->task_model->get_single_task_details($_POST['task_id']);

    $staff_ids = explode(',', $task_details->secondary_staff_ids);
    $staff_ids[] = $task_details->primary_staff_id;
    $staff_ids[] = $task_details->created_by_staff_id;
    $display_task_id = $task_details->display_task_id;
    $this->_send_attendance_notification($staff_ids, "A new comment added / status changed for staff task - $display_task_id");

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
      $data['task_details'] = $task_details;
    }

    echo json_encode($data);
  }

  public function add_comment_and_perc()
  {
    //Step 1: Add comment or change Percentage Completion
    $result = $this->task_model->add_comment_and_perc($_POST);

    //Step 2: Send notification
    $task_details = $this->task_model->get_single_task_details($_POST['task_id']);

    $staff_ids = explode(',', $task_details->secondary_staff_ids);
    $staff_ids[] = $task_details->primary_staff_id;
    $staff_ids[] = $task_details->created_by_staff_id;
    $display_task_id = $task_details->display_task_id;
    $this->_send_attendance_notification($staff_ids, "Priority changed for staff task - $display_task_id");

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }

    echo json_encode($data);
  }

  public function add_comment_and_priority()
  {
    //Step 1: Add comment or change Priority
    $result = $this->task_model->add_comment_and_priority($_POST);

    //Step 2: Send notification
    $task_details = $this->task_model->get_single_task_details($_POST['task_id']);

    $staff_ids = explode(',', $task_details->secondary_staff_ids);
    $staff_ids[] = $task_details->primary_staff_id;
    $staff_ids[] = $task_details->created_by_staff_id;
    $display_task_id = $task_details->display_task_id;
    $this->_send_attendance_notification($staff_ids, "A new comment added / status changed for staff task - $display_task_id");

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }

    echo json_encode($data);
  }

  public function add_to_basket()
  {
    $data = $this->task_model->add_to_basket($_POST['basket_name']);
    echo json_encode($data);
  }

  public function get_tasks_data()
  {
    $data = $this->task_model->get_tasks_data($_POST['taskId']);
    echo json_encode($data);
  }

  public function verify_task()
  {
    // echo '<pre>';print_r($_POST);die();

    //Step 1: Verify the task
    $result = $this->task_model->verify_task($_POST['verify_box_task_id'], $_POST['comment'], $_POST['secondary_staff_points_ids'], $_POST['secondary_effort_points'], $_POST['secondary_discipline_points'], $_POST['secondary_misc_points'], $_POST['secondary_total_points'], $_POST['primary_staff_points_id'], $_POST['primary_effort_points'], $_POST['primary_discipline_points'], $_POST['primary_misc_points'], $_POST['primary_total_points']);

    //Step 2: Send notification
    $task_details = $this->task_model->get_single_task_details($_POST['verify_box_task_id']);

    $staff_ids = explode(',', $task_details->secondary_staff_ids);
    $staff_ids[] = $task_details->created_by_staff_id;
    $display_task_id = $task_details->display_task_id;
    $this->_send_attendance_notification($staff_ids, "Task $display_task_id is marked as Verified");

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }

    echo json_encode($data);
  }

  public function reopen_task()
  {
    // echo '<pre>';print_r($_POST);die();

    //Step 1: Verify the task
    $result = $this->task_model->reopen_task($_POST['task_id'], $_POST['comment']);

    //Step 2: Send notification
    $task_details = $this->task_model->get_single_task_details($_POST['task_id']);

    $staff_ids = explode(',', $task_details->secondary_staff_ids);
    $staff_ids[] = $task_details->created_by_staff_id;
    $display_task_id = $task_details->display_task_id;
    $this->_send_attendance_notification($staff_ids, "Task $display_task_id is marked as Not Verified");

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
    }

    echo json_encode($data);
  }

  public function save_resources()
  {
    $data = $this->task_model->save_resources($_POST['task_id'], $_POST['paths']);
    echo json_encode($data);
  }

  public function mytasks(){
    $data['staff_list'] = $this->task_model->getStaffList();
    $data['basket_list'] = $this->task_model->getBasketList();
    $data['background_image'] = $this->task_model->getBackgroundImage();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'stb/manage_mytask/index_tablet.php';
    }else{
      $data['main_content']    = 'stb/manage_mytask/index_desktop.php';
    }
    $this->load->view('inc_v2/template', $data);
  }

  public function addmylist(){
    $result = $this->task_model->addmylist($_POST);
    echo json_encode($result);
  }

  public function getallmylists(){
    $result = $this->task_model->getallmylists();
    echo json_encode($result);
  }

  public function add_mytask(){
    $result = $this->task_model->add_mytask($_POST);
    echo json_encode($result);
  }

  public function getallmytasks(){
    $input=$this->input->post('list_id');
    $result = $this->task_model->getallmytasks($input);
    echo json_encode($result);
  }

  public function getallmytasksbydates(){
    $input=$this->input->post();
    $result = $this->task_model->getallmytasksbydates($input);
    echo json_encode($result);
  }

  public function get_list_by_id(){
    $input=$this->input->post('id');
    $result = $this->task_model->get_list_by_id($input);
    echo json_encode($result);
  }

  public function get_task_by_id(){
    $input = $this->input->post();
    $result = $this->task_model->get_task_by_id($input);
    echo json_encode($result);
  }

  public function getallcompletedmytasks(){
    $input=$this->input->post('list_id');
    $result = $this->task_model->getallcompletedmytasks($input);
    echo json_encode($result);
  }

  public function getalldelegatedmytasks(){
    $input=$this->input->post('list_id');
    $result = $this->task_model->getalldelegatedmytasks($input);
    echo json_encode($result);
  }

  public function get_completed_task_by_id(){
    $input = $this->input->post();
    $result = $this->task_model->get_completed_task_by_id($input);
    echo json_encode($result);
  }

  public function editmylist(){
    $input = $this->input->post();
    $result = $this->task_model->editmylist($input);
    echo json_encode($result);
  }

  public function editmytask(){
    $input = $this->input->post();
    $result = $this->task_model->editmytask($input);
    echo json_encode($result);
  }

  public function updateTaskCompletion(){
    $input = $this->input->post();
    $result = $this->task_model->updateTaskCompletion($input);
    echo json_encode($result);
  }

  public function delete_task(){
    $input = $this->input->post();
    $result = $this->task_model->delete_task($input);
    echo json_encode($result);
  }

  public function delete_list(){
    $input = $this->input->post();
    $result = $this->task_model->delete_list($input);
    echo json_encode($result);
  }

  public function add_list_color(){
    $input = $this->input->post();
    $result = $this->task_model->add_list_color($input);
    echo json_encode($result);
  }

  public function update_priority(){
    $input = $this->input->post();
    $result = $this->task_model->update_priority($input);
    echo json_encode($result);
  }

  public function delegate_task(){
    $input = $this->input->post();
    $result = $this->task_model->delegate_task($input);
    echo json_encode($result);
  }

  public function add_attachment(){
    $input = $this->input->post();
    $attached_file = $_FILES['attached_file'];
    $url = '';
    if(!empty($attached_file)){
      $url = $this->s3FileUpload($_FILES['attached_file'], 'stb_mytasks_attachments');
    }
    $result = $this->task_model->add_attachment($input, $url, $attached_file['name']);
    echo json_encode($result);
  }

  private function s3FileUpload($file, $folder_name = 'stb_mytasks_attachments')
  {
      if ($file['tmp_name'] == '' || $file['name'] == '') {
          return ['status' => 'empty', 'file_name' => ''];
      }
      return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
  }

  public function delete_attachment(){
    $input = $this->input->post();
    $result = $this->task_model->delete_attachment($input);
    echo json_encode($result);
  }

  public function add_comment(){
      $input = $this->input->post();
      $task_details = $this->task_model->get_task_comments_by_id($input['task_id']);
      $result = $this->task_model->add_comment($input);

      $data = array();
      if (!$result) {
        $data['result'] = 0;
      } else {
        $data['result'] = 1;
        $data['task_details'] = $task_details;
      }

      echo json_encode($data);
  }

  function update_list_tasks(){
    $input = $this->input->post();
    $result = $this->task_model->update_list_tasks($input);
    echo json_encode($result);
  }

  public function add_update_background(){
    $input = $this->input->post();
    $background_img = $_FILES['background_img'];
    $url = '';
    if(!empty($background_img)){
      $url = $this->s3FileUpload($_FILES['background_img'], 'stb_user_background');
    }
    $result = $this->task_model->add_user_background_img($url);
    echo "<pre>";print_r($result);die();

    echo json_encode($result);
  }

  public function add_due_date(){
    $input = $this->input->post();
    $input['due_date'] = date("Y-m-d", strtotime($_POST['due_date']));
    $result = $this->task_model->add_due_date($input);
    echo json_encode($result);
  }

  public function remove_due_date(){
    $input = $this->input->post();
    $result = $this->task_model->remove_due_date($input);
    echo json_encode($result);
  }

  public function delete_delegated_task(){
    $input = $this->input->post();
    $result = $this->task_model->delete_delegated_task($input);
    echo json_encode($result);
  }
}
