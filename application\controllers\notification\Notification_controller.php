<?php

class Notification_controller extends CI_Controller 
{
               
	function __construct()
	{
		parent::__construct();
	    $this->lang->load('master_lang','english');
	    $this->load->model('admin/notification_model');
	    
	}
   //circular
	public function index()
	{
		if (!$this->ion_auth->logged_in())
        {
            redirect('auth/login', 'refresh');
        }else{
        $data['circular']=$this->notification_model->get_list_infocircular();
        $data['main_content']    = 'admin/notification/circular_index';
        $this->load->view('includes_admin/template', $data);
       }
	}
    
	public function add_circular()
    {
        $data['main_content']    = 'admin/notification/circular_add';
        $this->load->view('includes_admin/template', $data);
    } 

    public function submit_circular()
    {
    	$result=$this->notification_model->submi_data_circular();
    	if($result){
           $this->session->set_flashdata('flashSuccess', 'Submit Successfully Circular.');
           redirect('admin/notification/notification_controller');
    	}else{
           $this->session->set_flashdata('flashError', 'Something Wrong..');
           redirect('admin/notification/notification_controller');
    	}
    }
    
    public function update_circular($id)
    {
    	$result=$this->notification_model->update_data_circular($id);
    	if($result){
           $this->session->set_flashdata('flashSuccess', 'Update Successfully Circular.');
           redirect('admin/notification/notification_controller');
    	}else{
           $this->session->set_flashdata('flashError', 'Something Wrong..');
           redirect('admin/notification/notification_controller');
    	}
    }

    public function edit_circular($id)
    {   
    	$data['circular_edit']=$this->notification_model->get_info_circularById($id);
    	$data['main_content']    = 'admin/notification/circular_edit';
      $this->load->view('includes_admin/template', $data);
    }

    public function delete_circular($id)
    {
    	$result=$this->notification_model->delete_circular($id);
    	if($result){
           $this->session->set_flashdata('flashSuccess', 'Delete Successfully Circular.');
           redirect('admin/notification/notification_controller');
    	}
    }
    //notification board
    public function notification_index()
    {
        $data['notifi']=$this->notification_model->get_list_notification();
        $data['main_content']    = 'admin/notification/notifi_index';
        $this->load->view('includes_admin/template', $data);
    }

    public function add_notification()
    {
        $data['main_content']    = 'admin/notification/notification_add';
        $this->load->view('includes_admin/template', $data);
    }

    public function edit_notifi($id)
    {  
       $data['editNotice']=$this->notification_model->get_infoByIdNotice($id);
       $data['main_content']    = 'admin/notification/notification_edit';
       $this->load->view('includes_admin/template', $data);
    }

    public function submit_notification()
    {
      $result=$this->notification_model->submit_data_notification();
      if($result){
           $this->session->set_flashdata('flashSuccess', 'Submit Successfully Notice Board.');
           redirect('admin/notification/notification_controller/notification_index');
      }else{
           $this->session->set_flashdata('flashError', 'Something Wrong..');
           redirect('admin/notification/notification_controller/notification_index');
      }
    }

   public function update_notification($id)
    {
      $result=$this->notification_model->update_data_notification($id);
      if($result){
           $this->session->set_flashdata('flashSuccess', 'Update Successfully Notice Board.');
           redirect('admin/notification/notification_controller/notification_index');
      }else{
           $this->session->set_flashdata('flashError', 'Something Wrong..');
           redirect('admin/notification/notification_controller/notification_index');
      }
    } 

    public function delete_notice_board($id)
    {
      $result=$this->notification_model->delete_notification($id);
      if($result){
           $this->session->set_flashdata('flashSuccess', 'Delete Successfully Notice Board.');
           redirect('admin/notification/notification_controller/notification_index');
      }
    }

    //jobs index

    public function jobs_index()
    {  
        $data['jobs']=$this->notification_model->list_jobs_info();
        $data['main_content']    = 'admin/notification/job_index';
        $this->load->view('includes_admin/template', $data);
    }

    public function add_jobs()
    {
       $data['main_content']    = 'admin/notification/add_job';
       $this->load->view('includes_admin/template', $data);
    }
    public function submit_jobs()
    {
      $result=$this->notification_model->submit_data_job();
      if($result){
           $this->session->set_flashdata('flashSuccess', 'Job Add Successfully.');
           redirect('admin/notification/notification_controller/jobs_index');
      }else{
           $this->session->set_flashdata('flashError', 'Something Wrong..');
           redirect('admin/notification/notification_controller/jobs_index');
      }
    }

    public function edit_jobs($id)
    {  
       $data['jobs_edit']=$this->notification_model->get_infoByIdJob($id);
       $data['main_content']    = 'admin/notification/edit_job';
       $this->load->view('includes_admin/template', $data);
    }

    public function update_job($id)
    {
      $result=$this->notification_model->update_data_job($id);
      if($result){
           $this->session->set_flashdata('flashSuccess', 'Job Add Successfully.');
           redirect('admin/notification/notification_controller/jobs_index');
      }else{
           $this->session->set_flashdata('flashError', 'Something Wrong..');
           redirect('admin/notification/notification_controller/jobs_index');
      }
    }

    public function delete_jobs($id)
    {
       $result=$this->notification_model->delete_job($id);
      if($result){
           $this->session->set_flashdata('flashSuccess', 'Delete Successfully.');
           redirect('admin/notification/notification_controller/jobs_index');
      }
    }
}
?>