<?php

  defined('BASEPATH') OR exit('No direct script access allowed');

  class Student_join_controller extends CI_Controller {

    private $year_id;
    private $online_server_domain;

    public function __construct() {
      parent::__construct();

      // header('Access-Control-Allow-Origin: *');
      // header('Cross-Origin-Resource-Policy: cross-origin');
      // header('Access-Control-Allow-Headers: Authorization,Content-Type,Accept,Origin,User-Agent,DNT,Cache-Control,X-Mx-ReqToken,Keep-Alive,X-Requested-With,If-Modified-Since');

      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
        redirect('dashboard', 'refresh');
      }

      if (CONFIG_ENV['domain'] === 'localhost') {
        $this->online_server_domain = 'meet.schoolelement.in';
      } else {
        $this->online_server_domain = $this->settings->getSetting('online_class_server');
      }
      // $this->load->model('Helpdesk_model', 'helpdesk'); 
      // $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('Parent_model','parent_model');
      $this->load->model('online_class/Online_classroom_model','ocm');
      // $this->load->library('openvidu');
      $this->load->library('filemanager');
    }

    public function index(){
      $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
      $std_data = $this->parent_model->getStudentDataById($studentId);
      $modules = json_decode($this->settings->getSetting('deactivation_modules'));
      $is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
      $data['is_deactivated'] = 0;
      if($is_deactivated && in_array('Online Class', $modules)) {
        $data['is_deactivated'] = 1;
      }
      else {
        $data['is_deactivated'] = 0;
      }
      
      $data['is_deactive'] = $this->parent_model->check_is_student_deactivated_temporarily($studentId);
      $data['recording_enabled'] = 0;
      $recording_enabled = $this->settings->getSetting('online_recording');
      if($recording_enabled) {
        $data['recording_enabled'] = $recording_enabled;
      }
    // echo "<pre>";print_r($data);die();
      // $data['schedule_list'] = $this->ocm->get_schedule_data_student($studentId, $std_data->sectionId);
      //$data['chatData'] = $this->helpdesk->getChatContent('Online Class');
      // $this->load->helper('chatdata_helper');
      // $data['chatData'] = getChatData('Online Class');
      // $data['path_prefix'] = $this->filemanager->getFilePath('');
      $data['type'] = 'Online Class';
      $data['back_url'] = site_url('dashboard');        
      
      
    if ($this->mobile_detect->isMobile()) {
      $data['is_mobile'] = 1;
      $data['main_content']    = 'online_class/parent/mobile_index.php';
    } else {
      $data['is_mobile'] = 0;
      $data['main_content']    = 'online_class/parent/index.php';
    }
      $this->load->view('inc/template', $data);  
    }

    public function join_window() {
      if(!isset($_POST['schedule_id'])) {
        redirect('online_class/student_join_controller');
      }
      $data['schedule_id'] = $_POST['schedule_id'];
      $data['server_date'] = date('d-m-Y');
      $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
      $data['schedule_room'] = $this->ocm->getRoomByScheduleId($data['schedule_id']);
      $data['student'] = $this->ocm->getStudentData($studentId);
      $data['student']->webcam_avatar = ($data['student']->webcam_avatar)?$this->filemanager->getFilePath($data['student']->webcam_avatar):'';
      // $data['student']->webcam_avatar = 'asdf';
      // echo "<pre>"; print_r($data['schedule_id']); die();
      if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'online_class/join_class/mobile_student_join_window.php';
      } else {
        $data['main_content']    = 'online_class/join_class/student_join_window.php';
      }
      $this->load->view('inc/template', $data);  
    }

    public function check_class_started() {
      $schedule_id = $_POST['schedule_id'];
      echo $this->ocm->check_class_started($schedule_id);
    }

    public function troubleshoot() {
      $data['schedule_id'] = $_POST['schedule_id'];
      $data['back_url'] = site_url('online_class/student_join_controller/join_window');
      if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'online_class/parent/mobile_troubleshoot.php';
      } else {
        $data['main_content']    = 'online_class/parent/troubleshoot.php';
      }
      $this->load->view('inc/template', $data);
    }

    public function join_class() {
      if(!isset($_POST['schedule_id'])) {
        redirect('online_class/student_join_controller');
      }
      $data['schedule_id'] = $_POST['schedule_id'];
      $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
      $data['schedule_room'] = $this->ocm->getRoomByScheduleId($data['schedule_id']);
      $data['student'] = $this->ocm->getStudentData($studentId);
      $data['student']->webcam_avatar = ($data['student']->webcam_avatar)?$this->filemanager->getFilePath($data['student']->webcam_avatar):'';
      $data['is_mobile'] = $this->mobile_detect->isMobile();
      $data["online_server_domain"] = $this->online_server_domain;
      // echo "<pre>"; print_r($data['schedule_id']); die();

      if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'online_class/parent/join_class/mobile_index.php';
      } else {
        $data['main_content']    = 'online_class/parent/join_class/index';
      }
      // $this->load->view('inc/template_virtualClass', $data);      
      $this->load->view($data['main_content'], $data);      
    }

    public function getSchedules() {
      $type = $_POST['type'];
      $from_date = $_POST['from_date'];
      $end_date = $_POST['end_date'];
      $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
      $schedules = $this->ocm->getStudentSchedules($studentId, $type, $from_date, $end_date);
      $data = array();
      $now = gmdate('Y-m-d H:i:s');
      foreach ($schedules as $key => $sch) {
        /*$schedules[$key]->session_status = 'Not Started';
        if (strtotime($sch->end_time)<strtotime($now)) {
          $schedules[$key]->session_status = 'Completed';
        } else if((strtotime($sch->start_time)<strtotime($now)) && (strtotime($sch->end_time)>strtotime($now))) {
          $schedules[$key]->session_status = 'Active';
        }*/
        $schedules[$key]->session_status = 'Not Started';
        $time = round((strtotime($sch->start_time) - strtotime($now)) / 60); //time diff in minutes
        if (strtotime($sch->end_time)<strtotime($now)) {
          $schedules[$key]->session_status = 'Completed';
        } else if((strtotime($sch->start_time)<=strtotime($now)) && (strtotime($sch->end_time)>=strtotime($now))) {
          $schedules[$key]->session_status = 'Active';
        }
        $sch->date = local_time($sch->start_time, 'd-M Y');
        $sch->start_time = local_time($sch->start_time, 'h:i a');
        $sch->end_time = local_time($sch->end_time, 'h:i a');
        $sch->now = local_time($now);
        if($sch->recording) {
          $schedules[$key]->recording = $this->filemanager->getFilePath($sch->recording);
        }
        $sch->paths = array();
        if($sch->files) {
          $paths = json_decode($sch->files);
          foreach ($paths as $path) {
            array_push($sch->paths, array('name' => $path->name, 'path' => $this->filemanager->getFilePath($path->path)));
          }
        }
        $data[] = $sch;
      }
      echo json_encode($data);
    }

    private function __checkStudentInSession($session_id, $studentId) {
      $session_data = $this->openvidu->get_session_details($session_id);
      $session = json_decode($session_data);
      if(!empty($session)) {
        $connections = $session->connections->content;
        foreach ($connections as $key => $con) {
          $c_data = json_decode($con->clientData);
          if($c_data->role == 'student') {
            list($role, $student_id) = explode("_", $c_data->clientId);
            if($studentId == $student_id) {
              return 1;
            }
          }
        }
      }
      return 0;
      // echo 'Data: <pre>'; print_r($session->connections); die();
    }

    public function generateJWT() {
      $this->load->library('JWT');
      $secret = "bmsjman09rac1ezin_tx";

      $payload = [
        "context" => [
          "user" => [
            "avatar" => "",
            "name" => $_POST['student_name'],
            "id" => $_POST['student_id']
          ]
        ],
        "aud" => "nextelement",
        "iss" => "nextelement",
        "sub" => $this->online_server_domain,
        "moderator" => false,
        "room" => $_POST['session_id'],
        "exp" => time() + (1 * 24 * 60 * 60)
      ];

      // echo '<pre>';print_r($payload);die();
      $jwt = JWT::encode($payload, $secret, 'HS256');
      echo json_encode(["jwt" => $jwt]);
    }


    //Creates a new OpenVidu session
    public function create_token () {
      $session_id = $_POST['session_id'];
      echo $this->openvidu->create_token($session_id, 100, 100);
    }

    public function save_answer() {
      $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
      $status = $this->ocm->saveStudentAnswer($student_id);
      echo $status;
    }

    public function get_questions() {
      $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
      $schedule_id = $_POST['schedule_id'];
      $questions = $this->ocm->getScheduleQuestions($student_id, $schedule_id);
      echo json_encode($questions);
    }

    public function s3FileUpload($file) {
      if ($file['tmp_name'] == '' || $file['name'] == '') {
        return ['status' => 'empty', 'file_name' => ''];
      }
      $path = $this->filemanager->uploadFile($file['tmp_name'], $file['name'], 'student_avatar');
      return $path;
    }

    public function save_avatar() {
      $file = $_FILES['avatar'];
      $path = $this->s3FileUpload($file);
      if($path['file_name'] != '') {
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $status = $this->ocm->save_student_avatar($student_id, $path['file_name']);
        echo $status;
      } else {
        echo 0;
      }
    }

    public function downloadFile() {
      $file_path = $_POST['file_path'];
      $file_name = $_POST['file_name'];
      $data = file_get_contents($file_path);
      $this->load->helper('download');
      force_download($file_name, $data, TRUE);
      $this->load->library('user_agent');
      redirect($this->agent->referrer());
    }

    public function getQuizStatus() {
      $schedule_id = $_POST['schedule_id'];
      $quiz = $this->ocm->getQuizDetail($schedule_id);
      echo $quiz->quiz_status;
    }

    public function getQuizQuestions() {
      $schedule_id = $_POST['schedule_id'];
      $quiz = $this->ocm->getQuizDetail($schedule_id);
      $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
      $questions = $this->ocm->getQuizQuestions($quiz->quiz_instance_id, $student_id);
      echo json_encode($questions);
    }

    public function saveQuiz() {
      $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
      $status = $this->ocm->saveQuizAnswers($student_id);
      echo $status;
    }

    public function getQuizResults() {
      $schedule_id = $_POST['schedule_id'];
      $quiz_instance_id = $_POST['quiz_instance_id'];
      $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
      $result = $this->ocm->getQuizResult($quiz_instance_id, $student_id);
      echo json_encode($result);
    }
  }

?>