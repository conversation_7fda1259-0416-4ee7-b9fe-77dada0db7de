<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  20 April 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_dashboard
 */
class Analytics extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isSuperAdmin()) {
      redirect('dashboard', 'refresh');
    }
  } 

  public function index(){
    $data['main_content'] = 'analytics/index';  
    $this->load->view('inc/template', $data);
  }
}