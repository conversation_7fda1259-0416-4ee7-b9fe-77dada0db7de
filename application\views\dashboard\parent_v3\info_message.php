<!-- Info Message Page - Desktop/Tablet Not Available -->

<style>
    .info-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 80vh;
        padding: 2rem 1rem;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .info-card {
        background: #fff;
        border-radius: 20px;
        padding: 3rem 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        max-width: 500px;
        width: 100%;
        margin: 0 auto;
    }

    .info-icon {
        font-size: 4rem;
        color: #7b5cff;
        margin-bottom: 1.5rem;
        animation: pulse 2s infinite;
    }

    .info-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1rem;
        line-height: 1.3;
    }

    .info-message {
        font-size: 1.1rem;
        color: #666;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .info-highlight {
        background: linear-gradient(135deg, #7b5cff 0%, #4e8cff 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        margin-bottom: 2rem;
        font-size: 1rem;
    }

    .device-icons {
        display: flex;
        justify-content: center;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .device-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        opacity: 0.3;
        transition: all 0.3s ease;
    }

    .device-icon.mobile {
        opacity: 1;
        color: #28a745;
    }

    .device-icon i {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
    }

    .device-icon span {
        font-size: 0.9rem;
        font-weight: 600;
    }

    .back-button {
        background: linear-gradient(135deg, #7b5cff 0%, #4e8cff 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.8rem 2rem;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .back-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(123, 92, 255, 0.3);
        color: white;
        text-decoration: none;
    }

    .features-list {
        text-align: left;
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 1.5rem;
    }

    .features-list h4 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .features-list ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .features-list li {
        padding: 0.5rem 0;
        color: #666;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .features-list li i {
        color: #28a745;
        font-size: 0.9rem;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .info-card {
            padding: 2rem 1.5rem;
            margin: 1rem;
        }

        .info-title {
            font-size: 1.5rem;
        }

        .info-message {
            font-size: 1rem;
        }

        .device-icons {
            gap: 1rem;
        }

        .device-icon i {
            font-size: 2rem;
        }
    }

    @media (min-width: 600px) {
        .info-container {
            max-width: 600px;
            margin: 0 auto;
        }
    }
</style>

<div class="info-container">
    <div class="info-card">
        <div class="info-icon">
            <i class="fa fa-mobile"></i>
        </div>

        <h1 class="info-title">Mobile Version Only</h1>

        <p class="info-message">
            Current desktop and tablet versions are not available. Please use the mobile version only for the best experience.
        </p>

        <div class="info-highlight">
            <i class="fa fa-info-circle me-2"></i>
            Switch to mobile view or use a mobile device to access all features
        </div>

        <div class="device-icons">
            <div class="device-icon">
                <i class="fa fa-desktop"></i>
                <span>Desktop</span>
            </div>
            <div class="device-icon">
                <i class="fa fa-tablet"></i>
                <span>Tablet</span>
            </div>
            <div class="device-icon mobile">
                <i class="fa fa-mobile"></i>
                <span>Mobile</span>
            </div>
        </div>

        <div class="features-list">
            <h4><i class="fa fa-check-circle me-2"></i>Available on Mobile:</h4>
            <ul>
                <li><i class="fa fa-check"></i> Student Profile & Information</li>
                <li><i class="fa fa-check"></i> Attendance & Academic Records</li>
                <li><i class="fa fa-check"></i> Fee Details & Payments</li>
                <li><i class="fa fa-check"></i> Circulars & Announcements</li>
                <li><i class="fa fa-check"></i> Parent-Teacher Communication</li>
                <li><i class="fa fa-check"></i> Real-time Notifications</li>
            </ul>
        </div>

        <a href="javascript:history.back()" class="back-button">
            <i class="fa fa-arrow-left"></i>
            Go Back
        </a>
    </div>
</div>

<!-- Additional CSS for utility classes -->
<style>
    .me-2 { margin-right: 0.5rem; }

    /* Animation for better user experience */
    .info-card {
        animation: slideUp 0.6s ease-out;
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Additional responsive improvements */
    @media (max-width: 480px) {
        .info-container {
            padding: 1rem 0.5rem;
            min-height: 70vh;
        }

        .info-card {
            padding: 1.5rem 1rem;
        }

        .info-icon {
            font-size: 3rem;
        }

        .device-icons {
            flex-wrap: wrap;
            gap: 1.5rem;
        }
    }
</style>