<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Studentleave_controller
 *
 * <AUTHOR>
 */
class Studentleave_admin_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->load->model('student/Student_leave_model');
        $this->load->model('avatar');
        $this->load->library('filemanager');
    }

    public function index() {
        $AvatarId = $this->authorization->getAvatarId();
        $data['avatar'] = $this->avatar->getAvatarById($AvatarId);

        $data['getFullStudentDetail'] = $this->Student_leave_model->get_student_leave_details();
        $data['classSection'] = $this->Student_leave_model->get_ClassSection_list();
        //   echo "<pre>"; print_r($data['getFullStudentDetail']); die();
        // $data['getFullStudentDetail'] = array();
        // if($this->authorization->isAuthorized('LEAVE.STUDENT_LEAVE_ADMIN')) {
        //     $data['getFullStudentDetail'] = $this->Student_leave_model->getStudentLeaves(0);
        // } else {
        //     $data['classSection'] = $this->Student_leave_model->getClassSection($data['avatar']->stakeholderId);
        //     if(!empty($data['classSection'])){
        //         $data['getFullStudentDetail'] = $this->Student_leave_model->getStudentLeaves($data['classSection']->SectionId);
        //     }
        // }

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'leave_approval/edit_studentleaveapporval';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'leave_approval/edit_studentleaveapporval_mobile';
        } else {
            $data['main_content'] = 'leave_approval/edit_studentleaveapporval';
        }

        $this->load->view('inc/template', $data);
    }


//     public function updateleave($stuid, $rowid) {

//         $data['getupdatedInfo'] = $this->Student_leave_model->getStudentLeavebyId($stuid, $rowid);
// //echo "<pre>";print_r($data['getFulldatanew']);die();
//         $data['main_content'] = 'leave_approval/getapporval_info';
//         $this->load->view('inc/template', $data);
//     }

    public function edit_student_leave(){
        $student_id = $_POST['student_id'];
        $leave_id = $_POST['leave_id'];
        $result = $this->Student_leave_model->getStudentLeavebyId($student_id, $leave_id);
        echo json_encode($result);
    }
    public function update_leaveapproval_application() {
      
        $rowid = $this->input->post('leave_id');
        $student_id = $this->input->post('student_id'); 
        $updateFulldata =(int) $this->Student_leave_model->updateleave($rowid, $student_id);
        if ($updateFulldata) {
            $leave_status = $this->input->post('status');
            $description = $this->input->post('description');
            $school_name = $this->settings->getSetting('school_name');
            $message = 'Leave rejected.';
            if($leave_status == 'Approved') {
                $message = 'Leave approved.';
            }
            $message .= $description;
            // $stdIds = [$student_id];
            $url = site_url('student/Student_leave_controller/index');

            $input_array = array(
                'mode' => 'notification', 
                'title' => $school_name, 
                'message' => $message, 
                'source' => 'Student Leave',
                'student_url' => site_url('student/Student_leave_controller/index'),
                'visible' => 1,
                'send_to' => 'Both',
                'acad_year_id' => $this->acad_year->getAcadYearId(),
                'student_ids' => [$student_id]
            );
            //echo "<pre>"; print_r($input_array);die();
            $this->load->helper('texting_helper');
            sendText($input_array);
            echo 1;
            // sendStudentNotifications($stdIds, $school_name, $message, $url, $send_to='Both');
            // $this->session->set_flashdata('flashSuccess', 'Leave Updated Successfully');
            // redirect('student/studentleave_admin_controller/index');
        } else {
            echo  0;
            // $this->session->set_flashdata('flashSuccess', 'Leave Not updated  ');
            // redirect('student/studentleave_admin_controller/index');
        }
    }

    public function viewCertificate($id){
        $data['cert'] = $this->Student_leave_model->getStudentLeaveColumnById($id, 'doctor_certificate');
        $data['main_content'] = 'leave_approval/view_certificate';
        $this->load->view('inc/template', $data);
    }

    public function get_leave_reportbyfilter(){
        $classId = $_POST['classId'];
        $leave_status = $_POST['leave_status'];
        $result = $this->Student_leave_model->get_student_leave_details_filter($classId, $leave_status);
        echo json_encode($result);
    }

}
