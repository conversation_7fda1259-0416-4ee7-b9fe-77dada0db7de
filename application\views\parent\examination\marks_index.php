<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li>Assessment Marks</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            Assessment Marks
          </h3> 
          <div class="d-flex justify-content-end align-items-center" style="width: 25%;">
            <?php if(!empty($acad_years)) { ?>
              <div class="text-muted" style="font-size: 1.2rem;">Select Academic Year:</div>
              <select onchange="getAssessments()" class="form-control ml-3" id="acad_year_id" style="width: 25%;">
                <?php foreach ($acad_years as $acad_year) {
                    echo "<option value='$acad_year->id'>$acad_year->acad_year</option>";
                } ?>
              </select>
            <?php } ?>
          </div>  
        </div>
      </div>
    </div>
    <div class="card-body pt-1">
      <?php 
        if(empty($acad_years)) { 
          echo '<br><div class="col-md-12 mb-5"><div class="no-data-display">No Assessment Marks Published as yet!</div></div>';
          die();
        }
      ?>
      <div class="col-md-4" id="assessment-data" style="max-height: 70vh; overflow-y: auto;">
        
      </div>
      <div class="col-md-8">
        <h5 id="assessment-title">&nbsp;</h5>
        <div style="max-height: 70vh; overflow-y: auto;" id="marks-data">
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  $(document).ready(function(){
    getAssessments();
  });

  function getAssessments() {
      var acad_year_id = $("#acad_year_id").val();
      if(acad_year_id === undefined) return false;

      $.ajax({
          url: '<?php echo site_url('parent/examination/getAssessments'); ?>',
          type: 'post',
          data: {'acad_year_id':acad_year_id},
          success: function(data) {
              data = JSON.parse(data);
              if(data.assessments.length == 0) {
                $("#marks-data").html(`<div class="col-md-12 mb-5"><div class="no-data-display">No Subjects Available</div></div>`);
                $("#assessment-data").html(`<div class="no-data-display">No Assessments Available</div>`);
                return;
              }
              constructAssessments(data.assessments);
          }
      });
  }

    function constructAssessments(assessments) {
      var html = '';
     
      for(var i=0; i<assessments.length; i++) {
        html += `
            <div class="d-flex justify-content-between align-items-center mt-3 px-3 py-2" style="border: 1px solid #ccc;border-radius: 8px;">
                <div>
                    <h5><b>${assessments[i].short_name}</b></h5>
                    <p class="text-muted">${assessments[i].long_name}</p>
                </div>
                <div>
                    <button data-toggle="modal" data-target="#marks-modal" data-name="${assessments[i].short_name}" data-long_name="${assessments[i].long_name}" id="ass-${assessments[i].id}" onclick="getAssessmentMarks(${assessments[i].id})" class="btn btn-primary">View Marks</button>
                </div>
            </div>
        `;
      }
      $("#marks-data").html(`<div class="col-md-12 mb-5"><div class="no-data-display">Click on 'View Marks' to show the marks</div></div>`);
      $("#assessment-data").html(html);
    }

    function getAssessmentMarks(id) {
      var name = $("#ass-"+id).data('name');
      var long_name = $("#ass-"+id).data('long_name');
      $("#assessment-title").html(`<b>${name}</b><br><p>${long_name}</p>`);
      $.ajax({
          url: '<?php echo site_url('parent/examination/getAssessmentMarks'); ?>',
          type: 'post',
          data: {'assessment_id':id},
          success: function(data) {
              data = JSON.parse(data);
              constructMarks(data.marks, data.computed)
          }
      });
    }

    function constructMarks(marks, computed_fields) {
      if(marks.length == 0 && computed_fields.length) {
        $("#marks-data").html(`<div class="no-data-display">Subjects Not Available</div>`);
        return;
      }
      var html = '';
      var total_marks = 0
      var total_obtained = 0

      html = `
        <table class="table table-bordered">
          <thead>
            <tr>
              <th>Subject</th>
              <th class="text-center">Marks</th>
            </tr>
          </thead>
          <tbody>
      `;
      if(marks.length) {
// Calculations: 
// 1. for -1: Absent, means total marks should calculate but marks obtained should not because it is -1.00
// 2. for -2: TBD, means total marks should calculate but marks obtained should not because it is -2.00
// 3. for -3: NA, means total marks should not calculate as well as marks obtained should not because it is not applicable to the particular student. 
// 4. for grade: marks obtained should not calculate but total marks should calculate because it is a grade and not a numerical value.
      for (var i = 0; i < marks.length; i++) {
        var subjects = marks[i].subjects;
        for (var k = 0; k < subjects.length; k++) {
          var display_marks = '';
          if(subjects[k].evaluation_type == 'grade') {
            display_marks = subjects[k].grade;
          } else {
            display_marks = `${subjects[k].marks} / ${subjects[k].total_marks}`;
            
            if(subjects[k].marks == '-3.00') {
                display_marks = 'NA';
            } else if(subjects[k].marks == '-2.00') {
                display_marks = 'TBD';

            // total_obtained += subjects[k].marks;
            total_marks += subjects[k].total_marks;

            } else if(subjects[k].marks == '-1.00') {
                display_marks = '<span class="text-danger">Absent</span>';

            // total_obtained += subjects[k].marks;
            total_marks += subjects[k].total_marks;

            }
            else {
                total_obtained += subjects[k].marks;
                total_marks += subjects[k].total_marks;
            }
          }
          html += `
            <tr>
              <td>${subjects[k].gName ? subjects[k].gName + ' <span class="fa fa-angle-double-right"></span> ' : ''}<b>${subjects[k].name}</b></td>
              <td class="text-center">${display_marks}</td>
            </tr>
          `;
        }
      }
    }
      html += `
            </tbody>
              <tr style="background-color:#f1f5f9; color:#56688a" class="">
                <th style="border-right: none;">TOTAL</th>
                <td style="border-left: none;" class="text-center"><strong>${total_obtained} / ${total_marks}</strong></td>
              </tr>`;
        if(computed_fields.length) {
          for(var v of computed_fields) {
            html += `<tr style="background-color:#f1f5f9; color:#56688a" class="">
                        <th style="border-right: none;">${v.name}</th>
                        <td style="border-left: none;" class="text-center"><strong>${v.result}</strong></td>
                      </tr>`;
                      
          }
        }
       html += ` </table>
      `;
      
      $("#marks-data").html(html);
      
    }
</script>