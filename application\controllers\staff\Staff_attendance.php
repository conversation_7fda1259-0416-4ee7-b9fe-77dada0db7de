<?php

class Staff_attendance extends CI_Controller {

    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      $this->load->model('staff/Staff_attendance_model', 'staff_attendance');
    }

    public function index($date='') {
      $data['date'] = $date;
      if($date == '') {
        $data['date'] = date('d-m-Y'); 
      }
      $data['staff_using'] = $this->authorization->getAvatarStakeHolderId();
    	$data['main_content'] = 'staff/attendance/index.php';
      $this->load->view('inc/template', $data);
    }

    public function getStaffAttendance() {
    	$date = date('Y-m-d', strtotime($_POST['date']));
      // $data['att_session_id'] = $this->staff_attendance->addAttendanceSession($date);
    	$data['att_session_id'] = $this->staff_attendance->getStaffAttendanceSession($date);
    	if($data['att_session_id'] != 0) {
    	  $data['staff_att'] = $this->staff_attendance->getStaffAttendance($data['att_session_id'], $date);
    	}
    	echo json_encode($data);
    }

    public function applyLeave() {
      // $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $this->load->model('staff/staffleave_model');
      $insert = $this->staffleave_model->insert_leave_application();
      if ($insert) {
          $this->session->set_flashdata('flashSuccess', 'Successful Inserted.');
      } else {
          $this->session->set_flashdata('flashError', 'Something Wrong.');
      }
      $date = $this->input->post('request_date');
      redirect('staff/staff_attendance/index/'.$date);
    }

    public function changeLeaveStatus() {
      $leave_id = $_POST['leave_id'];
      $status = $_POST['status'];
      $approved_by = $_POST['approved_by'];
      echo $this->staff_attendance->change_leave_status($leave_id, $status, $approved_by);
    }

    public function editAttendance() {
      $status = $this->staff_attendance->updateAttendance();
      echo $status;
    }

    public function getAttendanceHistory() {
      $staffId = $_POST['staffId'];
      $att_session_id = $_POST['att_session_id'];
      $history = $this->staff_attendance->staffAttendanceHistory($staffId, $att_session_id);
      foreach ($history as $key => $value) {
        $value->status = $value->comment;
        if($value->comment == 'AB') {
          $value->status = 'Absent';
        } else if ($value->comment == 'HD') {
          $value->status = 'Half Day';
        } else if ($value->comment == 'FD') {
          $value->status = 'Full Day';
        } else if ($value->comment == 'LC') {
          $value->status = 'Late Comer';
        }
      }
      echo json_encode($history);
      // echo "<pre>"; print_r($history); die();
    }

    public function loadCsvData() {
      $this->load->library('csvimport'); 
      $csv = $_FILES['csv_file'];
      $csv_data = $this->csvimport->get_array($csv['tmp_name']);
      $status = 0;
      if(!empty($csv_data)) {
        $date = date('Y-m-d', strtotime($csv_data[0]['punch_time']));
        $att_session_id = $this->staff_attendance->addAttendanceSession($date);
        $status = $this->staff_attendance->biometricUpdate($att_session_id, $csv_data);
      }
      if($status == -1) {
        $this->session->set_flashdata('flashWarning', 'Data is up-to date.');
      } 
      else if($status) {
        $this->session->set_flashdata('flashSuccess', 'Succefully added attendance.');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('staff/staff_attendance');
      // echo "<pre>"; print_r($data); die();
    }

    public function addCsvData() {
      $this->load->library('csvimport'); 
      if(!isset($_FILES['csv_file'])) {
        redirect('staff/staff_attendance/import_attendance');
      }
      $csv = $_FILES['csv_file'];
      $csv_data = $this->csvimport->get_array($csv['tmp_name']);
      $status = 0;
      usort($csv_data, function($a, $b){
        $t1 = strtotime($a['punch_time']);
        $t2 = strtotime($b['punch_time']);
        return $t1 - $t2;
      });
      // echo "<pre>"; print_r($csv_data); die();    
      if(!empty($csv_data)) {
        $status = $this->staff_attendance->attendance_logs($csv_data);
      }
      $data['summary'] = array();
      if($status == -1) {
        $data['summary'] = array('status' => -1);
      } 
      else if($status) {
        $data['summary'] = $this->_updateAttendanceFromLogs();
      } else {
        $data['summary'] = array('status' => -1);
      }

      $this->import_attendance($data['summary']);
    }

    public function addCsvDataToLogs() {
      /*$att_arr = [
        "DeviceLogId" => "12251",
        "DownloadDate" => "2019-09-30 03:20:35",
        "DeviceId" => "2",
        "UserId" => "16",
        "LogDate" => "2019-09-30 08:50:25",
        "Direction" => "in",
        "C1" => "in",
        "C4" => "0",
        "C5" => "1",
        "C6" => "0",
        "C7" => "0",
        "WorkCode" => "0",
        "log_sent_to_se" => "0",
        "SchoolURL" => "https:\/\/jnanamudra.schoolelement.in"
      ];*/
      $this->load->library('csvimport'); 
      if(!isset($_FILES['csv_file'])) {
        redirect('staff/staff_attendance/import_attendance');
      }
      $csv = $_FILES['csv_file'];
      $csv_data = $this->csvimport->get_array($csv['tmp_name']);
      $status = 0;
      usort($csv_data, function($a, $b){
        $t1 = strtotime($a['punch_time']);
        $t2 = strtotime($b['punch_time']);
        return $t1 - $t2;
      });
      // echo "<pre>"; print_r($csv_data); die();    
      foreach ($csv_data as $key => $csv_row) {
        /*$data = array(
          "DeviceId" => $csv_row['DeviceId'],
          "UserId" => $csv_row['UserId'],
          "LogDate" => $csv_row['LogDate'],
          "C5" => $csv_row['C5'],
          "C4" => $csv_row['C4'],
          "Direction" => $csv_row['Direction']
        );*/
        $this->_updateAttendanceRawDataFromCSV($csv_row);
      }

      $this->import_attendance();
    }

    private function _updateAttendanceRawDataFromCSV($input) {
        // trigger_error('In UpdateAttendance');
        // trigger_error(json_encode($_POST));
        $input = (array)$_POST;
        /*$direction = 0;
        if($input['Direction'] == 'out') {
            $direction = 1;
        }
        $data = array(
            'device_id' => $input['DeviceId'],
            'staff_code' => $input['UserId'],
            'punch_time' => $input['LogDate'],
            'access_type' => $input['C5'],
            'direction' => $direction,
            'timezone_code' => $input['C4']
        );*/

        $log_id = $this->staff_attendance->update_attendance_log($input);
        if($log_id>0) {
            $this->_updateAttendanceFromCSVLogs($input, $log_id);
            return 1;
        } else if($log_id == -1) {
            return 1;
        } else {
            trigger_error("Failed to add attendance logs");
            return 0;
        }
    }

    private function _updateAttendanceFromCSVLogs($log_data, $log_id) {
      $date = date('Y-m-d', strtotime($log_data['punch_time']));
        $att_session_id = $this->staff_attendance->addAttendanceSession($date);
        $status = $this->staff_attendance->updateAttendanceFromLog($log_data, $att_session_id, $log_id);
      return $status;
    }

    private function _updateAttendanceFromLogs() {
      $att_data = $this->staff_attendance->getAttendanceLogs();
      $summary = array();
      $dateWise = array();
      foreach ($att_data as $key => $att) {
        $day = date('Y-m-d', strtotime($att->punch_time));
        if(!array_key_exists($day, $dateWise)) {
          $dateWise[$day] = array();
        }
        array_push($dateWise[$day], $att);
        if(!array_key_exists($att->device_id, $summary)) {
          $summary[$att->device_id] = array();
          $summary[$att->device_id]['count'] = 0;
          $summary[$att->device_id]['from'] = $att->punch_time;
          $summary[$att->device_id]['to'] = '';
        }
        $summary[$att->device_id]['count']++;
        $summary[$att->device_id]['to'] = $att->punch_time;
      }
      foreach ($dateWise as $date => $att) {
        $att_session_id = $this->staff_attendance->addAttendanceSession($date);
        $status = $this->staff_attendance->addAttendanceData($att, $att_session_id);
      }
      return array('status' => $status, 'summary' => $summary);
    }

    public function report() {
      // echo "<pre>"; print_r($_POST); die();
      // $data['from'] = date('Y-m-d', strtotime('-30 days'));
      $data['from'] = date('Y-m-d');
      $data['to'] = date('Y-m-d');
      $data['staff_ids'] = [];
      if(isset($_POST['from']) && isset($_POST['to'])) {
        $data['from'] = date('Y-m-d', strtotime($_POST['from']));
        $data['to'] = date('Y-m-d', strtotime($_POST['to']));
        $data['staff_ids'] = $_POST['staff_id'];
      }
      $data['staffList'] = $this->staff_attendance->getActiveStaffs();
      $data['reportData'] = $this->staff_attendance->getAttendanceReport($data['from'], $data['to'], $data['staff_ids']);
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/attendance/report_tablet.php';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/attendance/report_mobile.php';
      }else{
        $data['main_content'] = 'staff/attendance/report.php';     	
      }
      $this->load->view('inc/template', $data);
    }

    public function updateAttendanceData() {
      $att_data = $this->staff_attendance->getAttendanceLogs();
      $status = -1;
      if(!empty($att_data)) {
        $date = date('Y-m-d', strtotime($att_data[0]->punch_time));
        $att_session_id = $this->staff_attendance->addAttendanceSession($date);
        $status = $this->staff_attendance->addAttendanceData($att_data, $att_session_id);
      }
      if($status == -1) {
        $this->session->set_flashdata('flashWarning', 'Data is up-to date.');
      } else if($status) {
        $this->session->set_flashdata('flashSuccess', 'Succefully added csv data.');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('staff/staff_attendance');
    }

    public function import_attendance($summary=[]) {
      $data['summary'] = $summary;
      if(empty($summary)) {
        $data['summary'] = $this->staff_attendance->getSummary();
      }      
      $data['main_content'] = 'staff/attendance/import';
      $this->load->view('inc/template', $data);
    }

    public function getAttendanceReport() {
      $from = date('Y-m-d', strtotime($_POST['from']));
      $to = date('Y-m-d', strtotime($_POST['to']));
      $report = $this->staff_attendance->getAttendanceReport($from, $to);
      echo json_encode($report);
    }

    public function assignStaff() {
      $staffCode = $_POST['staff_code'];
      $staffId = $_POST['staff_id'];
      $status = $this->staff_attendance->assignStaffCode($staffCode, $staffId);
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Succefully assigned.');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('staff/staff_attendance/missingAttendance');
    }

    public function shifts() {
      $data['shifts'] = $this->staff_attendance->getShifts();
      $data['main_content'] = 'staff/attendance/shifts';
      $this->load->view('inc/template', $data);
    }

    public function saveShift() {
      $status = $this->staff_attendance->saveShift();
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Succefully added shift.');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('staff/staff_attendance/shifts');
    }

    public function assignShifts() {
      $data['shifts'] = $this->staff_attendance->getShiftsNames();
      $data['staff_shifts'] = $this->staff_attendance->getStaffShifts();
      $data['main_content'] = 'staff/attendance/assign_shifts';
      $this->load->view('inc/template', $data);
    }

    public function saveAssignedShifts() {
      $status = $this->staff_attendance->saveAssignedShifts();
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Succefully assigned.');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('staff/staff_attendance/shifts');
    }

    public function late_comer() {
      $data['from'] = date('Y-m-d', strtotime('-30 days'));
      $data['to'] = date('Y-m-d');
      if(isset($_POST['from']) && isset($_POST['to'])) {
        $data['from'] = date('Y-m-d', strtotime($_POST['from']));
        $data['to'] = date('Y-m-d', strtotime($_POST['to']));
      }
      $data['reportData'] = $this->staff_attendance->getLatecomerReport($data['from'], $data['to']);
      $data['main_content'] = 'staff/attendance/late_comer.php';
      $this->load->view('inc/template', $data);
    }

    public function manualAttendance($date='') {
      $data['date'] = $date;
      if($date == '') {
        $data['date'] = date('d-m-Y'); 
      }
      $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
      $data['main_content'] = 'staff/attendance/manual_index.php';
      $this->load->view('inc/template', $data);
    }

    public function getStaffAttendanceList() {

      $date = date('Y-m-d', strtotime($_POST['date']));
      $data['att_session_id'] = $this->staff_attendance->getStaffAttendanceSession($date);
      if($data['att_session_id'] != 0) {
        $data['staff_att'] = $this->staff_attendance->getStaffAttendance($data['att_session_id'], $date);
      } else {
        $data['staff_att'] = $this->staff_attendance->getStaffList($date);
      }
      echo json_encode($data);
    }

    public function saveAttendance() {
      $status = $this->staff_attendance->saveAttendance();
      echo $status;
    }

    public function apply_leave() {
      $this->load->model('staff/staffleave_model');
      $insert = $this->staffleave_model->insert_leave_application();
      if ($insert) {
          $this->session->set_flashdata('flashSuccess', 'Applied leave successfully.');
      } else {
          $this->session->set_flashdata('flashError', 'Something Wrong.');
      }
      $date = $this->input->post('request_date');
      redirect('staff/staff_attendance/manualAttendance/'.$date);
    }

}