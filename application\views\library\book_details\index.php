
<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('library_controller') ?>">Library Dashboard</a></li>
  <li class="active">Books</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('library_controller'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Books
          </h3>

          <?php if ($this->authorization->isAuthorized('LIBRARY.VIEW_EDIT_DELETE')) { ?>
            <ul class="panel-controls">
              <li><a class="new_circleShape_res" style="background-color: #fe970a;"  href="<?php echo site_url('library_controller/add_book_details'); ?>" class="control-primary"><span class="fa fa-plus" style="font-size: 19px;"></span></a></li>
            </ul>
          <?php } ?>

        </div>
      </div>
    </div>
    <div class="card-body pt-1">
      <div class="row" style="margin-left: 0px;margin-right: 0px;margin-bottom: 16px">

        <div class="col-md-2">
          <p>Search By Filter</p>
          <select class="form-control input-md select" name="search_by_filter" id="searchByFilter">
           <option value="title">Book Title</option>
           <!-- <option value="new_arrivals">New Arrivals</option> -->
           <option value="author">Author</option>
           <option value="publisher_name">Publisher Name</option>
           <option value="access_number">Access Number</option>
           <option value="isbn">ISBN</option>
          </select>
        </div>



        <div class="col-md-4" id="search_by_title">
          <div class="form-group" style="border-radius:5px;">
            <p style="margin-left: 15px;">Search by Title</p>
            <div class="row" style="margin: 0px;">
              <div class="col-md-8 ">
                <input id="books_title" autocomplete="off" placeholder="Search by Title" class="form-control input-md" name="books_title">
              </div>
              <div class="col-md-2">
                <input type="button" value="Search" id="books" class="input-md btn  btn-primary">
              </div> 
            </div> 
          </div>
        </div>
        <div class="col-md-4" id="search_by_author" style="display: none;">
          <div class="form-group  text-center" style="border-radius:5px;">
              <p  style="margin-left: 15px;text-align: left;">Search by author</p>
              <div class="row" style="margin: 0px;">
                <div class="col-md-8">
                    <input id="book_author" autocomplete="off" placeholder="Search by author" class="form-control input-md" name="book_author">
                </div>
                <div class="col-md-2">
                  <input type="button" value="Search" id="author" class="input-md btn  btn-primary">
                </div>
              </div>
            </div>
        </div>
        <div class="col-md-4" id="search_by_publisher" style="display: none;">
          <div class="form-group  text-center" style="border-radius:5px;">
            <div class="form-horizontal">
              <p style="margin-left: 15px;text-align: left;">Search by publisher name</p>
              <div class="row" style="margin: 0px;">
                <div class="col-md-8">
                  <input id="publisher_name" autocomplete="off" placeholder="Search by publisher name" class="form-control input-md" name="publisher_name">
                </div> 
                <div class="col-md-2">
                    <input type="button" value="Search" id="pName" class="input-md btn  btn-primary">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-4" id="search_by_access_number" style="display: none;"> 
          <div class="form-group  text-center" style="border-radius:5px;">
            <div class="form-horizontal">
              <p style="margin-left:15px;text-align: left;">Search by access number</p>
              <div class="row" style="margin: 0px;">
                <div class="col-md-8">
                  <input id="access_number" autocomplete="off" placeholder="Search by access number" class="form-control input-md" name="access_number">
                </div>
                <div class="col-md-2">
                    <input type="button" value="Search" id="accessget" class="input-md btn  btn-primary">
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-4" id="search_by_isbn" style="display: none;">
          <div class="form-group" style="border-radius:5px;">
            <p style="margin-left: 15px;">Search by ISBN</p>
            <div class="row" style="margin: 0px;">
              <div class="col-md-8 ">
                <input id="isbn_number" autocomplete="off" placeholder="Search by ISBN Number" class="form-control input-md" name="isbn_number">
              </div>
              <div class="col-md-2">
                <input type="button" value="Search" id="isbn" class="input-md btn  btn-primary">
              </div> 
            </div> 
          </div>
        </div>

    </div>
  </div> 


    <div class="card-body pt-1">
      <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>
      <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
          <div class="row" style="margin: 0px;">
            <div class="d-flex justify-content-between" style="width:100%;">
              <h3 class="card-title panel_title_new_style_staff"> 
                <strong>Books List </strong>
              </h3>   
            </div>
          </div>
        </div>
        <div>
        <div class="panel-body" style="display: none; text-align: center;" id="books_filter">
                                        <i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i>
                                    </div>
     
        <div class="panel-body" id="book_data">
          <h3 class="panel-title">Search books to get list.</h3>
        </div>
        </div>
      </div>
    </div>

  </div>
</div>

<div class="visible-xs visible-sm">
  <a href="<?php echo site_url('library_controller');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<style>
.new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}

#tags{
  position:relative;
  padding: 10px;
}
.autocomplete-items {
  position: absolute;
  overflow-y:auto;
  border-bottom: none;
  border-top: none;
  height:300px;
  margin:0px 15px;
  z-index: 99;
  /*position the autocomplete items to be the same width as the container:*/
  top: 100%;
  left: 0;
  right: 0;
}
.autocomplete-items div {
  padding: 10px;
  cursor: pointer;
  background-color: #fff; 
  border-bottom: 1px solid #d4d4d4; 
}
.autocomplete-items div:hover {
  /*when hovering an item:*/
  background-color: #e9e9e9; 
}
.autocomplete-active {
  /*when navigating through the items using the arrow keys:*/
  background-color: DodgerBlue !important; 
  color: #ffffff; 
}
  ul.panel-controls>li>a {
    border-radius: 50%;
}

</style>

<script>
     $(document).ready(function () {
        var books_title = _get_cookie('books_title');
        var author = _get_cookie('author');
        var pName = _get_cookie('pName');
        var access_number = _get_cookie('access_number');
        var isbn_number = _get_cookie('isbn_number');
       
     });

  

$('#books_title').keypress(function(e){
  if(e.which == 13){//Enter key pressed
    $('#books').click();//Trigger search button click event
  }
});

$('#access_number').keypress(function(e){
  if(e.which == 13){//Enter key pressed
    $('#accessget').click();//Trigger search button click event
  }
});

$('#book_author').keypress(function(e){
  if(e.which == 13){//Enter key pressed
    $('#author').click();//Trigger search button click event
  }
});

$('#publisher_name').keypress(function(e){
  if(e.which == 13){//Enter key pressed
    $('#pName').click();//Trigger search button click event
  }
});
$('#new_arrivals').keypress(function(e){
  if(e.which == 13){//Enter key pressed
    $('#narrivals').click();//Trigger search button click event
  }
});

$('#accessget').click(function(){
  var accessId= $('#access_number').val();
  $('#accessget').val('Please Wait...').prop('disabled', true);
  //_set_cookie('access_number', accessId);

  $.ajax({
    url: '<?php echo site_url('library_controller/serachby_book_accessNumber'); ?>',
    type: 'post',
    data: {'accessId':accessId},
    success: function(data) {
      if (data!=0) {
        $("#book_data").html(data);
        $('#accessget').val('Search').prop('disabled', false);
      }else{
        $('#book_data').html('<div class="no-data-display">Result not found</div>')
        $(function(){
          new PNotify({
            title: 'Info',
            text:  'Invaild code enter library books access number',
            type: 'info',
          });
        });
        $('#accessget').val('Search').prop('disabled', false);
      }
    }
  });
});

// $(document).ready(function(){
//     get_default_newly_add_books();
// });

// function get_default_newly_add_books() {
//   $('#books_filter').show();
//   $("#book_data").html('');
//   var books_title= $('#books_title').val();
//   $('#books_title').val('');
//   $.ajax({
//     url: '<?php //echo site_url('library_controller/display_newly_add_books'); ?>',
//     type: 'post',
//     data: {'books_title':books_title},
//     success: function(data) {
//       if (data!=0) {
//         $("#book_data").html(data);
//       }else{
//         $('#book_data').html('<div class="no-data-display">Result not found</div>')
//         $(function(){
//           new PNotify({
//             title: 'Info',
//             text:  'Book not found',
//             type: 'info',
//           });
//         });
//       }
//       $('#books_filter').hide();
//     }
//   });
// }

$('#isbn').click(function(){
  $('#isbn').val('Please Wait...').prop('disabled', true);
  $('#books_filter').show();
  $("#book_data").html('');
  var isbn_number= $('#isbn_number').val();
  // _set_cookie('isbn_number', isbn_number);
  $('#isbn_number').val('');
  $.ajax({
    url: '<?php echo site_url('library_controller/serachby_isbn'); ?>',
    type: 'post',
    data: {'isbn_number':isbn_number},
    success: function(data) {
      if (data!=0) {
        $("#book_data").html(data);
        $('#isbn').val('Search').prop('disabled', false);
      }else{
        $('#book_data').html('<div class="no-data-display">Result not found</div>')
        $(function(){
          new PNotify({
            title: 'Info',
            text:  'Book not found',
            type: 'info',
          });
        });
        $('#isbn').val('Search').prop('disabled', false);
      }
      $('#books_filter').hide();
    }
  });
});

$('#books').click(function(){
  $('#books').val('Please Wait...').prop('disabled', true);
  $('#books_filter').show();
  $("#book_data").html('');
  var books_title= $('#books_title').val();
  // _set_cookie('books_title', books_title);
  $('#books_title').val('');
  $.ajax({
    url: '<?php echo site_url('library_controller/serachby_book'); ?>',
    type: 'post',
    data: {'books_title':books_title},
    success: function(data) {
      if (data!=0) {
        $("#book_data").html(data);
        $('#books').val('Search').prop('disabled', false);
        //delete_cookie('books_title');
      }else{
        $('#book_data').html('<div class="no-data-display">Result not found</div>')
        $(function(){
          new PNotify({
            title: 'Info',
            text:  'Book not found',
            type: 'info',
          });
        });
        $('#books').val('Search').prop('disabled', false);
      }
      $('#books_filter').hide();
    }
  });
});

$('#author').click(function(){
  $('#author').val('Please Wait...').prop('disabled', true);
  $('#books_filter').show();
  $("#book_data").html('');
  var book_author= $('#book_author').val();
  // _set_cookie('author', book_author);
  $('#book_author').val('');
  $.ajax({
    url: '<?php echo site_url('library_controller/serachby_author'); ?>',
    type: 'post',
    data: {'book_author':book_author},
    success: function(data) {
      if (data!=0) {
        $("#book_data").html(data);
        $('#author').val('Search').prop('disabled', false);
      }else{
        $('#book_data').html('<div class="no-data-display">Result not found</div>')
        $(function(){
          new PNotify({
            title: 'Info',
            text:  'Author Not found',
            type: 'info',
          });
        });
        $('#author').val('Search').prop('disabled', false);
      }
      $('#books_filter').hide();
    }
  });
});

$('#pName').click(function(){
  $('#pName').val('Please Wait...').prop('disabled', true);
    $('#books_filter').show();
    $("#book_data").html('');
  var publisher_name= $('#publisher_name').val();
  // _set_cookie('pName', publisher_name);
  $('#publisher_name').val('');
  $.ajax({
    url: '<?php echo site_url('library_controller/serachby_p_name'); ?>',
    type: 'post',
    data: {'publisher_name':publisher_name},
    success: function(data) {
      if (data!=0) {
        $("#book_data").html(data);
        $('#pName').val('Search').prop('disabled', false);
      }else{
        $('#book_data').html('<div class="no-data-display">Result not found</div>')
        $(function(){
          new PNotify({
            title: 'Info',
            text:  'Publisher name Not found',
            type: 'info',
          });
        });
        $('#pName').val('Search').prop('disabled', false);
      }
      $('#books_filter').hide();
    }
  });
});


// href='.site_url('library_controller/delete_booksDetails/'.$book->id).' 
function delete_books_confirm_box(bookId) {
     bootbox.confirm({
      title:'Confirm',
      message: "Are you sure you want to delete this book?",
      buttons: {
          confirm: {
              label: 'Yes',
              className: 'btn-success'
          },
          cancel: {
              label: 'No',
              className: 'btn-danger'
          }
      },
      callback: function (result) {
        if(result) {        
          $.ajax({
            url: '<?php echo site_url('library_controller/delete_booksDetails'); ?>',
            type: 'post',
            data: {'bookId' : bookId}
          });
          if (result == true) {
           location.reload();
            new PNotify({
              title: 'Success',
              text: 'Successfully updated',
              type: 'success',
            });
          } else {
           new PNotify({
              title: 'Error',
              text: 'Something went wrong',
              type: 'Error',
            });
          }
        }
      }
    });
}

function get_books_map_copies(bookId) { 
   $.ajax({
      url: '<?php echo site_url('library_controller/get_copies_of_books'); ?>',
      type: 'post',
      data: {'bookId' : bookId},
      success: function(data) {
          var bc = JSON.parse(data);
          var html = '';
          var html_head = '<label>Book name : ' + bc.book_title + '</label>';
          html += '<tr>';
          html += '<th>#</th>';
          html += '<th>Access Number</th>';
          html += '<th>Status</th>';
          html += '<th>Date of Accession</th>';
          html += '<th>Cost of Book</th>';
          html += '</tr>';
          var m = 1; 
          for(var i = 0; i < bc.copies.length; i++) {
              var access = bc.copies[i].access_code ? bc.copies[i].access_code : '';
              var dateof_accession = bc.copies[i].date_of_accession != '00-00-0000' ? bc.copies[i].date_of_accession : 'N/A';
              html += '<tr>';
              html += '<td>' + m + '</td>';
              html += '<td><input type="text" placeholder="Enter Access Number" autofocus="true" data-id="' + bc.copies[i].id + '" id="' + bc.copies[i].id + '"  value="' + access + '" onkeyup="check_access_number_exit_in_db(this.value,'+bc.copies[i].id+')" class="form-control access"><span id="error_access'+bc.copies[i].id+'" style="color:red; display:none">Access number already exit or format is invalid.</span></td>';
              html += '<td>' + bc.copies[i].status + '</td>';
              html += '<td>' + dateof_accession + '</td>';
              html += '<td>' + bc.copies[i].costof_book + '</td>';
              html += '</tr>';
              m++;
          }
          $('#lb-content').html(html);
          $('.modal-title').html(html_head);
      }
   });
}

function check_access_number_exit_in_db(value,id) {
    $.ajax({
      url :'<?php echo site_url('library_controller/check_access_number_exit_in_db_value') ?>',
      type:"post",
      data:{'value':value},
      success:function(data){
        var resDat = data.trim();
        if (resDat == 1) {
          $('#error_access'+id).show();
        }else{
          $('#error_access'+id).hide();
        }
      }
    });
  }


function get_transcation_report(bookId) {
  $.ajax({
    url: '<?php echo site_url('library_controller/get_transcation_report') ?>',
    type: 'post',
    data: {'bookId':bookId},
    success: function(data) {
       var libData = JSON.parse(data);
       
      $('.library_daily_tx').html(construct_library_daily_tx_table(libData));
    }
  });
}

function construct_library_daily_tx_table(libData) {
  var html ='';
  if(libData.length != 0){
    html +=`<table class="table table-bordered">
        <thead>
          <tr>
            <th>#</th>
            <th>Date</th>
            <th>Borrowed by</th>
            <th>LBR Id</th>
            <th>Book title</th>
            <th>Access Id</th>
            <th>Author</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>`;
        for(var i=0; i < libData.length; i++){
          var dateParts = libData[i].created_date.split("-"); 
          var date = new Date(dateParts[2], dateParts[1] - 1, dateParts[0]); 
          var formattedDate = date.toLocaleDateString('en-GB', {
              day: '2-digit',
              month: 'short', 
              year: 'numeric'
          }).replace(' ', '-');
          var dateParts1 = libData[i].return_date.split("-"); 
          var date1 = new Date(dateParts1[2], dateParts1[1] - 1, dateParts1[0]); 
          var formattedDate1 = date.toLocaleDateString('en-GB', {
              day: '2-digit',
              month: 'short', 
              year: 'numeric'
          }).replace(' ', '-');
         
          html +='<tr>';
          html +='<td>'+(i+1)+'</td>';
          html +='<td>'+formattedDate+'</td>';
          html +='<td>'+(libData[i].name || libData[i].stake_holder_type)+'</td>';
          html +='<td>'+libData[i].card_access_code+'</td>';
          html +='<td>'+libData[i].book_title+'</td>';
          html +='<td>'+libData[i].book_id+'</td>';
          html +='<td>'+libData[i].author+'</td>';
          html +='<td>'+libData[i].status+'<br>'+formattedDate1+'</td>';
          html +='</tr>';
        }
        html +='</tbody>';
        html +='</table>';

  }else{
    html+='<div class="no-data-display">No Transactions Yet</div>';
  }
  
      return html;

}

</script>
<script type="text/javascript">
$(document).ready(function(){
    $("#summary").on('shown.bs.modal', function(){
        $(this).find('input[type="text"]').focus();
    });
});
</script>

<div id="summary" class="modal fade" role="dialog">
    <div class="modal-dialog" style="width: 50%;margin: auto;">

    <!-- Modal content-->
    <div class="modal-content">
        <div class="modal-header">
        <h4 class="modal-title">Map and confirm</h4>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:auto;">
            <div id="modal-loader" style="display: none; text-align: center;">
                <!-- ajax loader -->
                <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>" style="width:400px; height:400px;">
           </div>
            <table class="table" id="lb-content" width="100%">
                
            </table>
        </div>
        <div class="modal-footer">
          <button type="button" id="cancelModal" class="btn btn-danger" data-dismiss="modal">Cancel</button>
        <button type="button" id="confirmBtn" class="btn btn-primary">Confirm</button>
        </div>
    </div>
    </div>
</div>

<div id="view_transction" class="modal fade" role="dialog">
    <div class="modal-dialog" style="width: 50%;margin: auto;">

    <!-- Modal content-->
    <div class="modal-content">
        <div class="modal-header">
        <h4>View Transactions</h4>
        </div>
        <div  class="modal-body table-responsive library_daily_tx" id="library_daily_tx" style="overflow-y:auto;height:auto;">
            
        </div>
        <div class="modal-footer">
        <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal">Close</button>
        </div>
    </div>
    </div>
</div>

<script type="text/javascript">
  //  $('#customers2').DataTable({
  //       // Optional configuration
  //       paging: true,        // Enable pagination
  //       searching: true,     // Enable search box
  //       ordering: true,      // Enable sorting
  //       info: true,          // Show table info
  //       pageLength: 5,       // Default number of rows per page
  //       lengthMenu: [5, 10, 25, 50, 100], // Rows per page options
  //   });

  $('#confirmBtn').click(function(){
    let array = [];
    $('.access').each(function () {
        array.push($(this).data('id')); 
    });

    let access_code = {};
    let values = [];
    let hasDuplicate = false;

    
    array.forEach(id => {
        let val = $("#" + id).val(); 
        if (values.includes(val)) {
            hasDuplicate = true; 
        }
        values.push(val); 
        access_code[id] = val; 
    });

    if (hasDuplicate) {
        
        Swal.fire({
            icon: 'error',
            title: 'Duplicate Values Detected',
            text: 'Each access number must be unique. Please check and try again.',
        });
        return false; 
    }

    $.ajax({
      url: "<?php echo site_url('library_controller/update_accees_number_maping');?>",
      data: {'access_code':access_code},
      type: 'post',
      success: function(data) {
        console.log(data);
        if (data) {
          $("#summary").modal('hide');
          new PNotify({
            title: 'Success',
            text: 'Successfully updated',
            type: 'success',
          });
        }
      },
      error: function(err) {
          console.log(err);
      }
    });

  // var access_code = $("input[name='access_code[]']").val();
});
</script>

<script type="text/javascript">
  $('#searchByFilter').on('change',function(){
    switch(this.value){
      case 'title':
        $('#search_by_title').show();
        $('#search_by_author').hide();
        $('#search_by_publisher').hide();
        $('#search_by_isbn').hide();
        $('#search_by_access_number').hide();
        $('#book_data').html('<h3>Search by Book title  get data</h3>');
      break;
      case 'author':
        $('#search_by_title').hide();
        $('#search_by_author').show();
        $('#search_by_publisher').hide();
        $('#search_by_isbn').hide();
        $('#search_by_access_number').hide();
         $('#book_data').html('<h3>Search by Book author get data</h3>');
      break;
      case 'publisher_name':
        $('#search_by_title').hide();
        $('#search_by_author').hide();
        $('#search_by_publisher').show();
        $('#search_by_isbn').hide();
        $('#search_by_access_number').hide();
         $('#book_data').html('<h3>Search by publisher name get data</h3>');
      break;
      case 'access_number':
        $('#search_by_title').hide();
        $('#search_by_isbn').hide();
        $('#search_by_author').hide();
        $('#search_by_publisher').hide();
        $('#search_by_access_number').show();
         $('#book_data').html('<h3>Search by access number get data</h3>');
      break;
      case 'isbn':
        $('#search_by_title').hide();
        $('#search_by_author').hide();
        $('#search_by_publisher').hide();
        $('#search_by_access_number').hide();
        $('#search_by_isbn').show();

         $('#book_data').html('<h3>Search by publisher name get data</h3>');
      break;
      case 'new_arrivals':
        $('#search_by_title').hide();
        $('#search_by_author').hide();
        $('#search_by_publisher').hide();
        $('#search_by_access_number').hide();
        $('#search_by_isbn').hide();
        get_new_arrivals_books_list();
      break;
    }   
  });
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">

  function get_new_arrivals_books_list() {
    $('#books_filter').show();
    $.ajax({
      url: '<?php echo site_url('library_controller/new_arrivals'); ?>',
      type: 'post',
      success: function(data) {
        console.log(data);
        if (data!=0) {
          $("#book_data").html(data);
        }else{
          $('#book_data').html('<div class="no-data-display">Result not found</div>')
          $(function(){
            new PNotify({
              title: 'Info',
              type: 'info',
            });
          });

        }$('#books_filter').hide();
      }
    });
  }
function bootbox_reserved() {
bootbox.confirm({
                                message: 'This is a confirm with custom button text and color! Do you like it?',
                                buttons: {
                                confirm: {
                                  class: 'bootbox',
                                label: 'Yes',
                                className: 'btn-success'
                                },
                                cancel: {
                                label: 'No',
                                className: 'btn-danger'
                                }
                                },
                                callback: function (result) {
                                console.log('This was logged in the callback: ' + result);
                                }
                                });
}
</script>
<style type="text/css">
.bootbox{
  width: 50%;
  margin: auto;
}
</style>