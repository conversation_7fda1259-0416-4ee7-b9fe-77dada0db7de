<?php
  /**
   * Name:    Oxygen
   * Author:  <PERSON>
   *          <EMAIL>
   *
   * Created:  17 May 2018
   *
   * Description:  
   *
   * Requirements: PHP5 or above
   *
   */

  defined('BASEPATH') OR exit('No direct script access allowed');

  /**
   * Class Fee
   * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
   * @property CI_Form_validation      $form_validation The form validation library
   */
  class Dashboard extends CI_Controller {

    private $year_id;

    public function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isAuthorized('VIRTUAL_CLASSROOM.MODULE')) {
        redirect('dashboard', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
    }


    public function index(){
      $data['is_admin'] = $this->authorization->isAuthorized('VIRTUAL_CLASSROOM.ADMIN');
      $data['main_content']    = 'virtual_classroom/dashboard/index.php';
      $this->load->view('inc/template', $data);  
    }
  }

?>