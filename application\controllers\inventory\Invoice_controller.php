<?php

class Invoice_controller extends CI_Controller {

    function __construct(){
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('INVENTORY')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('inventory/vendor_model');
        $this->load->model('inventory/invoice_model');
    }

    public function index() {
        $data['main_content'] = 'inventory/invoice/index';
        $this->load->view('inc/template', $data);
    }

    public function invoice_list() {
        $data['invoiceData'] = $this->invoice_model->get_invoice_details();
        // echo "<pre>";print_r($data);die();
        $data['main_content'] = 'inventory/invoice/invoice_list';
        $this->load->view('inc/template', $data);
    }

    public function new_invoice(){
        $data['vendorData'] = $this->vendor_model->get_vendor_details();
        // $data['products'] = $this->invoice_model->getVendorProducts();
        // echo "<pre>"; print_r($data['vendorData']); die();
        $data['main_content'] = 'inventory/invoice/add_invoice';
        $this->load->view('inc/template', $data);
    }

    public function getVendorData(){
        $vendorId = $_POST['vendorId'];
        $payments = $this->vendor_model->getPaymentInsData($vendorId);
        $products = $this->invoice_model->getVendorProducts($vendorId);
        echo json_encode(array('payments' => $payments, 'products' => $products));
    }

    public function getVariants(){
        $products = $_POST['products'];
        $variants = $this->vendor_model->getVariants();
        echo json_encode($variants);
    }

    public function submitInvoice(){
        $input = $this->input->post();
        // echo "<pre>"; print_r($input); die();
        $status = $this->invoice_model->addInvoice();
        if($status)
            $this->session->set_flashdata('flashSuccess', 'Added Successfully.');
        else
            $this->session->set_flashdata('flashError', 'Something went wrong!');
        redirect('inventory/invoice_controller');
    }

    public function readInvoice($id) {
        $data['invoiceData'] = $this->invoice_model->getInvoiceById($id);
        $data['items'] = $this->invoice_model->getItemsId($id);
        $data['main_content'] = 'inventory/invoice/read_invoice';
        $this->load->view('inc/template', $data);
    }

    public function invoice_report() {
        $invoices = $this->invoice_model->getInvoiceData();
        $data['invoices'] = array();
        foreach ($invoices as $key => $invoice) {
            $id = $invoice->invoice_id;
            if(!array_key_exists($id, $data['invoices'])) {
                $data['invoices'][$id] = array();
                $data['invoices'][$id]['invoice_no'] = $invoice->invoice_no;
                $data['invoices'][$id]['invoiceTotal'] = $invoice->invoiceTotal;
                $data['invoices'][$id]['vendor_name'] = $invoice->vendor_name;
                $data['invoices'][$id]['invoiceDate'] = $invoice->invoiceDate;
                $data['invoices'][$id]['items'] = array();
                $data['invoices'][$id]['total'] = 0;
                $data['invoices'][$id]['created_on'] = date('d-m-Y',strtotime($invoice->created_on));
                $data['invoices'][$id]['created_by'] = $invoice->friendly_name;
            }
            $data['invoices'][$id]['items'][] = array('name' => $invoice->variantName, 'quantity' => $invoice->quantity, 'price' => $invoice->itemTotal);
            $data['invoices'][$id]['total'] += $invoice->itemTotal;
        }
        // echo "<pre>"; print_r($data); die();
        $data['main_content'] = 'inventory/invoice/invoice_report';
        $this->load->view('inc/template', $data);
    }

    public function delete_invoice(){
        $result = $this->invoice_model->delete_invoice();
        if($result)
            $this->session->set_flashdata('flashSuccess', 'Deleted Successfully.');
        else
            $this->session->set_flashdata('flashError', 'Something went wrong!');
        echo $result;
    }
}