<?php

class Vendor_controller extends CI_Controller {

    function __construct(){
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('VENDOR') || !$this->authorization->isAuthorized('VENDOR.MODULE')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('inventory/vendor_model');
        $this->config->load('form_elements');
    }

    //landing page display vendor info
    public function index(){
        $data['permitVendorAdd'] = $this->authorization->isAuthorized('VENDOR.CRUD');
        $data['vendorData'] = $this->vendor_model->get_vendor_details(0);

        $site_url = site_url();
        $data['tiles'] = array(
              [
                'title' => 'New Vendor',
                'sub_title' => 'Add new vendor',
                'icon' => 'svg_icons/freshentry.svg',
                'url' => $site_url.'inventory/vendor_controller/new_vendor',
                'permission' => $this->authorization->isAuthorized('VENDOR.CRUD')
              ],
              [
                'title' => 'View Vendors',
                'sub_title' => 'View vendors list',
                'icon' => 'svg_icons/view.svg',
                'url' => $site_url.'inventory/vendor_controller/view_vendors',
                'permission' => 1
              ]          
        );
        $data['tiles'] = checkTilePermissions($data['tiles']);

        $data['main_content'] = 'inventory/vendors/index';
        $this->load->view('inc/template', $data);
    }

    public function new_vendor() {
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $data['main_content'] = 'inventory/vendors/new_vendor';
        $this->load->view('inc/template', $data);
    }

    public function view_vendors() {
        $data['vendors'] = $this->vendor_model->getVendors();
        $data['main_content'] = 'inventory/vendors/view_vendors';
        $this->load->view('inc/template', $data);
    }

    //add new vendor form page
    public function addVendorInfo(){
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $data['date'] = date("Y-m-d");
        $data['main_content'] = 'vendor_master/addVendor';
        $this->load->view('inc/template', $data);
    }

    //add vendor into database
    public function submitVendor(){
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $status = (int) $this->vendor_model->addNewVendor();

        if($status != 0){
            $this->session->set_flashdata('flashSuccess', 'Vendor Details Added Successfully.');
        } else{
            $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        }

        redirect('inventory/vendor_controller');
    }

    //Edit vendor information
    public function editVendorInfo($vendor_id){
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $data['vendor_id'] = $vendor_id;
        $data['vendorData'] = $this->vendor_model->get_vendor_details($vendor_id);
        $data['main_content'] = 'inventory/vendors/editVendor';
        $this->load->view('inc/template', $data);
    }

    //update the vendor
    public function updateVendor($vendor_id){
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $this->db->trans_begin();
        $status = (int) $this->vendor_model->updateVendorInfo($vendor_id);

        if($status != 0){
            $this->db->trans_commit();
            $this->session->set_flashdata('flashSuccess', 'Vendor Details Updated Successfully.');
        } else{
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        }

        redirect('inventory/vendor_controller/vendor_details/'.$vendor_id);
    }

    public function vendor_details($vendor_id) {
        if (!$this->authorization->isAuthorized('VENDOR.MODULE')) {
            redirect('dashboard', 'refresh');
        }
        $data['permitVendorAdd'] = $this->authorization->isAuthorized('VENDOR.CRUD');
        $data['permitVendorModule'] = $this->authorization->isAuthorized('VENDOR.MODULE');
        $data['vendor_id'] = $vendor_id;
        $vendorAddress = $this->vendor_model->checkVendorAddress($vendor_id);
        $data['flag'] = 0;//address not present
        if(!empty($vendorAddress)) {
            $data['flag'] = 1;
        }
        $data['vendor'] = $this->vendor_model->get_vendor_basic_info($vendor_id);
        $data['main_content'] = 'inventory/vendors/vendor_details';
        $this->load->view('inc/template', $data);
    }

    //vendor address information
    public function vendorInfo($vendor_id){
        if (!$this->authorization->isAuthorized('VENDOR.MODULE')) {
            redirect('dashboard', 'refresh');
        }
        $data['vendor_id'] = $vendor_id;
        $vendorAddress = $this->vendor_model->checkVendorAddress($vendor_id);
        $payData = $this->vendor_model->getPaymentInsData($vendor_id);
        $data['categories'] = $this->vendor_model->getUnassignedCategories($vendor_id);
        // echo "<pre>"; print_r($data); die();
        $data['payData'] = array();
        foreach ($payData as $key => $pay) {
            $pay_info = '';
            $type = ucwords(str_replace('_', ' ', $pay->payment_type));
            if($type == 'Cash') {
                $pay_info = 'Payment by '.ucwords($type);
            } else if($type == 'Cheque') {
                $pay_info = '<b>Cheque in favour of :</b>'.$pay->cheque_in_favor_of;
            } else {
                $pay_info = '<b>Bank Name : </b>'.$pay->bank_name.'<br>';
                $pay_info .= '<b>Branch Name : </b>'.$pay->branch.'<br>';
                $pay_info .= '<b>Account Number : </b>'.$pay->account_number.'<br>';
                $pay_info .= '<b>IFSC Code : </b>'.$pay->ifsc_code.'<br>';
            }
            $name = $pay->name;
            array_push($data['payData'], array('name' => $name, 'type' => $type, 'info' => $pay_info));
        }
        // echo "<pre>"; print_r($data); die();
        $data['office_address'] = 'Not Added';
        $data['warehouse_address'] = 'Not Added';
        $data['flag'] = 0;//address not present
        if(!empty($vendorAddress)) {
            $data['flag'] = 1; //address present
            foreach ($vendorAddress as $key => $address) {
                $addr = '';
                if($address->address_line1 != '') {
                    $addr .= $address->address_line1.'<br>';
                }
                if($address->address_line2 != '') {
                    $addr .= $address->address_line2.'<br>';
                }
                if($address->area != '') {
                    $addr .= $address->area.'<br>';
                }
                if($address->district != '') {
                    $addr .= $address->district.'<br>';
                }
                if($address->state != '') {
                    $addr .= $address->state.'<br>';
                }
                if($address->country != '') {
                    $addr .= $address->country.'<br>';
                }
                if($address->pin_code != '') {
                    $addr .= $address->pin_code;
                }
                
                if($address->address_type == 0) {
                    $data['office_address'] = $addr;
                } else {
                    $data['warehouse_address'] = $addr;
                }
            }
        }
        $data['vendor'] = $this->vendor_model->get_vendor_details($data['vendor_id']);
        $data['main_content'] = 'inventory/vendors/more/vendorInfo';
        $this->load->view('inc/template', $data);
    }

    public function get_categories() {
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $vendor_id = $_POST['vendor_id'];
        $categories = $this->vendor_model->getVendorCategories($vendor_id);
        echo json_encode($categories);
    }

    public function add_vendor_category() {
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $vendor_id = $_POST['vendor_id'];
        $category_id = $_POST['category_id'];
        $status = $this->vendor_model->addVendorCategories($vendor_id, $category_id);
        echo $status;
    }

    //add vendor address
    public function addVendorAddress($vendor_id){
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $data['vendor_id'] = $vendor_id;
        $data['vendor'] = $this->vendor_model->get_vendor_basic_info($vendor_id);
        $data['main_content'] = 'inventory/vendors/more/addVendorAddress';
        $this->load->view('inc/template', $data);
    }

    //Delete vendor info
    public function deleteVendorInfo($vendor_id){
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $this->db->trans_begin();
        $status =(int) $this->vendor_model->deleteVendor($vendor_id);
        if($status != 0){
            $this->db->trans_commit();
            $this->session->set_flashdata('flashSuccess', 'Vendor Details Deleted Successfully.');
        } else{
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        }

        redirect('inventory/vendor_controller');
    }

    //submit vendor details
    public function submitVendorAddress($vendor_id){
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $address = $this->vendor_model->generateAddressData($vendor_id);
        $addressData = array();
        array_push($addressData, $address['office']);
        array_push($addressData, $address['warehouse']);
        $status = $this->vendor_model->submitVendorAddressInfo($addressData);
        if($status) {
            $this->session->set_flashdata('flashSuccess', 'Vendor Address Added Successfully.');
        } else {
            $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        }
        redirect('inventory/vendor_controller/vendor_details/'.$vendor_id);
    }

    //edit vendor address
    public function editVendorAddress($vendor_id){
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $data['vendorAddress'] = $this->vendor_model->checkVendorAddress($vendor_id);
        $data['vendor_id'] = $vendor_id;
        $data['vendor'] = $this->vendor_model->get_vendor_basic_info($vendor_id);
        $data['main_content'] = 'inventory/vendors/more/editVendorAddress';
        $this->load->view('inc/template', $data);
    }

    //update vendor address
    public function updateVendorAddress($vendor_id){
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $address = $this->vendor_model->generateAddressData($vendor_id); 
        $status1 = $this->vendor_model->updateVendorAddressInfo($vendor_id,0,$address['office']);
        $status2 = $this->vendor_model->updateVendorAddressInfo($vendor_id,1,$address['warehouse']);
        if($status1 && $status2) {
            $this->session->set_flashdata('flashSuccess', 'Vendor Address Updated Successfully.');
        } else {
            $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        }

        redirect('inventory/vendor_controller/vendor_details/'.$vendor_id);
    }

    public function payment_instruments($vendor_id){
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $data['vendor_id'] = $vendor_id;
        $data['vendor'] = $this->vendor_model->get_vendor_basic_info($vendor_id);
        $data['payData'] = $this->vendor_model->getPaymentInsData($vendor_id);
        $data['main_content'] = 'inventory/vendors/more/payment_instruments';
        $this->load->view('inc/template', $data);
    }

    public function addPaymentInstrument($vendor_id){
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $data['vendor_id'] = $vendor_id;
        $status = $this->vendor_model->addPaymentInstrument($vendor_id);

        if($status === -1) {
            $this->session->set_flashdata('flashInfo', 'Already exist');
        }
        else if($status){
            $this->session->set_flashdata('flashSuccess', 'Added Successfully.');
        }
        else {
            $this->session->set_flashdata('flashError', 'Something went wrong!');
        }
        redirect('inventory/vendor_controller/payment_instruments/'.$vendor_id);
    }

    public function remove_payment_instruments() {
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $payInsId = $_POST['pay_ins_id'];
        $status = $this->vendor_model->removePaymentInstrument($payInsId);
        echo $status;
    }

    public function add_observation_rating() {
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $observation = $_POST['observation'];
        $rating = $_POST['rating'];
        $vendor_id = $_POST['vendor_id'];
        $status = $this->vendor_model->addObservationRating($observation, $rating, $vendor_id);
        echo $status;
    }

    public function get_observations() {
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $vendor_id = $_POST['vendor_id'];
        $observations = $this->vendor_model->getObservations($vendor_id);
        echo json_encode($observations);
    }

    public function change_vendor_status() {
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $vendor_id = $_POST['vendor_id'];
        $status = $_POST['status'];
        $status = $this->vendor_model->changeVendorStatus($status, $vendor_id);
        echo $status;
    }

    public function vendor_product_categories($vendor_id) {
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $data['vendor_id'] = $vendor_id;
        $data['vendor'] = $this->vendor_model->get_vendor_basic_info($vendor_id);
        $data['categories'] = $this->vendor_model->getUnassignedCategories($vendor_id);
        $data['assignedCategories'] = $this->vendor_model->getVendorCategoriesData($vendor_id);
        // echo "<pre>"; print_r($data); die();
        $data['main_content'] = 'inventory/vendors/more/vendor_categories';
        $this->load->view('inc/template', $data);
    }

    public function vendor_observations_ratings($vendor_id) {
        if (!$this->authorization->isAuthorized('VENDOR.CRUD')) {
            redirect('dashboard', 'refresh');
        }
        $data['vendor_id'] = $vendor_id;
        $data['vendor'] = $this->vendor_model->get_vendor_basic_info($vendor_id);
        $data['main_content'] = 'inventory/vendors/more/vendor_observations';
        $this->load->view('inc/template', $data);
    }
}