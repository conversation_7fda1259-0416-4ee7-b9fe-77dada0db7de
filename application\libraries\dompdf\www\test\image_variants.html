<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
<style>
body {
  color: #7d7a7a;
  font-family: sans-serif;
}
div.bgimages a {
  padding-left:20px;
  background:url(images/pdf.png) no-repeat yellow;
}
div.bgimages ul {
  text-align: left;
  line-height:170%;
}
</style>
</head>

<body>
<div style="text-align: center;">

<h2> 40% of box width:</h2>
<div style="width:360pt; border:1pt solid #7d7a7a">
<img style="width:40%;" src="images/dompdf_simple.png"/>
</div>

<h2>multiple identical images jpg:</h2>
<img src="images/goldengate.jpg" width="204" height="272"/>
<img src="images/goldengate.jpg" width="204" height="272"/>
<img src="images/goldengate.jpg" width="204" height="272"/>

<h2>multiple identical images gif (will be recoded to png by dompdf):</h2>
<img src="images/what_ordered.gif" width="560" height="272"/>
<img src="images/what_ordered.gif" width="560" height="272"/>
<img src="images/what_ordered.gif" width="560" height="272"/>

<h2>multiple identical images png:</h2>
<img src="images/dompdf_simple.png" width="200" height="76"/>
<img src="images/dompdf_simple.png" width="200" height="76"/>
<img src="images/dompdf_simple.png" width="200" height="76"/>

<h2>local png image with alpha channel:</h2>
<img src="images/dokuwiki-128.png" width="128" height="128"/>

<h2 style="color:red;">Attention!</h2>

<p style="color:red;">For external images to work, the following configuration is required:</p>
<pre style="color:red;">dompdf_config.inc.php :</pre>

<pre style="color:red;">define("DOMPDF_ENABLE_REMOTE", true);</pre>

<h2>external png Image with alpha channel:</h2>
<img src="http://www.dokuwiki.org/lib/exe/fetch.php?media=wiki:dokuwiki-128.png" width="128" height="128" />

<h2>external image, dynamically created with id in url parameter at end of parameter(.jpg):</h2>
<img src="http://www.dokuwiki.org/lib/exe/fetch.php?media=example:sunset.jpg" width="500" height="375" />

<h2>external image, dynamically created with id in url parameter not at end of parameter (.jpg):</h2>
<img src="https://chart.googleapis.com/chart?chst=d_bubble_icon_text_small&chld=ski|bb|Wheeee!|FFFFFF|000000" />

<h2>external Image without file extension (.jpg):</h2>
<img width="200" height="300" src="http://placekitten.com/g/200/300" />

<h2><a name="bgimages">Background images</a></h2>
<div class="bgimages">
<ul>
<li><a href="bgimages">no-repeat position:default</a></li>
<li><a href="bgimages" style="background-position:left center;">no-repeat position:left-center</a></li>
<li><a href="bgimages" style="background-position:left bottom;">no-repeat position:left-bottom</a></li>
<li><a href="bgimages" style="background-position:right bottom;">no-repeat position:right-bottom</a></li>
<li><a href="bgimages" style="background-position:center center;">no-repeat position:center-center</a></li>
<li><a href="bgimages" style="background-position:50% 50%;">no-repeat position:50%-50%</a></li>
<li><a href="bgimages" style="background-position:4px 8px;">no-repeat position:4px-8px</a></li>
<li><a href="bgimages" style="background-position:-4px -8px;">no-repeat position:-4px--8px</a></li>
<li><a href="bgimages" style="background-position:32px 0px;">no-repeat position:32px-0px</a></li>
<li><a href="bgimages" style="background-position:0px 8px;">no-repeat position:0px-8px</a></li>
</ul>
<ul>
<li><a href="bgimages" style="background-repeat:repeat-y;">repeat-y position:default</a></li>
<li><a href="bgimages" style="background-repeat:repeat-y; background-position:left center;">repeat-y position:left-center</a></li>
<li><a href="bgimages" style="background-repeat:repeat-y; background-position:left bottom;">repeat-y position:left-bottom</a></li>
<li><a href="bgimages" style="background-repeat:repeat-y; background-position:right bottom;">repeat-y position:right-bottom</a></li>
<li><a href="bgimages" style="background-repeat:repeat-y; background-position:center center;">repeat-y position:center-center</a></li>
<li><a href="bgimages" style="background-repeat:repeat-y; background-position:50% 50%;">repeat-y position:50%-50%</a></li>
<li><a href="bgimages" style="background-repeat:repeat-y; background-position:4px 8px;">repeat-y position:4px-8px</a></li>
<li><a href="bgimages" style="background-repeat:repeat-y; background-position:-4px -8px;">repeat-y position:-4px--8px</a></li>
<li><a href="bgimages" style="background-repeat:repeat-y; background-position:32px 0px;">repeat-y position:32px-0px</a></li>
<li><a href="bgimages" style="background-repeat:repeat-y; background-position:0px 8px;">repeat-y position:0px-8px</a></li>
</ul>
<ul>
<li><a href="bgimages" style="background-repeat:repeat-x;">repeat-x position:default</a></li>
<li><a href="bgimages" style="background-repeat:repeat-x; background-position:left center;">repeat-x position:left-center</a></li>
<li><a href="bgimages" style="background-repeat:repeat-x; background-position:left bottom;">repeat-x position:left-bottom</a></li>
<li><a href="bgimages" style="background-repeat:repeat-x; background-position:right bottom;">repeat-x position:right-bottom</a></li>
<li><a href="bgimages" style="background-repeat:repeat-x; background-position:center center;">repeat-x position:center-center</a></li>
<li><a href="bgimages" style="background-repeat:repeat-x; background-position:50% 50%;">repeat-x position:50%-50%</a></li>
<li><a href="bgimages" style="background-repeat:repeat-x; background-position:4px 8px;">repeat-x position:4px-8px</a></li>
<li><a href="bgimages" style="background-repeat:repeat-x; background-position:-4px -8px;">repeat-x position:-4px--8px</a></li>
<li><a href="bgimages" style="background-repeat:repeat-x; background-position:32px 0px;">repeat-x position:32px-0px</a></li>
<li><a href="bgimages" style="background-repeat:repeat-x; background-position:0px 8px;">repeat-x position:0px-8px</a></li>
</ul>
<ul>
<li><a href="bgimages" style="background-repeat:repeat;">repeat position:default</a></li>
<li><a href="bgimages" style="background-repeat:repeat; background-position:left center;">repeat position:left-center</a></li>
<li><a href="bgimages" style="background-repeat:repeat; background-position:left bottom;">repeat position:left-bottom</a></li>
<li><a href="bgimages" style="background-repeat:repeat; background-position:right bottom;">repeat position:right-bottom</a></li>
<li><a href="bgimages" style="background-repeat:repeat; background-position:center center;">repeat position:center-center</a></li>
<li><a href="bgimages" style="background-repeat:repeat; background-position:50% 50%;">repeat position:50%-50%</a></li>
<li><a href="bgimages" style="background-repeat:repeat; background-position:4px 8px;">repeat position:4px-8px</a></li>
<li><a href="bgimages" style="background-repeat:repeat; background-position:-4px -8px;">repeat position:-4px--8px</a></li>
<li><a href="bgimages" style="background-repeat:repeat; background-position:32px 0px;">repeat position:32px-0px</a></li>
<li><a href="bgimages" style="background-repeat:repeat; background-position:0px 8px;">repeat position:0px-8px</a></li>
</ul>
<ul>
<li><a href="bgimages" style="background-position:32px 8px;">no-repeat position:32px-8px</a></li>
<li><a href="bgimages" style="background-position:32pt 8pt;">no-repeat position:32pt-8pt</a></li>
<li><a href="bgimages" style="background-position:32px 32px;">no-repeat position:32px-32px</a></li>
<li><a href="bgimages" style="background-position:-16px -16px;">no-repeat position:-16px--16px</a></li>
</ul>
<ul>
<li><a href="bgimages" style="background-image:url(http://www.dokuwiki.org/lib/images/fileicons/html.png);">no-repeat position:default image:url(http://www.dokuwiki.org/lib/images/fileicons/html.png) [image remote]</a></li>
<li><a href="bgimages" style="background-image:url(images/png.png);">no-repeat position:default image:url(images/png.png) [image in subfolder]</a></li>
<li><a href="bgimages" style="background-image:url(images/html.png);">no-repeat position:default image:url(html.png) [image in same folder]</a></li>

<li><a href="bgimages" style="background-image:url(images/html.png);">no-repeat position:default image:url(html.png) [image in same folder]
Added very long description, to see if non repeating background image is repeated after a line break.
blah, blah, blah, blah, blah, blah, blah, blah, blah, blah, blah, blah,
blah, blah, blah, blah, blah, blah, blah, blah, blah, blah, blah, blah,
...      
</a></li>


<h2 style="color:red;">Attention!</h2>
<p style="color:red;">For absolute image references to work, the file (dompdf)/www/test/images/smiley.png must be copyied exactly to (virtual web server root)/absimagetest/smiley.png</p>
<li><a href="bgimages" style="background-image:url(/absimagetest/smiley.png);">no-repeat position:default image:url(/absimagetest/smiley.png) [abs image ]</a></li>
</ul>
</div>
<p><a href="bgimages" style="line-height:170%;padding-left:20px;background:url(images/pdf.png) no-repeat yellow;background-position:left top;">paragraph link no-repeat position:default</a></p>
<p style="text-align:left;line-height:170%;padding-left:20px;background:url(images/pdf.png) no-repeat yellow;background-position:left top;">paragraph text no-repeat position:left-top; more text text more text text bla bla sdfjkhs sdfsjksdfks sdfkjsfsf skjfh ksjdfhsd </p>
<p style="text-align:left;line-height:170%;padding-left:20px;background:url(images/pdf.png) no-repeat yellow;background-position:left center;">paragraph text no-repeat position:left-center; more text text more text text bla bla sdfjkhs sdfsjksdfks sdfkjsfsf skjfh ksjdfhsd </p>
<p>
<span>The PHP 5 HTML to PDF converter</span>
</p>

</div>

</body> </html>
