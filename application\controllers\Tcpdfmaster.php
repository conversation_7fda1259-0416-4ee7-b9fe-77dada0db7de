<?php 
class Tcpdfmaster extends CI_Controller{
function __construct() {
        parent::__construct();
        $this->load->library('Pdf');
        $this->load->model('student/Certificates_Model');
}
public function index(){
        
      $templateId = $this->uri->segment(3);
      $studentId = $this->uri->segment(4);
      $template = $this->Certificates_Model->getTemplate($templateId);
      //echo "<pre>"; print_r($data['template']); die();
      $stdData = $this->Certificates_Model->getFullStudentDataById($studentId);
      $stdFatherData = $this->Certificates_Model->getParentDataById($studentId,"Father",array());
      $stdMotherData = $this->Certificates_Model->getParentDataById($studentId,"Mother",array());
      $a = date("d-m-Y", strtotime($stdData->dob));
      $date = explode("-",$a);
      $d = $this->Certificates_Model->convert_number($date[0]);
      $m = $this->Certificates_Model->convert_number($date[1]);
      $y = $this->Certificates_Model->convert_number($date[2]);
      $dob_in_words = $d.'-'.$m.'-'.$y;
      $time = time();
  
      $year = date('Y', $time);
      if(date('n', $time) < 6)
          $ayear = ($year - 1).'/'.$year;
      else
          $ayear = ($year).'/'.($year + 1);
      $academic_year = $ayear;

      if(!empty($stdData->address)){
        $student_address = $stdData->address->Address_line1."<br>".$stdData->address->Address_line2."<br>".$stdData->address->area."<br>".$stdData->address->district."<br>".$stdData->address->state."<br>".$stdData->address->country."<br>".$stdData->address->pin_code;
    }else if(!empty($stdFatherData->address)){
        $student_address = $stdFatherData->address->Address_line1."<br>".$stdFatherData->address->Address_line2."<br>".$stdFatherData->address->area."<br>".$stdFatherData->address->district."<br>".$stdFatherData->address->state."<br>".$stdFatherData->address->country."<br>".$stdFatherData->address->pin_code;
    }else if(!empty($stdMotherData->address)){
        $student_address = $stdMotherData->address->Address_line1."<br>".$stdMotherData->address->Address_line2."<br>".$stdMotherData->address->area."<br>".$stdMotherData->address->district."<br>".$stdMotherData->address->state."<br>".$stdMotherData->address->country."<br>".$stdMotherData->address->pin_code;
    }else{
        $student_address = ' ';
    }
   if($stdData->gender=="M"){$g = "MALE"; $a = "His";}else{$g="FEMALE"; $a = "Her";}
    if($stdData->gender=="M"){$g = "MALE"; $b = "S";}else{$g="FEMALE"; $b = "D";}
    $template->html_content = str_replace('%%admission_no%%',$stdData->admission_no, $template->html_content); 
    $template->html_content =  str_replace('%%student_name%%',$stdData->stdName, $template->html_content);
    $template->html_content =  str_replace('%%academic_year%%',$academic_year, $template->html_content);
    $template->html_content = str_replace('%%class%%',$stdData->className.'/'.$stdData->sectionName, $template->html_content);
    $template->html_content =  str_replace('%%gender%%',$g, $template->html_content);
    $template->html_content = str_replace('%%dob%%',date("d-m-Y", strtotime($stdData->dob)), $template->html_content);
    $template->html_content = str_replace('%%dob_in_words%%',$dob_in_words, $template->html_content);
    $template->html_content =  str_replace('%%father_name%%',$stdFatherData->pName, $template->html_content);
    $template->html_content = str_replace('%%mother_name%%',$stdMotherData->pName, $template->html_content);
    $template->html_content =  str_replace('%%address%%',$student_address, $template->html_content);
    $template->html_content =  str_replace('His / Her',$a, $template->html_content);
    $template->html_content =  str_replace('S/D',$b, $template->html_content);
    $template->html_content =  str_replace(site_url(),'././', $template->html_content);
    $template->html_content .= "<link rel='stylesheet' type='text/css' href='././assets/css/bootstrap/bootstrap.min.css' />";

    $html = "<div class='panel'><div class='panel-body'>";
    $html .= $template->html_content;
    $html .= "</div></div>";


    $pdf = new Pdf('P', 'mm', 'A4', true, 'UTF-8', false);
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);    
    $pdf->SetCreator(PDF_CREATOR);
    $pdf->SetAuthor('Next Element');
    $pdf->SetTitle($template->template_name);
    $pdf->SetSubject($template->purpose);
    $pdf->SetKeywords($template->purpose);   
  

    // set default monospaced font
    $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED); 

    // set margins
    $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
    $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
    $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);    

    // set auto page breaks
    $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM); 

    // set image scale factor
    $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);  

    // set some language-dependent strings (optional)
    if (@file_exists(dirname(__FILE__).'/lang/eng.php')) {
        require_once(dirname(__FILE__).'/lang/eng.php');
        $pdf->setLanguageArray($l);
    }   
    // set default font subsetting mode
    $pdf->setFontSubsetting(true);   
    $pdf->SetFont('dejavusans', '', 14, '', true);   
    $pdf->AddPage(); 

    // set text shadow effect
    $pdf->setTextShadow(array('enabled'=>true, 'depth_w'=>0.2, 'depth_h'=>0.2, 'color'=>array(196,196,196), 'opacity'=>1, 'blend_mode'=>'Normal'));    

    // Set some content to print
    

    // Print text using writeHTMLCell()
    $pdf->writeHTMLCell(0, 0, '', '', $template->html_content, 0, 1, 0, true, '', true);  
    $pdf->Output($template->template_name.'-'.$stdData->id.'.pdf', 'I');    
    } 
 
}