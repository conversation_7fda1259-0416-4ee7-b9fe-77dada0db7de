<?php

defined('BASEPATH') OR exit('No direct script access allowed');

//this controller handles the call from remote server (attendance server for sending reports to management) 
class Management_communication extends CI_Controller 
{
    function __construct()
	{
		parent::__construct();
		$this->load->model('Management_communication_model', 'management');
		$this->load->helper('email_helper');
        date_default_timezone_set("Asia/Kolkata");
	}

    public function sendAdmissionReport() {
        $members = explode(',', $this->settings->getSetting('management_staff_ids'));
        $from_email = $this->settings->getSetting('circularv2_from_email');
        if($from_email != '') {
            $emails = $this->management->getMemberEmailIds($members);
            // trigger_error(json_encode($emails));
            $subject = 'Subject';
            $message = 'Sample Message';
            sendEmail($message, $subject, $emails, $from_email);
        } 
    }

    public function sendReports() {
        $this->_sendEveryMinuteData();
        $this->_sendEveryHourData();
        $this->_sendEveryDayData();
    }

    private function _sendEveryMinuteData() {
        $current_time = date('Y-m-d H:i:s');
        $str_time = strtotime($current_time);
        $data = $this->management->getTemplateData(3);
        foreach ($data as $key => $val) {
            if($val->last_executed_time == '' || ((($str_time - strtotime($val->last_executed_time))/60) >= 1) ) {
                $this->_reportSender($val, $current_time);
            }
        }
    }

    private function _sendEveryHourData() {
        $current_time = date('Y-m-d H:i:s');
        $str_time = strtotime($current_time);
        $data = $this->management->getTemplateData(2);
        foreach ($data as $key => $val) {
            if($val->last_executed_time == '' || ((($str_time - strtotime($val->last_executed_time))/3600) >= 1) ) {
                $this->_reportSender($val, $current_time);
            }
        }
    }

    private function _sendEveryDayData() {
        $current_time = date('Y-m-d H:i:s');
        $str_time = strtotime($current_time);
        $data = $this->management->getTemplateData(1);
        foreach ($data as $key => $val) {
            if($val->last_executed_time == null) {
                if((abs($str_time - strtotime($val->execution_time))/3600) <= 0.5) {
                    $this->_reportSender($val, $current_time);
                }
            } else if((($str_time - strtotime($val->last_executed_time))/3600) >= 24) {
                $this->_reportSender($val, $current_time);
            }
        }
    }

    private function _reportSender($template, $current_time) {
        //update last execution time
        $this->db->where('id', $template->id)->update('management_report_templates', array('last_executed_time' => $current_time));

        $content = $template->email_template;
        $content = str_replace("%%date%%", date('d M Y h:i a', strtotime($current_time)), $content);
        $report = '';
        preg_match_all("/%%.*?%%/", $content, $matched);
        foreach ($matched as $i => $match) {
          foreach ($match as $key => $matchStr) {
            $report = $this->_prepareReport($matchStr);
            $content = str_replace($matchStr, $report, $content);
          }
        }

        $emails = $this->_getMemberEmails(json_decode($template->members));
        // trigger_error(json_encode($emails));
        if(!empty($emails)) {
            sendEmail($content, $template->email_subject, $emails, $template->from_email);
        }
    }

    private function _prepareReport($mapString) {
        switch ($mapString) {
            case '%%attendance_report_today%%':
                $report = $this->management->attendanceReportToday();
                break;

            case '%%admission_report_today%%':
                $report = $this->management->admissionReportToday();
                break;

            case '%%enquiry_report_today%%':
                $report = $this->management->enquiryReportToday();
                break;

            default: $report = '';
        }
        return $report;
    }

    private function _getMemberEmails($memberIds) {
        $emails = array();
        if(!empty($memberIds)) {
            $emails = $this->management->getMemberEmailIds($memberIds);
        }
        return $emails;
    }

}