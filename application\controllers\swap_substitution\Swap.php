<?php
    class Swap extends CI_Controller {
        function __construct() {
            parent::__construct();
            if (!$this->ion_auth->logged_in()) {
                redirect('auth/login', 'refresh');
            }
            if (!$this->authorization->isModuleEnabled('SUBSTITUTION')) {
                redirect('dashboard', 'refresh');
            }
            $this->load->model('timetablev2/Swap_model', 'swap');
        }

        public function index(){
            $data['main_content']    = 'swap/index.php';
            $this->load->view('inc/template', $data);
        }

        public function fetch_section_names(){
            $templateId = $_POST['templateId'];
            $data['section_list'] = $this->swap->fetch_section_names($templateId);
            echo json_encode($data);
          }

        public function get_section_staff_timetables(){
            $template_id = $_POST['template_id'];
            $week_day = $_POST['week_day'];
            $section_id = $_POST['section_id'];
            $substitution_id = $_POST['substitution_id'];
            $staff_list = $this->swap->get_section_staff_list($section_id);
            $sub_data = $this->swap->get_staff_substitutions_data_by_id($substitution_id);
            $substituted_staff = $this->swap->get_substitutable_staff($substitution_id);
            $ttArr = array ();
            foreach ($staff_list as $staff) {
                // if(in_array($staff->staff_id, $substituted_staff)) continue;
                $temp = new stdClass();
                $temp->staff_id = $staff->staff_id;
                $temp->staff_name = $staff->staff_name;
                $temp->staff_exception = $staff->staff_exception;
                $tta = $this->swap->get_staff_template_periods_subjects($template_id, $substitution_id, $staff->staff_id, $week_day);
                if($tta){
                    $temp->tt_exists = 1;
                    foreach ($tta as &$period) {
                    $stf_id = $staff->staff_id;
                    if(array_key_exists($stf_id, $sub_data)) {
                        $p_id = $period->period_id;
                        $period->substitute_status = 0;//not substituted
                        if($this->_is_exists($p_id, $sub_data[$stf_id])) {
                            $period->substitute_status = 1;//substituted
                            $period->sub_staff_name[] = $sub_data[$stf_id][$p_id]->staff_name;
                            $period->sub_sec_name = $sub_data[$stf_id][$p_id]->class_section_name;
                        }
                    }
                    }
                    $temp->tta = $tta;
                }
                else{
                    $temp->tt_exists = 0;
                }
                $ttArr[] = $temp;
            }
        
            function compareStaffByException($a, $b) {
                if ($a->staff_exception == $b->staff_exception) {
                    return 0;
                }
                return ($a->staff_exception > $b->staff_exception) ? 1 : -1;
            }
            usort($ttArr, 'compareStaffByException');
            $data['staffTT'] = $ttArr;
            echo json_encode($data);
        }

        private function _is_exists($stp_id, $sub_stp_arr) {
            foreach ($sub_stp_arr as $sub_stp_arr_obj) {
              $temp_arr = explode(',',$sub_stp_arr_obj->sub_staff_period_id);
              if (in_array($stp_id, $temp_arr)) {
                return 1;
              }
            }
            return 0;
        }

        public function getSectionTTWithSubs() {
            $csIdList = $_POST['csIdList'];
            $subDate = $_POST['subDate'];
            $weekDay = $_POST['weekDay'];
            $template_id = $_POST['template_id'];
            $date = $_POST['subDate'];
            $rDate = date('Y-m-d',strtotime($subDate));
        
            $data['template_obj'] = $this->swap->get_template_by_id($template_id);
            $ttArr = array ();
            foreach ($csIdList as $csId) {
                $temp = new stdClass();
                $temp->csId = $csId;
                $temp->csName = $this->swap->getSectionName($csId);
                $temp->section_oc_links = $this->swap->get_section_oc_links($csId);
                $tta =  $this->swap->getSectionTTByWeekDay($csId, $weekDay, $rDate, $template_id, $date);        
                if($tta){
                    $temp->tt_exists = 1;
                    $temp->tta =$tta;
                }
                else{
                    $temp->tt_exists = 0;
                }
                $ttArr[] = $temp;
            }
            $data['TT'] = $ttArr;
            echo json_encode($data);
        }

        public function get_template_id(){
            echo json_encode($this->swap->get_template_id());
        }

        public function getFullTTForStaff(){
            $staffId = $this->input->post('staffId');
            $template_id = $this->input->post('template_id');
            $week_day = $this->input->post('week_day');

            $data = $this->swap->getFullTTForStaff($staffId, $template_id, $week_day);
            echo json_encode($data);
        }

        public function add_substitution_day() {
            $date = date('Y-m-d', strtotime($_POST['date']));
            $substitution_id = $this->swap->add_substitution_day($date);
            echo json_encode($substitution_id);
        }

        public function deallocate_staff(){
            $period_id = $_POST['period_id'];
            $class_section_name = $_POST['cs_name'];
            $class_section_id = $_POST['cs_id'];
            $staff_id = $_POST['staff_id'];
            $week_day = $_POST['week_day'];
            $date = date('Y-m-d', strtotime($_POST['date']));
            $old_sub_name = $_POST['old_sub_name'];
            $template_id = $_POST['template_id'];
            $old_sub_id = $_POST['old_sub_id'];
            $res = $this->swap->deallocate_staff($period_id, $class_section_name, $class_section_id, $staff_id, $week_day, $date, $old_sub_name, $template_id, $old_sub_id);
            echo $res;
        }

        public function substitute_staff() {
            $status = $this->swap->substitute_staff();
            echo $status;
        }

        public function get_sub_inp_id(){
            $sub_id = $_POST['sub_id'];
            $staff_id = $_POST['staff_id'];
            echo json_encode($this->swap->get_sub_inp_id($sub_id, $staff_id));
        }

        public function get_old_staff_period_id(){
            $ttv2_sub_input_id = $_POST['ttv2_sub_input_id'];
            $period_id = $_POST['period_id'];
            echo json_encode($this->swap->get_old_staff_period_id($ttv2_sub_input_id, $period_id));
        }

        public function remove_substitution() {
            $status = $this->swap->remove_substitution();
            echo $status;
        }

        public function delete_adhoc_staff() {
            $sin_id = $_POST['sin_id'];
            echo $this->swap->delete_adhoc_staff($sin_id);
        }
    }
?>