<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Academic Year
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Academic_year extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      		redirect('auth/login', 'refresh');
    	}
  	}

  	public function session_load_year(){
  		$year = $_POST['year'];
      $this->acad_year->setAcadYearId($year);
  	}

}