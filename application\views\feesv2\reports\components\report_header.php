<!-- 
 * Common Header File for Fees Reports Pages
 *
 * This file sets up the top portion of a report page. It includes:
 *  - Report-specific CSS (report_css.php)
 *  - Export button JavaScript (reports_export_button_js.php)
 *  - Bootstrap 5 and Bootstrap Icons from CDN
 *  - Report breadcrumb, card layout, and export/print buttons
 *
 * ⚠️ IMPORTANT:
 * Make sure to **close the HTML tags properly in the views page** after including this file.
 *
 *   You must close the following 3 <div> tags in the view file:
 *    1. <div class="card new-panel-style_3">
 *    2. <div class="card-header ...">
 *    3. <div class="col-md-12">
 *
 * If not closed properly, it can break the layout or affect other UI components.
 *
 *   Created by: <PERSON><PERSON><PERSON>all<PERSON>
 *   Date: 13 June 2025
 -->

<?php include(APPPATH . 'views/feesv2/reports/components/report_css.php'); ?>
<?php include(APPPATH . 'views/feesv2/reports/inc/reports_export_button_js.php'); ?>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">

<ul class="breadcrumb">
</ul>
<div class="col-md-12 mb-10">
  <div class="card new-panel-style_3" style="background-color: #F9F7FE;">
    <div class="card-header panel_heading_new_style_staff_border border-0" style="background-color: #F9F7FE;">
      <div class="row m-0">
        <div class="d-flex align-items-center justify-content-between w-100">
          <h3 class="card-title panel_title_new_style_staff mb-0 d-flex align-items-center">
            <a class="back_anchor me-2" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
              <span class="bi bi-chevron-left report-header-icon"></span>
            </a>
            <span class="report-header-title">
              <?php echo isset($report_title) ? $report_title : 'Report'; ?>
            </span>

          </h3>
          <div style="margin-right:20px;margin-top:-13px">
            <?php $this->load->helper('reports_datatable');
            echo render_report_buttons2('printProfile', 'exportToExcel_daily'); ?>
          </div>
        </div>
      </div>