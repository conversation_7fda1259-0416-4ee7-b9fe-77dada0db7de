<?php
class Assessment_electives extends CI_Controller {
    private $yearId;
    function __construct() {
        parent::__construct();
       	if (!$this->ion_auth->logged_in()) {       
         	redirect('auth/login', 'refresh');
       	}
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('examination/Assessment_model','assessment_model');
      $this->load->model('examination/Assessment_electives_model', 'assessment_electives');
      
    }

    public function index($class_id=0){
	    $data['classList'] = $this->assessment_model->getClassess();
	    $data['selectedClass'] = $class_id;
	    $data['className'] = 'All';
	    if($class_id != 0) {
	      $class = $this->assessment_model->getClassName($class_id);
	      $data['className'] = $class->className;
	    }
	    $data['electiveGroups'] = $this->assessment_electives->getAllEGroups($class_id);
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'examination/elective/elective_groups_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'examination/elective/elective_groups_mobile';
        }else{
          $data['main_content'] = 'examination/elective/elective_groups';    	
        }
	    $this->load->view('inc/template', $data);
	}

	public function get_electives() {
		$class_id = $_POST['class_id'];
		$data['groups'] = $this->assessment_electives->getsubjects($class_id);
	    $data['electives'] = $this->assessment_electives->getAllEGroups($class_id);
		foreach( $data['electives'] as $key => $value) {
			$data['electives']["$key"]['is_deletable']= $this->assessment_electives->check_if_deletable($key);
		}

	    echo json_encode($data);
	}

	public function getEntityGroups(){
	    $groups = $this->assessment_electives->getGroups($_POST['classId']);
	    $electiveGroups = $this->assessment_electives->getAllEGroups($_POST['classId']);
	    if(empty($electiveGroups)){
	          $template = '<h4>No groups added</h4>';
	        } else {
	          $template = "<table id='customers1' class='table datatable'>
	            <thead>
	              <tr>
	                <th>#</th>
	                <th>Class</th>
	                <th>Group Name</th>
	                <th>Mapping string</th>
	                <th>Subjects</th>
	                <th>Actions</th>
	              </tr>
	            </thead>
	            <tbody>";
	               
	            $i=1;     
	              foreach ($electiveGroups as $id => $group) { 
	              $site = site_url("examination/assessment_electives/bifercateStd/".$id);
	             $template .= '<tr><td>'.$i++.'</td><td>'.$group["class_name"].'</td><td>'.$group["group_name"].'</td><td>'.$group["mapping_string"].'</td><td>'.$group["entity_name"].'</td><td><a href='.$site.' class="btn btn-warning " data-placement="to" data-toggle="tooltip" data-original-title="Bifercate Students"><i class="fa fa-code-fork"></i></a></td></tr>';
	              }

	           $template .= "</tbody></table>";
	        }
	        $data = array('table'=>$template, 'groups'=>$groups);
	        
	        echo json_encode($data);
	}

	public function addElectiveGroup(){
	    $classId = $this->input->post('class_id');
	    $status = (int)$this->assessment_electives->insertElectiveGroup();
	    if ($status == '1') {
	      $this->session->set_flashdata('flashSuccess', 'Elective added successfully');
	    } 
		else if ($status == '-1') {
	      $this->session->set_flashdata('flashError', 'Duplicate Elective name found');
		} else {
	      $this->session->set_flashdata('flashError', 'Something went wrong...');
	    } 
	    redirect('examination/assessment_electives/index/'.$classId);
	}

	public function bifercateStd($eleGroupId){
	    $data['groupData'] = $this->assessment_electives->getEGData($eleGroupId);
	    $data['groupGroups'] = $this->assessment_electives->getGroupData($eleGroupId);
	    $data['getStudents'] = $this->assessment_electives->getStudents($data['groupData']->id, $data['groupData']->class_id);
	    $data['electedStd'] = $this->assessment_electives->getElectedStd($data['groupGroups']);
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'examination/elective/bifercate_students_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'examination/elective/bifercate_students_mobile';
        }else{
          $data['main_content'] = 'examination/elective/bifercate_students';      	
        }
	    $this->load->view('inc/template', $data);
	}

	public function associateStdElective(){
	    echo $this->assessment_electives->allocateStdElectives();
	}

	public function removeEleStudent() {
    	echo $this->assessment_electives->removeEleStd($_POST['id']);
  	}

	public function remove_all_Student(){
		echo $this->assessment_electives->remove_all_Student($_POST['entity_group_id']);
	}

  	public function report() {
  		$data['classList'] = $this->assessment_model->getClassess();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'examination/elective/report_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'examination/elective/report_mobile';
        }else{
          $data['main_content'] = 'examination/elective/report';      	
        }
	    $this->load->view('inc/template', $data);
  	}

  	public function getElectiveReport() {
  		$class_id = $_POST['class_id'];
		$section_id = $_POST['section_id'];
  		$data['students'] = $this->assessment_electives->getElectiveReport($class_id, $section_id);
  		$data['electives'] = $this->assessment_electives->getElectives($class_id);
  		echo json_encode($data);
  	}

	public function getElectiveReport_section_wise() {
  		$class_id = $_POST['class_id'];
		$section_id = $_POST['section_id'];
  		$data['students'] = $this->assessment_electives->getElectiveReport($class_id, $section_id);
  		$data['electives'] = $this->assessment_electives->getElectives($class_id);
  		echo json_encode($data);
  	}

  	public function edit_elective() {
  		echo $this->assessment_electives->edit_elective($_POST);
  	}

	public function getSections() {
		$class_id = $_POST['class_id'];
  		$sections = $this->assessment_electives->getSections($class_id);
  		echo json_encode($sections);
	}
	
	public function delete_elective_group() {
		$egId= $_POST['egId'];
		$status= $this->assessment_electives->delete_elective_group($egId);
		echo $status;
	}

	public function check_if_group_name_is_unique() {
		$status= $this->assessment_electives->check_if_group_name_is_unique();
		echo json_encode($status);
	}

	public function check_if_elective_is_unique() {
		$status= $this->assessment_electives->check_if_elective_is_unique();
		echo json_encode($status);
	}

	public function check_if_mapping_is_unique() {
		$status= $this->assessment_electives->check_if_mapping_is_unique();
		echo json_encode($status);
	}

	public function delete_subject_in_elective() {
		$status= $this->assessment_electives->delete_subject_in_elective();
		echo json_encode($status);
	}

	public function get_detail_in_ilective() {
		$status= $this->assessment_electives->get_detail_in_ilective();
		echo json_encode($status);
	}

	public function submit_edit_subject_in_elective() {
		$status= $this->assessment_electives->submit_edit_subject_in_elective();
		echo json_encode($status);
	}

	public function add_more_subjects_to_elective() {
		$status= $this->assessment_electives->add_more_subjects_to_elective();
		echo json_encode($status);
	}

}