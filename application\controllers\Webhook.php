<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Webhook extends CI_Controller {
	function __construct(){
		parent::__construct();
	}


  public function payment_debited() {
      $jsonPayload = file_get_contents('php://input');
	  $decodedArray = json_decode($jsonPayload, true);
      $student_details  = $decodedArray['payload']['student'];

      $fees_details  = $decodedArray['payload']['payment'];
      $jodo_student_id  = $decodedArray['payload']['jodo_student_id'];
      $comp_amount_paid = [];
      foreach ($fees_details['details'] as $key => $value) {
          $comp_amount_paid[$value['fee_component']] = $value['amount'];
      }
      $this->load->model('feesv2/fees_collection_model');
	  $discountComp = $this->_get_discount_by_jodo_student_id($jodo_student_id);
	  $discountArry = [];
	  if(!empty($discountComp)){
		foreach ($discountComp as $key => $val) {
			if($val->discount_amount !=0){
				$discountArry[$val->component_type] = $val->discount_amount;
			}
		  }
	  }
      $result = $this->fees_collection_model->debited_call_back_fee_transaction($student_details['identifier'], $student_details['academic_year_start'], $student_details['grade']['code']);
      // Generate transaction receipts
      $this->webhook_fee_trans_inesrt($result, $comp_amount_paid, $fees_details['paid_at'], $fees_details, $jsonPayload,$discountArry);
  }
  	private function webhook_fee_trans_inesrt($fees_data, $compAmountPaid, $paid_at, $fees_details, $jsonPayload, $inputDiscount) {
		
		foreach ($compAmountPaid as $code => $value) {
			if(array_key_exists($code, $inputDiscount)){
				$compAmountPaid[$code] += $inputDiscount[$code];
			}
		}

	$blueprintArray = [];
	$discountArray = [];
		foreach ($fees_data as $key => $fee) {
			if(array_key_exists($fee->jodo_code, $compAmountPaid)){
				$totalAmontPaid[$fee->blueprint_id] = 0;
				if (!array_key_exists($fee->blueprint_id, $blueprintArray)) {
					$blueprintArray[$fee->blueprint_id] = [
						"student_id" => $fee->student_id,
						"fee_student_schedule_id" => $fee->fee_student_schedule_id,
						"paid_at" => $paid_at,
						"acad_year_id" => $fee->acad_year_id,
					];
				}
				$blueprintArray[$fee->blueprint_id]["comp_details"][] = [
					"blueprint_component_id" => $fee->blueprint_component_id,
					"blueprint_installments_id" => $fee->blueprint_installments_id,
					"amount_paid" => $compAmountPaid[$fee->jodo_code],
					"concession_amount" =>  $fee->concession_amount,
					"fee_student_installments_components_id" => $fee->fee_student_installments_components_id,
					"fee_student_installments_id" =>  $fee->fee_student_installments_id,
					"refund_amount" => '0',
					"fine_amount" => '0',
					"adjustment_amount" => '0',				
				];
			}

			if(array_key_exists($fee->jodo_code, $inputDiscount)){
				$discountArray[$fee->blueprint_id] = [
					'discount' => $inputDiscount[$fee->jodo_code],
				];
			}
		}

		foreach ($blueprintArray as $bpId => $value) {
			$totalAmountPaid = 0;
			$input = array();
			foreach ($value['comp_details'] as $key => $val) {
				$totalAmountPaid += $val['amount_paid'];
				$input['pay_amount'][$val['blueprint_installments_id']][$val['blueprint_component_id']] =  $val['amount_paid'];
				$input['conc_amount'][$val['blueprint_installments_id']][$val['blueprint_component_id']] =  $val['concession_amount'];
				$input['comp_id'][] = $val['blueprint_component_id'];
				$input['fsicompId'][$val['blueprint_installments_id']][$val['blueprint_component_id']] = $val['fee_student_installments_components_id'];
				$input['fsInsId'][$val['blueprint_installments_id']][$val['blueprint_component_id']] = $val['fee_student_installments_id'];
				$input['cohort_student_id'] = $value['student_id'];
				$input['fee_student_schedule_id'] = $value['fee_student_schedule_id'];
				$input['acad_year_id'] = $value['acad_year_id'];
			}
			$bpDiscount = isset($discountArray[$bpId]['discount']) ? $discountArray[$bpId]['discount'] : 0;
			$input['total_amount'] = $totalAmountPaid;
			$input['discount_amount'] = $bpDiscount;
			$input['loan_provider_charges'] = 0;
			$input['final_amount'] = $totalAmountPaid - $bpDiscount;
			$input['payment_type'] = '10_0';
			$input['bank_name'] = '';
			$input['branch_name'] = '';
			$input['cheque_dd_nb_cc_dd_number'] = '';
			$input['dd_number'] = '';
			$input['bank_date'] = '';
			$input['cc_number'] = '';
			$input['nb_number'] = '';
			$input['remarks'] = '';
			$input['excess_amount_id'] = '';
			$input['student_id'] = $value['student_id'];
			$input['transaction_mode'] = 'ONLINE';
			$input['blueprint_id'] = $bpId;
			$input['receipt_date'] = $value['paid_at'];
			
			$this->load->model('feesv2/fees_collection_model');
			$fTrans = $this->fees_collection_model->insert_fee_transcation_webhook($input);

			if (empty($fTrans)) {
				$this->db->trans_rollback();
			}else{
				// Update online payment master
				$this->fees_collection_model->online_payment_master_insert($fTrans, $fees_details, $jsonPayload);
				// generate and update receipt number after transcation
				$this->fees_collection_model->update_receipt_transcation_wise($fTrans);
				// Update all Schedule table          
				$result =  $this->fees_collection_model->update_student_schedule_all_table($fTrans);
				// trigger_error('result');             
				// trigger_error(json_encode($result));       
				if (empty($result)){
					$this->db->trans_rollback();
				}
				if ($this->db->trans_status()){   
					$transaction_id =  isset($fees_details['transaction_id']) ? $fees_details['transaction_id'] : $fees_details['id'];
					if(isset($fees_details['paid_at'])){
						$transaction_date = date('d-m-Y', strtotime($fees_details['paid_at']));
						$transaction_time = date('H:i', strtotime($fees_details['paid_at']));
					}
					$this->fees_collection_model->create_pdf_template_for_fee_receipts_webhook($fTrans, $transaction_id, $transaction_date, $transaction_time); 
					$this->db->trans_commit();
				}
				else{
					$this->session->set_flashdata('flashError', 'Something went wrong');
				}
			}
		}
	}
  private function _get_discount_by_jodo_student_id($jodo_student_id){
	$authorization_key = $this->settings->getSetting('jodo_header_authorization_key');
	$jodo_key = 'Basic YTIwOWQ2NWNkNjc4OjI4ZGM0ODRkLTZkOTgtNDdjNy1iMDBlLWY1M2NiZDg3ZDQ1Mw==';
	if(!empty($authorization_key)){
		$jodo_key = $authorization_key;
	}
	$this->jodo_payment_discount_url = 'https://ext.jodo.in/api/v2/integrations/erp/students';
	$curl = curl_init();
	curl_setopt_array($curl, array(
		CURLOPT_URL => $this->jodo_payment_discount_url.'/'.$jodo_student_id.'?includes=fee_components',
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'GET',
		CURLOPT_HTTPHEADER => array(
			// 'Key:'.$order_id,
			// 'Authorization: Basic ODlmZmNiYjExOWU3OmFiOTMwNDY1LTBlZTgtNDI0MS04MTJkLWU4N2YyZTM4NjNiNg=='
			'Authorization: '.$jodo_key.''
		),
	));
	$response = curl_exec($curl);

	$err = curl_error($curl);
	$res = json_decode($response);
	if(!empty($res->data->fee_components)){
		return $res->data->fee_components;
	}else{
		return [];
	}	
  }

  

    public function payment_settled() {
        $paysetted = file_get_contents('php://input');
        $settled = json_decode($paysetted, true);

        // trigger_error("Settled file jodo!");
        // trigger_error(json_encode($settled));
        // $transaction_id = $data['transaction_id'];
        // $amount = $data['amount'];
    }

	public function flex_setup(){
	    $jsonPayload = file_get_contents('php://input');
		$decodedArray = json_decode($jsonPayload, true);
		$student_details  = $decodedArray['payload']['student'];
		$fees_details  = $decodedArray['payload']['payment_schedule'];
		$subscription  = $decodedArray['payload']['subscription'];
		$comp_amount_flex_setup = [];
		foreach ($fees_details as $key => $value) {
			foreach ($value['details'] as $key => $val) {
				$comp_amount_flex_setup[$val['fee_component']] = $value['due_date'];
			}
		}
		$this->load->model('feesv2/fees_collection_model');
		$result = $this->fees_collection_model->debited_call_back_fee_transaction($student_details['identifier'], $student_details['academic_year_start'], $student_details['grade']['code']);
		$flexSetupArry = [];
		foreach ($result as $key => $val) {
			if(array_key_exists($val->jodo_code, $comp_amount_flex_setup)){
				$flexSetupArry[$val->fee_student_installments_id] = $comp_amount_flex_setup[$val->jodo_code];
			}
		}
		$this->_update_student_installments_audo_debit_status($subscription, $flexSetupArry);
	}

	public function _update_student_installments_audo_debit_status($subscription, $flexSetupArry){
		$data = array();
		foreach ($flexSetupArry as $insId => $due_date) {
			$data[] = array(
				'id' => $insId,
				'auto_debit' => 1,
				'auto_debit_due_date' => $due_date,
				'auto_debit_status' => $subscription['status'],
				'auto_debit_setup_date' => $subscription['setup_date'],
				'auto_debit_subscription_id' => $subscription['id'],
			);
		}
		$this->db->update_batch('feev2_student_installments',$data,'id');
	}

	public function flex_active(){
		$jsonPayload = file_get_contents('php://input');
		$decodedArray = json_decode($jsonPayload, true);
		$student_details  = $decodedArray['payload']['student'];
		$fees_details  = $decodedArray['payload']['payment_schedule'];
		$subscription  = $decodedArray['payload']['subscription'];
		$comp_amount_flex_setup = [];
		foreach ($fees_details as $key => $value) {
			foreach ($value['details'] as $key => $val) {
				$comp_amount_flex_setup[$val['fee_component']] = $value['due_date'];
			}
		}
		$this->load->model('feesv2/fees_collection_model');
		$result = $this->fees_collection_model->debited_call_back_fee_transaction($student_details['identifier'], $student_details['academic_year_start'], $student_details['grade']['code']);
		$flexSetupArry = [];
		foreach ($result as $key => $val) {
			if(array_key_exists($val->jodo_code, $comp_amount_flex_setup)){
				$flexSetupArry[$val->fee_student_installments_id] = $comp_amount_flex_setup[$val->jodo_code];
			}
		}
		$this->_update_student_installments_audo_debit_status($subscription, $flexSetupArry);
	}

	public function flex_instalment_debited() {
		$jsonPayload = file_get_contents('php://input');
		$decodedArray = json_decode($jsonPayload, true);
		$student_details  = $decodedArray['payload']['student'];
		$fees_details  = $decodedArray['payload']['payment'];
		$jodo_student_id  = $decodedArray['payload']['jodo_student_id'];
		$comp_amount_paid = [];
		foreach ($fees_details['details'] as $key => $value) {
			$comp_amount_paid[$value['fee_component']] = $value['amount'];
		}
		
		$this->load->model('feesv2/fees_collection_model');
		$discountComp = $this->_get_discount_by_jodo_student_id($jodo_student_id);
		$discountArry = [];
		foreach ($discountComp as $key => $val) {
		if($val->discount_amount !=0){
			$discountArry[$val->component_type] = $val->discount_amount;
		}
		}	  
		$result = $this->fees_collection_model->debited_call_back_fee_transaction($student_details['identifier'], $student_details['academic_year_start'], $student_details['grade']['code']);
		// Generate transaction receipts
		$this->webhook_fee_trans_inesrt($result, $comp_amount_paid, $fees_details['paid_at'], $fees_details, $jsonPayload,$discountArry);
	}


	public function direct_payment_debited() {
		$jsonPayload = file_get_contents('php://input');
		$decodedArray = json_decode($jsonPayload, true);
		$student_details  = $decodedArray['payload']['student'];
		$fees_details  = $decodedArray['payload']['payment'];
		$jodo_student_id  = $decodedArray['payload']['jodo_student_id'];
		$comp_amount_paid = [];
		foreach ($fees_details['details'] as $key => $value) {
			$comp_amount_paid[$value['fee_component']] = $value['amount'];
		}
		
		$this->load->model('feesv2/fees_collection_model');
		$discountComp = $this->_get_discount_by_jodo_student_id($jodo_student_id);
		$discountArry = [];
		foreach ($discountComp as $key => $val) {
		  if($val->discount_amount !=0){
			  $discountArry[$val->component_type] = $val->discount_amount;
		  }
		}
		$result = $this->fees_collection_model->debited_call_back_fee_transaction($student_details['identifier'], $student_details['academic_year_start'], $student_details['grade']['code']);
		// Generate transaction receipts
		$this->webhook_fee_trans_inesrt($result, $comp_amount_paid, $fees_details['paid_at'], $fees_details, $jsonPayload,$discountArry);
	}
}