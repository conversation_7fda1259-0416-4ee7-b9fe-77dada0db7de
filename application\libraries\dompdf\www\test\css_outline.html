<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
<style>
table {
  border-spacing: 15px;
  margin: 2em auto;
}
td {
  outline-width: 5px;
  border: thin dotted gray;
  padding: 1em;
}

td.dotted { outline-style: dotted; color: maroon;}
td.dashed { outline-style: dashed; color: orange; }
td.solid  { outline-style: solid; color: yellow; }
td.double { outline-style: double; color: olive; }
td.groove { outline-style: groove; color: green; }
td.ridge  { outline-style: ridge; color: lime; }
td.inset  { outline-style: inset; color: teal; }
td.outset { outline-style: outset; color: blue; }

div { margin: 5pt; }

div.full { outline:thin blue solid;}

div.partialthinsolid,
div.partial1,
div.partial2 { outline:thin solid;}

div.partial1 { outline-color:green; }
div.partial2 { outline:blue;}

div.partial3 { outline-style:dashed; }
div.partial4 { outline-width:thick; }
div.partial5 { outline-width:medium; }
div.partial6 { outline-width:3pt; }

</style>
</head>

<body>
<table>
	<tr>
		<th colspan="4">The dotted gray line is the border box</th>
  </tr>
  <tr>
    <td class="dotted">dotted</td>
    <td class="dashed">dashed</td>
    <td class="solid">solid</td>
    <td class="double">double</td>
  </tr>
  <tr>
    <td class="groove">groove</td>
    <td class="ridge">ridge</td>
    <td class="inset">inset</td>
    <td class="outset">outset</td>
  </tr>
</table>

<h2>partial attributes merged</h2>

<div style="outline:thin solid red;">outline:thin solid red;</div>
<div style="outline:red thin solid;">outline:red thin solid;</div>
<div class="partialthinsolid partial2">{ outline:thin solid; }{outline:blue; } (merged, reset all - color has no effect)</div>
<div class="partialthinsolid partial1">{ outline:thin solid; }{outline-color:green; } (merged, overwrite only color)</div>
<div class="partial2">{ outline:thin solid; }{outline:blue; } (merged, reset all - color has no effect)</div>
<div class="partial1">{ outline:thin solid; }{outline-color:green; } (merged, overwrite only color)</div>
<div class="full partial1">{ outline:thin blue solid; }{outline-color:green; } (merged, overwrite only color)</div>
<div class="full partial3">{ outline:thin blue solid; }{outline-style:dashed; } (merged, overwrite only style)</div>
<div class="full partial4">{ outline:thin blue solid; }{outline-width:thick; } (merged, overwrite only width)</div>
<div class="full partial5">{ outline:thin blue solid; }{outline-width:medium; } (merged, overwrite only width)</div>
<div class="full partial6">{ outline:thin blue solid; }{outline-width:3pt; } (merged, overwrite only width)</div>

</body> </html>
