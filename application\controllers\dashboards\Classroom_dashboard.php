<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>
 *          <EMAIL>
 *
 * Created:  17 August 2022
 *
 * Description: Controller for Classroom Dashboard Module.
 *
 * Requirements: PHP5 or above
 *
 */

class Classroom_dashboard extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
     $this->load->model('student/Student_Model');
  }

  //Landing function to show non-compliance menu
  public function index() {
    $data['getclassinfo'] = $this->Student_Model->getClassSectionNames();  
    $data['main_content']    = 'dashboard/classroom/index';
    $this->load->view('inc/template', $data);
  }

}