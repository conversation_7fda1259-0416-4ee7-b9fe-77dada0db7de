<?php

class Calenderevents extends CI_Controller{
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('SCHOOL_CALENDAR')) {
            redirect('dashboard', 'refresh');
        }    
        $this->load->model('Calender_events');
        $this->load->model('Holiday_info_model');
        $this->load->model('calenderevents_model');
        $this->load->model('class_section');
    }

    function _remap($method,$args)
    {
        if (method_exists($this, $method))
        {
            $this->$method($args);
        }
        else
        {
            $this->index($method,$args);
        }
    }

    public function index($id = null){
        $data['id'] = $id;
        $data['editData'] = array();
        if($id){
            $data['editData'] = $this->calenderevents_model->get_eventDetails($id);
        }
        $start_month = (int)$this->settings->getSetting('academic_start_month');
        if($start_month == '' || $start_month > 12 || $start_month < 01) {
            $start_month = 5;
        }
        $acad_year = explode("-", $this->acad_year->getAcadYear())[0];
        $month = date('m');
        $data['months'] = [];
        $start_date = $acad_year.'-'.$start_month.'-01';
        for($i=0; $i < 12; $i++) {
            $date = date($start_date);
            $m = date('m', strtotime($date." +$i months"));
            $data['months'][] = array('index' => date('Y-m', strtotime($date. " +$i months")), 'name' => date('F-Y', strtotime($date." +$i months")), 'is_current' => ($m==$month)?'selected':'');
        }
        // echo "<pre>"; print_r($data['editData']); die();
        $data['class_section'] = $this->class_section->getAllClassSections();
        $data['main_content'] = 'calenderEvents/index';
        $this->load->view('inc/template', $data);
    }

    //formating date
    public function formatDate($date) {
        $date1 = explode("-", $date);
        $newDate = $date1[2] . "-" . $date1[1] . "-" . $date1[0];
        return $newDate;
    }

    public function get_monthEvents(){
        $month = $_POST['month'];
        $data['monthEvents'] = $this->calenderevents_model->get_monthEvent($month);

        $boards = $this->settings->getSetting('board');
        foreach ($data['monthEvents'] as &$event) {
            foreach ($boards as $key => $board) {
                if ($event->board == '100') {
                    $event->board_name = 'All'; //'100' means applicable for all boards
                }
                if ($event->board == $key) {
                    $event->board_name = $board;
                    break;
                }
            }
        }

        echo json_encode($data['monthEvents']);
    }

    public function addEvent($id = null){
        $this->db->trans_begin();
        $input = $this->input->post();
        $from_date = $this->formatDate($input['from_date']);
        $to_date = null;

        if ($input['to_date'] != "" && strtotime($input['to_date'])>=strtotime($input['from_date'])) {
            $to_date = $this->formatDate($input['to_date']);
        }else{
            $to_date = $from_date;
        }
        
        $status = (int) $this->calenderevents_model->add_eventDetails($id, $from_date, $to_date);
        $string = "Event/Holiday added successfully .";
        if($id != null) {
            $string = "Event/Holiday updated successfully .";
        }
        if($status){
            $this->db->trans_commit();
            $this->session->set_flashdata('flashSuccess', $string);
        } else {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Sometrhing went wrong!');
        }
        redirect('calender_events/calenderevents');
    }

    public function deleteEvent(){
        $id = $_POST['id'];
        $status = $this->calenderevents_model->delete_Event($id);
        if($status){
            $this->db->trans_commit();
            $this->session->set_flashdata('flashSuccess', 'Deleted Successfully');
        } else {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Sometrhing went wrong!');
        }
        echo $status;
    }

    function getallEvents() {
        $result = $this->calenderevents_model->getallEvents();
        // echo"<pre>"; print_r($result);die();
        echo json_encode($result);
    }
}