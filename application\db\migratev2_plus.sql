--
-- Migrations after 9 March 2019 (Post-NPS Agara LIVE)
--

---
--Start of transportation tables
---
CREATE TABLE `tx_attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `journey_id` int(11) NOT NULL,
  `event` varchar(50) NOT NULL COMMENT 'pick_house, pick_school, drop_house, drop_school',
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `stop_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `tx_drivers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `phone_number` varchar(20) NOT NULL,
  `driver_licence` varchar(255) NOT NULL,
  `alternative_number` varchar(20) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0: Inactive 1: Active',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `tx_journeys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `journey_name` varchar(255) NOT NULL,
  `thing_id` int(11) NOT NULL,
  `route_id` int(11) NOT NULL,
  `tentative_start_time` time DEFAULT NULL,
  `tentative_end_time` time DEFAULT NULL,
  `journey_type` varchar(50) NOT NULL,
  `days` text NOT NULL,
  `is_refreshed` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `tx_routes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `route_name` varchar(255) NOT NULL,
  `total_km` decimal(10,2) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `tx_stops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stop_name` varchar(255) NOT NULL,
  `distance_to_stop` decimal(10,2) DEFAULT NULL,
  `tentative_reach_time` time NOT NULL,
  `route_id` int(11) NOT NULL,
  `landmark` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `tx_student_routes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `entity_type` varchar(50) NOT NULL DEFAULT 'Student' COMMENT 'Student, Staff',
  `entity_source_id` int(11) NOT NULL COMMENT 'student_id or staff_id',
  `journey_id` int(11) NOT NULL,
  `stop_id` int(11) NOT NULL,
  `day` varchar(20) NOT NULL,
  `journey_type` varchar(45) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `tx_student_routes_override` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `journey_id` int(11) NOT NULL,
  `stop_id` int(11) NOT NULL,
  `from_date` date NOT NULL,
  `to_date` date NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `journey_type` varchar(45) NOT NULL,
  `type` varchar(45) NOT NULL,
  `day` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `tx_things` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `thing_name` varchar(255) NOT NULL,
  `thing_reg_number` varchar(20) NOT NULL,
  `driver_id` int(11) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `tracking_url` text,
  `refresh_required` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

---
--End of transportation tables
---


CREATE TABLE `feev2_km` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kilometer` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `feev2_stops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `kilometer` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

--
-- Adding has_transport_km to student_year
-- Adding stop to student_year
-- pickpup_mode to student_year
--
ALTER TABLE `student_year` 
ADD COLUMN `has_transport_km` VARCHAR(255) NULL DEFAULT NULL AFTER `promoted_by`,
ADD COLUMN `stop` VARCHAR(255) NULL DEFAULT NULL AFTER `has_transport_km`;
ADD COLUMN `pickup_mode` VARCHAR(255) NULL DEFAULT NULL AFTER `stop`;
ADD COLUMN `after_school_sport` VARCHAR(255) NULL DEFAULT NULL AFTER `pickup_mode`;
ADD COLUMN `after_school_sport_days` INT(11) NULL AFTER `after_school_sport`;

--
-- Adding Receipt Book to feev2lblueprint
--
CREATE TABLE `feev2_receipt_book` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_format` varchar(100) NOT NULL,
  `infix` varchar(100) NOT NULL,
  `digit_count` int(11) NOT NULL,
  `running_number` int(11) NOT NULL,
  `year` varchar(100)  NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


--
-- Adding receipt_bookdi to feev2lblueprint
--
ALTER TABLE `feev2_blueprint` 
ADD COLUMN `receipt_book_id` int(11) NULL DEFAULT NULL;

ALTER TABLE `feev2_blueprint` ADD `receipt_for` VARCHAR(100) NOT NULL AFTER `receipt_book_id`, ADD `enable_sms` TINYINT NOT NULL DEFAULT '0' AFTER `receipt_for`;


ALTER TABLE `attendance_student` ADD `student_admission_id` INT NOT NULL AFTER `student_id`;
ALTER TABLE `student_actions` ADD `student_admission_id` INT NOT NULL AFTER `student_id`;
ALTER TABLE `competition_attendance` ADD `student_admission_id` INT NOT NULL AFTER `student_id`;
ALTER TABLE `attendance_master` ADD COLUMN `acad_year_id` INT(11) NOT NULL DEFAULT 18 AFTER `name`;


--
-- Sales Transaction
--
CREATE TABLE `sales_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_name` varchar(100) NOT NULL,
  `item_type` varchar(100) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `sales_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `receipt_no` varchar(100) NOT NULL,
  `student_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_type` tinyint(4) NOT NULL,
  `final_amount` decimal(10,2) NOT NULL,
  `receipt_date` date NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `variant_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `sales_variant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) NOT NULL,
  `item_name` varchar(100) NOT NULL,
  `item_type` varchar(100) NOT NULL,
  `var_option` varchar(20) NOT NULL COMMENT 'stop_id or class_id or sizes ',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `sales_variant_qty` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `variant_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


---
-- Inventory tables changes
---
CREATE TABLE `inventory_invoice_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `invoice_no` varchar(255) DEFAULT NULL,
  `bill_no` varchar(255) DEFAULT NULL,
  `invoice_date` date DEFAULT NULL,
  `delivery_note` text,
  `mode_of_payment` varchar(100) DEFAULT NULL,
  `supplier_ref_no` varchar(100) DEFAULT NULL,
  `order_no` varchar(100) DEFAULT NULL,
  `order_date` date DEFAULT NULL,
  `dispatch_doc_no` varchar(100) DEFAULT NULL,
  `delivery_note_date` date DEFAULT NULL,
  `total_amount` float DEFAULT NULL,
  `payment_instrument_id` int(11) DEFAULT NULL,
  `dispatched_by` varchar(255) DEFAULT NULL,
  `destination` varchar(255) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_invoice_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_master_id` int(11) NOT NULL,
  `variant_id` int(11) NOT NULL,
  `variant_type` char(50) NOT NULL,
  `description` text,
  `hsn_sac_no` varchar(255) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `unit` varchar(50) DEFAULT NULL,
  `price` float DEFAULT NULL,
  `cgst` decimal(5,2) DEFAULT NULL,
  `sgst` decimal(5,2) DEFAULT NULL,
  `total_amount` float DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_product_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_code` varchar(255) NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `is_stockable` tinyint(1) NOT NULL,
  `picture_url` varchar(255) DEFAULT NULL,
  `description` text,
  `attributes` text,
  `category_id` int(11) NOT NULL,
  `created_on` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `modified_on` int(11) DEFAULT NULL,
  `last_modified_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_product_variant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(256) NOT NULL,
  `attributes` text NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `initial_quantity` int(11) NOT NULL,
  `threshold_quantity` int(11) NOT NULL,
  `current_quantity` int(11) NOT NULL,
  `total_quantity` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_vendor_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_code` varchar(100) NOT NULL,
  `vendor_name` varchar(255) NOT NULL,
  `gst_no` varchar(255) DEFAULT NULL,
  `vendor_email` varchar(100) DEFAULT NULL,
  `vendor_website` varchar(255) DEFAULT NULL,
  `contact_first_name` varchar(255) NOT NULL,
  `contact_last_name` varchar(255) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `contact_number` varchar(20) NOT NULL,
  `customer_service_number` varchar(20) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `on_board` date NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_vendor_address_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `address_type` tinyint(4) NOT NULL,
  `address_line1` varchar(255) NOT NULL,
  `address_line2` varchar(255) NOT NULL,
  `area` varchar(100) NOT NULL,
  `district` varchar(100) NOT NULL,
  `state` varchar(100) NOT NULL,
  `country` varchar(100) NOT NULL,
  `pin_code` varchar(10) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_vendor_observations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `observation` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `rating` int(5) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_payment_instruments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `payment_type` char(20) NOT NULL,
  `bank_name` varchar(255) DEFAULT NULL,
  `account_number` int(11) DEFAULT NULL,
  `ifsc_code` varchar(50) DEFAULT NULL,
  `branch` varchar(100) DEFAULT NULL,
  `cheque_in_favor_of` varchar(255) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_vendor_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `inventory_purchase_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `current_status` varchar(100) NOT NULL COMMENT 'created, approved, not-approved, need-correction',
  `description` text NOT NULL,
  `po_number` varchar(100) NOT NULL,
  `category_id` int(11) NOT NULL,
  `vendor_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;


CREATE TABLE `inventory_purchase_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `description` text NOT NULL,
  `quantity` int(11) NOT NULL,
  `variant_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=latin1;


CREATE TABLE `inventory_purchase_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `status` varchar(20) NOT NULL COMMENT 'created, approved, not-approved, need-correction',
  `status_by` int(11) NOT NULL,
  `remarks` text NOT NULL,
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_product_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_product_variant_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `collected_by` int(11) NOT NULL,
  `variant_id` int(11) NOT NULL,
  `allocated_quantity` int(11) NOT NULL,
  `given_by` int(11) NOT NULL,
  `given_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


---
-- Adding acad year id to School calendar table
---
ALTER TABLE `school_calender` 
ADD COLUMN `acad_year_id` INT(11) NOT NULL DEFAULT 18 AFTER `applicable_to`;

---
-- Competition acad_year_id column added
---

ALTER TABLE `competition_master` 
ADD COLUMN `acad_year_id` INT(11) NOT NULL AFTER `competition_code`;

---
-- ALL EXPENSE TABLE STRUCTURE
---

DROP TABLE IF EXISTS `expense_category`;
DROP TABLE IF EXISTS `expense_master`;
DROP TABLE IF EXISTS `expense`;
DROP TABLE IF EXISTS `expense_reject_comments`;


CREATE TABLE `expense_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100),
  `category_id` int(11),
  `made_on` date NOT NULL,
  `voucher_url` varchar(100),
  `approved_by` int(11),
  `approve_status` int(11),
  `made_by` int(11),
  `amount` int(11),
  `acad_year_id` int(11),
  `vendor_id` int(11),
  `description`  text ,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `expense_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(100),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `expense_reject_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `expense_id` int(11),
  `comments` text,
  `status` int(11),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `additional_income_category` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

  CREATE TABLE `additional_income_master` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NULL,
  `received_on` DATE NULL,
  `voucher_url` VARCHAR(225) NULL,
  `amount` DECIMAL(10,2) NULL,
  `vendor_id` INT NULL,
  `category_id` INT NULL,
  `acad_year_id` INT NOT NULL,
  `description` TEXT NULL,
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `library_books` 
ADD COLUMN `acc_no` VARCHAR(255) NULL DEFAULT NULL AFTER `edition`,
ADD COLUMN `isbn` VARCHAR(255) NULL DEFAULT NULL AFTER `acc_no`,
ADD COLUMN `pages` INT(11) NULL DEFAULT NULL AFTER `isbn`,
ADD COLUMN `call_number` VARCHAR(255) NULL DEFAULT NULL AFTER `pages`,
ADD COLUMN `supplier` VARCHAR(255) NULL DEFAULT NULL AFTER `call_number`,
ADD COLUMN `bill_no_date` VARCHAR(255) NULL DEFAULT NULL AFTER `supplier`;
ADD COLUMN `soft_delete` INT(11) NULL DEFAULT 0 AFTER `bill_no_date`;

CREATE TABLE `staff_attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `staff_attendance_session_id` int(11) NOT NULL,
  `status` varchar(45) NOT NULL DEFAULT 'AB',
  `check_in_time` timestamp NULL DEFAULT NULL,
  `check_out_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `staff_attendance_session` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `day` date NOT NULL,
  `attendance_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `staff_attendance_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `staff_attendance_session_id` int(11) NOT NULL,
  `event_time` timestamp NULL DEFAULT NULL,
  `action` text,
  `comment` text,
  `action_by` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `staff_attendance_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `staff_code` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `staff_attendance_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` int(11) NOT NULL,
  `staff_code` int(11) NOT NULL,
  `punch_time` timestamp NULL DEFAULT NULL,
  `access_type` int(5) NOT NULL,
  `direction` tinyint(4) NOT NULL,
  `timezone_code` int(3) NOT NULL,
  `is_updated` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `asset_master`;
DROP TABLE IF EXISTS `asset_allocation`;


CREATE TABLE `asset_allocation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_tag` VARCHAR(45) NOT NULL,
  `allocating_date` date NOT NULL,
  `allocating_to` int(11) NOT NULL COMMENT 'contains room id or staff id',
  `allocation_type` int(11) DEFAULT NULL COMMENT '2 for Room\n1 for Staff',
  `description` text,
  `asset_id` int(11) DEFAULT NULL,
  `collected_on` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=latin1;

  
CREATE TABLE `asset_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;


CREATE TABLE `asset_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_name` varchar(255) NOT NULL,
  `quantity` int(11) NOT NULL,
  `available_assets` int(11) NOT NULL,
  `allocated_assets` int(11) NOT NULL,
  `asset_cost` decimal(10,2) NOT NULL,
  `total_cost` decimal(10,2) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `description` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=latin1;


CREATE TABLE `asset_tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_id` int(11) DEFAULT NULL,
  `asset_tag` varchar(256) DEFAULT NULL,
  `date_of_addition` date DEFAULT NULL,
  `remarks` text,
  `discarded_date` date DEFAULT NULL,
  `status` int(11) DEFAULT NULL COMMENT '1 for available , 2 for alloted, 3 for discarded and 4 for missing',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=latin1;

ALTER TABLE `school_calender` 
ADD COLUMN `board` INT(3) NOT NULL DEFAULT 1 AFTER `acad_year_id`;

CREATE TABLE `texting_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `sent_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `sent_by` int(11) NOT NULL,
  `reciever` text NOT NULL COMMENT 'Students & Staffs & 1A,1B,5A,6B... & Custom Numbers',
  `source` varchar(100) NOT NULL,
  `visible` tinyint(4) NOT NULL DEFAULT '1',
  `mode` varchar(45) NOT NULL COMMENT 'notification_sms / sms / notification',
  `acad_year_id` int(11) NOT NULL,
  `text_count` int(11) NOT NULL,
  `sms_credits` int(11) NOT NULL,
  `msg_id` varchar(100) DEFAULT NULL,
  `is_unicode` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `text_sent_to` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `texting_master_id` int(11) NOT NULL,
  `stakeholder_id` int(11) NOT NULL,
  `mobile_no` varchar(15) NOT NULL,
  `mode` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1: notification, 2:  sms',
  `status` varchar(100) NOT NULL,
  `response_id` int(11) DEFAULT NULL,
  `avatar_type` int(4) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `texting_credits_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action` varchar(45) NOT NULL,
  `action_by` int(11) NOT NULL,
  `texting_master_id` int(11) DEFAULT NULL,
  `sms_credits` int(11) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `action_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;



--
-- Migrations after 9 March 2019 (Post-NPS Agara LIVE)
--

---
--Start of transportation tables
---
CREATE TABLE `tx_attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `journey_id` int(11) NOT NULL,
  `event` varchar(50) NOT NULL COMMENT 'pick_house, pick_school, drop_house, drop_school',
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `stop_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `tx_drivers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `phone_number` varchar(20) NOT NULL,
  `driver_licence` varchar(255) NOT NULL,
  `alternative_number` varchar(20) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0: Inactive 1: Active',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `tx_journeys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `journey_name` varchar(255) NOT NULL,
  `thing_id` int(11) NOT NULL,
  `route_id` int(11) NOT NULL,
  `tentative_start_time` time DEFAULT NULL,
  `tentative_end_time` time DEFAULT NULL,
  `journey_type` varchar(50) NOT NULL,
  `days` text NOT NULL,
  `is_refreshed` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `tx_routes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `route_name` varchar(255) NOT NULL,
  `total_km` decimal(10,2) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `tx_stops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stop_name` varchar(255) NOT NULL,
  `distance_to_stop` decimal(10,2) DEFAULT NULL,
  `tentative_reach_time` time NOT NULL,
  `route_id` int(11) NOT NULL,
  `landmark` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `tx_student_routes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `entity_type` varchar(50) NOT NULL DEFAULT 'Student' COMMENT 'Student, Staff',
  `entity_source_id` int(11) NOT NULL COMMENT 'student_id or staff_id',
  `journey_id` int(11) NOT NULL,
  `stop_id` int(11) NOT NULL,
  `day` varchar(20) NOT NULL,
  `journey_type` varchar(45) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `tx_student_routes_override` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `journey_id` int(11) NOT NULL,
  `stop_id` int(11) NOT NULL,
  `from_date` date NOT NULL,
  `to_date` date NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `journey_type` varchar(45) NOT NULL,
  `type` varchar(45) NOT NULL,
  `day` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `tx_things` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `thing_name` varchar(255) NOT NULL,
  `thing_reg_number` varchar(20) NOT NULL,
  `driver_id` int(11) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `tracking_url` text,
  `refresh_required` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

---
--End of transportation tables
---


CREATE TABLE `feev2_km` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kilometer` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `feev2_stops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `kilometer` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

--
-- Adding has_transport_km to student_year
-- Adding stop to student_year
--
ALTER TABLE `student_year` 
ADD COLUMN `has_transport_km` VARCHAR(255) NULL DEFAULT NULL AFTER `promoted_by`,
ADD COLUMN `stop` VARCHAR(255) NULL DEFAULT NULL AFTER `has_transport_km`;

--
-- Adding Receipt Book to feev2lblueprint
--
CREATE TABLE `feev2_receipt_book` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_format` varchar(100) NOT NULL,
  `infix` varchar(100) NOT NULL,
  `digit_count` int(11) NOT NULL,
  `running_number` int(11) NOT NULL,
  `year` varchar(100)  NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


--
-- Adding receipt_bookdi to feev2lblueprint
--
ALTER TABLE `feev2_blueprint` 
ADD COLUMN `receipt_book_id` int(11) NULL DEFAULT NULL;

ALTER TABLE `feev2_blueprint` ADD `receipt_for` VARCHAR(100) NOT NULL AFTER `receipt_book_id`, ADD `enable_sms` TINYINT NOT NULL DEFAULT '0' AFTER `receipt_for`;


ALTER TABLE `attendance_student` ADD `student_admission_id` INT NOT NULL AFTER `student_id`;
ALTER TABLE `student_actions` ADD `student_admission_id` INT NOT NULL AFTER `student_id`;
ALTER TABLE `competition_attendance` ADD `student_admission_id` INT NOT NULL AFTER `student_id`;
ALTER TABLE `attendance_master` ADD COLUMN `acad_year_id` INT(11) NOT NULL DEFAULT 18 AFTER `name`;


--
-- Sales Transaction
--
CREATE TABLE `sales_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_name` varchar(100) NOT NULL,
  `item_type` varchar(100) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `sales_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `receipt_no` varchar(100) NOT NULL,
  `student_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_type` tinyint(4) NOT NULL,
  `final_amount` decimal(10,2) NOT NULL,
  `receipt_date` date NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `variant_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `sales_variant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) NOT NULL,
  `item_name` varchar(100) NOT NULL,
  `item_type` varchar(100) NOT NULL,
  `var_option` varchar(20) NOT NULL COMMENT 'stop_id or class_id or sizes ',
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `sales_variant_qty` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `variant_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


---
-- Inventory tables changes
---
CREATE TABLE `inventory_invoice_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `invoice_no` varchar(255) DEFAULT NULL,
  `bill_no` varchar(255) DEFAULT NULL,
  `invoice_date` date DEFAULT NULL,
  `delivery_note` text,
  `mode_of_payment` varchar(100) DEFAULT NULL,
  `supplier_ref_no` varchar(100) DEFAULT NULL,
  `order_no` varchar(100) DEFAULT NULL,
  `order_date` date DEFAULT NULL,
  `dispatch_doc_no` varchar(100) DEFAULT NULL,
  `delivery_note_date` date DEFAULT NULL,
  `total_amount` float DEFAULT NULL,
  `payment_instrument_id` int(11) DEFAULT NULL,
  `dispatched_by` varchar(255) DEFAULT NULL,
  `destination` varchar(255) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_invoice_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_master_id` int(11) NOT NULL,
  `variant_id` int(11) NOT NULL,
  `variant_type` char(50) NOT NULL,
  `description` text,
  `hsn_sac_no` varchar(255) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `unit` varchar(50) DEFAULT NULL,
  `price` float DEFAULT NULL,
  `cgst` decimal(5,2) DEFAULT NULL,
  `sgst` decimal(5,2) DEFAULT NULL,
  `total_amount` float DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_product_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_code` varchar(255) NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `is_stockable` tinyint(1) NOT NULL,
  `picture_url` varchar(255) DEFAULT NULL,
  `description` text,
  `attributes` text,
  `category_id` int(11) NOT NULL,
  `created_on` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `modified_on` int(11) DEFAULT NULL,
  `last_modified_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_product_variant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` varchar(256) NOT NULL,
  `attributes` text NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `initial_quantity` int(11) NOT NULL,
  `threshold_quantity` int(11) NOT NULL,
  `current_quantity` int(11) NOT NULL,
  `total_quantity` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_vendor_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_code` varchar(100) NOT NULL,
  `vendor_name` varchar(255) NOT NULL,
  `gst_no` varchar(255) DEFAULT NULL,
  `vendor_email` varchar(100) DEFAULT NULL,
  `vendor_website` varchar(255) DEFAULT NULL,
  `contact_first_name` varchar(255) NOT NULL,
  `contact_last_name` varchar(255) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `contact_number` varchar(20) NOT NULL,
  `customer_service_number` varchar(20) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `on_board` date NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_vendor_address_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `address_type` tinyint(4) NOT NULL,
  `address_line1` varchar(255) NOT NULL,
  `address_line2` varchar(255) NOT NULL,
  `area` varchar(100) NOT NULL,
  `district` varchar(100) NOT NULL,
  `state` varchar(100) NOT NULL,
  `country` varchar(100) NOT NULL,
  `pin_code` varchar(10) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_vendor_observations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `observation` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `rating` int(5) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_payment_instruments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `payment_type` char(20) NOT NULL,
  `bank_name` varchar(255) DEFAULT NULL,
  `account_number` int(11) DEFAULT NULL,
  `ifsc_code` varchar(50) DEFAULT NULL,
  `branch` varchar(100) DEFAULT NULL,
  `cheque_in_favor_of` varchar(255) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_vendor_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vendor_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `inventory_purchase_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `current_status` varchar(100) NOT NULL COMMENT 'created, approved, not-approved, need-correction',
  `description` text NOT NULL,
  `po_number` varchar(100) NOT NULL,
  `category_id` int(11) NOT NULL,
  `vendor_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;


CREATE TABLE `inventory_purchase_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `description` text NOT NULL,
  `quantity` int(11) NOT NULL,
  `variant_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=latin1;


CREATE TABLE `inventory_purchase_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `status` varchar(20) NOT NULL COMMENT 'created, approved, not-approved, need-correction',
  `status_by` int(11) NOT NULL,
  `remarks` text NOT NULL,
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_product_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `inventory_product_variant_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `collected_by` int(11) NOT NULL,
  `variant_id` int(11) NOT NULL,
  `allocated_quantity` int(11) NOT NULL,
  `given_by` int(11) NOT NULL,
  `given_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


---
-- Adding acad year id to School calendar table
---
ALTER TABLE `school_calender` 
ADD COLUMN `acad_year_id` INT(11) NOT NULL DEFAULT 18 AFTER `applicable_to`;

---
-- Competition acad_year_id column added
---

ALTER TABLE `competition_master` 
ADD COLUMN `acad_year_id` INT(11) NOT NULL AFTER `competition_code`;

---
-- ALL EXPENSE TABLE STRUCTURE
---

DROP TABLE IF EXISTS `expense_category`;
DROP TABLE IF EXISTS `expense_master`;
DROP TABLE IF EXISTS `expense`;
DROP TABLE IF EXISTS `expense_reject_comments`;


CREATE TABLE `expense_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100),
  `category_id` int(11),
  `made_on` date NOT NULL,
  `voucher_url` varchar(100),
  `approved_by` int(11),
  `approve_status` int(11),
  `made_by` int(11),
  `amount` int(11),
  `acad_year_id` int(11),
  `vendor_id` int(11),
  `description`  text ,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `expense_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(100),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `expense_reject_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `expense_id` int(11),
  `comments` text,
  `status` int(11),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `additional_income_category` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

  CREATE TABLE `additional_income_master` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NULL,
  `received_on` DATE NULL,
  `voucher_url` VARCHAR(225) NULL,
  `amount` DECIMAL(10,2) NULL,
  `vendor_id` INT NULL,
  `category_id` INT NULL,
  `acad_year_id` INT NOT NULL,
  `description` TEXT NULL,
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `library_books` 
ADD COLUMN `acc_no` VARCHAR(255) NULL DEFAULT NULL AFTER `edition`,
ADD COLUMN `isbn` VARCHAR(255) NULL DEFAULT NULL AFTER `acc_no`,
ADD COLUMN `pages` INT(11) NULL DEFAULT NULL AFTER `isbn`,
ADD COLUMN `call_number` VARCHAR(255) NULL DEFAULT NULL AFTER `pages`,
ADD COLUMN `supplier` VARCHAR(255) NULL DEFAULT NULL AFTER `call_number`,
ADD COLUMN `bill_no_date` VARCHAR(255) NULL DEFAULT NULL AFTER `supplier`;

CREATE TABLE `staff_attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `staff_attendance_session_id` int(11) NOT NULL,
  `status` varchar(45) NOT NULL DEFAULT 'AB',
  `check_in_time` timestamp NULL DEFAULT NULL,
  `check_out_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `staff_attendance_session` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `day` date NOT NULL,
  `attendance_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `staff_attendance_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `staff_attendance_session_id` int(11) NOT NULL,
  `event_time` timestamp NULL DEFAULT NULL,
  `action` text,
  `comment` text,
  `action_by` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `staff_attendance_bio` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `staff_code` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;



DROP TABLE IF EXISTS `asset_master`;
DROP TABLE IF EXISTS `asset_allocation`;


CREATE TABLE `asset_allocation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_tag` VARCHAR(45) NOT NULL,
  `allocating_date` date NOT NULL,
  `allocating_to` int(11) NOT NULL COMMENT 'contains room id or staff id',
  `allocation_type` int(11) DEFAULT NULL COMMENT '2 for Room\n1 for Staff',
  `description` text,
  `asset_id` int(11) DEFAULT NULL,
  `collected_on` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

  
CREATE TABLE `asset_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `asset_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_name` varchar(255) NOT NULL,
  `quantity` int(11) NOT NULL,
  `available_assets` int(11) NOT NULL,
  `allocated_assets` int(11) NOT NULL,
  `asset_cost` decimal(10,2) NOT NULL,
  `total_cost` decimal(10,2) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `description` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `asset_tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_id` int(11) DEFAULT NULL,
  `asset_tag` varchar(256) DEFAULT NULL,
  `date_of_addition` date DEFAULT NULL,
  `remarks` text,
  `discarded_date` date DEFAULT NULL,
  `status` int(11) DEFAULT NULL COMMENT '1 for available , 2 for alloted, 3 for discarded and 4 for missing',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `school_calender` 
ADD COLUMN `board` INT(3) NOT NULL DEFAULT 1 AFTER `acad_year_id`;

CREATE TABLE `new_payroll_advance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `despersement_method` int(11) DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `amount_per_month` int(11) DEFAULT NULL,
  `paid_advance` int(11) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `new_payroll_arrears` (
  `id` int(11) NOT NULL,
  `accumlated_payslip_id` int(11) DEFAULT NULL,
  `paidoff_payslip_id` int(11) DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `new_payroll_bonus` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `schedule_id` int(11) DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `reason` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `new_payroll_financial_year` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `f_year` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `new_payroll_increments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `increment_amount` int(11) DEFAULT NULL,
  `increment_date` date DEFAULT NULL,
  `increment_doc_link` varchar(256) DEFAULT NULL,
  `start_schedule_id` int(11) DEFAULT NULL,
  `old_ctc` int(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `new_payroll_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `aadhar_number` varchar(45) DEFAULT NULL,
  `account_number` varchar(45) DEFAULT NULL,
  `pan_number` varchar(45) DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `doj` date DEFAULT NULL,
  `uan_number` varchar(45) DEFAULT NULL,
  `ifsc_code` varchar(45) DEFAULT NULL,
  `branch_name` varchar(45) DEFAULT NULL,
  `bank_name` varchar(45) DEFAULT NULL,
  `contact_number` varchar(45) DEFAULT NULL,
  `staff_designation` varchar(45) DEFAULT NULL,
  `location` varchar(45) DEFAULT NULL,
  `pf_number` varchar(45) DEFAULT NULL,
  `employee_id` int(11) DEFAULT NULL,
  `esi_number` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `new_payroll_payslip` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `schedule_id` int(11) DEFAULT NULL,
  `no_of_days_present` int(11) DEFAULT NULL,
  `accumlative_gratuity` int(11) DEFAULT NULL,
  `paid_gratuity` int(11) DEFAULT NULL,
  `lop` int(11) DEFAULT NULL,
  `monthly_gross` int(11) DEFAULT NULL,
  `basic` int(11) DEFAULT NULL,
  `da` int(11) DEFAULT NULL,
  `hra` int(11) DEFAULT NULL,
  `special_allowance` int(11) DEFAULT NULL,
  `bonus_amount` int(11) DEFAULT NULL,
  `bonus_id` int(11) DEFAULT NULL,
  `total_earnings` int(11) DEFAULT NULL,
  `professional_tax` int(11) DEFAULT NULL,
  `tds` int(11) DEFAULT NULL,
  `pf_employee_contribution` int(11) DEFAULT NULL,
  `monthly_net` int(11) DEFAULT NULL,
  `standard_deduction` int(11) DEFAULT NULL,
  `hra_deductions` int(11) DEFAULT NULL,
  `total_deductions` int(11) DEFAULT NULL,
  `monthly_net_tax_calculation` int(11) DEFAULT NULL,
  `advance` int(11) DEFAULT NULL,
  `arrears` int(11) DEFAULT NULL,
  `path` text,
  `status` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `new_payroll_pfs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` int(11) DEFAULT NULL,
  `pf_amount_to_be_paid` int(11) DEFAULT NULL,
  `pf_paid` int(11) DEFAULT NULL,
  `payment_reference` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


CREATE TABLE `new_payroll_salary` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `CTC` int(11) DEFAULT NULL,
  `pf_employee_contribution` int(11) DEFAULT NULL,
  `yearly_gross` int(11) DEFAULT NULL,
  `payroll_master_id` int(11) DEFAULT NULL,
  `start_schedule_id` int(11) DEFAULT NULL,
  `end_schedule_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `new_payroll_schedules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `financial_year_id` int(11) DEFAULT NULL,
  `schedule_name` varchar(45) DEFAULT NULL,
  `no_of_days` int(11) DEFAULT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `new_payroll_settings` (
  `payroll_tan` varchar(256) DEFAULT NULL,
  `payroll_gratuity` int(11) DEFAULT NULL,
  `payroll_pf` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `new_payroll_staff_declaration` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `declare_LIC_premium_year` varchar(256) DEFAULT NULL,
  `declare_hra` varchar(256) DEFAULT NULL,
  `declare_vpf` int(11) DEFAULT NULL,
  `declare_fd_five_year_contributaion` int(11) DEFAULT NULL,
  `declare_loan_principle_payment` int(11) DEFAULT NULL,
  `declare_loan_interest_payment` int(11) DEFAULT NULL,
  `declare_ELSS_mutual_fund` int(11) DEFAULT NULL,
  `declare_financial_year` int(11) DEFAULT NULL,
  `actual_LIC_premium_year` varchar(256) DEFAULT NULL,
  `actual_hra` varchar(256) DEFAULT NULL,
  `actual_vpf` int(11) DEFAULT NULL,
  `actual_fd_five_year_contributaion` int(11) DEFAULT NULL,
  `actual_loan_principle_payment` int(11) DEFAULT NULL,
  `actual_loan_interest_payment` int(11) DEFAULT NULL,
  `actual_ELSS_mutual_fund` int(11) DEFAULT NULL,
  `actual_financial_year` int(11) DEFAULT NULL,
  `proof_LIC_premium_year` varchar(256) DEFAULT NULL,
  `proof_hra` varchar(256) DEFAULT NULL,
  `proof_vpf` int(11) DEFAULT NULL,
  `proof_fd_five_year_contributaion` int(11) DEFAULT NULL,
  `proof_loan_principle_payment` int(11) DEFAULT NULL,
  `proof_loan_interest_payment` int(11) DEFAULT NULL,
  `proof_ELSS_mutual_fund` int(11) DEFAULT NULL,
  `proof_financial_year` int(11) DEFAULT NULL,
  `form_16_link` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `new_payroll_tds` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` int(11) DEFAULT NULL,
  `tds_amount_to_be_paid` int(11) DEFAULT NULL,
  `tds_paid` int(11) DEFAULT NULL,
  `payment_reference` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


CREATE TABLE `new_payroll_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;




ALTER TABLE `tx_stops` 
ADD COLUMN `order` INT(5) NULL AFTER `last_modified_by`;

ALTER TABLE `homework` 
ADD COLUMN `acad_year_id` INT(11) NOT NULL AFTER `staff_id`;
CREATE TABLE `tx_journey_stops` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `journey_id` INT(11) NOT NULL,
  `stop_id` INT(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `tx_journeys` 
DROP COLUMN `route_id`,
CHANGE COLUMN `is_refreshed` `refresh_required` TINYINT(4) NOT NULL DEFAULT '0' ;

ALTER TABLE `tx_stops` 
DROP COLUMN `route_id`;

ALTER TABLE `assessments` 
ADD COLUMN `class_id` INT(11) NOT NULL AFTER `acad_year_id`;

ALTER TABLE `assessments_entities_marks_students` 
ADD COLUMN `acad_year_id` INT(11) NOT NULL AFTER `status`;

ALTER TABLE `student_admission` 
CHANGE COLUMN `contact_no` `preferred_contact_no` VARCHAR(100) NULL DEFAULT NULL ;

CREATE TABLE `circularv2_master` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `title` VARCHAR(255) NOT NULL,
  `body` TEXT NOT NULL,
  `category` VARCHAR(255) NOT NULL,
  `sent_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `sent_by` INT(11) NOT NULL,
  `recievers` TEXT NOT NULL COMMENT '\'Student\', \'Staff\', \'1A,1B,1C...\'',
  `mode` VARCHAR(45) NOT NULL,
  `file_path` VARCHAR(255) NULL DEFAULT NULL,
  `acad_year_id` INT(11) NOT NULL,
  `visible` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `circularv2_sent_to` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `circularv2_master_id` INT(11) NOT NULL,
  `stakeholder_id` INT(11) NOT NULL COMMENT 'ParentId / StaffId',
  `email` VARCHAR(255) NULL DEFAULT NULL,
  `avatar_type` INT(3) NOT NULL COMMENT '2: parent, 4: Staff',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

DROP TABLE IF EXISTS  `new_payroll_settings`;

CREATE TABLE `new_payroll_settings` (
  `payroll_tan` varchar(256) DEFAULT NULL,
  `payroll_gratuity` int(11) DEFAULT NULL,
  `payroll_pf` int(11) DEFAULT NULL,
  `da_percentage` decimal(10,2) DEFAULT NULL,
  `hra_percentage` decimal(10,2) DEFAULT NULL,
  `json` text,
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS  `new_payroll_financial_year`;

CREATE TABLE `new_payroll_financial_year` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `f_year` varchar(45) DEFAULT NULL,
  `from_date` date DEFAULT NULL,
  `to_date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=70 DEFAULT CHARSET=latin1;

--- Sales ---

CREATE TABLE `sales_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` varchar(100) NOT NULL,
  `receipt_no` varchar(100) NOT NULL,
  `student_id` int(11) NOT NULL,
  `payment_type` tinyint(4) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `receipt_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `bank_name` varchar(255) DEFAULT NULL,
  `bank_branch` varchar(255) DEFAULT NULL,
  `cheque_dd_number` varchar(100) DEFAULT NULL,
  `recon_status` tinyint(4) DEFAULT NULL,
  `recon_submitted_on` timestamp NULL DEFAULT NULL,
  `cheque_dd_date` date DEFAULT NULL,
  `soft_delete` tinyint(4) NOT NULL,
  `remarks` varchar(255) DEFAULT NULL,
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `sales_receipt_book` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_format` int(11) NOT NULL,
  `prefix` varchar(100) NOT NULL,
  `digit_count` int(11) NOT NULL,
  `running_number` int(11) NOT NULL,
  `year` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `sales_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sales_master_id` int(11) NOT NULL,
  `inventory_product_id` int(11) NOT NULL,
  `inventory_product_variant_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


ALTER TABLE `inventory_product_category` 
ADD COLUMN `receipt_book_id` INT(11) NOT NULL AFTER `category_name`;
ADD COLUMN `receipt_template` LONGTEXT  NULL AFTER `receipt_book_id`;

ALTER TABLE `inventory_product_variant` 
ADD COLUMN `cost_prodcut` DECIMAL(10,2) NOT NULL AFTER `total_quantity`, 
ADD COLUMN `selling_price` DECIMAL(10,2) NOT NULL AFTER `cost_prodcut`;
ADD COLUMN `hsn_sac` varchar(255) NOT NULL AFTER `selling_price`;


CREATE TABLE `texting_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `sent_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `sent_by` int(11) NOT NULL,
  `reciever` text NOT NULL,
  `source` varchar(100) NOT NULL,
  `visible` tinyint(4) NOT NULL DEFAULT '1',
  `mode` varchar(45) NOT NULL COMMENT 'notification_sms / sms / notification',
  `acad_year_id` int(11) NOT NULL,
  `text_count` int(11) NOT NULL,
  `sms_credits` int(11) NOT NULL,
  `msg_id` varchar(100) DEFAULT NULL,
  `is_unicode` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `text_sent_to` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `texting_master_id` int(11) NOT NULL,
  `stakeholder_id` int(11) NOT NULL,
  `mobile_no` varchar(15) NOT NULL,
  `mode` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1: notification, 2:  sms',
  `status` varchar(100) NOT NULL,
  `response_id` int(11) DEFAULT NULL,
  `avatar_type` int(4) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `texting_credits_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action` varchar(45) NOT NULL,
  `action_by` int(11) NOT NULL,
  `texting_master_id` int(11) DEFAULT NULL,
  `sms_credits` int(11) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `action_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


---
-- Adding onlinepayment for parent portal pay the fees
---

ALTER TABLE `feev2_cohort_student` 
ADD COLUMN `online_payment` VARCHAR(100) NULL DEFAULT 'NOT_PUBLISHED' AFTER `fee_collect_status`;


ALTER TABLE `feev2_receipt_book` 
ADD COLUMN `gst_no` VARCHAR(150) NULL AFTER `year`;
ADD COLUMN `pan_no` VARCHAR(150) NULL AFTER `gst_no`;


------------------
------ Adding for parent ticketing feature
------------------
DROP TABLE issue_master;

CREATE TABLE `ticketing_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `issue_type` varchar(100) NOT NULL,
  `student_id` int(11) NOT NULL,
  `student_cs_id` int(11) NOT NULL,
  `escalation_level` tinyint(4) NOT NULL,
  `status` varchar(100) NOT NULL,
  `assigned_to` int(11) NOT NULL,
  `comments` text,
  `category_id` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ticket_number` VARCHAR(45) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `ticketing_category` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `description` TEXT NULL,
  `default_assignee_type` VARCHAR(100) NOT NULL,
  `default_assignee_other_staff_id` INT(11) NULL,
  `escalation_level_1_type` VARCHAR(45) NULL,
  `escalation_level_2_type` VARCHAR(45) NULL,
  `escalation_level_3_type` VARCHAR(45) NULL,
  `escalation_level_4_type` VARCHAR(45) NULL,
  `no_of_escalation_levels` TINYINT(4) NOT NULL,
  `is_default` TINYINT(1) NOT NULL DEFAULT 0
  PRIMARY KEY (`id`));

CREATE TABLE `ticketing_history` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` INT(11) NULL,
  `action_by_staff_id` INT(11) NOT NULL,
  `action` VARCHAR(255) NOT NULL,
  `action_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`));

ALTER TABLE `class` 
ADD COLUMN `coordinator_id` INT(11) NULL DEFAULT NULL,
ADD COLUMN `academic_director_id` INT(11) NULL DEFAULT NULL;


CREATE TABLE `libraries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `library_master` 
ADD COLUMN `libraries` varchar(255) NOT NULL AFTER `display_color`;

ALTER TABLE `library_books` 
ADD COLUMN `libraries` TINYINT(4) NOT NULL AFTER `soft_delete`;

ALTER TABLE `sales_master` 
ADD COLUMN `card_charge_amount` DECIMAL(10,2) NULL AFTER `remarks`;
ADD COLUMN `receipt_pdf_path` varchar(255) NULL AFTER `card_charge_amount`;
ADD COLUMN `html_sales` text NULL AFTER `receipt_pdf_path`;

CREATE TABLE `tx_logs` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `journey_id` INT(11) NOT NULL,
  `journey_data` TINYTEXT NOT NULL,
  `event` TINYTEXT NULL,
  `notifications_sent_to` TEXT NULL,
  `notification_json` TEXT NULL,
  `notification_response` TEXT NULL,
  `event_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `student_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `document_type` varchar(100) NOT NULL,
  `document_url` varchar(100) NOT NULL,
  `document_other` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `student_prev_school` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `year_id` int(11) NOT NULL,
  `school_name` varchar(255) NOT NULL,
  `class` varchar(100) NOT NULL,
  `board` varchar(100) DEFAULT NULL,
  `board_other` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `student_prev_school_marks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sub_name` varchar(100) NOT NULL,
  `grade` varchar(100) NOT NULL,
  `percentage` decimal(10,2) NOT NULL,
  `sps_id` int(11) NOT NULL COMMENT 'std_prev_school_id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `assessment_entity_master` 
ADD COLUMN `evaluation_type` VARCHAR(20) NULL DEFAULT 'marks' AFTER `derived_formula`;

CREATE TABLE `tx_staff_journeys` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `staff_id` INT(11) NOT NULL,
  `journey_id` INT(11) NOT NULL,
  `stop_id` INT(11) NOT NULL,
  `day` VARCHAR(15) NOT NULL,
  `journey_type` VARCHAR(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `new_payroll_payslip` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `schedule_id` int(11) DEFAULT NULL,
  `no_of_days_present` int(11) DEFAULT NULL,
  `accumlative_gratuity` decimal(10,2) DEFAULT NULL,
  `paid_gratuity` decimal(10,2) DEFAULT NULL,
  `lop` decimal(10,2) DEFAULT NULL,
  `monthly_gross` decimal(10,2) DEFAULT NULL,
  `basic` decimal(10,2) DEFAULT NULL,
  `da` decimal(10,2) DEFAULT NULL,
  `hra` decimal(10,2) DEFAULT NULL,
  `bonus_amount` decimal(10,2) DEFAULT NULL,
  `bonus_id` decimal(10,2) DEFAULT NULL,
  `professional_tax` decimal(10,2) DEFAULT NULL,
  `tds` decimal(10,2) DEFAULT NULL,
  `pf_employee_contribution` decimal(10,2) DEFAULT NULL,
  `monthly_net` decimal(10,2) DEFAULT NULL,
  `standard_deduction` decimal(10,2) DEFAULT NULL,
  `hra_deductions` decimal(10,2) DEFAULT NULL,
  `total_deductions` decimal(10,2) DEFAULT NULL,
  `monthly_net_tax_calculation` decimal(10,2) DEFAULT NULL,
  `special_allowance` decimal(10,2) DEFAULT NULL,
  `total_earnings` decimal(10,2) DEFAULT NULL,
  `advance` decimal(10,2) DEFAULT NULL,
  `arrears` decimal(10,2) DEFAULT NULL,
  `esi` decimal(10,2) DEFAULT NULL,
  `path` text,
  `status` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;

CREATE TABLE `new_payroll_salary` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `yearly_gross` decimal(10,2) DEFAULT NULL,
  `payroll_master_id` int(11) DEFAULT NULL,
  `yearly_ctc` decimal(10,2) DEFAULT NULL,
  `pf` decimal(10,2) DEFAULT NULL,
  `monthly_gross` decimal(10,2) NOT NULL,
  `monthly_basic_salary` decimal(10,2) NOT NULL,
  `slab_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;


CREATE TABLE `new_payroll_settings` (
  `slab_name` varchar(100) NOT NULL,
  `gratuity` decimal(10,2) DEFAULT NULL,
  `basic_salary` decimal(10,2) NOT NULL,
  `pf` decimal(10,2) DEFAULT NULL,
  `da` decimal(10,2) DEFAULT NULL,
  `hra` decimal(10,2) DEFAULT NULL,
  `medical_allowance` decimal(10,2) DEFAULT NULL,
  `conveyance` decimal(10,2) DEFAULT NULL,
  `esi` decimal(10,2) DEFAULT NULL,
  `special_allowance` decimal(10,2) DEFAULT NULL,
  `vpf` decimal(10,2) DEFAULT NULL,
  `advance` int(11) DEFAULT NULL,
  `bonus` int(11) DEFAULT NULL,
  `lunch_allowance` decimal(10,2) DEFAULT NULL,
  `uniform_allowance` decimal(10,2) DEFAULT NULL,
  `cleaning_allowance` decimal(10,2) DEFAULT NULL,
  `tds` decimal(10,2) DEFAULT NULL,
  `loan` int(11) DEFAULT NULL,
  `loss_of_pay` int(11) DEFAULT NULL,
  `professional_tax` int(11) DEFAULT NULL,
  `cca` decimal(10,2) DEFAULT NULL,
  `basic_mode` int(11) DEFAULT NULL,
  `arrears` decimal(10,2) DEFAULT NULL,
  `ta` decimal(10,2) DEFAULT NULL,
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `new_payroll_disbursement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` int(11) NOT NULL,
  `status` varchar(100) NOT NULL,
  `description` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `modified_by` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `new_payroll_disbursement_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `disbursement_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `tx_journeys` 
ADD COLUMN `status` TINYINT NOT NULL DEFAULT 1 COMMENT '0: Inactive, 1: Active' AFTER `refresh_required`;

ALTER TABLE `assessments_entities` 
ADD COLUMN `class_average` DECIMAL(10,2) NOT NULL DEFAULT -1 AFTER `total_marks`,
ADD COLUMN `class_highest` DECIMAL(10,2) NULL DEFAULT -1 AFTER `class_average`;

ALTER TABLE `tx_attendance` 
CHANGE COLUMN `student_id` `rfid` VARCHAR(255) NOT NULL ,
CHANGE COLUMN `event` `journey_type` VARCHAR(50) NOT NULL COMMENT 'pick_house, pick_school, drop_house, drop_school' ,
CHANGE COLUMN `time` `thing_id` INT(11) NOT NULL ,
CHANGE COLUMN `stop_id` `latitude` VARCHAR(50) NULL ,
ADD COLUMN `longitude` VARCHAR(50) NULL AFTER `latitude`,
ADD COLUMN `updated_at` VARCHAR(45) NOT NULL DEFAULT 'CURRENT_TIMESTAMP' AFTER `longitude`;

CREATE TABLE `tx_mismatch_journeys` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `actual_journey_id` INT(11) NULL,
  `stored_journey_id` INT(11) NULL,
  `journey_type` VARCHAR(45) NULL,
  `student_id` INT(11) NOT NULL,
  `remarks` VARCHAR(255) NULL,
  `frequency` INT(11) NULL,
  `resolved` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `tx_drivers` 
ADD COLUMN `attender_name` VARCHAR(255) NULL AFTER `status`,
ADD COLUMN `attender_number` VARCHAR(20) NULL AFTER `attender_name`;


---
-- AFL tables start
---

CREATE TABLE `afl_subjects` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `subject_name` VARCHAR(255) NOT NULL,
  `class_id` INT(11) NOT NULL,
  `description` TEXT NULL,
  `is_elective` TINYINT NOT NULL DEFAULT 0,
  `afl_subject_elective_id` INT(11) NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_strands` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `strand_name` VARCHAR(255) NOT NULL,
  `afl_subject_id` INT(11) NOT NULL,
  `description` TEXT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_sub_strands` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `sub_strand_name` TINYTEXT NOT NULL,
  `afl_strand_id` INT(11) NOT NULL,
  `description` TEXT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_topics` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `topic` MEDIUMTEXT NOT NULL,
  `afl_sub_strand_id` INT(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_assessments` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `assessment_name` VARCHAR(255) NOT NULL,
  `class_id` VARCHAR(45) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_assessment_subjects` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `afl_assessment_id` INT(11) NOT NULL,
  `afl_subject_id` INT(11) NOT NULL,
  `afl_topic_ids_json` VARCHAR(255) NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_grading_scale` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `grading_scale_name` VARCHAR(255) NOT NULL,
  `description` TINYTEXT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_grading_scale_values` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `afl_grading_scale_id` INT(11) NOT NULL,
  `range_name` VARCHAR(255) NOT NULL,
  `start_range` VARCHAR(20) NOT NULL,
  `end_range` VARCHAR(20) NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_rubrics` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `rubric_name` VARCHAR(255) NOT NULL,
  `afl_grading_scale_id` INT(11) NULL,
  `description` MEDIUMTEXT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_rubric_perf_parameters` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `perf_parameter_name` TINYTEXT NOT NULL,
  `afl_rubric_id` INT(11) NOT NULL,
  `description` MEDIUMTEXT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_rubric_perf_parameters_grading_scale_desc` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `afl_grading_scale_values_id` INT(11) NOT NULL,
  `afl_rubric_perf_parameters_id` INT(11) NOT NULL,
  `description` MEDIUMTEXT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_assessment_subject_rubrics` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `afl_rubric_id` INT(11) NOT NULL,
  `afl_assessment_subject_id` INT(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_assessment_rubrics` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `afl_rubric_id` INT(11) NOT NULL,
  `afl_assessment_subject_id` INT(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_student_rubrics` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `student_id` INT(11) NOT NULL,
  `afl_assessment_rubric_id` INT(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `afl_student_marks` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `afl_student_rubric_id` INT(11) NOT NULL,
  `afl_rubric_perf_parameter_id` INT(11) NOT NULL,
  `self_marks` DECIMAL(10,2) NULL,
  `evaluated_marks` DECIMAL(10,2) NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `afl_assessments` 
ADD COLUMN `show_self_evaluation` TINYINT NOT NULL DEFAULT 1 AFTER `class_id`;

ALTER TABLE `afl_assessments` 
ADD COLUMN `schedule` VARCHAR(45) NOT NULL AFTER `show_self_evaluation`;

ALTER TABLE `afl_student_rubrics` 
ADD COLUMN `self_marks_status` TINYINT NULL DEFAULT 0 COMMENT '0: Not Added, 1: Partially Added, 2: Completed' AFTER `afl_assessment_rubric_id`,
ADD COLUMN `evaluated_marks_status` TINYINT NULL DEFAULT 0 COMMENT '0: Not Added, 1: Partially Added, 2: Completed' AFTER `self_marks_status`;

CREATE TABLE `afl_performance_pointers` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `pointer_name` MEDIUMTEXT NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `afl_strands` 
ADD COLUMN `status` TINYINT NOT NULL DEFAULT 0 AFTER `description`,
ADD COLUMN `last_status_changed_by` INT(11) NULL AFTER `status`;

ALTER TABLE `afl_assessments` 
ADD COLUMN `afl_grading_scale_id` INT(11) NULL AFTER `schedule`;

ALTER TABLE `afl_rubric_perf_parameters` 
CHANGE COLUMN `perf_parameter_name` `perf_pointer_id` INT(11) NOT NULL,
CHANGE COLUMN `afl_rubric_id` `afl_assessment_subject_id` INT(11) NOT NULL ;

ALTER TABLE `afl_student_rubrics` 
CHANGE COLUMN `afl_assessment_rubric_id` `afl_assessment_subject_id` INT(11) NOT NULL, 
RENAME TO `afl_student_perf_pointers`;

ALTER TABLE `afl_student_marks` 
CHANGE COLUMN `afl_student_rubric_id` `afl_student_perf_id` INT(11) NOT NULL ;

---
-- AFL tables end
---

ALTER TABLE `tx_logs` 
ADD COLUMN `event_type` VARCHAR(50) NULL AFTER `event_time`,
ADD COLUMN `thing_id` INT(11) NULL AFTER `event_type`,
ADD COLUMN `stop_id` INT(11) NULL AFTER `thing_id`,
ADD COLUMN `message` TINYTEXT NULL AFTER `stop_id`;

ALTER TABLE `tx_logs` 
ADD COLUMN `student_id` INT(11) NULL DEFAULT NULL AFTER `message`;

CREATE TABLE `assessment_subject_remarks` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` INT(11) NOT NULL,
  `student_id` INT(11) NOT NULL,
  `subject_id` INT(11) NOT NULL,
  `subject_remarks` TEXT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `assessments` 
ADD COLUMN `enable_subject_remarks` TINYINT NOT NULL DEFAULT 0 AFTER `class_id`;

ALTER TABLE `staff_attendance_logs` 
ADD COLUMN `status` TINYINT NOT NULL DEFAULT 1 AFTER `is_updated`;

CREATE TABLE `assessment_subject_remarks_group` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `group_name` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `assessment_subject_remarks_description` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `remarks_group_id` INT(11) NOT NULL,
  `subject_remarks` TEXT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `assessments` 
ADD COLUMN `remarks_group_id` INT(11) NULL AFTER `enable_subject_remarks`;

ALTER TABLE `class_section` 
ADD COLUMN `assistant_class_teacher_id` INT(11) NULL AFTER `is_placeholder`;

CREATE TABLE `assessment_halltickets` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `assessment_id` INT(11) NOT NULL,
  `template` TEXT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `assessment_subject_remarks_group` 
ADD COLUMN `remarks_for` TINYINT NOT NULL DEFAULT 0 COMMENT '0: Subject Remarks, 1: Reprt Card Remarks' AFTER `group_name`;

CREATE TABLE `texting_groups` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `group_name` VARCHAR(255) NOT NULL,
  `group_json` TEXT NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


ALTER TABLE `admission_forms` 
ADD COLUMN `g_name` VARCHAR(255) NULL AFTER `student_blood_group`, 
ADD COLUMN `g_mobile_no` VARCHAR(255) NULL AFTER `g_name`, 
ADD COLUMN `g_email_id` VARCHAR(255) NULL AFTER `g_mobile_no`, 
ADD COLUMN `g_addr` VARCHAR(255) NULL AFTER `g_email_id`, 
ADD COLUMN `g_area` VARCHAR(255) NULL AFTER `g_addr`, 
ADD COLUMN `g_district` VARCHAR(255) NULL AFTER `g_area`, 
ADD COLUMN `g_state` VARCHAR(255) NULL AFTER `g_district`, 
ADD COLUMN `g_county` VARCHAR(255) NULL AFTER `g_state`, 
ADD COLUMN `g_pincode` VARCHAR(255) NULL AFTER `g_county`, 
ADD COLUMN `g_office_ph` VARCHAR(255) NULL AFTER `g_pincode`;
ADD COLUMN `g_annual_gross_income` VARCHAR(255) NULL AFTER `g_office_ph`, 
ADD COLUMN `g_res_ph` VARCHAR(255) NULL AFTER `g_annual_gross_income`, 
ADD COLUMN `g_position` VARCHAR(255) NULL AFTER `g_res_ph`, 
ADD COLUMN `g_company_name` VARCHAR(255) NULL AFTER `g_position`, 
ADD COLUMN `g_company_addr` VARCHAR(255) NULL AFTER `g_company_name`, 
ADD COLUMN `g_company_area` VARCHAR(255) NULL AFTER `g_company_addr`, 
ADD COLUMN `g_company_district` VARCHAR(255) NULL AFTER `g_company_area`, 
ADD COLUMN `g_company_state` VARCHAR(255) NULL AFTER `g_company_district`, 
ADD COLUMN `g_company_county` VARCHAR(255) NULL AFTER `g_company_state`, 
ADD COLUMN `g_company_pincode` VARCHAR(255) NULL AFTER `g_company_county`;
ADD COLUMN `guardian_mother_tongue_other` VARCHAR(255) NULL AFTER `g_company_pincode`, 
ADD COLUMN `guardian_aadhar` VARCHAR(255) NULL AFTER `guardian_mother_tongue_other`, 
ADD COLUMN `g_profession` VARCHAR(255) NULL AFTER `guardian_aadhar`, 
ADD COLUMN `guardian_mother_tongue` VARCHAR(255) NULL AFTER `g_profession`;

ALTER TABLE `admission_settings` 
ADD COLUMN `show_guardian_details` BOOLEAN NOT NULL DEFAULT FALSE AFTER `application_no_gen`;

CREATE TABLE `enquiry` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_name` varchar(100) DEFAULT NULL,
  `gender` char(4) DEFAULT NULL,
  `student_dob` date DEFAULT NULL,
  `class_name` int(11) DEFAULT NULL,
  `parent_name` varchar(100) NOT NULL,
  `mobile_number` varchar(100) NOT NULL,
  `alternate_mobile_number` varchar(100) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=latin1;


CREATE TABLE `enquiry_follow_up` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `enquiry_id` int(11) NOT NULL,
  `follow_up_by` int(11) NOT NULL,
  `status` varchar(100) NOT NULL,
  `message` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=latin1;

CREATE TABLE `email_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `members_email` varchar(100) DEFAULT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;

ALTER TABLE `enquiry` 
ADD COLUMN `student_current_school` VARCHAR(255) NULL AFTER `academic_year`;

ALTER TABLE `enquiry` ADD `board` VARCHAR(100) NULL AFTER `student_current_school`;


ALTER TABLE `admission_settings` 
ADD COLUMN `address` TEXT NULL AFTER `show_guardian_details`,
ADD COLUMN `receipt_book_id` INT(11) NULL AFTER `address`,
ADD COLUMN `online_payment` TINYINT(1) NOT NULL DEFAULT 0 AFTER `receipt_book_id`,
ADD COLUMN `admission_logo` VARCHAR(255) NULL AFTER `online_payment`,
ADD COLUMN `admission_bg_logo` VARCHAR(255) NULL AFTER `admission_logo`,
CHANGE COLUMN `instructin_file` `instruction_file` TEXT NOT NULL ,
CHANGE COLUMN `documents` `documents` TEXT NULL ,
CHANGE COLUMN `prev_eduction_info` `prev_eduction_info` TEXT NULL ,
CHANGE COLUMN `streams` `streams` TEXT NULL ,
CHANGE COLUMN `guidelines` `guidelines` TEXT NULL ,
CHANGE COLUMN `application_no_gen` `application_no_gen` TEXT NULL ,
CHANGE COLUMN `show_guardian_details` `show_guardian_details` TINYINT(1) NOT NULL DEFAULT '0' ;

ALTER TABLE `admission_settings` 
ADD COLUMN `final_description` TEXT NULL DEFAULT NULL AFTER `admission_bg_logo`,
ADD COLUMN `admission_fee_amount` DECIMAL(10,2) NULL DEFAULT NULL AFTER `final_description`;

ALTER TABLE `admission_forms` 
ADD COLUMN `admission_setting_id` INT(11) NOT NULL

CREATE TABLE `management_report_templates` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `template_name` VARCHAR(255) NOT NULL,
  `from_email` VARCHAR(255) NOT NULL,
  `members` TEXT NULL,
  `email_subject` VARCHAR(255) NOT NULL,
  `email_template` TEXT NULL,
  `interval` TINYINT NOT NULL,
  `execution_time` TIME NULL,
  `last_executed_time` DATETIME NULL,
  `status` TINYINT NULL COMMENT '0:inactive, 1:active',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `assessments_marks_cards` 
ADD COLUMN `pictures` TEXT NULL DEFAULT NULL AFTER `additional_remarks`;

ALTER TABLE `staff_attendance_code` 
CHANGE COLUMN `staff_code` `staff_code` VARCHAR(100) NULL DEFAULT NULL ;

ALTER TABLE `staff_attendance_logs` 
CHANGE COLUMN `staff_code` `staff_code` VARCHAR(100) NOT NULL ;

CREATE TABLE `shifts` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `shift_timings` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `shift_id` INT(11) NOT NULL,
  `weekday` VARCHAR(20) NOT NULL,
  `start_time` TIME NULL,
  `end_time` TIME NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `staff_master` 
ADD COLUMN `shift_id` INT(11) NULL DEFAULT NULL AFTER `identification_code`;

CREATE TABLE `afl_subject_access` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `afl_subject_id` INT(11) NOT NULL,
  `staff_id` INT(11) NOT NULL,
  `section_id` INT(11) NOT NULL,
  `access_level` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `afl_assessment_subjects` 
ADD COLUMN `created_by` INT(11) NOT NULL AFTER `afl_topic_ids_json`,
ADD COLUMN `created_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER `created_by`,
ADD COLUMN `last_modified_by` INT(11) NOT NULL AFTER `created_on`,
ADD COLUMN `last_modified_on` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `last_modified_by`,
ADD COLUMN `publish_status` TINYINT NOT NULL DEFAULT 0 COMMENT '0: not published, 1: published' AFTER `last_modified_on`,
ADD COLUMN `verification_status` TINYINT NOT NULL DEFAULT 0 COMMENT '0: not added, 1: created, 2: verified, 3:need changes' AFTER `publish_status`;

CREATE TABLE `afl_student_marks_history` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `afl_student_marks_id` INT(11) NOT NULL,
  `marks` DECIMAL(10,2) NULL DEFAULT NULL,
  `marks_type` TINYINT NOT NULL DEFAULT 1 COMMENT '1:self marks, 2: facilitator marks',
  `stakeholder_id` INT(11) NOT NULL,
  `avatar_type` INT(4) NOT NULL,
  `status` TINYINT NOT NULL COMMENT '1:saved, 2:locked',
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `gallery_list` 
ADD COLUMN `publish_status` TINYINT NULL DEFAULT 1 AFTER `created_by`;

CREATE TABLE `sms_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(255) DEFAULT NULL,
  `default_template_id` int(11) DEFAULT NULL,
  `deleted` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `sms_template_new` 
CHANGE COLUMN `category` `category_id` INT(11) NOT NULL,
ADD COLUMN `template_content` TEXT NULL AFTER `created_by`, 
ADD COLUMN `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `template_content`;

ALTER TABLE `enquiry` 
ADD COLUMN `got_to_know_by` VARCHAR(255) NULL DEFAULT 'Others' AFTER `admission_form_id`;

ALTER TABLE `enquiry` 
ADD COLUMN `has_sibling` TINYINT NOT NULL DEFAULT 0 AFTER `age`,
ADD COLUMN `where_is_sibling` VARCHAR(255) NULL DEFAULT NULL AFTER `has_sibling`;

ALTER TABLE `sales_master` 
ADD COLUMN `sales_type` VARCHAR(20) NOT NULL DEFAULT 'existing' COMMENT 'existing | new' AFTER `html_sales`,
ADD COLUMN `student_name` VARCHAR(255) NULL DEFAULT NULL AFTER `sales_type`,
ADD COLUMN `parent_name` VARCHAR(255) NULL DEFAULT NULL AFTER `student_name`,
ADD COLUMN `contact_number` VARCHAR(20) NULL DEFAULT NULL AFTER `parent_name`;

ALTER TABLE `follow_up` 
ADD COLUMN `closure_reason` VARCHAR(255) NULL DEFAULT NULL AFTER `registered_email`;

ALTER TABLE `class` 
ADD COLUMN `enquiry_budget` INT(11) NOT NULL DEFAULT 0 AFTER `academic_director_id`;

ALTER TABLE `inventory_product_master` 
ADD COLUMN `unit_type` VARCHAR(100) NULL DEFAULT NULL AFTER `last_modified_by`;

ALTER TABLE `inventory_product_category` 
ADD COLUMN `is_sellable` TINYINT NOT NULL DEFAULT 0 AFTER `receipt_template`;

ALTER TABLE `student_admission` 
ADD COLUMN `preferred_parent` VARCHAR(45) NOT NULL DEFAULT 'Both' AFTER `admission_form_id`;

CREATE TABLE `other_links` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `link_name` VARCHAR(255) NOT NULL,
  `link` TEXT NOT NULL,
  `description` TEXT NULL,
  `is_enabled` TINYINT NOT NULL DEFAULT 1,
  `created_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` INT(11) NULL,
  `modified_on` TIMESTAMP NOT NULL CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `modified_by` INT(11) NULL,
PRIMARY KEY (`id`));

ALTER TABLE `assessments_marks_cards` 
ADD COLUMN `published` TINYINT NOT NULL DEFAULT 1 AFTER `pictures`;

ALTER TABLE `users` 
ADD COLUMN `restore_password` VARCHAR(255) NULL DEFAULT NULL AFTER `loggedin_atleast_once`;

CREATE TABLE `circularv2_categories` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `category_name` VARCHAR(255) NOT NULL,
  `require_approval` TINYINT NOT NULL DEFAULT 0,
PRIMARY KEY (`id`));

ALTER TABLE `circularv2_master` 
ADD COLUMN `is_approved` TINYINT NOT NULL DEFAULT 1 AFTER `visible`;

ALTER TABLE `assessment_marks_card_templates` 
ADD COLUMN `generation_type` TINYINT NOT NULL DEFAULT 0 COMMENT '0: Auto, 1: Manual' AFTER `grading_systems`;

ALTER TABLE `student_year` 
ADD COLUMN `high_quality_picture_url` VARCHAR(255) NULL DEFAULT NULL AFTER `after_school_sport_days`;

CREATE TABLE `branches` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NULL,
  PRIMARY KEY (`id`));

ALTER TABLE `class` 
ADD COLUMN `branch_id` INT(11) NULL DEFAULT NULL AFTER `academic_budget`;

ALTER TABLE `other_links` 
ADD COLUMN `show_to` TINYINT NOT NULL DEFAULT 0 COMMENT '0:parent, 1:staff, 2:both' AFTER `description`;

ALTER TABLE `circularv2_master` 
CHANGE COLUMN `file_path` `file_path` MEDIUMTEXT NULL DEFAULT NULL ;

ALTER TABLE `circularv2_master` 
ADD COLUMN `is_file_path_json` TINYINT NOT NULL DEFAULT 0 COMMENT '0: No, 1: Yes' AFTER `is_approved`;

CREATE TABLE `texting_templates` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `template_name` VARCHAR(255) NULL,
  `template_content` TEXT NULL,
  `created_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` INT(11) NOT NULL,
  `is_approved` TINYINT NOT NULL DEFAULT 0 COMMENT '0: No, 1: Yes',
  `modified_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`));

ALTER TABLE `ticketing_master` 
ADD COLUMN `attachments` MEDIUMTEXT NULL DEFAULT NULL AFTER `ticket_number`;

ALTER TABLE `homework` 
CHANGE COLUMN `image` `image` MEDIUMTEXT NULL DEFAULT NULL ,
ADD COLUMN `is_image_json` TINYINT NOT NULL DEFAULT 0 AFTER `acad_year_id`;

CREATE TABLE `homework_submissions` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `homework_id` INT(11) NOT NULL,
  `student_id` INT(11) NOT NULL,
  `file` VARCHAR(255) NULL DEFAULT NULL,
  `name` VARCHAR(255) NULL DEFAULT NULL,
  `description` MEDIUMTEXT NULL,
  `remarks` MEDIUMTEXT NULL,
  `created_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_checked` TINYINT NOT NULL DEFAULT 0,
  `checked_by` INT(11) NULL DEFAULT NULL COMMENT 'Id of staff who checked the homework',
  PRIMARY KEY (`id`));

ALTER TABLE `homework` 
ADD COLUMN `expect_submissions` TINYINT(4) NOT NULL DEFAULT 0 AFTER `is_image_json`;

CREATE TABLE `virtual_classrooms` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `classroom_code` VARCHAR(255) NULL DEFAULT NULL,
  `name` VARCHAR(255) NULL DEFAULT NULL,
  `is_occupied` TINYINT NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '0: Inactive, 1:Active',
  PRIMARY KEY (`id`));

CREATE TABLE `virtual_slots` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NULL DEFAULT NULL,
  `start_time` TIME NULL DEFAULT NULL,
  `end_time` TIME NULL DEFAULT NULL,
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '0: Inactive, 1:Active',
  PRIMARY KEY (`id`));

CREATE TABLE `virtual_classroom_schedule_slots` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `classroom_code` VARCHAR(255) NOT NULL,
  `schedule_id` INT(11) NOT NULL,
  `slot_id` INT(11) NOT NULL,
  `date` DATE NOT NULL,
  `status` VARCHAR(100) NULL COMMENT 'created, cancelled',
  PRIMARY KEY (`id`));

ALTER TABLE `virtual_schedule` 
ADD COLUMN `status` VARCHAR(45) NOT NULL DEFAULT 'created' COMMENT 'created, cancelled' AFTER `openvidu_token`;

ALTER TABLE `homework` 
ADD COLUMN `submission_date` DATE NULL DEFAULT NULL AFTER `expect_submissions`;

ALTER TABLE `virtual_classrooms` 
CHANGE COLUMN `is_occupied` `occupied_by` VARCHAR(255) NULL DEFAULT NULL;

ALTER TABLE `virtual_schedule` 
DROP COLUMN `openvidu_token`,
CHANGE COLUMN `text_room_id` `openvidu_session_id` VARCHAR(45) NULL DEFAULT NULL ,
CHANGE COLUMN `video_room_id` `virtual_classroom_code` VARCHAR(45) NULL DEFAULT NULL ;

ALTER TABLE `virtual_classroom_schedule_slots` 
CHANGE COLUMN `class_section_ids` `class_section_ids` VARCHAR(255) NULL DEFAULT NULL ;

ALTER TABLE `virtual_student_answers` 
CHANGE COLUMN `student_year_id` `student_id` INT(11) NULL DEFAULT NULL ;

ALTER TABLE `student_admission` 
ADD COLUMN `webcam_avatar` VARCHAR(255) NULL DEFAULT NULL AFTER `language_spoken`;

---
-- Online Classes Table for JITSI
---
CREATE TABLE `online_classrooms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `occupied_by` varchar(255) DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '0: Inactive, 1:Active',
  `student_video_quality` varchar(255) DEFAULT '320x240',
  `student_frame_rate` int(11) DEFAULT 10,
  `staff_video_quality` varchar(255) DEFAULT '640x480',
  `staff_frame_rate` int(11) DEFAULT 10,
  `staff_screen_share_quality` varchar(255) DEFAULT '640x480',
  `staff_screen_frame_rate` int(11) DEFAULT 10,
  `video_to_student` tinyint(4) DEFAULT 0,
  `audio_to_student` tinyint(4) DEFAULT 0,
  `recording` tinyint(4) DEFAULT 1,
  `push_to_talk` tinyint(4) DEFAULT 1,
  `asking_questions` tinyint(4) DEFAULT 0,
  `classmates_video` tinyint(4) DEFAULT 1,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `online_classroom_slots` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0: Inactive, 1:Active',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `online_schedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `description` text,
  `acad_year_id` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `actual_start_time` datetime DEFAULT NULL,
  `actual_end_time` datetime DEFAULT NULL,
  `session_id` varchar(45) DEFAULT NULL,
  `classroom_id` varchar(45) DEFAULT NULL,
  `status` varchar(45) NOT NULL DEFAULT 'created' COMMENT 'created, cancelled',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `online_classroom_schedule_slots` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `classroom_id` INT(11) NOT NULL,
  `schedule_id` int(11) NOT NULL,
  `slot_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `status` varchar(100) DEFAULT NULL COMMENT 'created, cancelled',
  `class_section_ids` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `online_schedule_invitees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` int(11) DEFAULT NULL,
  `type` varchar(45) DEFAULT NULL COMMENT 'staff, student, class_section',
  `stake_holder_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

ALTER TABLE `online_schedule` 
ADD COLUMN `is_online` TINYINT NOT NULL DEFAULT 0 AFTER `status`;

ALTER TABLE `staff_master` 
ADD COLUMN `webcam_avatar` VARCHAR(255) NULL DEFAULT NULL AFTER `shift_id`;

ALTER TABLE `flash_master` 
ADD COLUMN `enforce_reading` TINYINT NOT NULL DEFAULT 0 AFTER `send_to`;

ALTER TABLE `online_classroom_slots` 
ADD COLUMN `classroom_id` INT(11) NULL DEFAULT NULL AFTER `status`;

ALTER TABLE `online_schedule` 
ADD COLUMN `recording` VARCHAR(255) NULL DEFAULT NULL AFTER `is_online`;

ALTER TABLE `online_schedule` 
ADD COLUMN `files` TEXT NULL DEFAULT NULL AFTER `recording`;

ALTER TABLE `online_classrooms` 
ADD COLUMN `max_student_participants` INT(11) NOT NULL DEFAULT 50 AFTER `grid_view`;

ALTER TABLE `circularv2_master` 
ADD COLUMN `sender_list` TEXT NULL DEFAULT NULL AFTER `is_file_path_json`,
ADD COLUMN `sending_status` VARCHAR(20) NOT NULL DEFAULT 'Initiated' AFTER `sender_list`,
ADD COLUMN `texting_master_id` INT(11) NULL DEFAULT NULL AFTER `sending_status`;

ALTER TABLE `texting_master` 
ADD COLUMN `sender_list` TEXT NULL DEFAULT NULL AFTER `is_unicode`,
ADD COLUMN `sending_status` VARCHAR(20) NOT NULL DEFAULT 'Initiated' AFTER `sender_list`;

CREATE TABLE `lp_subjects` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `subject_name` VARCHAR(255) NOT NULL,
  `class_name` VARCHAR(50) NULL,
  PRIMARY KEY (`id`));

ALTER TABLE `lp_tasks` 
ADD COLUMN `lp_sub_topic_id` INT(11) NULL DEFAULT NULL AFTER `download_status`, 
ADD COLUMN `lp_quiz_id` INT(11) NULL DEFAULT NULL AFTER `lp_sub_topic_id`;

CREATE TABLE `lp_lessons` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `lp_subject_id` INT(11) NOT NULL,
  `lesson_name` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `lp_sub_topics` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `lp_lesson_id` INT(11) NOT NULL,
  `sub_topic_name` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `lp_questions` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `lp_sub_topic_id` INT(11) NOT NULL,
  `question` TEXT NOT NULL,
  `last_modified_by` INT(11) NOT NULL,
  `question_type` TINYINT NOT NULL DEFAULT 1 COMMENT '1: Multiple Choice\n2: Fill in the blank\n3: One Liner',
  `options` MEDIUMTEXT NULL DEFAULT NULL,
  `answer` MEDIUMTEXT NULL DEFAULT NULL,
  `points` DOUBLE NULL DEFAULT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `lp_assessment` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `description` MEDIUMTEXT NOT NULL,
  `lp_subject_id` INT(11) NOT NULL,
  `lp_class_name` VARCHAR(45) NOT NULL,
  `total_points` DOUBLE NULL DEFAULT NULL,
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '0: Inactive, 1: Active',
  `created_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` INT(11) NOT NULL,
  `last_modified_by` INT(11) NOT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `lp_assessment_questions` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `lp_question_id` INT(11) NOT NULL,
  `lp_assessment_id` INT(11) NOT NULL,
  PRIMARY KEY (`id`));

ALTER TABLE `lp_tasks_students` 
ADD COLUMN `assessment_status` TINYINT NOT NULL DEFAULT 0 COMMENT '0: Not attended,\n1: Attended' AFTER `evaluation_files`;

CREATE TABLE `lp_assessment_answers` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `lp_task_student_id` INT(11) NOT NULL,
  `lp_question_id` INT(11) NOT NULL,
  `answer` TEXT NULL DEFAULT NULL,
  `status` TINYINT NULL DEFAULT NULL COMMENT '0: Incorrect,\n1: Correct',
  PRIMARY KEY (`id`));

CREATE TABLE `lp_student_rewards` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `student_id` INT(11) NOT NULL,
  `source` VARCHAR(255) NOT NULL COMMENT 'Formative Assessment, Decipline, etc',
  `secured_points` DOUBLE NOT NULL,
  `total_points` DOUBLE NOT NULL,
  `given_by` INT(11) NOT NULL COMMENT 'staff_id',
  `given_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`));

ALTER TABLE `lp_questions` 
ADD COLUMN `explaination` TEXT NULL DEFAULT NULL AFTER `points`;

ALTER TABLE `lp_assessment` 
ADD COLUMN `is_ready` TINYINT NOT NULL DEFAULT 0 AFTER `last_modified_by`;

CREATE TABLE `afl_student_remarks` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `afl_assessment_subject_id` INT(11) NOT NULL,
  `student_id` INT(11) NOT NULL,
  `remarks` TEXT NULL DEFAULT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `email_master` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `subject` VARCHAR(255) NOT NULL,
  `body` TEXT NOT NULL,
  `sent_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `sent_by` INT(11) NOT NULL,
  `source` VARCHAR(255) NULL DEFAULT NULL,
  `from_email` VARCHAR(255) NOT NULL,
  `visible` TINYINT NOT NULL DEFAULT 1,
  `acad_year_id` INT(11) NOT NULL,
  `recievers` MEDIUMTEXT NULL DEFAULT NULL,
  `files` TEXT NULL DEFAULT NULL,
  `sender_list` TEXT NULL DEFAULT NULL,
  `sending_status` VARCHAR(45) NULL DEFAULT 'Initiated',
  `texting_master_id` INT(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `email_sent_to` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `email_master_id` INT(11) NOT NULL,
  `stakeholder_id` INT(11) NOT NULL,
  `avatar_type` INT NOT NULL,
  `email` VARCHAR(255) NULL DEFAULT NULL,
  `status` VARCHAR(45) NULL DEFAULT 'Awaited',
  `is_read` TINYINT(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`));

ALTER TABLE `circularv2_master` 
ADD COLUMN `email_master_id` INT(11) NULL DEFAULT NULL AFTER `texting_master_id`;

ALTER TABLE `afl_student_perf_pointers` 
ADD COLUMN `publish_status` TINYINT NOT NULL DEFAULT 1 COMMENT '0:Not Published, 1: Published' AFTER `evaluated_marks_status`;

ALTER TABLE `online_classrooms` 
ADD COLUMN `host_resolution` VARCHAR(45) NULL DEFAULT NULL AFTER `disable_simulcast`;

ALTER TABLE `student_admission` 
ADD COLUMN temp_deactivation TINYINT(1) NOT NULL DEFAULT 0;

CREATE TABLE `questions` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `question_type` VARCHAR(100) NOT NULL,
  `question` TEXT() NULL,
  `sub_topic_id` INT(11) NOT NULL,
  `status` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '0: unusable, 1: usable',
  `points` DOUBLE NOT NULL DEFAULT 0,
  `created_by` INT(11) NOT NULL,
  `created_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` INT(11) NOT NULL,
  `last_modified_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`));

CREATE TABLE `quiz_template` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `description` MEDIUMTEXT NULL,
  `lp_subject_id` INT(11) NOT NULL,
  `class_name` VARCHAR(45) NOT NULL,
  `total_points` DOUBLE NOT NULL DEFAULT 0,
  `is_ready` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '0: not ready, 1: ready',
  `created_by` INT(11) NOT NULL,
  `created_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` INT(11) NOT NULL,
  `last_modified_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`));

CREATE TABLE `quiz_template_questions` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `quiz_template_id` INT(11) NOT NULL,
  `question_id` INT(11) NOT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `quiz_instance` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `quiz_template_id` INT(11) NOT NULL,
  `source` VARCHAR(100) NOT NULL,
  `source_id` INT(11) NOT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `quiz_answers` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `student_id` INT(11) NOT NULL,
  `quiz_instance_id` INT(11) NOT NULL,
  `question_id` INT(11) NOT NULL,
  `answer` TEXT NULL DEFAULT NULL,
  PRIMARY KEY (`id`));

ALTER TABLE `online_schedule` 
ADD COLUMN `quiz_instance_id` INT(11) NULL DEFAULT NULL COMMENT '0:Not Started, 1:In Progress, 2: Completed' AFTER `files`,
ADD COLUMN `quiz_status` TINYINT NULL DEFAULT 0 AFTER `quiz_instance_id`;

ALTER TABLE `online_classrooms` 
ADD COLUMN `zoomfactor` DOUBLE NOT NULL DEFAULT 1.3 AFTER `student_screen_share_frame_rate`;

ALTER TABLE `quiz_answers` 
ADD COLUMN `status` VARCHAR(45) NULL DEFAULT NULL COMMENT '\'In-correct\', \'Correct\' & \'Partially Correct\'' AFTER `answer`;

ALTER TABLE `online_classrooms` 
ADD COLUMN `student_zoomfactor` DOUBLE NOT NULL DEFAULT 1.3 AFTER `zoomfactor`;

ALTER TABLE `questions` 
ADD COLUMN `negative_points` DOUBLE NOT NULL DEFAULT 0 AFTER `last_modified_on`;

ALTER TABLE `quiz_answers` 
ADD COLUMN `points` DOUBLE NOT NULL DEFAULT 0 AFTER `status`;

ALTER TABLE `lp_tasks` 
ADD COLUMN `group_id` INT(11) NULL DEFAULT NULL AFTER `lp_assessment_id`;

ALTER TABLE `lp_tasks_students` 
ADD COLUMN `resubmission_status` TINYINT NOT NULL DEFAULT 0 COMMENT '0: Not Required, 1: Required, 2: Re-submission Done' AFTER `is_late_submission`,
ADD COLUMN `resubmission_comment` TEXT NULL AFTER `resubmission_status`;

ALTER TABLE `lp_tasks_students_submission_files` 
ADD COLUMN `status` TINYINT NOT NULL DEFAULT 1 COMMENT '0: not used, 1: used' AFTER `file_name`;

ALTER TABLE `online_classrooms` ADD COLUMN `enable_chat` TINYINT NOT NULL DEFAULT 0;

ALTER TABLE `lp_tasks` 
ADD COLUMN `version` TINYINT NOT NULL DEFAULT 1 AFTER `group_id`;

ALTER TABLE `lp_tasks_students_submission_files` 
ADD COLUMN `evaluation_id` INT(11) NOT NULL DEFAULT 0 AFTER `status`;

--- View for storing staff permissions
CREATE OR REPLACE VIEW staff_permissions AS
SELECT concat(p.name, '.', ps.name) as privilege, rs.staff_id 
from privileges p 
join privileges_sub ps on ps.privilege_id=p.id 
join roles_privileges_sub rsp on rsp.privilege_sub_id=ps.id 
join roles_staff rs on rs.role_id=rsp.role_id 
join roles r on r.id=rs.role_id 
join staff_master sm on sm.id=rs.staff_id 
where r.active=1 and p.active=1;

ALTER TABLE `staff_master` 
ADD COLUMN `is_reporting_manager` TINYINT NOT NULL DEFAULT 0 AFTER `profile_confirmed_date`,
ADD COLUMN `reporting_manager_id` INT(11) NULL AFTER `is_reporting_manager`;

CREATE TABLE `leave_v2_category` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `short_name` VARCHAR(10) NOT NULL,
  `has_quota` TINYINT(1) NOT NULL DEFAULT 0,
  `can_be_carried` TINYINT(1) NOT NULL DEFAULT 0,
  `created_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` INT(11) NOT NULL,
  `last_modified_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by` INT(11) NOT NULL,
PRIMARY KEY (`id`));

CREATE TABLE `leave_v2_staff_quota` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `staff_id` INT(11) NOT NULL,
  `leave_category_id` INT(11) NOT NULL,
  `start_date` DATE NULL,
  `end_date` DATE NULL,
  `total_quota` DECIMAL(5,2) NULL,
  `used_quota` DECIMAL(5,2) NULL,
PRIMARY KEY (`id`));

CREATE TABLE `st_attendance` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `date` DATE NOT NULL,
  `staff_shift_id` INT(11) NOT NULL,
  `staff_id` INT(11) NOT NULL,
  `first_check_in_time` TIMESTAMP NULL,
  `last_check_out_time` TIMESTAMP NULL,
  `status` VARCHAR(100) NULL,
  `absent_halfday_reason` VARCHAR(100) NULL,
  `is_late` TINYINT NOT NULL DEFAULT 0,
  `shift_start_time` TIMESTAMP NULL,
  `shift_end_time` TIMESTAMP NULL,
  `is_manually_changed` TINYINT(11) NOT NULL DEFAULT 0,
  `source` VARCHAR(50) NULL COMMENT 'biometric-auto, biometric-manual, mobile, laptop, manual',
  `is_approved` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`));

CREATE TABLE `st_attendance_transactions` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `attendance_id` INT(11) NOT NULL,
  `event_type` VARCHAR(100) NOT NULL COMMENT 'check-in/check-out',
  `event_time` TIMESTAMP NULL,
  `latitude` VARCHAR(100) NULL,
  `longitude` VARCHAR(100) NULL,
  `mobile_number` VARCHAR(20) NULL,
  `device_details` VARCHAR(255) NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `st_attendance_history` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `attendance_id` INT(11) NOT NULL,
  `action` VARCHAR(100) NOT NULL COMMENT 'check-in, check-out, status change',
  `action_by` INT(11) NOT NULL,
  `action_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `reason` TEXT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `st_attendance_shifts_master` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `shift_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '1: Working, 0: Not-Working',
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `is_shift_over_midnight` tinyint(4) NOT NULL DEFAULT 0,
  `created_by` int(11) NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_modified_by` int(11) NOT NULL,
  `last_modified_on` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
);

CREATE TABLE `st_attendance_staff_shifts` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `shift_master_id` INT(11) NOT NULL,
  `staff_id` INT(11) NOT NULL,
  `date` DATE NULL,
  `month` VARCHAR(20) NULL,
  `type` TINYINT(4) NOT NULL DEFAULT 0 COMMENT '0: NA, 1: Shift, 2: Week-Off 3: Holiday',
  PRIMARY KEY (`id`));

CREATE TABLE `st_attendance_staff_shift_history` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `staff_shift_id` INT(11) NOT NULL,
  `action` TINYINT(2) NOT NULL DEFAULT 1 COMMENT '1: Created, 2: Modified',
  `action_by` INT(11) NOT NULL,
  `action_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `old_shift` VARCHAR(50) NULL,
  `new_shift` VARCHAR(50) NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `leave_v2_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `leave_category_id` int(11) NOT NULL,
  `request_date` date NOT NULL,
  `from_date` date NOT NULL,
  `to_date` date NOT NULL,
  `noofdays` decimal(10,2) NOT NULL,
  `reason` text NOT NULL,
  `status` tinyint(1) NOT NULL COMMENT '0: Pending, 1: Approved, 2: Auto Approved, 3: Rejected',
  `description` text NOT NULL,
  `created_on` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_modified_by` int(11) NOT NULL,
  `modified_on` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `leave_for` char(10) DEFAULT NULL,
  `leave_filed_by` int(11) NOT NULL,
  `leave_approved_by` int(11) DEFAULT NULL,
  `leave_staffcol` varchar(45) DEFAULT NULL,
  `substitution_added` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `leave_v2_staff_substitute` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leave_id` int(11) NOT NULL,
  `staff_id` int(11) DEFAULT NULL,
  `substitute_date` date NOT NULL,
  `from_period` tinyint(1) DEFAULT NULL,
  `to_period` tinyint(1) DEFAULT NULL,
  `substitution_added` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

CREATE TABLE `leave_v2_year` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `start_date` DATE NULL,
  `end_date` DATE NULL,
  `is_active` TINYINT(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`));

ALTER TABLE `leave_v2_staff_quota` 
DROP COLUMN `end_date`,
DROP COLUMN `start_date`,
ADD COLUMN `quota_carried_to_next_year` DECIMAL(5,2) NOT NULL DEFAULT 0 AFTER `used_quota`,
ADD COLUMN `leave_v2_year_id` INT(11) NOT NULL AFTER `id`;

ALTER TABLE `leave_v2_category` 
ADD COLUMN `leave_type` TINYINT NOT NULL DEFAULT 1 COMMENT '1: Quota assignable, 2: LOP, 3: Comp-Off' AFTER `can_be_carried`
ADD COLUMN `status` TINYINT(1) NOT NULL DEFAULT 1 AFTER `leave_type`;

ALTER TABLE `leave_v2_staff` 
ADD COLUMN `cancel_reason` TEXT NULL AFTER `substitution_added`;

CREATE TABLE `online_v2_schedule` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NULL,
  `description` TEXT NULL,
  `host_id` INT(11) NULL,
  `meeting_link` VARCHAR(255) NULL,
  `schedule_date` DATE NULL,
  `start_time` TIMESTAMP NULL,
  `end_time` VARCHAR(45) NULL,
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '0: Cancelled, 1: Active',
  `class_status` TINYINT NOT NULL DEFAULT 0 COMMENT '0: Not Started, 1: In-Progress, 2: Completed',
  `created_by` INT(11) NOT NULL,
  `created_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP(),
  `last_modified_by` INT(11) NOT NULL,
  `last_modified_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP(),
  PRIMARY KEY (`id`));

CREATE TABLE `online_v2_schedule_invitee` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` INT(11) NOT NULL,
  `stakeholder_id` INT(11) NOT NULL,
  `stakeholder_type` TINYINT NOT NULL COMMENT '0: student, 1: class_section, 2: staff',
  PRIMARY KEY (`id`));

ALTER TABLE `online_v2_schedule` 
ADD COLUMN `subject_name` VARCHAR(255) NULL AFTER `meeting_link`;

DROP VIEW IF EXISTS `staff_permissions`;
CREATE TABLE `staff_permissions` (
  `privilege` varchar(255) NOT NULL,
  `staff_id` int(11) NOT NULL
);


CREATE TABLE `attendance_v2_master` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `date` DATE NOT NULL,
  `class_section_id` INT(11) NOT NULL,
  `type` TINYINT NOT NULL DEFAULT 0 COMMENT '0: subject_wise, 1: session_wise',
  `type_id` INT(11) NOT NULL,
  `taken_by` INT(11) NOT NULL COMMENT 'Staff Id',
  `taken_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by` INT(11) NOT NULL,
  `last_modified_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`));

CREATE TABLE `attendance_v2_student` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `attendance_v2_master_id` INT(11) NOT NULL,
  `student_admission_id` INT(11) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '0: Not Taken, 1: Present, 2: Absent',
  PRIMARY KEY (`id`));

CREATE TABLE `attendance_v2_student_audit` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `attendance_v2_student_id` INT(11) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '0: Not Taken, 1: Present, 2: Absent',
  `staff_id` INT(11) NOT NULL,
  `reason` VARCHAR(255) NULL,
  `created_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`));

CREATE TABLE `class_master` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `class_name` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`));

ALTER TABLE `class` 
ADD COLUMN `class_master_id` INT(11) NULL AFTER `class_name`;


CREATE TABLE `subject_master` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `subject_name` VARCHAR(255) NOT NULL,
  `short_name` VARCHAR(255) NULL,
  `subject_code` VARCHAR(50) NULL,
  `mapping_string` VARCHAR(255) NULL,
  `order` FLOAT NULL,
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '0: In-active, 1: Active',
  PRIMARY KEY (`id`));

CREATE TABLE `elective_master_group` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `elective_master_group_subjects` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `elective_master_group_id` INT(11) NOT NULL,
  `subject_master_id` INT(11) NOT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `elective_student_master` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `elective_master_group_subject_id` INT(11) NOT NULL,
  `student_admission_id` INT(11) NOT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `ex_elective_group` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `mapping_string` VARCHAR(255) NULL,
  `class_id` INT(11) NOT NULL,
  `friendly_name` VARCHAR(255) NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `ex_elective_group_subjects` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `elective_group_id` INT(11) NOT NULL,
  `class_subject_id` INT(11) NOT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE `ex_elective_student` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `elective_group_subject_id` INT(11) NOT NULL,
  `student_admission_id` INT(11) NOT NULL,
  PRIMARY KEY (`id`));

ALTER TABLE lp_tasks add acad_year_id INT(11) NOT NULL DEFAULT 20;

ALTER TABLE `attendance_v2_student` 
ADD COLUMN `duration` VARCHAR(45) NULL AFTER `status`;

ALTER TABLE `attendance_v2_master` 
ADD COLUMN `duration` VARCHAR(45) NULL AFTER `last_modified_on`;

ALTER TABLE `online_v2_schedule_invitee` 
ADD COLUMN `first_click` TIMESTAMP NULL AFTER `stakeholder_type`,
ADD COLUMN `last_click` TIMESTAMP NULL AFTER `first_click`;

ALTER TABLE `online_v2_schedule` 
ADD COLUMN `attendance_status` TINYINT NOT NULL DEFAULT 0 AFTER `last_modified_on`;

ALTER TABLE `attendance_v2_master` 
ADD COLUMN `source_id` INT(11) NULL AFTER `source`;

ALTER TABLE `st_attendance` 
ADD COLUMN `is_checkout_outside_campus` TINYINT(4) NOT NULL DEFAULT 0 AFTER `is_checkin_outside_campus`,
CHANGE COLUMN `is_outside_campus` `is_checkin_outside_campus` TINYINT(4) NOT NULL DEFAULT 0 ;

ALTER TABLE `st_attendance_transactions` 
ADD COLUMN `is_outside_campus` TINYINT NOT NULL DEFAULT 0 AFTER `distance_from_campus`;

ALTER TABLE `ttv2_substitution_input` 
CHANGE COLUMN `created_on` `created_on` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ;

ALTER TABLE `assessments_entities` 
CHANGE COLUMN `portions` `portions` TEXT CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;

ALTER TABLE `assessment_subject_group` 
CHANGE COLUMN `portions` `portions` TEXT CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;

ALTER TABLE `assessment_portions` 
CHANGE COLUMN `portions` `portions` TEXT CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL ;

ALTER TABLE `ttv2_substitution_output` 
ADD COLUMN `old_subject_id` INT(11) NULL AFTER `new_ttv2_staff_period_id`,
ADD COLUMN `old_subject_name` VARCHAR(255) NULL AFTER `old_subject_id`,
ADD COLUMN `ttv2_template_id` INT(11) NULL AFTER `old_subject_name`;

ALTER TABLE `ttv2_substitution_day` 
DROP COLUMN `ttv2_template_id`;

ALTER TABLE `student_admission` 
ADD COLUMN `joined_helium` TINYINT NOT NULL DEFAULT 0 AFTER `temp_deactivation`,
ADD COLUMN `joined_helium_on` DATE NULL DEFAULT NULL AFTER `joined_helium`;

alter table assessments add show_marks_to_parents TINYINT(1) default 0;

ALTER TABLE `batch_registration` 
ADD COLUMN `c_gst` DOUBLE NULL AFTER `batch_offer_id`,
ADD COLUMN `s_gst` DOUBLE NULL AFTER `c_gst`;

ALTER TABLE `course` 
ADD COLUMN `rating` VARCHAR(10) NULL AFTER `market_price`,
ADD COLUMN `rating_description` TEXT NULL AFTER `rating`;

ALTER TABLE `vendor_trainer` 
ADD COLUMN `staff_id` INT(11) NOT NULL AFTER `status`;

ALTER TABLE `vendor` 
ADD COLUMN `school` VARCHAR(45) NOT NULL AFTER `website`;

ALTER TABLE `vendor_trainer` 
ADD COLUMN `school` VARCHAR(45) NOT NULL AFTER `staff_id`;

ALTER TABLE `staff_master` 
ADD COLUMN `joined_helium` TINYINT NOT NULL DEFAULT 0 AFTER `webcam_avatar`,
ADD COLUMN `joined_helium_on` DATE NULL AFTER `joined_helium`;

ALTER TABLE `batch` 
ADD COLUMN `batch_link` VARCHAR(255) NULL AFTER `helium_price`;

ALTER TABLE `batch_session` 
ADD COLUMN `recording_url` VARCHAR(255) NULL AFTER `type`;

CREATE TABLE `batch_session_resource` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `course_resource_id` INT(11) NOT NULL,
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '0: Inactive, 1: Active',
PRIMARY KEY (`id`));

CREATE TABLE `course_resource` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `type` varchar(45) DEFAULT NULL COMMENT 'audio, video, pdf, image',
  `resource_url` varchar(255) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0: Inactive, 1: Active',
  `created_on` timestamp NOT NULL DEFAULT current_timestamp(),
PRIMARY KEY (`id`));

ALTER TABLE `course_resource` 
ADD COLUMN `course_id` INT(11) NOT NULL AFTER `id`;

ALTER TABLE `batch_session_resource` 
ADD COLUMN `batch_session_id` INT(11) NOT NULL AFTER `id`;

ALTER TABLE `semester` 
ADD COLUMN `type` VARCHAR(45) NOT NULL COMMENT 'odd/even' AFTER `promotion_type`;

ALTER TABLE `st_attendance_transactions` 
ADD COLUMN `location_capture_error` VARCHAR(255) NULL AFTER `is_outside_campus`;

ALTER TABLE `course` 
ADD COLUMN `is_free_registration` TINYINT NOT NULL DEFAULT 0 COMMENT '0: No 1: Yes' AFTER `rating_description`;

ALTER TABLE `assessments_entities` 
ADD COLUMN `is_editable` TINYINT(1) NOT NULL DEFAULT 1 AFTER `class_highest`;

ALTER TABLE `lp_subjects` 
ADD COLUMN `semester_id` INT(11) NULL AFTER `is_for_examination`;

ALTER TABLE `student` 
ADD COLUMN `consent_status` TINYINT NOT NULL DEFAULT 0 COMMENT '0: Not Taken, 1: Accepted, 2: Rejected' AFTER `dob`;

CREATE TABLE `notification` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `title` VARCHAR(255) NULL,
  `message` TEXT NULL,
  `mode` TINYINT NOT NULL DEFAULT 0 COMMENT '0: Notification',
  `created_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` INT(11) NULL,
  `sent_to` TEXT NULL,
  PRIMARY KEY (`id`)
);

CREATE TABLE `notification_receiver` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `stakeholder_id` INT(11) NOT NULL,
  `stakeholder_type` TINYINT NOT NULL COMMENT '0: Student, 1: Staff',
  `read_on` TIMESTAMP NULL,
  PRIMARY KEY (`id`)
);

ALTER TABLE `school` 
ADD COLUMN `click_action` VARCHAR(255) NULL AFTER `url`,
ADD COLUMN `push_notification_key` TEXT NULL AFTER `click_action`;

ALTER TABLE `helium`.`course` 
ADD COLUMN `course_type` TINYINT NOT NULL DEFAULT 0 COMMENT '0: In house courses, 1: Advertise courses' AFTER `is_free_registration` 
ADD COLUMN `registration_link` VARCHAR(255) NULL AFTER `course_type`;
