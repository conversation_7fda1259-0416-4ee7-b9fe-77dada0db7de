<?php 
class Payment_controller extends CI_Controller{

	function __construct(){
		parent::__construct();
        $this->load->library('filemanager');
	}

	//callback from payment provider after payment is done
	public function payment_done() {
		// trigger_error("School done called");
		// trigger_error("HELIUM: API response from TraknPay: ");
		// trigger_error(json_encode($_POST));

		//Call the payment callback handler
		$result = $this->_payment_callback_school_handler($_POST);

		// trigger_error("HELIUM: Payment Callback handler called");
		// trigger_error(json_encode($result));

		$data = $result;
	 	if (!isset($_SESSION['loginstatus'])){
		 	// trigger_error('Session data is not set. Setting now!');
		 	$sid = $_POST['udf4'];
	 		$query = $this->db->query("select data from ci_sessions where id = '$sid' ");
			session_decode($query->row()->data);
	 	}

		$this->load->view('helium/payment/payment_done', $data);
	}

	//called by payment_done function with the response data from payment provider
	/*** 
	 * Sample POST input
	 * Array
			(
					[transaction_status] => SUCCESS
					[source_id] => 12
					[display_message] => Transaction Successful!
					[transaction_id] => <id>
					[transaction_date] = <date>
					[response_type] = IMMEDIATE/RECON
			)
	 * 
	 * 
	 */
	public function complete_payment() {
		trigger_error("HELIUM: Response at fee_trans_done");
		trigger_error(json_encode($_POST));

		$this->__handle_immediate_op_response($_POST);
	}

	private function __handle_immediate_op_response($response) {
		// echo '<pre>'; print_r($response); die();

		$data = $response;
		//send this to helium for updating
        $curl_request = [
            CURLOPT_URL => CONFIG_ENV['helium']."Course/payment_completion",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => [
            	"content-type: multipart/form-data;"
            ]
        ];

        $curl = curl_init();
        curl_setopt_array($curl, $curl_request);
        $db_response = curl_exec($curl);
        $db_response = json_decode($db_response);
        // echo "<pre>"; print_r($db_response); die();
        curl_close($curl);
        if (!$db_response->status) {
        	$return_data['transaction_status'] = 'FAILED';
            $return_data['display_message'] = 'Updating payment invoice failed.';
            return $return_data;
        }

        $this->load->model('parent_model');
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['jwt'] = $this->__generate_token($student_id, ['batch_id' => $data['batch_id'], 'course_id' => $data['course_id']]);
        // echo '<pre>'; print_r($data); die();
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/payment/payment_completion';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/payment/payment_completion';
        }else{
          $data['main_content']    = 'helium/payment/payment_completion_desktop';
        }
        $this->load->view('helium/inc/template', $data);
		// $this->load->view('helium/payment/payment_completion', $data);
	}

	private function __generate_token($student_id, $additional=[]) {
		$this->load->model('helium/Learning_model');
        $parent_id = $this->authorization->getAvatarStakeHolderId();
        $student = $this->Learning_model->getStudentInfo($student_id, $parent_id);
        $school = $this->settings->getSetting('school_short_name');
        $student = [
            "id" => $student->student_id,
            "school" => $school
        ];
        if(!empty($additional)) {
            $student = array_merge($student, $additional);
        }
        // echo '<pre>'; print_r($student); die();
        $jwt = $this->generateJWT($student);
        return $jwt;
    }

    private function generateJWT($data) {
      $this->load->library('JWT');
      $secret = "bmsjman09rsasaac1ezin_tx";

      $payload = [
            "user" => $data,
            "iss" => "nextelement"
      ];

      // echo '<pre>';print_r($payload);die();
      $jwt = JWT::encode($payload, $secret, 'HS256');
      return $jwt;
    }

	private function __handle_recon_op_response($response) {
		//NOT NOW
	}


	private function _payment_callback_school_handler($response) {
        // $salt = CONFIG_ENV['ne_salt'];
		$salt = '16426d514b1ca8b7060986a9cfa7dfdcb53951ae';
		//Step 1: Validate no-man-in-the-middle attack.
        // $salt = $source_salt;

        //Capture source data
        $return_data['source'] = $response['udf1'];
        $return_data['source_id'] = $response['udf2'];
        $return_data['source_callback_url'] = $response['udf3'];
        $return_data['other_info'] = json_decode($response['udf5']);
        $isInputGood = $this->__validate_callback_data($response, $salt);
        if (!$isInputGood) {
            //Update the db with hash mismatch status. RED ALERT!!!
            //Log that there is a hash mismatch!!!!!
            $return_data['transaction_status'] = 'FAILED';
            $return_data['display_message'] = 'Something went wrong.';
            return $return_data;
        }

        //Step 2: Update the db with the transacion inputs
        $data = array (
            'hash_match' => 1,
            'order_id' => $response['order_id'],
            'tx_id' => $response['transaction_id'],
            'tx_cardmasked' => $response['cardmasked'], 
            'tx_payment_channel' => $response['payment_channel'],
            'tx_payment_mode' => $response['payment_mode'],
            'tx_response_code' => $response['response_code'],
            'tx_response_message' => $response['response_message'],
            'tx_date_time' => $response['payment_datetime'],
            'status' => 'COMPLETED',
            'api_response' => json_encode($response)
        );
        // $this->CI->db->where('order_id', $response['order_id']);
        // $db_status = $this->CI->db->update('online_payment_master', $data);

        //send this to helium for updating
        $curl_request = [
            CURLOPT_URL => CONFIG_ENV['helium']."Course/update_online_master",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => [
            	"content-type: multipart/form-data;"
            ]
        ];

        $curl = curl_init();
        curl_setopt_array($curl, $curl_request);
        $db_response = curl_exec($curl);
        $db_response = json_decode($db_response);
        // echo "<pre>"; print_r($db_response); die();
        curl_close($curl);
        if (!$db_response->status) {
        	$return_data['transaction_status'] = 'FAILED';
            $return_data['display_message'] = 'Updating payment data failed.';
            return $return_data;
        }

        //Step 3: Parse the transaction response
        $parse_msg = $this->__parse_response_code($response['response_code']);
        if ($parse_msg === 'SUCCESSFUL') {
            $return_data['transaction_status'] = 'SUCCESS';
            $return_data['transaction_id'] = $response['transaction_id'];
            $return_data['transaction_date'] = date('d-m-Y', strtotime($response['payment_datetime']));
            $return_data['transaction_time'] = date('H:i', strtotime($response['payment_datetime']));
            $return_data['display_message'] = 'Transaction Successful!';
            $return_data['tx_response_code'] = $response['response_code'];
            $return_data['response_type'] = 'IMMEDIATE';
        } else {
            $return_data['transaction_status'] = 'FAILED';
            $return_data['transaction_id'] = $response['transaction_id'];
            $return_data['transaction_date'] = date('d-m-Y', strtotime($response['payment_datetime']));
            $return_data['transaction_time'] = date('H:i', strtotime($response['payment_datetime']));
            $return_data['display_message'] = $parse_msg;
            $return_data['response_type'] = 'IMMEDIATE';
            $return_data['tx_response_code'] = $response['response_code'];
        }

        return $return_data;
	}

    private function __hashCalculate($salt,$input){
        /*Sort the array before hashing*/
        ksort($input);

        /*Create a | (pipe) separated string of all the $input values which are available in $hash_columns*/
        $hash_data = $salt;
        foreach ($input as $key => $value) {
            if (isset($value)) {
                if (strlen($value) > 0) {
                    $hash_data .= '|' . trim($value);
                }
            }
        }
        $hash = strtoupper(hash("sha512", $hash_data));
        return $hash;
    }

	private function __validate_callback_data ($input, $salt) {
        $hash = $input['hash'];
        unset($input['hash']);
        $newHash = $this->__hashCalculate($salt, $input);

        return ($hash === $newHash);
    }

    private function __parse_response_code($code) {
        switch ($code){
            case 0: return 'SUCCESSFUL'; break; // Transaction  Success
            case 1000: return 'Transaction Failed.'; break;
            case 1005: return 'Invalid Authentication at Bank.'; break;
            case 1006: return 'Waiting for the response from bank.'; break;
            case 1007: return 'Transaction Failed'; break; 
            case 1008: return 'Transaction Failed.'; break; 
            case 1011: return 'Authorization refused.'; break; 
            case 1012: return 'Invalid Card / Member Name'; break; 
            case 1013: return 'Invalid Expity Date.'; break; 
            case 1014: return 'Transaction Denied.'; break; 
            case 1016: return 'Transaction Denied.'; break; 
            case 1027: return 'Invalid Transaction.'; break; 
            case 1028: return 'Invalid Transaction.'; break; 
            case 1030: return 'Transaction Failed'; break; 
            case 1040: return 'Invalid CVV. Please check and try again.'; break; 
            case 1042: return 'No responce from bank. Please try again'; break;
            case 1043: return 'Tansaction Cancelled';  break; 
            case 1051: return 'Error occured at bank.'; break; 
            case 1052: return 'Something went wrong'; break;
            case 1053: return 'Something went wrong'; break;
            case 1072: return 'Payment declined by bank'; break;
            case 9999: return 'Something went wrong'; break;
            case 997:  return 'Something went wrong'; break; 
            case '-1': return 'Payment initiated'; break;
            default : return 'something went wrong'; break;
        }
    }
}