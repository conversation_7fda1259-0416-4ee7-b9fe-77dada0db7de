DROP TRIGGER IF EXISTS users_audit;
DE<PERSON><PERSON>ITER $$
CREATE TRIGGER users_audit
AFTER UPDATE ON users FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.ip_address, NEW.ip_address, 'ip_address');
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.username, NEW.username, 'username');
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.password, NEW.password, 'password');
    CALL merge_object(@old<PERSON>son, @newJson, OLD.salt, NEW.salt, 'salt');
    CALL merge_object(@oldJson, @newJson, OLD.email, NEW.email, 'email');
    CALL merge_object(@oldJson, @newJson, OLD.activation_code, NEW.activation_code, 'activation_code');
    CALL merge_object(@old<PERSON>son, @newJson, OLD.forgotten_password_code, NEW.forgotten_password_code, 'forgotten_password_code');
    CALL merge_object(@old<PERSON><PERSON>, @new<PERSON>son, OLD.forgotten_password_time, NEW.forgotten_password_time, 'forgotten_password_time');
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.remember_code, NEW.remember_code, 'remember_code');
    CALL merge_object(@oldJson, @newJson, OLD.last_login, NEW.last_login, 'last_login');
    CALL merge_object(@oldJson, @newJson, OLD.active, NEW.active, 'active');
    CALL merge_object(@oldJson, @newJson, OLD.phone_number, NEW.phone_number, 'phone_number');
    CALL merge_object(@oldJson, @newJson, OLD.freeze_username, NEW.freeze_username, 'freeze_username');
    CALL merge_object(@oldJson, @newJson, OLD.donot_show, NEW.donot_show, 'donot_show');
    CALL merge_object(@oldJson, @newJson, OLD.token, NEW.token, 'token');
    CALL merge_object(@oldJson, @newJson, OLD.loggedin_atleast_once, NEW.loggedin_atleast_once, 'loggedin_atleast_once');
    CALL merge_object(@oldJson, @newJson, OLD.restore_password, NEW.restore_password, 'restore_password');
    CALL merge_object(@oldJson, @newJson, OLD.last_accessed_on, NEW.last_accessed_on, 'last_accessed_on');
      
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'users',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        'NA'
    );
    END IF;
END;
$$
DELIMITER ;