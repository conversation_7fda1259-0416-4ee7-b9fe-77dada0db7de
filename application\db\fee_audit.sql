DROP TRIGGER IF EXISTS feev2_adjustment_audit;
DELIMITER $$
CREATE TRIGGER feev2_adjustment_audit
AFTER UPDATE ON feev2_adjustment FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.cohort_student_id, NEW.cohort_student_id, 'cohort_student_id');
    CALL merge_object(@oldJson, @newJson, OLD.adjustment_by, NEW.adjustment_by, 'adjustment_by');
    CALL merge_object(@oldJson, @newJson, OLD.adjustment_remarks, NEW.adjustment_remarks, 'adjustment_remarks');
    CALL merge_object(@oldJson, @newJson, OLD.is_applied, NEW.is_applied, 'is_applied');
    CALL merge_object(@oldJson, @newJson, OLD.transaction_id, NEW.transaction_id, 'transaction_id');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_adjustment',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;


DROP TRIGGER IF EXISTS feev2_blueprint_audit;
DELIMITER $$
CREATE TRIGGER feev2_blueprint_audit
AFTER UPDATE ON feev2_blueprint FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.name, NEW.name, 'name');
    CALL merge_object(@oldJson, @newJson, OLD.filter_blueprint, NEW.filter_blueprint, 'filter_blueprint');
    CALL merge_object(@oldJson, @newJson, OLD.receipt_gen_algo, NEW.receipt_gen_algo, 'receipt_gen_algo');
    CALL merge_object(@oldJson, @newJson, OLD.allowed_payment_modes, NEW.allowed_payment_modes, 'allowed_payment_modes');
    CALL merge_object(@oldJson, @newJson, OLD.concession_mode, NEW.concession_mode, 'concession_mode');
    CALL merge_object(@oldJson, @newJson, OLD.enable_custom_fee, NEW.enable_custom_fee, 'enable_custom_fee');
    CALL merge_object(@oldJson, @newJson, OLD.enable_fee_cohort_check, NEW.enable_fee_cohort_check, 'enable_fee_cohort_check');
    CALL merge_object(@oldJson, @newJson, OLD.acad_year_id, NEW.acad_year_id, 'acad_year_id');
    CALL merge_object(@oldJson, @newJson, OLD.is_acad_fee, NEW.is_acad_fee, 'is_acad_fee');
    CALL merge_object(@oldJson, @newJson, OLD.is_split, NEW.is_split, 'is_split');
    CALL merge_object(@oldJson, @newJson, OLD.display_fields, NEW.display_fields, 'display_fields');
    CALL merge_object(@oldJson, @newJson, OLD.select_filter, NEW.select_filter, 'select_filter');
    CALL merge_object(@oldJson, @newJson, OLD.select_columns, NEW.select_columns, 'select_columns');
    CALL merge_object(@oldJson, @newJson, OLD.description, NEW.description, 'description');
    CALL merge_object(@oldJson, @newJson, OLD.receipt_book_id, NEW.receipt_book_id, 'receipt_book_id');
    CALL merge_object(@oldJson, @newJson, OLD.receipt_for, NEW.receipt_for, 'receipt_for');
    CALL merge_object(@oldJson, @newJson, OLD.enable_sms, NEW.enable_sms, 'enable_sms');
    CALL merge_object(@oldJson, @newJson, OLD.consolidated_receipt_html, NEW.consolidated_receipt_html, 'consolidated_receipt_html');
    CALL merge_object(@oldJson, @newJson, OLD.terms_conditions, NEW.terms_conditions, 'terms_conditions');
    CALL merge_object(@oldJson, @newJson, OLD.branches, NEW.branches, 'branches');
    CALL merge_object(@oldJson, @newJson, OLD.pre_condition_blueprint, NEW.pre_condition_blueprint, 'pre_condition_blueprint');
    CALL merge_object(@oldJson, @newJson, OLD.transport_selection, NEW.transport_selection, 'transport_selection');
    CALL merge_object(@oldJson, @newJson, OLD.message, NEW.message, 'message');
    CALL merge_object(@oldJson, @newJson, OLD.enable_adjustment_amount, NEW.enable_adjustment_amount, 'enable_adjustment_amount');
    CALL merge_object(@oldJson, @newJson, OLD.enable_online_partial_payment, NEW.enable_online_partial_payment, 'enable_online_partial_payment');
    CALL merge_object(@oldJson, @newJson, OLD.tnc_path, NEW.tnc_path, 'tnc_path');
    CALL merge_object(@oldJson, @newJson, OLD.tnc_enabled, NEW.tnc_enabled, 'tnc_enabled');
    CALL merge_object(@oldJson, @newJson, OLD.open_installment_at_start_date_only, NEW.open_installment_at_start_date_only, 'open_installment_at_start_date_only');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_blueprint',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_blueprint_components_audit;
DELIMITER $$
CREATE TRIGGER feev2_blueprint_components_audit
AFTER UPDATE ON feev2_blueprint_components FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.feev2_blueprint_id, NEW.feev2_blueprint_id, 'feev2_blueprint_id');
    CALL merge_object(@oldJson, @newJson, OLD.name, NEW.name, 'name');
    CALL merge_object(@oldJson, @newJson, OLD.vendor_code, NEW.vendor_code, 'vendor_code');
    CALL merge_object(@oldJson, @newJson, OLD.is_concession_eligible, NEW.is_concession_eligible, 'is_concession_eligible');
    CALL merge_object(@oldJson, @newJson, OLD.enable_if_partial, NEW.enable_if_partial, 'enable_if_partial');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_blueprint_components',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_blueprint_installment_types_audit;
DELIMITER $$
CREATE TRIGGER feev2_blueprint_installment_types_audit
AFTER UPDATE ON feev2_blueprint_installment_types FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.feev2_installment_type_id, NEW.feev2_installment_type_id, 'feev2_installment_type_id');
    CALL merge_object(@oldJson, @newJson, OLD.feev2_blueprint_id, NEW.feev2_blueprint_id, 'feev2_blueprint_id');
    CALL merge_object(@oldJson, @newJson, OLD.discount_algo, NEW.discount_algo, 'discount_algo');
    CALL merge_object(@oldJson, @newJson, OLD.discount_amount, NEW.discount_amount, 'discount_amount');
    CALL merge_object(@oldJson, @newJson, OLD.allow_partial, NEW.allow_partial, 'allow_partial');
    CALL merge_object(@oldJson, @newJson, OLD.allocation_algo, NEW.allocation_algo, 'allocation_algo');
    CALL merge_object(@oldJson, @newJson, OLD.allocation_params, NEW.allocation_params, 'allocation_params');
    CALL merge_object(@oldJson, @newJson, OLD.fine_amount_algo, NEW.fine_amount_algo, 'fine_amount_algo');
    CALL merge_object(@oldJson, @newJson, OLD.fine_amount_params, NEW.fine_amount_params, 'fine_amount_params');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_blueprint_installment_types',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;


DROP TRIGGER IF EXISTS feev2_cohort_installment_components_audit;
DELIMITER $$
CREATE TRIGGER feev2_cohort_installment_components_audit
AFTER UPDATE ON feev2_cohort_installment_components FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.feev2_cohort_id, NEW.feev2_cohort_id, 'feev2_cohort_id');
    CALL merge_object(@oldJson, @newJson, OLD.feev2_blueprint_installment_types_id, NEW.feev2_blueprint_installment_types_id, 'feev2_blueprint_installment_types_id');
    CALL merge_object(@oldJson, @newJson, OLD.feev2_installment_id, NEW.feev2_installment_id, 'feev2_installment_id');
    CALL merge_object(@oldJson, @newJson, OLD.feev2_blueprint_component_id, NEW.feev2_blueprint_component_id, 'feev2_blueprint_component_id');
    CALL merge_object(@oldJson, @newJson, OLD.amount, NEW.amount, 'amount');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_cohort_installment_components',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;


DROP TRIGGER IF EXISTS feev2_cohort_student_audit;
DELIMITER $$
CREATE TRIGGER feev2_cohort_student_audit
AFTER UPDATE ON feev2_cohort_student FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.student_id, NEW.student_id, 'student_id');
    CALL merge_object(@oldJson, @newJson, OLD.blueprint_id, NEW.blueprint_id, 'blueprint_id');
    CALL merge_object(@oldJson, @newJson, OLD.feev2_cohort_id, NEW.feev2_cohort_id, 'feev2_cohort_id');
    CALL merge_object(@oldJson, @newJson, OLD.publish_status, NEW.publish_status, 'publish_status');
    CALL merge_object(@oldJson, @newJson, OLD.fee_cohort_status, NEW.fee_cohort_status, 'fee_cohort_status');
    CALL merge_object(@oldJson, @newJson, OLD.fee_collect_status, NEW.fee_collect_status, 'fee_collect_status');
    CALL merge_object(@oldJson, @newJson, OLD.online_payment, NEW.online_payment, 'online_payment');
    CALL merge_object(@oldJson, @newJson, OLD.tnc_status, NEW.tnc_status, 'tnc_status');
    CALL merge_object(@oldJson, @newJson, OLD.tnc_accepted_by, NEW.tnc_accepted_by, 'tnc_accepted_by');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_cohort_student',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_cohorts_audit;
DELIMITER $$
CREATE TRIGGER feev2_cohorts_audit
AFTER UPDATE ON feev2_cohorts FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.filter, NEW.filter, 'filter');
    CALL merge_object(@oldJson, @newJson, OLD.total_fee, NEW.total_fee, 'total_fee');
    CALL merge_object(@oldJson, @newJson, OLD.acad_year_id, NEW.acad_year_id, 'acad_year_id');
    CALL merge_object(@oldJson, @newJson, OLD.blueprint_id, NEW.blueprint_id, 'blueprint_id');
    CALL merge_object(@oldJson, @newJson, OLD.default_ins, NEW.default_ins, 'default_ins');
    CALL merge_object(@oldJson, @newJson, OLD.friendly_name, NEW.friendly_name, 'friendly_name');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_cohorts',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_concessions_audit;
DELIMITER $$
CREATE TRIGGER feev2_concessions_audit
AFTER UPDATE ON feev2_concessions FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.cohort_student_id, NEW.cohort_student_id, 'cohort_student_id');
    CALL merge_object(@oldJson, @newJson, OLD.concession_by, NEW.concession_by, 'concession_by');
    CALL merge_object(@oldJson, @newJson, OLD.is_applied, NEW.is_applied, 'is_applied');
    CALL merge_object(@oldJson, @newJson, OLD.transaction_id, NEW.transaction_id, 'transaction_id');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_concessions',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_concessions_installment_components_audit;
DELIMITER $$
CREATE TRIGGER feev2_concessions_installment_components_audit
AFTER UPDATE ON feev2_concessions_installment_components FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.feev2_concession_id, NEW.feev2_concession_id, 'feev2_concession_id');
    CALL merge_object(@oldJson, @newJson, OLD.feev2_blueprint_installment_types_id, NEW.feev2_blueprint_installment_types_id, 'feev2_blueprint_installment_types_id');
    CALL merge_object(@oldJson, @newJson, OLD.feev2_installments_id, NEW.feev2_installments_id, 'feev2_installments_id');
    CALL merge_object(@oldJson, @newJson, OLD.feev2_blueprint_components_id, NEW.feev2_blueprint_components_id, 'feev2_blueprint_components_id');
    CALL merge_object(@oldJson, @newJson, OLD.amount, NEW.amount, 'amount');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_concessions_installment_components',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_fine_waiver_audit;
DELIMITER $$
CREATE TRIGGER feev2_fine_waiver_audit
AFTER UPDATE ON feev2_fine_waiver FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.cohort_student_id, NEW.cohort_student_id, 'cohort_student_id');
    CALL merge_object(@oldJson, @newJson, OLD.installment_id, NEW.installment_id, 'installment_id');
    CALL merge_object(@oldJson, @newJson, OLD.transaction_id, NEW.transaction_id, 'transaction_id');
    CALL merge_object(@oldJson, @newJson, OLD.remarks, NEW.remarks, 'remarks');
    CALL merge_object(@oldJson, @newJson, OLD.is_applied, NEW.is_applied, 'is_applied');
    CALL merge_object(@oldJson, @newJson, OLD.amount, NEW.amount, 'amount');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_fine_waiver',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_installment_types_audit;
DELIMITER $$
CREATE TRIGGER feev2_installment_types_audit
AFTER UPDATE ON feev2_installment_types FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.name, NEW.name, 'name');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_installment_types',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_installments_audit;
DELIMITER $$
CREATE TRIGGER feev2_installments_audit
AFTER UPDATE ON feev2_installments FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.name, NEW.name, 'name');
    CALL merge_object(@oldJson, @newJson, OLD.end_date, NEW.end_date, 'end_date');
    CALL merge_object(@oldJson, @newJson, OLD.start_date, NEW.start_date, 'start_date');
    CALL merge_object(@oldJson, @newJson, OLD.feev2_installment_type_id, NEW.feev2_installment_type_id, 'feev2_installment_type_id');
    CALL merge_object(@oldJson, @newJson, OLD.installment_order, NEW.installment_order, 'installment_order');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_installments',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_km_audit;
DELIMITER $$
CREATE TRIGGER feev2_km_audit
AFTER UPDATE ON feev2_km FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.kilometer, NEW.kilometer, 'kilometer');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_km',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_receipt_book_audit;
DELIMITER $$
CREATE TRIGGER feev2_receipt_book_audit
AFTER UPDATE ON feev2_receipt_book FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.template_format, NEW.template_format, 'template_format');
    CALL merge_object(@oldJson, @newJson, OLD.infix, NEW.infix, 'infix');
    CALL merge_object(@oldJson, @newJson, OLD.digit_count, NEW.digit_count, 'digit_count');
    CALL merge_object(@oldJson, @newJson, OLD.running_number, NEW.running_number, 'running_number');
    CALL merge_object(@oldJson, @newJson, OLD.year, NEW.year, 'year');
    CALL merge_object(@oldJson, @newJson, OLD.gst_no, NEW.gst_no, 'gst_no');
    CALL merge_object(@oldJson, @newJson, OLD.pan_no, NEW.pan_no, 'pan_no');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_receipt_book',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_receipt_template_audit;
DELIMITER $$
CREATE TRIGGER feev2_receipt_template_audit
AFTER UPDATE ON feev2_receipt_template FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.template, NEW.template, 'template');
    CALL merge_object(@oldJson, @newJson, OLD.blueprint_id, NEW.blueprint_id, 'blueprint_id');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_receipt_template',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_refund_transactions_audit;
DELIMITER $$
CREATE TRIGGER feev2_refund_transactions_audit
AFTER UPDATE ON feev2_refund_transactions FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.trans_id, NEW.trans_id, 'trans_id');
    CALL merge_object(@oldJson, @newJson, OLD.student_id, NEW.student_id, 'student_id');
    CALL merge_object(@oldJson, @newJson, OLD.amount, NEW.amount, 'amount');
    CALL merge_object(@oldJson, @newJson, OLD.blueprint_id, NEW.blueprint_id, 'blueprint_id');
    CALL merge_object(@oldJson, @newJson, OLD.payment_type, NEW.payment_type, 'payment_type');
    CALL merge_object(@oldJson, @newJson, OLD.refund_date, NEW.refund_date, 'refund_date');
    CALL merge_object(@oldJson, @newJson, OLD.bank_name, NEW.bank_name, 'bank_name');
    CALL merge_object(@oldJson, @newJson, OLD.bank_branch, NEW.bank_branch, 'bank_branch');
    CALL merge_object(@oldJson, @newJson, OLD.cheque_or_dd_date, NEW.cheque_or_dd_date, 'cheque_or_dd_date');
    CALL merge_object(@oldJson, @newJson, OLD.remarks, NEW.remarks, 'remarks');
    CALL merge_object(@oldJson, @newJson, OLD.cheque_dd_nb_cc_dd_number, NEW.cheque_dd_nb_cc_dd_number, 'cheque_dd_nb_cc_dd_number');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_refund_transactions',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_stops_audit;
DELIMITER $$
CREATE TRIGGER feev2_stops_audit
AFTER UPDATE ON feev2_stops FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.name, NEW.name, 'name');
    CALL merge_object(@oldJson, @newJson, OLD.kilometer, NEW.kilometer, 'kilometer');
    CALL merge_object(@oldJson, @newJson, OLD.route, NEW.route, 'route');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_stops',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_student_installments_audit;
DELIMITER $$
CREATE TRIGGER feev2_student_installments_audit
AFTER UPDATE ON feev2_student_installments FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.fee_student_schedule_id, NEW.fee_student_schedule_id, 'fee_student_schedule_id');
    CALL merge_object(@oldJson, @newJson, OLD.feev2_installments_id, NEW.feev2_installments_id, 'feev2_installments_id');
    CALL merge_object(@oldJson, @newJson, OLD.installment_amount, NEW.installment_amount, 'installment_amount');
    CALL merge_object(@oldJson, @newJson, OLD.installment_amount_paid, NEW.installment_amount_paid, 'installment_amount_paid');
    CALL merge_object(@oldJson, @newJson, OLD.status, NEW.status, 'status');
    CALL merge_object(@oldJson, @newJson, OLD.total_concession_amount, NEW.total_concession_amount, 'total_concession_amount');
    CALL merge_object(@oldJson, @newJson, OLD.total_concession_amount_paid, NEW.total_concession_amount_paid, 'total_concession_amount_paid');
    CALL merge_object(@oldJson, @newJson, OLD.total_fine_amount_paid, NEW.total_fine_amount_paid, 'total_fine_amount_paid');
    CALL merge_object(@oldJson, @newJson, OLD.refund_amount, NEW.refund_amount, 'refund_amount');
    CALL merge_object(@oldJson, @newJson, OLD.total_fine_amount, NEW.total_fine_amount, 'total_fine_amount');
    CALL merge_object(@oldJson, @newJson, OLD.total_fine_waived, NEW.total_fine_waived, 'total_fine_waived');
    CALL merge_object(@oldJson, @newJson, OLD.total_adjustment_amount, NEW.total_adjustment_amount, 'total_adjustment_amount');
    CALL merge_object(@oldJson, @newJson, OLD.total_adjustment_amount_paid, NEW.total_adjustment_amount_paid, 'total_adjustment_amount_paid');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_student_installments',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_student_installments_components_audit;
DELIMITER $$
CREATE TRIGGER feev2_student_installments_components_audit
AFTER UPDATE ON feev2_student_installments_components FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.fee_student_installment_id, NEW.fee_student_installment_id, 'fee_student_installment_id');
    CALL merge_object(@oldJson, @newJson, OLD.blueprint_component_id, NEW.blueprint_component_id, 'blueprint_component_id');
    CALL merge_object(@oldJson, @newJson, OLD.component_amount, NEW.component_amount, 'component_amount');
    CALL merge_object(@oldJson, @newJson, OLD.component_amount_paid, NEW.component_amount_paid, 'component_amount_paid');
    CALL merge_object(@oldJson, @newJson, OLD.concession_amount, NEW.concession_amount, 'concession_amount');
    CALL merge_object(@oldJson, @newJson, OLD.concession_amount_paid, NEW.concession_amount_paid, 'concession_amount_paid');
    CALL merge_object(@oldJson, @newJson, OLD.refund_amount, NEW.refund_amount, 'refund_amount');
    CALL merge_object(@oldJson, @newJson, OLD.adjustment_amount, NEW.adjustment_amount, 'adjustment_amount');
    CALL merge_object(@oldJson, @newJson, OLD.adjustment_amount_paid, NEW.adjustment_amount_paid, 'adjustment_amount_paid');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_student_installments_components',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_student_schedule_audit;
DELIMITER $$
CREATE TRIGGER feev2_student_schedule_audit
AFTER UPDATE ON feev2_student_schedule FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.feev2_cohort_student_id, NEW.feev2_cohort_student_id, 'feev2_cohort_student_id');
    CALL merge_object(@oldJson, @newJson, OLD.total_fee, NEW.total_fee, 'total_fee');
    CALL merge_object(@oldJson, @newJson, OLD.total_fee_paid, NEW.total_fee_paid, 'total_fee_paid');
    CALL merge_object(@oldJson, @newJson, OLD.payment_status, NEW.payment_status, 'payment_status');
    CALL merge_object(@oldJson, @newJson, OLD.discount, NEW.discount, 'discount');
    CALL merge_object(@oldJson, @newJson, OLD.total_concession_amount, NEW.total_concession_amount, 'total_concession_amount');
    CALL merge_object(@oldJson, @newJson, OLD.total_concession_amount_paid, NEW.total_concession_amount_paid, 'total_concession_amount_paid');
    CALL merge_object(@oldJson, @newJson, OLD.total_fine_amount_paid, NEW.total_fine_amount_paid, 'total_fine_amount_paid');
    CALL merge_object(@oldJson, @newJson, OLD.total_card_charge_amount, NEW.total_card_charge_amount, 'total_card_charge_amount');
    CALL merge_object(@oldJson, @newJson, OLD.carry_over_total_amount, NEW.carry_over_total_amount, 'carry_over_total_amount');
    CALL merge_object(@oldJson, @newJson, OLD.carry_over_concession, NEW.carry_over_concession, 'carry_over_concession');
    CALL merge_object(@oldJson, @newJson, OLD.carry_over_amount_paid, NEW.carry_over_amount_paid, 'carry_over_amount_paid');
    CALL merge_object(@oldJson, @newJson, OLD.carry_over_status, NEW.carry_over_status, 'carry_over_status');
    CALL merge_object(@oldJson, @newJson, OLD.acad_year_id, NEW.acad_year_id, 'acad_year_id');
    CALL merge_object(@oldJson, @newJson, OLD.feev2_blueprint_installment_types_id, NEW.feev2_blueprint_installment_types_id, 'feev2_blueprint_installment_types_id');
    CALL merge_object(@oldJson, @newJson, OLD.consolidated_fee_html, NEW.consolidated_fee_html, 'consolidated_fee_html');
    CALL merge_object(@oldJson, @newJson, OLD.consolidated_fee_pdf_path, NEW.consolidated_fee_pdf_path, 'consolidated_fee_pdf_path');
    CALL merge_object(@oldJson, @newJson, OLD.pdf_status, NEW.pdf_status, 'pdf_status');
    CALL merge_object(@oldJson, @newJson, OLD.refund_amount, NEW.refund_amount, 'refund_amount');
    CALL merge_object(@oldJson, @newJson, OLD.loan_provider_charges, NEW.loan_provider_charges, 'loan_provider_charges');
    CALL merge_object(@oldJson, @newJson, OLD.total_adjustment_amount, NEW.total_adjustment_amount, 'total_adjustment_amount');
    CALL merge_object(@oldJson, @newJson, OLD.total_adjustment_amount_paid, NEW.total_adjustment_amount_paid, 'total_adjustment_amount_paid');
    CALL merge_object(@oldJson, @newJson, OLD.total_fine_amount, NEW.total_fine_amount, 'total_fine_amount');
    CALL merge_object(@oldJson, @newJson, OLD.total_fine_waived, NEW.total_fine_waived, 'total_fine_waived');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_student_schedule',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_transaction_audit;
DELIMITER $$
CREATE TRIGGER feev2_transaction_audit
AFTER UPDATE ON feev2_transaction FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.student_id, NEW.student_id, 'student_id');
    CALL merge_object(@oldJson, @newJson, OLD.fee_student_schedule_id, NEW.fee_student_schedule_id, 'fee_student_schedule_id');
    CALL merge_object(@oldJson, @newJson, OLD.amount_paid, NEW.amount_paid, 'amount_paid');
    CALL merge_object(@oldJson, @newJson, OLD.fine_amount, NEW.fine_amount, 'fine_amount');
    CALL merge_object(@oldJson, @newJson, OLD.discount_amount, NEW.discount_amount, 'discount_amount');
    CALL merge_object(@oldJson, @newJson, OLD.soft_delete, NEW.soft_delete, 'soft_delete');
    CALL merge_object(@oldJson, @newJson, OLD.concession_amount, NEW.concession_amount, 'concession_amount');
    CALL merge_object(@oldJson, @newJson, OLD.transaction_mode, NEW.transaction_mode, 'transaction_mode');
    CALL merge_object(@oldJson, @newJson, OLD.receipt_pdf_link, NEW.receipt_pdf_link, 'receipt_pdf_link');
    CALL merge_object(@oldJson, @newJson, OLD.receipt_number, NEW.receipt_number, 'receipt_number');
    CALL merge_object(@oldJson, @newJson, OLD.card_charge_amount, NEW.card_charge_amount, 'card_charge_amount');
    CALL merge_object(@oldJson, @newJson, OLD.acad_year_id, NEW.acad_year_id, 'acad_year_id');
    CALL merge_object(@oldJson, @newJson, OLD.status, NEW.status, 'status');
    CALL merge_object(@oldJson, @newJson, OLD.op_recon_status, NEW.op_recon_status, 'op_recon_status');
    CALL merge_object(@oldJson, @newJson, OLD.receipt_html, NEW.receipt_html, 'receipt_html');
    CALL merge_object(@oldJson, @newJson, OLD.pdf_status, NEW.pdf_status, 'pdf_status');
    CALL merge_object(@oldJson, @newJson, OLD.refund_amount, NEW.refund_amount, 'refund_amount');
    CALL merge_object(@oldJson, @newJson, OLD.adjustment_amount, NEW.adjustment_amount, 'adjustment_amount');
    CALL merge_object(@oldJson, @newJson, OLD.loan_provider_charges, NEW.loan_provider_charges, 'loan_provider_charges');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_transaction',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_transaction_installment_component_audit;
DELIMITER $$
CREATE TRIGGER feev2_transaction_installment_component_audit
AFTER UPDATE ON feev2_transaction_installment_component FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.fee_transaction_id, NEW.fee_transaction_id, 'fee_transaction_id');
    CALL merge_object(@oldJson, @newJson, OLD.blueprint_component_id, NEW.blueprint_component_id, 'blueprint_component_id');
    CALL merge_object(@oldJson, @newJson, OLD.blueprint_installments_id, NEW.blueprint_installments_id, 'blueprint_installments_id');
    CALL merge_object(@oldJson, @newJson, OLD.amount_paid, NEW.amount_paid, 'amount_paid');
    CALL merge_object(@oldJson, @newJson, OLD.concession_amount, NEW.concession_amount, 'concession_amount');
    CALL merge_object(@oldJson, @newJson, OLD.fee_student_installments_components_id, NEW.fee_student_installments_components_id, 'fee_student_installments_components_id');
    CALL merge_object(@oldJson, @newJson, OLD.fee_student_installments_id, NEW.fee_student_installments_id, 'fee_student_installments_id');
    CALL merge_object(@oldJson, @newJson, OLD.refund_amount, NEW.refund_amount, 'refund_amount');
    CALL merge_object(@oldJson, @newJson, OLD.fine_amount, NEW.fine_amount, 'fine_amount');
    CALL merge_object(@oldJson, @newJson, OLD.adjustment_amount, NEW.adjustment_amount, 'adjustment_amount');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_transaction_installment_component',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;

DROP TRIGGER IF EXISTS feev2_transaction_payment_audit;
DELIMITER $$
CREATE TRIGGER feev2_transaction_payment_audit
AFTER UPDATE ON feev2_transaction_payment FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJson, @newJson, OLD.fee_transaction_id, NEW.fee_transaction_id, 'fee_transaction_id');
    CALL merge_object(@oldJson, @newJson, OLD.payment_type, NEW.payment_type, 'payment_type');
    CALL merge_object(@oldJson, @newJson, OLD.bank_name, NEW.bank_name, 'bank_name');
    CALL merge_object(@oldJson, @newJson, OLD.bank_branch, NEW.bank_branch, 'bank_branch');
    CALL merge_object(@oldJson, @newJson, OLD.cheque_or_dd_date, NEW.cheque_or_dd_date, 'cheque_or_dd_date');
    CALL merge_object(@oldJson, @newJson, OLD.card_reference_number, NEW.card_reference_number, 'card_reference_number');
    CALL merge_object(@oldJson, @newJson, OLD.reconciliation_status, NEW.reconciliation_status, 'reconciliation_status');
    CALL merge_object(@oldJson, @newJson, OLD.remarks, NEW.remarks, 'remarks');
    CALL merge_object(@oldJson, @newJson, OLD.cheque_dd_nb_cc_dd_number, NEW.cheque_dd_nb_cc_dd_number, 'cheque_dd_nb_cc_dd_number');
    CALL merge_object(@oldJson, @newJson, OLD.payment_gateway_info, NEW.payment_gateway_info, 'payment_gateway_info');
    CALL merge_object(@oldJson, @newJson, OLD.canceled_remarks, NEW.canceled_remarks, 'canceled_remarks');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'feev2_transaction_payment',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        ''
    );
    END IF;
END;
$$
DELIMITER ;