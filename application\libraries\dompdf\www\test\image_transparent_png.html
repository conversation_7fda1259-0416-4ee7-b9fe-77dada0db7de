<html>
	
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />
  <title>PNG transparency test</title>
	<style type="text/css">
		body {
			font-size: 11px;
			font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
			padding: 2em;
		}
		
		p {
      background: #C6C6FF;
	  }
		
		img[width="85"] {
			margin: 8px;
		}
		
		@page {
			margin: 0;
		}
	</style>
</head>

<body>

<h1>PNG transparency test</h1>

Examples from <a href="http://entropymine.com/jason/testbed/pngtrans/">http://entropymine.com/jason/testbed/pngtrans/</a>

<p>
Not all possible results are shown; there are too many combinations
of background colors and shapes of the opaque region. However, I
intend to include every result that actually occurs in a mainstream
browser. If I am missing any, please 
<a href="mailto:<EMAIL>">let me know</a>.

</p><p>It's come to my attention that my images which show how
alpha transparency should look are not quite perfect
in regard to precisely how transparent they are at various points.
Rather than try to modify this page to test gamma
correction issues as well, I've created a
<a href="http://entropymine.com/jason/testbed/alphagamma/">separate test page</a> for that.

</p><p>
This test page was constructed by
<a href="http://pobox.com/~jason1/">Jason Summers</a>.
Comments may be emailed to <a href="mailto:<EMAIL>"><EMAIL></a>.<br>
There are
<a href="http://www.libpng.org/pub/png/pngmisc.html#images">other test
pages</a> listed at the PNG web site.

<!-- <p>
There's a
<a href="http://www.petitiononline.com/msiepng/petition.html">petition</a>
(at PetitionOnline) for proper PNG support in IE for Windows. -->

<!-- 
<p>I am aware that MSIE 5.5+ for Windows supports an
"AlphaImageLoader" filter that
<a href="http://webfx.eae.net/dhtml/pngbehavior/pngbehavior.html">can be
used</a> to display PNG images with transparency. Personally, I don't consider
it to
be an acceptable replacement for standard PNG support, though it does mostly
work, with some caveats (requires the site author to modify each web page;
requires scripting to be turned on; requires extra documents to be loaded
from the server; prevents visitors from easily saving the image to disk;
slows down processing of non-transparent images;
seems to fail once in a while for no apparent reason). I don't intend to
hack this page to support it, but maybe I'll put up a separate test page
sometime.
-->

</p>

<h3>Alpha and palette transparency</h3>

<p>Expected result:<br>
<img width="95" height="72" src="./images/png/result_ok.gif" alt="[Test image]">
</p>

<p><b>(T1)</b> 8-bit palette, includes background color:<br>
<img width="85" height="62" src="./images/png/pal_bk.png" alt="[Test image]">
</p>

<p><b>(T2)</b> 8-bit palette, no background color:<br>
<img width="85" height="62" src="./images/png/pal.png" alt="[Test image]">
</p>

<p><b>(T3)</b> 32-bit RGBA, includes background color:<br>
<img width="85" height="62" src="./images/png/rgba8_bk.png" alt="[Test image]">
</p>

<p><b>(T4)</b> 32-bit RGBA, no background color:<br>
<img width="85" height="62" src="./images/png/rgba8.png" alt="[Test image]">
</p>

<p><b>(T5)</b> 64-bit RGBA, includes background color:<br>
<img width="85" height="62" src="./images/png/rgba16_bk.png" alt="[Test image]">
</p>

<p><b>(T6)</b> 64-bit RGBA, no background color:<br>
<img width="85" height="62" src="./images/png/rgba16.png" alt="[Test image]">
</p>

<h3>RGB binary transparency</h3>

<p>Expected result:<br>
<img width="95" height="72" src="./images/png/resultb_ok.gif" alt="[Test image]">
</p>

<p><b>(T7)</b> 24-bit RGB, binary transparency, includes background color:<br>
<img width="85" height="62" src="./images/png/rgb8_t_bk.png" alt="[Test image]">
</p>

<p><b>(T8)</b> 24-bit RGB, binary transparency, no background color:<br>
<img width="85" height="62" src="./images/png/rgb8_t.png" alt="[Test image]">
</p>

<p><b>(T9)</b> 48-bit RGB, binary transparency, includes background color:<br>
<img width="85" height="62" src="./images/png/rgb16_t_bk.png" alt="[Test image]">
</p>

<p><b>(T10)</b> 48-bit RGB, binary transparency, no background color:<br>
<img width="85" height="62" src="./images/png/rgb16_t.png" alt="[Test image]">
</p>


<h3>Grayscale alpha transparency</h3>

<p>Expected result:<br>
<img width="95" height="72" src="./images/png/resultga.gif" alt="[Test image]">
</p>

<p><b>(G1)</b> 16 bpp grayscale (8 gray + 8 alpha), includes background color:<br>
<img width="85" height="62" src="./images/png/gray8a_bk.png" alt="[Test image]">
</p>

<p><b>(G2)</b> 16 bpp grayscale (8 gray + 8 alpha), no background color:<br>
<img width="85" height="62" src="./images/png/gray8a.png" alt="[Test image]">
</p>

<p><b>(G3)</b> 32 bpp grayscale (16 gray + 16 alpha), includes background color:<br>
<img width="85" height="62" src="./images/png/gray16a_bk.png" alt="[Test image]">
</p>

<p><b>(G4)</b> 32 bpp grayscale (16 gray + 16 alpha), no background color:<br>
<img width="85" height="62" src="./images/png/gray16a.png" alt="[Test image]">


<h3>Grayscale binary transparency</h3>

<p>Expected result:<br>
<img width="95" height="72" src="./images/png/resultgb.gif" alt="[Test image]">
</p>

<p><b>(G5)</b> 8 bpp grayscale (8 gray), includes background color:<br>
<img width="85" height="62" src="./images/png/gray8b_bk.png" alt="[Test image]">
</p>

<p><b>(G6)</b> 8 bpp grayscale (8 gray), no background color:<br>
<img width="85" height="62" src="./images/png/gray8b.png" alt="[Test image]">
</p>

<p><b>(G7)</b> 16 bpp grayscale (16 gray), includes background color:<br>
<img width="85" height="62" src="./images/png/gray16b_bk.png" alt="[Test image]">
</p>

<p><b>(G8)</b> 16 bpp grayscale (16 gray), no background color:<br>
<img width="85" height="62" src="./images/png/gray16b.png" alt="[Test image]">

<h3>Miscellaneous</h3>

<p><b>(M1)</b> 8-bit palette, no transparency, includes background color:<br>
Expected result:<br>
<img width="95" height="72" src="./images/png/result_no.gif" alt="[Test image]"><br>
<img width="85" height="62" src="./images/png/pal_bk_notrns.png" alt="[Test image]">
</p>

<p><b>(M2)</b> (4-bit) palette, binary transparency only, no background color:<br>
Expected result:<br>
<img width="95" height="72" src="./images/png/resultb_ok.gif" alt="[Test image]"><br>
<img width="85" height="62" src="./images/png/palb.png" alt="[Test image]"><br>
(This tests a few things that may have slipped through the cracks.)
</p>

</body></html>