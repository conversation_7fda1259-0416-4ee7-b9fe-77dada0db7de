<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Questiontemplate_Controller
 *
 * <AUTHOR>
 */
class Questiontemplate_Controller extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->load->model('student_feedback/Feedback_student');
    }

    public function index() {
        $data['classList'] = $this->Feedback_student->getClassList();
        $data['staffListInfo'] = $this->Feedback_student->staffListData();
        $data['questionList'] = $this->Feedback_student->QuestionInfo();
        $data['main_content'] = 'student_feedback/index';
        $this->load->view('inc/template', $data);
    }

    public function questionsAddPage($transterid) {
        $data['transterid'] = $transterid;
        $data['main_content'] = 'student_feedback/add';
        $this->load->view('inc/template', $data);
    }

    public function staffData() {
        $data['staffNameList'] = $this->Feedback_student->staffList();
        $data['main_content'] = 'student_feedback/addstaff';
        $this->load->view('inc/template', $data);
    }

    public function addQuestions($transterid) {

        $add_Result = $this->Feedback_student->addQuestion();
        if ($add_Result == "1") {
            $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
        } else {

            $this->session->set_flashdata('flashError', 'Failed To Insert....................');
        }

        if ($transterid == '1') {
            redirect('student_feedback/Questiontemplate_Controller/index');
        } else {
            redirect('student_feedback/Desginservey_Controller/designSurveyPage');
        }
    }

    public function deletequestion($id) {
        $del_Result = $this->Feedback_student->deleteQuestion($id);
        if ($del_Result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Deleted.');
        } else {

            $this->session->set_flashdata('flashSuccess', 'Something went wrong .');
        }
        redirect('student_feedback/feedback_controller/index');
    }

}
