<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  24 March 2018
 *
 * Description:  Contains the configuration details for NorthHills.
 *
 * Requirements: PHP5 or above
 *
 */
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| School General Settings
| -------------------------------------------------------------------------
| All the meta data for the school is stored in this section.
*/

/*
| -------------------------------------------------------------------------
| School Meta data
|---------------------------------------------------------------------------
*/

/*
| -------------------------------------------------------------------------
| Admission Number
| Config settings for generating admission number - 
| manual: (TRUE/FALSE) - If false, generate admission number automatically.
| admission_generation_algo - Algorithm to use to generate admission number. Vaues -
| 1) NEXTELEMENT - default
| 2) WPL
| 3) NH
| infix - The infix to use in the algo
| digit-count - ?
| index_offset - The starting number for admissions.
|---------------------------------------------------------------------------
*/
$config['school_name'] = 'Poorna Vikasa Vidyalaya';
$config['school_name_line1'] = 'Poorna Vikasa Vidyalaya';
$config['school_name_line2'] = 'JP Nagar, Banglore - 83';
$config['school_short_name'] = "pvv20";
$config['school_image'] = 'assets/img/pvv_school.jpg';
$config['school_logo'] = 'assets/img/pvv_logo.jpg';
$config['company_logo'] = 'assets/img/nextelement.jpg';
$config['company_name'] = 'NextElement';
$config['login_background'] = 'assets/img/pvv_school.jpg';
$config['school_header'] = 'https://s3.amazonaws.com/nextelement-common/Nps_RNR/nps_header.png';
$config['school_abbreviation'] = 'pvv20';
$config['favicon'] = 'https://s3.amazonaws.com/nextelement-common/scicon16pxeteach.png';
$config['mobile_app_name'] = "pvv20";

$config['admission_number'] = [ 
                                'manual' => TRUE, 
                                'admission_generation_algo' => 'NEXTELEMENT',
                                'infix' => 'NPSRNR',
                                'digit_count' => 5,
                                'index_offset' => 0
                              ];

/*
| -------------------------------------------------------------------------------------
| Timetable
| -------------------------------------------------------------------------------------
| header_class_section_id: Provide the class_section id of the section timetable from which the timetable header should be used.
| -------------------------------------------------------------------------------------
*/
$config['timetable']['header_class_section_id'] = 40;
$config['timetable']['half_day_period'] = 5;
$config['timetable']['last_period'] = 9;
$config['timetable']['consider_4_alloc_p6_for_cs_lab'] = 1;  //This config is required *ONLY* for NPS. This is a hack to support a 4-section allocation for NPS RNR for Computer Sr Lab.

$config['timetable']['timetable_template_for_staff'] = 2;
$config['timetable']['timetable_template_for_room'] = 2;

/*
| --------------------------------------------------------------------------
| Modules
| Define all the modules enabled for the school. The ones that are not here aren't enabled for the school
|
| Basic modules allowed for all schools by default - 
| SCHOOL_ADMIN - Enable School Admin in sidemenu.
| PROFILE - Enable Staff Profile
| ACTIVITY - Enable Activity in sidemenu.
| COMMUNCIATION - Enable Communication in sidemenu.
| PERMISSIONS - Manage roles, assign privileges.
| CLASS_MASTER - Add classes.
| STUDENT_MASTER - CRUD Student data.
| STAFF_MASTER - CRUD Staff data.
| SUBJECTS_MASTER - CRUD Subjects, add and assign elective subjects to students.
| STAFF_LOGIN - Staff console, Staff dashboard.
| 
| The following are optional modules and are enabled based on school requirements -
| REGISTER - Enable Register in sidemenu
| STUDENT_ATTENDANCE - Take student attendance.
| LEAVE_MANAGEMENT: Enable Leave management in sidemenu.
| STUDENT_LEAVE - Students can apply leave.
| STAFF_LEAVE - Staff can apply leave.
| STAFF_ATTENDANCE - Take Staff attendance.
| EXAMINATION - Create and publish Exam timetable and portions, Marks card generation.
| TIMETABLE - Add Staff, Subject and Section association, Construct Timetable.
| SUBSTITUTION - Substitute absent staff
| FEES - Create Fee Structure, Pay Fees.
| LIBRARY - CRUD Books, manage Library transacations.
| INVENTORY - CRUD Inventory, manage inventory transacations.
| PARENTS_LOGIN - Parents console, Parents dashboard.
| ONLINE_EXAM - Teachers create online examination. Students can take the exam under Parent's guidance.
| ASSIGNMENT - Teachers can create assignment. Parents can view assignment.
| PUBLICATIONS - Teachers can create publications for school, class, section and students.
| PTM - To handle Parent-Teachers' meeting.
| PARENT_INITIATIVE - Parents can take an initiative in the school.
| STAFF_INITIATIVE - Staff can take an initiative in the school.
| WORKSHOP - Similar to Events.
| COMPETITION - CRUD Competitions.
| EVENTS - CRUD Events.
| VISITOR - Track visitors.
| STUDENT_EMERGENCY_EXIT - Track students leaving the class in between the day.
| SCHOOL_CALENDAR - Enables entering holiday list, events, competitions, examination calendar, etc.
| TRANSPORT - CRUD Transport.
| SMS - Send SMS to School, Class, Section or Student.
| REPORTS - Access to reports side-menu bar.
| FLASH_NEWS - Access the Flash News Module.
| 
| --------------------------------------------------------------------------
*/
$config['modules'] = 
array( 'SCHOOL_ADMIN', 'STUDENT_MASTER', 'STAFF_MASTER','REPORTS','TIMETABLE','SUBSTITUTION', 'BUILDING_MASTER','STAFF_LOGIN','COMPETITION','VISITOR','REGISTER','COMMUNICATION','ACTIVITY','TASKSTODO','PARENTS_LOGIN','FEES_NH','STUDENT_LEAVE','SCHOOL_CALENDAR','INVENTORY','STAFF_LEAVE','EXAMINATION','ROOM_BOOKING', 'STUDENT_ATTENDANCE','STUDENT_OBSERVATION','STAFF_OBSERVATION','SMS','STAFF_INITIATIVE','PERMISSIONS','CIRCULAR','LIBRARY','ADMISSION','FLASH_NEWS');

/**
 * 
 * Modules that should be shown in Parent module
 * STUDENT_LEAVE: Shows Student Leave
 * ATTENDANCE: Shows Student Attendance
 * ATTENDANCE_SUMMARY: Shows Student Attendance Summary
 * CIRCULARS: Shows Student Circulars
 * SMS: Shows Student Messages
 * ASSESSMENTS: Shows Student Assessments
 * SCHOOL_CALENDAR: Shows Student School Calendar
 * PARENT_INITIATIVE: Show Parent Initiative
 * FEES: Shows Fees
 * PTM: Parents Teachers' meeting
 * LIBRARY: Look up the books borrowed by your ward
 * TIMETABLE: Show Class Timetable
 * MARKS_CARD: Shows Marks Card
 * ISSUE_RAISE: Facility to raise issues by parents
 * MARKS_ANALYSIS: Shows Marks Analysis
 */
$config['parent_modules'] = 
array('ATTENDANCE_SUMMARY', 'CIRCULARS', 'SMS', 'TIMETABLE', 'ASSESSMENTS', 'SCHOOL_CALENDAR', 'PARENT_INITIATIVE', 'MARKS_CARD', 'ASSESSMENT_POSRTIONS_V1');

/*
| --------------------------------------------------------------------------
| Board
| Define all the boards the school supports in this array.
|
| Allowed values are -
| 1 -> 'State', 2->CBSE, 3 -> 'ICSE', 4-> 'IGCSE'
| --------------------------------------------------------------------------
*/
$config['board'] = array(
    '2' => 'CBSE'
  );

/*
| --------------------------------------------------------------------------
| Medium
| Define all the mediums the school supports in this array.
|
| Allowed values are -
| 1 -> English, 2 -> Kannada, 3 -> Hindi
| --------------------------------------------------------------------------
*/
$config['medium'] = array(
  '1' => 'English',
);

/*
| --------------------------------------------------------------------------
| classType
| Define the type of class per the school standards
|
| Values here will just show up when creating a class
| --------------------------------------------------------------------------
*/
$config['classType'] = array(
  '1' => 'Pre-Nursery (PREP-1 TO  PREP-3)',
  '2' => 'High School (1 TO 10)',
);

/*
| --------------------------------------------------------------------------
| Admission Type
| Define all the admission types the school supports in this array.
|
| Allowed values are -
| 1 -> Re-admission, 2 -> New Admission
| --------------------------------------------------------------------------
*/
$config['admission_type'] = array(
  '1'=>'Re-admission',
  '2'=>'New Admission'
);

/*
| --------------------------------------------------------------------------
| Admission Status
| Define all the admission status the school supports in this array.
|
| Allowed values are -
|1 -> Pending, 2 -> Approved, 3 -> Rejected 4 -> Alumni
| --------------------------------------------------------------------------
*/

$config['admission_status'] = array(
  '1'=>'Pending',
  '2'=>'Approved',
  '3'=>'Rejected',
  '4'=>'Alumni'
);

/*
| --------------------------------------------------------------------------
| Staff Selection Status
| Define all the Staff status the school supports in this array.
|
| Allowed values are -
|1 -> Pending, 2 -> Approved, 3 -> Rejected 4 -> Resigned
| --------------------------------------------------------------------------
*/
$config['staff_status'] = array(
  '1'=>'Pending',
  '2'=>'Approved',
  '3'=>'Rejected',
  '4'=>'Resigned',
  '5'=>'Retired'
);

/*
| --------------------------------------------------------------------------
| RTE Options
| Define all the RTE Options the school supports in this array.
|
| Allowed values are -
| 1 -> RTE, 2 -> Non-RTE
| --------------------------------------------------------------------------
*/
$config['rte'] = array(
  '2'=>'Non-RTE',
  '1'=>'RTE'
);

/*
| --------------------------------------------------------------------------
| Boarding
| Define all the boarding types the school supports in this array.
|
| Allowed values are -
| 1-> Day School, 2 -> Residential
| --------------------------------------------------------------------------
*/
$config['boarding'] = array(
  '1'=>'Day School',
);

/*
| --------------------------------------------------------------------------
| Category
| Define all the student categories the school supports in this array.
| 
| Allowed values are -
| '1' => 'General',
| '2' => 'SC/ST',
| '3' => 'CATEGORY IIA',
| '4' => 'CATEGORY IIB',
| '5' => 'CATEGORY IIIA',
| '6' => 'OBC'
| eg: $config['category'] = array('General', 'SC/ST');
| --------------------------------------------------------------------------
*/
$config['category'] = array(
  '1' => 'General',
  '2' => 'SC/ST',
  '3' => 'CATEGORY IIA',
  '4' => 'CATEGORY IIB',
  '5' => 'CATEGORY IIIA',
  '6' => 'OBC'
);

/*
|-----------------------------------------------------------------------
| This setting is used to set the allocation template for online room booking. Set this value to an existing (i.e. already created) timetable_template->id.
|-----------------------------------------------------------------------
*/

$config['online_room_booking_template_id'] = '2';

/*
| --------------------------------------------------------------------------
| Address Types
| Define all the address types the school likes to collect from student and parents.
|
| Allowed values are -
|(1: Present Address and 2: Permanent Address) or (1: Office Address And 2: Home Address)
| --------------------------------------------------------------------------
*/

$config['address_types'] = array(
  'Student'=> array(),
  'Father'=>  array("0" => "Office Address", "1" => "Home Address"),
  'Mother'=>  array("0" => "Office Address", "1" => "Home Address")
);

/*
| --------------------------------------------------------------------------
| Parent profile display filds based on school config
| Define all the types the school likes profile fileds.
|
| Allowed values are -
|(0: Present Address and 1: Permanent Address) or (0: Office Address And 1: Home Address)
| Array of address array('0','1') ? Office address / Present adress : Home Address / Permanent Address
| --------------------------------------------------------------------------
*/

$config['parent_profile_display'] = array(
  'Student'=> array('admission_no','dob','gender','roll_no'),
  'Father'=> ['display' => '1', 'fields' => array('name_photo','email','contact_no','address' => array('1'),'occupation')],
  'Mother'=> ['display' => '1', 'fields' => array('name_photo','email','contact_no','address' => array('1'),'occupation')],
);


/*
| --------------------------------------------------------------------------
| Staff Profile
| All configuration parameters for Staff Profile edit
|
| Allowed values are -
| enableStaffProfileEdit: 
| 1: Enables Staff to edit their profile in staff App.
| 0: Disables Staff to edit their profile in staff App.
| --------------------------------------------------------------------------
*/

$config['staff_profile']['enableStaffProfileEdit'] = 0;
$config['staff_profile']['enableQualificationEdit'] = 0;

/*
| -------------------------------------------------------------------------
| Fees.
| -------------------------------------------------------------------------
| Fee Specific settings
*/

/*
| --------------------------------------------------------------------------
| Filter Criteria
|
| This is used for creating Master Fee Structure.
|
| Different schools have different filter criteria. For eg: NPS RNR uses academic year
| when student joined and whether he is RTE candidate to determine Fee Structure.
| NPS CNP uses class the student is going into and RTE candidature.
| Specify here the filter criteria to be used.
| This should be the same table column name you use in Student.
|
| The currently supported school criteria are - academic_year_of_joining, rte, class,
| medium, category, admission_type, boards, boarding
| --------------------------------------------------------------------------
|   Allowed values are -
|   0 -> 'admission_type',
|   1 -> 'medium
|   2 -> 'rte
|   3 -> 'category
|   4 -> 'academic_year_of_joining
|   5 -> 'class
|   6 -> 'boards
|   7 -> 'boarding
*/
$config['fees']['filter_criteria'] = array('admission_type');

/*
| ------------------------------------------------------------------------------
| Fee Components
|
| This is used to create Fee Components in Master.
|
| Different schools have different fee components. We assume that the components are same across all
| fee Structures. If this is wrong, we have to handle this as a database table rather than config.
|
| Add all the different components in 'Allocation Order' (See Allocation algorithm).
|      
| --------------------------------------------------------------------------------
*/

$config['fees']['components'] = array(
  array ( 'name' => 'Admission', 'concession_eligible' => TRUE ),
);

/*
| ----------------------------------------------------------------------------------
| Fee Payment modes
| 
| This is used in Fee payment.
| Add all the payment modes supported by this school
| Allowed values - 

            $config['allowed_payment_modes'] = array(
                            
                              '1'=>'dd',
                              '2'=>'credit card',
                              '3'=>'debit card',
                              '4'=>'cheque'
                              '5'=>'wallet payment'
                              '6'=>'challan'
                              '7'=>'Card (POS)'
                              '8'=> Net Banking (POS)
                              '9'=>'cash',
                            );

| ------------------------------------------------------------------------------------
*/
$config['fees']['allowed_payment_modes'] = array(
  array ('name'=>'cash','value'=>'9', 'reconcilation_reqd' => FALSE),
  array ('name'=>'card (POS)','value'=>'7', 'reconcilation_reqd' => FALSE),
  array ('name'=>'net banking','value'=>'8', 'reconcilation_reqd' => TRUE)
);

/*
| ----------------------------------------------------------------------------------
| Fee Receipt template
| 
| This is used to generate the fee receipt.
| Add the template file that should be used (probably customized) for this school.
| ------------------------------------------------------------------------------------
*/
$config['fees']['receipt_template'] = 'NHS';


/*
| ----------------------------------------------------------------------------------
| Fee Component Allocation Type
| 
| This is used in Fee transaction.
|
| If TRUE, the allocation algorithm will kick in to allocate the total amount entered across Fee Components.
| If FALSE, the allocation has to be manually keyed in by the user (cashier).
|
| ------------------------------------------------------------------------------------
*/
$config['fees']['component_allocation_type_auto'] = TRUE;

$config['fees']['cComponent_allocation_type_auto'] = TRUE;
/*
| --------------------------------------------------------------------------------------
| Fee Component Allocation algorithm
| 
| This is used in conjunction with Fee transaction.
|
| If Allocation algorithm is AUTO, then the specified algorithm is used to allocate total amount across Fee Components.
|
| Algo 1: 'ALLOCATE_FROM_LEFT_TO_RIGHT' - This algorithm allocates the total amount from left to right as specified in the fee component array above.
| It will allocate the full amount for the component before proceeding to the next.
|
| ----------------------------------------------------------------------------------------
*/
$config['fees']['component_allocation_algorithm_if_auto'] = 'ALLOCATE_FROM_LEFT_TO_RIGHT';


/*
| --------------------------------------------------------------------------------------
| Fee receipt generation
| ----------------------------------------------------------------------------------------
*/

$config['fees']['recipt_number_gen'] = [ 
                      'fee_generation_algo' => 'NHS',
                      'infix' => 'NHS',
                      'digit_count' => 4,
                      ];

/*
| --------------------------------------------------------------------------------------
| Discount for fully paid fee amount for north hill school only  
| ----------------------------------------------------------------------------------------
*/
$config['fees']['discount'] =[
                        'percent'=> 5,
                        'isProvided' => TRUE,
                        'isManual' => FALSE
                       ];

/*
| --------------------------------------------------------------------------
| Attendance Module
| Define the Attendance Module of class per the school standards
|
| --------------------------------------------------------------------------
*/

$config['attendance']['absentee_sms'] = 'Your ward %std_name% of %cs_name% is absent today %date%. Kindly ignore the sms if you have already informed the school through email. Principal - NPSRNR';
$config['attendance']['latecomer_sms'] = 'Your ward %std_name% of %cs_name% has come to school late today %date%. Principal - NPSRNR';

$config['student_action']['outGoing'] = [
  [ 'class' => 'KG1,KG2,1,2,3,4,5',
    'time-range' => '7:20 am - 11:45 am'
  ],
  [ 'class' => '6,7,8,9,10,11,12',
    'time-range' => '8:20 am - 12:20 pm'
  ]
];

$config['student_action']['healthCare'] = [
  [ 'class' => 'KG1,KG2,1,2,3,4,5',
    'time-range' => '8:20 am - 11:45 am'
  ],
  [ 'class' => '6,7,8,9,10,11,12',
    'time-range' => '8:20 am - 12:20 pm'
  ]
];

/*
| --------------------------------------------------------------------------------------
| Predefined SMS Templates
| ----------------------------------------------------------------------------------------
*/
$config['smstemplates'] = array("Send student profile for updating.");
/*
| --------------------------------------------------------------------------------------
| SMS Integration
| ----------------------------------------------------------------------------------------
*/

if (ENVIRONMENT !== 'production') {
  //Do not allow SMS config in other environments!
} else {
  $config['smsintergration']  = 
    array('url' => 'alerts.valueleaf.com/api/v4/index.php',
      'api_key' => 'Ae6b5684768b2741508f447a71545290a',
      'sender' => 'PVVSCH');
}


/*
| --------------------------------------------------------------------------------------
| SMS Credits per message (unicode/non-unicode)
| ----------------------------------------------------------------------------------------
*/
$config['sms_credit_length']['unicode_single'] = '70';
$config['sms_credit_length']['unicode_multi'] = '60';
$config['sms_credit_length']['non_unicode_single'] = '160';
$config['sms_credit_length']['non_unicode_multi'] = '150';


/*
| --------------------------------------------------------------------------------------
| Circular Related Config
| ----------------------------------------------------------------------------------------
*/
$config['circular']['sms_msg1'] = "You have a new circular with title ";
$config['circular']['sms_msg2'] = ", Please check your School App.";

$config['circular']['categories'] = ['Competition','Co-Scholastic','Co-curricular','Scholastic','Time-Table','Board-Circular','Fee','Student-Info'];
$config['circular']['from_email'] = "<EMAIL>";
$config['circular']['enable_email'] = 0;
$config['circular']['enable_sms'] = 1;

/*
| --------------------------------------------------------------------------------------
| User Provisioning Module
| Challenge Fields: class, section, dob, admission_no, class_teacher
| ----------------------------------------------------------------------------------------
*/
//To customize the Instructions Page
$config['user_provisioning']['instruction_page'] = 'npsrnr';


//To customize Validation Fiels
$config['user_provisioning']['challenge_fields'] = [
  ['name'=>'class','help_text'=>''],
  ['name'=>'section','help_text'=>''],
  ['name'=>'dob','help_text'=>'As per school records (in DD-MM-YYYY).']
];

//Message with the activation link
$config['user_provisioning']['link_message_body'] = "Dear Parent,\n\nGreetings!\nIn an endeavour to enhance our IT Infrastructure, we have decided to integrate our School Data Management System with a new vendor EteachGuru.
Parent access will be provided through a series of steps with specific instructions on how to register, login and check data.
Login can be through internet browser or specially designed Mobile App catering to both Android and iPhones. 
All data is encrypted to ensure privacy. Parents will be able to access their ward's personal information, Section timetable, Circulars, School calendar, Competition details, Report cards, Unit test / Exam timetable and Portions. 
Thank you for your co-operation, ensuring you of our best services always. Should you have any difficulty in accessing the portal, please send an email to the <NAME_EMAIL>\n\n";
//message footer
$config['user_provisioning']['link_message_footer'] = "\n\nRegards,\nPrincipal and IT Team NPSRNR";
//User cridential message for provisioning
$config['user_provisioning']['cridential_message'] = "Your NPS school account is activated. Android App Link: https://play.google.com/store/apps/details?id=com.nps.nextelement\niOS App Link: https://itunes.apple.com/in/app/nps/id1418371501?mt=8\n Browser Link: https://npsrnr.eteachguru.com/.\nSchool code is npsrnr. ";
$config['user_provisioning']['school_code'] = 'npsrnr';

//User manual activation message
$config['user_provisioning']['manual_cridential_part_1'] = "\nGreetings!\nIn an endeavour to enhance our IT Infrastructure, we have decided to integrate our School Data Management System with a new vendor ETeachGuru. Use these credentials to login to your new account.\n\n";

$config['user_provisioning']['manual_cridential_part_2'] = ".\nAndroid App Link: https://goo.gl/vpi2Lr\niOS App Link: https://goo.gl/mWYhJy\nBrowser Link: https://yashasvi.nextelement.in/.\n\nKindly change your password on your first login. Do not share this SMS to anybody. In case of any support, mail  <EMAIL>.";

//provisioning process to be followed
$config['user_provisioning']['process'] = 'validate_and_activate';

/*
|-----------------------------
| EMAIL: General Settings
|-----------------------------
|
| 'settings'   => General settings
|               Can be overriden with the template settings
|               or when calling the send() method
|
| Following configs tested working on gmail account
*/
$config['email']['settings'] = array( 
  'from_email'    => '<EMAIL>',
  'smtp_user'     => getenv('SMTP_USER'),
  'smtp_pass'     => getenv('SMTP_PASS'),
  'from_name'     => 'National Public School Rajajinagar',
  'smtp_host'     => getenv('SMTP_HOST'),
  'smtp_port'     => getenv('SMTP_PORT'),
  'protocol'      => 'smtp',
  'smtp_crypto'   => 'ssl',
  'mailtype'      => 'html',
  'charset'       => 'utf-8',
  'newline'       => "\r\n",
);

/*
|-----------------------------
| Templates location
|-----------------------------
|
| 'templates' = Folder located @ application/views/{}
|
| Each template created must have a config in templates
*/

$config['email']['templates'] = 'emails';

/*
|-----------------------------
| Dev Email controls
|-----------------------------
|
|
| Stop remove actual users from DB and sent it to specified 
*/

$config['email']['dev_email'] = '<EMAIL>';

/*
|-----------------------------
| Email Templates
|-----------------------------
|
| 'mailtype'    = Mail type, if not set will use general type
| 'charset'     = Charset, if not set will use general charset
| 'from_email'  = From email, if not set will use general email
| 'from_name'   = From name, if not set will use general name
| 'subject'     = Email Subject
| 'send_email'  = If false, it will send the email to site owner instead of actual user
| 'bcc_admin'   = Add the site admin as a BCC to the email
*/

$config['email']['templates'] = array(
  // Demo
  'demo'   => array(
      'subject'    => 'Test Demo',
      'send_email' => true,
      'bcc_admin'  => false,
  ),
  'forgot_password'   => array(
      'subject'    => '',
      'send_email' => true,
      'bcc_admin'  => false,
  ),
);

/**
 * 
 * 
 * Switch to switch to old login page. Required for supporting old android mobiles.
 * 
 */
//$config['loginPageToUse'] = 'old';

/*
| --------------------------------------------------------------------------------------
| Library Relateed Config
| ----------------------------------------------------------------------------------------
*/
$config['lbr']['books_type'] = array(
  array ('name'=>'Issue','value'=>'1', 'type_reqd' => TRUE),
  array ('name'=>'Reference','value'=>'2', 'type_reqd' => TRUE),
  array ('name'=>'Specimen copy','value'=>'3', 'type_reqd' => TRUE),
  array ('name'=>'CD','value'=>'4', 'type_reqd' => TRUE),
  array ('name'=>'Non-Issue','value'=>'5', 'type_reqd' => TRUE)
);

/*
| ----------------------------------------------------------------------------------
| Library Payment modes
| 
| This is used in library payment.
| Add all the payment modes supported by this school
| Allowed values - 

            $config['allowed_payment_modes'] = array(
                            
                              '1'=>'dd',
                              '2'=>'credit card',
                              '3'=>'debit card',
                              '4'=>'cheque'
                              '5'=>'wallet payment'
                              '6'=>'challan'
                              '7'=>'Card (POS)'
                              '8'=> Net Banking (POS)
                              '9'=>'cash',
                              '10'=>'waive off',
                            );

| ------------------------------------------------------------------------------------
*/
$config['lbr']['allowed_payment_modes'] = array(
  array ('name'=>'cash','value'=>'9', 'reconcilation_reqd' => FALSE),
  array ('name'=>'waive off','value'=>'10', 'reconcilation_reqd' => FALSE),
);

/*
| --------------------------------------------------------------------------------------
| Library access code generation manual or auto based on school config 
| ----------------------------------------------------------------------------------------
*/

$config['lbr']['lbr_access_code'] = FALSE;

// Enable Competition Attendance
$config['competition_attendance'] = true;

//Show Acknowledgement button for parents 

$config['show_acknowledgment'] = true;

//Show Language update based on school access 
$config['student']['record_selected_lang'] = TRUE;

$config['student']['record_height_weight'] = FALSE;

// Latecomer Attendance if set to true only absents students would be show else all class students would be displayed.
$config['latecomer_attendance'] = true;

// Emergency Exit requires Visitor entry?.
$config['emergency_exit_visitor'] = true;

//Examination related config
$config['examination']['allow_additional_remarks'] = false;
$config['examination']['remarks_length'] = 250;
$config['examination']['marks_rounding_digits'] = 1;

$config['new_admissions'] = 'nps';

$config['admissions']['forms'] =
    array(
      array(
        'form_name'=>'MONTESSORI',
        'form_year'=>'2019-2020',
        'school_name'=>'GLOBAL MONTESSORI CENTRE, RAJAJINAGAR',
        'instructin_file'=>'mont',
        'school_short'=>'nps',
        'school'=>'NPS - Rajajinagar',
        'open_for_admissions'=>FALSE,
        'class_applied_for'=>array('MONTESSORI'),
        'application_no_gen'=>array(
                                    'prefix' => 'MONT',
                                    'name' => 'NPSRNR',
                                    'year' =>'2019',
                                    'digit_count' => 5
                                  ),
        'documents' => array(
                          'Birth Certificate'=>'Birth Certificate',
                          'Aadhar card'=>'Aadhar card',
                          'Others'=>'Others',
                        ),
        'guidelines' => array(
                          'name'=>'NATIONAL PUBLIC SCHOOL',
                          'line1'=>'1036-A, 5th Block,',
                          'line2'=>'Rajajinagar,',
                          'line3'=>'Bengaluru – 560010',
                          'line4'=>'South India',
                          'phone'=>'<strong>Phone: </strong> 091-080-********/091-080-********',
                          'fax'=>'',
                          'email'=>'<strong>Email: </strong> <EMAIL>',
                          'school_short'=>'NPS - Rajajinagar',
                          'school_full'=>'NATIONAL PUBLIC SCHOOL - Rajajinagar',
                        ),
        ),

      array(
      'form_name'=>'KG1',
      'form_year'=>'2019-2020',
      'school_name'=>'GLOBAL MONTESSORI CENTRE, RAJAJINAGAR',
      'instructin_file'=>'kg',
      'school_short'=>'nps',
      'school'=>'NPS - Rajajinagar',
      'open_for_admissions'=>FALSE,
      'class_applied_for'=>array('KG1'),
      'application_no_gen'=>array(
                                  'prefix' => 'KG',
                                  'name' => 'NPSRNR',
                                  'year' =>'2019',
                                  'digit_count' => 5
                                ),
      'documents' => array(
                        'Birth Certificate'=>'Birth Certificate',
                        'Aadhar card'=>'Aadhar card',
                        'Others'=>'Others',
                      ),
      'guidelines' => array(
                          'name'=>'NATIONAL PUBLIC SCHOOL',
                          'line1'=>'1036-A, 5th Block,',
                          'line2'=>'Rajajinagar,',
                          'line3'=>'Bengaluru – 560010',
                          'line4'=>'South India',
                          'phone'=>'<strong>Phone: </strong> 091-080-********/091-080-********',
                          'fax'=>'',
                          'email'=>'<strong>Email:</strong> <EMAIL>',
                          'school_short'=>'NPS - Rajajinagar',
                          'school_full'=>'NATIONAL PUBLIC SCHOOL - Rajajinagar',
                        ),
      ),

      array(
      'form_name'=>'Grade 2 to Grade 9',
      'form_year'=>'2019-2020',
      'school_name'=>'NATIONAL PUBLIC SCHOOL, RAJAJINAGAR',
      'instructin_file'=>'2-9',
      'school_short'=>'npsrnr',
      'school'=>'NPS - RAJAJINAGAR',
      'open_for_admissions'=>TRUE,
      'class_applied_for'=>array('2','3','4','5','6','7','8','9'),
      'application_no_gen'=>array(
                                  'name' => 'NPSRNR',
                                  'prefix' => '2-9',
                                  'year' =>'2019',
                                  'digit_count' => 5
                                ),
      'documents' => array(
                        'Birth Certificate'=>'Birth Certificate',
                        // 'Aadhar card'=>'Aadhar card',
                        'Others'=>'Others',
                      ),

      'prev_eduction_info'=>array(
                        // 'subject'=>array('Eng','Hindi','Math','Science'),
                        'year'=>array('2016-17','2017-18','2018-19'),
                        'result_types'=>array('Percent','Grade'),
                        'class'=> array(
                          '2'=>array(
                            'subject'=> array(
                              ['id'=>'1','name'=>'English','type'=>'label'],
                              ['id'=>'2','name' => 'Language 2','type'=>'text'],
                              ['id'=>'3','name' =>'Maths','type'=>'label'],
                              ['id'=>'4','name'=>'Science','type'=>'label']
                            ),
                          ),

                          '3'=>array(
                            'subject'=> array(
                              ['id'=>'1','name'=>'English','type'=>'label'],
                              ['id'=>'2','name' => 'Language 2','type'=>'text'],
                              ['id'=>'3','name' =>'Maths','type'=>'label'],
                              ['id'=>'4','name'=>'Science','type'=>'label']
                            ),
                          ),

                          '4'=>array(
                            'subject'=> array(
                              ['id'=>'1','name'=>'English','type'=>'label'],
                              ['id'=>'2','name' => 'Language 2','type'=>'text'],
                              ['id'=>'3','name' =>'Maths','type'=>'label'],
                              ['id'=>'4','name'=>'Science','type'=>'label']
                            ),
                          ),

                          '5'=>array(
                            'subject'=> array(
                              ['id'=>'1','name'=>'English','type'=>'label'],
                              ['id'=>'2','name' => 'Language 2','type'=>'text'],
                              ['id'=>'3','name' =>'Maths','type'=>'label'],
                              ['id'=>'4','name'=>'Science','type'=>'label']
                            ),
                          ),

                          '6'=>array(
                            'subject'=> array(
                              ['id'=>'1','name'=>'English','type'=>'label'],
                              ['id'=>'2','name' => 'Language 2','type'=>'text'],
                              ['id'=>'3','name' => 'Language 3','type'=>'text'],
                              ['id'=>'4','name' =>'Maths','type'=>'label'],
                              ['id'=>'5','name'=>'Science','type'=>'label']
                            ),
                          ),

                          '7'=>array(
                            'subject'=> array(
                              ['id'=>'1','name'=>'English','type'=>'label'],
                              ['id'=>'2','name' => 'Language 2','type'=>'text'],
                              ['id'=>'3','name' => 'Language 3','type'=>'text'],
                              ['id'=>'4','name' =>'Maths','type'=>'label'],
                              ['id'=>'5','name'=>'Science','type'=>'label']
                            ),
                          ),

                          '8'=>array(
                            'subject'=> array(
                              ['id'=>'1','name'=>'English','type'=>'label'],
                              ['id'=>'2','name' => 'Language 2','type'=>'text'],
                              ['id'=>'3','name' => 'Language 3','type'=>'text'],
                              ['id'=>'4','name' =>'Maths','type'=>'label'],
                              ['id'=>'5','name'=>'Science','type'=>'label']
                            ),
                          ),

                          '9'=>array(
                             'subject'=> array(
                              ['id'=>'1','name'=>'English','type'=>'label'],
                              ['id'=>'2','name' => 'Language 2','type'=>'text'],
                              ['id'=>'3','name' => 'Language 3','type'=>'text'],
                              ['id'=>'4','name' =>'Maths','type'=>'label'],
                              ['id'=>'5','name'=>'Science','type'=>'label']
                            ),
                          ),
                        ),
                      ),

      'custom_field' => array( '9'=>array(
                            'II Language choice'=> array(
                              ['name' => 'II Language choice','options' => array('Hindi','French','Sanskrit')],
                            ),
                          ),
                      ),

      'guidelines' => array(
                          'name'=>'NATIONAL PUBLIC SCHOOL',
                          'line1'=>'1036-A, 5th Block,',
                          'line2'=>'Rajajinagar,',
                          'line3'=>'Bengaluru – 560010',
                          'line4'=>'South India',
                          'phone'=>'<strong>Phone: </strong> 091-080-********/091-080-********',
                          'fax'=>'',
                          'email'=>'<strong>Email: </strong> <EMAIL>',
                          'school_short'=>'NPS - Rajajinagar',
                          'school_full'=>'NATIONAL PUBLIC SCHOOL - Rajajinagar',
                        ),
      ),
      array(
          'form_name'=>'Grade 11',
          'form_year'=>'2019-2020',
          'school_name'=>'NATIONAL PUBLIC SCHOOL, RAJAJINAGAR',
          'instructin_file'=>'11',
          'school_short'=>'npsrnr',
          'school'=>'NPS - RAJAJINAGAR',
          'open_for_admissions'=>TRUE,
          'class_applied_for'=>array('11'),
          'application_no_gen'=>array(
                                    'name' =>'NPSRNR',
                                    'prefix' => '11',
                                    'year' =>'2019',
                                    'digit_count' => 5
                                  ),
        'documents' => array(
                          'Birth Certificate'=>'Birth Certificate',
                          // 'Aadhar card'=>'Aadhar card',
                          'Others'=>'Others',
                        ),
        'prev_eduction_info'=>array(
                          // 'subject'=>array('Eng','Hindi','Math','Science'),
                          'year'=>array('2016-17','2017-18','2018-19'),
                          'result_types'=>array('Percent','Grade'),
                          'class'=> array(
                            '11'=>array(
                               'subject'=> array(
                                ['id'=>'1','name'=>'English','type'=>'label'],
                                ['id'=>'2','name' => 'Language 2','type'=>'text'],
                                ['id'=>'3','name' => 'Language 3','type'=>'text'],
                                ['id'=>'4','name' =>'Maths','type'=>'label'],
                                ['id'=>'5','name'=>'Science','type'=>'label']
                              ),
                            ),
                          ),
                        ),

         'streams'=>array(
                      'Science'=> array(
                        ['id'=>'1','name'=>'Math, Physics, Chemistry, Biology'],
                        ['id'=>'2','name'=>'Math, Physics, Chemistry, Computer Science'],
                        ['id'=>'3','name'=>'Math, Physics, Chemistry, Economics'],
                        // ['id'=>'4','name'=>'Physics, Chemistry, Biology & Psychology'],
                      ),
                      'Commerce'=> array(
                        ['id'=>'1','name'=>'Math, Accountancy, Business Studies, Economics'],
                        // ['id'=>'2','name'=>'Math, Economics, Business Studies, Entrepreneurship'],
                        ['id'=>'2','name'=>'Entrepreneurship, Accountancy, Business Studies, Economics'],
                      ),
                      // 'Humanities'=> array(
                      //   ['id'=>'1','name'=>'Psychology, History, Entrepreneurship, Economics'],                      
                      // ),
                    ),

         'guidelines' => array(
                            'name'=>'NATIONAL PUBLIC SCHOOL',
                            'line1'=>'1036-A, 5th Block,',
                            'line2'=>'Rajajinagar,',
                            'line3'=>'Bengaluru – 560010',
                            'line4'=>'South India',
                            'phone'=>'<strong>Phone: </strong> 091-080-********/091-080-********',
                            'fax'=>'',
                            'email'=>'<strong>Email: </strong> <EMAIL>',
                            'school_short'=>'NPS - Rajajinagar',
                            'school_full'=>'NATIONAL PUBLIC SCHOOL - Rajajinagar',
                          ),
          ),

    );

//Forgot Password 
$config['forgot_password']['email'] = true;
$config['forgot_password']['mobile'] = true;
