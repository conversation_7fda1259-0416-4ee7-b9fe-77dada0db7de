<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>ulus<PERSON>
 *          <EMAIL>
 *
 * Created:  17 August 2022
 *
 * Description: Controller for Alert Management Module. Entry point for Alert Management Module
 *
 * Requirements: PHP5 or above
 *
 */

class Alerts extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
  }

  //Landing function to show non-compliance menu
  public function index() {
    $site_url = site_url();

    $data['main_content']    = 'alert_mgmt/manage_alerts/index';
    $this->load->view('inc/template', $data);
  }

}
