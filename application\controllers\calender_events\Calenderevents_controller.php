<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Calender_events
 *
 * <AUTHOR>
 */
class Calenderevents_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('SCHOOL_CALENDAR')) {
            redirect('dashboard', 'refresh');
        }    
       // $this->lang->load('master_lang', 'english');
        $this->load->model('Calender_events');
        $this->load->helper('url');
    }

    public function index($year = null, $month = null, $day = null) {
        // if no month in the url intially set this as  month and date
        if ($this->uri->segment(4) == null) {
            $year = date('Y');
        } else {
            $year = $this->uri->segment(4);
        }
        $data['year'] = $year;

        if ($this->uri->segment(5) == null) {
            $month = date('m');
        } else {
            $month = $this->uri->segment(5);
        }
        $data['month'] = $month;
//intialize the calender with  below calender template
        $data = array(
            'years' => $this->uri->segment(4),
            'months' => $this->uri->segment(5),
            'show_next_prev' => TRUE,
            'show_next_prev' => TRUE,
            'next_prev_url' => site_url('calender_events/Calenderevents_controller/index'),
            'start_day' => 'monday',
            'month_type' => 'long',
            'day_type' => 'short',
            'template' => '{table_open}<table align="center" class="date" style="width: 800px;height:300px;">{/table_open}
           {heading_row_start}&nbsp;{/heading_row_start}
           {heading_previous_cell}<caption><a href="{previous_url}" class="prev_date" title="Previous Month">&lt;&lt;Prev</a>{/heading_previous_cell}
           {heading_title_cell}{heading}{/heading_title_cell}
           {heading_next_cell}<a href="{next_url}" id="trest" class="next_date"  title="Next Month">Next&gt;&gt;</a></caption>{/heading_next_cell}
           {heading_row_end}<col class="weekday" span="5"><col class="weekend_sat"><col class="weekend_sun">{/heading_row_end}
           {week_row_start}<thead><tr>{/week_row_start}
           {week_day_cell}<th>{week_day}</th>{/week_day_cell}
           {week_row_end}</tr></thead><tbody>{/week_row_end}
           {cal_row_start}<tr>{/cal_row_start}
           {cal_cell_start}<td>{/cal_cell_start}
           {cal_cell_content}<div class="date_event detail" val="{day}" ><span class="date">{day}</span><span class="event d{day}">{content}</span></div>{/cal_cell_content}
           {cal_cell_content_today}<div class="active_date_event detail" val="{day}"><span class="date">{day}</span><span class="event d{day}">{content}</span></div>{/cal_cell_content_today}
           {cal_cell_no_content}<div class="no_event detail" val="{day}"><span class="date">{day}</span><span class="event d{day}">&nbsp;</span></div>{/cal_cell_no_content}
           {cal_cell_no_content_today}<div class="active_no_event detail" val="{day}"><span class="date">{day}</span><span class="event d{day}">&nbsp;</span></div>{/cal_cell_no_content_today}
           {cal_cell_blank}&nbsp;{/cal_cell_blank}
           {cal_cell_end}</td>{/cal_cell_end}
           {cal_row_end}</tr>{/cal_row_end}
           {table_close}</tbody></table>{/table_close}'); //$this->load->library('calendar');
//$data=array();
        //get all month events 
        $data['getCompetationEvent'] = $this->Calender_events->getCompetationEvent($year, $month);
        $data['holidaysEventInfo'] = $this->Calender_events->holidaysEventInfo($year, $month);
        $data['getCollegeEvent'] = $this->Calender_events->getCollegeEvent($year, $month);
        foreach ($data['getCompetationEvent'] as $getCompetationEvents) {

            $rt = $getCompetationEvents['competition_date'];
            $k = explode("-", $rt);
//$test=preg_replace('/\[(.*)\]/', '', $k[2]);

            $calenders[] = array(
                (int) ($k[2]) => $getCompetationEvents['competition_name']
            );
            $arraydata = array_filter(array_map('array_filter', $calenders));
            foreach ($arraydata as $key => $srach) {
                foreach ($srach as $key => $val) {
                    $data1[$key] = $val;
                }
            }  if(isset($data1))
            $data['calenders'] = $data1;
        }  //echo "<pre>";print_r($data['calenders']);die();
        foreach ($data['holidaysEventInfo'] as $holidaysEventInfos) {
            $rt1 = $holidaysEventInfos['event_date'];
            $k1 = explode("-", $rt1);
            $calenders[] = array(
                (int) ($k1[2]) => $holidaysEventInfos['name']
            );
            $arraydata = array_filter(array_map('array_filter', $calenders));
            foreach ($arraydata as $key => $srach) {
                foreach ($srach as $key => $val) {
                    $data1[$key] = $val;
                }
            }
              if(isset($data1))
            $data['calenders'] = $data1;
        }
        foreach ($data['getCollegeEvent'] as $getCollegeEvents) {
            //echo "<pre>";print_r($data['getCollegeEvent']);die();
            $rt2 = $getCollegeEvents['event_date'];
            $k2 = explode("-", $rt2);
            $calenders[] = array(
                (int) ($k2[2]) => $getCollegeEvents['event_name']
            );
            $arraydata = array_filter(array_map('array_filter', $calenders));
            foreach ($arraydata as $key => $srach) {
                foreach ($srach as $key => $val) {
                    $data1[$key] = $val;
                }
            }  if(isset($data1))
            $data['calenders'] = $data1;
        }
        $this->load->library('calendar', $data); // $this->calendar->adjust_date($month, $year);
        $data['main_content'] = 'calenderEvents/calendar_show';
        $this->load->view('inc/template', $data);
    }

    public function calenderEvents() {
        // month and day through ajax post
        $month = $_POST['postData']; //echo "<pre>";print_r($month);
        $day = $_POST['day']; // echo "<pre>";print_r($day);die();
        // get detail info of all the events
        $data['holidayDetail'] = $this->Calender_events->holidaysInfo(date('Y'), $month, $day);
        // $data['noHolidayDetail'] = $this->Calender_events->holidaysInfo(date('Y'), $month, $day);
        $data['competationEvents'] = $this->Calender_events->competationEventsInfo(date('Y'), $month, $day);
        $data['collegeEvents'] = $this->Calender_events->collegeEventsInfo(date('Y'), $month, $this->input->post('day'));
        $template = '';
        if (!empty($data['competationEvents'])) {
            foreach ($data['competationEvents'] as $competationEventss) {

                $template .= '<h4 style="color:#337ab7 ; display:inline-block;">';
                $template .= 'Competition:';
                $template .= '</h4>' . "<br>";
                $template .= '<h5 style="display:inline-block;color:#fe970a">' . $competationEventss['competition_name'];
                $template .= '</h5>';
                $template .= '&nbsp;&nbsp;&nbsp';
                $template .= '<h5 style="display:inline-block;color:#3c763d">' . $competationEventss['start_time'];
                $template .= '</h5>';
                $template .= '&nbsp;&nbsp;&nbsp';
                $template .= '<h5  style="display:inline-block;color:#3c763d">' . $competationEventss['end_time'];
                $template .= '</h5>' . "<br>";
                $template .= "<hr style='border-color:#fe970a'>";
            }
        }
      
        
       else if (!empty($data['collegeEvents'])) {
            foreach ($data['collegeEvents'] as $collegeEventss) {
                $template .= '<h4 style="color:#337ab7 ; display:inline-block;">';
                $template .= 'Event:';
                $template .= '</h4>' . "<br>";
                $template .= '<h5 style="display:inline-block;color:#fe970a">' . $collegeEventss['event_name'];
                $template .= '</h5>';
                $template .= '&nbsp;&nbsp;&nbsp';
                $template .= '<h5 style="display:inline-block;color:#3c763d">' . $collegeEventss['start_time'];
                $template .= '</h5>';
                $template .= '&nbsp;&nbsp;&nbsp';
                $template .= '<h5  style="display:inline-block;color:#3c763d">' . $collegeEventss['end_time'];
                $template .= '</h5>' . "<br>";
                $template .= "<hr style='border-color:#fe970a'>";
            }
        }
       
   
      else  if (!empty($data['holidayDetail'])) {

            foreach ($data['holidayDetail'] as $holidayEventss) {
                $template .= '<h4 style="color:#337ab7 ; display:inline-block;">';
                $template .= 'Event:'. $holidayEventss['type'];;
                $template .= '</h4>' . "<br>";
                $template .= '<h5 style="display:inline-block;color:#fe970a">' . $holidayEventss['name'];
                $template .= '</h5>';
                $template .= '&nbsp;&nbsp;&nbsp';
                $template .= '<h5 style="display:inline-block;color:#3c763d">' . $holidayEventss['event_date'];
                $template .= '</h5>';
                $template .= '&nbsp;&nbsp;&nbsp';
               
                $template .= "<hr style='border-color:#fe970a'>";
            }
        }
       else {
      $template .= '<h4 style="color:#337ab7 ; display:inline-block;">';
                $template .= 'Event:'.' No Event';;
               
                $template .= '&nbsp;&nbsp;&nbsp';
               
                $template .= "<hr style='border-color:#fe970a'>";
            
        }
        print $template;
//        
    }

}
