body, select {
  color: #7d7a7a;
  font-family: 'trebuchet ms', verdana, sans-serif;
	font-size: 13px;
}

a:link, a:visited, a:active { 
  color: #5F83BA; 
  text-decoration: none;
}

a:hover { 
  color: #5f83ba; 
  text-decoration: underline; 
}

img { 
  border: none;
}

pre,
code { 
  font-size: 0.8em;
  font-family: "lucida console", monospace;
}

pre {
  background-color: #f8f8f8; 
  padding: 10px;
}

select { 
	font-weight: bold; 
}

h2 {
	margin: 0.3em 0;
}

.bar {
  background-image: url('images/h_bar.gif');
  background-repeat: repeat-x;
  background-position: bottom right;
}

#header {
  height: 50px;
  line-height: 30px;
}

#footer {
  font-size: 0.75em;
  padding-top: 12px;
  background-image: url('images/h_bar.gif');
  background-repeat: repeat-x;
  background-position: top left;
  height: 35px;
  vertical-align: middle;
  clear: both;
}

#logo {
  position: absolute;
  top: 0px;
  right: 0px;
  border: none;
}

.badges { 
  float: right; 
}

#left_col, #content {
  vertical-align: top;
}
/*
#left_col {
  padding: 3px 3px 2em 3px;
  margin-top: 2px;
  width: 210px;
  padding-right: 10px;
  background-image: url('images/v_bar.gif');
  background-repeat: repeat-y;
  background-position: top right;
}
*/

#left_col {
  padding: 3px 3px 3em 3px;
  margin-top: 2px;
  width: 120px;
  padding-right: 10px;
  float: left;
}

#left_col h2 {
  font-size: 1.0em;
  margin-top: 0.5em;
  margin-bottom: 0.25em;
}

#left_col ul {
  margin-top: 0.25em;
  padding-left: 0px;
  margin-left: 0px;
  position: fixed;
}

#left_col ul li { list-style-position: inside; }

#left_col iframe { margin-left: 40px; margin-top: 10px; }

#content {
  margin-left: 120px;
  padding: 1em 1em 1em 2em;
  min-width: 800px;
  background-image: url('images/v_bar.gif');
  background-repeat: repeat-y;
  background-position: top left;
}

.message { 
  margin-top: 1em;
  border: 1px dashed #5E83BA;
}

#content li {
  margin-top: 0.3em;
  vertical-align: top;
}

#content>*>li { 
  margin-right: 40px;  /* keep things in line */
}

#content h2 {
  text-align: left;
  color: #4A9166;
}

#content h3 { 
  margin-top: 2em;
}

#content p {
  text-align: justify;
}

#content table td,
#content table th { 
  padding: 0.3em; 
}

#content table td.input { 
  white-space: nowrap;
  font-family: "lucida console", monospace; 
  font-size: 0.8em;
}

#content textarea { 
  padding: 4px;
  width: 100%;
  border: 1px dashed #5F83BA;
  -moz-box-shadow: inset 3px 3px 3px rgba(0,0,0,0.1);
  -webkit-box-shadow: inset 3px 3px 3px rgba(0,0,0,0.1);
  box-shadow: inset 3px 3px 3px rgba(0,0,0,0.1);
}

#content button { 
  color: #6d6a6a;
  font-family: 'trebuchet ms', verdana, sans-serif;
}

#preview {
	float: right; 
	height: 800px; 
	min-width: 400px; 
	width: 60%; 
	border: 1px solid #666; 
	margin-left: 1em;
  -moz-box-shadow: 0px 0px 6px rgba(0,0,0,0.5);
  -webkit-box-shadow: 0px 0px 6px rgba(0,0,0,0.5);
  box-shadow: 0px 0px 6px rgba(0,0,0,0.5);
}

table.setup {
	border: 1px solid #ccc;
	border-collapse: collapse;
}

table.setup td,
table.setup th {
  border: 1px solid #ccc;
}

table.setup th {
  background-color: #ddd;
}

table.setup td.title {
  background-color: #f6f6f6;
}

table.setup td.ok,
table.setup tr:hover td.ok {
	background-color: #9e4;
}

table.setup td.failed,
table.setup tr:hover td.failed {
  background-color: #f43;
	color: white;
}

table.setup td.warning,
table.setup tr:hover td.warning {
  background-color: #FCC612;
}

table.setup tr:hover td {
  background-color: #EBF1F7;
}

table.setup tr:hover td.title {
  background-color: #D0E0F2;
}

input[type="file"] {
	width: 30em;
}

/* Method definitions from phpdoc */
.method-definition {
  background-image: url('images/h_bar.gif');
  background-position: bottom center;
  background-repeat: repeat-x;
  padding: 10px 10px 20px 10px;  
  margin-bottom: 1em;
}

.method-title {
  color: #5F83BA;
}

.var-name,
.method-name,
.method-title {
  font-weight: bold;
}

.var-type,
.method-result {
  color: #4A9166;
  font-style: italic;  
}
