<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Bitly_url extends CI_Controller {  
    
    function __construct()
    {
        parent::__construct();
        $this->load->library('bitly');
    }
    
    function index()
    {

        $url = 'http://dev.corneliusdms.com';
        if ($result = $this->bitly->shorten($url)) { 
            echo '<pre>'; print_r($result);
        }
        
        die('End of Shorten url');
    }
}
