<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Driver_controller extends CI_Controller {

  public function __construct() {
        parent::__construct();
    if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
        $this->load->model('driver_model');
    }

    public function index() {
      $data['drivers'] = $this->driver_model->getDrivers();
      $driverId = $this->authorization->getAvatarStakeHolderId();
      $data['things'] = $this->driver_model->getDriverBuses($driverId);
      $data['class_sections'] = $this->driver_model->getClassSections();
      $data['main_content'] = 'transportation/drivers/attendance_new';
      $this->load->view('inc/template', $data);
    }

    public function getDriverBuses() {
      $driver_id = $_POST['driver_id'];
      $things = $this->driver_model->getDriverBuses($driver_id);
      echo json_encode($things);
    }

    public function getDriverJourneys() {
      $thing_id = $_POST['thing_id'];
      $journeys = $this->driver_model->getDriverJourneys($thing_id);
      echo json_encode($journeys);
    }

    public function getJourneyStudents() {
      $journeyId = $_POST['journey_id'];
      $students = $this->driver_model->getJourneyStudents($journeyId);
      $stops = $this->driver_model->getJourneyStops($journeyId);
      echo json_encode(array("students" => $students, "stops" => $stops));
    }

    public function addAttendance() {
      $status = $this->driver_model->addAttendance();
      $journeyId = $_POST['journey_id'];
      $students = $this->driver_model->getJourneyStudents($journeyId);
      echo json_encode($students);
    }

    public function getSectionStudents() {
      $section_id = $_POST['section_id'];
      $students = $this->driver_model->getSectionStudents($section_id);
      echo json_encode($students);
    }

    public function change_bus() {
      $thing_id = $_POST['thing_id'];
      $new_thing_id = $_POST['new_thing_id'];
      $journey_id = $_POST['journey_id'];
      $status = $this->driver_model->changeBus($thing_id, $new_thing_id, $journey_id);
      echo $status;
    }

    public function getStopById() {
      $stop_id = $_POST['stop_id'];
      $stopData = $this->driver_model->getStopById($stop_id);
      echo json_encode($stopData);
    }

  }
?>