<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  14 May 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Fee_transportation extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('FEES_NH')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('fees/fee_transportation_model');
  }

  //Landing function for Fee Transaction
  public function index ($classId = '') {

     if (empty($classId)){
        $classId = $this->input->post('stud_class');
      }
      if (empty($classId)){
        $classId = 1; 
      }

    $data['selectedClassId'] = $classId; 
    $data['clasess'] = $this->fee_transportation_model->get_all_class();
    // student data
    $data['std_search'] = $this->fee_transportation_model->get_classwise_student_data($classId);
   //echo "<pre>"; print_r($data['std_search']); die();
    $data['main_content'] = 'fees/transportation/fee_index';
    $this->load->view('inc/template', $data);

  }

  public function fee_select($std_id,$classId){

    $data['variants'] = $this->fee_transportation_model->get_varients_names($std_id);
    $data['prev_trans'] = $this->fee_transportation_model->previousFeeTrans($std_id);
    $data['amount'] = $data['variants']->price -  $data['prev_trans']->paidAmount;
  
    if (empty($data['variants'])) {
      $this->session->set_flashdata('flashInfo', 'Assign stop to student before collecting Fees');
      redirect('fees/fee_transportation');
    }
    $feeConfig = $this->settings->getSetting('fees');
    $data['payment_modes'] = $feeConfig['allowed_payment_modes'];
    $data['selectedClassId'] = $classId;
    // fetch data from selected student Id;
    $data['std_details'] = $this->fee_transportation_model->get_student_detail($std_id);
    $data['main_content'] = 'fees/transportation/transaction/index';
    $this->load->view('inc/template', $data);
  }

  public function submit_feethree_transaction($std_id,$classId){ 
 // echo "<pre>"; print_r($this->input->post()); die();  

    $pAmount =  $this->input->post('amount');
    $previousCalucate =  $this->input->post('trans_amount') - $this->input->post('preAmount');

    if($pAmount <= $previousCalucate) {
      $receiptNo = $this->fee_transportation_model->fee_variants_receiptNo();
      $result = $this->fee_transportation_model->insert_variants($std_id,$receiptNo);
      if($result){ 
        //$this->session->set_flashdata('flashSuccess', 'Successfully inserted');
        redirect('fees/fee_transportation/fee_receipt/'.$receiptNo.'/'.$classId.'/'.'1'.'/'.$std_id);
      }
    }else{
      $this->session->set_flashdata('flashError', 'Please check enter amount');
      redirect('fees/fee_transportation/fee_select/'.$std_id.'/'.$classId);
    } 
  
  }

  public function fee_receipt($receiptNo,$classId,$traverse_to,$std_id){
    $data['traverse_to'] = $traverse_to;
    $data['std_id'] = $std_id;
    $feeConfig = $this->settings->getSetting('fees');
    $rTemplate = $feeConfig['receipt_template_trans'];
    $data['paymentType'] = $feeConfig['allowed_payment_modes'];
    $data['selectedClassId'] = $classId;
    $data['misc_details'] = $this->fee_transportation_model->get_misecellaneousFees($receiptNo);
    //echo "<pre>"; print_r($data['misc_details']); die();
    //echo "<pre>"; print_r($data['misc_details']); die();
    //echo "<pre>"; print_r($data['misc_details']); die();
   // TODO PDF Created
    // $this->load->library('pdfmanager');
    // $file = $this->pdfmanager->generatePdf($data,'exportPdf');
    
    // $this->load->library('filemanager');
    // $this->filemanager->uploadFile($file['path'],$file['filename']); 

    // $file_path = $this->filemanager->getFilePath($file['filename']); 
    // $this->fee_transaction_two->update_pdfpath_url($fee_id,$file_path);
    $data['main_content'] = 'receipts/'.$rTemplate;
    $this->load->view('inc/template', $data);
  }

  public function history($std_id, $traverse_to,$classId){
    $data['std_id'] = $std_id;
    $data['traverse_to'] = $traverse_to;
    $data['selectedClassId'] = $classId;
    $data['misc_details'] = $this->fee_transportation_model->get_misecellaneousFeesbyStdId($std_id);
    $data['main_content'] = 'fees/transportation/history';
    $this->load->view('inc/template', $data);
  }

}

