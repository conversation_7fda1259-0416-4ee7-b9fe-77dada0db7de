<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN"
   "http://www.w3.org/TR/REC-html40/strict.dtd">
<html lang=en>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>HTML 4.0 Special Entities</title>
<link rev=Made href="mailto:<EMAIL>">
<link rel=Start href="../index.html">
<link rel=Prev href="symbols.html">
<style>
body {
  background: white;
  color: black;
  font-family: sans-serif;
}

h1 {
  color: #c33;
  background: none;
  font-weight: bold;
  text-align: center
}

h2 {
  color: #00008b;
  background: none;
  font-weight: bold
}

h3 {
  color: #006400;
  background: none;
  margin-left: 4%;
  margin-right: 4%;
  font-weight: bold
}

h4 {
  margin-left: 6%;
  margin-right: 6%;
  font-weight: bold
}

h5 {
  margin-left: 6%;
  margin-right: 6%;
  font-weight: bold
}

ul, ol, dl, p {
  margin-left: 6%;
  margin-right: 6%
}

ul ul, table ol, table ul, dl ol, dl ul {
  margin-left: 1.2em;
  margin-right: 1%;
  padding-left: 0
}

pre {
  margin-left: 10%;
  white-space: pre
}

table caption {
  font-size: larger;
  font-weight: bolder
}

table p, table dl, ol p, ul p, dl p, blockquote p, .note p, .note ul, .note ol, .note dl, li pre, dd pre {
  margin-left: 0;
  margin-right: 0
}

p.top {
  margin-left: 1%;
  margin-right: 1%
}

blockquote {
  margin-left: 8%;
  margin-right: 8%;
  border: thin ridge #dc143c
}

blockquote pre {
  margin-left: 1%;
  margin-right: 1%
}

dt a {
  font-weight: bold;
  margin-top: .8em
}

a:link {
  color: #00f;
  background: none;
}

a:visited {
  color: #800080;
  background: none;
}

a:active {
  color: green;
  background: #FFD700
}

.html {
  color: #000080;
  background: none
}

.css {
  color: #800000;
  background: none
}

.javascript {
  color: #008000;
  background: none
}

.example { margin-left: 10% }

dfn {
  font-style: normal;
  font-weight: bolder
}

var sub { font-style: normal }

.note {
  font-size: 85%;
  margin-left: 10%
}

.SMA {
  color: fuchsia;
  background: none;
  font-family: Kids, "Comic Sans MS", Jester
}

.oops {
  font-family: Jester, "Comic Sans MS"
}

.author {
  font-style: italic
}

.copyright {
  font-size: smaller;
  text-align: right;
  clear: right
}

.toolbar {
  text-align: center
}

.toolbar IMG {
  float: right
}

.error {
  color: #DC143C;
  background: none;
  text-decoration: none
}

.warning {
  color: #FF4500;
  background: none;
  text-decoration: none
}

.error strong {
  color: #DC143C;
  background: #FFD700;
  text-decoration: none
}

.warning strong {
  color: #FF4500;
  background: #FFD700;
  text-decoration: none
}

.warning a:link, .warning a:visited, .warning a:active {
  color: #FF4500;
  background: none;
  text-decoration: underline
}

.error a:link, .error a:visited, .error a:active {
  color: #DC143C;
  background: none;
  text-decoration: underline
}

.error strong a:link, .error strong a:visited, .error strong a:active {
  color: #DC143C;
  background: #FFD700
}

.warning strong a:link, .warning strong a:visited, .warning strong a:active {
  color: #FF4500;
  background: #FFD700
}

colgroup.entity { text-align: center }

.default { text-decoration: underline; font-style: normal }
.required { font-weight: bold }
td li.transitional, .elements li.transitional {
  font-weight: lighter;
  color: #696969;
  background: none
}
td li.frameset, .elements li.frameset {
  font-weight: lighter;
  color: #808080;
  background: none
}

.footer, .checkedDocument {
  margin-top: 2em;
  padding-top: 1em;
  border-top: solid thin black
}

strong.legal {
  font-weight: normal;
  text-transform: uppercase
}

@media print {
  input#toggler, .toolbar { display: none }
}

table { border-collapse: collapse; width: 100%; }
td { border: 0.5pt solid black; }

</style>
<meta name="author" content="Liam Quinn">
<meta name="description" content="A table of the HTML 4.0 entities for markup-significant and internationalization characters.">
<meta name="keywords" content="internationalization, internationalisation, i18n, I18N, entities, characters, character set, HTML, HyperText Markup Language, HTML 4.0, HTML4, character entity reference, decimal, hexadecimal, hex, browser test, WDG, Web Design Group">
<body>
<h1>Special Entities</h1>
<p>The following table gives the character entity reference, decimal character reference, and hexadecimal character reference for markup-significant and internationalization characters, as well as the rendering of each in your browser. <a href="http://www.unicode.org/charts/">Glyphs</a> of the characters are available at the <a href="http://www.unicode.org/">Unicode Consortium</a>.</p>
<p>With the exception of <a href="http://www.w3.org/MarkUp/html-spec/">HTML 2.0</a>'s <strong class=html>&amp;quot;</strong>, <strong class=html>&amp;amp;</strong>, <strong class=html>&amp;lt;</strong>, and <strong class=html>&amp;gt;</strong>, browser support for these entities is generally quite poor, but recent browsers support some of the character entity references and decimal character references.</p>
<table>
  <thead>
    <tr>
      <th scope=col rowspan=2>Character</th>
      <th scope=col rowspan=2>Entity</th>
      <th scope=col rowspan=2>Decimal</th>
      <th scope=col rowspan=2>Hex</th>
      <th scope=colgroup colspan=3>Rendering in Your Browser</th>
    </tr>
    <tr>
      <th scope=col>Entity</th>
      <th scope=col>Decimal</th>
      <th scope=col>Hex</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td scope=row>quotation mark = APL quote</td>
      <td>&amp;quot;</td>
      <td>&amp;#34;</td>
      <td>&amp;#x22;</td>
      <td>&quot;</td>
      <td>&#34;</td>
      <td>&#x22;</td>
    </tr>
    <tr>
      <td scope=row>ampersand</td>
      <td>&amp;amp;</td>
      <td>&amp;#38;</td>
      <td>&amp;#x26;</td>
      <td>&amp;</td>
      <td>&#38;</td>
      <td>&#x26;</td>
    </tr>
    <tr>
      <td scope=row>less-than sign</td>
      <td>&amp;lt;</td>
      <td>&amp;#60;</td>
      <td>&amp;#x3C;</td>
      <td>&lt;</td>
      <td>&#60;</td>
      <td>&#x3C;</td>
    </tr>
    <tr>
      <td scope=row>greater-than sign</td>
      <td>&amp;gt;</td>
      <td>&amp;#62;</td>
      <td>&amp;#x3E;</td>
      <td>&gt;</td>
      <td>&#62;</td>
      <td>&#x3E;</td>
    </tr>
    <tr>
      <td scope=row>Latin capital ligature OE</td>
      <td>&amp;OElig;</td>
      <td>&amp;#338;</td>
      <td>&amp;#x152;</td>
      <td>&OElig;</td>
      <td>&#338;</td>
      <td>&#x152;</td>
    </tr>
    <tr>
      <td scope=row>Latin small ligature oe</td>
      <td>&amp;oelig;</td>
      <td>&amp;#339;</td>
      <td>&amp;#x153;</td>
      <td>&oelig;</td>
      <td>&#339;</td>
      <td>&#x153;</td>
    </tr>
    <tr>
      <td scope=row>Latin capital letter S with caron</td>
      <td>&amp;Scaron;</td>
      <td>&amp;#352;</td>
      <td>&amp;#x160;</td>
      <td>&Scaron;</td>
      <td>&#352;</td>
      <td>&#x160;</td>
    </tr>
    <tr>
      <td scope=row>Latin small letter s with caron</td>
      <td>&amp;scaron;</td>
      <td>&amp;#353;</td>
      <td>&amp;#x161;</td>
      <td>&scaron;</td>
      <td>&#353;</td>
      <td>&#x161;</td>
    </tr>
    <tr>
      <td scope=row>Latin capital letter Y with diaeresis</td>
      <td>&amp;Yuml;</td>
      <td>&amp;#376;</td>
      <td>&amp;#x178;</td>
      <td>&Yuml;</td>
      <td>&#376;</td>
      <td>&#x178;</td>
    </tr>
    <tr>
      <td scope=row>modifier letter circumflex accent</td>
      <td>&amp;circ;</td>
      <td>&amp;#710;</td>
      <td>&amp;#x2C6;</td>
      <td>&circ;</td>
      <td>&#710;</td>
      <td>&#x2C6;</td>
    </tr>
    <tr>
      <td scope=row>small tilde</td>
      <td>&amp;tilde;</td>
      <td>&amp;#732;</td>
      <td>&amp;#x2DC;</td>
      <td>&tilde;</td>
      <td>&#732;</td>
      <td>&#x2DC;</td>
    </tr>
    <tr>
      <td scope=row>en space</td>
      <td>&amp;ensp;</td>
      <td>&amp;#8194;</td>
      <td>&amp;#x2002;</td>
      <td>&ensp;</td>
      <td>&#8194;</td>
      <td>&#x2002;</td>
    </tr>
    <tr>
      <td scope=row>em space</td>
      <td>&amp;emsp;</td>
      <td>&amp;#8195;</td>
      <td>&amp;#x2003;</td>
      <td>&emsp;</td>
      <td>&#8195;</td>
      <td>&#x2003;</td>
    </tr>
    <tr>
      <td scope=row>thin space</td>
      <td>&amp;thinsp;</td>
      <td>&amp;#8201;</td>
      <td>&amp;#x2009;</td>
      <td>&thinsp;</td>
      <td>&#8201;</td>
      <td>&#x2009;</td>
    </tr>
    <tr>
      <td scope=row>zero width non-joiner</td>
      <td>&amp;zwnj;</td>
      <td>&amp;#8204;</td>
      <td>&amp;#x200C;</td>
      <td>&zwnj;</td>
      <td>&#8204;</td>
      <td>&#x200C;</td>
    </tr>
    <tr>
      <td scope=row>zero width joiner</td>
      <td>&amp;zwj;</td>
      <td>&amp;#8205;</td>
      <td>&amp;#x200D;</td>
      <td>&zwj;</td>
      <td>&#8205;</td>
      <td>&#x200D;</td>
    </tr>
    <tr>
      <td scope=row>left-to-right mark</td>
      <td>&amp;lrm;</td>
      <td>&amp;#8206;</td>
      <td>&amp;#x200E;</td>
      <td>&lrm;</td>
      <td>&#8206;</td>
      <td>&#x200E;</td>
    </tr>
    <tr>
      <td scope=row>right-to-left mark</td>
      <td>&amp;rlm;</td>
      <td>&amp;#8207;</td>
      <td>&amp;#x200F;</td>
      <td>&rlm;</td>
      <td>&#8207;</td>
      <td>&#x200F;</td>
    </tr>
    <tr>
      <td scope=row>en dash</td>
      <td>&amp;ndash;</td>
      <td>&amp;#8211;</td>
      <td>&amp;#x2013;</td>
      <td>&ndash;</td>
      <td>&#8211;</td>
      <td>&#x2013;</td>
    </tr>
    <tr>
      <td scope=row>em dash</td>
      <td>&amp;mdash;</td>
      <td>&amp;#8212;</td>
      <td>&amp;#x2014;</td>
      <td>&mdash;</td>
      <td>&#8212;</td>
      <td>&#x2014;</td>
    </tr>
    <tr>
      <td scope=row>left single quotation mark</td>
      <td>&amp;lsquo;</td>
      <td>&amp;#8216;</td>
      <td>&amp;#x2018;</td>
      <td>&lsquo;</td>
      <td>&#8216;</td>
      <td>&#x2018;</td>
    </tr>
    <tr>
      <td scope=row>right single quotation mark</td>
      <td>&amp;rsquo;</td>
      <td>&amp;#8217;</td>
      <td>&amp;#x2019;</td>
      <td>&rsquo;</td>
      <td>&#8217;</td>
      <td>&#x2019;</td>
    </tr>
    <tr>
      <td scope=row>single low-9 quotation mark</td>
      <td>&amp;sbquo;</td>
      <td>&amp;#8218;</td>
      <td>&amp;#x201A;</td>
      <td>&sbquo;</td>
      <td>&#8218;</td>
      <td>&#x201A;</td>
    </tr>
    <tr>
      <td scope=row>left double quotation mark</td>
      <td>&amp;ldquo;</td>
      <td>&amp;#8220;</td>
      <td>&amp;#x201C;</td>
      <td>&ldquo;</td>
      <td>&#8220;</td>
      <td>&#x201C;</td>
    </tr>
    <tr>
      <td scope=row>right double quotation mark</td>
      <td>&amp;rdquo;</td>
      <td>&amp;#8221;</td>
      <td>&amp;#x201D;</td>
      <td>&rdquo;</td>
      <td>&#8221;</td>
      <td>&#x201D;</td>
    </tr>
    <tr>
      <td scope=row>double low-9 quotation mark</td>
      <td>&amp;bdquo;</td>
      <td>&amp;#8222;</td>
      <td>&amp;#x201E;</td>
      <td>&bdquo;</td>
      <td>&#8222;</td>
      <td>&#x201E;</td>
    </tr>
    <tr>
      <td scope=row>dagger</td>
      <td>&amp;dagger;</td>
      <td>&amp;#8224;</td>
      <td>&amp;#x2020;</td>
      <td>&dagger;</td>
      <td>&#8224;</td>
      <td>&#x2020;</td>
    </tr>
    <tr>
      <td scope=row>double dagger</td>
      <td>&amp;Dagger;</td>
      <td>&amp;#8225;</td>
      <td>&amp;#x2021;</td>
      <td>&Dagger;</td>
      <td>&#8225;</td>
      <td>&#x2021;</td>
    </tr>
    <tr>
      <td scope=row>per mille sign</td>
      <td>&amp;permil;</td>
      <td>&amp;#8240;</td>
      <td>&amp;#x2030;</td>
      <td>&permil;</td>
      <td>&#8240;</td>
      <td>&#x2030;</td>
    </tr>
    <tr>
      <td scope=row>single left-pointing angle quotation mark</td>
      <td>&amp;lsaquo;</td>
      <td>&amp;#8249;</td>
      <td>&amp;#x2039;</td>
      <td>&lsaquo;</td>
      <td>&#8249;</td>
      <td>&#x2039;</td>
    </tr>
    <tr>
      <td scope=row>single right-pointing angle quotation mark</td>
      <td>&amp;rsaquo;</td>
      <td>&amp;#8250;</td>
      <td>&amp;#x203A;</td>
      <td>&rsaquo;</td>
      <td>&#8250;</td>
      <td>&#x203A;</td>
    </tr>
    <tr>
      <td scope=row>euro sign</td>
      <td>&amp;euro;</td>
      <td>&amp;#8364;</td>
      <td>&amp;#x20AC;</td>
      <td>&euro;</td>
      <td>&#8364;</td>
      <td>&#x20AC;</td>
    </tr>
  </tbody>
</table>

<div class=footer>
<address>Maintained by <a href="http://www.htmlhelp.com/%7Eliam/">Liam Quinn</a> &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</address>
<p class=toolbar><a href="../index.html" rel=Start>HTML&#160;4.0&#160;Reference</a>&#160;~ <a href="latin1.html">Latin-1&#160;Characters</a>&#160;~ <a href="symbols.html" rel=Prev>Symbols&#160;and&#160;Greek&#160;Letters</a></p>
<p class=copyright>Copyright &copy; 1998 by <a href="http://www.htmlhelp.com/%7Eliam/">Liam Quinn</a>. This material may be distributed only subject to the terms and conditions set forth in the Open Publication License, v1.0 or later (the latest version is presently available at <a href="http://www.opencontent.org/openpub/">http://www.opencontent.org/openpub/</a>).</p>
<p class=copyright>Modfications made by Benj Carson <a><EMAIL></a> for dompdf, Jan 5, 2006.</p>
</div>
</body>
</html>
