<?php
class ParentTicket_dashboard extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if(!$this->authorization->isAuthorized('PARENT_TICKETING.MODULE')) {
      redirect('dashboard', 'refresh');
  }
  }

  function index() {
    $site_url = site_url();
    $data['ticket_tiles'] = array(
      [
        'title' => 'Add Ticket',
        'sub_title' => '',
        'icon' => 'svg_icons/parentticketcategory.svg',
        'url' => $site_url.'parent_ticketing/add_tickting',
        'permission' => $this->authorization->isAuthorized('PARENT_TICKETING.ADD_OFFLINE_TICKET') && $this->authorization->isModuleEnabled('PARENT_TICKETING')
      ],  
      [
        'title' => 'Manage Tickets',
        'sub_title' => '',
        'icon' => 'svg_icons/parenttickets.svg',
        'url' => $site_url.'parent_ticketing/view_assigned_tickets',
        'permission' => $this->authorization->isAuthorized('PARENT_TICKETING.MODULE') && $this->authorization->isModuleEnabled('PARENT_TICKETING')
      ]);
    $data['ticket_tiles'] = checkTilePermissions($data['ticket_tiles']);

      $data['report_tiles'] = array(
        [
          'title' => 'Ticket Analytics',
          'sub_title' => '',
          'icon' => 'svg_icons/parentticketcategory.svg',
          'url' => $site_url.'parent_ticketing/parent_report',
          'permission' => $this->authorization->isAuthorized('PARENT_TICKETING.TICKET_ANALYTICS') && $this->authorization->isModuleEnabled('PARENT_TICKETING')
        ],
        [
            'title' => 'Staff Ticket Report',
            'sub_title' => '',
            'icon' => 'svg_icons/parentticketcategory.svg',
            'url' => $site_url.'parent_ticketing/ticketing_staff_report',
            'permission' => $this->authorization->isAuthorized('PARENT_TICKETING.STAFF_TICKET_REPORT') && $this->authorization->isModuleEnabled('PARENT_TICKETING')
          ],
          [
              'title' => 'Student Ticket Report',
              'sub_title' => '',
              'icon' => 'svg_icons/parentticketcategory.svg',
              'url' => $site_url.'parent_ticketing/student_report',
              'permission' => $this->authorization->isAuthorized('PARENT_TICKETING.STUDENT_TICKET_REPORT') && $this->authorization->isModuleEnabled('PARENT_TICKETING')
            ]);
      $data['report_tiles'] = checkTilePermissions($data['report_tiles']);
  
      $data['admin_tiles'] = array(
        [
          'title' => 'Ticket Category',
          'sub_title' => '',
          'icon' => 'svg_icons/parentticketcategory.svg',
          'url' => $site_url.'parent_ticketing/category_index',
          'permission' => $this->authorization->isAuthorized('PARENT_TICKETING.MODULE') && $this->authorization->isModuleEnabled('PARENT_TICKETING')
        ]);
        $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);

          $data['main_content']    = 'ticketing/parent/menu';
          $this->load->view('inc/template', $data);
    }


    
}