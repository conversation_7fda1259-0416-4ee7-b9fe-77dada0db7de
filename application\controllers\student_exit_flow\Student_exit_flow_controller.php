<?php

class Student_exit_flow_controller extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_EXIT_FLOW_STAFF')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT_EXIT_FLOW_STAFF.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('staff/Staff_Model');
    $this->load->library('filemanager');
    $this->load->model('class_section');
    $this->load->model('student/Student_Model');
    $this->load->model('student/Certificates_Model');
    $this->load->model('student_exit_flow/Student_exit_flow_model');
		$this->load->model('parent_model');
    $this->load->model('staff/Staff_leave', 'staff_leave');
    $this->load->model('role');
  }

  public function index() {
    $site_url = site_url();
    $data['adm_tiles'] = array(
      [
        'title' => 'Manage Exit',
        'sub_title' => 'View From Parentside',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_exit_flow/Student_exit_flow_controller/student_exitflow',  
        'permission' =>$this->authorization->isAuthorized('STUDENT_EXIT_FLOW_STAFF.MODULE') && $this->authorization->isAuthorized('STUDENT_EXIT_FLOW_STAFF.ADMIN')
      ],
      [
        'title' => 'Issue NOC',
        'sub_title' => 'Staff member can issue NOC to the students',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url . 'student_exit_flow/Student_exit_flow_controller/issue_student_exit_noc',
        'permission' => $this->authorization->isAuthorized('STUDENT_EXIT_FLOW_STAFF.MODULE')
      ]
    );
        
    $data['adm_tiles'] = checkTilePermissions($data['adm_tiles']);

    $data['admin_tiles'] = array(
      [
        'title' => 'Exit Report',
        'sub_title' => 'View From Parentside',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url . 'student_exit_flow/Student_exit_flow_controller/student_exitreport',
        'permission' => $this->authorization->isAuthorized('STUDENT_EXIT_FLOW_STAFF.MODULE')
      ],
      [
        'title' => 'Assign Default NOC Approver',
        'sub_title' => '',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_exit_flow/Student_exit_flow_controller/access_controller',  
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Manage Exit Reasons',
        'sub_title' => '',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url . 'student_exit_flow/Student_exit_flow_controller/manage_stduent_exit_reasons',
        'permission' => $this->authorization->isSuperAdmin()
      ]
    );
    $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);
    $data['main_content']    = 'student_exit_flow/index';
    $this->load->view('inc/template', $data);
  }

  public function student_exitflow(){
	  $data["student_terminate_reasons"] = $this->parent_model->getStudentTerminateReasons();

    $data['getclassinfo'] = $this->Student_exit_flow_model->getClassSectionNames();
    $data['staff_list'] = $this->Student_exit_flow_model->get_approved_staff_list();
    $data['admin_permission'] = $this->authorization->isAuthorized('STUDENT_EXIT_FLOW_STAFF.ADMIN');

    // Get current and next academic year ids
		$data['acad_year_id_applied_in'] = (int)$this->settings->getSetting("academic_year_id");
		if($data['acad_year_id_applied_in']<=0){
			$data['acad_year_id_applied_in']=0;
		}

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student_exit_flow/student_exitflow_mobile';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student_exit_flow/student_exitflow_mobile';
    } else {
      $data['main_content'] = 'student_exit_flow/student_exitflow';
    }
    $this->load->view('inc/template', $data);
  }

  public function isStudentsPromotionClassExists(){
    $studentId=$this->input->post("studentId");
    echo $this->Student_exit_flow_model->isStudentsPromotionClassExists($studentId);
  }

  public function canStaffApplyTCForNextYear(){
    $studentId=$this->input->post("studentId");
    echo $this->Student_exit_flow_model->canStaffApplyTCForNextYear($studentId);
  }

  public function get_student_by_class_section(){
    $classSectionId = $this->input->post('classSectionId');
    $result = $this->Student_exit_flow_model->get_student_by_class_sectionbyid_studentexit($classSectionId);
    // echo "<pre>"; print_r($result); die();
    echo json_encode($result);
  }

  public function get_exit_student_list(){
    $result = $this->Student_exit_flow_model->get_exit_student_list();
    // echo "<pre>"; print_r($result); die();
    echo json_encode($result);
  }

  public function save_std_exit(){    
    // check if any pending tc application pending
	  $is_any_un_rejected_tc_exists=$this->parent_model->is_any_un_rejected_tc_exists(["student_id" => $_POST["selectStudents"]]);
    if($is_any_un_rejected_tc_exists==1){
      $this->session->set_flashdata('flashSuccess', 'Your TC request already exists');
      redirect('student_exit_flow/Student_exit_flow_controller/student_exitflow');
      return;
    }

    $result = $this->Student_exit_flow_model->save_student_exit_flow();

    if ($result) {
      $student_id=$_POST["selectStudents"];
      $student_info = $this->Student_exit_flow_model->get_exit_student_detail($student_id);

      if(!empty($student_info)){
        $student_name=$student_info->student_name;
        $class_section = $student_info->class_section;
        $message= "New Student '". $student_name ."' of class '". $class_section ."' is added for exit.";;
      }else{
        $message = "New Student is added for exit.";
      }

      $admins = $this->role->getStaffListByPrivilege('STUDENT_EXIT_FLOW_STAFF', 'ADMIN');
      
      $member_ids = $this->Student_exit_flow_model->get_save_student_exit_flow($result);
      
      $member_ids=array_merge($admins,$member_ids);

      if(!empty($member_ids)) {
        $this->load->helper('texting_helper');
        $input_array = array(
          'mode' => 'notification', 
          'title' => 'Student Exit Flow Approval Pending', 
          'source' => 'Student Exit Flow',
          'is_unicode' => '0',
          'visible' => 1,
          'staff_ids' => $member_ids,
          'message' => $message
        );
        $response = sendText($input_array);

        // send email-notification
        $fromMail = $this->settings->getSetting("tc_request_notification_from_email");
        if (strlen($fromMail)) {
          $subject = "Received TC application request";
          $body = $message;
          $receivers = $member_ids;
          // SENDING EMAILS TO CONCERNED STAFF MEMBERS AND ADMIN
          $this->sendStudentExitApplicationEmailToStaff($subject, $body, $fromMail, $receivers);
        }
      }
    }else{
      // display error message
      $this->session->set_flashdata('flashError', $result["message"]);
    }
    redirect('student_exit_flow/Student_exit_flow_controller/student_exitflow');
  }

  public function get_std_details(){
    $id = $this->input->post('id');
    $result = $this->Student_exit_flow_model->get_std_details($id);
    // echo "<pre>"; print_r($result); die();
    echo json_encode($result);

  }

  public function insert_approver_list_data(){
    $primaryId = $this->input->post('primaryId');
    $noc_type = $this->input->post('noc_type');
    $noc_staff_id = $this->input->post('noc_staff_id');
    $admin_remarks = $this->input->post('admin_remarks');
    $result = $this->Student_exit_flow_model->insert_approver_list_data($primaryId, $noc_type, $noc_staff_id, $admin_remarks);

   if ($result) {

      if(!empty($noc_staff_id)) {
        $this->load->helper('texting_helper');
        $input_array = array(
          'mode' => 'notification', 
          'title' => 'Student Exit Flow Approval Pending', 
          'source' => 'Student Exit Flow',
          'is_unicode' => '0',
          'visible' => 1,
          'staff_ids' =>(array)$noc_staff_id,
          'message' => 'New Student is added for Approval.'
        );

        $response = sendText($input_array);

    }
  }

    echo json_encode($result);
  }

  public function delete_staff_approval(){
    $id = $this->input->post('id');
    $primaryId = $this->input->post('primaryId');
    $noc_type = $this->input->post('noc_type');
    echo $this->Student_exit_flow_model->delete_staff_approval($id, $primaryId, $noc_type);
  }

  public function rejected_exit_flow(){
    $stdId = $this->input->post('stdId');
    $remarks = $this->input->post('remarks');
    $result = $this->Student_exit_flow_model->rejected_exit_flow($stdId, $remarks);
    echo json_encode($result);
  }

  public function get_student_exit_fees_details(){
    // getting student fee status for noc purpose
    $studentTerminateId = $this->input->post('id');
    $result = $this->Student_exit_flow_model->get_student_exit_fees_details($studentTerminateId);
    echo json_encode($result);
  }

  public function get_student_exit_library_details(){
    $id = $this->input->post('id');
    $result = $this->Student_exit_flow_model->get_student_exit_library_details($id);
    echo json_encode($result);
  }

  public function final_approval_submit_staff(){
    $id = $this->input->post('id');
    $remarks = $this->input->post('remarks');
    $result = $this->Student_exit_flow_model->final_approval_submit_staff($id, $remarks);
    echo json_encode($result);
  }

  public function getFatherUsername(){
    $stdId = $this->input->post('stdId');
    $result = $this->Student_exit_flow_model->getFatherUsername($stdId);
    echo json_encode($result);
  }

  public function getMotherUsername(){
    $stdId = $this->input->post('stdId');
    $result = $this->Student_exit_flow_model->getFatherUsername($stdId);
    echo json_encode($result);
  }

  public function final__submit(){
    $stdId = $_POST['stdId'];
    $fatherUserName = $_POST['fatherUserName'];
    $motherUserName = $_POST['motherUserName'];
    $terminate_date = $_POST['terminate_date'];
    $terminate_remarks = $_POST['terminate_remarks'];
    $tc_number = $_POST['tc_number'];
    $user_login_status = $_POST['user_login_status'];
    $term_completion_status = $_POST['termComplitionStatus'];
    $result = $this->Student_exit_flow_model->assign_alumnibyStdId($stdId, $fatherUserName, $motherUserName, $terminate_date, $terminate_remarks, $tc_number, $user_login_status, $term_completion_status);
    echo json_encode($result);
  }

  public function final_approval_button(){
    $id = $this->input->post('id');
    $result = $this->Student_exit_flow_model->final_approval_button($id);
    echo $result;
  }

   public function getStudentTcNumberBeforeApproval(){
    $id = $this->input->post('id');
    $result = $this->Student_exit_flow_model->getStudentTcNumberBeforeApproval($id);
    echo json_encode($result);
  }

  function get_final_approved_student_details(){
    $stdId = $this->input->post('stdId');
    $result = $this->Student_exit_flow_model->get_termniate_student_tc_number($stdId);
    echo json_encode($result);
  }

  function get_noc_selection_type(){
    $primaryId = $this->input->post('primaryId');
    $result = $this->Student_exit_flow_model->get_noc_selection_type_list($primaryId);
    $nocType = array(
      'Fees'=>'Fees',
      'Library'=>'Library',
      'Principal'=>'Principal',
      'Vice Principal'=>'Vice Principal',
      'Class Teacher'=>'Class Teacher',
      'Other 1'=>'Other 1',
      'Other 2'=>'Other 2',
      'Other 3'=>'Other 3'    
      
    );
    $differences = array_merge(array_diff($nocType, $result), array_diff($result, $nocType));
    echo json_encode($differences);
  }

  public function access_controller(){
    $data['staff_list'] = $this->Student_exit_flow_model->get_approved_staff_list();
    $data['main_content']    = 'student_exit_flow/access_control';
    $this->load->view('inc/template', $data);
  }

  public function manage_stduent_exit_reasons(){
    $data["main_content"]= "student_exit_flow/manage_student_exit_reasons/desktop_manage_exit_reasons";
    $this->load->view("inc/template",$data);
  }

  public function getStudentExitReasons(){
    $response=$this->Student_exit_flow_model->getStudentExitReasons();
    echo json_encode($response);
  }
  public function addStudentExitReason(){
    echo $this->Student_exit_flow_model->addStudentExitReason($_POST);
  }

  public function changeStudentExitReasonStatus(){
    echo $this->Student_exit_flow_model->changeStudentExitReasonStatus($_POST);
  }

  public function save_access_control() {
   $result = $this->Student_exit_flow_model->save_access_control();
   if ($result) {
      // $this->session->set_flashdata('flashSuccess', 'Added Successful');
    redirect('student_exit_flow/Student_exit_flow_controller/access_controller');

    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    redirect('student_exit_flow/Student_exit_flow_controller/access_controller');
      
    }
  }

  public function get_access_control_list(){
    $result = $this->Student_exit_flow_model->get_access_control();
    echo json_encode($result);
  }

  public function get_access_control_by_id() {
    $id = $_POST['id'];
    $result = $this->Student_exit_flow_model->get_access_control_by_id($id);
    echo json_encode($result);
  }

  public function update_access_control_id(){
    $edit_access_control_id = $_POST['edit_access_control_id'];
    $edit_type = $_POST['edit_type'];
    $edit_staff_id = $_POST['edit_staff_id'];
    $edit_remarks = $_POST['edit_remarks'];
    echo $this->Student_exit_flow_model->update_access_control($edit_access_control_id,$edit_type,$edit_staff_id, $edit_remarks);
  }

  public function delete_access_control_id() {
    $access_control_id = $_POST['access_control_id'];
    $success = $this->Student_exit_flow_model->delete_access_control_id($access_control_id);
    echo json_encode($success);
  }

  public function edit_staff_approval(){
    $id = $_POST['id'];
    $result = $this->Student_exit_flow_model->edit_staff_approval($id);
    echo json_encode($result);
  }

  public function update_staff_approval(){
    $edit_staff_approval_id = $_POST['edit_staff_approval_id'];
    $edit_type = $_POST['edit_type'];
    $edit_staff_id = $_POST['edit_staff_id'];
    $edit_approver_remarks = $_POST['edit_approver_remarks'];
    $edit_primaryId = $_POST['edit_primaryId'];
    echo $this->Student_exit_flow_model->update_staff_approval($edit_staff_approval_id,$edit_type,$edit_staff_id, $edit_approver_remarks, $edit_primaryId);
  }

  public function tc_documents_show($flag = 0){
    $stdId = $_POST['stdId'];
    $data['student_uid'] = $stdId;
    $data['certificates'] = $this->Certificates_Model->getCertificates($stdId);

    // echo "<pre>"; print_r($data); die();
    echo json_encode($data);
  }

  public function pdf_download($certificate_id, $template_name){
    $link = $this->Certificates_Model->getPDFLink($certificate_id);
    $url = $this->filemanager->getFilePath($link);
    $data = file_get_contents($url);
    $cert_name = $template_name. ' .pdf';
    $this->load->helper('download');
    force_download($cert_name, $data, TRUE);
  }

  public function student_exit_flow_historydetails() {
    $id = $_POST['id'];
    $result = $this->Student_exit_flow_model->student_exit_flow_historydetails($id);
    echo json_encode($result);
  }

  public function get_noc_selection_type_settings(){
    $result = $this->Student_exit_flow_model->get_noc_selection_type_list_settings();
    $nocType = array(
      'Fees'=>'Fees',
      'Library'=>'Library',
      'Principal'=>'Principal',
      'Vice Principal'=>'Vice Principal',
      'Class Teacher'=>'Class Teacher',
      'Other 1'=>'Other 1',
      'Other 2'=>'Other 2',
      'Other 3'=>'Other 3'    
      
    );
    $differences = array_merge(array_diff($nocType, $result), array_diff($result, $nocType));
    echo json_encode($differences);
  }

  public function student_exitreport(){
    $data['staff_list'] = $this->Student_exit_flow_model->get_approved_staff_list();
    $data['main_content']    = 'student_exit_flow/student_exit_report';
    $this->load->view('inc/template', $data);
  }

  public function getstudent_exit_report(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    
    $result = $this->Student_exit_flow_model->getstudent_exit_report($from_date, $to_date);
    // echo "<pre>"; print_r($result); die();
    echo json_encode($result);
  }

  public function issue_student_exit_noc(){
    $data["main_content"] = "student_exit_flow/issue_student_exit_noc/desktop_issue_student_exit_noc";
    $this->load->view("inc/template", $data);
  }

  public function get_students_pending_noc_assigned_to_me(){
    // echo "<pre>"; print_r($result); die();
    $result = $this->Student_exit_flow_model->get_students_pending_noc_assigned_to_me();
    echo json_encode($result);
  }

  public function issue_noc_to_student_exit(){
    echo $this->Student_exit_flow_model->issue_noc_to_student_exit($_POST);
  }

  private function sendStudentExitApplicationEmailToStaff($subject, $body, $fromMail, $staffs){
    // filter staff ids
    $receivers=[];
    foreach($staffs as $key => $staffId){
      if(empty($staffId)) continue;
      $receivers[]=$staffId;
    }

    $email_master_data = array(
        'subject' => $subject,
        'body' => $body,
        'source' => 'Manual Template',
        'sent_by' => $this->authorization->getAvatarId(),
        'recievers' => implode(',', $receivers),
        'from_email' => $fromMail,
        'files' => NULL,
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'visible' => 1,
        'sender_list' => NULL,
        'sending_status' => 'Send Email'
      );

      $this->load->model('communication/emails_model');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);

      // fetch all the staff mails ids whom the email need to send
      $staffEmails=$this->Student_exit_flow_model->getStaffMailsById($receivers);
      $receiversMailIds=[];

      $sent_data = [];
      if (!empty($email_master_id)) {
        foreach ($receivers as $staffId) {
          $obj = new stdClass();
          $obj->email_master_id = $email_master_id;
          $obj->id = $staffId;
          $obj->email = array_key_exists($staffId,$staffEmails) ? $staffEmails[$staffId] : Null;
          $obj->avatar_type = 4;
          $obj->status = array_key_exists($staffId, $staffEmails) ? 'Awaited' : 'No Email';
          $sent_data[] = $obj;
        }
      }

      $this->emails_model->save_sending_data($sent_data, $email_master_id);
      $this->load->helper('email_helper');
      return sendEmail($body, $subject, $email_master_id, $receiversMailIds, $fromMail);
  }

  public function update_noc_status(){
    $is_updated=$this->Student_exit_flow_model->update_noc_status($_POST);

    if(!$is_updated){
      echo 0;
      return;
    }
    // send email and notifications to aprovers
    $staff_id=$this->authorization->getAvatarStakeHolderId();
    $published_to_staff_name=$_POST["staffName"];
    $logged_in_staff_data = $this->staff_leave->getStaffData($staff_id);
    $nocType=$_POST['nocType'];
    $newPublishStatus=$_POST['publishStatus'];

    if($newPublishStatus==0){
      $publish_status='Un-published';
    }else{
      $publish_status = 'Published';
    }

    $admins = $this->role->getStaffListByPrivilege('STUDENT_EXIT_FLOW_STAFF', 'ADMIN');

    $rep_manager_id = $this->staff_leave->get_reporting_manager_id($staff_id);
    $staff_ids = $admins;

    if(!empty($rep_manager_id)){
      foreach ($rep_manager_id as $id) {
        $staff_ids[] = $id;
      }
    }
    
    $staff_ids[] = $staff_id;
    $staff_ids[] = $_POST["publishedToStaffId"];

    // SEND NOTIFICATIONS
    $this->load->helper('texting_helper');
    $message="";
    if (!empty($staff_ids)) {
      // send notification
      if($logged_in_staff_data->staffName){
        $message = "$nocType type NOC has been $publish_status to staff '$published_to_staff_name' by $logged_in_staff_data->staffName";
      }else{
        $message = "$nocType type NOC has been $publish_status to staff '$published_to_staff_name'";
      }

      $input_arr = array();
      $input_arr['staff_ids'] = $staff_ids;
      $input_arr['mode'] = 'notification';
      $input_arr['source'] = 'Student Exit';
      $input_arr['message'] = $message;
      $response = sendText($input_arr);

      // send email-notification
      $fromMail=$this->settings->getSetting("tc_request_notification_from_email");
      if(strlen($fromMail)){
        $subject=$publish_status;
        $body=$message;
        $receivers=$staff_ids;
        // SENDING EMAILS TO CONCERNED STAFF MEMBERS AND ADMIN
        $this->sendStudentExitApplicationEmailToStaff($subject,$body,$fromMail,$receivers);
      }
    }
    echo $is_updated;
  }

  // private function sendStaffLeaveEmail($staff_ids, $email_body){
  //   $this->load->helper('email_helper');
  //   $this->load->model('communication/emails_model');

  //   // this below line needs to be changed
  //   $from_email = $this->settings->getSetting('publish_student_exit_noc_from_email');
  //   // $from_email="<EMAIL>";
  //   if (empty($from_email)) {
  //     return false;
  //     // $from_email = "<EMAIL>";
  //   }

  //   $sender_list = [];
  //   $emaildata = array();
  //   $leave_approvers = $this->staff_leave->get_leave_approvers($staff_ids);
  //   $email_ids = [];

  //   if (!empty($leave_approvers)) {
  //     foreach ($leave_approvers as $key => $val) {
  //       $sender_list['staff'] = [
  //         'send_to_type' => 'Staff',
  //         'id' => $val->staff_id,
  //       ];

  //       $object = new stdclass();
  //       $object->id = $val->staff_id;
  //       $object->email = $val->email;
  //       $object->avatar_type = '4';
  //       array_push($emaildata, $object);

  //       if ($val->email) {
  //         array_push($email_ids, $val->email);
  //       }
  //     }
  //   }

  //   $email_master_data = array(
  //     'subject' => 'Publish Student NOC',
  //     'body' => $email_body,
  //     'source' => 'Student Exit',
  //     'sent_by' => $this->authorization->getAvatarId(),
  //     'recievers' => 'Staff',
  //     'from_email' => $from_email,
  //     'files' => '',
  //     'acad_year_id' => $this->acad_year->getAcadYearId(),
  //     'visible' => 1,
  //     'sender_list' => empty($sender_list) ? NULL : json_encode($sender_list),
  //     'sending_status' => 'Send Email'
  //   );

  //   $email_master_id = $this->emails_model->saveEmail($email_master_data);
  //   $this->emails_model->save_sending_data((object) $emaildata, $email_master_id);
  //   $email = $this->emails_model->getEmailInfo($email_master_id);
  //   return sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
  // }
}?>