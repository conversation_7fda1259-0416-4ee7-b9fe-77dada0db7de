<?php

require APPPATH . 'libraries/REST_Controller.php';

class Student_task extends REST_Controller
{
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
            $data = ['error' => 'User not logged in'];
            $this->response($data, REST_Controller::HTTP_UNAUTHORIZED);
			$this->output->_display();
			exit;
		}
		$this->load->model('parentv2/Student_task_model');
	}

	public function subject_wise_task_get() {
        $student_id = $this->get('student_id');

		$data['subject_tasks'] = $this->Student_task_model->getStudentSubjectTasks($student_id);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function subject_wise_task_list_get() {
		$student_id = $this->get('student_id');
		$subject_id = $this->get('subject_id');
		$filter_name = $this->get('filter_name');

		$data['subject_tasks_list'] = $this->Student_task_model->getSubjectTaskList($student_id,$subject_id,$filter_name);
		
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function single_task_details_get() {
		$student_id = $this->get('student_id');
		$subject_id = $this->get('subject_id');
		$task_id = $this->get('task_id');

		$data['task_details'] = $this->Student_task_model->getTaskDetails($student_id,$subject_id,$task_id);
		
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}


	public function mark_read_post() {

		$student_id = $this->post('student_id');
		$is_read = $this->post('is_read');
		$task_id = $this->post('task_id');

		if (empty($student_id) || $student_id <= 0) {
			$data = ['error' => 'INVALID_INPUT'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}

		if (empty($task_id) || $task_id <= 0) {
			$data = ['error' => 'INVALID_INPUT'];
			$this->response($data, REST_Controller::HTTP_BAD_REQUEST);
			$this->output->_display();
			exit;	
		}

		$is_read = ($is_read == '1' || $is_read == 1) ? '1' : '0';

		$status = $this->Student_task_model->markAsReadUnRead($student_id, $task_id, $is_read);

		switch ($status) {
			case -1:
				$data = ['message' => 'PARENT_NOT_AUTHORIZED'];
				break;
			case 0:
				$data = ['message' => 'UPDATE_FAILED'];
				break;
			case 1:
				$data = ['message' => 'SUCCESS'];
				break;
			default:
				$data = ['message' => 'UNKNOWN_ERROR'];
				break;
		}

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

}
?>