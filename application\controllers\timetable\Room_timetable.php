<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>chhipulusu
 *          <EMAIL>
 *
 * Created:  22 June 2018
 *
 * Description: Controller for Room Timetables.
 *
 * Requirements: PHP5 or above
 *
 */

class Room_timetable extends CI_Controller {
	function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
        redirect('dashboard', 'refresh');
      }
      $this->load->model('timetable/room_tt');
      $this->load->model('timetable/timetable');
  }

  //Landing function to print all timetables
  public function printRoomTimetables(){
    if (!$this->authorization->isAuthorized('TIMETABLE.ROOM_TIMETABLE_REPORT')) {
      redirect('dashboard', 'refresh');
    }

    $selectedRoomId = $this->input->post('selectedRoomId');

    // $data['pHeadArr'] = $this->timetable->getPeriodHeader('40');
    $data['roomAll'] = $this->room_tt->get_allRoom();
    $data['selectedRoomIds'] = $selectedRoomId;
    $data['main_content'] = 'timetable/room_timetable/print_room_tt';
    $this->load->view('inc/template_fee', $data);
  }

  private function __getHeaderCSConfig () {
    $headerCS = $this->settings->getSetting('timetable_header_class_section_id');
    if ($headerCS == 0)
      $headerCS = 4;  //Initialize to a default class section.

    // if (isset($ttSetting))
    //   $headerCS = $ttSetting['header_class_section_id'];
    // else
    //   $headerCS = 4; //Initialize to a default class section.

    return $headerCS;
  }

  public function getFullTTForRoom(){
    $roomId = $this->input->post('roomId');
    $tttId = $this->settings->getSetting('timetable_template_for_room');
    $roomTT = $this->room_tt->getFullTTForRoom($roomId, $tttId);
    $roomObj = $this->room_tt->getRoomDetails($roomId);

    $headerTT = $this->timetable->getPeriodHeader($this->__getHeaderCSConfig());

    echo json_encode(array("roomObj"=>$roomObj, "roomTT" =>$roomTT, "headerTT" => $headerTT));
   }
   
  public function validateRoomTTIndex() {
    $selectedRoomId = $this->input->post('selectedRoomId');

    $data['roomAll'] = $this->room_tt->get_allRoom();
    $data['selectedRoomId'] = $selectedRoomId;
    $data['main_content'] = 'timetable/room_timetable/validate_room_tt_index';
    $this->load->view('inc/template', $data);
  }

  public function getRoomJSON () {
    $roomId = $_POST['roomId'];
    $tttId = $this->settings->getSetting('timetable_template_for_room');

    $data['roomTTJSON'] = $this->room_tt->getRoomTTById($roomId, '1');
    $data['genRoomTTJSON'] = $this->generateJSONFromTT($roomId, $tttId);

    echo json_encode($data);
  }

  public function generateJSONFromTT ($roomId, $tttId) {
    //$newRoomJSON = $this->room_tt->initRoomJSON($tttId);

    $roomTT = $this->room_tt->getFullTTForRoom($roomId, $tttId);
    // echo '<pre>sdfd';print_r($tttId);
    // echo '<pre>asdf';print_r($roomTT);die();
    
    //Construct the timetable
    $prevWd = 0;
    $isVisited = 0;
    $allocationData = array();
    $numPeriods = 0;
    foreach ($roomTT as $p) {
      //TODO: Consider allocation string
      if ($p->weekDay != $prevWd) {
        if ($prevWd != '0') {
          $allocationWeekDay['numPeriods'] = $numPeriods; 
          $allocationWeekDay['allocationStr'] = $allocationStr; 
          $allocationWeekDay['allocationDisplay'] = $allocationDisplay; 

          $allocationData[$prevWd] = $allocationWeekDay;  
        }

        $numPeriods = 0;
        $allocationStr = array();
        $allocationDisplay = array();
        $prevWd = $p->weekDay;
      }
      
      if ($p->pSeq != '0') {
        $numPeriods ++;
        if (isset($p->allocRoom))
          $allocationStr[$numPeriods] = $this->__getAllocString ($p->csName, $p->csCount, $p->allocRoom);
        else
          $allocationStr[$numPeriods] = 0;
        $allocationDisplay[$numPeriods] = (empty($p->csName)?'-':$p->csName);
      }
    }

    if ($numPeriods != 0) {
      $allocationWeekDay['numPeriods'] = $numPeriods; 
      $allocationWeekDay['allocationStr'] = $allocationStr; 
      $allocationWeekDay['allocationDisplay'] = $allocationDisplay; 
      $allocationData[$prevWd] = $allocationWeekDay;  
    }

    return $allocationData;
  }

  private function __getAllocString($csName, $csCount, $allocType) {
    switch ($allocType) {
      case '1':
        switch ($csCount) {
          case '0':
            return '0';
          case '1':
            return '1';
          default:
            return 'E';
        }
        break;
      case '2':
        switch ($csCount) {
          case '0':
            return '0';
          case '1':
            return '21';
          case '2':
            return '22';
          default:
            return '2E';
        }
        break;
      case '3':
        switch ($csCount) {
          case '0':
            return '0';
          case '1':
            return '31';
          case '2':
            return '32';
          case '3':
            return '33';
          default:
            return '3E';
        }
        break;
      case '4':
        switch ($csCount) {
          case '0':
            return '0';
          case '1':
            return '41';
          case '2':
            return '42';
          case '3':
            return '43';
          case '4':
            return '44';
          default:
            return '4E';
        }
      case '5':
        //Multiple allows to combine 50 classes
        //First allocation, $allocated will be 0
        //Subsequent allocations, $allocated will be between 51 and 100
        if ($csCount == 0)
          return '0';
        else if ($csCount == 1)
          return '51';
        else if ($csCount > 1 && $csCount <= 100)
          return '' . ($csCount + 50); //To convert to string
        break;
      // case '5':
        
      //   return '0';
      //   break;
    }
  }

  public function repairRoomTT() {
    //echo '<pre>';print_r($this->input->post());

    $allocData = $this->input->post('allocData');
    $roomId = $this->input->post('roomId');

    $rAllocData = json_decode($allocData);
    //echo '<pre>';print_r($rAllocData);

    $oldJSON = $rAllocData->roomTTJSON;
    $newJSON = json_encode($rAllocData->genRoomTTJSON);

    //echo '<pre>';print_r($newJSON);die();

    $result = $this->room_tt->upsertRoomTTJSON($oldJSON, $newJSON, $roomId);

    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Repair Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong...');
	  }	
    redirect('timetable/room_timetable/validateRoomTTIndex');
  }
}