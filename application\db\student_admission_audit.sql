DROP TRIGGER IF EXISTS student_admission_audit;
DELIMITER $$
CREATE TRIGGER student_admission_audit
AFTER UPDATE ON student_admission FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@oldJ<PERSON>, @newJson, OLD.first_name, NEW.first_name, 'first_name');
    CALL merge_object(@oldJson, @newJson, OLD.last_name, NEW.last_name, 'last_name');
    CALL merge_object(@oldJson, @newJson, OLD.admission_no, NEW.admission_no, 'admission_no');
    CALL merge_object(@oldJson, @newJson, OLD.enrollment_number, NEW.enrollment_number, 'enrollment_number');
    CALL merge_object(@oldJson, @newJson, OLD.dob, NEW.dob, 'dob');
    CALL merge_object(@oldJson, @newJson, OLD.gender, NEW.gender, 'gender');
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.nationality, NEW.nationality, 'nationality');
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.religion, NEW.religion, 'religion');
    CALL merge_object(@oldJson, @newJson, OLD.category, NEW.category, 'category');
    CALL merge_object(@oldJson, @newJson, OLD.mother_tongue, NEW.mother_tongue, 'mother_tongue');
    CALL merge_object(@oldJson, @newJson, OLD.preferred_contact_no, NEW.preferred_contact_no, 'preferred_contact_no');
    CALL merge_object(@oldJson, @newJson, OLD.sibling_type, NEW.sibling_type, 'sibling_type');
    CALL merge_object(@oldJson, @newJson, OLD.has_staff, NEW.has_staff, 'has_staff');
    CALL merge_object(@oldJson, @newJson, OLD.staff_id, NEW.staff_id, 'staff_id');
    CALL merge_object(@oldJson, @newJson, OLD.is_rte, NEW.is_rte, 'is_rte');
    CALL merge_object(@oldJson, @newJson, OLD.admission_status, NEW.admission_status, 'admission_status');
    CALL merge_object(@oldJson, @newJson, OLD.date_of_joining, NEW.date_of_joining, 'date_of_joining');
    CALL merge_object(@oldJson, @newJson, OLD.birth_taluk, NEW.birth_taluk, 'birth_taluk');
    CALL merge_object(@oldJson, @newJson, OLD.birth_district, NEW.birth_district, 'birth_district');
    CALL merge_object(@oldJson, @newJson, OLD.caste, NEW.caste, 'caste');
    CALL merge_object(@oldJson, @newJson, OLD.aadhar_no, NEW.aadhar_no, 'aadhar_no');
    CALL merge_object(@oldJson, @newJson, OLD.class_admitted_to, NEW.class_admitted_to, 'class_admitted_to');
    CALL merge_object(@oldJson, @newJson, OLD.admission_year, NEW.admission_year, 'admission_year');
    CALL merge_object(@oldJson, @newJson, OLD.emergency_info, NEW.emergency_info, 'emergency_info');
    CALL merge_object(@oldJson, @newJson, OLD.identification_code, NEW.identification_code, 'identification_code');
    CALL merge_object(@oldJson, @newJson, OLD.second_language_choice, NEW.second_language_choice, 'second_language_choice');
    CALL merge_object(@oldJson, @newJson, OLD.third_language_choice, NEW.third_language_choice, 'third_language_choice');
    CALL merge_object(@oldJson, @newJson, OLD.admission_acad_year_id, NEW.admission_acad_year_id, 'admission_acad_year_id');
    CALL merge_object(@oldJson, @newJson, OLD.email, NEW.email, 'email');
    CALL merge_object(@oldJson, @newJson, OLD.email_password, NEW.email_password, 'email_password');
    CALL merge_object(@oldJson, @newJson, OLD.rfid_number, NEW.rfid_number, 'rfid_number');
    CALL merge_object(@oldJson, @newJson, OLD.has_transport, NEW.has_transport, 'has_transport');
    CALL merge_object(@oldJson, @newJson, OLD.sts_number, NEW.sts_number, 'sts_number');
    CALL merge_object(@oldJson, @newJson, OLD.admission_form_id, NEW.admission_form_id, 'admission_form_id');
    CALL merge_object(@oldJson, @newJson, OLD.preferred_parent, NEW.preferred_parent, 'preferred_parent');
    CALL merge_object(@oldJson, @newJson, OLD.language_spoken, NEW.language_spoken, 'language_spoken');
    CALL merge_object(@oldJson, @newJson, OLD.quota, NEW.quota, 'quota');
    CALL merge_object(@oldJson, @newJson, OLD.webcam_avatar, NEW.webcam_avatar, 'webcam_avatar');
    CALL merge_object(@oldJson, @newJson, OLD.life_time_fee_mode, NEW.life_time_fee_mode, 'life_time_fee_mode');
    CALL merge_object(@oldJson, @newJson, OLD.attempt, NEW.attempt, 'attempt');
    CALL merge_object(@oldJson, @newJson, OLD.temp_deactivation, NEW.temp_deactivation, 'temp_deactivation');
    CALL merge_object(@oldJson, @newJson, OLD.joined_helium, NEW.joined_helium, 'joined_helium');
    CALL merge_object(@oldJson, @newJson, OLD.joined_helium_on, NEW.joined_helium_on, 'joined_helium_on');
    CALL merge_object(@oldJson, @newJson, OLD.vaccination_name, NEW.vaccination_name, 'vaccination_name');
    CALL merge_object(@oldJson, @newJson, OLD.vaccination_status, NEW.vaccination_status, 'vaccination_status');
    CALL merge_object(@oldJson, @newJson, OLD.vaccination_last_submitted_date, NEW.vaccination_last_submitted_date, 'vaccination_last_submitted_date');
    CALL merge_object(@oldJson, @newJson, OLD.vaccination_verification_status, NEW.vaccination_verification_status, 'vaccination_verification_status');
    CALL merge_object(@oldJson, @newJson, OLD.vaccination_supporting_document, NEW.vaccination_supporting_document, 'vaccination_supporting_document');
    CALL merge_object(@oldJson, @newJson, OLD. registration_no, NEW. registration_no, ' registration_no');
    CALL merge_object(@oldJson, @newJson, OLD.ration_card_number, NEW.ration_card_number, 'ration_card_number');
    CALL merge_object(@oldJson, @newJson, OLD.ration_card_type, NEW.ration_card_type, 'ration_card_type');
    CALL merge_object(@oldJson, @newJson, OLD.caste_income_certificate_number, NEW.caste_income_certificate_number, 'caste_income_certificate_number');
    CALL merge_object(@oldJson, @newJson, OLD.extracurricular_activities, NEW.extracurricular_activities, 'extracurricular_activities');
    CALL merge_object(@oldJson, @newJson, OLD. student_sub_caste, NEW. student_sub_caste, ' student_sub_caste');
    CALL merge_object(@oldJson, @newJson, OLD.student_mobile_no, NEW.student_mobile_no, 'student_mobile_no');
    CALL merge_object(@oldJson, @newJson, OLD.medium_of_instruction, NEW.medium_of_instruction, 'medium_of_instruction');
    CALL merge_object(@oldJson, @newJson, OLD.blood_group, NEW.blood_group, 'blood_group');
    CALL merge_object(@oldJson, @newJson, OLD.student_biometric_code, NEW.student_biometric_code, 'student_biometric_code');
    CALL merge_object(@oldJson, @newJson, OLD. family_picture_url, NEW. family_picture_url, ' family_picture_url');
    CALL merge_object(@oldJson, @newJson, OLD.student_remarks, NEW.student_remarks, 'student_remarks');
    CALL merge_object(@oldJson, @newJson, OLD.nick_name, NEW.nick_name, 'nick_name');
    CALL merge_object(@oldJson, @newJson, OLD.student_indian_visa_number, NEW.student_indian_visa_number, 'student_indian_visa_number');
    CALL merge_object(@oldJson, @newJson, OLD.student_indian_visa_expiry_date, NEW.student_indian_visa_expiry_date, 'student_indian_visa_expiry_date');
    CALL merge_object(@oldJson, @newJson, OLD.identification_mark1, NEW.identification_mark1, 'identification_mark1');
    CALL merge_object(@oldJson, @newJson, OLD. identification_mark2, NEW. identification_mark2, ' identification_mark2');
    CALL merge_object(@oldJson, @newJson, OLD.sibling1_name, NEW.sibling1_name, 'sibling1_name');
    CALL merge_object(@oldJson, @newJson, OLD.sibling1_occupation, NEW.sibling1_occupation, 'sibling1_occupation');
    CALL merge_object(@oldJson, @newJson, OLD.sibling1_mobile_num, NEW.sibling1_mobile_num, 'sibling1_mobile_num');
    CALL merge_object(@oldJson, @newJson, OLD.sibling2_name, NEW.sibling2_name, 'sibling2_name');
    CALL merge_object(@oldJson, @newJson, OLD. sibling2_occupation, NEW. sibling2_occupation, ' sibling2_occupation');
    CALL merge_object(@oldJson, @newJson, OLD.sibling2_mobile_num, NEW.sibling2_mobile_num, 'sibling2_mobile_num');
    CALL merge_object(@oldJson, @newJson, OLD.student_whatsapp_num, NEW.student_whatsapp_num, 'student_whatsapp_num');
    CALL merge_object(@oldJson, @newJson, OLD.is_single_child, NEW.is_single_child, 'is_single_child');
    CALL merge_object(@oldJson, @newJson, OLD.is_minority, NEW.is_minority, 'is_minority');
    CALL merge_object(@oldJson, @newJson, OLD.current_nearest_location, NEW.current_nearest_location, 'current_nearest_location');
    CALL merge_object(@oldJson, @newJson, OLD.passport_number, NEW.passport_number, 'passport_number');
    CALL merge_object(@oldJson, @newJson, OLD.passport_issued_place, NEW.passport_issued_place, 'passport_issued_place');
    CALL merge_object(@oldJson, @newJson, OLD.passport_validity, NEW.passport_validity, 'passport_validity');
    CALL merge_object(@oldJson, @newJson, OLD. parents_marriage_anniversary, NEW. parents_marriage_anniversary, ' parents_marriage_anniversary');
    CALL merge_object(@oldJson, @newJson, OLD.sibling3_name, NEW.sibling3_name, 'sibling3_name');
    CALL merge_object(@oldJson, @newJson, OLD.sibling3_occupation, NEW.sibling3_occupation, 'sibling3_occupation');
    CALL merge_object(@oldJson, @newJson, OLD.sibling3_mobile_num, NEW.sibling3_mobile_num, 'sibling3_mobile_num');
    CALL merge_object(@oldJson, @newJson, OLD.point_of_contact, NEW.point_of_contact, 'point_of_contact');
    CALL merge_object(@oldJson, @newJson, OLD.student_living_with, NEW.student_living_with, 'student_living_with');
    CALL merge_object(@oldJson, @newJson, OLD.last_tc_num, NEW.last_tc_num, 'last_tc_num');
    CALL merge_object(@oldJson, @newJson, OLD.last_hallticket_num, NEW.last_hallticket_num, 'last_hallticket_num');
    CALL merge_object(@oldJson, @newJson, OLD.name_as_per_aadhar, NEW.name_as_per_aadhar, 'name_as_per_aadhar');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'student_admission',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        NEW.last_modified_by
    );
    END IF;
END;
$$
DELIMITER ;