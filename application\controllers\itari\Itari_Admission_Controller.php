<?php
class Itari_Admission_Controller extends CI_Controller
{
    function __construct() {
        parent::__construct();
        $this->load->model('itari/Itari_Admission_Model');
        $this->load->library('filemanager');
        $this->load->library('payment_application');
        $this->config->load('form_elements');
    }

    public function verifyMobile() {
        if(isset($_SESSION['loginstatus'])) {
            redirect('itari_admissions/home');
        } else {
            $this->load->view('itari/itari_admission_login/verify_mobile_num');
        }
    }
    public function testform(){
        $data['mobile'] = $this->session->userdata('mobile');
        $data['main_content']= 'itari/itari_admission_forms/index';
        $this->load->view('itari/inc/template',$data);
    }
    public function my_application(){
        $data['mobile'] = $this->session->userdata('mobile');
        $data['my_application'] = $this->Itari_Admission_Model->my_application_data_cards($data['mobile']);
        $data['main_content'] = 'itari/itari_admission_forms/my_applications/index';
        $this->load->view('itari/inc/template',$data);
    }
    public function my_application_data(){
        $data['mobile'] = $this->session->userdata('mobile');
        $res = $_POST['admission_id'];
        $data['result'] = $this->Itari_Admission_Model->my_application_data($res);
        $data['main_content'] = 'itari/itari_admission_forms/index';
        $this->load->view('itari/inc/template',$data);
    }
    public function validate_personal_details(){
        $result = $this->Itari_Admission_Model->insert_itari_admissions_personal_details($_POST,$this->s3FileUpload($_FILES['student_photo'], 'itari_admission_photo'), $this->input->post('academic_year_applied_for'));
        echo $result;
    }
    public function validate_additional_academic_info(){
        $result = $this->Itari_Admission_Model->insert_itari_admissions_additional_academic_details($_POST);
        echo $result;
    }
    public function validate_employment_info(){
        $result = $this->Itari_Admission_Model->insert_itari_admissions_employment_details($_POST);
        echo $result;
    }
    public function validate_additional_employment_info(){
        $result = $this->Itari_Admission_Model->insert_itari_admissions_additional_employment_details($_POST);
        echo $result;
    }
    public function validate_references_info(){
        $result = $this->Itari_Admission_Model->insert_itari_admissions_references_information($_POST,$this->s3FileUpload($_FILES['essay'], 'itari_essay'));
        echo $result;
    }
    public function remove_information(){
        $result = $this->Itari_Admission_Model->remove_information($_POST);
        echo $result;
    }
    private function s3FileUpload($file, $folder_name = 'profile'){
        if ($file['tmp_name'] == '' || $file['name'] == '') {
            return ['status' => 'empty', 'file_name' => ''];
        }
        return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
    }
    public function add_follow_up_details(){
        $result = $this->Itari_Admission_Model->add_follow_up_details($_POST);
        echo $result;
    }
    public function final_submit(){
        $result = $this->Itari_Admission_Model->final_submit($_POST);
        $this->get_payment_link($_POST);
    }
    public function follow_up_details_insert_for_form(){
        $input = $this->input->post();
        $enquiry_id = $input['enquiry_id'];
        $result = $this->Itari_Admission_Model->update_follow_up_details_insert_for_form($enquiry_id, $input);
        echo $result;
    }
    public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }
    private function get_amount_for_itari_admission_forms($course){
        $amount=0;
        if($course === "pgde")
            $amount = 2000;
        elseif($course === "ibtl")
            $amount = 2000;
        elseif($course === "cidtl")
            $amount = 2000;
        elseif($course === "pgpece")
            $amount = 2000;
        elseif($course === "maedrpl")
            $amount = 2000;
        elseif($course === "maedpgde")
            $amount = 2000;
        return $amount;
    }
    public function get_payment_link($input) {
        $itari_admission_id = $this->input->post('itari_adm_id');
        $course = $this->input->post('course');
        $amount = $this->get_amount_for_itari_admission_forms($course);
        $this->payment_application->init_payment_to_itari($amount, 'ITARI APPLICATION FEES', $itari_admission_id);
    }
    public function get_payment_link_for_saved_applications() {
        $itari_admission_id = $_POST['itari_adm_id'];
        $course = $_POST['course'];
        $amount = $this->get_amount_for_itari_admission_forms($course);
        $this->payment_application->init_payment_to_itari($amount, 'ITARI APPLICATION FEES', $itari_admission_id);
    }  
    public function logout(){
        unset($_SESSION['loginstatus']);
        redirect('itari_admissions');        
    }
    public function itari_handle_payment_callback() {
        // trigger_error("Response at application_fee_trans_done");
        // trigger_error(json_encode($_POST));
        if ($_POST['response_type'] === 'IMMEDIATE') {
            $this->__handle_immediate_op_response($_POST);
        } elseif ($_POST['response_type'] === 'DELAYED') {
            // $this->__handle_delayed_op_response($_POST);
            $this->__handle_immediate_op_response($_POST);
        } else {
            $this->__handle_recon_op_response($_POST);
        }
    }
    private function __handle_immediate_op_response($response){
        if ($response['transaction_status'] === 'SUCCESS') {
            //Update Itari admission form status
            $this->Itari_Admission_Model->update_itari_form_status_on_success($response['source_id']);
            $this->session->set_flashdata('flashError', 'Payment Successful');
            redirect('itari_admissions/payment_success');
        } else {
            // $this->Itari_Admission_Model->update_itari_form_status($response['source_id'], 'FAILED');
            $this->session->set_flashdata('flashError', 'Payment Failed');
            redirect('itari_admissions/payment_failure');
        }
    }
    public function saveAction() {
        $input = $this->input->post();
        if(isset($_SESSION['loginstatus'])) {
            $mobileNumber = $this->session->userdata('loginstatus');
            $data['isGuest'] = true;
            $data['mobileNumber'] = $mobileNumber;
            $data['au_id'] = $this->Itari_Admission_Model->get_id_mobile_number($mobileNumber);
            $this->session->set_userdata('user_id', $data['au_id']);
            $this->session->set_userdata('mobile', $data['mobileNumber']);
            $data['main_content'] = 'itari/itari_admission_login/dashboard';
            $this->load->view('itari/inc/template', $data);
        } 
        else if($this->Itari_Admission_Model->verifyOTP($input)) {
            $this->session->set_userdata('loginstatus', $input['mobileNumber']);
            $data['isGuest'] = true;
            $data['mobileNumber'] = $input['mobileNumber'];
            $data['au_id'] = $this->Itari_Admission_Model->get_id_mobile_number($data['mobileNumber']);
            $this->session->set_userdata('user_id', $data['au_id']);
            $this->session->set_userdata('mobile', $data['mobileNumber']);
            $data['main_content'] = 'itari/itari_admission_login/dashboard';
            $this->load->view('itari/inc/template', $data);
        } 
        else {
            redirect('itari_admissions');
        }
    }
    public function success($itari_admission_id){
        $data['result'] = $this->Itari_Admission_Model->my_application_data($itari_admission_id);
        $data['main_content'] = 'itari/itari_admission_forms/success';
        $this->load->view('itari/inc/template',$data);
    }
    public function back_to_my_application_data(){
        $data['result'] = $this->Itari_Admission_Model->my_application_data($res);
        $data['main_content'] = 'itari/itari_admission_forms/index';
        $this->load->view('itari/inc/template',$data);
    }
    public function payment_success(){
        $data['main_content'] = 'itari/itari_admission_forms/success';
        $this->load->view('itari/inc/template',$data);
    }
    public function payment_failure(){
        $data['main_content'] = 'itari/itari_admission_forms/failure';
        $this->load->view('itari/inc/template',$data);
    }
}
?>