<?php

defined('BASEPATH') OR exit('No direct script access allowed');

require APPPATH . 'third_party/aws/aws-autoloader.php';
use Aws\Sns\Message;
use Aws\Sns\MessageValidator;
class EmailNotifications extends CI_Controller {

    function __construct()
    {
        // Construct the parent class
        parent::__construct();

    }

    public function index() {

        if ('POST' !== $_SERVER['REQUEST_METHOD']) {
            http_response_code(405);
            die;
        }

        try {
            $message = Aws\Sns\Message::fromRawPostData();
            $validator = new Aws\Sns\MessageValidator();
            $validator->validate($message);

            if (in_array($message['Type'], ['SubscriptionConfirmation', 'UnsubscribeConfirmation'])) {
                file_get_contents($message['SubscribeURL']);
            } else {

                $response = json_decode($message['Message']);
                $status_known = 1;
                $data = [];
                switch ($response->eventType) {
                    case 'Delivery':
                        $data['status'] = 'Delivered';
                        $data['recipient'] = $response->delivery->recipients[0];
                        break;

                    case 'Bounce':
                        $data['status'] = 'Bounce-'.$response->bounce->bouncedRecipients[0]->action;
                        $data['recipient'] = $response->bounce->bouncedRecipients[0]->emailAddress;
                        break;

                    case 'Complaint':
                        $data['status'] = 'Complaint-'.$response->complaint->complaintFeedbackType;
                        $data['recipient'] = $response->complaint->complainedRecipients[0]->emailAddress;
                        break;

                    case 'Reject':
                        $data['status'] = 'Reject-'.$response->reject->reason;
                        $data['recipient'] = $response->mail->destination[0];
                        break;

                    case 'Open':
                        $data['status'] = 'Opened';
                        $data['recipient'] = $response->mail->destination[0];
                        break;
                    
                    default: $status_known = 0;
                        break;
                }
                $tags = $response->mail->tags;
                $data['email_master_id'] = $tags->email_master_id[0];
                $school = $tags->school[0];

                if($status_known) {
                    $url = "https://".$school.".schoolelement.in/Callback_Controller/updateEmailDeliveryStatus";
                    $useragent = 'Mozilla/5.0 (Windows NT 6.2; WOW64; rv:17.0) Gecko/20100101 Firefox/17.0';
                    $data = http_build_query($data);

                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                        CURLOPT_URL => $url,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_USERAGENT => $useragent,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 30,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "POST",
                        CURLOPT_POSTFIELDS => $data,            
                        CURLOPT_HTTPHEADER => array(
                            "Accept: application/json",
                            "Cache-Control: no-cache",
                            "Content-Type: application/x-www-form-urlencoded"
                        ),
                    ));

                    curl_exec($curl);
                }
            }
        } catch (Exception $e) {
            http_response_code(404);
            die;
        }

    }
}

?>