<?php

class Suy_menu extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('INDUS_SUY') && !$this->authorization->isAuthorized('INDUS_SUY.MODULE')) {
      redirect('dashboard', 'refresh');
    }  

  }

  function index() {
    $site_url = site_url();
      $data['tiles'] = array(
          // [
          //   'title' => 'Manage SUY',
          //   'sub_title' => 'Manage SUY',
          //   'icon' =>'svg_icons/staffreport.svg',
          //   'url' => $site_url.'suy/suy_controller/suy_manage_page',  
          //   'permission' => $this->authorization->isAuthorized('SUY.MODULE')
          // ],
          [
            'title' => 'Create Enquiry',
            'sub_title' => 'Create Enquiry',
            'icon' =>'svg_icons/freshentry.svg',
            'url' => $site_url.'suy/suy_controller/create_suy_enquiry_page',  
            'permission' => $this->authorization->isAuthorized('INDUS_SUY.MODULE')
          ],
          [
            'title' => 'Manage Enquiries',
            'sub_title' => 'Manage Enquiries',
            'icon' =>'svg_icons/reportcard.svg',
            'url' => $site_url.'suy/suy_controller/suy_enquiry_page',  
            'permission' => $this->authorization->isAuthorized('INDUS_SUY.MODULE')
          ],
          [
            'title' => 'Re-registered Student',
            'sub_title' => 'Re-registered Student',
            'icon' =>'svg_icons/reportcard.svg',
            'url' => $site_url.'suy/suy_controller/registered_student_page',  
            'permission' => $this->authorization->isAuthorized('INDUS_SUY.MODULE')
          ]
          );
          $data['tiles'] = checkTilePermissions($data['tiles']);
    $data['main_content']    = 'suy/menu';
    $this->load->view('inc/template', $data);
    }


    
}