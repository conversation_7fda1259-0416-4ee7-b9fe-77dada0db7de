<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Items
 *
 * <AUTHOR>
 */
class Items extends CI_Controller {

    //put your code here
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->load->model('items/Items_model');
    }

    public function index($id = null) {
        if ($id != null) {
            $data['edititem'] = $this->Items_model->edit_item($id);
        }
        $data['itemList'] = $this->Items_model->get_items();
        $data['main_content'] = 'items/item';
        $this->load->view('inc/template', $data);
    }

    public function addItems() {
        $addItems = $this->Items_model->add_items();

        if ($addItems) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
            redirect('items/items');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('items/items');
        }
    }

    public function updateItem($id) {

        $updateItem = $this->Items_model->update_item($id);
        if ($updateItem) {
            $this->session->set_flashdata('flashSuccess', 'Successfully updated.');
            redirect('items/items/');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('items/items' . $id);
        }
    }

    public function deleteItem($id) {

        $deleteVariant = $this->Items_model->delete_item($id);
        if ($deleteVariant) {
            $this->session->set_flashdata('flashSuccess', 'Deleted Succssfully');
            redirect('items/items/index');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('items/items/index');
        }
    }
}
