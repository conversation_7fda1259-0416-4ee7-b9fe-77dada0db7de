[{"name": "modules", "type": "multiple", "options": ["SCHOOL_ADMIN", "STUDENT_MASTER", "STAFF_MASTER", "REPORTS", "TIMETABLE", "SUBSTITUTION", "BUILDING_MASTER", "STAFF_LOGIN", "COMPETITION", "VISITOR", "REGISTER", "COMMUNICATION", "ACTIVITY", "TASKSTODO", "PARENTS_LOGIN", "FEES_NH", "STUDENT_LEAVE", "SCHOOL_CALENDAR", "STAFF_LEAVE", "EXAMINATION", "ROOM_BOOKING", "STUDENT_ATTENDANCE", "STUDENT_OBSERVATION", "STAFF_OBSERVATION", "SMS", "STAFF_INITIATIVE", "PERMISSIONS", "CIRCULAR", "LIBRARY", "ADMISSION", "FLASH_NEWS", "PARENT_INITIATIVE"]}, {"name": "parent_modules", "type": "multiple", "options": ["ATTENDANCE_SUMMARY", "CIRCULARS", "SMS", "TIMETABLE", "ASSESSMENTS", "SCHOOL_CALENDAR", "PARENT_INITIATIVE", "MARKS_CARD", "ASSESSMENT_POSRTIONS_V1"]}, {"name": "school_name", "type": "string", "default": "School Name"}, {"name": "school_name_line1", "type": "string", "default": "School Address 1"}, {"name": "school_name_line2", "type": "string", "default": ""}, {"name": "school_short_name", "type": "string", "default": ""}, {"name": "school_image", "type": "string", "default": ""}, {"name": "school_logo", "type": "string", "default": ""}, {"name": "company_logo", "type": "string", "default": ""}, {"name": "company_name", "type": "string", "default": ""}, {"name": "login_background", "type": "string", "default": ""}, {"name": "school_header", "type": "string", "default": ""}, {"name": "school_abbreviation", "type": "string", "default": ""}, {"name": "favicon", "type": "string", "default": ""}, {"name": "mobile_app_name", "type": "string", "default": ""}, {"name": "online_room_booking_template_id", "type": "string", "default": ""}, {"name": "smstemplates", "type": "string", "default": ""}, {"name": "competition_attendance", "type": "string", "default": ""}, {"name": "show_acknowledgment", "type": "string", "default": ""}, {"name": "latecomer_attendance", "type": "string", "default": ""}, {"name": "emergency_exit_visitor", "type": "string", "default": ""}, {"name": "new_admissions", "type": "string", "default": ""}, {"name": "academic_start_year", "type": "string", "default": ""}, {"name": "examination", "type": "json", "default": "{ record_selected_lang:TRUE, record_height_weight:FALSE}"}, {"name": "forgot_password", "type": "json", "default": "{mobile:TRUE, email:TRUE}"}, {"name": "student", "type": "json", "default": "{allow_additional_remarks:FALS<PERSON>, remarks_length:250, marks_rounding_digits:1}"}, {"name": "admission_number", "type": "json", "default": "{manual:TRUE, admission_generation_algo:NEXTELEMENT, infix:NULL, digit_count:5, index_offset:TRUE}"}, {"name": "timetable", "type": "json", "default": "{ header_class_section_id:40, half_day_period:NULL, last_period:consider_4_alloc_p6_for_cs_lab, timetable_template_for_room:NULL}"}, {"name": "board", "type": "json", "default": "{2:CBSE}"}, {"name": "medium", "type": "json", "default": "{1:English}"}, {"name": "classType", "type": "json", "default": "{1:Pre-Nursery (PREP-1 TO  PREP-3), 2:High School (1 TO 10)}"}, {"name": "admission_type", "type": "json", "default": "{1:Re-admission, 2:New Admission}"}, {"name": "admission_status", "type": "json", "default": "{1:Pen<PERSON>, 2:Approved, 3:Rejected, 4:Alumni}"}, {"name": "staff_status", "type": "json", "default": "{1:Pending, 2:Approved, : 3:Rejected, 4:Resigned, 5:Retired}"}, {"name": "rte", "type": "json", "default": "{1:RTE, 2:Non-RTE}"}, {"name": "category", "type": "json", "default": "{1:<PERSON>, 2:SC/ST, 3:CATEGORY IIA, 4:CATEGORY IIB, 5:CATEGORY IIIA, 6: OBC}"}, {"name": "boarding", "type": "json", "default": "{1: Day School }"}, {"name": "staff_profile", "type": "json", "default": "{enableStaffProfileEdit:0, enableQualificationEdit:0 }"}, {"name": "sms_credit_length", "type": "json", "default": "{ unicode_single:70, unicode_multi:60, non_unicode_single:160, non_unicode_multi:150}"}, {"name": "circular", "type": "json", "default": "{ sms_msg1:You have a new circular with title , sms_msg2:, Please check your School App., categories;Competition,Co-Scholastic,Co-curricular,Scholastic,Time-Table,Board-Circular,Fee,Student-Info, from_email:NULL, enable_email:0, enable_sms:1}"}, {"name": "email_settings", "type": "json", "default": [{"from_email": ""}, {"smtp_user": "getenv('SMTP_USER')"}, {"smtp_pass": "getenv('SMTP_PASS')"}, {"from_name": ""}, {"smtp_host": "getenv('SMTP_HOST')"}, {"smtp_port": "getenv('SMTP_PORT')"}, {"protocol": "smtp"}, {"smtp_crypto": "ssl"}, {"mailtype": "html"}, {"charset": "utf-8"}, {"newline": "\\r\\n"}]}, {"name": "email", "type": "json", "default": [{"dev_email": "manju<PERSON><EMAIL>", "templates": [{"demo": [{"subject": "Test Demo", "send_email": "TRUE", "bcc_admin": "false"}]}, {"forgot_password": [{"subject": "", "send_email": "TRUE", "bcc_admin": "false"}]}]}]}, {"name": "lbr", "type": "json", "default": "{lbr_access_code:FALSE}"}, {"name": "address_types", "type": "json", "default": [{"Student": "{}", "Father": {"0": "Office Address", "1": "Home Address"}, "Mother": {"0": "Office Address", "1": "Home Address"}}]}, {"name": "books_type", "type": "json", "default": [{"name": "Issue", "value": "1", "type_reqd": "TRUE"}, {"name": "Reference", "value": "2", "type_reqd": "TRUE"}, {"name": "Specimen copy", "value": "3", "type_reqd": "TRUE"}, {"name": "CD", "value": "4", "type_reqd": "TRUE"}, {"name": "Non-Issue", "value": "5", "type_reqd": "TRUE"}]}, {"name": "parent_profile_display", "type": "json", "default": [{"Student": [{"admission_no": "", "dob": "", "roll_no": ""}]}, {"Father": [{"display": "1", "fields": [{"name_photo": "", "email": "", "contact_no": "", "address": [{"1": "", "occupation": ""}]}]}]}, {"Mother": [{"display": "1", "fields": [{"name_photo": "", "email": "", "contact_no": "", "address": [{"1": "", "occupation": ""}]}]}]}]}, {"name": "fees", "type": "json", "default": [{"filter_criteria": [{"admission_type": ""}]}, {"components": [{"name": "Admission", "concession_eligible": "TRUE"}]}, {"allowed_payment_modes": [[{"name": "cash", "value": "9", "reconcilation_reqd": "FALSE"}], [{"name": "card (POS)", "value": "7", "reconcilation_reqd": "FALSE"}], [{"name": "net banking", "value": "8", "reconcilation_reqd": "TRUE"}]]}, {"receipt_template": ""}, {"component_allocation_type_auto": "TRUE"}, {"cComponent_allocation_type_auto": "TRUE"}, {"component_allocation_algorithm_if_auto": "ALLOCATE_FROM_LEFT_TO_RIGHT"}, {"recipt_number_gen": [{"fee_generation_algo": "", "infix": "", "digit_count": ""}]}, {"discount": [{"percent": "5", "isProvided": "TRUE", "isManual": "FALSE"}]}]}, {"name": "attendance", "type": "json", "default": [{"absentee_sms": "Your ward %std_name% of %cs_name% is absent today %date%. Kindly ignore the sms if you have already informed the school through email. Principal - ", "latecomer_sms": "Your ward %std_name% of %cs_name% has come to school late today %date%. Principal - "}]}, {"name": "student_action", "type": "json", "default": [{"outGoing": [{"class": "KG1,KG2,1,2,3,4,5", "time-range": "7:20 am - 11:45 am"}, {"class": "6,7,8,9,10,11,12", "time-range": "8:20 am - 12:20 pm"}]}]}, {"name": "smsintergration", "type": "json", "default": [{"url": "alerts.solutionsinfini.com/api/v4/index.php", "api_key": "A27187ab48f33e1d9602ef6dfe0c960c0", "sender": ""}]}, {"name": "admissions_forms", "type": "json", "default": [[{"form_name": "MONTESSORI", "form_year": "2019-2020", "school_name": "", "instructin_file": "mont", "school_short": "", "school": "", "open_for_admissions": "FALSE", "class_applied_for": [{"MONTESSORI": ""}], "application_no_gen": [{"prefix": "MONT", "name": "", "year": "2019", "digit_count": "5"}], "documents": [{"Birth Certificate": "Birth Certificate", "Aadhar card": "<PERSON><PERSON><PERSON> card", "Others": "Others"}], "guidelines": [{"name": "", "line1": "", "line2": "", "line3": "", "line4": "", "phone": "", "fax": "", "email": "", "school_short": "", "school_full": ""}]}], [{"form_name": "KG1", "form_year": "2019-2020", "school_name": "", "instructin_file": "kg", "school_short": "", "school": "", "open_for_admissions": "FALSE", "class_applied_for": [{"KG1": ""}], "application_no_gen": [{"prefix": "KG", "name": "", "year": "2019", "digit_count": "5"}], "documents": [{"Birth Certificate": "Birth Certificate", "Aadhar card": "<PERSON><PERSON><PERSON> card", "Others": "Others"}], "guidelines": [{"name": "", "line1": "", "line2": "", "line3": "", "line4": "", "phone": "", "fax": "", "email": "", "school_short": "", "school_full": ""}]}], [{"form_name": "Grade 2 to Grade 9", "form_year": "2019-2020", "school_name": "", "instructin_file": "2-9", "school_short": "", "school": "", "open_for_admissions": "FALSE", "class_applied_for": [{"2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": ""}], "application_no_gen": [{"prefix": "2-9", "name": "", "year": "2019", "digit_count": "5"}], "documents": [{"Birth Certificate": "Birth Certificate", "Aadhar card": "<PERSON><PERSON><PERSON> card", "Others": "Others"}], "prev_eduction_info": [{"year": [{"2016-17": "", "2017-18": "", "2018-19": ""}], "result_types": [{"Percent": "", "Grade": ""}], "class": [{"2": [{"subject": [{"id": "1", "name": "English", "type": "label"}, {"id": "2", "name": "Language 2", "type": "text"}, {"id": "3", "name": "Maths", "type": "label"}, {"id": "4", "name": "Science", "type": "label"}]}]}, {"3": [{"subject": [{"id": "1", "name": "English", "type": "label"}, {"id": "2", "name": "Language 2", "type": "text"}, {"id": "3", "name": "Maths", "type": "label"}, {"id": "4", "name": "Science", "type": "label"}]}]}, {"4": [{"subject": [{"id": "1", "name": "English", "type": "label"}, {"id": "2", "name": "Language 2", "type": "text"}, {"id": "3", "name": "Maths", "type": "label"}, {"id": "4", "name": "Science", "type": "label"}]}]}, {"5": [{"subject": [{"id": "1", "name": "English", "type": "label"}, {"id": "2", "name": "Language 2", "type": "text"}, {"id": "3", "name": "Maths", "type": "label"}, {"id": "4", "name": "Science", "type": "label"}]}]}, {"6": [{"subject": [{"id": "1", "name": "English", "type": "label"}, {"id": "2", "name": "Language 2", "type": "text"}, {"id": "3", "name": "Language 3", "type": "text"}, {"id": "4", "name": "Maths", "type": "label"}, {"id": "5", "name": "Science", "type": "label"}]}]}, {"7": [{"subject": [{"id": "1", "name": "English", "type": "label"}, {"id": "2", "name": "Language 2", "type": "text"}, {"id": "3", "name": "Language 3", "type": "text"}, {"id": "4", "name": "Maths", "type": "label"}, {"id": "5", "name": "Science", "type": "label"}]}]}, {"8": [{"subject": [{"id": "1", "name": "English", "type": "label"}, {"id": "2", "name": "Language 2", "type": "text"}, {"id": "3", "name": "Language 3", "type": "text"}, {"id": "4", "name": "Maths", "type": "label"}, {"id": "5", "name": "Science", "type": "label"}]}]}, {"9": [{"subject": [{"id": "1", "name": "English", "type": "label"}, {"id": "2", "name": "Language 2", "type": "text"}, {"id": "3", "name": "Language 3", "type": "text"}, {"id": "4", "name": "Maths", "type": "label"}, {"id": "5", "name": "Science", "type": "label"}]}]}]}], "custom_field": [{"9": [{"II Language choice": [{"name": "II Language choice", "options": [{"Hindi": "", "French": "", "Sanskrit": ""}]}]}]}], "guidelines": [{"name": "", "line1": "", "line2": "", "line3": "", "line4": "", "phone": "", "fax": "", "email": "", "school_short": "", "school_full": ""}]}], [{"form_name": "Grade 11", "form_year": "2019-2020", "school_name": "", "instructin_file": "11", "school_short": "", "school": "", "open_for_admissions": "FALSE", "class_applied_for": [{"11": ""}], "application_no_gen": [{"prefix": "11", "name": "", "year": "2019", "digit_count": "5"}], "documents": [{"Birth Certificate": "Birth Certificate", "Others": "Others"}], "prev_eduction_info": [{"year": [{"2016-17": "", "2017-18": "", "2018-19": ""}], "result_types": [{"Percent": "", "Grade": ""}], "class": [{"11": [{"subject": [{"id": "1", "name": "English", "type": "label"}, {"id": "2", "name": "Language 2", "type": "text"}, {"id": "3", "name": "Language 3", "type": "text"}, {"id": "4", "name": "Maths", "type": "label"}, {"id": "5", "name": "Science", "type": "label"}]}]}]}], "streams": [{"Science": [{"id": "1", "name": "Math, Physics, Chemistry, Biology"}, {"id": "2", "name": "Math, Physics, Chemistry, Computer Science"}, {"id": "3", "name": "Math, Physics, Chemistry, Economics"}]}], "guidelines": [{"name": "", "line1": "", "line2": "", "line3": "", "line4": "", "phone": "", "fax": "", "email": "", "school_short": "", "school_full": ""}]}]]}]