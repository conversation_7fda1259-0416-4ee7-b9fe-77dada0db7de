<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  16 May 2018
 *
 * Description: Model class for Class Master table
 *
 * Requirements: PHP5 or above
 *
 */

class Other_fee extends CI_Controller {
	function __construct() {
		  parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      $this->load->model('report/fee_report_model');
	}


    public function index($filterMode=''){
      $data['classes'] =  $this->fee_report_model->get_Allclasses();
      $data['items'] =  $this->fee_report_model->getAllItems();
      $item = $this->input->post('item');
      $from_date = $this->input->post('from_date');
      $to_date = $this->input->post('to_date');
      if ($filterMode == '1')  {
        $data['SelectedClass'] = '';
        $data['SelectedItem'] = '';
        $data['SelectedFrom_date'] = '';
        $data['SelectedTo_date'] = '';
        $miscellaneous = $this->fee_report_model->miscellaneousDaywise();
      }else{
        $classId = $this->input->post('class_name');
        if (empty($classId)) {
          $classId = 0; //0 signifies show all classes
        } else {
          if ($classId == 'All') {
            $classId = 0; //0 signifies show all classes
          }
        }
       	$miscellaneous = $this->fee_report_model->miscellaneousDatetodate($classId,$item,$from_date,$to_date);
       	$data['items'] =  $this->fee_report_model->getAllItems();
  	 	$item = $this->input->post('item');
        $from_date = $this->input->post('from_date');
        $to_date = $this->input->post('to_date');   
        $data['SelectedClass'] = $classId;
        $data['SelectedItem'] = $item;
        $data['SelectedFrom_date'] = $from_date;
        $data['SelectedTo_date'] = $to_date;
      }
      $data['result_Data'] = $miscellaneous;
      $data['main_content'] = 'reports/miscellaneous/index';
      $this->load->view('inc/template', $data);
    }


    public function history_view_report(){
      $postData = $this->input->post();
      $filterMode = empty($postData);
      //Supported filters by daily report
      $reportFilters = ['class'];
      //Supported filters by school
      $feeConfig = $this->settings->getSetting('fees');
      $schoolSupportedFilters = $feeConfig['supportedFilters'];
      $finalFilters = array();
      foreach ($reportFilters as $rf) {
        foreach ($schoolSupportedFilters as $ssf) {
          if ($rf == $ssf) {
            $finalFilters[] = $rf;
            break;
          }
        }
      }
      $data['reportColumns'] = [
          ['name' => 'slNo', 'displayName' => 'Sl #'],
          ['name' => 'date','displayName' => 'Date'],
          ['name' => 'receiptNo','displayName' => 'Receipt No'],
          ['name' => 'stdName','displayName' => 'Name'],
          ['name' => 'className','displayName' => 'Class'],
          // ['name' => 'installment','displayName' => 'Installments'],
          ['name' => 'paidAmount','displayName' => 'Collected Amount'],
          ['name' => 'concession','displayName' => 'Concession'],
          ['name' => 'pos','displayName' => 'POS'],
          ['name' => 'fine','displayName' => 'Fine'],
          ['name' => 'medium','displayName' => 'Medium'],
          ['name' => 'discount','displayName' => 'Discount'],
          ['name' => 'remarks','displayName' => 'Remarks'],
          ['name' => 'pType','displayName' => 'Payment Type'],
          ['name' => 'collectedBy','displayName' => 'Collected By'],
        ];
    
      //Supported columns by school
      $feeConfig = $this->settings->getSetting('fees');
      $schoolSupportedColumns = $feeConfig['supportedColumns'];
      $finalColumns = array();
      foreach ($data['reportColumns'] as $rc) {
        foreach ($schoolSupportedColumns as $ssc) {
          if ($rc['name'] == $ssc) {
            $finalColumns[] = $rc;
            break;
          }
        }
      }

      $data['classes'] =  $this->fee_report_model->get_Allclasses();   
      $data['medium'] = $this->settings->getSetting('medium');
      $data['boarding'] = $this->settings->getSetting('boarding');
      $data['admission_type'] = $this->settings->getSetting('admission_type');
      $data['rteType'] = $this->settings->getSetting('rte');
      $data['board'] = $this->settings->getSetting('board');
      $data['donors'] = $this->fee_report_model->get_Alldonors();
      $data['payment_mode'] = $feeConfig['allowed_payment_modes'];
      $data['feeCollectedBy'] = $this->fee_report_model->get_feeCollectedName();
      $data['finalFilters'] = $finalFilters;
      $data['selectedColumns'] = $this->input->post('selectedColumns');
      $classId = $this->input->post('class_name');
      $data['finalColumns'] = $finalColumns;
      $data['finalColumnsJSON'] = json_encode($finalColumns);
      $data['main_content'] = 'reports/fees/fee_summary/history_view';
      $this->load->view('inc/template', $data);

    }


    public function generate_historyReport(){
  
      $selectedColumns = $this->input->post('selectedColumns'); 
      $finalColumns = json_decode($this->input->post('finalColumnsJSON'));
     
      $viewColumns = array ();
      if (!empty($selectedColumns)) {
        foreach ($selectedColumns as $sc) {
          foreach ($finalColumns as $rc) {
            if ($rc->name == $sc) {
              $viewColumns[] = $rc;
              break;
            }
          }
        }   
      } else {
        $viewColumns = $finalColumns;
      }
      
      $clsId = $this->input->post('clsId');
      $admission_type = $this->input->post('admission_type');
      $paymentModes = $this->input->post('paymentModes');
      $fee_type = $this->input->post('fee_type');
      $mediumId = $this->input->post('mediumId');
      $donorsId = $this->input->post('donorsId');
     // $boardingId = $this->input->post('boardingId');
      $rte_nrteId = $this->input->post('rte_nrteId');
      $created_byId = $this->input->post('created_byId');
      $from_date = $this->input->post('from_date');
      $to_date = $this->input->post('to_date');
      $stopId = $this->input->post('stopId');
      $routeId = $this->input->post('routeId');
      $itemId = $this->input->post('itemId');

      $medium = $this->settings->getSetting('medium');
      $dailyAcademicCollection = $this->fee_report_model->serachGeneration_history($clsId,$admission_type,$paymentModes,$mediumId,$donorsId,$rte_nrteId,$created_byId,$from_date,$to_date);
      $dailyFeeCollection = $dailyAcademicCollection;
       $feeConfig = $this->settings->getSetting('fees');
      $payment_mode = $feeConfig['allowed_payment_modes'];
      $padiAmount = 0;
      $totalConcession = 0;
      $fAmount = 0;
      $pos = 0;
      $dAmount = 0;
      $cAmount = 0;
      foreach ($dailyFeeCollection as $result){
        $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
        if (!empty($reqTemp)) {
          $padiAmount +=$result->amount_paid;
          $fAmount +=$result->fine_amount;
          $pos +=$result->card_charge_amount;
          $dAmount +=$result->discount_amount;
          $cAmount = $padiAmount + $fAmount + $pos - $dAmount;
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'concession');
          if (!empty($reqTemp)) {
          $totalConcession +=$result->total_concession;
        }
      }
      $template = "";

      $template .= '<div>
        <label>Total Collected Amount : '.$cAmount.'</label><br>
        <label>Total Concession : '.$totalConcession.'</label><br>
      </div>';
      $template .="<span class='help-block pull-right'>Collected amount includes discount, POS charge and fine amount. </span>";
      $template .="<table class='table table-bordered'>";
      $template .="<thead>";
      $template .="<tr>";

      $reqTemp  = $this->requireColumns($viewColumns, 'slNo');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'date');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'receiptNo');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'stdName');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'className');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'medium');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'stop');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'items');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }


      $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'concession');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'pos');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'fine');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'discount');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'pType');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'remarks');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'collectedBy');
      if (!empty($reqTemp)) {
        $template .='<th>'.$reqTemp->displayName.'</th>';
      }

      $template .="</tr>";
      $template .="</thead>";
      $template .="<tbody>";
      $template .="</tbody>";
      $i = 1;
      foreach ($dailyFeeCollection as $result){
        $template .="<tr>";

        $reqTemp  = $this->requireColumns($viewColumns, 'slNo');
          if (!empty($reqTemp)) {
          $template .='<td>'.$i++.'</td>';
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'date');
          if (!empty($reqTemp)) {
          $template .='<td>'.$result->date.'</td>';
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'receiptNo');
          if (!empty($reqTemp)) {
           $template .='<td>'.$result->receipt_number.'</td>';
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'stdName');
          if (!empty($reqTemp)) {
          $template .='<td>'.$result->student_name.'</td>';
        }


        $reqTemp  = $this->requireColumns($viewColumns, 'className');
          if (!empty($reqTemp)) {
          $template .='<td>'.$result->class_name.'</td>';
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'medium');
          if (!empty($reqTemp)) {
          foreach ($medium as $key => $med) {
            if ($result->medium == $key) { 
            $template .='<td>'.$med.'</td>';
            }
          }
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'stop');
        if (!empty($reqTemp)) {
          $template .='<td>'.$result->stopName.'</td>';
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'items');
        if (!empty($reqTemp)) {
          $template .='<td>'.$result->item_name.'</td>';
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
          if (!empty($reqTemp)) {
          // $cAmount = $result->amount_paid + $result->fine_amount +  $result->card_charge_amount - $result->discount_amount;
          $template .='<td>'.$result->amount_paid.'</td>';
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'concession');
          if (!empty($reqTemp)) {
          $template .='<td>'.$result->total_concession.'</td>';
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'pos');
          if (!empty($reqTemp)) {
          $template .='<td>'.$result->card_charge_amount.'</td>';
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'fine');
          if (!empty($reqTemp)) {
          $template .='<td>'.$result->fine_amount.'</td>';
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'discount');
          if (!empty($reqTemp)) {
          $template .='<td>'.$result->discount_amount.'</td>';
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'pType');
          if (!empty($reqTemp)) {
            foreach ($payment_mode as $key => $type) {
              if ($type['value'] == $result->payment_type) {
                if ($type['reconcilation_reqd'] == TRUE) {
                  if ($result->reconciliation_status == 1){
                   $template .='<td>'.strtoupper($type['name']) . ' <span style="color:red; font-weight:bold"> (N/C)</span>'.'</td>';
                  }elseif($result->reconciliation_status == 2){
                    $template .='<td>'.strtoupper($type['name']) . ' <span style="font-weight:bold"> (C)</span>'.'</td>';
                  }
                }else{
                  $template .='<td>'.strtoupper($type['name']).'</td>';
                } 
                
              }
            } 
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'remarks');
          if (!empty($reqTemp)) {
            $template .='<td>'.$result->remarks.'</td>';
          }

        $reqTemp  = $this->requireColumns($viewColumns, 'collectedBy');
        if (!empty($reqTemp)) {
          $template .='<th>'.$result->staff_name.'</th>';
        }

      }
      $template .="</tr>";
      $template .="</tbody>";
      $template .="</table";  
      print($template);
    // echo json_encode($challansDetails);
    }

    private function requireColumns($filterArray, $filter) {
      foreach ($filterArray as $f) {
        if ($f->name == $filter)
          return $f;
        }
        return array();
    }
  


} 
