<?php
defined('BASEPATH') or exit('No direct script access allowed');
/**
 *  oxygen
 *  Shree <PERSON> 
 *  25/12/2018
 *  Homework Module
 *
 */
class Homework extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('HOMEWORK') && !$this->authorization->isAuthorized('HOMEWORK.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('HomeworkModel', 'home');
    $this->load->library('filemanager');
    $this->load->helper('text');

    $this->templates = [
      ["name" => "Blank Template", "value" => ""],
      ["name" => "3-Column Table template", "value" => "<style>table-class {  font-family: arial, sans-serif;  border-collapse: collapse;  width: 100%;}td, th {  border: 1px solid #dddddd;  text-align: left;  padding: 6px;}table-class>tr:nth-child(even) {  background-color: #dddddd;}</style><table class='table-class'>  <tbody><tr>    <th widht='20%'>SUBJECT</th>    <th width='40%'>CHAPTER</th>    <th width='40%'>HOME WORK</th>  </tr>    <tr>    <td>English</td><td>-</td>    <td>-</td>  </tr>  <tr>    <td>Maths</td>    <td>-</td>    <td>-</td>  </tr>  <tr>    <td>Science</td>    <td>-</td>    <td>-</td>  </tr>  <tr>    <td>GP/EVM/SST</td><td>-</td>    <td>-</td>  </tr>  <tr>    <td>Language</td><td>-</td>    <td>-</td>  </tr><tr>    <td>ICT</td><td>-</td>    <td>-</td>  </tr></tbody></table>"],
      ["name" => "2-Column Table template", "value" => "<style>table-class {  font-family: arial, sans-serif;  border-collapse: collapse;  width: 100%;}td, th {  border: 1px solid #dddddd;  text-align: left;  padding: 6px;}table-class>tr:nth-child(even) {  background-color: #dddddd;}</style><table class='table-class'>  <tbody><tr>    <th width='20%'>SUBJECT</th> <th width='80%'>HOME WORK</th>  </tr>    <tr>    <td>English</td>  <td>-</td>  </tr>  <tr>    <td>Maths</td>    <td>-</td>  </tr>  <tr>    <td>Science</td>    <td>-</td>  </tr>  <tr>    <td>GP/EVM/SST</td><td>-</td>  </tr>  <tr>    <td>Language</td><td>-</td>  </tr><tr>    <td>ICT</td><td>-</td>  </tr></tbody></table>"],
      ["name" => "Simple Template", "value" => "<html><body><p><b>English</b><br>Put your homework here<br></p><p><b>Maths</b><br>Put your homework here<br></p></body></html>"],
      ["name" => "Template 1", "value" => "<p><br></p><table class='table table-bordered'><tbody><tr><td align='center'><span style='font-weight: bold;font-size:20px;'>Subjects</span></td><td align='center'><span style='font-weight: bold;font-size:20px;'>Assignments</span></td></tr><tr><td>Mathematics</td><td><br></td></tr><tr><td>English</td><td><br></td></tr><tr><td>Social Science</td><td><br></td></tr><tr><td>Science</td><td><br></td></tr><tr><td>Languages</td><td><br></td></tr></tbody></table><p><br></p><table class='table table-bordered'><tbody><tr><td rowspan='2' style='height:100px;'>Notes :</td></tr></tbody></table><p><br></p>"],
      ["name" => "Template 2", "value" => "<center><p></p><h4><span style='font-weight: bold;'>Homework Planner</span></h4><p></p></center><table class='table table-bordered'><tbody><tr><td align='center'><span style='font-weight: bold; font-size:12px;min-width: 90px'>Class</span></td><td align='center'><span style='font-weight: bold;font-size:12px;min-width: 116px'>Subject</span></td><td align='center'><span style='font-weight: bold;font-size:12px;min-width: 116px'>Homework</span></td><td align='center'><span style='font-weight: bold;font-size:12px;min-width: 116px'>Due Date</span></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr></tbody></table><p><br></p>"],
      ["name" => "Template 3", "value" => "<p><br></p><table class='table table-bordered'><tbody><tr><td align='center'><span style='font-weight: bold;font-size:14px; min-width: 116px;'>Subjects</span></td><td align='center'><span style='font-weight: bold;font-size:14px;min-width: 116px;'>Homework</span></td><td align='center'><span style='font-weight: bold;font-size:14px;min-width: 116px;'>Topics to be covered</span></td><td align='center'><span style='font-weight: bold;font-size:14px;min-width: 116px;'>Notes (if any)</span></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr></tbody></table><p><br></p>"],
      ["name" => "Template 4", "value" => "<p><br></p><table class='table table-bordered'><tbody><tr><td style='min-width:95px;'><b>Subject</b></td><td><span style='font-weight: bold;font-size:17px;'>Mathematics</span></td></tr><tr><td>Reading</td><td>Something to Read</td></tr><tr><td>Writing</td><td>Something to Write</td></tr><tr><td>Notes (If any)</td><td>And here is a note</td></tr></tbody></table><p><br></p>"]
    ];
  }

  public function index()
  {
    $loggedInStaffId = $this->authorization->getAvatarStakeHolderId();
    $data['sectionList'] = $this->home->getAllSections();
    if ($loggedInStaffId == '0') {
      //Super admin is logged in
      $data['classteacherSection'] = '-1';
    } else {
      $data['classteacherSection'] = $this->home->getClassTeacherSections($loggedInStaffId);
    }
    // if(empty($data['classteacherSection'])) { // In view page, it appears as if it is a non-object.
    //   $this->session->set_flashdata('flashError', 'Something went wrong, please try again');
    //   redirect('homework');
    // }
    $data['is_homework_admin'] = $this->authorization->isAuthorized('HOMEWORK.HOMEWORK_ADMIN');
    $data['submission_enabled'] = $this->authorization->isModuleEnabled('HOMEWORK_SUBMISSION');
    // echo "<pre>";print_r($data);die();

    $data['main_content'] = 'homework/index';   	

    $this->load->view('inc/template', $data);
  }

  public function view_submissions($homework_id) {
    $data['homework'] = $this->home->getHomeworkById($homework_id);
    $data['submissions'] = $this->home->getHomeworkSubmissionsById($homework_id, $data['homework']['csId']);
    $data['main_content'] = 'homework/submissions';
    $this->load->view('inc/template', $data);
  }

  public function getSubmissionDataByStudent(){
    $hs_id = $_POST['hs_id'];
    $data = $this->home->getSubmissionDataByStudent($hs_id);
    echo json_encode($data);


  }

  public function getSubmissionData() {
    $homework_id = $_POST['homework_id'];
    $student_id = $_POST['student_id'];
    $data = $this->home->getSubmissionData($student_id, $homework_id);
    echo json_encode($data);
  }

  public function addHomeworkRemarks() {
    $hs_id = $_POST['hs_id'];
    $remarks = $_POST['remarks'];
    $status = $this->home->addHomeworkRemarks($hs_id, $remarks);
    if($status) {
      //send notification to staff
      $sub = $this->home->getMinimalSubmissionData($hs_id);
      $message = 'Homework of '.$sub->homework_date.' with name '.$sub->name.' is checked by staff.';
      $this->load->helper('texting_helper');
      $input_arr = array();
      $input_arr['student_ids'] = [$sub->student_id];
      $input_arr['mode'] = 'notification';
      $input_arr['source'] = 'Homework';
      $input_arr['message'] = $message;
      $input_arr['student_url'] = site_url('parent_controller/homework_submissions/').$sub->homework_id;
      sendText($input_arr);
      echo 1;
    } else {
      echo 0;
    }
  }

  public function getHomeworkData()
  {
    

    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $sectionId = $this->input->post('sectionId');

    $homeworkList = $this->home->get_homework($sectionId, $from_date, $to_date);
    if(!empty($homeworkList)) {
      $submission_checklist = $this->home->get_homework_submission_list($sectionId, $from_date, $to_date);
      if(!empty($submission_checklist))
      foreach ($homeworkList as $key => $value) {
        if(array_key_exists($value->hwId, $submission_checklist)) {
          $value->created_date =  date('d-m-Y (D)', strtotime($value->created_date));
          $homeworkList[$key]->students = $submission_checklist[$value->hwId]['students'];
          $homeworkList[$key]->submissions = $submission_checklist[$value->hwId]['submissions'];
          $homeworkList[$key]->checked = $submission_checklist[$value->hwId]['checked'];
        }
      }
      
    } 
    echo json_encode($homeworkList);
  }

  public function getTemplate () {
    $templateName = $this->input->post('templateName');
    echo json_encode($this->__getTemplateByName($templateName));
  }

  private function __getTemplateByName ($templateName) {
    $templateValue = '';
    foreach ($this->templates as $temp) {
      if ($temp['name'] == $templateName) {
        $templateValue = $temp['value'];
        break;
      }
    }
    return $templateValue;
  }

  public function getSectionsByDate(){
    $classSectionList = $this->home->getAllClassSections_Ajax();
    echo json_encode($classSectionList);
  }

  public function getHomeworkDataByDateSection () {
    // echo "<pre>"; print_r($_POST); die();
    $selectedDate = $this->input->post('selectedDate');
    $sectionId = $this->input->post('sectionId');
    $templateName = $this->input->post('templateName');

    $homework = $this->home->get_homework_by_date_section($sectionId, $selectedDate);

    if (!$homework) {
      $data['template'] = $this->__getTemplateByName($templateName);
      $data['status'] = 0;
    } else {
      $data['template'] = $homework->body;
      $data['status'] = 1;
    }
    echo json_encode($data);
  }

  public function notReadList(){
    $data = $this->home->notReadList();
    echo json_encode($data);
  }

  public function addOrEdit($hwId = null) {
    $data['is_homework_admin'] = $this->authorization->isAuthorized('HOMEWORK.HOMEWORK_ADMIN');
    $data['submission_enabled'] = $this->authorization->isModuleEnabled('HOMEWORK_SUBMISSION');
    $loggedInStaffId = $this->authorization->getAvatarStakeHolderId();
    $data['sectionList'] = $this->home->getAllSections();
    $data['classSectionList'] = $this->home->getAllClassSections();
    $data['classteacherSection'] = $this->home->getClassTeacherSections($loggedInStaffId);
    $data['templates'] = $this->templates;
    $data['homework_default_template'] = $this->settings->getSetting('homework_default_template');
    // echo "<pre>";print_r($data['classSectionList']);die();
    if ($hwId != null) {
      $data['mode'] = 'edit';
      $data['selHomework'] = $this->home->getHomeworkById($hwId);
    } else {
      $data['mode'] = 'add';
      $data['selHomework'] = 0;
    }
    // echo '<pre>'; print_r($data); die();

    $data['video_size_limit']=$this->settings->getSetting('resources');

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'homework/add_edit_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'homework/add_edit_mobile';
    }else{
      $data['main_content'] = 'homework/desktop_add_edit';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function getHomeworkId()
  {
    $input = $this->input->post();
    $sectionId = $input['sectionId'];
    $date = $input['date'];
    echo $this->home->getHomeworkId($sectionId, $date);
  }

  /**
   * Store Data from this method.
   *
   * @return Response
   */
  public function store()
  {
    $filename = '';
    $_POST['date'] = date('Y-m-d', strtotime($_POST['date']));
    
    // echo "<pre>"; print_r($_FILES); die();
    $status = 0;
    $files_array = array();
    $files_string = '';
    if(isset($_FILES['homework'])) {
      foreach ($_FILES['homework']['name'] as $key => $file_name) {
        $file = array(
          'name' => $file_name,
          'type' => $_FILES['homework']['type'][$key],
          'tmp_name' => $_FILES['homework']['tmp_name'][$key],
          'error' => $_FILES['homework']['error'][$key],
          'size' => $_FILES['homework']['size'][$key]
        );
        $path = $this->s3FilUpload($file);
        if($path['file_name'] != '') {
          array_push($files_array, array('name' => $file_name, 'path' => $path['file_name']));
        }
      }
      // $files_string = implode(",", $files_array);
      if(!empty($files_array))
        $files_string = json_encode($files_array);
    }
    $status = $this->home->insert_home($files_string);

    /*if (isset($_FILES['homework'])) {
      $filepath = $this->s3FilUpload($_FILES['homework']);
      $status = $this->home->insert_home($filepath['file_name']);
    } else {
      $status = $this->home->insert_home();
    }*/

    if($status) {
      $section_type=$_POST['section'];
      if($section_type=='single'){
        $sectionId = $this->input->post('sectionId');
      }
      else{
        $sectionId =$_POST['classSectionId'];
      }
      $stdIds = $this->home->getStudentIds($sectionId);

      $student_ids = array();
      foreach ($stdIds as $key => $value) {
        array_push($student_ids, $value->id);
      }
      if(!empty($student_ids)) {
        $this->load->helper('notification_helper');
        $title = $this->settings->getSetting('school_name');
        $message = 'New homework added for your kid';
        $url = site_url('parent_controller/homework_view');
        sendStudentNotifications($student_ids, $title, $message, $url, 'Both');
      }
      $this->session->set_flashdata('flashSuccess', 'Successfully added homework.');
    } else {
      $this->session->set_flashdata('flashError', 'Failed to add homework.');
    }
    redirect('homework/addOrEdit');
  }

  public function update_image()
  {
    $filename = '';
    $_POST['date'] = date('Y-m-d', strtotime($_POST['date']));

    $status = 0;
    $files_array = array();
    $files_string = '';
    if(isset($_FILES['homework'])) {
      foreach ($_FILES['homework']['name'] as $key => $file_name) {
        $file = array(
          'name' => $file_name,
          'type' => $_FILES['homework']['type'][$key],
          'tmp_name' => $_FILES['homework']['tmp_name'][$key],
          'error' => $_FILES['homework']['error'][$key],
          'size' => $_FILES['homework']['size'][$key]
        );
        $path = $this->s3FilUpload($file);
        if($path['file_name'] != '') {
          array_push($files_array, array('name' => $file_name, 'path' => $path['file_name']));
        }
      }
      // $files_string = implode(",", $files_array);
      if(!empty($files_array))
        $files_string = json_encode($files_array);
    }
    $status = $this->home->update_home_work($files_string);
    /*if (isset($_FILES['homework'])) {
      $filepath = $this->s3FilUpload($_FILES['homework']);
      $status = $this->home->update_home_work($filepath['file_name']);
    } else {
      $status = $this->home->update_home_work();
    }*/

    if($status) {
      $sectionId = $this->input->post('sectionId');
      $stdIds = $this->home->getStudentIds($sectionId);
      $student_ids = array();
      foreach ($stdIds as $key => $value) {
        array_push($student_ids, $value->id);
      }
      $homId = trim($this->input->post('homeworkId'));
      if(!empty($student_ids)) {
        $this->load->helper('notification_helper');
        $title = $this->settings->getSetting('school_name');
        $message = 'Homework updated for your kid.';
        $url = site_url('parent_controller/view_homework/').$homId;
        sendStudentNotifications($student_ids, $title, $message, $url, 'Both');
      }
      $this->session->set_flashdata('flashSuccess', 'Successfully added homework.');
    } else {
      $this->session->set_flashdata('flashError', 'Failed to add homework.');
    }

    redirect('homework/addOrEdit');
  }

  public function webcamImage($date, $sectionId)
  {
    $data['date'] = $date;
    $data['sectionId'] = $sectionId;
    $data['main_content'] = 'homework/webcam';
    $this->load->view('inc/template', $data);
  }

  public function addImage()
  {
    $filename = '';
    if (isset($_FILES['webcam']['name'])) {
      $filepath = $this->s3FileUpload($_FILES['webcam']['name']);
      echo $filepath['file_name'];
    }
  }

  public function addCapture()
  {
    $filename = $_POST['file_name'];
    $sectionId = $_POST['sectionId'];
    $loggedInStaffId = $this->authorization->getAvatarStakeHolderId();
    $date = $_POST['date'];
    $status = $this->home->insertImage($date, $sectionId, $filename, $loggedInStaffId);
    redirect('homework/index');
  }

  public function fetch_section()
  {
    if ($this->input->post('clasId')) {
      echo $this->HomeworkModel->fetch_sectionid($this->input->post('clasId'));
    }
  }

  /**
   * Uplaod image from this method.
   */
  public function upload($filename = '', $upload_path)
  {
    $config['upload_path'] = $upload_path;
    $config['allowed_types'] = 'jpg|png|jpeg|GIF|JPG|PNG|JPEG';
    $config['max_size'] = 3040;
    $config['max_width'] = 1900;
    $config['max_height'] = 1200;
    // $config['remove_spaces'] = true;
    //  $config['overwrite'] = false;
    $this->load->library('upload', $config);
    $this->upload->initialize($config);
    if (!$this->upload->do_upload($filename)) {
      $error = array(
        'status' => 'error',
        'data' => $this->upload->display_errors()
      );
      $this->session->set_flashdata('flashError', 'Failed to upload - ' . $filename);
      $errors = array(
        'error' => $this->upload->display_errors()
      );
      $post_image = 'noimage.jpg';
      return $error;
    } else {
      $image = $this->upload->data();
      $success = array(
        'status' => 'success',
        'data' => $image
      );
      return $success;
    }
    redirect('Homework/index');
  }

  public function mobile_view()
  {
    $loggedInStaffId = $this->authorization->getAvatarStakeHolderId();
    $data['sectionList'] = $this->home->getAllSections();
    $data['classteacherSection'] = $this->home->getClassTeacherSections($loggedInStaffId);

    $data['main_content'] = 'homework/mobile_view';
    $this->load->view('inc/template', $data);
  }

  public function s3FilUpload($file)
  {
    if ($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }

    return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], 'home_work');
  }

  /**
   * Update Data from this method.
   */
  public function update($id)
  {
    $filename = '';
    $home = new HomeworkModel;
    if (isset($_FILES['usersfile']['name'])) {
      $filepath = $this->s3FilUpload($_FILES['usersfile']);
      $result = $this->HomeworkModel->update_home($id, $filepath['file_name']);
    } else {
      $result = update_home($id);
    }

    if ($result) {
      $this->session->set_flashdata('flashSuccess', "Update Successful");
    } else {
      // $this->session->set_flashdata('flashError', "Something went wrong");
    }
    // $home->update_home($id);
    redirect('Homework/index');
  }

  public function delete($id)
  {
    $this->db->set('status', 0);
    $this->db->where('id', $id);
    $status = $this->db->update('homework');
    return $status;
  }

  public function s3FileUpload($file){
    if ($_FILES['webcam']['tmp_name'] == '' || $_FILES['webcam']['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }
    return $this->filemanager->uploadFile($_FILES['webcam']['tmp_name'], $_FILES['webcam']['name'], 'home_work');
  }

  public function check_exit_database(){
    $input = $this->input->post();
    $sectionId = $input['sectionId'];
    $date = date('Y-m-d');
    echo $this->home->check_exit_database_byclssteacher($date, $sectionId);
  }

  public function upload_image(){
    $filename = $_POST['file_name'];
    $homeworkId = $_POST['homeworkId'];
    $input = $this->input->post();
    $sectionId = $input['sectionId'];
    $date = date('Y-m-d', strtotime($_POST['date']));
    $status = $this->home->updateImage($homeworkId, $date, $sectionId, $filename);

    redirect('homework/index');
  }

  public function view_homework($id){
    $data['submission_enabled'] = $this->authorization->isModuleEnabled('HOMEWORK_SUBMISSION');
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'homework/view_homework_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'homework/view_homework_mobile';
    }else{
      $data['main_content'] = 'homework/view_homework';    	
    }
    $this->load->view('inc/template', $data);
    //echo '<pre>'; print_r(view_homeworkdata['schedules']); die();
  }

  public function get_homework_by_id(){
    $homeworkId=$_POST["homeworkId"];
    $result=$this->home->getHomeworkById($homeworkId);
    echo json_encode($result);
  }

  public function homework_reports(){
        // echo "hello"; die();

    // echo $section_id;
    // echo "<pre>"; print_r($_POST); die();
    // if(isset($_POST['daterange'])){
    //   $data['daterange'] = $_POST['daterange'];
    //   $data['from_date'] =$_POST['from_date'];
    //   $data['to_date'] =$_POST['to_date'];
    //   $data['selected_section'] = $_POST['selected_section']; // To Do change
    //   // echo '<pre>'; print_r($_POST); die();

    //   $data['hwreports'] = $this->home->gethomeworkReports($_POST['selected_section']);
      
    //   // echo "hello"; die();
    // } else{
    //   $data['hwreports'] = $this->home->gethomeworkReports($section_id);
    //   $data['daterange'] = 1;
    //   $data['from_date'] = '';
    //   $data['to_date'] = '';
    //   $data['selected_section'] = -1;
    // }


    $data['sectionList'] = $this->home->getAllSections();

    // echo '<pre>'; print_r($data); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'homework/homework_report_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'homework/homework_report_mobile';
    }else{ 	
      $data['main_content'] = 'homework/homework_report';
    }
    $this->load->view('inc/template', $data);
  }

  public function get_homework_report($section_id = '-1'){
    // echo "<pre>";print_r($_POST);die();
    // if(isset($_POST['daterange'])){
    //   $data['daterange'] = $_POST['daterange'];
    //   $data['from_date'] =$_POST['from_date'];
    //   $data['to_date'] =$_POST['to_date'];
    //   $data['selected_section'] = $_POST['selected_section']; // To Do change

    //   // $data['hwreports'] = $this->home->gethomeworkReports($_POST['selected_section']);
    //   $result= $this->home->gethomeworkReports($_POST['selected_section']);
    //   echo json_encode($result);
      
    // } else{
    //   // $data['hwreports'] = $this->home->gethomeworkReports($section_id);
    //   $data['daterange'] = 1;
    //   $data['from_date'] = '';
    //   $data['to_date'] = '';
    //   $data['selected_section'] = -1;
    //   $result= $this->home->gethomeworkReports($_POST['selected_section']);
    //   echo json_encode($result);
    // }
    $result= $this->home->gethomeworkReports($_POST['selected_section']);
    echo json_encode($result);
  }

  public function student_wise_homework_report(){
    $data['sectionList'] = $this->home->getAllSections();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'homework/homework_report_student_wise_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'homework/homework_report_student_wise_mobile';
    }else{
      $data['main_content'] = 'homework/homework_report_student_wise';    	
    }
    $this->load->view('inc/template', $data);//it starts from here . this is the function
  }

  public function getStudentsByClass(){
    $section = $_POST['section'];

		$data['studentData'] = $this->home->getStudentsByClass($section);
		// echo "<pre>";print_r($data);die();	
    echo json_encode($data);
  }

  public function get_homework_by_student_date()
  {
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $studentId = $this->input->post('studentId');
    $sectionId = $this->input->post('sectionId');

    $data['hwData'] = $this->home->get_homework_by_student_date($studentId, $sectionId, $from_date, $to_date);
    echo json_encode($data);
  }

  public function get_student_wise_homework_report(){
    $result=$this->home->get_student_wise_homework_report($_POST);
    echo json_encode($result);
  }

  public function getHomework() {
    $id = $_POST['id'];
    echo json_encode($this->home->getHomeworkView($id));
  }
}
