<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON>
 *          <EMAIL>
 *
 * Created:  04 August 2022
 *
 * Description: Controller for Donation Module. Entry point for Donation Module
 *
 * Requirements: PHP5 or above
 *
 */

class Menu extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
  }

  //Landing function to show non-compliance menu
  public function index() {
    $site_url = site_url();

    $data['tiles'] = array(
      [
        'title' => 'Manage Donors',
        'sub_title' => 'View and add Donors',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'donation/manage_donors/index',
        'permission' => $this->authorization->isAuthorized('DONATION.MODULE')
      ],
      [
        'title' => 'Add Pledge For',
        'sub_title' => 'View and add Pledge for category',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'donation/manage_donors/pledge_for',
        'permission' => $this->authorization->isAuthorized('DONATION.MODULE')
      ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
      [
        'title' => 'Donor Summary',
        'sub_title' => 'Donor Summary',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'donation/manage_donors/donor_summary_report',
        'permission' =>  $this->authorization->isAuthorized('DONATION.VIEW_REPORTS')
      ],
      [
        'title' => 'Pledge Summary',
        'sub_title' => 'Pledge Summary',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'donation/manage_donors/pledge_summary_report',
        'permission' =>  $this->authorization->isAuthorized('DONATION.VIEW_REPORTS')
      ],
       [
        'title' => 'Donation Collections',
        'sub_title' => 'Donations Summary',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'donation/manage_donors/donation_collections',
        'permission' =>  $this->authorization->isAuthorized('DONATION.VIEW_REPORTS')
      ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['main_content']    = 'donation/menu';
    $this->load->view('inc/template', $data);
  }

}