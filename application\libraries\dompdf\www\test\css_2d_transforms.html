<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
 "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en"><head>
<title></title>


<style>
  body {
    font-family: sans-serif;
    font-size: 11px;
  }
  div.transformed {
    border: 1px solid red;
    -webkit-transform-origin: 50% 50%;
    -moz-transform-origin: 50% 50%;
    -ms-transform-origin: 50% 50%;
  }
  div.transformed:after {
    content: attr(style);
  }
  
  div.grid {
    border: 1px dotted grey;
    margin: 0;
    padding: 1em;
    margin-bottom: -1px;
  }
</style>

<!--[if IE]>
<script type="text/javascript" src="../cssSandpaper/js/EventHelpers.js"></script>
<script type="text/javascript" src="../cssSandpaper/js/cssQuery-p.js"></script>
<script type="text/javascript" src="../cssSandpaper/js/jcoglan.com/sylvester.js"></script>
<script type="text/javascript" src="../cssSandpaper/js/cssSandpaper.js"></script>

<script type="text/javascript">
  if (!document.documentMode || document.documentMode < 9)
  
  window.onload = function(){
    var nodes = document.querySelectorAll("*[style]");
    
    for (var i = 0; i < nodes.length; i++) {
      var style = nodes[i].getAttribute("style");
      var trans = /-ms-transform\s*:\s*([^;]+)/i.exec(style);
      
      try {
        if (trans && trans[1] !== "none") {
          cssSandpaper.setTransform(nodes[i], trans[1]);
        }
      } catch(e) {}
    }
  }
</script>
<![endif]-->

</head>
<body>

<h3>none</h3>
<div class="grid"><div class="transformed" style="-webkit-transform: none; -moz-transform: none; -ms-transform: none;">&nbsp; </div></div>

<h3>rotate</h3>
<div class="grid"><div class="transformed" style="-webkit-transform: rotate(0.1rad); -moz-transform: rotate(0.1rad); -ms-transform: rotate(0.1rad);">&nbsp; </div></div>

<h3>scale</h3>
<div class="grid"><div class="transformed" style="-webkit-transform: scale(0.5, 1.5); -moz-transform: scale(0.5, 1.5); -ms-transform: scale(0.5, 1.5);">&nbsp; </div></div>
<div class="grid"><div class="transformed" style="-webkit-transform: scale(0.5); -moz-transform: scale(0.5); -ms-transform: scale(0.5);">&nbsp; </div></div>
<div class="grid"><div class="transformed" style="-webkit-transform: scaleX(0.5); -moz-transform: scaleX(0.5); -ms-transform: scaleX(0.5);">&nbsp; </div></div>
<div class="grid"><div class="transformed" style="-webkit-transform: scaleY(0.5); -moz-transform: scaleY(0.5); -ms-transform: scaleY(0.5);">&nbsp; </div></div>

<h3>translate</h3>
<div class="grid"><div class="transformed" style="-webkit-transform: translate(10px, 10px); -moz-transform: translate(10px, 10px); -ms-transform: translate(10px, 10px);">&nbsp; </div></div>
<div class="grid"><div class="transformed" style="-webkit-transform: translate(20px); -moz-transform: translate(20px); -ms-transform: translate(20px);">&nbsp; </div></div>
<div class="grid"><div class="transformed" style="-webkit-transform: translateX(20%); -moz-transform: translateX(20%); -ms-transform: translateX(20%);">&nbsp; </div></div>
<div class="grid"><div class="transformed" style="-webkit-transform: translateY(30%); -moz-transform: translateY(30%); -ms-transform: translateY(30%);">&nbsp; </div></div>

<h3>skew</h3>
<div class="grid"><div class="transformed" style="-webkit-transform: skew(30deg, -4deg); -moz-transform: skew(30deg, -4deg); -ms-transform: skew(30deg, -4deg);">&nbsp; </div></div>
<div class="grid"><div class="transformed" style="-webkit-transform: skew(-4deg); -moz-transform: skew(-4deg); -ms-transform: skew(-4deg);">&nbsp; </div></div>
<div class="grid"><div class="transformed" style="-webkit-transform: skewX(20deg); -moz-transform: skewX(20deg); -ms-transform: skewX(20deg);">&nbsp; </div></div>
<div class="grid"><div class="transformed" style="-webkit-transform: skewY(-4deg); -moz-transform: skewY(-4deg); -ms-transform: skewY(-4deg);">&nbsp; </div></div>

<h3>mixed</h3>
<div class="grid"><div class="transformed" style="-webkit-transform: rotate(10deg) scale(0.5, 1.5); -moz-transform: rotate(10deg) scale(0.5, 1.5); -ms-transform: rotate(10deg) scale(0.5, 1.5);">&nbsp; </div></div>

</body>
</html>
