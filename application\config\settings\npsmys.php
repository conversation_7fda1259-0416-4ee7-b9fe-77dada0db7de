<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  06 July 2018
 *
 * Description:  Contains the configuration details for NorthHills.
 *
 * Requirements: PHP5 or above
 *
 */
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| School General Settings
| -------------------------------------------------------------------------
| All the meta data for the school is stored in this section.
*/

/*
| -------------------------------------------------------------------------
| School Meta data
|---------------------------------------------------------------------------
*/

/*
| -------------------------------------------------------------------------
| Admission Number
| Config settings for generating admission number - 
| manual: (TRUE/FALSE) - If false, generate admission number automatically.
| admission_generation_algo - Algorithm to use to generate admission number. Vaues -
| 1) NEXTELEMENT - default
| 2) WPL
| 3) NH
| infix - The infix to use in the algo
| digit-count - ?
| index_offset - The starting number for admissions.
|---------------------------------------------------------------------------
*/
$config['school_name'] = 'NPS International School Mysuru';
$config['school_name_line1'] = 'NPS International School';
$config['school_name_line2'] = 'Mysuru';
$config['school_short_name'] = "npsmys";
$config['school_image'] = 'assets/img/npsmys.jpg';
$config['school_logo'] = 'assets/img/npsmys_logo.gif';
$config['company_logo'] = 'assets/img/nps.png';
$config['company_name'] = 'eteachguru';
$config['login_background'] = 'assets/img/npsmys_login_bg.jpg';
$config['favicon'] = 'https://s3.amazonaws.com/nextelement-common/scicon16pxeteach.png';
$config['school_header'] = 'https://s3.amazonaws.com/nextelement-common/Nps+Mysore/Marks+Card+Template/npsmysore_header.png';
$config['admission_number'] = [ 
                                'manual' => TRUE, 
                                'admission_generation_algo' => 'NEXTELEMENT',
                                'infix' => 'NPSMYS',
                                'digit_count' => 5,
                                'index_offset' => 0
                              ];

/*
| -------------------------------------------------------------------------------------
| Timetable
| -------------------------------------------------------------------------------------
| header_class_section_id: Provide the class_section id of the section timetable from which the timetable header should be used.
| -------------------------------------------------------------------------------------
*/
$config['timetable']['header_class_section_id'] = 40;
$config['timetable']['half_day_period'] = 5;
$config['timetable']['last_period'] = 9;
$config['timetable']['consider_4_alloc_p6_for_cs_lab'] = 1;  //This config is required *ONLY* for NPS. This is a hack to support a 4-section allocation for NPS RNR for Computer Sr Lab.

$config['timetable']['timetable_template_for_staff'] = 2;
$config['timetable']['timetable_template_for_room'] = 2;

/*
| --------------------------------------------------------------------------
| Modules
| Define all the modules enabled for the school. The ones that are not here aren't enabled for the school
|
| Basic modules allowed for all schools by default - 
| SCHOOL_ADMIN - Enable School Admin in sidemenu.
| PROFILE - Enable Staff Profile
| ACTIVITY - Enable Activity in sidemenu.
| COMMUNCIATION - Enable Communication in sidemenu.
| PERMISSIONS - Manage roles, assign privileges.
| CLASS_MASTER - Add classes.
| STUDENT_MASTER - CRUD Student data.
| STAFF_MASTER - CRUD Staff data.
| SUBJECTS_MASTER - CRUD Subjects, add and assign elective subjects to students.
| STAFF_LOGIN - Staff console, Staff dashboard.
| 
| The following are optional modules and are enabled based on school requirements -
| REGISTER - Enable Register in sidemenu
| STUDENT_ATTENDANCE - Take student attendance.
| LEAVE_MANAGEMENT: Enable Leave management in sidemenu.
| STUDENT_LEAVE - Students can apply leave.
| STAFF_LEAVE - Staff can apply leave.
| STAFF_ATTENDANCE - Take Staff attendance.
| EXAMINATION - Create and publish Exam timetable and portions, Marks card generation.
| TIMETABLE - Add Staff, Subject and Section association, Construct Timetable.
| SUBSTITUTION - Substitute absent staff
| FEES - Create Fee Structure, Pay Fees.
| LIBRARY - CRUD Books, manage Library transacations.
| INVENTORY - CRUD Inventory, manage inventory transacations.
| PARENTS_LOGIN - Parents console, Parents dashboard.
| ONLINE_EXAM - Teachers create online examination. Students can take the exam under Parent's guidance.
| ASSIGNMENT - Teachers can create assignment. Parents can view assignment.
| PUBLICATIONS - Teachers can create publications for school, class, section and students.
| PTM - To handle Parent-Teachers' meeting.
| PARENT_INITIATIVE - Parents can take an initiative in the school.
| STAFF_INITIATIVE - Staff can take an initiative in the school.
| WORKSHOP - Similar to Events.
| COMPETITION - CRUD Competitions.
| EVENTS - CRUD Events.
| VISITOR - Track visitors.
| STUDENT_EMERGENCY_EXIT - Track students leaving the class in between the day.
| SCHOOL_CALENDAR - Enables entering holiday list, events, competitions, examination calendar, etc.
| TRANSPORT - CRUD Transport.
| SMS - Send SMS to School, Class, Section or Student.
| REPORTS - Access to reports side-menu bar.
| 
| --------------------------------------------------------------------------
*/
$config['modules'] = 
array( 'SCHOOL_ADMIN', 'STUDENT_MASTER', 'STAFF_MASTER','REPORTS','TIMETABLE','SUBSTITUTION', 'BUILDING_MASTER','STAFF_LOGIN','COMPETITION','VISITOR','REGISTER','COMMUNICATION','ACTIVITY','TASKSTODO','PARENTS_LOGIN','FEES_NH','STUDENT_LEAVE','SCHOOL_CALENDAR','INVENTORY','STAFF_LEAVE','EXAMINATION','ROOM_BOOKING', 'STUDENT_ATTENDANCE','STUDENT_OBSERVATION','STAFF_OBSERVATION','SMS','STAFF_INITIATIVE','PERMISSIONS');

/**
 * 
 * Modules that should be shown in Parent module
 * STUDENT_LEAVE: Shows Student Leave
 * ATTENDANCE: Shows Student Attendance
 * CIRCULARS: Shows Student Circulars
 * SMS: Shows Student Messages
 * ASSESSMENTS: Shows Student Assessments
 * SCHOOL_CALENDAR: Shows Student School Calendar
 * PARENT_INITIATIVE: Show Parent Initiative
 * FEES: Shows Fees
 * PTM: Parents Teachers' meeting
 */
$config['parent_modules'] = 
array('STUDENT_LEAVE', 'ATTENDANCE', 'CIRCULARS', 'SMS', 'TIMETABLE', 'ASSESSMENTS', 'SCHOOL_CALENDAR', 'PARENT_INITIATIVE', 'FEES', 'MARKS_CARD', 'ASSESSMENT_POSRTIONS_V1');

/*
| --------------------------------------------------------------------------
| Attendance Module
| Define the Attendance Module of class per the school standards
|
| --------------------------------------------------------------------------
*/

$config['attendance']['absentee_sms'] = 'Your ward %std_name% of %cs_name% is absent today %date%. Kindly ignore the sms if you have already informed the school through email. Principal - NPSMYS';
$config['attendance']['latecomer_sms'] = 'Your ward %std_name% of %cs_name% has come to school late today %date%. Principal - NPSMYS';

/*
| --------------------------------------------------------------------------
| Board
| Define all the boards the school supports in this array.
|
| Allowed values are -
| 1 -> 'State', 2->CBSE, 3 -> 'ICSE', 4-> 'IGCSE'
| --------------------------------------------------------------------------
*/
$config['board'] = array(
    '2' => 'CBSE'
  );

/*
| --------------------------------------------------------------------------
| Medium
| Define all the mediums the school supports in this array.
|
| Allowed values are -
| 1 -> English, 2 -> Kannada, 3 -> Hindi
| --------------------------------------------------------------------------
*/
$config['medium'] = array(
  '1' => 'English',
);

/*
| --------------------------------------------------------------------------
| classType
| Define the type of class per the school standards
|
| Values here will just show up when creating a class
| --------------------------------------------------------------------------
*/
$config['classType'] = array(
  '1' => 'Pre-Nursery (PREP-1 TO  PREP-3)',
  '2' => 'High School (1 TO 10)',
);

/*
| --------------------------------------------------------------------------
| Admission Type
| Define all the admission types the school supports in this array.
|
| Allowed values are -
| 1 -> Re-admission, 2 -> New Admission
| --------------------------------------------------------------------------
*/
$config['admission_type'] = array(
  '1'=>'Re-admission',
  '2'=>'New Admission'
);

/*
| --------------------------------------------------------------------------
| Admission Status
| Define all the admission status the school supports in this array.
|
| Allowed values are -
|1 -> Pending, 2 -> Approved, 3 -> Rejected 4 -> Alumni
| --------------------------------------------------------------------------
*/

$config['admission_status'] = array(
  '1'=>'Pending',
  '2'=>'Approved',
  '3'=>'Rejected',
  '4'=>'Alumni'
);

/*
| --------------------------------------------------------------------------
| Staff Selection Status
| Define all the Staff status the school supports in this array.
|
| Allowed values are -
|1 -> Pending, 2 -> Approved, 3 -> Rejected 4 -> Resigned
| --------------------------------------------------------------------------
*/
$config['staff_status'] = array(
  '1'=>'Pending',
  '2'=>'Approved',
  '3'=>'Rejected',
  '4'=>'Resigned',
  '5'=>'Retired'
);

/*
| --------------------------------------------------------------------------
| RTE Options
| Define all the RTE Options the school supports in this array.
|
| Allowed values are -
| 1 -> RTE, 2 -> Non-RTE
| --------------------------------------------------------------------------
*/
$config['rte'] = array(
  '2'=>'Non-RTE',
  '1'=>'RTE'
);

/*
| --------------------------------------------------------------------------
| Boarding
| Define all the boarding types the school supports in this array.
|
| Allowed values are -
| 1-> Day School, 2 -> Residential
| --------------------------------------------------------------------------
*/
$config['boarding'] = array(
  '1'=>'Day School',
);

/*
| --------------------------------------------------------------------------
| Category
| Define all the student categories the school supports in this array.
| 
| Allowed values are -
| '1' => 'General',
| '2' => 'SC/ST',
| '3' => 'CATEGORY IIA',
| '4' => 'CATEGORY IIB',
| '5' => 'CATEGORY IIIA',
| '6' => 'OBC'
| eg: $config['category'] = array('General', 'SC/ST');
| --------------------------------------------------------------------------
*/
$config['category'] = array(
  '1' => 'General',
  '2' => 'SC/ST',
  '3' => 'CATEGORY IIA',
  '4' => 'CATEGORY IIB',
  '5' => 'CATEGORY IIIA',
  '6' => 'OBC'
);

/*
|-----------------------------------------------------------------------
| This setting is used to set the allocation template for online room booking. Set this value to an existing (i.e. already created) timetable_template->id.
|-----------------------------------------------------------------------
*/

$config['online_room_booking_template_id'] = '2';

/*
| --------------------------------------------------------------------------
| Address Types
| Define all the address types the school likes to collect from student and parents.
|
| Allowed values are -
|(1: Present Address and 2: Permanent Address) or (1: Office Address And 2: Home Address)
| --------------------------------------------------------------------------
*/

$config['address_types'] = array(
  'Student'=> array(),
  'Father'=>  array("0" => "Office Address", "1" => "Home Address"),
  'Mother'=>  array("0" => "Office Address", "1" => "Home Address")
);

/*
| -------------------------------------------------------------------------
| Fees.
| -------------------------------------------------------------------------
| Fee Specific settings
*/

/*
| --------------------------------------------------------------------------
| Filter Criteria
|
| This is used for creating Master Fee Structure.
|
| Different schools have different filter criteria. For eg: NPS RNR uses academic year
| when student joined and whether he is RTE candidate to determine Fee Structure.
| NPS CNP uses class the student is going into and RTE candidature.
| Specify here the filter criteria to be used.
| This should be the same table column name you use in Student.
|
| The currently supported school criteria are - academic_year_of_joining, rte, class,
| medium, category, admission_type, boards, boarding
| --------------------------------------------------------------------------
|   Allowed values are -
|   0 -> 'admission_type',
|   1 -> 'medium
|   2 -> 'rte
|   3 -> 'category
|   4 -> 'academic_year_of_joining
|   5 -> 'class
|   6 -> 'boards
|   7 -> 'boarding
*/
$config['fees']['filter_criteria'] = array('admission_type');

/*
| ------------------------------------------------------------------------------
| Fee Components
|
| This is used to create Fee Components in Master.
|
| Different schools have different fee components. We assume that the components are same across all
| fee Structures. If this is wrong, we have to handle this as a database table rather than config.
|
| Add all the different components in 'Allocation Order' (See Allocation algorithm).
|      
| --------------------------------------------------------------------------------
*/

$config['fees']['components'] = array(
  array ( 'name' => 'Admission', 'concession_eligible' => TRUE ),
);

/*
| ----------------------------------------------------------------------------------
| Fee Payment modes
| 
| This is used in Fee payment.
| Add all the payment modes supported by this school
| Allowed values - 

            $config['allowed_payment_modes'] = array(
                            
                              '1'=>'dd',
                              '2'=>'credit card',
                              '3'=>'debit card',
                              '4'=>'cheque'
                              '5'=>'wallet payment'
                              '6'=>'challan'
                              '7'=>'Card (POS)'
                              '8'=> Net Banking (POS)
                              '9'=>'cash',
                            );

| ------------------------------------------------------------------------------------
*/
$config['fees']['allowed_payment_modes'] = array(
  array ('name'=>'cash','value'=>'9', 'reconcilation_reqd' => FALSE),
  array ('name'=>'card (POS)','value'=>'7', 'reconcilation_reqd' => FALSE),
  array ('name'=>'net banking','value'=>'8', 'reconcilation_reqd' => TRUE)
);

/*
| ----------------------------------------------------------------------------------
| Fee Receipt template
| 
| This is used to generate the fee receipt.
| Add the template file that should be used (probably customized) for this school.
| ------------------------------------------------------------------------------------
*/
$config['fees']['receipt_template'] = 'NHS';


/*
| ----------------------------------------------------------------------------------
| Fee Component Allocation Type
| 
| This is used in Fee transaction.
|
| If TRUE, the allocation algorithm will kick in to allocate the total amount entered across Fee Components.
| If FALSE, the allocation has to be manually keyed in by the user (cashier).
|
| ------------------------------------------------------------------------------------
*/
$config['fees']['component_allocation_type_auto'] = TRUE;

$config['fees']['cComponent_allocation_type_auto'] = TRUE;
/*
| --------------------------------------------------------------------------------------
| Fee Component Allocation algorithm
| 
| This is used in conjunction with Fee transaction.
|
| If Allocation algorithm is AUTO, then the specified algorithm is used to allocate total amount across Fee Components.
|
| Algo 1: 'ALLOCATE_FROM_LEFT_TO_RIGHT' - This algorithm allocates the total amount from left to right as specified in the fee component array above.
| It will allocate the full amount for the component before proceeding to the next.
|
| ----------------------------------------------------------------------------------------
*/
$config['fees']['component_allocation_algorithm_if_auto'] = 'ALLOCATE_FROM_LEFT_TO_RIGHT';


/*
| --------------------------------------------------------------------------------------
| Fee receipt generation
| ----------------------------------------------------------------------------------------
*/

$config['fees']['recipt_number_gen'] = [ 
                      'fee_generation_algo' => 'NHS',
                      'infix' => 'NHS',
                      'digit_count' => 4,
                      ];

/*
| --------------------------------------------------------------------------------------
| Discount for fully paid fee amount for north hill school only  
| ----------------------------------------------------------------------------------------
*/
$config['fees']['discount'] =[
                        'percent'=> 5,
                        'isProvided' => TRUE,
                        'isManual' => FALSE
                       ];

/*
| --------------------------------------------------------------------------
| Attendance Module
| Define the Attendance Module of class per the school standards
|
| --------------------------------------------------------------------------
*/

$config['student_action']['outGoing'] = [
  [ 'class' => 'KG1,KG2,1,2,3,4,5',
    'time-range' => '7:20 am - 11:45 am'
  ],
  [ 'class' => '6,7,8,9,10,11,12',
    'time-range' => '8:20 am - 12:20 pm'
  ]
];

$config['student_action']['healthCare'] = [
  [ 'class' => 'KG1,KG2,1,2,3,4,5',
    'time-range' => '8:20 am - 11:45 am'
  ],
  [ 'class' => '6,7,8,9,10,11,12',
    'time-range' => '8:20 am - 12:20 pm'
  ]
];

/*
| --------------------------------------------------------------------------------------
| Predefined SMS Templates
| ----------------------------------------------------------------------------------------
*/
$config['smstemplates'] = array("Send student profile for updating.");
/*
| --------------------------------------------------------------------------------------
| SMS Integration
| ----------------------------------------------------------------------------------------
*/

if (ENVIRONMENT !== 'production') {
  //Do not allow SMS config in other environments!  
} else {
  $config['smsintergration']  = 
    array('url' => 'alerts.solutionsinfini.com/api/v4/index.php',
    'api_key' => 'A27187ab48f33e1d9602ef6dfe0c960c0',
    'sender' => 'NPSBDS');
}


/*
| --------------------------------------------------------------------------------------
| SMS Credits per message (unicode/non-unicode)
| ----------------------------------------------------------------------------------------
*/
$config['sms_credit_length']['unicode_single'] = '70';
$config['sms_credit_length']['unicode_multi'] = '60';
$config['sms_credit_length']['non_unicode_single'] = '160';
$config['sms_credit_length']['non_unicode_multi'] = '150';


/*
|-----------------------------
| EMAIL: General Settings
|-----------------------------
|
| 'settings'   => General settings
|               Can be overriden with the template settings
|               or when calling the send() method
|
| Following configs tested working on gmail account
*/
$config['email']['settings'] = array( 
  'from_email'    => '',
  'smtp_user'     => getenv('SMTP_USER'),
  'smtp_pass'     => getenv('SMTP_PASS'),
  'from_name'     => 'National Public School Mysore',
  'smtp_host'     => getenv('SMTP_HOST'),
  'smtp_port'     => getenv('SMTP_PORT'),
  'protocol'      => 'smtp',
  'smtp_crypto'   => 'ssl',
  'mailtype'      => 'html',
  'charset'       => 'utf-8',
  'newline'       => "\r\n",
);

/*
|-----------------------------
| Templates location
|-----------------------------
|
| 'templates' = Folder located @ application/views/{}
|
| Each template created must have a config in templates
*/

$config['email']['templates'] = 'emails';

/*
|-----------------------------
| Dev Email controls
|-----------------------------
|
|
| Stop remove actual users from DB and sent it to specified 
*/

$config['email']['dev_email'] = '<EMAIL>';

/*
|-----------------------------
| Email Templates
|-----------------------------
|
| 'mailtype'    = Mail type, if not set will use general type
| 'charset'     = Charset, if not set will use general charset
| 'from_email'  = From email, if not set will use general email
| 'from_name'   = From name, if not set will use general name
| 'subject'     = Email Subject
| 'send_email'  = If false, it will send the email to site owner instead of actual user
| 'bcc_admin'   = Add the site admin as a BCC to the email
*/

$config['email']['templates'] = array(
  // Demo
  'demo'   => array(
      'subject'    => 'Test Demo',
      'send_email' => true,
      'bcc_admin'  => false,
  ),
  'forgot_password'   => array(
      'subject'    => '',
      'send_email' => true,
      'bcc_admin'  => false,
  ),
);

// Enable Competition Attendance
$config['competition_attendance'] = true;

// Latecomer Attendance if set to true only absents students would be show else all class students would be displayed.
$config['latecomer_attendance'] = true;

// Emergency Exit requires Visitor entry?.
$config['emergency_exit_visitor'] = false;

//Examination Related 
$config['examination']['allow_additional_remarks'] = true;
$config['examination']['additional_remarks_field_name'] = 'Specific Participation';
$config['examination']['remarks_length'] = 250;
$config['examination']['remarks_header'] = 'https://s3.amazonaws.com/nextelement-common/Nps+Mysore/Marks+Card+Template/npsmysore_header_remarks.png';
$config['examination']['marks_rounding_digits'] = 2;

$config['admissions']['forms'] =
    array(
      array(
        'form_name'=>'APPLICATION FOR REGISTRATION MONTESSORI',
        'form_year'=>'2019-2020',
        'school_name'=>'GLOBAL MONTESSORI CENTRE, MYSURU',
        'instructin_file'=>'mont',
        'school_short'=>'nps',
        'school'=>'NPS - Mysuru',
        'class_applied_for'=>array('MONTESSORI'),
        'application_no_gen'=>array(
                                  'name' =>'NPSMYS',
                                  'prefix' => 'MONT',
                                  'year' =>'2019',
                                  'digit_count' => 5
                                  ),
        'documents' => array(
                          'Birth Certificate'=>'Birth Certificate',
                          'Aadhar card'=>'Aadhar card',
                          'Others'=>'Others',
                        ),
        ),

      array(
      'form_name'=>'APPLICATION FOR REGISTRATION KG1',
      'form_year'=>'2019-2020',
      'school_name'=>'NATIONAL PUBLIC SCHOOL, MYSURU',
      'instructin_file'=>'kg',
      'school_short'=>'nps',
      'school'=>'NPS - Mysuru',
      'class_applied_for'=>array('KG1'),
      'application_no_gen'=>array(
                                'name' =>'NPSMYS',
                                'prefix' => 'KG',
                                'year' =>'2019',
                                'digit_count' => 5
                                ),
      'documents' => array(
                        'Birth Certificate'=>'Birth Certificate',
                        'Aadhar card'=>'Aadhar card',
                        'Others'=>'Others',
                      ),
      ),

      array(
      'form_name'=>'APPLICATION FOR REGISTRATION Grade 2 to Grade 9',
      'form_year'=>'2019-2020',
      'school_name'=>'NATIONAL PUBLIC SCHOOL, MYSURU',
      'instructin_file'=>'2-9',
      'school_short'=>'nps',
      'school'=>'NPS - Mysuru',
      'class_applied_for'=>array('2','3','4','5','6','7','8','9'),
      'application_no_gen'=>array(
                                  'name' =>'NPSMYS',
                                  'prefix' => '2-9',
                                  'year' =>'2019',
                                  'digit_count' => 5
                                ),
      'documents' => array(
                        'Birth Certificate'=>'Birth Certificate',
                        'Aadhar card'=>'Aadhar card',
                        'Others'=>'Others',
                      ),

      'prev_eduction_info'=>array(
                        'subject'=>array('Eng','Hindi','Math','Science'),
                        'year'=>array('2016-17','2017-18','2018-19'),
                        'result_types'=>array('Percent','Grade'),
                      ),
      ),

      array(
      'form_name'=>'APPLICATION FOR REGISTRATION Grade 11',
      'form_year'=>'2019-2020',
      'school_name'=>'NATIONAL PUBLIC SCHOOL, MYSURU',
      'instructin_file'=>'11',
      'school_short'=>'nps',
      'school'=>'NPS - Mysuru',
      'class_applied_for'=>array('11'),
      'application_no_gen'=>array(
                                  'name' =>'NPSMYS',
                                  'prefix' => '11',
                                  'year' =>'2019',
                                  'digit_count' => 5
                                ),
      'documents' => array(
                        'Birth Certificate'=>'Birth Certificate',
                        'Aadhar card'=>'Aadhar card',
                        'Others'=>'Others',
                      ),

      'prev_eduction_info'=>array(
                        'subject'=>array('Eng','Hindi','Math','Science'),
                        'year'=>array('2016-17','2017-18','2018-19'),
                        'result_types'=>array('Percent','Grade'),
                      ),
      ),

    );

//Forgot Password 
$config['forgot_password']['email'] = true;
$config['forgot_password']['mobile'] = true;
