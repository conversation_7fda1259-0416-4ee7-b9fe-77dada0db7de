<?php

class Helpdesk_controller extends CI_Controller {

  function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
      }
       $this->load->model('Helpdesk_model', 'helpdesk');
       $this->load->library('filemanager');
  }


  public function index() {
    $data['modules'] = $this->helpdesk->getModules();
    // $data['sub_modules'] = $this->helpdesk->getSubModules();
    $data['main_content'] = 'management/helpdesk';
    $this->load->view('inc/template', $data);
  }

  public function addNewModule() {
    $data = $this->helpdesk->addNewModule();
    echo json_encode($data);
  }
  public function addNewSubModule() {
    $data = $this->helpdesk->addNewSubModule();
    echo json_encode($data);
  }

  public function getSubModules() {
    $data = $this->helpdesk->getSubModules();
    echo json_encode($data);
  }

  public function getContents() {
    $data['contents'] = $this->helpdesk->getContents();
    $data['path_prefix'] = $this->filemanager->getFilePath('');
    echo json_encode($data);
  }

  public function addContent() {
    $data = $this->helpdesk->addContent();
    echo json_encode($data);
  }

  public function submitContent() {
    // echo "<pre>"; print_r($_POST);
    // echo "<pre>"; print_r($_FILES);die();
    $file_path = NULL;
    if($_POST['media_type'] != 'text') {
      $file = $this->s3FileUpload($_FILES['selectFiles']);
      $file_path = $file['file_name'];
      // trigger_error('File Path');
      // trigger_error($file_path);
    }
    echo $this->helpdesk->submitContent($file_path);
  }
  public function deleteContent() {
    $id = $_POST['id'];
    echo $this->helpdesk->deleteContent($id);
  }
  public function editContent() {
    $id = $_POST['id'];
    $data['content'] = $this->helpdesk->editContent($id);
    $data['path_prefix'] = $this->filemanager->getFilePath('');
    echo json_encode($data);
  }
  public function editItems() {
    $file_path = NULL;
    if($_POST['media_type'] != 'text' && $_POST['uploadflag']== 'on' && $_FILES['edit_selectFiles']['size']!=0) {
      $file = $this->s3FileUpload($_FILES['edit_selectFiles']);
      $file_path = $file['file_name'];
      // trigger_error('File Path');
      // trigger_error($file_path);
    }
    if($_POST['uploadflag'] != 'on') {
      $file_path = $_POST['edit_location_old'];
    }
    if($_POST['edit_media_type'] == 'text'){
      $file_path = NULL;
    }
    $data = $this->helpdesk->editItems($file_path);
    echo json_encode($data);
  }

  public function s3FileUpload($file) {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'helpdesk');
  } 
}