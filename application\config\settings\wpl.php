<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  12 March 2018
 *
 * Description:  Contains the configuration details for all school-specific settings.
 *
 * Requirements: PHP5 or above
 *
 */
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| School General Settings
| -------------------------------------------------------------------------
| All the meta data for the school is stored in this section.
*/

/*
| -------------------------------------------------------------------------
| School Meta data
|---------------------------------------------------------------------------
*/
$config['school_name'] = "Women's Peace League";
$config['school_name_line1'] = "Women's Peace League";
$config['school_name_line2'] = 'Bangalore';
$config['school_short_name'] = "wpl";
$config['school_image'] = 'assets/img/wpl.png';
$config['school_logo'] = 'assets/img/wpl.png';
$config['company_logo'] = 'assets/img/next.png';
$config['company_name'] = 'NextElement';
$config['login_background'] = 'assets/img/bg-02.jpg';
$config['favicon'] = 'https://s3.amazonaws.com/nextelement-common/scicon16pxnext.png';

/*
| -------------------------------------------------------------------------
| Admission Number
| Config settings for generating admission number - 
| manual: (TRUE/FALSE) - If false, generate admission number automatically.
| admission_generation_algo - Algorithm to use to generate admission number. Vaues -
| 1) NEXTELEMENT - default
| 2) WPL
| 3) NH
| infix - The infix to use in the algo
| digit-count - ?
| index_offset - The starting number for admissions.
|---------------------------------------------------------------------------
*/
$config['admission_number'] = [ 
                                'manual' => FALSE, 
                                'admission_generation_algo' => 'WPL',
                                'infix' => 'WPL',
                                'digit_count' => 0,
                                'index_offset' => 500
                              ];

/*
| --------------------------------------------------------------------------
| Attendance Module
| Define the Attendance Module of class per the school standards
|
| --------------------------------------------------------------------------
*/

$config['attendance']['absentee_sms'] = 'Your ward %std_name% of %cs_name% is absent today %date%. Kindly ignore the sms if you have already informed the school through email. Principal - WPL';
$config['attendance']['latecomer_sms'] = 'Your ward %std_name% of %cs_name% has come to school late today %date%. Principal - WPL';

/*
| --------------------------------------------------------------------------
| Modules
| Define all the modules enabled for the school. The ones that are not here aren't enabled for the school
|
| Basic modules allowed for all schools by default - 
| SCHOOL_ADMIN - Enable School Admin in sidemenu.
| ACTIVITY - Enable Activity in sidemenu.
| COMMUNCIATION - Enable Communication in sidemenu.
| PERMISSIONS - Manage roles, assign privileges.
| CLASS_MASTER - Add classes.
| STUDENT_MASTER - CRUD Student data.
| STAFF_MASTER - CRUD Staff data.
| SUBJECTS_MASTER - CRUD Subjects, add and assign elective subjects to students.
| STAFF_LOGIN - Staff console, Staff dashboard.
| 
| The following are optional modules and are enabled based on school requirements -
| REGISTER - Enable Register in sidemenu
| STUDENT_ATTENDANCE - Take student attendance.
| LEAVE_MANAGEMENT: Enable Leave management in sidemenu.
| STUDENT_LEAVE - Students can apply leave.
| STAFF_LEAVE - Staff can apply leave.
| STAFF_ATTENDANCE - Take Staff attendance.
| EXAMINATION - Create and publish Exam timetable and portions, Marks card generation.
| TIMETABLE - Add Staff, Subject and Section association, Construct Timetable.
| SUBSTITUTION - Substitute absent staff
| FEES - Create Fee Structure, Pay Fees.
| LIBRARY - CRUD Books, manage Library transacations.
| INVENTORY - CRUD Inventory, manage inventory transacations.
| PARENTS_LOGIN - Parents console, Parents dashboard.
| ONLINE_EXAM - Teachers create online examination. Students can take the exam under Parent's guidance.
| ASSIGNMENT - Teachers can create assignment. Parents can view assignment.
| PUBLICATIONS - Teachers can create publications for school, class, section and students.
| PTM - To handle Parent-Teachers' meeting.
| PARENT_INITIATIVE - Parents can take an initiative in the school.
| STAFF_INITIATIVE - Staff can take an initiative in the school.
| WORKSHOP - Similar to Events.
| COMPETITION - CRUD Competitions.
| EVENTS - CRUD Events.
| VISITOR - Track visitors.
| STUDENT_EMERGENCY_EXIT - Track students leaving the class in between the day.
| SCHOOL_CALENDAR - Enables entering holiday list, events, competitions, examination calendar, etc.
| TRANSPORT - CRUD Transport.
| SMS - Send SMS to School, Class, Section or Student.
| REPORTS - Access to reports side-menu bar.
| 
| --------------------------------------------------------------------------
*/
$config['modules'] = 
array( 'FEES', 'SCHOOL_ADMIN', 'PERMISSIONS', 'STUDENT_MASTER', 'STAFF_MASTER','REPORTS','TIMETABLE','STAFF_LOGIN','COMPETITION','VISITOR','REGISTER','COMMUNICATION','ACTIVITY','TASKSTODO','STAFF_LEAVE','STUDENT_ATTENDANCE','STUDENT_OBSERVATION','STAFF_OBSERVATION','SMS','SUBSTITUTION','FEES');

/**
 * 
 * Modules that should be shown in Parent module
 * STUDENT_LEAVE: Shows Student Leave
 * ATTENDANCE: Shows Student Attendance
 * CIRCULARS: Shows Student Circulars
 * SMS: Shows Student Messages
 * ASSESSMENTS: Shows Student Assessments
 * SCHOOL_CALENDAR: Shows Student School Calendar
 * PARENT_INITIATIVE: Show Parent Initiative
 * FEES: Shows Fees
 * PTM: Parents Teachers' meeting
 */
$config['parent_modules'] = 
array('STUDENT_LEAVE', 'ATTENDANCE', 'CIRCULARS', 'SMS', 'TIMETABLE', 'ASSESSMENTS', 'SCHOOL_CALENDAR', 'PARENT_INITIATIVE', 'FEES');

/*
| -------------------------------------------------------------------------------------
| Timetable
| -------------------------------------------------------------------------------------
| header_class_section_id: Provide the class_section id of the section timetable from which the timetable header should be used.
| -------------------------------------------------------------------------------------
*/
$config['timetable']['header_class_section_id'] = 10;
$config['timetable']['consider_4_alloc_p6_for_cs_lab'] = 0;  //This config is required *ONLY* for NPS. This is a hack to support a 4-section allocation for NPS RNR for Computer Sr Lab.

$config['timetable']['timetable_template_for_staff'] = 1;
$config['timetable']['timetable_template_for_room'] = 1;

/*
| --------------------------------------------------------------------------
| Board
| Define all the boards the school supports in this array.
|
| Allowed values are -
|  1 -> 'State', 2->CBSE, 3 -> 'ICSE', 4-> 'IGCSE'
| --------------------------------------------------------------------------
*/
$config['board'] = array(
  '1' => 'State'
);

/*
| --------------------------------------------------------------------------
| Medium
| Define all the mediums the school supports in this array.
|
| Allowed values are -
| 1 -> English, 2 -> Kannada, 3 -> Hindi
| --------------------------------------------------------------------------
*/
$config['medium'] = array(
  '1' => 'English',
  '2' => 'Kannada'
);

/*
| --------------------------------------------------------------------------
| classType
| Define the type of class per the school standards
|
| Values here will just show up when creating a class
| --------------------------------------------------------------------------
*/
$config['classType'] = array(
  '1' => 'Nursery (LKG, UKG)',
  '2' => 'Primary (1-5)',
  '3' => 'High School (6-10)'
);

/*
| --------------------------------------------------------------------------
| Admission Type
| Define all the admission types the school supports in this array.
|
| Allowed values are -
| 1 -> Re-admission, 2 -> New Admission
| --------------------------------------------------------------------------
*/
$config['admission_type'] = array(
  '1'=>'Re-admission',
  '2'=>'New Admission'
);

/*
| --------------------------------------------------------------------------
| Admission Status
| Define all the admission status the school supports in this array.
|
| Allowed values are -
|1 -> Pending, 2 -> Approved, 3 -> Rejected 4 -> Alumni
| --------------------------------------------------------------------------
*/

$config['admission_status'] = array(
  '1'=>'Pending',
  '2'=>'Approved',
  '3'=>'Rejected',
  '4'=>'Alumni'
);

/*
| --------------------------------------------------------------------------
| Staff Selection Status
| Define all the Staff status the school supports in this array.
|
| Allowed values are -
|1 -> Pending, 2 -> Approved, 3 -> Rejected 4 -> Resigned
| --------------------------------------------------------------------------
*/
$config['staff_status'] = array(
  '1'=>'Pending',
  '2'=>'Approved',
  '3'=>'Rejected',
  '4'=>'Resigned'
);

/*
| --------------------------------------------------------------------------
| RTE Options
| Define all the RTE Options the school supports in this array.
|
| Allowed values are -
| 1-> RTE, 2 -> Non-RTE
| --------------------------------------------------------------------------
*/
$config['rte'] = array(
  '2'=>'Non-RTE',
  '1'=>'RTE'
);

/*
| --------------------------------------------------------------------------
| Boarding
| Define all the boarding types the school supports in this array.
|
| Allowed values are -
| 1 -> Day School, 2 -> Residential
| --------------------------------------------------------------------------
*/
$config['boarding'] = array(
  '1'=>'Day School',
);

/*
| --------------------------------------------------------------------------
| Category
| Define all the student categories the school supports in this array.
| 
| Allowed values are -
| 1 -> 'General',
| 2 -> 'SC/ST 
|
| eg: $config['category'] = array('General', 'SC/ST');
| --------------------------------------------------------------------------
*/
$config['category'] = array(
  '1' => 'General',
  '2' => 'SC/ST'
);

/*
| -------------------------------------------------------------------------
| Fees
| -------------------------------------------------------------------------
| Fee Specific settings
*/

/*
| --------------------------------------------------------------------------
| Filter Criteria
|
| This is used for creating Master Fee Structure.
|
| Different schools have different filter criteria. For eg: NPS RNR uses academic year
| when student joined and whether he is RTE candidate to determine Fee Structure.
| NPS CNP uses class the student is going into and RTE candidature.
| Specify here the filter criteria to be used.
| This should be the same table column name you use in Student.
|
| The currently supported school criteria are - academic_year_of_joining, rte, class,
| medium, category, admission_type, boards, boarding
| --------------------------------------------------------------------------
|   Allowed values are -
|   0 -> 'admission_type',
|   1 -> 'medium
|   2 -> 'rte
|   3 -> 'category
|   4 -> 'academic_year_of_joining
|   5 -> 'class
|   6 -> 'boards
|   7 -> 'boarding
*/
$config['fees']['filter_criteria'] = array('admission_type', 'medium', 'rte', 'category');

/*
| --------------------------------------------------------------------------
| Address Types
| Define all the address types the school likes to collect from student and parents.
|
| Allowed values are -
|(1: Present Address and 2: Permanent Address) or (1: Office Address And 2: Home Address)
| --------------------------------------------------------------------------
*/

$config['address_types'] = array(
  'Student'=> array("0" => "Present Address", "1" => "Permanent Address"),
  'Father'=>  array(),
  'Mother'=>  array()
);

/*
| --------------------------------------------------------------------------
| Staff Profile
| All configuration parameters for Staff Profile edit
|
| Allowed values are -
| enableStaffProfileEdit: 
| 1: Enables Staff to edit their profile in staff App.
| 0: Disables Staff to edit their profile in staff App.
| --------------------------------------------------------------------------
*/

$config['staff_profile']['enableStaffProfileEdit'] = 0;
$config['staff_profile']['enableQualificationEdit'] = 0;

/*
| ------------------------------------------------------------------------------
| Fee Components
|
| This is used to create Fee Components in Master.
|
| Different schools have different fee components. We assume that the components are same across all
| fee Structures. If this is wrong, we have to handle this as a database table rather than config.
|
| Add all the different components in 'Allocation Order' (See Allocation algorithm).
|      
| --------------------------------------------------------------------------------
*/

$config['fees']['components'] = array(
  array ( 'name' => 'New Admission','concession_eligible' => TRUE ),
  array ( 'name' => 'Registration', 'concession_eligible' => TRUE ),
  array ( 'name' => 'Maintenance','concession_eligible' => TRUE ),
  array ( 'name' => 'Miscellaneous','concession_eligible' => TRUE ),
);

/*
| ------------------------------------------------------------------------------
| Fee Type
|
| This is used to create Fee type.
|
|      
| --------------------------------------------------------------------------------
*/

$config['fees']['fee_type'] = array(
  array ('name'=>'Academic fee','value'=>'1', 'required' => TRUE),
  array ('name'=>'Transporation fee','value'=>'2', 'required' => FALSE),
  array ('name'=>'Facilities fee','value'=>'3', 'required' => FALSE),
  array ('name'=>'Miscellaneous fee','value'=>'4', 'required' => FALSE),
);


/*
| ----------------------------------------------------------------------------------
| Fee Payment modes
| 
| This is used in Fee payment.
| Add all the payment modes supported by this school
| Allowed values - 

            $config['allowed_payment_modes'] = array(
                              '1'=>'dd',
                              '2'=>'credit card',
                              '3'=>'debit card',
                              '4'=>'cheque'
                              '5'=>'wallet payment'
                              '6'=>'challan'
                              '7'=>'Card (POS)'
                              '8'=> Net Banking (POS)
                              '9'=>'cash',
                            );

| ------------------------------------------------------------------------------------
*/
$config['fees']['allowed_payment_modes'] = array(
  array ('name'=>'challan','value'=>'6', 'reconcilation_reqd' => TRUE),
  array ('name'=>'dd','value'=>'1', 'reconcilation_reqd' => FALSE),
  array ('name'=>'cheque','value'=>'4', 'reconcilation_reqd' => TRUE)
);

/*
| ----------------------------------------------------------------------------------
| Fee Receipt template
| 
| This is used to generate the fee receipt.
| Add the template file that should be used (probably customized) for this school.
| ------------------------------------------------------------------------------------
*/
$config['fees']['receipt_template'] = 'wpl';

/*
| ----------------------------------------------------------------------------------
| Fee Component Allocation Type
| 
| This is used in Fee transaction.
|
| If TRUE, the allocation algorithm will kick in to allocate the total amount entered across Fee Components.
| If FALSE, the allocation has to be manually keyed in by the user (cashier).
|
| ------------------------------------------------------------------------------------
*/
$config['fees']['component_allocation_type_auto'] = FALSE;

/*
| ----------------------------------------------------------------------------------
| Fee Concession Component Allocation Type
| 
| This is used in Fee transaction. (How does the fee components get allocated when
| the total concession is entered).
|
| If TRUE, the allocation algorithm will kick in to allocate the total amount entered
| across Fee Components.
| If FALSE, the allocation has to be manually keyed in by the user (cashier).
|
| ------------------------------------------------------------------------------------
*/
$config['fees']['cComponent_allocation_type_auto'] = FALSE;

/*
| --------------------------------------------------------------------------------------
| Fee Component Allocation algorithm
| 
| This is used in conjunction with Fee transaction.
|
| If Allocation algorithm is AUTO, then the specified algorithm is used to allocate total amount across Fee Components.
|
| Algo 1: 'ALLOCATE_FROM_LEFT_TO_RIGHT' - This algorithm allocates the total amount from left to right as specified in the fee component array above.
| It will allocate the full amount for the component before proceeding to the next.
|
| ----------------------------------------------------------------------------------------
*/
$config['fees']['component_allocation_algorithm_if_auto'] = 'ALLOCATE_FROM_LEFT_TO_RIGHT';

/*
| --------------------------------------------------------------------------------------
| Fee receipt generation
| ----------------------------------------------------------------------------------------
*/

$config['fees']['recipt_number_gen'] = [ 
                      'fee_generation_algo' => 'WPL',
                      'infix' => 'WPL',
                      'digit_count' => 6,
                      ];


/*
| --------------------------------------------------------------------------------------
| Discount for fully paid fee amount for north hill school only  
| ----------------------------------------------------------------------------------------
*/
$config['fees']['discount'] =[
                  'percent'=> 0,
                  'isProvided' => FALSE,
                  'isManual' => FALSE
                 ];

$config['fees']['fee_installment'] = array(
  array ('name'=>'1st Installment','installment_per'=>100,'due_date'=>'2018-06-01'),
);

/*
| ------------------------------------------------------------------------------
| Fee Filters supported by school
|
| 
|      
| --------------------------------------------------------------------------------
*/

$config['fees']['supportedFilters'] = [
  'class', 'from_to_date',  'medium', 'admission_type', 'payment_options', 'donors', 'isRTE'
];

$config['fees']['supportedColumns'] = [
  'slNo', 'date', 'receiptNo', 'stdName', 'className', 'paidAmount', 'concession', 'remarks', 'pType', 'feeAmount','balance'
];

/*
| --------------------------------------------------------------------------------------
| SMS Integration
| ----------------------------------------------------------------------------------------
*/
if (ENVIRONMENT !== 'production') {
    $config['smsintergration']  = 
    array('url' => 'alerts.valueleaf.com/api/v4/index.php',
      'api_key' => 'Ae6b5684768b2741508f447a71545290a',
      'sender' => 'WOMENS');
} else {
    $config['smsintergration']  = 
    array('url' => 'alerts.valueleaf.com/api/v4/index.php',
      'api_key' => 'Ae6b5684768b2741508f447a71545290a',
      'sender' => 'WOMENS');
}

/*
|-----------------------------
| EMAIL: General Settings
|-----------------------------
|
| 'settings'   => General settings
|               Can be overriden with the template settings
|               or when calling the send() method
|
| Following configs tested working on gmail account
*/
$config['email']['settings'] = array( 
  'from_email'    => '',
  'smtp_user'     => getenv('SMTP_USER'),
  'smtp_pass'     => getenv('SMTP_PASS'),
  'from_name'     => "Women's Peace League",
  'smtp_host'     => getenv('SMTP_HOST'),
  'smtp_port'     => getenv('SMTP_PORT'),
  'protocol'      => 'smtp',
  'smtp_crypto'   => 'ssl',
  'mailtype'      => 'html',
  'charset'       => 'utf-8',
  'newline'       => "\r\n",
);

/*
|-----------------------------
| Templates location
|-----------------------------
|
| 'templates' = Folder located @ application/views/{}
|
| Each template created must have a config in templates
*/

$config['email']['templates'] = 'emails';

/*
|-----------------------------
| Dev Email controls
|-----------------------------
|
|
| Stop remove actual users from DB and sent it to specified 
*/

$config['email']['dev_email'] = '<EMAIL>';

/*
|-----------------------------
| Email Templates
|-----------------------------
|
| 'mailtype'    = Mail type, if not set will use general type
| 'charset'     = Charset, if not set will use general charset
| 'from_email'  = From email, if not set will use general email
| 'from_name'   = From name, if not set will use general name
| 'subject'     = Email Subject
| 'send_email'  = If false, it will send the email to site owner instead of actual user
| 'bcc_admin'   = Add the site admin as a BCC to the email
*/

$config['email']['templates'] = array(
  // Demo
  'demo'   => array(
      'subject'    => 'Test Demo',
      'send_email' => true,
      'bcc_admin'  => false,
  ),
  'forgot_password'   => array(
      'subject'    => '',
      'send_email' => true,
      'bcc_admin'  => false,
  ),
);

// Enable Competition Attendance
$config['competition_attendance'] = false;

// Latecomer Attendance if set to true only absents students would be show else all class students would be displayed.
$config['latecomer_attendance'] = true;

// Emergency Exit requires Visitor entry?.
$config['emergency_exit_visitor'] = false;

//Forgot Password 
$config['forgot_password']['email'] = true;
$config['forgot_password']['mobile'] = true;

/*
| --------------------------------------------------------------------------------------
| SMS Credits per message (unicode/non-unicode)
| ----------------------------------------------------------------------------------------
*/
$config['sms_credit_length']['unicode_single'] = '70';
$config['sms_credit_length']['unicode_multi'] = '60';
$config['sms_credit_length']['non_unicode_single'] = '160';
$config['sms_credit_length']['non_unicode_multi'] = '150';
