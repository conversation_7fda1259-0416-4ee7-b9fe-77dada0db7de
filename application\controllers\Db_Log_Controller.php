<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * CodeIgniter Log Library
 *
 * @category   Exceptions
 * @package    CodeIgniter
 * @subpackage Controller
 * <AUTHOR> <<EMAIL>>
 * @license    BSD License
 * @link       https://github.com/appleboy/CodeIgniter-Log-Library
 */
class Db_Log_Controller extends CI_Controller
{
    /**
     * constructor
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }

        $this->load->library('lib_log');
        $this->load->model('Db_Log_Model','logModel');
    }


    /**
     * index method
     *
     * @return void
     */
    public function index()
    {
        $data['logData'] = $this->logModel->getAll();
        $data['main_content']    = 'log_viewer/index';
        $this->load->view('inc/template', $data);  
    }

    /**
     * index method
     *
     * @return void
     */
    public function exampleindex()
    {
        echo $a;
        trigger_error("User error via trigger.", E_USER_ERROR);
        trigger_error("Warning error via trigger.", E_USER_WARNING);
        trigger_error("Notice error via trigger.", E_USER_NOTICE);
    }
}
/* End of file example.php */
/* Location: ./application/controllers/example.php */
