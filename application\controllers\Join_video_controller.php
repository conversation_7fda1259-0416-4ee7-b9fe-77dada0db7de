<?php

class Join_video_controller extends CI_Controller {

	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
			redirect('dashboard', 'refresh');
		}
		$this->load->model('Video_model', 'video_model');
	}

	// profile
	public function index() {
		$data['rooms_list'] = $this->video_model->get_room_list();
        $data['main_content'] = 'join_video/index.php';
        $this->load->view('inc/template', $data);
	}
	
	public function get_room_by_id () {
		$result = $this->video_model->get_room_by_id($_POST['room_id']);
		// echo '<pre>';print_r($result);die();
		echo $result->local_desc;
	}

	public function register_to_room() {
		$result = $this->video_model->register_to_room($_POST['room_id'], $_POST['localDescription']);
	}
}
