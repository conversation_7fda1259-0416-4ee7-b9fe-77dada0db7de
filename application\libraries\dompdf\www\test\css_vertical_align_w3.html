<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.4.4 vertical-align</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<STYLE type="text/css">
p {font-size: 12pt; border: 1px dotted orange;}
.one {vertical-align: sub;}
.two {vertical-align: super;}
.three {vertical-align: top; font-size: 12pt;}
.four {vertical-align: text-top; font-size: 12pt;}
.five {vertical-align: middle; font-size: 12pt;}
.six {vertical-align: bottom; font-size: 12pt;}
.seven {vertical-align: text-bottom; font-size: 12pt;}
.eight {vertical-align: baseline; font-size: 12pt;}
.nine {vertical-align: 50%; font-size: 12px; line-height: 16px;}

P.example {font-size: 14pt;}
BIG {font-size: 16pt;}
SMALL {font-size: 12pt;}
.ttopalign {vertical-align: text-top;}
.topalign {vertical-align: top;}
.midalign {vertical-align: middle;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P {font-size: 12pt;}
.one {vertical-align: sub;}
.two {vertical-align: super;}
.three {vertical-align: top; font-size: 12pt;}
.four {vertical-align: text-top; font-size: 12pt;}
.five {vertical-align: middle; font-size: 12pt;}
.six {vertical-align: bottom; font-size: 12pt;}
.seven {vertical-align: text-bottom; font-size: 12pt;}
.eight {vertical-align: baseline; font-size: 12pt;}
.nine {vertical-align: 50%; font-size: 12px; line-height: 16px;}

P.example {font-size: 14pt;}
BIG {font-size: 16pt;}
SMALL {font-size: 12pt;}
.ttopalign {vertical-align: text-top;}
.topalign {vertical-align: top;}
.midalign {vertical-align: middle;}

</PRE>
<HR>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50"><SPAN class="one">The first four words</SPAN> in this sentence should be subscript-aligned.  The font size of the superscripted text should not be different from that of the parent element.
</P>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50"><SPAN class="two">The first four words</SPAN> in this sentence should be superscript-aligned.  The font size of the subscripted text should not be different from that of the parent element.
</P>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50"><SPAN class="three">The first four words</SPAN> in this sentence should be top-aligned, which will align their tops with the top of the tallest element in the line (probably the orange rectangle).
</P>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50">
<SPAN style="font-size: 200%;">
<SPAN class="four">The first four words</SPAN> in this sentence should be text-top-aligned, which should align their tops with the top of the tallest text in the line.
</SPAN>
</P>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="five">
The image at the beginning of this sentence should be middle-aligned, which should align its middle with the point defined as the text baseline plus half the x-height.
</P>
<P>
<IMG SRC="images/vblank.gif" align="top" alt="[Image]" height="50">
<SPAN style="font-size: 200%;">
<SPAN class="six">The first four words</SPAN> in this sentence should be 12pt in size and bottom-aligned, which should align their bottom with the bottom of the lowest element in the line.
</SPAN>
</P>
<P>
<IMG SRC="images/vblank.gif" align="top" alt="[Image]" height="50">
<SPAN style="font-size: 200%;">
<SPAN class="seven">The first eight words ("eight" has a descender)</SPAN> in this sentence should be 12pt in size and text-bottom-aligned, which should align their bottom with the bottom of the lowest text (including descenders) in the line.
</SPAN>
</P>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50">
<SPAN style="font-size: 200%;">
<SPAN class="eight">The first four words</SPAN> in this sentence should be 12pt in size and baseline-aligned, which should align their baseline with the baseline of the rest of the text in the line.
</SPAN>
</P>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50"><SPAN class="nine">The first four words</SPAN> in this sentence should have a font-size of 12px and a line-height of 16px; they are also 50%-aligned, which should raise them 8px relative to the natural baseline.
</P>
<P>
In the following paragraph, all images should be aligned with the top of the 14-point text, which is identical to the first section of text, whereas any size text should be aligned with the text baseline (which is the default value).
</P>
<P class="example">
This paragraph
<IMG SRC="images/vblank.gif" alt="[Image]" height="30" class="ttopalign">
<SPAN style="font-size: 250%;">contains many images</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="ttopalign">
<BIG>of varying heights</BIG>
<IMG SRC="images/vblank.gif" alt="[Image]" height="10" class="ttopalign">
<SMALL>and widths</SMALL>
<IMG SRC="images/vblank.gif" alt="[Image]" height="20" class="ttopalign">
all of which
<IMG SRC="images/vblank.gif" alt="[Image]" height="65" class="ttopalign">
<SPAN style="font-size: 2em;">should be aligned</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="35" class="ttopalign">
with the top of
<IMG SRC="images/vblank.gif" alt="[Image]" height="30" class="ttopalign">
<SPAN style="font-size: 150%;">a <SPAN style="font-size: 250%;">14-point</SPAN> text element</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="ttopalign">
<SMALL>regardless of the line in which</SMALL>
<IMG SRC="images/vblank.gif" alt="[Image]" height="15" class="ttopalign">
<BIG>the images appear.</BIG>
<IMG SRC="images/vblank.gif" alt="[Image]" height="90" class="ttopalign">
</P>
<P class="explain">
In the following paragraph, all images should be aligned with the middle of the default text, whereas any text should be aligned with the text baseline (which is the default value).
</P>
<P>
This paragraph
<IMG SRC="images/vblank.gif" alt="[Image]" height="30" class="midalign">
<SPAN style="font-size: 250%;">contains many images</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="midalign">
of varying heights
<IMG SRC="images/vblank.gif" alt="[Image]" height="10" class="midalign">
<SMALL>and widths</SMALL>
<IMG SRC="images/vblank.gif" alt="[Image]" height="20" class="midalign">
all of which
<IMG SRC="images/vblank.gif" alt="[Image]" height="65" class="midalign">
should be aligned
<IMG SRC="images/vblank.gif" alt="[Image]" height="35" class="midalign">
<SPAN style="font-size: 2em;">with the middle of</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="midalign">
<SPAN style="font-size: 150%;">a <SPAN style="font-size: 250%;">14-point</SPAN> text element</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="midalign">
<SMALL>regardless of the line in which</SMALL>
<IMG SRC="images/vblank.gif" alt="[Image]" height="15" class="midalign">
<BIG>the images appear.</BIG>
<IMG SRC="images/vblank.gif" alt="[Image]" height="90" class="midalign">
</P>
<P class="explain">
In the following paragraph, all elements should be aligned with the top of the tallest element on the line, whether that element is an image or not.  Each fragment of text has been SPANned appropriately in order to cause this to happen.
</P>
<P>
<SPAN class="topalign">This paragraph</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="10" class="topalign">
<SPAN style="font-size: 250%;" class="topalign">contains many images</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="10" class="topalign">
<SPAN class="topalign">and some text</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="10" class="topalign">
<SPAN class="topalign">of varying heights</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="10" class="topalign">
<BIG class="topalign">and widths</BIG>
<IMG SRC="images/vblank.gif" alt="[Image]" height="20" class="topalign">
<SPAN class="topalign">all of which</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="65" class="topalign">
<SPAN class="topalign">should be aligned</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="35" class="topalign">
<SPAN style="font-size: 2em;" class="topalign">with the top of</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="topalign">
<SPAN class="topalign">the tallest element in</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="15" class="topalign">
<BIG class="topalign">whichever line the elements appear.</BIG>
<IMG SRC="images/vblank.gif" alt="[Image]" height="90" class="topalign">
</P>


<STRONG>TABLE Testing Section</STRONG>

<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50"><SPAN class="one">The first four words</SPAN> in this sentence should be subscript-aligned.  The font size of the superscripted text should not be different from that of the parent element.
</P>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50"><SPAN class="two">The first four words</SPAN> in this sentence should be superscript-aligned.  The font size of the subscripted text should not be different from that of the parent element.
</P>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50"><SPAN class="three">The first four words</SPAN> in this sentence should be top-aligned, which will align their tops with the top of the tallest element in the line (probably the orange rectangle).
</P>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50">
<SPAN style="font-size: 200%;">
<SPAN class="four">The first four words</SPAN> in this sentence should be text-top-aligned, which should align their tops with the top of the tallest text in the line.
</SPAN>
</P>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="five">
The image at the beginning of this sentence should be middle-aligned, which should align its middle with the point defined as the text baseline plus half the x-height.
</P>
<P>
<IMG SRC="images/vblank.gif" align="top" alt="[Image]" height="50">
<SPAN style="font-size: 200%;">
<SPAN class="six">The first four words</SPAN> in this sentence should be 12pt in size and bottom-aligned, which should align their bottom with the bottom of the lowest element in the line.
</SPAN>
</P>
<P>
<IMG SRC="images/vblank.gif" align="top" alt="[Image]" height="50">
<SPAN style="font-size: 200%;">
<SPAN class="seven">The first eight words ("eight" has a descender)</SPAN> in this sentence should be 12pt in size and text-bottom-aligned, which should align their bottom with the bottom of the lowest text (including descenders) in the line.
</SPAN>
</P>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50">
<SPAN style="font-size: 200%;">
<SPAN class="eight">The first four words</SPAN> in this sentence should be 12pt in size and baseline-aligned, which should align their baseline with the baseline of the rest of the text in the line.
</SPAN>
</P>
<P>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50"><SPAN class="nine">The first four words</SPAN> in this sentence should have a font-size of 12px and a line-height of 16px; they are also 50%-aligned, which should raise them 8px relative to the natural baseline.
</P>
<P>
In the following paragraph, all images should be aligned with the top of the 14-point text, which is identical to the first section of text, whereas any size text should be aligned with the text baseline (which is the default value).
</P>
<P class="example">
This paragraph
<IMG SRC="images/vblank.gif" alt="[Image]" height="30" class="ttopalign">
<SPAN style="font-size: 250%;">contains many images</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="ttopalign">
<BIG>of varying heights</BIG>
<IMG SRC="images/vblank.gif" alt="[Image]" height="10" class="ttopalign">
<SMALL>and widths</SMALL>
<IMG SRC="images/vblank.gif" alt="[Image]" height="20" class="ttopalign">
all of which
<IMG SRC="images/vblank.gif" alt="[Image]" height="65" class="ttopalign">
<SPAN style="font-size: 2em;">should be aligned</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="35" class="ttopalign">
with the top of
<IMG SRC="images/vblank.gif" alt="[Image]" height="30" class="ttopalign">
<SPAN style="font-size: 150%;">a <SPAN style="font-size: 250%;">14-point</SPAN> text element</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="ttopalign">
<SMALL>regardless of the line in which</SMALL>
<IMG SRC="images/vblank.gif" alt="[Image]" height="15" class="ttopalign">
<BIG>the images appear.</BIG>
<IMG SRC="images/vblank.gif" alt="[Image]" height="90" class="ttopalign">
</P>
<P class="explain">
In the following paragraph, all images should be aligned with the middle of the default text, whereas any text should be aligned with the text baseline (which is the default value).
</P>
<P>
This paragraph
<IMG SRC="images/vblank.gif" alt="[Image]" height="30" class="midalign">
<SPAN style="font-size: 250%;">contains many images</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="midalign">
of varying heights
<IMG SRC="images/vblank.gif" alt="[Image]" height="10" class="midalign">
<SMALL>and widths</SMALL>
<IMG SRC="images/vblank.gif" alt="[Image]" height="20" class="midalign">
all of which
<IMG SRC="images/vblank.gif" alt="[Image]" height="65" class="midalign">
should be aligned
<IMG SRC="images/vblank.gif" alt="[Image]" height="35" class="midalign">
<SPAN style="font-size: 2em;">with the middle of</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="midalign">
<SPAN style="font-size: 150%;">a <SPAN style="font-size: 250%;">14-point</SPAN> text element</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="midalign">
<SMALL>regardless of the line in which</SMALL>
<IMG SRC="images/vblank.gif" alt="[Image]" height="15" class="midalign">
<BIG>the images appear.</BIG>
<IMG SRC="images/vblank.gif" alt="[Image]" height="90" class="midalign">
</P>
<P class="explain">
In the following paragraph, all elements should be aligned with the top of the tallest element on the line, whether that element is an image or not.  Each fragment of text has been SPANned appropriately in order to cause this to happen.
</P>
<P>
<SPAN class="topalign">This paragraph</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="10" class="topalign">
<SPAN style="font-size: 250%;" class="topalign">contains many images</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="10" class="topalign">
<SPAN class="topalign">and some text</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="10" class="topalign">
<SPAN class="topalign">of varying heights</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="10" class="topalign">
<BIG class="topalign">and widths</BIG>
<IMG SRC="images/vblank.gif" alt="[Image]" height="20" class="topalign">
<SPAN class="topalign">all of which</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="65" class="topalign">
<SPAN class="topalign">should be aligned</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="35" class="topalign">
<SPAN style="font-size: 2em;" class="topalign">with the top of</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="50" class="topalign">
<SPAN class="topalign">the tallest element in</SPAN>
<IMG SRC="images/vblank.gif" alt="[Image]" height="15" class="topalign">
<BIG class="topalign">whichever line the elements appear.</BIG>
<IMG SRC="images/vblank.gif" alt="[Image]" height="90" class="topalign">
</P>
</BODY>
</HTML>
