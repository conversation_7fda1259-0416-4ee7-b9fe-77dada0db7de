<?php

class Staffleave_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('LEAVE') && !$this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPLY')){
            redirect('dashboard', 'refresh');
        }          
        $this->load->model('Holiday_info_model');
        $this->load->model('staff/staffleave_model');
        $this->load->model('avatar');
    }

    //apply leaves landing page
    public function leaveInfo() {
        $AvatarId = $this->authorization->getAvatarId();
        $applyLeave = $this->authorization->isAuthorized('LEAVE.APPLY_LEAVE_FOR_OTHER_STAFF');
        $avatar = $this->avatar->getAvatarById($AvatarId);
        $staffId = $avatar->stakeholderId;
        if($applyLeave) {
            $data['leave_details'] = $this->staffleave_model->get_AllLeaves();
        }
        else 
            $data['leave_details'] = $this->staffleave_model->get_leave_details($staffId);
        foreach ($data['leave_details'] as $key => $leave) {
            if ($leave->status == '0') {
                $data['leave_details'][$key]->status_string = "Pending";
            } elseif ($leave->status == '1') {
               $data['leave_details'][$key]->status_string = "Approved";
            } elseif ($leave->status == '2') {
                $data['leave_details'][$key]->status_string = "Auto Approved";
            } elseif ($leave->status == '3') {
                $data['leave_details'][$key]->status_string = "Rejected";
            }
        }
        $data['approved'] = $this->staffleave_model->getCountOfLeaves($staffId, 0);
        $data['applied'] = $this->staffleave_model->getCountOfLeaves($staffId, 1);
        $data['applyLeave'] = $applyLeave;
        $data['staff'] = $staffId;
        $data['staff_list'] = $this->staffleave_model->list_staffbyuserId();
        $this->load->library('filemanager');
        //echo '<pre>'; print_r($data); die();
        $data['main_content'] = 'staff/staff_leave_application/index';
        $this->load->view('inc/template', $data);
    }

    //add leaves form page
    public function add_leave() {
        $AvatarId = $this->authorization->getAvatarId();
        $avatar = $this->avatar->getAvatarById($AvatarId);
        $staffId = $avatar->stakeholderId;
        //$staff = $this->staffcache->getStaffCache();
        $data['staff_list'] = $this->staffleave_model->list_staffbyuserId();
        $applyLeave = $this->authorization->isAuthorized('LEAVE.APPLY_LEAVE_FOR_OTHER_STAFF');
        $data['staffDetails'] = $this->staffleave_model->getStaffDetails();
        $data['applyLeave'] = $applyLeave;
        $data['staff'] = $staffId;
        //print_r($data['staff_list']); die();
        $data['main_content'] = 'staff/staff_leave_application/add_leave';
        $this->load->view('inc/template', $data);
    }

    //get holiday count to deduct from number of leaves
    public function getHolidayCount() {
        $start_date = $_POST['startDate'];
        $end_date = $_POST['endDate'];
        $session = $_POST['session'];

        $start_date_formatted = strtotime($start_date);
        $end_date_formatted = strtotime($end_date);
        $datediff = $end_date_formatted - $start_date_formatted;
        $leaveDays = ($datediff / (60 * 60 * 24)) + 1;
 
        $holidays = 0;
        for($i = 0; $i < $leaveDays; $i++){
            $date = date("Y-m-d", strtotime($start_date." +".$i." days"));
            $val = $this->staffleave_model->getHoliday($date);
            if($val > 0){
                $holidays++;
            }
            if($val == 0 && (date('w', strtotime($date)) == 0 || ((date('w', strtotime($date)) == 6) && ($session == 'noon')))){
                $holidays++;
            }
        }
        echo $leaveDays - $holidays;
    }

    //submit the leave application - by applicant
    public function submit_leave() {
        $status =(int) $this->staffleave_model->checkAlreadyTaken(null);
        if($status == 0){
            $this->session->set_flashdata('flashError', 'Leave already taken on this date.');
            redirect('staff/staffleave_controller/leaveInfo');
        }
        
        $insert = $this->staffleave_model->insert_leave_application();
        if ($insert) {
            $staffId = $this->input->post('staff_id');
            $staff = $this->staffleave_model->getStaffData($staffId);
            $staffName = $staff->staffName;
            $from_date = $this->input->post('from_date');
            $to_date = $this->input->post('to_date');
            $title = 'Leave application';
            $message = $staffName . ' applied leave - ';
            if($from_date == $to_date) {
                $message .= 'On '.$from_date;
            } else {
                $message .= 'From '.$from_date.' to '.$to_date;
            }
            $url = site_url('staff/staffleave_controller/getAggregateReport');
            $this->load->helper('notification_helper');
            sendNotificationsToCommity($title, $message, $url);

            $author = $this->input->post('author');
            if($author == 1) {
                $staff_id = $this->input->post('staff_id');
                $message = 'Leave applied and approved.';
                if($from_date == $to_date) {
                    $message .= 'On '.$from_date;
                } else {
                    $message .= 'From '.$from_date.' to '.$to_date;
                }
                $stfIds = [$staff_id];
                $url = site_url('staff/staffleave_controller/leaveInfo');
                $this->load->helper('notification_helper');
                sendStaffNotifications($stfIds, $title, $message, $url);
            }
            $this->session->set_flashdata('flashSuccess', 'Successful Inserted.');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong.');
        }
        redirect('staff/staffleave_controller/leaveInfo');
    }

    //edit applied leave - by applicant
    public function edit_leave($id) {
        $AvatarId = $this->authorization->getAvatarId();
        $avatar = $this->avatar->getAvatarById($AvatarId);
        $staffId = $avatar->stakeholderId;
        $applyLeave = $this->authorization->isAuthorized('STAFF_LEAVE.APPLY_LEAVE_FOR_OTHERS');
        $data['applyLeave'] = $applyLeave;
        $data['staff'] = $staffId;
        $data['edit_leave'] = $this->staffleave_model->edit_leaveappicationbyId($id);

        //   echo "<pre>";print_r($data['edit_leave']);die();
        $data['staff_list'] = $this->staffleave_model->list_staffbyuserId();
        $data['main_content'] = 'staff/staff_leave_application/edit_leave';
        $this->load->view('inc/template', $data);
    }

    //submit the edited leave application
    public function update_leave($id) {
        $status =(int) $this->staffleave_model->checkAlreadyTaken($id);
        if($status == 0){
            $this->session->set_flashdata('flashError', 'Leave already taken on this date.');
            redirect('staff/staffleave_controller/leaveInfo');
        }
        $update = $this->staffleave_model->update_leave($id);
        if ($update) {
            $this->session->set_flashdata('flashSuccess', 'Successful updated.');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong.');
        }
        redirect('staff/staffleave_controller/leaveInfo');
    }

    //delete the leave application
    public function delete_leave($id) {
        $delete = $this->staffleave_model->delete_leave_application($id);
        if ($delete) {
            $this->session->set_flashdata('flashSuccess', 'Successful Deleted.');
            redirect('staff/staffleave_controller/leaveInfo');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong.');
            redirect('staff/staffleave_controller/leaveInfo');
        }
    }

    // leave approval landing page
    public function leave_approval() {
        $data['leave_details'] = $this->staffleave_model->getStaffLeaveApplications();
        $data['main_content'] = 'leave_approval/index';
        $this->load->view('inc/template', $data);
    }

    //approve or reject leave application page
    public function edit_approveleave($id) {
        $AvatarId = $this->authorization->getAvatarId();
        $avatar = $this->avatar->getAvatarById($AvatarId);
        $staffId = $avatar->stakeholderId;
        $data['staff'] = $staffId;
        $data['edit_leave'] = $this->staffleave_model->edit_leaveappicationbyId($id);
        $data['main_content'] = 'leave_approval/edit_leaveapproval';
        $this->load->view('inc/template', $data);
    }

    //update the approve or reject status
    public function update_leaveapproval_application($id) {
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $update = $this->staffleave_model->update_leaveapproval_applications($id);
        if ($update) {
            $leave_status = $this->input->post('status');
            $description = $this->input->post('description');
            $school_name = $this->settings->getSetting('school_name');
            $message = 'Leave rejected. ';
            if($leave_status == 1) {
                $message = 'Leave approved. ';
            }
            $message .= $description;
            $stfIds = [$staffId];
            $url = site_url('staff/staffleave_controller/leaveInfo');
            $this->load->helper('notification_helper');
            sendStaffNotifications($stfIds, $school_name, $message, $url);
            $this->session->set_flashdata('flashSuccess', 'Successful updated.');
            redirect('staff/staffleave_controller/leave_approval');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong.');
            redirect('staff/staffleave_controller/leave_approval');
        }
    }

    public function get_Leaves(){
        $AvatarId = $this->authorization->getAvatarId();
        $avatar = $this->avatar->getAvatarById($AvatarId);
        $staffId = $avatar->stakeholderId;
        $result = $this->staffleave_model->get_Leaves($staffId);
        $mobile = $_POST['mobile'];

        $data = "";
        if(empty($result)) $data = '<h4>No Leaves Taken Yet</h4>';
        else {
            if($mobile) {
                $i = 1;
                foreach ($result as $key => $val) {

                    
                    $data .= '<p><strong>Staff Name : </strong>'. $val->first_name .'</p>';
                    $data .= '<p><strong>From to To Date : </strong>'. date('d-M-y', strtotime($val->from_date)) .' to '.date('d-M-y', strtotime($val->to_date)) .'</p>';
                    $data .= '<p><strong>No of Days : </strong>'. $val->noofdays .'</p>';
                    $data .= '<p><strong>Leave Type : </strong>'. $val->leave_type .'</p>';
                    $data .= '<p><strong>Reason : </strong>'. $val->reason .'</p>';
                    $status = "Pending";
                    if ($val->status == '1') {
                        $status = "Approved";
                    } elseif ($val->status == '2') {
                        $status = "Auto Approved";
                    } elseif ($val->status == '3') {
                        $status = "Approved";
                    }
                    //$data .= '<p><strong>Status : </strong>'. $status .'</p>';
                    $data .= '<td></td>';
                    if ($val->sub == 1) {
                        // $data .= '<p><strong>Status : </strong>'. $status .'<a disabled href='.site_url("staff/staffleave_controller/delete_leave/" . $val->id). ' class="btn btn-warning  pull-right" data-placement="top"  data-toggle="tooltip" data-original-title="Delete Leave"><i class="fa fa-trash-o"></i></a><a disabled href='.site_url("staff/staffleave_controller/edit_leave/" . $val->id). ' class="btn btn-warning  pull-right" data-placement="top"  data-toggle="tooltip" data-original-title="Edit Leave"><i class="fa fa-edit"></i></a></p>';
                    } else {
                        if ($val->status == '0') {
                            $disabled = "";
                            if($staffId != $val->staff_id) $disabled = " disabled";
                            $data .= '<p><strong>Status : </strong>'. $status .'<a'. $disabled.' href='.site_url("staff/staffleave_controller/delete_leave/" . $val->id). ' class="btn btn-warning  pull-right" data-placement="top"  data-toggle="tooltip" data-original-title="Delete Leave"><i class="fa fa-trash-o"></i></a><a'. $disabled .' href='.site_url("staff/staffleave_controller/edit_leave/" . $val->id). ' class="btn btn-warning  pull-right" data-placement="top"  data-toggle="tooltip" data-original-title="Edit Leave"><i class="fa fa-edit"></i></a></p>';
                        } else if($val->status == '2') {
                            $disabled = "";
                            if($staffId != $val->leave_filed_by) $disabled = " disabled";
                            // $data .= '<p><strong>Status : </strong>'. $status .'<a href='.site_url("staff/staffleave_controller/delete_leave/" . $val->id). ' class="btn btn-warning  pull-right" data-placement="top"  data-toggle="tooltip" data-original-title="Delete Leave"><i class="fa fa-trash-o"></i></a><a href='.site_url("staff/staffleave_controller/edit_leave/" . $val->id). ' class="btn btn-warning  pull-right" data-placement="top"  data-toggle="tooltip" data-original-title="Edit Leave"><i class="fa fa-edit"></i></a></p>';
                        } else {
                            // $data .= '<p><strong>Description : </strong>'.$val->description.'<a disabled href='.site_url("staff/staffleave_controller/delete_leave/" . $val->id). ' class="btn btn-warning   pull-right" data-placement="top"  data-toggle="tooltip" data-original-title="Delete Leave"><i class="fa fa-trash-o"></i></a><a disabled href='.site_url("staff/staffleave_controller/edit_leave/" . $val->id). ' class="btn btn-warning  pull-right" data-placement="top"  data-toggle="tooltip" data-original-title="Edit Leave"><i class="fa fa-edit"></i></a></p>';
                        }
                    }
                    
                    $data .= '<div class="col-xs-12 line" style="border:2px solid #ccc;margin:5px 0px;"></div>';
                }
            } else {
                $data .= '<table class="table table-bordered datatable dataTable no-footer">';
                $data .='<thead><tr><th width="4%">#</th><th width="10%">Staff Name</th><th width="9%">From Date</th><th width="9%">To Date</th><th width="8%">No of Days</th><th width="11%">Leave Type</th><th width="28%">Reason</th><th width="11%">Status</th><th width="10%">Action</th></tr></thead><tbody>';
                $i = 1;
                foreach ($result as $key => $val) {

                    $data .= '<tr><td>'. $i++ .'</td>';
                    $data .= '<td>'. $val->first_name .'</td>';
                    $data .= '<td>'. date('d-M-Y', strtotime($val->from_date)) .'</td>';
                    $data .= '<td>'. date("d-M-Y", strtotime($val->to_date)) .'</td>';
                    $data .= '<td>'. $val->noofdays .'</td>';
                    $data .= '<td>'. $val->leave_type .'</td>';
                    $data .= '<td>'. $val->reason .'</td>';
                    $status = "Pending";
                    if ($val->status == '1') {
                        $status = "Approved";
                    } elseif ($val->status == '2') {
                        $status = "Auto Approved";
                    } elseif ($val->status == '3') {
                        $status = "Approved";
                    }
                    $data .= '<td>'. $status .'</td>';
                    $data .= '<td></td>';
                    if ($val->sub == 1) {
                        // $data .= '<td><a disabled href='.site_url("staff/staffleave_controller/edit_leave/" . $val->id). ' class="btn btn-warning " data-placement="top"  data-toggle="tooltip" data-original-title="Edit Leave"><i class="fa fa-edit"></i></a><a disabled href='.site_url("staff/staffleave_controller/delete_leave/" . $val->id). ' class="btn btn-warning " data-placement="top"  data-toggle="tooltip" data-original-title="Delete Leave"><i class="fa fa-trash-o"></i></a></td>';
                    } else {
                        if ($val->status == '0') {
                            $disabled = "";
                            if($staffId != $val->staff_id) $disabled = " disabled";
                            // $data .= '<td><a'. $disabled .' href='.site_url("staff/staffleave_controller/edit_leave/" . $val->id). ' class="btn btn-warning " data-placement="top"  data-toggle="tooltip" data-original-title="Edit Leave"><i class="fa fa-edit"></i></a><a'. $disabled .' href='.site_url("staff/staffleave_controller/delete_leave/" . $val->id). ' class="btn btn-warning " data-placement="top"  data-toggle="tooltip" data-original-title="Delete Leave"><i class="fa fa-trash-o"></i></a></td>';
                        } else if($val->status == '2'){
                            $disabled = "";
                            if($staffId != $val->leave_filed_by) $disabled = " disabled";
                            // $data .= '<td><a href='.site_url("staff/staffleave_controller/edit_leave/" . $val->id). ' class="btn btn-warning " data-placement="top"  data-toggle="tooltip" data-original-title="Edit Leave"><i class="fa fa-edit"></i></a><a href='.site_url("staff/staffleave_controller/delete_leave/" . $val->id). ' class="btn btn-warning " data-placement="top"  data-toggle="tooltip" data-original-title="Delete Leave"><i class="fa fa-trash-o"></i></a></td>';
                        } else {
                            $data .= '<td>'.$val->description.'</td>';
                        }
                    }
                }
                $data .= '</tbody></table>';
            }
            
        }
        
        echo json_encode($data);
    }

    public function getAggregateReport(){
        $from = date('Y-m-d');
        $dates = array(date('Y-m-d'));
        // $result = $this->staffleave_model->getLeaveAggregate($dates);
        // $data['leaveData'] = $result;
        $data['staffDetails'] = $this->staffleave_model->getStaffDetails();
        // echo "<pre>"; print_r($result); die();
        $data['main_content'] = 'staff/staff_leave_application/staff_leave_report';
        $this->load->view('inc/template', $data);
    }

    public function getAggregateLeaves(){
        if(!empty($_POST['from']) && !empty($_POST['to'])) {
            $from = $_POST['from'];
            $to = $_POST['to'];
            $staffId = $_POST['staffId'];
            $filter = $_POST['filter'];
            
            $start_date = strtotime($from);
            $end_date = strtotime($to);
            $datediff = $end_date - $start_date;
            $leaveDays = ($datediff / (60 * 60 * 24)) + 1;
            $dates = array();
            for($i = 0; $i < $leaveDays; $i++){
                $date = date("Y-m-d", strtotime($from." +".$i." days"));
                array_push($dates, $date);
            }
            // echo "<pre>"; print_r($dates);die();
            $result = $this->staffleave_model->getLeaveAggregate($dates, $staffId, $filter);
            // echo "<pre>"; print_r($result); die();
            $data = "";
            if(empty($result)) $data = '<h4>No Leaves Taken Yet</h4>';
            else {
                $data .= '<table class="table table-bordered datatable"><thead><tr><th>#</th><th>Staff Name</th><th>Day Count</th><th>Action</th></tr></thead><tbody>';
                $i=1;
                foreach ($result as $key => $val) {
                        $data .= '<tr><td>'. $i++ .'</td>';
                        $data .= '<td>'. $val->Name .'</td>';
                        $data .= '<td>'. $val->count .'</td>';
                        $data .= '<td><a onclick="viewDetails('.$val->id.', \''.$val->Name.'\')" class="btn  btn-primary">View Details</a></td>';
                        $data .= '</tr>';
                }
                $data .= '</tbody></table>';
            }
            echo json_encode($data);
        }
    }

    public function getstaffAggregateLeaves(){
        $staff = $_POST['staff'];
        $result = $this->staffleave_model->getLeaveAggregate_staff($staff);
        $data = "";
        if(empty($result)) $data = '<h4>No Leaves Taken Yet</h4>';
        else {
            $data .= '<table class="table table-bordered datatable"><thead><tr><th>#</th><th>Staff Name</th><th>Day Count</th></tr></thead><tbody>';
            $i=1;
            foreach ($result as $key => $val) {
                    $data .= '<tr><td>'. $i++ .'</td>';
                    $data .= '<td>'. $val->Name .'</td>';
                    $data .= '<td>'. $val->count .'</td></tr>';
            }
            $data .= '</tbody></table>';
        }
        echo json_encode($data);
    }

    public function getstaffLeaves() {
        $staffId = $_POST['staffId'];
        $result = $this->staffleave_model->getStaffLeaves($staffId);
        echo json_encode($result);
    }
}