<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

if(!function_exists('sendEmail')) {
    function sendEmail($message, $subject, $email_master_id, $email_ids, $from_email, $file=[]) {
        $CI =& get_instance();
        $from_name = $CI->settings->getSetting('school_name');
        $CI->load->library('filemanager');
        $smtp_user = CONFIG_ENV['smtp_user'];
        $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
        $smtp_host = CONFIG_ENV['smtp_host'];
        $smtp_port = CONFIG_ENV['smtp_port'];

        $attachment = '';
        if(!empty($file)) {
            $message .= '<br>Attachments: ';
            // $files = explode(",", $file);
            $i=1;
            foreach ($file as $k => $path) {
                $file_path = $CI->filemanager->getFilePath($path->path);
                $file[$k]->path = $file_path;
                $message .= '<br><a target="_blank" rel="noopener noreferrer" href="'.$file_path.'">'.$path->name.'</a>';
            }
        }

        $data = array(
            'message' => $message,
            'attachment' => $attachment,
            'files' => $file,
            'subject' => $subject,
            'members' => $email_ids,
            'email_master_id' => $email_master_id,
            'school' => CONFIG_ENV['school_sub_domain'],
            'from_email' => $from_email,
            'from_name' => $from_name,
            'smtp_user' => $smtp_user,
            'smtp_pass' => $smtp_pass,
            'smtp_host' => $smtp_host,
            'smtp_port' => $smtp_port
        );

        // echo "<pre>"; print_r($data);

        $data = http_build_query($data);
        // trigger_error($data);
        // echo "data: ".$data;die();
        
        $curl = curl_init();


        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];

        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_email_service_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
          // echo "cURL Error #:" . $err;
          return 0;
        } else {
          return 1;
        }
    }
}