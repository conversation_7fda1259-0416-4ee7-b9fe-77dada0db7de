<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Sibling_staff_controller extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isAuthorized('USER_MANAGEMENT.CONNECT_SIBLINGS')) {
			redirect('dashboard', 'refresh');
		}
    $this->load->model('student/Sibling_staff_model','ssm');
    $this->load->model('user_provisioning_model');
  }

  public function index(){
  	$data['staffList'] = $this->ssm->getStaff();
  	$data['classSections'] = $this->ssm->getClassSections();
  	$data['main_content'] = 'student/sibling_staff/index';
    $this->load->view('inc/template', $data);
  }

  public function getStudents(){
  	echo json_encode($this->ssm->getStudents($_POST['class_section']));
  }

  public function getAccounts(){
  	$staff = $_POST['staff'];
  	$student = $_POST['student'];
  	$data = array('staff'=>array(), 'student'=>array());
  	if($staff){
  		$data['staff'] = $this->ssm->getStaffDetails($staff);
  	}
  	if($student){
  		$data['student'] = $this->ssm->getStdDetails($student);
  	}
  	echo json_encode($data);
  }

  public function linkAccounts(){
  	
		
    // echo '<pre>'; print_r($_POST); die();
  	echo $this->ssm->linkAccounts();
  }

  public function viewLinkAccounts(){
		$result = $this->ssm->getLinkedAccounts_New();
		// echo "<pre>";print_r($result);die();
		//Send a json of student ids. This will help it easier to unlink
		// foreach ($result as &$link) {
		// 	$saIds = [];	
		// 	foreach ($link->avatarArray as $avt) {
		// 		if ($avt['aType'] === 'Parent') {
		// 			$saIds[] = $avt['saId'];
		// 		}
		// 	}
		// 	$link->json_saIds = json_encode($saIds, JSON_HEX_QUOT);
		// }
		
		$data['result'] = $result;
		// echo "<pre>";print_r($data['result']);die();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'student/sibling_staff/linked_accounts_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'student/sibling_staff/linked_accounts_mobile';
        }else{
          $data['main_content'] = 'student/sibling_staff/linked_accounts';     	
        }

		
		$this->load->view('inc/template', $data);
	}

  public function unlinkAccounts(){
		$userId = $_POST['userId'];
		// $sa_ids = json_decode($_POST['json_sa_ids']);
		$sa_id = $_POST['sa_id'];
		$stakeholder_id = $_POST['stakeholder_id'];

		echo $this->ssm->unlinkAccounts($userId, $sa_id, $stakeholder_id);
  }

  public function connect_siblingbyid($studentId){
    $data['student_uid'] = $studentId;
    $data['stdData'] = $this->user_provisioning_model->get_student_details_by_id($studentId);
    // echo "<pre>"; print_r($data['stdData']); die();
    $data['staffList'] = $this->ssm->getStaff();
    $data['classSections'] = $this->ssm->getClassSections();
	if ($this->mobile_detect->isTablet()) {
		$data['main_content'] = 'student/sibling_staff/connect_sibling_tablet';
	}else if($this->mobile_detect->isMobile()){
		$data['main_content'] = 'student/sibling_staff/connect_sibling_mobile';
	}else{
		$data['main_content'] = 'student/sibling_staff/connect_sibling';     	
	}
    $this->load->view('inc/template', $data);
  }

  public function find_linked_student_ids() {
	$section_id= $_POST['section_id'];
	$ids= $this->ssm->find_linked_student_ids($section_id);
	echo json_encode($ids);
  }
  
}