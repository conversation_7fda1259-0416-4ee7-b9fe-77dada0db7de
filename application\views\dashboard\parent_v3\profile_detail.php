<!-- Profile Detail Page - Mobile UI -->

<style>
    .student-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
    }

    .student-photo {
        flex-shrink: 0;
    }

    .student-avatar {
        width: 80px;
        height: 100px;
        border-radius: 12px;
        object-fit: cover;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border: 3px solid #fff;
    }

    .student-info {
        flex: 1;
    }

    .student-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: #333;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .profile-details {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #7b5cff;
        gap: 1rem;
    }

    .detail-label {
        font-weight: 600;
        color: #555;
        font-size: 0.9rem;
        min-width: 40%;
        flex-shrink: 0;
    }

    .detail-value {
        color: #333;
        font-size: 0.9rem;
        text-align: right;
        word-break: break-word;
        flex: 1;
    }

    .edit-profile-btn {
        background: #7b5cff;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
    }

    .edit-profile-btn:hover {
        background: #6a4de8;
        transform: translateY(-1px);
        color: white;
        text-decoration: none;
    }

    .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .student-header {
            flex-direction: column;
            text-align: center;
            gap: 0.75rem;
        }

        .detail-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
        }

        .detail-label {
            min-width: auto;
        }

        .detail-value {
            text-align: left;
        }
    }
</style>

<style>
    .profile-header {
        background: #fff;
        padding: 1rem 1rem 0.5rem 1rem;
        border-bottom-left-radius: 18px;
        border-bottom-right-radius: 18px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        display: flex;
        align-items: center;
        position: relative;
    }
    .profile-header .back-btn {
        font-size: 1.5rem;
        color: #7b5cff;
        background: none;
        border: none;
        margin-right: 0.5rem;
        padding: 0;
        cursor: pointer;
    }
    .profile-header-title {
        flex: 1;
        text-align: center;
        font-weight: 700;
        font-size: 1.1rem;
        color: #333;
    }
    .profile-alert {
        background: linear-gradient(135deg, #dae6fa 0%, #e8f2ff 100%);
        border-radius: 16px;
        margin: 1rem;
        padding: 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    }
    .profile-alert .alert-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
    }
    .profile-alert .alert-text {
        flex: 1;
        font-size: 0.95rem;
        color: #333;
        line-height: 1.4;
    }
    .profile-alert .alert-button {
        background: #7b5cff;
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        white-space: nowrap;
    }
    .profile-alert .alert-button:hover {
        background: #6a4de8;
        transform: translateY(-1px);
    }
    .profile-alert.confirmed {
        background: linear-gradient(135deg, #d4edda 0%, #e8f5e8 100%);
        border: 1px solid #c3e6cb;
    }
    .profile-alert.confirmed .alert-text {
        color: #155724;
    }
    .profile-alert.locked {
        background: linear-gradient(135deg, #f8d7da 0%, #fce4e6 100%);
        border: 1px solid #f5c6cb;
    }
    .profile-alert.locked .alert-text {
        color: #721c24;
    }
    .referral-section {
        background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
        border-radius: 16px;
        margin: 1rem;
        padding: 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        border: 1px solid #ffeaa7;
    }
    .referral-section .referral-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
    }
    .referral-section .referral-text {
        flex: 1;
        font-size: 0.95rem;
        color: #856404;
        font-weight: 500;
    }
    .referral-section .referral-button {
        background: #ffc107;
        color: #212529;
        border: none;
        border-radius: 12px;
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        white-space: nowrap;
    }
    .referral-section .referral-button:hover {
        background: #e0a800;
        transform: translateY(-1px);
    }
    .content-container {
        margin-bottom: 6rem;
        padding-bottom: 1rem;
    }
    @media (min-width: 600px) {
        .profile-header, .profile-alert, .referral-section, .content-container {
            max-width: 440px;
            margin-left: auto;
            margin-right: auto;
        }
        .profile-alert, .referral-section {
            margin-left: auto;
            margin-right: auto;
            margin-top: 1rem;
            margin-bottom: 1rem;
        }
    }
</style>

<div class="profile-header">
    <button class="back-btn" onclick="window.history.back()">
        <i class="fa fa-chevron-left"></i>
    </button>
    <div class="profile-header-title">Profile Detail</div>
    <div style="width:32px;"></div>
</div>

<div class="content-container">
    <!-- Referral Link Section -->
    <?php if($this->settings->getSetting('enable_enquiry_student_referral_url') == 1) { ?>
    <div class="referral-section">
        <div class="referral-content">
            <div class="referral-text">Get your referral link</div>
            <button class="referral-button" onclick="generate_enquiry_referal_link('<?php echo $student_id ?>')">
                <i class="fa fa-link me-1"></i> Get URL
            </button>
        </div>
    </div>
    <?php } ?>

    <!-- Profile Status Alert -->
    <?php if ($studentData['profile_status'] == 'Unlock') { ?>
    <div class="profile-alert">
        <div class="alert-content">
            <div class="alert-text">
                <i class="fa fa-info-circle me-2"></i>
                Click the Confirm button if the below information is correct.
            </div>
            <button class="alert-button" onclick="update_profile_confirmedbyuser('<?php echo $studentData['stdYearId'] ?>')">
                <i class="fa fa-check me-1"></i> Confirm
            </button>
        </div>
    </div>
    <?php } else { ?>
        <?php if ($studentData['profile_confirmed'] == 'Yes') { ?>
        <div class="profile-alert confirmed">
            <div class="alert-content">
                <div class="alert-text">
                    <i class="fa fa-check-circle me-2"></i>
                    <?php if ($studentData['profile_confirmed_date']) { ?>
                        You confirmed your profile information on <?php echo date('d-M-Y', strtotime($studentData['profile_confirmed_date'])) ?>
                    <?php } else { ?>
                        You have confirmed your profile information
                    <?php } ?>
                </div>
            </div>
        </div>
        <?php } else { ?>
        <div class="profile-alert locked">
            <div class="alert-content">
                <div class="alert-text">
                    <i class="fa fa-lock me-2"></i>
                    <?php if ($studentData['profile_status_changed_date']) { ?>
                        Profile locked on <?php echo date('d-M-Y', strtotime($studentData['profile_status_changed_date'])) ?>
                    <?php } else { ?>
                        Profile has been locked to prevent further edits
                    <?php } ?>
                </div>
            </div>
        </div>
        <?php } ?>
    <?php } ?>
        <div class="profile-section">
            <?php 
            $headerName = '';
            foreach ($displayList as $list => $value) { 
                    switch ($list) {
                        case 'student_info':
                            $headerName = 'Student Profile';
                            break;
                        case 'father_info':
                             $headerName = 'Father Profile';
                            break;
                        case 'mother_info':
                             $headerName = 'Mother Profile';
                            break;
                        case 'guardian_info':
                            $headerName = 'Guardian Profile';
                            break;
                        default:
                            $headerName = 'Profile';
                            break;
                    }
                ?>
                <div class="section-header">
                    <h4><i class="fa fa-user me-2"></i><?php echo $headerName ?></h4>
                    <?php if ($studentData['profile_status'] == 'Unlock') { ?>
                    <a href="<?php echo site_url('parent_controller/edit_profile_parent_mobile/'.$callFrom); ?>" class="edit-profile-btn">
                        <i class="fa fa-pencil"></i>
                    </a>
                    <?php } ?>
                </div>

                <?php 
                foreach ($value as $val) {
                    if ($val['data_input'] == 'file') { ?>
                        <div class="section-content">
                            <div class="student-header">
                                <div class="student-photo">
                                    <img src="" alt="<?php echo $list; ?> Photo"
                                        class="student-avatar"
                                        data-column="<?php echo $val['table_column_name']; ?>"
                                        data-field="<?php echo $val['column_name']; ?>">
                                </div>
                                <div class="student-info">
                                    <h3 class="student-name"
                                        data-column="<?php echo $val['table_column_name']; ?>"
                                        data-field="<?php echo $val['column_name']; ?>">
                                    </h3>
                                </div>
                            </div>
                        </div>
                    <?php } else { ?>
                        <div class="detail-item"
                            data-field="<?php echo $val['column_name']; ?>"
                            data-table-column="<?php echo $val['table_column_name']; ?>">
                            <span class="detail-label"><?php echo $val['display_name']; ?></span>
                            <span class="detail-value"
                                data-column="<?php echo $val['table_column_name']; ?>">
                                Loading...
                            </span>
                        </div>
                    <?php }
                } 
            } 
            ?>
        
        </div>
    </div>
</div>

<style>
    .profile-section {
        background: #fff;
        border-radius: 16px;
        margin: 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        overflow: hidden;
    }
    .section-header {
        background: linear-gradient(135deg, #dae6fa 0%, #e8f2ff 100%);
        padding: 1rem;
        border-bottom: 1px solid #e0e0e0;
    }
    .section-header h4 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        display: flex;
        align-items: center;
    }
    .section-header h4 i {
        color: #7b5cff;
        margin-right: 0.5rem;
    }
    .section-content {
        padding: 1rem;
    }
    .family-photo-container {
        text-align: center;
        padding: 1rem 0;
    }
    .family-photo {
        max-width: 200px;
        max-height: 200px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        object-fit: cover;
    }
    .remarks-text {
        font-size: 0.95rem;
        line-height: 1.5;
        color: #333;
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 12px;
        border-left: 4px solid #7b5cff;
    }
    .text-muted {
        color: #888 !important;
        font-style: italic;
    }
    @media (min-width: 600px) {
        .profile-section {
            max-width: 440px;
            margin-left: auto;
            margin-right: auto;
            margin-top: 1rem;
            margin-bottom: 1rem;
        }
    }
</style>



<script type="text/javascript">
    let student_id = '<?php echo $student_id ?>';
    // loadStudentProfilcolumns();

    loadStudentProfileData();
   
    function loadStudentProfileData(){
        var listDataInput = [];
        $('.detail-item').each(function () {
            var field = $(this).data('field');
            var tableColumn = $(this).data('table-column');
            // Push the extracted data into the array
            listDataInput.push({
                field: field,
                table_column: tableColumn
            });
        });

        // Also collect photo fields
        $('img[data-field]').each(function () {
            var field = $(this).data('field');
            var tableColumn = $(this).data('column');
            if (field && tableColumn) {
                listDataInput.push({
                    field: field,
                    table_column: tableColumn
                });
            }
        });

        console.log('Sending data to server:', listDataInput);

        $.ajax({
            url: '<?php echo site_url('parent/dashboard_controller/getStudentDetailsById'); ?>',
            type: 'post',
            data: {'student_id' : student_id,'listDataInput':listDataInput},
            success: function(data) {
                console.log('Raw response:', data);
                try {
                    var response = JSON.parse(data);
                    console.log('Parsed response:', response);
                    construct_data(response);
                } catch (e) {
                    console.error('Error parsing JSON:', e);
                    $('.profile-section').html('<div style="text-align: center; padding: 2rem; color: #dc3545;">Error parsing server response.</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                $('.profile-section').html('<div style="text-align: center; padding: 2rem; color: #dc3545;">Error loading profile data.</div>');
            }
        });
    }

    function construct_data(response){
        try {
            console.log('Constructing data with response:', response);

            if (!response) {
                console.error('No response data received');
                $('.profile-section').html('<div style="text-align: center; padding: 2rem; color: #dc3545;">No data received from server.</div>');
                return;
            }

            if (response.error) {
                console.error('Server error:', response.error);
                $('.profile-section').html('<div style="text-align: center; padding: 2rem; color: #dc3545;">Error: ' + response.error + '</div>');
                return;
            }

            // Update profile fields with actual data
            updateProfileFields(response);

        } catch (error) {
            console.error('Error in construct_data:', error);
            $('.profile-section').html('<div style="text-align: center; padding: 2rem; color: #dc3545;">Error processing data.</div>');
        }
    }

    function updateProfileFields(data) {
        // Update all detail items with data
        $('.detail-item[data-field]').each(function() {
            var $item = $(this);
            var fieldName = $item.data('field');
            var $valueSpan = $item.find('.detail-value');

            // Get the value from response data
            var value = getFieldValue(data, fieldName);

            if (value !== null && value !== undefined && value !== '') {
                $valueSpan.text(value);
                $item.show();
            } else {
                $valueSpan.text('-');
                // Optionally hide empty fields
                // $item.hide();
            }
        });

        // Update photo fields
        $('img[data-field]').each(function() {
            var $img = $(this);
            var fieldName = $img.data('field');
            var photoUrl = getFieldValue(data, fieldName);

            if (photoUrl) {
                $img.attr('src', photoUrl);
            } else {
                // Set default photo
                $img.attr('src', '<?php echo base_url('assets/img/icons/profile.png'); ?>');
            }
        });

        // Update name fields
        $('.student-name[data-field]').each(function() {
            var $name = $(this);
            var fieldName = $name.data('field');
            var nameValue = getFieldValue(data, fieldName);

            if (nameValue) {
                $name.text(nameValue);
            }
        });
    }

    function getFieldValue(data, fieldName) {
        // Since we're using the column names directly from the configuration,
        // the response should have the field names as keys
        return data[fieldName] || null;
    }

    function loadStudentProfilcolumns(){
        // Parse the PHP data properly
        var parent_columns = <?php echo json_encode($parent_columns); ?>;
        var display_columns = <?php echo json_encode($display_columns); ?>;

        // Group columns by tabs
        var groupedColumns = {
            'student_info': [],
            'father_info': [],
            'mother_info': [],
            'guardian_info': []
        };

        // Group parent columns by tabs and filter by display_columns
        parent_columns.forEach(function(column) {
            if (display_columns.includes(column.column_name)) {
                if (groupedColumns[column.tabs]) {
                    groupedColumns[column.tabs].push(column);
                }
            }
        });

        var html = '';
        // Generate Student Profile Section
        if (groupedColumns.student_info.length > 0) {
            html += generateProfileSection('Student Profile', 'fa-user', groupedColumns.student_info, 'student');
        }

        // Generate Father Profile Section
        if (groupedColumns.father_info.length > 0) {
            html += generateProfileSection('Father Profile', 'fa-male', groupedColumns.father_info, 'father');
        }

        // Generate Mother Profile Section
        if (groupedColumns.mother_info.length > 0) {
            html += generateProfileSection('Mother Profile', 'fa-female', groupedColumns.mother_info, 'mother');
        }

        // Generate Guardian Profile Section
        if (groupedColumns.guardian_info.length > 0) {
            html += generateProfileSection('Guardian Profile', 'fa-users', groupedColumns.guardian_info, 'guardian');
        }

        // Replace the existing profile section with dynamic content
        if (html.trim() !== '') {
            $('.profile-section').html(html);
        } else {
            $('.profile-section').html('<div style="text-align: center; padding: 2rem; color: #666;">No profile fields configured to display.</div>');
        }

    }

    function generateProfileSection(title, icon, columns, type) {
        var html = `
            <div class="section-header">
                <h4><i class="fa ${icon} me-2"></i>${title}</h4>
                <?php if ($studentData['profile_status'] == 'Unlock') { ?>
                <a href="<?php echo site_url('parent_controller/edit_profile_parent_mobile/'.$callFrom); ?>" class="edit-profile-btn">
                    <i class="fa fa-pencil"></i>
                </a>
                <?php } ?>
            </div>`;

        // Add photo section for profiles that have photos
        var photoColumn = columns.find(col => col.data_input === 'file');
        if (photoColumn) {
            html += `
            <div class="section-content">
                <div class="student-header">
                    <div class="student-photo">
                        <img src="" alt="${title} Photo" class="student-avatar" data-field="${photoColumn.column_name}">
                    </div>
                    <div class="student-info">
                        <h3 class="student-name" data-field="${type.toUpperCase()}_NAME"></h3>
                    </div>
                </div>
            </div>`;
        }

        // Add profile details
        html += '<div class="profile-details">';

        columns.forEach(function(column) {
            // Skip photo columns as they're handled above
            if (column.data_input !== 'file') {
                html += `
                    <div class="detail-item" data-field="${column.column_name}">
                        <span class="detail-label">${column.display_name}</span>
                        <span class="detail-value" data-column="${column.table_column_name}">Loading...</span>
                    </div>`;
            }
        });

        html += '</div>';

        return html;
    }

  function update_profile_confirmedbyuser(stdYearId) {
    bootbox.confirm({
      title: "Confirm Profile",
      message: "Are you sure that the profile information is correct?",
      className: "modern-modal",
      buttons: {
        confirm: {
            label: '<i class="fa fa-check me-1"></i> Yes, Confirm',
            className: 'btn btn-success'
        },
        cancel: {
            label: '<i class="fa fa-times me-1"></i> Cancel',
            className: 'btn btn-secondary'
        }
      },
      callback: function (result) {
        if(result) {
          // Show loading state
          $('.alert-button').html('<i class="fa fa-spinner fa-spin me-1"></i> Confirming...');
          $('.alert-button').prop('disabled', true);

          $.ajax({
            url: '<?php echo site_url('parent_controller/update_profile_confirmed'); ?>',
            type: 'post',
            data: {'stdYearId' : stdYearId},
            success: function(data) {
              var response = JSON.parse(data);
              if (response == 1) {
                // Show success message briefly before reload
                $('.alert-button').html('<i class="fa fa-check me-1"></i> Confirmed!');
                setTimeout(() => location.reload(), 1000);
              } else {
                mandatory_fields_display_in_popup(response);
                $('.alert-button').html('<i class="fa fa-check me-1"></i> Confirm');
                $('.alert-button').prop('disabled', false);
              }
            },
            error: function() {
              $('.alert-button').html('<i class="fa fa-check me-1"></i> Confirm');
              $('.alert-button').prop('disabled', false);
              bootbox.alert({
                title: 'Error',
                message: 'Something went wrong. Please try again.',
                className: 'modern-modal'
              });
            }
          });
        }
      }
    });
  }

  function mandatory_fields_display_in_popup(response) {
    var html = '<div class="missing-fields-container">';
    html += '<div class="missing-fields-header">';
    html += '<i class="fa fa-exclamation-triangle text-warning me-2"></i>';
    html += '<h6 class="mb-3">The following fields need to be completed:</h6>';
    html += '</div>';
    html += '<div class="missing-fields-list">';

    for (var i = 0; i < response.length; i++) {
      var field = response[i].replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      html += '<div class="missing-field-item">';
      html += '<span class="field-number">' + (i + 1) + '</span>';
      html += '<span class="field-name">' + field + '</span>';
      html += '</div>';
    }

    html += '</div>';
    html += '<div class="missing-fields-footer">';
    html += '<p class="mb-0"><i class="fa fa-info-circle me-1"></i> Please complete these fields and try confirming again.</p>';
    html += '</div>';
    html += '</div>';

    bootbox.alert({
      title: 'Missing Information',
      message: html,
      className: 'modern-modal missing-fields-modal',
      backdrop: true
    });
  }

  function generate_enquiry_referal_link(student_id) {
    // Show loading state
    $('.referral-button').html('<i class="fa fa-spinner fa-spin me-1"></i> Generating...');
    $('.referral-button').prop('disabled', true);

    $.ajax({
        url: '<?php echo site_url('parent_controller/get_referal_link'); ?>',
        type: 'post',
        data: { 'student_id': student_id },
        success: function(data) {
            var response = JSON.parse(data);
            if (response) {
                bootbox.alert({
                    title: '<i class="fa fa-link me-2"></i>Your Referral Link',
                    message: `
                        <div class="referral-link-container">
                            <div class="referral-link-text">
                                <input type="text" id="referralLinkInput" value="${response}" readonly class="form-control mb-3">
                            </div>
                            <div class="referral-link-actions">
                                <button class="btn btn-primary" onclick="copyReferralLink('${response}', this)">
                                    <i class="fa fa-copy me-1"></i> Copy Link
                                </button>
                            </div>
                        </div>
                    `,
                    className: 'modern-modal referral-modal',
                    backdrop: true
                });
            }
            // Reset button state
            $('.referral-button').html('<i class="fa fa-link me-1"></i> Get URL');
            $('.referral-button').prop('disabled', false);
        },
        error: function() {
            $('.referral-button').html('<i class="fa fa-link me-1"></i> Get URL');
            $('.referral-button').prop('disabled', false);
            bootbox.alert({
                title: 'Error',
                message: 'Unable to generate referral link. Please try again.',
                className: 'modern-modal'
            });
        }
    });
  }

  // Enhanced copy function for referral link
  function copyReferralLink(text, button) {
      navigator.clipboard.writeText(text).then(() => {
          $(button).html('<i class="fa fa-check me-1"></i> Copied!');
          $(button).removeClass('btn-primary').addClass('btn-success');

          setTimeout(() => {
              $(button).html('<i class="fa fa-copy me-1"></i> Copy Link');
              $(button).removeClass('btn-success').addClass('btn-primary');
          }, 2000);
      }).catch(err => {
          console.error('Could not copy text: ', err);
          // Fallback for older browsers
          $('#referralLinkInput').select();
          document.execCommand('copy');
          $(button).html('<i class="fa fa-check me-1"></i> Copied!');
          $(button).removeClass('btn-primary').addClass('btn-success');

          setTimeout(() => {
              $(button).html('<i class="fa fa-copy me-1"></i> Copy Link');
              $(button).removeClass('btn-success').addClass('btn-primary');
          }, 2000);
      });
  }

  // Dynamic profile data loading function
  function loadDynamicProfileData(studentId) {
      if (!studentId) {
          console.error('Student ID is required');
          return;
      }

      $.ajax({
          url: '<?php echo site_url('parent/dashboard_controller/getStudentDetailsById'); ?>',
          type: 'POST',
          data: { 'student_id': studentId },
          dataType: 'json',
          beforeSend: function() {
              // Show loading state
              $('.profile-section').addClass('loading');
          },
          success: function(response) {
              if (response.error) {
                  console.error('Error loading profile data:', response.error);
                  return;
              }

              // Update profile sections dynamically
              updateProfileSections(response);

              console.log('Profile data loaded successfully:', response);
          },
          error: function(xhr, status, error) {
              console.error('AJAX error loading profile data:', error);
          },
          complete: function() {
              // Remove loading state
              $('.profile-section').removeClass('loading');
          }
      });
  }

  // Function to update profile sections with dynamic data
  function updateProfileSections(data) {
      const studentData = data.student_data;
      const enabledFields = data.enabled_fields;
      const fieldLabels = data.field_labels;

      // Update student name if enabled
      if (enabledFields.includes('STUDENT_NAME') && studentData.STUDENT_NAME) {
          $('.student-name').text(studentData.STUDENT_NAME);
      }

      // Update profile photo if enabled
      if (enabledFields.includes('STUDENT_PHOTO') && studentData.STUDENT_PHOTO) {
          $('.student-avatar').attr('src', studentData.STUDENT_PHOTO);
      }

      // Update other fields dynamically
      enabledFields.forEach(function(field) {
          if (studentData[field] && fieldLabels[field]) {
              const value = studentData[field];
              const label = fieldLabels[field];

              // Find or create detail item for this field
              let detailItem = $(`.detail-item[data-field="${field}"]`);
              if (detailItem.length === 0) {
                  detailItem = $(`
                      <div class="detail-item" data-field="${field}">
                          <span class="detail-label">${label}</span>
                          <span class="detail-value">${value}</span>
                      </div>
                  `);
                  $('.profile-details').append(detailItem);
              } else {
                  detailItem.find('.detail-value').text(value);
              }
          }
      });
  }

  // Initialize dynamic loading on page load
  $(document).ready(function() {
      // Check if we have dynamic configuration
      <?php if (isset($profileDisplayConfig) && !empty($profileDisplayConfig['enabled_fields'])) { ?>
      console.log('Dynamic profile configuration detected');
      console.log('Enabled fields:', <?php echo json_encode($profileDisplayConfig['enabled_fields']); ?>);

      // Optionally reload data dynamically
      // loadDynamicProfileData(<?php echo $student_id; ?>);
      <?php } else { ?>
      console.log('Using static profile configuration');
      <?php } ?>
  });
</script>
<!-- Modern Modal Styles -->
<style>
    /* Modern Modal Styling */
    .modern-modal .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
    }

    .modern-modal .modal-content {
        border: none;
        border-radius: 16px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    }

    .modern-modal .modal-header {
        background: linear-gradient(135deg, #7b5cff 0%, #4e8cff 100%);
        color: white;
        border-radius: 16px 16px 0 0;
        padding: 1.25rem 1.5rem;
        border-bottom: none;
    }

    .modern-modal .modal-title {
        font-weight: 600;
        font-size: 1.1rem;
    }

    .modern-modal .modal-body {
        padding: 1.5rem;
        font-size: 0.95rem;
        line-height: 1.5;
    }

    .modern-modal .modal-footer {
        border-top: 1px solid #e0e0e0;
        padding: 1rem 1.5rem;
        border-radius: 0 0 16px 16px;
    }

    .modern-modal .btn {
        border-radius: 12px;
        padding: 0.6rem 1.5rem;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.2s;
    }

    .modern-modal .btn:hover {
        transform: translateY(-1px);
    }

    /* Missing Fields Modal Specific Styles */
    .missing-fields-container {
        max-height: 400px;
        overflow-y: auto;
    }

    .missing-fields-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e0e0e0;
    }

    .missing-fields-header h6 {
        margin: 0;
        color: #333;
        font-weight: 600;
    }

    .missing-fields-list {
        margin-bottom: 1rem;
    }

    .missing-field-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        background: #f8f9fa;
        border-radius: 12px;
        border-left: 4px solid #ffc107;
    }

    .field-number {
        background: #ffc107;
        color: #212529;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
        margin-right: 0.75rem;
        flex-shrink: 0;
    }

    .field-name {
        font-weight: 500;
        color: #333;
    }

    .missing-fields-footer {
        padding-top: 1rem;
        border-top: 1px solid #e0e0e0;
        color: #666;
        font-size: 0.9rem;
    }

    /* Referral Modal Specific Styles */
    .referral-link-container {
        text-align: center;
    }

    .referral-link-text input {
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 0.75rem;
        text-align: center;
    }

    .referral-link-actions .btn {
        min-width: 120px;
    }

    /* Responsive Modal Adjustments */
    @media (max-width: 576px) {
        .modern-modal .modal-dialog {
            margin: 1rem;
            max-width: calc(100% - 2rem);
        }

        .modern-modal .modal-body,
        .modern-modal .modal-header,
        .modern-modal .modal-footer {
            padding-left: 1rem;
            padding-right: 1rem;
        }
    }

    /* Utility classes for icons */
    .me-1 { margin-right: 0.25rem; }
    .me-2 { margin-right: 0.5rem; }
    .mb-0 { margin-bottom: 0; }
    .mb-3 { margin-bottom: 1rem; }
    .text-warning { color: #ffc107; }

    /* Loading states for dynamic content */
    .profile-section.loading {
        position: relative;
        opacity: 0.6;
        pointer-events: none;
    }

    .profile-section.loading::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #7b5cff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 1000;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Dynamic field highlighting */
    .detail-item[data-field] {
        transition: all 0.3s ease;
    }

    .detail-item[data-field].updated {
        background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
        border-left-color: #28a745;
    }

    /* Configuration info panel */
    .config-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border: 1px solid #e1bee7;
        border-radius: 12px;
        padding: 1rem;
        margin: 1rem;
        font-size: 0.9rem;
    }

    .config-info h5 {
        color: #4a148c;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .config-info .config-details {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .config-tag {
        background: #7b5cff;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
    }
</style>