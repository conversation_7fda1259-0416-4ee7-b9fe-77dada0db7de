<?php

class Menu extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('EXAMINATION_V2')) {
            redirect('dashboard', 'refresh');
        }
    }

    public function index(){
        $site_url = site_url();
        $data['admin_tiles'] = array(
            /*[
              'title' => 'Manage Subjects',
              'sub_title' => 'Add/Edit Subjects',
              'icon' => 'svg_icons/attendance.svg',
              'url' => $site_url.'examination_v2/subjects',
              'permission' => $this->authorization->isAuthorized('EXAMINATION_V2.MANAGE_SUBJECTS')
            ],*/
            [
              'title' => 'Manage Electives',
              'sub_title' => 'Add/Edit Electives',
              'icon' => 'svg_icons/attendance.svg',
              'url' => $site_url.'examination_v2/electives',
              'permission' => $this->authorization->isAuthorized('EXAMINATION_V2.MANAGE_ELECTIVES')
            ]
        );
        $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);
        $data['main_content'] = 'examination_v2/menu';
        $this->load->view('inc/template_without_top_nav', $data);
    }

}