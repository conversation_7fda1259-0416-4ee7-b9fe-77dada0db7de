<?php
class Nc_reports extends CI_Controller{
	function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('STUDENT_NONCOMPLIANCE')) {
          redirect('dashboard', 'refresh');
        }
        $this->load->model('noncompliance_model', 'noncompliance_model');
      }
    // balance nc report 
    public function balance_nc_report_view(){
        $data['section_list'] = $this->noncompliance_model->getClassNames();
        // echo '<pre>'; print_r($data['nc_report']); die();
    
        // echo '<pre>'; print_r($data['new']); die();
        $data['acad_years'] = $this->noncompliance_model->get_acad_years();
        $data['main_content'] = 'student_nc/balance_nc_report';    	
        $this->load->view('inc/template', $data);
      }
    
    //balance nc report data
    public function get_balance_nc_data(){
        $section_id=$_POST['section_id'];
        $acad_year=$_POST['acad_year'];
        $data['nc_report'] = $this->noncompliance_model->get_student_wise_balance_report($section_id,$acad_year);
        
        echo json_encode($data);
    }

    // student nc report
    public function student_non_complaince_report(){
        $data['section_list'] = $this->noncompliance_model->getClassNames();
        $data['admissionStatusArr'] = $this->settings->getSetting('admission_status');
        // echo '<pre>'; print_r($data['nc_report']); die();
    
        // echo '<pre>'; print_r($data['new']); die();
        $data['acad_years'] = $this->noncompliance_model->get_acad_years();
        $data['main_content'] = 'student_nc/student_non_complaince_report';    	
        $this->load->view('inc/template', $data);
    }

    public function get_student_by_class_section(){
      $classSectionId=$_POST['classSectionId'];
      $admission_status=$_POST['admission_status'];
      $result = $this->noncompliance_model->get_student_by_class_section($classSectionId,$admission_status);
      echo json_encode($result);
    }
    public function get_student_nc_data(){
        $students_id=$_POST['students_id'];
        $acad_year = $_POST['acad_yr'];
        $data['student_wise_nc_report'] = $this->noncompliance_model->get_student_nc_report($students_id,$acad_year);
        // echo $students_id;
        echo json_encode($data);
    }

    //NC Analytics
    public function non_compliance_analytics(){
        $data['main_content'] = 'student_nc/non_compliance_analytics';   
        $this->load->view('inc/template', $data);
    }

    
    public function get_nc_analytics_data(){
      $data['nc_categories'] = $this->noncompliance_model->get_nc_categories();
      $data['non_analytics'] =$this->noncompliance_model->get_nc_analytics_report();
      // echo '<pre>'; print_r($data['non_analytics']); die();
      echo json_encode($data);
    }


  // class wise nc analytics data
    public function class_wise_analytics(){
      $data['grade_list'] = $this->noncompliance_model->getClassNames();
      $data['class_list'] = $this->noncompliance_model->get_class_list();
      $data['acad_years'] = $this->noncompliance_model->get_acad_years();
      $data['main_content'] = 'student_nc/class_wise_analytics';   
      $this->load->view('inc/template', $data);
    }

    public function get_class_wise_nc_analytics(){
      $mode=$_POST['mode_name'];
      $class_id=$_POST["class_id"];
      $filter=$_POST["filter"];
      $acad_year=$_POST["acad_year"];
      $data['class_data']=$this->noncompliance_model->get_class_wise_nc_report($class_id,$filter,$mode,$acad_year);
      echo json_encode($data);
      // echo "<prep>"; print_r($_POST);
    }

    public function disable_particular_non_compliance(){
      // echo "<prep>"; print_r($_POST);die();
      $result=$this->noncompliance_model->disable_particular_non_compliance($_POST);
      echo json_encode($result);
    }
}
 ?>