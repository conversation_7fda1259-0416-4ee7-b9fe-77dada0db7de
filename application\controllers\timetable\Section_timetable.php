<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  16 March 2018
 *
 * Description: Controller for Timetable.
 *
 * Requirements: PHP5 or above
 *
 */

class Section_timetable extends CI_Controller {
	function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
        redirect('dashboard', 'refresh');
      }
      $this->load->model('timetable/timetable');
      $this->load->model('parent_model');
  }
  // staff wise only can see time table 

  //Landing function for get my timetable
  public function index(){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$studentData = $this->parent_model->getStudentDataById($studentId);
		$csId = $studentData->sectionId;
		$data['csid'] = $csId;
		$data['main_content']    = 'parent/time_table/index';
    $this->load->view('inc/template', $data);
  }

  //Landing function to print all timetables
  public function printSectionTimetables() {
    if (!$this->authorization->isAuthorized('TIMETABLE.SECTION_TIMETABLE_VIEW')) {
      redirect('dashboard', 'refresh');
    }

    $data['sectionList'] = $this->timetable->getSectionList();
    $data['school_admin'] = $this->authorization->isAuthorized('TIMETABLE.STAFF_TIMETABLE_VIEW');
    $data['main_content'] = 'timetable/section_timetable/print_section_tt';
    //$data['sectionListWithStaff'] = $this->timetable->getSectionListWithStaff();
    $this->load->view('inc/template_fee', $data);
  }

  //Landing function to display day section timetables
  public function displayDaySectionTimetables () {
    if (!$this->authorization->isAuthorized('TIMETABLE.SECTION_TIMETABLE_VIEW')) {
      redirect('dashboard', 'refresh');
    }

    $data['sectionList'] = $this->timetable->getSectionList();
    $data['main_content'] = 'timetable/section_timetable/day_timetable/day_section_tt';
    //$data['sectionListWithStaff'] = $this->timetable->getSectionListWithStaff();
    $this->load->view('inc/template_fee', $data);
  }

  public function getStaffTTById(){
   $staffId = $this->input->post('staffId');
   $result = $this->staff_tt->getStaffTTById($staffId);
   echo json_encode($result);
  }

  public function getFullTTForStaff(){
    $staffId = $this->input->post('staffId');
    $staffTT = $this->staff_tt->getFullTTForStaff($staffId);
    $staffObj = $this->staff_tt->getStaffDetails($staffId);
    $headerTT = $this->timetable->getPeriodHeader('40');
    echo json_encode(array("staffObj"=>$staffObj, "staffTT" =>$staffTT, "headerTT" => $headerTT));
   }
   
   public function getPeriodHeader(){
    $result = $this->timetable->getPeriodHeader('40');
    echo json_encode($result);
   }

   public function getStaffTTSubstituteStaffById(){
    $staffId = $_POST['staffId'];
    $weekDay = $_POST['weekDay'];
    $todayDate = date('Y-m-d');
    $weekDay = date("w", strtotime($todayDate));
    $date = new DateTime($todayDate);
    $headerTT = $this->timetable->getPeriodHeader('40');
    $weekNum = $date->format("W");
//    $staffAllo = $this->staff_tt->getStaffTTById($staffId);
    $staffAllo = $this->staff_tt->getFullTTForStaff($staffId, $weekDay);
    // $staffsub = $this->staff_tt->getCSStaffTTByWeekDaySusbstitute($staffId,$weekDay,$weekNum);
    $staffsub = $this->staff_tt->getCSStaffTTByWeekDaySusbstitute($staffId,$todayDate);
    $old_staffId = $this->staff_tt->getData_isAssignStafftoOthers($staffId,$todayDate);

    echo json_encode(array("headerTT"=>$headerTT, "staffAlloc" =>$staffAllo, "subPeriodIn" => $staffsub, "subPeriodOut" => $old_staffId ));
    //echo 'here';
  }
}