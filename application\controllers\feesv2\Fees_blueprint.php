<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  12 Jan 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_Master
 */
class Fees_blueprint extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isSuperAdmin()) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('feesv2/Fees_blueprint_model','fbm');
    $this->load->model('feesv2/Fees_inst_model','finst_model');
    $this->load->model('class_master_model');
    $this->load->library('fee_library');
    $this->config->load('form_elements');
  }

  public function index(){
    $data['blueprints'] = $this->fbm->getFeeBlueP<PERSON>();
    $receipt_book = $this->fbm->get_receipt_books();
    $data['recept_format'] = $this->fee_library->receipt_format_creation($receipt_book);
    
     //echo "<pre>"; print_r($data['branches']); die();
    $data['main_content'] = 'feesv2/blueprint/index';
    $this->load->view('inc/template', $data);
  }

  public function add_blueprint(){
    // $data['instTypes'] = $this->finst_model->getInstallmentTypes(false);
    //echo '<pre>';print_r($data['instTypes']);die();
    $data['accounts'] = $this->fbm->get_accounts_details();
    $data['main_content'] = 'feesv2/blueprint/add';
    $this->load->view('inc/template', $data);
  }

  public function add_blueprint_installment_type($insid){
    $data['feev2_blueprint_id'] = $insid;
    $data['instTypes'] = $this->fbm->getInstallmentTypes_name($insid);
    $data['main_content'] = 'feesv2/blueprint/blueprint_ins_type';
    $this->load->view('inc/template', $data);
  }

  public function submit_fee_blueprint(){
    // echo '<pre>';print_r($this->input->post());die();
    $result = $this->fbm->insert_blueprint();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Fee Blueprint created Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('feesv2/fees_blueprint');
  }

  public function submit_fee_blueprint_instament_type(){
    $result = $this->fbm->insert_blueprint_installment_type();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Fee Blueprint created Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('feesv2/fees_blueprint');
  }

  public function add_receipt_book()
  {
    $receipt_book = $this->fbm->get_receipt_books();
    $data['recept_format'] = $this->fee_library->receipt_format_creation($receipt_book);
    $data['main_content'] = 'feesv2/receipt_book/index';
    $this->load->view('inc/template', $data);
  }

  public function insert_receipts_book(){
    $result = $this->fbm->insert_receipts_book();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Fee Receipt algo created Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('feesv2/fees_blueprint/add_receipt_book');
  }

  public function delete_receipt_book($id)
  {
    $result = $this->fbm->delete_receipt_book($id);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Fee Receipt algo created Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('feesv2/fees_blueprint/add_receipt_book');
  }


  public function get_receipt_numberbyid(){
    $rbId = $_POST['rbId'];
    $result = $this->fbm->get_receipt_numberbyid($rbId);
    echo json_encode($result);
  }

  public function receipt_book_assign_blueprint(){
    $blueprintid = $_POST['blueprintid'];
    $receiptbook = $_POST['receiptbook'];
    echo $this->fbm->update_receipt_book_byfeev2_blueprintId($blueprintid, $receiptbook);
  }

  public function update_receipt_numberbyid(){
    $rbId = $_POST['rbId'];
    $running_number = $_POST['running_number'];
    echo $this->fbm->update_receipt_book_numberybId($rbId, $running_number);
  }

  public function receipt_html_format_assign_blueprint(){
    $blueprintid = $_POST['blueprintid'];
    $full_receipt_html = $_POST['full_receipt_html'];
    echo $this->fbm->update_receipt_html_format_feev2_blueprintId($blueprintid, $full_receipt_html);
  }
  
 public function receipt_html_display(){
  $blueprintId = $_POST['blueprintId'];
  $result = $this->fbm->receipt_html_display($blueprintId);
  // echo"<pre>"; print_r($result); die();
  echo json_encode($result);

 }

  public function terms_conditions_assign_blueprint(){
    $blueprintid = $_POST['blueprintid'];
    $terms_conditions = $_POST['terms_conditions'];
    echo $this->fbm->update_term_condition_feev2_blueprintId($blueprintid, $terms_conditions);
  }

  public function fine_amount_add($insid){
    $data['feev2_blueprint_id'] = $insid;
    $data['instTypes'] = $this->fbm->getInstallmentTypes_name($insid);
    $data['main_content'] = 'feesv2/blueprint/fine_discount';
    $this->load->view('inc/template', $data);

  }

  public function get_previous_fine_alog_amount(){
    $bpId = $_POST['bpId'];
    $result = $this->fbm->get_previous_fine_alog_amount($bpId);
    echo json_encode($result);
  }

  public function update_fine_discount_alogo(){
    $result = $this->fbm->update_fine_discount_alogo_data();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Fee Blueprint created Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('feesv2/fees_blueprint');
  }

  public function transportSelectionenable_disabled(){
    $stngId = $_POST['stngId'];
    $value = $_POST['value'];
    echo $this->fbm->update_transport_status_in_blueprint($stngId,$value); 
  }

  public function view_component($blueprintId){
    $data['view_component'] = $this->fbm->get_component_detailsbyId($blueprintId);
    $data['main_content'] = 'feesv2/blueprint/component';
    $this->load->view('inc/template', $data);
  }

  public function get_component_detailsby(){
    $comp = $_POST['blueid'];
    $result = $this->fbm->get_component_detailsby($comp);
    

    echo json_encode($result);
  
   }


  public function fee_blueprints_new(){
    $data['instTypes'] = $this->fbm->getInstallmentTypes_name();
    $data['branches'] = $this->class_master_model->branchesName();
    //echo "<pre>"; print_r($data['branches']); die();
    $data['main_content'] = 'feesv2/blueprint/fee_blueprints';
    $this->load->view('inc/template', $data);
  }

  public function get_blueprints(){
    $result = $this->fbm->get_fee_bluePrints();
    echo json_encode($result);
  }

  public function get_blueprintId(){
    $id = $_POST['id'];
    $result = $this->fbm->get_blueprintId($id);
    
    echo json_encode($result);
  }

  public function save_blueprints(){
    
    $result = $this->fbm->save_blueprints();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Fee Blueprint created Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('feesv2/fees_blueprint/fee_blueprints_new');
  }

  public function save_filters(){
    echo $this->fbm->save_filters();
    
  }

  public function save_payment_modes(){
    //echo '<pre>';print_r($this->input->post());die();
    $result = $this->fbm->save_payment_modes();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Fee Blueprint created Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('feesv2/fees_blueprint/fee_blueprints_new');
    //echo $this->fbm->save_payment_modes();

  }

  public function submit_fee_blueprint_instament_type_new(){
    echo $this->fbm->insert_blueprint_installment_type_new();
  }
  
  

  public function receipt_html_format_assign_blueprint_new(){
    $result = $this->fbm->update_receipt_html_format_feev2_blueprintId_new();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Added Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }

    redirect('feesv2/fees_blueprint/fee_blueprints_new');
  }

  public function terms_conditions_assign_blueprint_new(){
    $result = $this->fbm->update_term_condition_feev2_blueprintId_new();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Added Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }

    redirect('feesv2/fees_blueprint/fee_blueprints_new');
  }


  function term_condition_feev2_blueprint(){
    $blueprintId = $_POST['blueprintId'];
    $result =$this->fbm->term_condition_feev2_blueprint($blueprintId);
    echo json_encode($result);

  }

  public function update_fine_discount_alogo_new(){
    $result = $this->fbm->update_fine_discount_alogo_data_new();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Fee Blueprint created Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('feesv2/fees_blueprint/fee_blueprints_new');
  }

  public function update_fee_blueprints(){
    $input = $this->input->post();
    echo $this->fbm->update_fee_blueprints_by_id($input);
  }

  public function get_blueprints_details(){
    $blueprintId = $_POST['blueprintId'];
    $result =$this->fbm->get_blueprints_details_by_id($blueprintId);
    echo json_encode($result);
  }

  public function get_blueprints_installmentstype_data(){
    $bpinsId = $_POST['bpinsId'];
    $result =$this->fbm->get_blueprints_installmentstype_Id($bpinsId);
    echo json_encode($result);
  }
  
  

}