<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd"> 
<html LANG="en"> 
<head> 
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1"> 
<title>CSS Line Height Inheritance</title> 
<style type="text/css"> 
<!--
h1 {margin-top: 0; margin-bottom: 0;}
h2 {font-size: 125%; margin-top: 0;}
h3 {font-size: medium; margin-bottom: .5ex;}
div {border: 1px solid red;}
div div {border: 1px solid blue;}
a:link img, a:visited img { border-style: none;}
.admin {
  background-color: #E0E0FF;
  width: 15ex;
  margin: 2ex;
  padding: 1ex;
  text-align: center;
  font-size: .85em;
  border: 0;
  z-index: 10;
}
-->
</style> 
</head> 
<body> 
<h1>Use only &lt;number&gt; for line-height</h1> 
<h2>unless you like solving inexplicable inheritance problems or setting an explicit line-height on every element</h2> 
<h3>div {line-height: 1}; div div {font-size: 200%}</h3> 
<div style="line-height: 1;">The quick brown fox<br>jumps over the crazy
<div style="font-size: 200%;">The quick brown fox<br>jumps over the crazy</div></div> 
<h3>div {line-height: 1em}; div div {font-size: 200%}</h3> 
<div style="line-height: 1em;">The quick brown fox<br>jumps over the crazy
<div style="font-size: 200%;">The quick brown fox<br>jumps over the crazy</div></div> 
<h3>div {line-height: 100%}; div div {font-size: 200%}</h3> 
<div style="line-height: 100%;">The quick brown fox<br>jumps over the crazy
<div style="font-size: 200%;">The quick brown fox<br>jumps over the crazy</div></div> 
<div class="updir" style="border: 0; margin-top: 2ex;"> 
<p>When rendered according to the
<a href="http://www.w3.org/TR/CSS21/visudet.html#propdef-line-height">css 2.1 spec</a>,
the 200% text in the second two div divs, those for which line-height of the parent
are specified in em or %, will overlap, the child divs being 1/2 the height of their containing
divs; while the first div div will be 2/3 the height of its containing div,
and its text won't overlap. The overlapping text in the latter div divs is because the spec requires the calculated
line-height specified in em or % be inherited by the children. In contrast, it is &lt;number&gt; itself that is inherited by
the children, which allows the line-height specified to be applied in reference to the font-size of the child div instead of
the ancestor.</p> 
<hr> 
<a href="http://fm.no-ip.com/Auth/IE/line-heightIE.html">IE has an additional problem with line-height.</a> 
<hr> 
</div> 
 
<div class="admin"> 
<a href="http://validator.w3.org/check/referer"> 
<img src="http://www.w3.org/Icons/valid-html401" alt="Valid HTML 4.01!" height="31" width="88"></a> 
<br> 
Last Modified<br>2005.11.29<br>&#169; Felix Miata<br> 
<A HREF="http://fm.no-ip.com/Auth/">Felix's Home</A> 
</div> 
 
</body> 
</html> 