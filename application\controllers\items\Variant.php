<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Variant
 *
 * <AUTHOR>
 */
class Variant Extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->load->model('items/Variant_model');
    }

    public function index($id) {

        $data['itemName'] = $this->Variant_model->getItemObject($id);
        $data['select_Dropdown'] = $this->Variant_model->getDropdownbyItmeWise($data['itemName']->item_type);
        $data['variantList'] = $this->Variant_model->get_variants($id, $data['itemName']->item_type);
        //echo "<pre>"; print_r($data['variantList']); die();
        $data['itemId'] = $id;
        $data['main_content'] = 'items/variant';
        $this->load->view('inc/template', $data);
    }

    public function addVariant($itemId) {
        $result = $this->Variant_model->add_variant($itemId);

        if ($result == 1) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
        } else if ($result == 2) {
             $this->session->set_flashdata('flashSuccess', 'Variant Already Present');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
        }
        redirect('items/variant/index/'.$itemId);      
    }

    public function editVariant($id, $item_id, $type_id) {
        $data['select_Dropdowntype'] = $this->Variant_model->getTypeDetails($id);
        $data['getVariant'] = $this->Variant_model->get_variants($id, $type_id);
        $data['editVariant'] = $this->Variant_model->edit_variant($id, $item_id, $type_id);
        // echo $id ;echo $item_id ;echo $type_id;die();
        $data['main_content'] = 'Items/variant';
        $this->load->view('inc/template', $data);
    }

    public function updateVariant($id, $item_id) {

        $updateVariant = $this->Variant_model->update_variant($id);
        if ($updateVariant) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Updated.');
            redirect('items/variant/index/' . $item_id);
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('items/variant/index/' . $item_id);
        }
    }

    public function deleteVariant($item_id,$id) {
        
        $deleteVariant = $this->Variant_model->delete_variants($id, $item_id);
        if ($deleteVariant) {
            $this->session->set_flashdata('flashSuccess', 'Deleted Succssfully');
            redirect('items/variant/index/' . $item_id . '/' . $id);
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('items/variant/index/' . $item_id . '/' . $id);
        }
    }

}
