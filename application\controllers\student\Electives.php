<?php

class Electives extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->load->model('student/Electives_model');
        $this->load->model('subjects/Subjects_model', 'subject_model');
        $this->load->model('class_section');
    }

    public function groups()
    {
        $data['subjects'] = $this->subject_model->get_subjectList();
        // print_r($data['subjects']);
        // die();
        $data['main_content'] = 'student/electives/elective_groups';
        $this->load->view('inc/template', $data);
    }

    public function elective_master_group_list()
    {
        $data['groups'] = $this->Electives_model->getElectiveMasterGroups();
        echo json_encode($data);
    }

    public function add_or_update_group()
    {
        $result = $this->Electives_model->add_or_update_group($_POST);
        if (!$result) {
            $data['result'] = 0;
        } else {
            $data['result'] = 1;
        }
        echo json_encode($data);
    }

    public function index()
    {
        $data['sections'] = $this->class_section->getAllClassSections();
        // $data['subjects'] = $this->Electives_model->get_subject_list();
        $data['electives'] = $this->Electives_model->get_electives_list();
        $data['main_content'] = 'student/electives/elective_master';
        $this->load->view('inc/template', $data);
    }

    public function student_elective_report()
    {
        $data['sections'] = $this->class_section->getAllClassSections();
        $data['main_content'] = 'student/electives/student_elective_report';
        $this->load->view('inc/template', $data);
    }

    public function get_electives()
    {
        $section_id = $_POST['section_id'];
        $electives = $this->Electives_model->get_electives($section_id);
        echo json_encode($electives);
    }

    public function getStudents()
    {
        $section_id = $_POST['section_id'];
        $data['students'] = $this->Electives_model->get_student_electives($section_id);
        echo json_encode($data);
    }

    public function getStudents_elective_wise()
    {
        $section_id = $_POST['section_id'];
        $electives_id = $_POST['electives_id'];
        $data['students'] = $this->Electives_model->getStudents_elective_wise($section_id, $electives_id);
        echo json_encode($data);
    }

    public function assign_master_electives()
    {
        $electives = $_POST['electives'];
        $student_ids = $_POST['student_ids'];
        if ($_POST['check_assign_status'] == 'Assign') {
            $status = $this->Electives_model->assign_master_electives($electives, $student_ids);
        } else {
            $status = $this->Electives_model->unassign_master_electives($electives, $student_ids);
        }
        echo $status;
    }

    public function remove_master_elective()
    {
        $elective_id = $_POST['elective_id'];
        $status = $this->Electives_model->remove_master_elective($elective_id);
        echo $status;
    }

    public function remove_elective_master_group()
    {
        $elective_group_id = $_POST['elective_group_id'];
        $status = $this->Electives_model->remove_elective_master_group($elective_group_id);
        echo $status;
    }

    public function remove_elective_master_subject()
    {
        $status = $this->Electives_model->remove_elective_master_subject($_POST);
        echo $status;
    }

    public function get_subject_name()
    {
        $result = $this->Electives_model->get_subject_name($_POST);
        echo json_encode($result);
    }

}