<?php

class Inventory extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
        redirect('dashboard', 'refresh');
    }
    $this->load->library('cart');
    $this->load->model('product/Uniform_model');
    $this->load->library('filemanager');
    $this->load->model('parent_model');
    $this->load->model('email_model');
    $this->load->helper('email_helper');
  }


  public function index(){
    $data['sub_cat_list'] = $this->Uniform_model->get_sub_cat_list();
    $data['main_content'] = 'product/product_list';
    $this->load->view('inc/template', $data);
  }

  public function view_cart() {
    $subcategory_names = [];

    foreach ($this->cart->contents() as $item) {
        $subcategory = $this->Uniform_model->get_subcategory_name($item['id']);
        
        if ($subcategory) {
            $subcategory_names[] = [
                'id' => $subcategory->id,
                'name' => $subcategory->subcategory_name
            ];
        }
    }
    $data['subcategory_names']=$subcategory_names;
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'product/cart_view_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'product/cart_view_mobile';
    } else {
      $data['main_content'] = 'product/cart_view';
    }
    $this->load->view('inc/template', $data);
  }

  public function get_cart_items() {
    $output = '<table class="table table-bordered mb-3">
    <thead>
      <tr>
        <th class="font-weight-bold text-dark" style="font-size: 1.1rem;">Item Name</th>';
        if(! $this->settings->getSetting("inventory_shoppingcart_donot_show_price")){
        $output.=' <th class="text-success font-weight-bold" style="font-size: 1.1rem;">Price</th>';
        }
        $output.='</tr>
    </thead>
    <tbody>';
    foreach ($this->cart->contents() as $item) {
    $subcategory_name= $this->Uniform_model->get_subcategory_name($item['id']);
        $output .= '

    <tr>
      <td class="font-weight-bold text-dark" style="font-size: 1.1rem;">
        ' . htmlspecialchars($subcategory_name->subcategory_name) .' - '. htmlspecialchars($item['name']) . '
      </td>';
      if(! $this->settings->getSetting("inventory_shoppingcart_donot_show_price")){
        $output .= '<td class="text-success font-weight-bold" style="font-size: 1.1rem;">';
        $output .= '₹' . number_format($item['price'], 2);
      }
      $output .= '</td>
    </tr>
  ';
  $output .=`</tbody>
  </table>`;
    }
    if (empty($this->cart->contents())) {
        $output = '<p class="text-center">Your cart is empty!</p>';
    }
    echo $output;
  }

  public function load_products($page = 1) {
    $category_id = $this->input->get('category');
    // $subcategory_id = $this->input->get('subcategory_id');
    $limit = 12;
    $offset = ($page - 1) * $limit;
    $uniforms = $this->Uniform_model->get_products_paginated($limit, $offset, $category_id);
    foreach ($uniforms as $key => $value) {
      $value->image= $this->filemanager->getFilePath($value->product_image);
    }
    echo json_encode($uniforms);
  }

  public function add_to_cart($id) {
    $product = $this->Uniform_model->get_product_by_id($id);
    $qtyinput = $this->input->post('qtyinput'); // Use POST to retrieve data
    $custom_name = $this->input->post('custom_name'); // Retrieve custom_name from POST request

    if (!$product) {
        echo json_encode(['status' => 'error', 'message' => 'Product not found']);
        return;
    }
    $data = [
        'id'      => $product->id,
        'qty'     => $qtyinput,
        'price'   => $product->selling_price,
        'name'    => htmlentities($product->item_name, ENT_QUOTES, 'UTF-8'),
        'options' => [
            'image' => $product->product_image,
            'custom_name' => htmlentities($custom_name, ENT_QUOTES, 'UTF-8') // Store custom_name in cart options
        ]
    ];
    if ($this->cart->insert($data)) {
        echo json_encode([
            'status' => 'success',
            'cart_item_count' => $this->cart->total_items(),
        ]);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Failed to add item to cart']);
    }
  }


  public function update_cart() {
    $data = [
        'rowid' => $this->input->post('rowid'),
        'qty'   => $this->input->post('qty')
    ];

    if ($this->cart->update($data)) {
      $total_subtotal = 0;
        $row_subtotals = [];
      foreach ($this->cart->contents() as $item) {
        $total_subtotal += $item['subtotal'];
        $row_subtotals[$item['rowid']] = number_format($item['subtotal'], 2);
      }
        echo json_encode([
            'status' => 'success',
            'cart_item_count' => $this->cart->total_items(),
            'sub_total' => $row_subtotals,
            'grand_total' => number_format($this->cart->total(), 2),
        ]);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Failed to add item to cart']);
    }
  }

  public function remove_item($rowid) {
    $this->cart->remove($rowid);
    redirect('parent/inventory/view_cart');
  }

  public function get_added_cart_items_list(){
    
    echo "<pre>"; print_r($this->cart->contents()); die();
  }

//   public function get_price() {
//     $uniform_id = $this->input->post('uniform_id');
//     $brand_id = $this->input->post('brand_id');
//     $size_id = $this->input->post('size_id');
//     $color_id = $this->input->post('color_id');
   
//     // Fetch price based on selected options
//     $this->db->select('price');
//     $this->db->from('uniform_variants');
//     $this->db->where(compact('uniform_id', 'brand_id', 'size_id', 'color_id'));
//     $query = $this->db->get();
//     if ($query->num_rows() > 0) {
//         $price = $query->row()->price;
//     } else {
//         $price = 'Not available';  // or some default price
//     }

//     echo json_encode(['price' => $price]);
//   }

//   public function search() {
//     $query = $this->input->get('query');
//     $data['uniforms'] = $this->Uniform_model->search_uniforms($query);
//     $this->load->view('uniform_catalog', $data);
//   }

  public function submit_selected_items(){
    $cart_data=$this->cart->contents();
   
    $uniforms_id = $this->Uniform_model->submit_selected_items($cart_data);
    if($uniforms_id){
      $avatar_details = $this->Uniform_model->get_parent_name($this->authorization->getAvatarId());
      $email_data=$this->Uniform_model->get_order_data_email($uniforms_id);

      $order_table='
        <table style="border-collapse: collapse;">
          <thead>
            <tr>
              <th style="border:1px solid black;">Item Name</th>
              <th style="border:1px solid black;">Quantity</th>';
              if(! $this->settings->getSetting("inventory_shoppingcart_donot_show_price")){
                $order_table.=' <th style="border:1px solid black;">Price</th>';
              }
              $order_table.='
            </tr>
          </thead>
          <tbody>
      ';
      foreach ($email_data as $item) {
          $order_table.='
            <tr>
              <td style="border:1px solid black;">' . htmlspecialchars($item->item_name) .'</td>
              <td style="border:1px solid black; text-align:right">' . htmlspecialchars($item->quantity) .'</td>';
              if(! $this->settings->getSetting("inventory_shoppingcart_donot_show_price")){
                $order_table.='<td style="border:1px solid black;  text-align:right">' . htmlspecialchars($item->price) .'</td>';
              }
              $order_table.='
              </tr>
            ';
      }
      $order_table.='</tbody>
      </table>';
      // mail to staff 
      $email_template = $this->email_model->get_email_template_to_send_visitor('inventory new order email template staff');
    

      if(!empty($email_template)){
        $emailBody =  $email_template->content;
        $emailBody = str_replace('%%table%%',$order_table, $emailBody);
        $emailBody = str_replace('%%school_name%%',$this->settings->getSetting('school_name'), $emailBody);
        $emailBody = str_replace('%%parent_name%%',$avatar_details->parent_name, $emailBody);
        $emailIds = explode(',',  $email_template->members_email);
        $memberEmail = [];
        foreach ($emailIds as $key => $val) {
            array_push($memberEmail,$val);
        }
        sendEmail($emailBody, $email_template->email_subject, 0, $memberEmail, $email_template->registered_email);
      }
      // mail to parent
      $email_template1 = $this->email_model->get_email_template_to_send_visitor('inventory new order email template parent');
      if(!empty($email_template1)){
        $emailBody1 =  $email_template1->content;
        $emailBody1 = str_replace('%%parent%%',$avatar_details->parent_name, $emailBody1);
        $emailBody1 = str_replace('%%table%%',$order_table, $emailBody1);
        $emailBody1 = str_replace('%%school_name%%',$this->settings->getSetting('school_name'), $emailBody1);

        $memberEmail1[] = $avatar_details->email;
        sendEmail($emailBody1, $email_template1->email_subject, 0, $memberEmail1, $email_template1->registered_email);
      }
      $this->cart->destroy();
    }
    echo $uniforms_id;
  }

  public function reserved_order_confirmation_page(){
    $data['main_content'] = 'product/reserved_order_confirmation_page';
    $this->load->view('inc/template', $data);
  }

  public function reserved_orders_view(){
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'product/reserved_orders_view_mobile';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'product/reserved_orders_view_mobile';
    } else {
      $data['main_content'] = 'product/reserved_orders_view';
    }
    $this->load->view('inc/template', $data);
  }

  public function get_order_data(){
    $data = $this->Uniform_model->get_order_data();
    echo json_encode($data);
  }

  public function cancel_order(){
    $data = $this->Uniform_model->cancel_order($_POST);
    echo json_encode($data);
  }

  public function individual_order_details(){
    $data = $this->Uniform_model->individual_order_details($_POST['ppso_id']);
    echo json_encode($data);
  }

  public function get_individual_item_data(){
    $data = $this->Uniform_model->get_individual_item_data($_POST['pii_id']);
    echo json_encode($data);
  }

  public function order_page_mobile($id){
    $data['ppso_id'] = $id;
    $data['order_data'] = $this->Uniform_model->individual_order_details($id);
    $data['get_status'] = $this->Uniform_model->get_order_status($id);
    $data['main_content'] = 'product/order_page_mobile';
    $this->load->view('inc/template', $data);
  }

  public function detailed_item_view($product_subcategory_id){
    $data['product_subcategory_id'] = $product_subcategory_id;
    $data['images'] = $this->Uniform_model->get_all_images_of_subcatagory($product_subcategory_id);
    $data['sub_cat_name'] = $this->Uniform_model->get_name_of_subcatagory($product_subcategory_id);
    $data['description'] = $this->Uniform_model->get_description_of_subcatagory($product_subcategory_id);
    $data['items'] = $this->Uniform_model->get_items_of_subcatagory($product_subcategory_id);
    // echo "<pre>"; print_r($data['items']); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'product/detailed_item_view_mobile';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'product/detailed_item_view_mobile';
    } else {
      $data['main_content'] = 'product/detailed_item_view';
    }
    $this->load->view('inc/template', $data);
  }
}