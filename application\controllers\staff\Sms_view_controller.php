<?php
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of student_controller
 *
 * <AUTHOR>
 */
class Sms_view_controller extends CI_Controller {

    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if ($this->authorization->isModuleEnabled('SMS') && ($this->authorization->isAuthorized('SMS.SHOW_STAFF_SMS'))) {
        $this->load->model('staff/Staff_Model');
      } else {
        redirect('dashboard', 'refresh');
      }
    }

    public function sms(){
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $data['staffwise_sms'] =  $this->Staff_Model->get_smsStaffIdwise($staffId);
        //echo "<pre>"; print_r($data['staffwise_sms']); die();
        $data['main_content']    = 'staff/notification/sms/index';
        $this->load->view('inc/template', $data);
    }

    public function view_sms($id){
    
      $data['sms'] =  $this->Staff_Model->getSMSDetail($id);
      // echo "<pre>"; print_r($data['sms']); die();
      $data['main_content']    = 'staff/notification/sms/sms_view';
      $this->load->view('inc/template', $data);
    }
    
}