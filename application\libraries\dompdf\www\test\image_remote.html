<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
 "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<link rel="stylesheet" href="https://github.com/dompdf/dompdf/raw/master/www/style.css" type="text/css"/>
<style>
#content {
  margin: 0.25in;
  width: 100%;
}
</style>
</head>
<body>
<div id="content">
<h2>Remote CSS &amp; Image Test</h2>
<p>Note: DOMPDF_ENABLE_REMOTE must be enabled for this test to work.</p>

<h3>CSS: <a href="https://github.com/dompdf/dompdf/raw/master/www/style.css">https://github.com/dompdf/dompdf/raw/master/www/style.css</a></h3>
<p>
	Remote image with extension: <br />
	<img src="https://github.com/dompdf/dompdf/raw/master/www/test/images/dompdf_simple.png"/>
</p>
<p>
  Remote image without extension: <br />
  <img src="https://github.com/dompdf/dompdf/raw/master/www/test/images/no_extension"/>
</p>
<p>
  Remote image with unknown extension: <br />
  <img src="https://github.com/dompdf/dompdf/raw/master/www/test/images/unknown_extension.foo"/>
</p>
<p>
  Remote image with unknown extension and params: <br />
  <img src="https://github.com/dompdf/dompdf/raw/master/www/test/images/unknown_extension.foo?bar=baz"/>
</p>
<p>
  Remote image with unknown extension and advanced params: <br />
  <img src="https://github.com/dompdf/dompdf/raw/master/www/test/images/unknown_extension.foo?bar=baz&amp;test[]=test space&amp;test[]=test%20space"/>
</p>
</div>
</body>
</html>
