<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  19 June 2020
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_Master
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Refund_controller extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->load->model('feesv2/fees_collection_model');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_cohorts_model');
    $this->load->model('avatar');
    $this->load->library('Payment_settlement');
    $this->load->library('fee_library');
    $this->load->library('filemanager');
    $this->load->model('parent_model');
    $this->load->model('feesv2/refund_model');
    $this->config->load('form_elements');
  }

  public function get_refund_databy_txId(){
    // $feeId = $_POST['feeId'];
    $stdId = $_POST['stdId'];
    $transId = $_POST['transId'];
    $result = $this->refund_model->get_trans_details_byId($stdId, $transId);
    echo json_encode($result);
  }

  public function update_refund_fees(){
    $input = $this->input->post();
    $totalRefundAmount = 0;
    foreach ($input['comp_amount'] as $key => $val) {
      $totalRefundAmount += array_sum($val);
    }
    if($totalRefundAmount <= 0){
       $this->session->set_flashdata('flashError', 'Something went wrong');
    }else{
      $result = $this->refund_model->update_refund_fees($input);
      if ($result) {
        $ePayment_type = explode('_', $input['payment_type']);
        $payment_type = $ePayment_type[0];
      
        if($payment_type =='888'){
          $this->refund_model->initiate_online_payment_refund_amount($totalRefundAmount, $input['remarks'],$input['trans_id']);
        }
        if($input['notification_flag'] == 'yes'){
          $this->send_notification_for_parent_refund_amount($input['std_id'], $totalRefundAmount);
        }

        $this->session->set_flashdata('flashSuccess', ' Refund amount update successfully');
      }

    }
    if ($this->settings->getSetting('fee_collection_v1')) {
      redirect('feesv2/fees_collection/fee_student_blueprints_v1/'.$input['std_id']);
    }else{
      redirect('feesv2/fees_collection/history/'.$input['std_id'].'/'.'0');
    }
    

  }

  public function send_notification_for_parent_refund_amount($student_admission_id, $refund_amount){
    $stake_holder_id = [$student_admission_id];
  
    $message = " Amount of Rs. ".$refund_amount." Refunded successfully.". ucwords($this->settings->getSetting('school_short_name')).' -NXTSMS';
    $input_arr = array();
    $this->load->helper('texting_helper');
    $input_arr['student_ids'] = $stake_holder_id;
    $input_arr['mode'] = 'notification';
    $input_arr['source'] = 'Fee';
    $input_arr['send_to'] = 'Both';
    $input_arr['message'] = $message;
    return $response = sendText($input_arr);
  }

  public function refund_reports(){
    // $from_date = $this->input->post('from_date');
    // $to_date = $this->input->post('to_date');
    // $data['from_date'] = $from_date;
    // $data['to_date'] = $to_date;
    // $data['refunds'] = $this->refund_model->get_refund_details($from_date, $to_date);
    // echo "<pre>"; print_r($data['refunds']); die();
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['main_content'] = 'feesv2/reports/refund_reports_new';
    $this->load->view('inc/template', $data);
  }

  public function fee_refund_transaction($stdId, $bpId){
    $data['stdId'] = $stdId;
    $data['bpId'] = $bpId;
    $data['refund'] = $this->refund_model->get_trans_details_byId($stdId, $bpId);

    $data['main_content'] = 'feesv2/student/refund_fee_history';
    $this->load->view('inc/template', $data);
  }

  public function get_refund_reports(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $classId = $_POST['classId'];
    $fee_type = $_POST['fee_type'];
    $result = $this->refund_model->get_refund_trans_details($from_date, $to_date, $classId, $fee_type);
    echo json_encode($result);
  }

  public function check_status_of_refund_by_id(){
    $refund_id = $_POST['refund_id'];
    $result = $this->refund_model->check_status_of_refund_by_id_api($refund_id);
    echo json_encode($result);
  }

}