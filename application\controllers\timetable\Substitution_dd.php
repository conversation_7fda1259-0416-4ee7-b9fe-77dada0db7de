<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>chhipulusu
 *          <EMAIL>
 *
 * Created:  05 June 2018
 *
 * Description: Controller for Section Timetable view with Substitutions.
 *
 * Requirements: PHP5 or above
 *
 */

class Substitution_dd extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('SUBSTITUTION')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('timetable/timetable');
    $this->load->model('timetable/substitution_dd_model');
    $this->load->library('building_cache');
  }
  
  //Landing function for Substitution drag and drop
  public function index(){
    $data['sectionList'] = $this->timetable->getSectionList();
    $data['staffList'] = $this->substitution_dd_model->getStaffDetails();
    //echo '<pre>';print_r($data['staffList']);die();
    $data['school_admin'] = $this->authorization->isAuthorized('SCHOOL_ADMIN.MODULE');
    $data['main_content'] = 'substitution_dd/index';
    $this->load->view('inc/template', $data);
  }

  //Landing function for Substitution input
  public function subInput() {
    $data['main_content'] = 'substitution_dd/subinput/index';
    $this->load->view('inc/template', $data);
  }

  //Landing function for Substitution output
  public function subOutput() {
    $data['main_content'] = 'substitution_dd/suboutput/index';
    $this->load->view('inc/template_fee', $data);
  }

  public function subReport() {
    $data['staffNames'] = $this->substitution_dd_model->getActiveStaffList();
    $data['main_content'] = 'substitution_dd/subreport/index';
    $this->load->view('inc/template_fee', $data);
  }

  public function generateSubReport() {
    $fromDate = $_POST['from_date'];
    $toDate = $_POST['to_date'];
    $staffId = $_POST['staff_id'];

    $data['subReport'] = $this->substitution_dd_model->getSubReport($fromDate, $toDate, $staffId);

    echo json_encode($data);
  }

  public function getSubInput() {
    $subDate = $_POST['subDate'];

    $data['subInput'] = $this->substitution_dd_model->getSubInput($subDate);

    echo json_encode($data);
  }

  public function updateSubInputStatus() {
    $subDate = $_POST['subDate'];
    $staffId = $_POST['staffId'];
    $fromPeriod = $_POST['fromPeriod'];
    $toPeriod = $_POST['toPeriod'];
    $source = $_POST['source'];
    $sourceId = $_POST['sourceId'];
    $action = $_POST['action'];

    $result = $this->substitution_dd_model->updateSubInputStatus($subDate, $staffId, $fromPeriod, $toPeriod, $source, $sourceId, $action);

    echo json_encode($result);
  }

  public function getSubOutputForPrint() {
    $subDate = $_POST['subDate'];
    $includeUnassigned = $_POST['includeUnassigned'];

    $data['subOutput'] = $this->substitution_dd_model->getSubOutputForPrint($subDate, $includeUnassigned);

    echo json_encode($data);
  }

  public function getSubSectionList() {
    $subDate = $_POST['subDate'];
    $sectionList = $this->substitution_dd_model->getSubSectionList($subDate);
    echo json_encode($sectionList);
  }

  public function getStaffList() {
    $csStaffList = $_POST['csStaffList'];
    $staffTypeList = $_POST['staffTypeList'];
    //echo '<pre';print_r($csStaffList);die();

    $staffIdList = array ();
    foreach ($staffTypeList as $staffType) {
      switch ($staffType) {
        case 'classTeacher':
        $temp = $this->timetable->getClassTeacherList($csStaffList);
        $staffIdList = array_merge($staffIdList, $temp);
      break;
        case 'scholastic':
          $temp = $this->timetable->getStaffList($csStaffList, '1');
          $staffIdList = array_merge($staffIdList, $temp);
        break;
        case 'nonScholastic':
          $temp = $this->timetable->getStaffList($csStaffList, '0');
          $staffIdList = array_merge($staffIdList, $temp);
        break;
      }
    }
    $result = $this->timetable->getOrderedStaffList($staffIdList);

    $staffIdList = array();
    foreach ($result as $t) {
      $staffIdList[] = $t->id;
    }

    //echo '<pre>';print_r($staffIdList);die();
    echo json_encode($staffIdList);
  }

  public function getSectionTTWithSubs() {
    $csIdList = $_POST['csIdList'];
    $subDate = $_POST['subDate'];
    $weekDay = $_POST['weekDay'];

    $rDate = date('Y-m-d',strtotime($subDate));

    $ttArr = array ();
    foreach ($csIdList as $csId) {
      $temp = new stdClass();
      $temp->csId = $csId;
      $temp->csName = $this->timetable->getSectionName($csId);
      $temp->tta = $this->substitution_dd_model->getSectionTTByWeekDay($csId, $weekDay, $rDate);
      $ttArr[] = $temp;
    }
//    $data['pHeadArr'] = $this->timetable->getPeriodHeader($csId, 1);
    $data['pHeadArr'] = $this->timetable->getPeriodHeader($this->__getHeaderCSConfig(), 1);
    $data['TT'] = $ttArr;

    // echo '<pre>';print_r($data);die();
    echo json_encode($data);
  }

  public function getStaffTTWithSubs(){
    $staffIdList = $_POST['staffIdList'];
    $ttDate = $_POST['ttDate'];
    $date = date('Y-m-d',strtotime($ttDate));
    $weekDay = date("w", strtotime($date));
    if ($weekDay == 0) {
      echo json_encode(array("headerTT"=>'', "staffTTList"=> ''));
      return;
    }
    $headerTT = $this->timetable->getPeriodHeader($this->__getHeaderCSConfig(), 1);
    $staffTTList = array();
    foreach ($staffIdList as $staffId) {
      $temp = new stdClass();
      $temp->staffObj = $this->substitution_dd_model->getStaffDetails($staffId);
      $temp->tt = $this->substitution_dd_model->getStaffTT($staffId, $weekDay);
      $temp->subPeriodIn = $this->substitution_dd_model->getSubInData($staffId,$date);
      $temp->subPeriodOut = $this->substitution_dd_model->getSubOutData($staffId,$date);

      $staffTTList[] = $temp;
    }

    //echo '<pre>';print_r($staffTTList);die();

    echo json_encode(array("headerTT"=> $headerTT, "staffTTList" => $staffTTList, "weekDay" => $weekDay ));
  }

  public function moveLeavePeriod () {
    $toPeriodId = $_POST['toPeriodId'];
    $toPeriodsssId = $_POST['toPeriodsssId'];
    $toPseqeb = $_POST['toPseqeb'];
    $leavePeriodId = $_POST['leavePeriodId'];
    $weekDay = $_POST['weekDay'];
    $substituteId = $_POST['substituteId'];
    $leavepseqeb = $_POST['leavepseqeb'];
    $csName = $_POST['csName'];
    $csId = $_POST['csId'];
    $deepCheck = $_POST['deepCheck'];
    $subDate = $_POST['subDate'];
    $newPeriodDisplay = $_POST['newPeriodDisplay'];
    $newStaffIdJSON = $_POST['newStaffIdJSON'];

    //Find the availability of the selected staff
    //If available, enter the substitution as new staff
    //Then, insert the new substitution that needs to be done

    $toPeriodAvailable = $this->timetable->__areResourcesAvailable($toPeriodsssId, $weekDay, $leavepseqeb, $deepCheck, $csName);

    if ($toPeriodAvailable == 1) {
      //Available

      $this->db->trans_start();

      $result = $this->substitution_dd_model->addPeriodToLeavePeriod($substituteId, $toPeriodId, $newPeriodDisplay, $newStaffIdJSON);

      $result = $this->substitution_dd_model->insertPeriodSubstitute($toPeriodId, $subDate, $csId, $newStaffIdJSON, $toPseqeb,$weekDay);

      $this->db->trans_complete();

      echo $this->db->trans_status();
    } else if ($toPeriodAvailable == 0) {
      echo -1;
    } else {
      echo $toPeriodAvailable;
    }
  }

  public function toggleNeedsSubstitute() {
    $substituteId = $_POST['substituteId'];

    return $this->substitution_dd_model->toggleNeedsSubstitute($substituteId);
  }

  public function swapPeriods () {
    $subDate = $_POST['subDate'];
    $csId = $_POST['csId'];
    $fromPeriodId = $_POST['fromPeriodId'];
    $weekDay = $_POST['weekDay'];
    $frompseqeb = $_POST['frompseqeb'];

    $fromStaffIdJSON = $_POST['fromStaffIdJSON'];
    $fromPeriodDisplay = $_POST['fromPeriodDisplay'];

    $toPeriodId = $_POST['toPeriodId'];
    $toPseqeb = $_POST['toPseqeb'];
    $toStaffIdJSON = $_POST['toStaffIdJSON'];
    $toPeriodDisplay = $_POST['toPeriodDisplay'];

    $csName = $_POST['csName'];
    $deepCheck = $_POST['deepCheck'];

    //Find the availability of the selected staff
    //If available, enter the substitution as new staff
    //Then, insert the new substitution that needs to be done

    $swapPossible = $this->timetable->swapPeriodsCheckOnly($toPeriodId, $fromPeriodId, $csName, $deepCheck);

    if ($swapPossible->isAvailable == 1) {
      $this->db->trans_start();

      //Swap one period
      $this->substitution_dd_model->insertSwapPeriod($subDate, $csId, $fromPeriodId, $weekDay, $frompseqeb, $fromStaffIdJSON, $toPeriodId, $toPseqeb, $toStaffIdJSON, $toPeriodDisplay);

      //Swap the other period
      $this->substitution_dd_model->insertSwapPeriod($subDate, $csId, $toPeriodId, $weekDay, $toPseqeb, $toStaffIdJSON, $fromPeriodId, $frompseqeb, $fromStaffIdJSON, $fromPeriodDisplay);

      $this->db->trans_complete();

      echo $this->db->trans_status();
    } else {
      echo -1; //Swap cannot happen
    }
  }

  
  public function getDeallocatedPeriods() {
    $subDate = $_POST['subDate'];

    $data['periodData'] = $this->substitution_dd_model->getDeallocatedPeriods($subDate);
    $data['pHeadArr'] = $this->timetable->getPeriodHeader($this->__getHeaderCSConfig(), 1);

    echo json_encode($data);
  }

  public function deallocPeriod() {
    $subDate = $_POST['subDate'];
    $csId = $_POST['csId'];
    $periodId = $_POST['periodId'];
    $weekDay = $_POST['weekDay'];
    $pseqeb = $_POST['pseqeb'];
    $subStaffIdJSON = $_POST['subStaffIdJSON'];
    $subPeriodDisplay = $_POST['subPeriodDisplay'];
    //echo 'subPeriodDisplay:' . $subPeriodDisplay;die();

    // echo 'subDate: ' . $subDate;
    // echo 'csId: ' . $csId;
    // echo 'periodId: ' . $periodId;
    // echo 'weekDay: ' . $weekDay;
    // echo 'pseqeb: ' . $pseqeb;
    // echo 'subStaffIdJSON: ' . $subStaffIdJSON;die();

    $result = $this->substitution_dd_model->deallocPeriod($subDate, $csId, $periodId, $weekDay, $pseqeb, $subStaffIdJSON, $subPeriodDisplay);

    echo $result;
  }

  public function addStaffToLeavePeriod() {
    $newStaffId = $_POST['newStaffId'];
    $substituteId = $_POST['substituteId'];
    $substituteType = $_POST['substituteType'];

    $result = $this->substitution_dd_model->addStaffToLeavePeriod($newStaffId, $substituteId,$substituteType);

    if ($result)
      echo 1; //Success
    else
      echo -1; //Fail
  }

  public function deleteDeallocatedPeriod() {
    $substituteId = $_POST['substituteId'];

    $result = $this->substitution_dd_model->deleteDeallocatedPeriod($substituteId);
    
    if ($result == 0)
      echo -1; //Fail
    else
      echo $result; //Fail
  }

  public function deleteSubstitution() {
    $substituteId = $_POST['substituteId'];
    $subType = $_POST['subType'];
    $subDate = $_POST['subDate'];
    $newttaId = $_POST['newttaId'];

    $result = $this->substitution_dd_model->deleteSubstitution($substituteId, $subType, $subDate, $newttaId);

    echo $result;die();

    if ($result == 0)
      echo -1; //Fail
    else
      echo $result; //Fail
  }

  public function refreshSubstitutions() {
    $subDate = $_POST['subDate'];
    $weekDay = $_POST['weekDay'];

    $result = $this->substitution_dd_model->refreshSubstitutions($subDate,$weekDay);

    echo $result;
  }

  private function __getHeaderCSConfig () {
    $headerCS = $this->settings->getSetting('timetable_header_class_section_id');
    if ($headerCS == 0)
      $headerCS = 4;  //Initialize to a default class section.

    // $ttSetting = $this->settings->getSetting('timetable');
    // if (isset($ttSetting))
    //   $headerCS = $ttSetting['header_class_section_id'];
    // else
    //   $headerCS = 4; //Initialize to a default class section.

    return $headerCS;
  }
}