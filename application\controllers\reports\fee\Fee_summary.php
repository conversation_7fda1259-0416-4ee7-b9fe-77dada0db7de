<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  24 June 2018
 *
 * Description: Model class for Class Master table
 *
 * Requirements: PHP5 or above
 *
 */
class Fee_summary extends CI_Controller {
	function __construct() {
		  parent::__construct();
		if (!$this->ion_auth->logged_in()) {
        	redirect('auth/login', 'refresh');
      	}
  	 	$this->load->model('report/fee_report_model');
  	  $this->load->model('report/fee_summary_model');
      $this->load->model('report/transportation_report_model');
 	}
  
  public function searchFeeTypeClasswise(){
    $feeType = $this->input->post('fee_type');
    if ($feeType == 1) {
     $this->classWiseAggregate($feeType);
    }elseif($feeType == 2){
      $this->_TransportwiseAggregateClass($feeType);
    }elseif($feeType == 3){
      $this->_FacilitiesAggregateClass($feeType);
    }
  }

   public function classWiseAggregate($feeType = ''){
    $postData = $this->input->post();
    $filterMode = empty($postData);
    //Supported filters by daily report
    $reportFilters = [
      'class', 'admission_type', 'medium', 'board'
    ];
    //Supported filters by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedFilters = $feeConfig['supportedFilters'];
    $data['fee_type'] = $feeConfig['fee_type'];
    $finalFilters = array();
    foreach ($reportFilters as $rf) {
      foreach ($schoolSupportedFilters as $ssf) {
        if ($rf == $ssf) {
          $finalFilters[] = $rf;
          break;
        }
      }
    }
    $data['reportColumns'] = [
        ['name' => 'slNo', 'displayName' => 'Sl #'],
        ['name' => 'className','displayName' => 'Class'],
        // ['name' => 'feeAmount','displayName' => 'Amount to be collected'],
        ['name' => 'installment','displayName' => 'Installments'],
        ['name' => 'paidAmount','displayName' => 'Collected Amount'],
        ['name' => 'concession','displayName' => 'Concession'],
        ['name' => 'pos','displayName' => 'POS'],
        ['name' => 'fine','displayName' => 'Fine'],
        ['name' => 'medium','displayName' => 'Medium'],
        ['name' => 'discount','displayName' => 'Discount'],
        ['name' => 'balance','displayName' => 'Balance'],
      ];
  
    //Supported columns by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedColumns = $feeConfig['supportedColumns'];
    $finalColumns = array();
    foreach ($data['reportColumns'] as $rc) {
      foreach ($schoolSupportedColumns as $ssc) {
        if ($rc['name'] == $ssc) {
          $finalColumns[] = $rc;
          break;
        }
      }
    }

    $data['classes'] =  $this->fee_report_model->get_Allclasses();   
    $data['medium'] = $this->settings->getSetting('medium');
    $data['boarding'] = $this->settings->getSetting('boarding');
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['board'] = $this->settings->getSetting('board');
    $data['donors'] = $this->fee_report_model->get_Alldonors();
    $data['payment_mode'] = $feeConfig['allowed_payment_modes'];
    $data['feeCollectedBy'] = $this->fee_report_model->get_feeCollectedName();
    $data['finalFilters'] = $finalFilters;
    $data['SelectedFtype'] = $feeType; 
    $data['selectedColumns'] = $this->input->post('selectedColumns');
    $classId = $this->input->post('class_name');
    $data['finalColumns'] = $finalColumns;
    $data['finalColumnsJSON'] = json_encode($finalColumns);
    $data['main_content'] = 'reports/fees/class_wise_aggregate';
    $this->load->view('inc/template', $data);
  }

  public function generate_ClasswiseAggregate(){

    $selectedColumns = $this->input->post('selectedColumns'); 
    $finalColumns = json_decode($this->input->post('finalColumnsJSON'));
    $viewColumns = array ();
    if (!empty($selectedColumns)) {
      foreach ($selectedColumns as $sc) {
        foreach ($finalColumns as $rc) {
          if ($rc->name == $sc) {
            $viewColumns[] = $rc;
            break;
          }
        }
      }   
    } else {
      $viewColumns = $finalColumns;
    }
  
    $clsId = $this->input->post('clsId');
    $admission_type = $this->input->post('admission_type');
    $paymentModes = $this->input->post('paymentModes');
    $fee_type = $this->input->post('fee_type');
    $mediumId = $this->input->post('mediumId');
    $donorsId = $this->input->post('donorsId');
    //$boardingId = $this->input->post('boardingId');
    $rte_nrteId = $this->input->post('rte_nrteId');
    $created_byId = $this->input->post('created_byId');
    // $from_date = $this->input->post('from_date');
    // $to_date = $this->input->post('to_date');
    $medium = $this->settings->getSetting('medium');
    $feeConfig = $this->settings->getSetting('fees');
    $feeInstallments = $feeConfig['fee_installment'];
    if ($fee_type == 1) {
    $feeData = $this->fee_summary_model->get_classwise_feeDetails($clsId,$admission_type,$mediumId);

    $feeStrutureClasswise = $this->fee_summary_model->get_feeStructureAllClasses($clsId,$admission_type,$mediumId);
    foreach ($feeData as $clsId => &$data) {
      if (array_key_exists($clsId, $feeStrutureClasswise)) {
        $data['tfAmount'] = $feeStrutureClasswise[$clsId];
        $data['balance'] = $feeStrutureClasswise[$clsId] - $data['collceted_amount'] - $data['tConc'] - $data['dAmount'];
      }else{
        $data['tfAmount'] = 0;
        $data['balance'] = 0;
      }
    }
    $data['aggregate'] = $feeData;
   }elseif ($fee_type == 2) {
      $transport = $this->fee_report_model->get_Student_TransportationfeeDetails($clsId,$stopId,$routeId);
      foreach ($transport as $key => &$trans) {
        $trans->balAmount = $trans->feeAmount - $trans->collceted_amount;
      }
      $data['aggregate'] = $transport;
    }elseif ($fee_type == 3) {
      $facility = $this->fee_report_model->get_Student_FacilityfeeDetails($clsId,$itemId);
      $data['aggregate'] = $facility;
    }
  
    $template = "";
    //$template .="Total Number of Student <strong>". $TotalNumberStd."</strong>";
    $template.="<span class='help-block'>Collected amount includes discount, POS charge and fine amount. </span>";
    $template .="<table class='table table-condensed table-bordered'>";
    $template .= "<thead>";
    $template .= "<tr>";
    // $reqTemp  = $this->requireColumns($viewColumns, 'feeAmount');
    // if (!empty($reqTemp)) {
      $template .='<th>'.'Amount to be collected'.'</th>';
    // }

   
    $reqTemp  = $this->requireColumns($viewColumns, 'installment');
    if (!empty($reqTemp)) {
      foreach ($feeInstallments as $insName) {
       $template .='<th>'.$insName['name'].'</th>';
      }
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'concession');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'discount');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'pos');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'fine');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'balance');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $template .= "<tr>";
    $template .= "</thead>";
    $template .= "<tbody>";
    $template .= "<tr>";

    $insTAmt = array();
    $reqTemp  = $this->requireColumns($viewColumns, 'installment');
    if (!empty($reqTemp)) {
      foreach ($feeInstallments as $ColumnsName) {
        if (!array_key_exists($ColumnsName['name'], $insTAmt)) {
          $insTAmt[$ColumnsName['name']]= 0;
        }
        foreach ($data['aggregate'] as $result) {
          if (!empty($result['installment'])) {
            foreach ($result['installment'] as $columns => $value) {
              if ($ColumnsName['name'] == $columns) {
                $insTAmt[$ColumnsName['name']] +=$value;
              }
            }
          }
        }
      }
    }

    $tFeeAmount = 0;
    $padiAmount = 0;
    $totalConcession = 0;
    $totalBalance = 0;
    $totalFineAmount = 0;
    $totalDiscount = 0;
    $totalCardAmount = 0;
    $installments = 0;

    $cAmount ="";
    foreach ($data['aggregate'] as  $result){
      if (!empty($result)) {
        // $reqTemp  = $this->requireColumns($viewColumns, 'feeAmount');
        // if (!empty($reqTemp)) {
          $tFeeAmount += $result['tfAmount'];
        // }
       
        $reqTemp  = $this->requireColumns($viewColumns, 'concession');
        if (!empty($reqTemp)) {
          $totalConcession +=$result['tConc'];
        }
        
        $reqTemp  = $this->requireColumns($viewColumns, 'discount');
        if (!empty($reqTemp)) {
          $totalDiscount += $result['dAmount'];
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'pos');
        if (!empty($reqTemp)) {
          $totalCardAmount +=$result['cardAmount'];
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'fine');
        if (!empty($reqTemp)) {
          $totalFineAmount += $result['fineAmt'];
        }
      
        $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
        if (!empty($reqTemp)) {
          $padiAmount += $result['collceted_amount'];
        }

        $totalBalance = ($tFeeAmount + $totalCardAmount + $totalFineAmount)  - $padiAmount - $totalConcession - $totalDiscount;
      }
    }
      // $reqTemp  = $this->requireColumns($viewColumns, 'feeAmount');
      // if (!empty($reqTemp)) {
        $template .='<td>'.$tFeeAmount.'</td>';
      // }

      $reqTemp  = $this->requireColumns($viewColumns, 'installment');
      if (!empty($reqTemp)) {
        foreach ($insTAmt as $insName => $amt) {
         $template .='<td>'.$amt.'</td>';
        }   
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'concession');
      if (!empty($reqTemp)) {
        $template .='<td>'.$totalConcession.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'discount');
      if (!empty($reqTemp)) {
        $template .='<td>'.$totalDiscount.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'pos');
      if (!empty($reqTemp)) {
        $template .='<td>'.$totalCardAmount.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'fine');
      if (!empty($reqTemp)) {
        $template .='<td>'.$totalFineAmount.'</td>';
      }

     
      $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
      if (!empty($reqTemp)) {
          $template .='<td>'.$padiAmount.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'balance');
      if (!empty($reqTemp)) {
        $template .='<td>'.$totalBalance.'</td>';
      }
           
    $template .= "<tr>";
    $template .= "</tbody>";
    $template .="</table>";

    
    $template .="<table class='table table-bordered'>";
   
    $template .="<thead>";
    $template .="<tr>";
    
    $reqTemp  = $this->requireColumns($viewColumns, 'slNo');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }
    $reqTemp  = $this->requireColumns($viewColumns, 'className');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'stop');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'items');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'medium');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    // $reqTemp  = $this->requireColumns($viewColumns, 'feeAmount');
    // if (!empty($reqTemp)) {
      $template .='<th>'.'Amount to be collected'.'</th>';
    // }

    $reqTemp  = $this->requireColumns($viewColumns, 'installment');
    if (!empty($reqTemp)) {
      foreach ($feeInstallments as $ColumnsName) {
        $template .='<th>'.$ColumnsName['name'].'</th>';
      }
    }

   
    $reqTemp  = $this->requireColumns($viewColumns, 'concession');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'discount');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'pos');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'fine');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'balance');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $template .="</tr>";
    $template .="</thead>";
    $template .="<tbody>";
    $i = 1;
    foreach ($data['aggregate'] as $result){
     // echo "<pre>"; print_r($result); die();
      $template .="<tr>";

      $reqTemp  = $this->requireColumns($viewColumns, 'slNo');
        if (!empty($reqTemp)) {
        $template .='<td>'.$i++.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'className');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['clsName'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'stop');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['stopName'].'</td>';
      }
      
      $reqTemp  = $this->requireColumns($viewColumns, 'items');
        if (!empty($reqTemp)) {
          $template .='<td>';
          foreach ($result['feeFacility'] as $key => $itemsName) {
           $template .=$itemsName['item_name'].', ';
          }
          $template .= '</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'medium');
        if (!empty($reqTemp)) {
        foreach ($medium as $key => $med) {
          if ($result['medium'] == $key) { 
          $template .='<td>'.$med.'</td>';
          }
        }
      }

      // $reqTemp  = $this->requireColumns($viewColumns, 'feeAmount');
      //   if (!empty($reqTemp)) {
        $template .='<td>'.$result['tfAmount'].'</td>';
      // }

      $reqTemp  = $this->requireColumns($viewColumns, 'installment');
      if (!empty($reqTemp)) {
        foreach ($feeInstallments as $ColumnsName) {
          if (!empty($result['installment'])) {
            foreach ($result['installment'] as $columns => $value) {
              if ($ColumnsName['name'] == $columns) {
                $template .='<td>'.$value.'</td>';
              }
            }
          }else{
            $template .='<td>'.'0'.'</td>';
          }
        }
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'concession');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['tConc'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'discount');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['dAmount'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'pos');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['cardAmount'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'fine');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['fineAmt'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['collceted_amount'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'balance');
      if (!empty($reqTemp)) {
        $totalBalance = ($result['tfAmount'] + $result['cardAmount'] + $result['fineAmt'])  -  $result['collceted_amount']-$result['tConc']-$result['dAmount'];
        $template .='<td>'.$totalBalance.'</td>';
      }
    }

    $template .='</tr>';
    $template .='</tbody>';
    $template .="</table";  
    print($template);
  }

  private function requireColumns($filterArray, $filter) {
    foreach ($filterArray as $f) {
      if ($f->name == $filter)
        return $f;
    }
    return array();
  }


  private function _TransportwiseAggregateClass($feeType){
    $postData = $this->input->post();
    $filterMode = empty($postData);
    //Supported filters by daily report
    $reportFilters = [
      'class', 'stop', 'route'
    ];
    //Supported filters by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedFilters = $feeConfig['supportedFilters'];
    $data['fee_type'] = $feeConfig['fee_type'];
    $finalFilters = array();
    foreach ($reportFilters as $rf) {
      foreach ($schoolSupportedFilters as $ssf) {
        if ($rf == $ssf) {
          $finalFilters[] = $rf;
          break;
        }
      }
    }
    $data['reportColumns'] = [
        ['name' => 'slNo', 'displayName' => 'Sl #'],
        ['name' => 'stdName','displayName' => 'Name'],
        ['name' => 'className','displayName' => 'Class'],
        ['name' => 'stop','displayName' => 'Stop'],
        ['name' => 'route','displayName' => 'Route'],
        ['name' => 'feeAmount','displayName' => 'Fee Amount'],
        ['name' => 'paidAmount','displayName' => 'Paid'],
        ['name' => 'balance','displayName' => 'Balance'],
      ];
    //Supported columns by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedColumns = $feeConfig['supportedColumns'];
    $finalColumns = array();
    foreach ($data['reportColumns'] as $rc) {
      foreach ($schoolSupportedColumns as $ssc) {
        if ($rc['name'] == $ssc) {
          $finalColumns[] = $rc;
          break;
        }
      }
    }
    $data['classes'] =  $this->fee_report_model->get_Allclasses();   
    $data['stops'] = $this->transportation_report_model->getAllStops();
    $data['routes'] = $this->transportation_report_model->getAllRouts();
    $data['finalFilters'] = $finalFilters;
    $data['selectedColumns'] = $this->input->post('selectedColumns');
    $classId = $this->input->post('class_name');
    $data['finalColumns'] = $finalColumns;
    $data['SelectedFtype'] = $feeType;
    $data['finalColumnsJSON'] = json_encode($finalColumns);
    $data['main_content'] = 'reports/fees/class_wise_aggregate';
    $this->load->view('inc/template', $data);
  }


  private function _FacilitiesAggregateClass($feeType){
    $postData = $this->input->post();
    $filterMode = empty($postData);
    //Supported filters by daily report
    $reportFilters = [
      'class', 'items'
    ];
    //Supported filters by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedFilters = $feeConfig['supportedFilters'];
    $data['fee_type'] = $feeConfig['fee_type'];
    $finalFilters = array();
    foreach ($reportFilters as $rf) {
      foreach ($schoolSupportedFilters as $ssf) {
        if ($rf == $ssf) {
          $finalFilters[] = $rf;
          break;
        }
      }
    }
    $data['reportColumns'] = [
        ['name' => 'slNo', 'displayName' => 'Sl #'],
        ['name' => 'stdName','displayName' => 'Name'],
        ['name' => 'className','displayName' => 'Class'],
        ['name' => 'items','displayName' => 'Item'],
        ['name' => 'paidAmount','displayName' => 'Paid'],
      ];
    //Supported columns by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedColumns = $feeConfig['supportedColumns'];
    $finalColumns = array();
    foreach ($data['reportColumns'] as $rc) {
      foreach ($schoolSupportedColumns as $ssc) {
        if ($rc['name'] == $ssc) {
          $finalColumns[] = $rc;
          break;
        }
      }
    }
    $data['classes'] =  $this->fee_report_model->get_Allclasses();   
    $data['items'] =  $this->fee_report_model->getAllItems();
    $data['finalFilters'] = $finalFilters;
    $data['selectedColumns'] = $this->input->post('selectedColumns');
    $classId = $this->input->post('class_name');
    $data['finalColumns'] = $finalColumns;
    $data['SelectedFtype'] = $feeType;
    $data['finalColumnsJSON'] = json_encode($finalColumns);
    $data['main_content'] = 'reports/fees/class_wise_aggregate';
    $this->load->view('inc/template', $data);
  }

}  