<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  16 January 2022
 *
 * Description: Controller for Staff Tasks Basket.
 *
 * Requirements: PHP5 or above
 *
 */

class Menu extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STAFF_TASKS_BASKET')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STAFF_TASKS_BASKET.MODULE')) {
      redirect('dashboard', 'refresh');
    }
  }

  //Landing function to show non-compliance menu
  public function index() {
    $site_url = site_url();

    $data['tiles'] = array(
      // [
      //   'title' => 'My Tasks',
      //   'sub_title' => 'View and add Tasks',
      //   'icon' => 'svg_icons/subjects.svg',
      //   'url' => $site_url.'stb/staff_tasks/mytasks',
      //   'permission' =>  $this->authorization->isSuperAdmin()
      // ],
      [
        'title' => 'My Tasks',
        'sub_title' => 'View and add Tasks (Kanban and Calendar)',
        'icon' => 'svg_icons/assestcategory.svg',
        'url' => $site_url.'stb/staff_mytasksv2/mytasks_v2',
        'permission' =>  $this->authorization->isAuthorized('STAFF_TASKS_BASKET.MY_TASKS'),
      ],
      // [
      //   'title' => 'My Tasks',
      //   'sub_title' => 'View and add Tasks (Simple Calendar View)',
      //   'icon' => 'svg_icons/assestcategory.svg',
      //   'url' => $site_url.'stb/staff_mytasksv2/mytasks_simple_calendar_view',
      //   'permission' =>  $this->authorization->isAuthorized('STAFF_TASKS_BASKET.MY_TASKS_SIMPLE_CALENDAR_VIEW'),
      // ],
      // [
      //   'title' => 'Delegated Tasks',
      //   'sub_title' => 'View Delegated Tasks',
      //   'icon' => 'svg_icons/assestcategory.svg',
      //   'url' => $site_url.'stb/staff_mytasksv2/delegated_tasks',
      //   'permission' =>  $this->authorization->isAuthorized('STAFF_TASKS_BASKET.MODULE')
      // ],
      [
        'title' => 'My Projects',
        'sub_title' => 'View and add Staff Tasks',
        'icon' => 'svg_icons/assestcategory.svg',
        'url' => $site_url.'stb/staff_tasks/index',
        'permission' =>  $this->authorization->isAuthorized('STAFF_TASKS_BASKET.MY_PROJECTS')
      ],
      // [
      //   'title' => 'My Notes',
      //   'sub_title' => 'View and add Staff Notes',
      //   'icon' => 'svg_icons/assestcategory.svg',
      //   'url' => $site_url.'stb/staff_mynotes/my_notes',
      //   'permission' =>  $this->authorization->isAuthorized('STAFF_TASKS_BASKET.MY_NOTES')
      // ],
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
      [
        'title' => 'Task Analytics',
        'sub_title' => 'Staff task analytics',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'stb/reports/task_analytics',
        'permission' =>  $this->authorization->isAuthorized('STAFF_TASKS_BASKET.REPORTS')
      ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['adminstration'] = array(
      [
        'title' => 'Manage Teams',
        'sub_title' => 'Staff Teams for Projects / Tasks',
        'icon' => 'svg_icons/staff.svg',
        'url' => $site_url.'stb/staff_teams/index',
        'permission' =>  $this->authorization->isAuthorized('STAFF_TASKS_BASKET.MANAGE_TEAMS')
      ],
      [
        'title' => 'Manage Task Types (Admin)',
        'sub_title' => 'Task Type for Individual Tasks',
        'icon' => 'svg_icons/staff.svg',
        'url' => $site_url.'stb/staff_task_types/admin_task_types',
        'permission' =>  $this->authorization->isSuperAdmin(),
      ],
      [
        'title' => 'Manage Task Types',
        'sub_title' => 'Task Type for Individual Tasks',
        'icon' => 'svg_icons/staff.svg',
        'url' => $site_url.'stb/staff_task_types/staff_task_types',
        'permission' =>  $this->authorization->isAuthorized('STAFF_TASKS_BASKET.MANAGE_STAFF_TASK_TYPES')
      ],
    );
    $data['adminstration'] = checkTilePermissions($data['adminstration']);

    $data['main_content']    = 'stb/menu';
    $this->load->view('inc/template', $data);
  }

}