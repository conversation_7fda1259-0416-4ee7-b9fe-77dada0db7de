<?php

/*
* To change this license header, choose License Headers in Project Properties.
* To change this template file, choose Tools | Templates
* and open the template in the editor.
*/

/**
* Description of Attendance_controller
*
* <AUTHOR>
*/
class Action_controller extends CI_Controller {

  function __construct() {
    parent::__construct();
    
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STAFF_MASTER')) {
      redirect('dashboard', 'refresh');
    }    

    date_default_timezone_set('Asia/Kolkata');

    $this->yearId = $this->acad_year->getAcadYearId();        
    $this->load->model('student/Student_Model','student');
    $this->load->model('attendance/Attendance_model','attendanceModel');
    $this->load->model('attendance/Attendance_action_model','actionModel');
  }

  private function _actionType($type, $isdisplay = false) {

    if($type == 0) {
      return ($isdisplay) ? 'Emergency Exit' : 'outGoing';
    } elseif ($type == 1) {
      return ($isdisplay) ? 'Late-comer' : 'lateComer';
    } elseif ($type == 2) {
      return ($isdisplay) ? 'Health Care' : 'healthCare';
    } else {
      return ($isdisplay) ? 'Fresh Student Entry' : 'freshEntry';
    }
  }

  public function filterIndex($type){
    $fromDate = $this->input->post('fromDate');
    $toDate = $this->input->post('toDate');
    $data['fromDate'] = date('Y-m-d', strtotime($this->input->post('fromDate')));
    $data['toDate'] = date('Y-m-d', strtotime($this->input->post('toDate')));

    $action = $this->_actionType($type);
    $data['type'] = $type;

    $classSectionId=$this->input->post('classsection');

    if(isset($classSectionId)){
      $data['secId'] = $classSectionId;
    }else{
      $data['secId'] = -1;
    }

    $data['secName'] = $this->attendanceModel->getSecName($data['secId']);
    $data['student'] = $this->actionModel->filterResults($type, $data['secId'], $data['fromDate'], $data['toDate']);
    $data['class_section'] = $this->attendanceModel->getAllClassandSection();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance/'.$action.'/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'attendance/'.$action.'/index_mobile';
    }else{
      $data['main_content'] = 'attendance/'.$action.'/index';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function studentActions($type) {

    $data['student'] = $this->actionModel->getStudentAction($type);
    $data['class_section'] = $this->attendanceModel->getAllClassandSection();
    $action = $this->_actionType($type);
    $data['type'] = $type;
    $data['fromDate'] =  date('Y-m-d');
    $data['toDate'] = date('Y-m-d'); 
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance/'.$action.'/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'attendance/'.$action.'/index_mobile';
    }else{
      $data['main_content'] = 'attendance/'.$action.'/index';    	
    }    
    $this->load->view('inc/template', $data);
  }

  public function addStudentActions($type) {
    $show_all_sections = $this->authorization->isAuthorized('STUDENT_ATTENDANCE.SHOW_ALL_SECTIONS');
    $data['getclassinfo'] = $this->student->getclass();
    $data['class_section'] = $this->attendanceModel->getAllClassandSection($show_all_sections);
    $action = $this->_actionType($type);
    $data['type'] = $type;
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance/'.$action.'/add_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'attendance/'.$action.'/add_mobile';
    }else{
      $data['main_content'] = 'attendance/'.$action.'/add';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function addNewLateComer($csid) {
    $data['getclassinfo'] = $this->student->getclass();
    $data['class_section'] = $this->attendanceModel->getAllClassandSection();
    $action = $this->_actionType(1);
    $data['type'] = 1;
    $data['current_section'] = $csid;
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'attendance/'.$action.'/add_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'attendance/'.$action.'/add_mobile';
    }else{
      $data['main_content'] = 'attendance/'.$action.'/add';     	
    }    
    // $data['main_content'] = 'attendance/'.$action.'/add';

    $this->load->view('inc/template', $data);
  }

  public function getPickedByInfo() {

    $studentId = $_POST['studentId'];
    $pickupby = $_POST['pickedBy'];

    $pickedByInfo = $this->attendanceModel->getStudentPickedbyInfo($studentId,$pickupby);

    echo json_encode($pickedByInfo);

  }

  public function saveStudentActions() {      

    $input = $this->input->post(); 
    $action = $this->_actionType($input['type']);
    if($input['type'] == 0) {
      $data['result'] = $this->attendanceModel->getStudentAttendance($input['studentId']);
      $data['emergency_exit_visitor'] = $this->settings->getSetting('emergency_exit_visitor');
      if(empty($data['result'])) {
        $this->session->set_flashdata('flashError', 'Attendance needs to be taken before marking this entry');
        redirect('attendance/'.$input['type'].'/action');
      } else {
        $data['input'] = json_encode($input);
        $data['pickupby'] = $input['pickupby'];
        $data['picked_info'] = $this->attendanceModel->getStudentPickedbyInfo($input['studentId'],$input['pickupby']); 

        $data['main_content'] = 'attendance/'.$action.'/studentAttendance';
        $this->load->view('inc/template', $data);
      }
    } elseif ($input['type'] == 3) {
      $data['result'] = $this->attendanceModel->getStudentAttendance($input['studentId']);
      if(empty($data['result'])) {
        $this->session->set_flashdata('flashError', 'Attendance needs to be taken before marking this entry');
        redirect('attendance/'.$input['type'].'/action');
      } else {
        $data['input'] = json_encode($input);     
        $data['student_info'] = $this->attendanceModel->getStudentPickedbyInfo($input['studentId']);

        $data['main_content'] = 'attendance/'.$action.'/studentAttendance';
        $this->load->view('inc/template', $data);
      }
    } else {

      if($input['type'] == 1) {
        $studentIds = $input['studentIds'];
        foreach ($studentIds as $key => $value) {
          if(!empty($value)) {
            $input['studentId'] = $value;
            $insert_status = $this->actionModel->insertStudentActions($input);
          }
        }
      } 

      if($input['type'] == 2) {
        $insert_status = $this->actionModel->insertStudentActions($input);
      }
     
      $display_type = $this->_actionType($input['type'],true);

      if ($insert_status['status']) {
        if($input['type'] != 1) 
          $this->session->set_flashdata('flashSuccess', $display_type.' Student Successfully Inserted.');
        else
          $this->session->set_flashdata('flashSuccess', 'Student(s) marked late-comer successfully');
      } else {
        $this->session->set_flashdata('flashError', $insert_status['msg']);
      }
      if($input['type'] == 1){
        $csid = $_POST['classsection'] + 1;
        redirect('attendance/Action_controller/addNewLateComer/'.$csid);
      }else{
        redirect('attendance/'.$input['type'].'/action');
      }
    }
  }

  public function submitOutGoingAttendance() {
    $input = json_decode($this->input->post('input'),true);
    $all_input = $this->input->post();

    if(isset($all_input['picked_id'])) {
      $input['pickupid'] = $all_input['picked_id'];
    }
    $insert_status = $this->actionModel->insertStudentActions($input);
    $display_type = $this->_actionType($input['type'],true);
    $student_admission_id=$this->actionModel->getStudentAdmissionId($input['studentId']);
    if ($insert_status['status']) {

      if($this->settings->getSetting('emergency_exit_student_notification')){
        $this->load->helper('texting_helper');
        $input_arr = array();
        $input_arr['student_ids'] = [$student_admission_id];
        $input_arr['mode'] = 'notification_sms';
        $input_arr['source'] = 'Student Exit';
        $input_arr['message'] = "Your kid left early from school today.";
        $input_arr['title'] = 'Emergency Exit';
        sendText($input_arr);
      }

      unset($all_input['input']);
      $this->attendanceModel->updateAttendenceDataById($all_input, $input['type']);
      $this->session->set_flashdata('flashSuccess', 'Student(s) marked as Emergency exit successfully');
    } else {
      $this->session->set_flashdata('flashError', $insert_status['msg']);
    }
    redirect('attendance/'.$input['type'].'/action');
  }

  public function submitFreshEntryAttendance() {

    $input = json_decode($this->input->post('input'),true);
    $all_input = $this->input->post();

    $insert_status = $this->actionModel->insertStudentActions($input);
    $display_type = $this->_actionType($input['type'],true);

    if ($insert_status['status']) {
      unset($all_input['input']);
      $this->attendanceModel->updateAttendenceDataById($all_input, $input['type']);
      $this->session->set_flashdata('flashSuccess', $display_type.' Student Successfully Inserted.');
    } else {
      $this->session->set_flashdata('flashError', $insert_status['msg']);
    }

    redirect('attendance/'.$input['type'].'/action');
  }

  public function editStudentActions() {
    $input = $this->input->post('id');
    echo $this->actionModel->editStudentActions($input);
  }
}
