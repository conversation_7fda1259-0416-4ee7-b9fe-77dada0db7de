<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xml:lang="en" xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />

<title>Printed document</title>

<style type="text/css">

body {
	font-family: sans-serif;
	font-size: 10pt;
}

/* These inline styles will always be active even if an alternate   stylesheet is selected with the stylesheet swticher */

.example {
  width: 400px;
	page-break-after: always;
	margin-bottom: 10em;
}

.div-before, .div-after {
  background-color: #88d;
  color: #000;
}

.div-1 {
  background-color: #000;
  color: #fff;
}

.div-1-padding {
  padding: 10px;
}

.div-1a {
  background-color: #d33;
  color: #fff;
}

.div-1b {
  background-color: #3d3;
  color: #fff;
}

.div-1c {
  background-color: #33d;
  color: #fff;
}

.example div p {
  margin: 0 .25em;
  padding: .25em 0;
}



#example-1 .div-1 {
 position: static;
}

#example-2 .div-1 {
	position: relative;
	top:20px;
	left:-40px;
}

#example-3 .div-1a {
 position:absolute;
 top:0;
 right:0;
 width:200px;
}

#example-4 .div-1 {
 position:relative;
}
#example-4 .div-1a {
 position:absolute;
 top:0;
 right:0;
 width:200px;
}

#example-5 .div-1 {
 position:relative;
}
#example-5 .div-1a {
 position:absolute;
 top:0;
 right:0;
 width:200px;
}
#example-5 .div-1b {
 position:absolute;
 top:0;
 left:0;
 width:200px;
}

#example-6 .div-1 {
 position:relative;
 height:250px;
}
#example-6 .div-1a {
 position:absolute;
 top:0;
 right:0;
 width:200px;
}
#example-6 .div-1b {
 position:absolute;
 top:0;
 left:0;
 width:200px;
}

#example-7 .div-1a {
 float:left;
 width:200px;
}

#example-8 .div-1a {
 float:left;
 width:150px;
}
#example-8 .div-1b {
 float:left;
 width:150px;
}

#example-9 .div-1a {
 float:left;
 width:190px;
}
#example-9 .div-1b {
 float:left;
 width:190px;
}
#example-9 .div-1c {
 clear:both;
}
</style>
  
</head>

<body>
	
<h3>Examples from <br />http://www.barelyfitz.com/screencast/html-training/css/positioning/</h3>

position: static
<div id="example-1" class="example">
  <div class="div-before"><p>div-before</p></div>
  
  <div class="div-1">
    <div class="div-1-padding">
      <p>div-1</p>
      
      <div class="div-1a">
        <p>div-1a</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit.</p>
      </div>
      
      <div class="div-1b">
        <p>div-1b</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit. Nam mattis, arcu ut bibendum commodo, magna nisi tincidunt tortor, quis accumsan augue ipsum id lorem.</p>
      </div>
      
      <div class="div-1c"><p>div-1c</p></div>
    </div>
  </div>
  
  <div class="div-after"><p>div-after</p></div>
</div>

position: relative
<div id="example-2" class="example">
  <div class="div-before"><p>div-before</p></div>
  
  <div class="div-1">
    <div class="div-1-padding">
      <p>div-1</p>
      
      <div class="div-1a">
        <p>div-1a</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit.</p>
      </div>
      
      <div class="div-1b">
        <p>div-1b</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit. Nam mattis, arcu ut bibendum commodo, magna nisi tincidunt tortor, quis accumsan augue ipsum id lorem.</p>
      </div>
      
      <div class="div-1c"><p>div-1c</p></div>
    </div>
  </div>
  
  <div class="div-after"><p>div-after</p></div>
</div>

position: absolute
<div id="example-3" class="example">
  <div class="div-before"><p>div-before</p></div>
  
  <div class="div-1">
    <div class="div-1-padding">
      <p>div-1</p>
      
      <div class="div-1a">
        <p>div-1a</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit.</p>
      </div>
      
      <div class="div-1b">
        <p>div-1b</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit. Nam mattis, arcu ut bibendum commodo, magna nisi tincidunt tortor, quis accumsan augue ipsum id lorem.</p>
      </div>
      
      <div class="div-1c"><p>div-1c</p></div>
    </div>
  </div>
  
  <div class="div-after"><p>div-after</p></div>
</div>

position: relative + position: absolute
<div id="example-4" class="example">
  <div class="div-before"><p>div-before</p></div>
  
  <div class="div-1">
    <div class="div-1-padding">
      <p>div-1</p>
      
      <div class="div-1a">
        <p>div-1a</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit.</p>
      </div>
      
      <div class="div-1b">
        <p>div-1b</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit. Nam mattis, arcu ut bibendum commodo, magna nisi tincidunt tortor, quis accumsan augue ipsum id lorem.</p>
      </div>
      
      <div class="div-1c"><p>div-1c</p></div>
    </div>
  </div>
  
  <div class="div-after"><p>div-after</p></div>
</div>

two column absolute
<div id="example-5" class="example">
  <div class="div-before"><p>div-before</p></div>
  
  <div class="div-1">
    <div class="div-1-padding">
      <p>div-1</p>
      
      <div class="div-1a">
        <p>div-1a</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit.</p>
      </div>
      
      <div class="div-1b">
        <p>div-1b</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit. Nam mattis, arcu ut bibendum commodo, magna nisi tincidunt tortor, quis accumsan augue ipsum id lorem.</p>
      </div>
      
      <div class="div-1c"><p>div-1c</p></div>
    </div>
  </div>
  
  <div class="div-after"><p>div-after</p></div>
</div>

two column absolute height
<div id="example-6" class="example">
  <div class="div-before"><p>div-before</p></div>
  
  <div class="div-1">
    <div class="div-1-padding">
      <p>div-1</p>
      
      <div class="div-1a">
        <p>div-1a</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit.</p>
      </div>
      
      <div class="div-1b">
        <p>div-1b</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit. Nam mattis, arcu ut bibendum commodo, magna nisi tincidunt tortor, quis accumsan augue ipsum id lorem.</p>
      </div>
      
      <div class="div-1c"><p>div-1c</p></div>
    </div>
  </div>
  
  <div class="div-after"><p>div-after</p></div>
</div>

float
<div id="example-7" class="example">
  <div class="div-before"><p>div-before</p></div>
  
  <div class="div-1">
    <div class="div-1-padding">
      <p>div-1</p>
      
      <div class="div-1a">
        <p>div-1a</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit.</p>
      </div>
      
      <div class="div-1b">
        <p>div-1b</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit. Nam mattis, arcu ut bibendum commodo, magna nisi tincidunt tortor, quis accumsan augue ipsum id lorem.</p>
      </div>
      
      <div class="div-1c"><p>div-1c</p></div>
    </div>
  </div>
  
  <div class="div-after"><p>div-after</p></div>
</div>

float columns
<div id="example-8" class="example">
  <div class="div-before"><p>div-before</p></div>
  
  <div class="div-1">
    <div class="div-1-padding">
      <p>div-1</p>
      
      <div class="div-1a">
        <p>div-1a</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit.</p>
      </div>
      
      <div class="div-1b">
        <p>div-1b</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit. Nam mattis, arcu ut bibendum commodo, magna nisi tincidunt tortor, quis accumsan augue ipsum id lorem.</p>
      </div>
      
      <div class="div-1c"><p>div-1c</p></div>
    </div>
  </div>
  
  <div class="div-after"><p>div-after</p></div>
</div>

float columns with clear
<div id="example-9" class="example">
  <div class="div-before"><p>div-before</p></div>
  
  <div class="div-1">
    <div class="div-1-padding">
      <p>div-1</p>
      
      <div class="div-1a">
        <p>div-1a</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit.</p>
      </div>
      
      <div class="div-1b">
        <p>div-1b</p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Integer pretium dui sit amet felis. Integer sit amet diam. Phasellus ultrices viverra velit. Nam mattis, arcu ut bibendum commodo, magna nisi tincidunt tortor, quis accumsan augue ipsum id lorem.</p>
      </div>
      
      <div class="div-1c"><p>div-1c</p></div>
    </div>
  </div>
  
  <div class="div-after"><p>div-after</p></div>
</div>

</body>

</html>
