<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  25 Sept 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Canteen extends CI_Controller {

		public function __construct() {
			parent::__construct();
			if (!$this->ion_auth->logged_in()) {
				redirect('auth/login', 'refresh');
			}
			if (!$this->authorization->isModuleEnabled('CANTEEN')) {
				redirect('dashboard', 'refresh');
			}
			$this->load->model('canteen_model');
			$this->load->model('library_model');
    }

	public function index(){
		$site_url = site_url();
	    $data['tiles'] = array(
	        [
	          'title' => 'Load Money',
	          'sub_title' => 'Load Money',
	          'icon' => 'svg_icons/loadmoeny.svg',
	          'url' => $site_url.'canteen/load_money',
	          'permission' => 1
	        ],
	        [
	          'title' => 'Canteen Collection',
	          'sub_title' => 'Canteen Collection',
	          'icon' => 'svg_icons/canteencollection.svg',
	          'url' => $site_url.'canteen/pay_money',
	          'permission' => 1
	        ],
	        [
	          'title' => 'Menu Master',
	          'sub_title' => 'Menu Master',
	          'icon' => 'svg_icons/substitutionmaster.svg',
	          'url' => $site_url.'canteen/add_menu',
	          'permission' => 1
	        ],
	        [
	          'title' => 'Daily Menu',
	          'sub_title' => 'Daily Menu',
	          'icon' => 'svg_icons/dailymenu.svg',
	          'url' => $site_url.'canteen/menu_master',
	          'permission' => 1
	        ]
	    );
      	$data['tiles'] = checkTilePermissions($data['tiles']);

      	$data['report_tiles'] = array(
	        [
	          'title' => 'Daily Transaction',
	          'sub_title' => 'Daily Transaction',
	          'icon' => 'svg_icons/dailytransaction.svg',
	          'url' => $site_url.'canteen/daily_transaction',
	          'permission' => 1
	        ]
	    );
      	$data['report_tiles'] = checkTilePermissions($data['report_tiles']);
      	$data['main_content'] = 'canteen/index';
      	$this->load->view('inc/template', $data);
	}

	public function load_money($identification_code =''){
		$data['identification_code'] = $identification_code;
		$data['all_names'] = $this->library_model->get_staff_student_names();
		$data['main_content'] = 'canteen/load_money';
		$this->load->view('inc/template', $data);
	}

	public function pay_money(){
		$data['menu_summary'] = $this->canteen_model->get_menu_summary_list();
		$data['all_names'] = $this->library_model->get_staff_student_names();
		$data['main_content'] = 'canteen/pay_money';
		$this->load->view('inc/template', $data);
	}

	// Canteen Menu Master index
	public function menu_master(){
		$data['menu_list'] = $this->canteen_model->get_all_canteen_menu();
		$data['main_content'] = 'canteen/menu_master/menu_master';
      	$this->load->view('inc/template', $data);
	}

	public function add_menu(){
		$data['menu_list'] = $this->canteen_model->get_all_canteen_menu();
		$data['main_content'] = 'canteen/menu_master/menu_add';
      	$this->load->view('inc/template', $data);
	}
	// menu list insert to database one by one

	public function insert_menu(){
	  	$result=$this->canteen_model->insert_canteen_menu_list();
	    if($result) {
	      $this->session->set_flashdata('flashSuccess', 'Menu Item successfully added.');
	      redirect('canteen/add_menu');
	    } else {
	      $this->session->set_flashdata('flashError', 'Something Wrong..');
	      redirect('canteen/add_menu');
	    }
	}

	public function update_daywise_menu(){
		$result=$this->canteen_model->canteen_menu_status_update();
	    if($result) {
	      $this->session->set_flashdata('flashSuccess', 'Menu Item successfully update.');
	      redirect('canteen/menu_master');
	    } else {
	      $this->session->set_flashdata('flashError', 'Something Wrong..');
	      redirect('canteen/menu_master');
	    }
	}

	public function menu_edit($menuId){
		$data['menu_list'] = $this->canteen_model->get_all_canteen_menu();
		$data['menu_edit'] = $this->canteen_model->edit_canteen_menu_byId($menuId);
		$data['main_content'] = 'canteen/menu_master/menu_add';
      	$this->load->view('inc/template', $data);
	}

	public function menu_delete($menuId){
		$result=$this->canteen_model->delete_canteen_menu_list($menuId);
	    if($result) {
	      $this->session->set_flashdata('flashSuccess', 'Successfully deleted.');
	      redirect('canteen/add_menu');
	    } else {
	      $this->session->set_flashdata('flashError', 'Something Wrong..');
	      redirect('canteen/add_menu');
	    }
	}
	public function update_menu($menuId){
	  	$result=$this->canteen_model->update_canteen_menu_list($menuId);
	    if($result) {
	      $this->session->set_flashdata('flashSuccess', 'Successfully Updated.');
	      redirect('canteen/add_menu');
	    } else {
	      $this->session->set_flashdata('flashError', 'Something Wrong..');
	      redirect('canteen/add_menu');
	    }
	}

	public function daily_transaction($filter_mode=""){
		
		if ($filter_mode == 1) {
	        $fromDate = date('Y-m-d',strtotime($this->input->post('from_date')));
	        $toDate =date('Y-m-d',strtotime($this->input->post('to_date')));
	        $data['SelectedFrom_date'] = $fromDate;
	        $data['SelectedTo_date'] = $toDate;
	        $dailyTrans = $this->canteen_model->get_transactionfor_canteen_daywise($fromDate,$toDate);
      	}else{
        	$dailyTrans = $this->canteen_model->get_today_transactionfor_canteen();
      	}
      	$data['canteen_transaction'] = $dailyTrans;
		$data['main_content'] = 'canteen/daily_transaction';
      	$this->load->view('inc/template_fee', $data);
	}

	public function consolidate(){
		$data['main_content'] = 'canteen/consolidate';
      	$this->load->view('inc/template', $data);
	}

	private function __disambiguateAccessId($inputId) {
		$sName = $this->settings->getSetting('school_short_name');
		if (strpos($inputId, strtoupper($sName)) === false){
		 $accessId = hex2bin($inputId);
		}else{
		 $accessId = $inputId;
		}
		return $accessId;
	}

	public function fetch_accessId_wise_data(){
		$accessId = $this->__disambiguateAccessId($this->input->post('accessId'));
		$data['accessId'] = $accessId;
//		$accessId = $this->input->post('accessId');
		$sName = $this->settings->getSetting('school_short_name');
    	$strReplce = str_replace(strtoupper($sName), "", $accessId);
		$cardName = substr($strReplce, 0,2);
    	switch ($cardName) {
	      case 'SD':
	        $result = $this->canteen_model->fetch_accessId_wise_data_student($accessId);
	        break;
	      case 'ST':
	        $result =$this->canteen_model->fetch_accessId_wise_data_Staff($accessId);
	        break;
			}
		echo json_encode($result);
	}

	public function get_menuid_wise_price(){
		$menuId = $this->input->post('menuId');
		$result = $this->canteen_model->fetch_canteen_menu_price_byId($menuId);
		echo json_encode($result);
	}

	public function insert_payment_history(){

		$input = $this->input->post();
		if (empty($input['identification_code']) || empty($input['totalAmount']) || $input['totalAmount'] == 0 ) {
			$this->session->set_flashdata('flashInfo', 'Identification code or items not found');
			redirect('canteen/pay_money');
		} 
		
		$this->db->trans_begin();
		$trans_id = $this->canteen_model->insert_canteen_transcation($input);
		if (empty($trans_id)) {
			$this->db->trans_rollback();
		 	$this->session->set_flashdata('flashError', 'Something Wrong..');
		} else {
			$trans_items = $this->canteen_model->insert_canteen_transcation_items($input, $trans_id);
			if (empty($trans_items)) {
				$this->db->trans_rollback();
			 	$this->session->set_flashdata('flashError', 'Something Wrong..');
			}

			$card_trans = $this->canteen_model->insert_card_transcation($input, $trans_id);
			if (empty($card_trans)) {
				$this->db->trans_rollback();
			 	$this->session->set_flashdata('flashError', 'Something Wrong..');
			} 
			$result = $this->db->trans_commit();
	        if ($result) {
	            $this->session->set_flashdata('flashSuccess', 'Successfully added');
          	} else {
	            $this->db->trans_rollback();
	            $this->session->set_flashdata('flashError', 'Something went wrong');
          	} 
		}
	 	redirect('canteen/pay_money');
	}

	public function insert_load_payment_history(){
		$input = $this->input->post();
		if (empty($input['identification_code'])) {
			$this->session->set_flashdata('flashInfo', 'Student or Staff identification code not found');
			redirect('canteen/load_money');
		} 
		$result = $this->canteen_model->load_canteen_amount($input);
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully added');
      	} else {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something went wrong');
      	}
      	redirect('canteen/load_money/'.$input['identification_code']); 
	}
	
	
}