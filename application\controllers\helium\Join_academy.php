<?php

class Join_academy extends CI_Controller {
    function __construct(){
        parent::__construct();
        // if (!$this->ion_auth->logged_in()) {
        //     redirect('auth/login', 'refresh');
        // }
        $this->acad_year->loadAcadYearDataToSession();
        $this->load->model('helium/Join_academy_model');
        $this->load->model('student/Student_Model');
    }

    public function index() {
        $data['classes'] = $this->Join_academy_model->getClassList();
        if ($this->mobile_detect->isTablet()) {
            $data['main_content']   = 'helium/academy/join_mobile';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content']    = 'helium/academy/join_mobile';
        }else{
            $data['main_content']    = 'helium/academy/join_desktop';
        }
        $this->load->view($data['main_content'], $data);
        // $this->load->view('helium/inc/template', $data);
    }

    public function join_student() {
        // echo '<pre>'; print_r($_POST); die();
        $input = $_POST;
        $input_form = new stdClass();
        $input_form->student_firstname = $input['student_first_name'];
        $input_form->student_lastname = $input['student_last_name'];
        $input_form->student_dob = date('Y-m-d', strtotime($input['student_dob']));
        $input_form->s_email = $input['email_id'];
        $input_form->contact_no = $input['contact_number'];
        $input_form->gender = $input['gender'];
        $input_form->rteid = 1;
        $input_form->board = 1;
        $input_form->boardingid = 1;
        $input_form->medid = 1;
        $input_form->roll_num = 0;
        $input_form->classid = $input['class'];
        $input_form->classsection = $this->Join_academy_model->getClassSectionId($input['class']);
        $input_form->add_status = 2;
        $input_form->acad_year = $this->acad_year->getAcadYearId();
        $input_form->admission_acad_year = $input_form->acad_year;
        $input_form->acad_year_id = $input_form->acad_year;
        $input_form->std_aadhar = null;
        $input_form->birth_taluk = null;
        $input_form->birth_district = null;
        $input_form->nationality = 'Indian';
        $input_form->religion = null;
        $input_form->caste = null;
        $input_form->category = null;
        $input_form->donor_name = null;
        $input_form->donor_name = null;
        
        $input_form->f_userid = '';
        $input_form->m_userid = '';
        $input_form->f_first_name = $input['father_first_name'];
        $input_form->f_last_name = $input['father_last_name'];
        $input_form->f_email = $input['email_id'];
        $input_form->f_mobile_no = $input['contact_number'];
        $input_form->f_qualification = null;
        $input_form->f_occupation = null;
        $input_form->f_company = null;
        $input_form->f_annual_income = null;
        $input_form->f_aadhar = null;

        $input_form->m_first_name = 'Mother of '.$input['student_first_name'];
        $input_form->m_last_name = null;
        $input_form->m_email = null;
        $input_form->m_mobile_no = null;
        $input_form->m_qualification = null;
        $input_form->m_occupation = null;
        $input_form->m_company = null;
        $input_form->m_annual_income = null;
        $input_form->m_aadhar = null;

        $status = $this->__submitStudent($input_form);
        $data['status'] = $status;
        $data['student_name'] = $input['student_first_name'];
        $data['email_id'] = $input['email_id'];
        $data['mobile_no'] = $input['contact_number'];
        if ($this->mobile_detect->isTablet()) {
            $data['main_content']   = 'helium/academy/join_completion_mobile';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content']    = 'helium/academy/join_completion_mobile';
        }else{
            $data['main_content']    = 'helium/academy/join_completion_desktop';
        }
        $this->load->view($data['main_content'], $data);
    }

    public function join_status() {
        $data['status'] = $_POST['status'];
        $data['student_name'] = $_POST['student_name'];
        $data['email_id'] = $_POST['email_id'];
        $data['mobile_no'] = $_POST['mobile_no'];
        if ($this->mobile_detect->isTablet()) {
            $data['main_content']   = 'helium/academy/join_status_mobile';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content']    = 'helium/academy/join_status_mobile';
        }else{
            $data['main_content']    = 'helium/academy/join_status_desktop';
        }
        $this->load->view($data['main_content'], $data);
    }

    private function __submitStudent($input_form) {
       
        $grouped_input = $this->_prepareStudentInput($input_form);
        //admission_no
        $config_admission_number = $this->settings->getSetting('admission_number');
        // echo "<pre>"; print_r($config_admission_number); die();
        $lastRecord = $this->Student_Model->getLastStudentid(); 
        if (!$lastRecord) {
            $lastRecordId = $config_admission_number->index_offset + 1;
        } else {
            $lastRecordId = $config_admission_number->index_offset + ($lastRecord->id + 1);
        }
        $params['number'] = sprintf("%0".$config_admission_number->digit_count . "s", $lastRecordId);
        $params['year_of_joining'] = $this->acad_year->getAcadYear();
        $params['classid'] = $this->Student_Model->getClassByID($grouped_input['student']['classid']);
        $grouped_input['student']['admission_no'] = $this->_generateAdmissionNo($config_admission_number,$params);
        $grouped_input['student']['last_modified_by'] = 1;
        $grouped_input['father']['last_modified_by'] = 1;
        $grouped_input['mother']['last_modified_by'] = 1;

      //$this->db->trans_off();
      $this->db->trans_begin();

      $student_uid = $this->Student_Model->addStudentInfo($grouped_input['student'],null,$grouped_input['father']['userid'], ['file_name' => '']);
      if($student_uid == 0) {
        $this->db->trans_rollback();
        return 0;
      } else {

       $father_uid = $this->Student_Model->addParentInfo($grouped_input['father'],$student_uid['stdAdmId'],'Father',null, $grouped_input['student']['student_firstname'], $grouped_input['father']['mobile_no']);
      if (!$father_uid) {
        $this->db->trans_rollback();
        return 0;
      }

      
       $mother_uid = $this->Student_Model->addParentInfo($grouped_input['mother'],$student_uid['stdAdmId'],'Mother',null, $grouped_input['student']['student_firstname'], $grouped_input['mother']['mobile_no']);

        if (!$mother_uid) {
          $this->db->trans_rollback();
          return 0;
        }

        if ($this->db->trans_status()) {
          $this->db->trans_commit();
          $this->_smsParentCredentials($father_uid);
          return 1;
        } else {
          $this->db->trans_rollback();
          return 0;
        }
        return 1;
      }
    }

    private function _prepareStudentInput(&$input) {
      $return_data = [];
      foreach ($input as $k => $v) {
        $start_key = substr($k, 0, 2);
        if ($start_key == 'f_') {
          $key = str_replace("f_","",$k);
          $return_data['father'][$key] = $v;
        }
        elseif ($start_key == 'm_') {
          $key = str_replace("m_","",$k);
          $return_data['mother'][$key] = $v;
        } else {
          $return_data['student'][$k] = $v;
        }
      }

      //echo '<pre>';print_r($return_data);

      return $return_data;

    }

    private function _generateAdmissionNo($config_admission, $params = []) {
        $admission_number = '';
        switch ($config_admission->admission_generation_algo) {
            case 'NPSRNR':
            break;
        case 'NH': {
            // Greater than 2 takes all the class from 1st to so on.
            if ($params['classid']->type >= 2) 
              $admission_number = $config_admission->infix.$params['number'];
            else 
              $admission_number = 'P'.$params['number'];            
            break;
        }
        case 'NEXTELEMENT': {
            $admission_number = $params['year_of_joining'].$config_admission->infix.$params['number'];
            break;
        }
        case 'YASHASVI': {
            $admission_number = $config_admission->infix.$params['number'];
            break;
        }
        case 'WPL':
        //Infix 'N' if nursery, 'P' if primary, 'H' if high school
          switch ($params['classid']->type) {
            case 1:
              $classType = 'N';
              break;
            case 2:
              $classType = 'P';
              break;
            case 3:
              $classType = 'H';
              break;
          }
          $admission_number =$config_admission->infix.$classType.$params['number'];
          break;
        }

        return $admission_number;
    }

    private function generatePassword($length = 10) {
        return substr(str_shuffle(str_repeat($x='abcdefghijkmnopqrstuvwxyz', ceil($length/strlen($x)) )),1,$length);
    }

    private function _smsParentCredentials($parent_id) {
        $password = $this->generatePassword(6);
        $user = $this->Join_academy_model->getFatherUsername($parent_id);
        if(empty($user)) return 0;
        $this->Join_academy_model->activateUser($user->id);
        $this->Join_academy_model->resetPassword($user->id,$password);
        $username = $user->username;
        $mobile_no = $user->mobile_no;
        $message = "Welcome to Helium Academy. Your credentials are Username: %%username%% Password: %%password%% . You can continue the registration process by clicking on https://heliumacademy.schoolelement.in . DO NOT SHARE THESE CREDENTIALS with anybody.";
        $message = str_replace("%%username%%", $username, $message);
        $message = str_replace("%%password%%", $password, $message);
        $input_arr = array();
        $input_arr['custom_numbers'] = array($mobile_no);
        $input_arr['mode'] = 'sms';
        $input_arr['source'] = 'Credentials';
        $input_arr['message'] = $message;
        $input_arr['avatar_id'] = 1;
        $this->load->helper('texting_helper');
        sendText($input_arr);
    }
}