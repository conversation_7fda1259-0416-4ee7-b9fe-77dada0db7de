<?php
require APPPATH . 'libraries/REST_Controller.php';
 
class Auth extends REST_Controller {
    
    public function __construct() {
        parent::__construct();
    }

    public function login_post() {
        if ($this->post('username') == '' || $this->post('password') == '') {
            $data = ['error' => ['message' => 'INVALID_CREDENTIALS']];
            $this->response($data, REST_Controller::HTTP_OK);
			$this->output->_display();
			exit;
        }

        $isauth = $this->ion_auth->login($this->post('username'), $this->post('password'));
        if (!$isauth) {
            $data = ['error' => ['message' => 'INVALID_CREDENTIALS']];
            $this->response($data, REST_Controller::HTTP_OK);
            $this->output->_display();
            exit;    
        }

        $userid = $this->session->userdata('user_id');

        $std_query = $this->db->query("select p.id as parent_id, a.id as avatar_id, sa.id as student_id, CONCAT(IFNULL(sa.first_name,''), ' ', IFNULL(sa.last_name, '')) as std_name, sy.class_section_id as csid, 
            sy.class_id as cid, cs.class_name as class_name, cs.section_name as section_name from student_admission sa
            join student_year sy on sy.student_admission_id=sa.id and sy.acad_year_id=19
            join class_section cs on sy.class_section_id=cs.id
            join parent p on sa.id=p.student_id
            join avatar a on p.id=a.stakeholder_id and a.avatar_type=2
            where a.id in (select id from avatar where user_id=$userid)")->result();

        foreach ($std_query as &$std) {
            $student_id = $std->student_id;
            $acad_years = $this->db->query("select acad_year_id,acad_year, promotion_status, is_current_year as active_year from student_year sy join academic_year ay on sy.acad_year_id=ay.id where student_admission_id=$student_id;")->result();
            $std->acad_years = $acad_years;
        }

        $data = ['isauth' => $isauth,'std_details' => $std_query,'parent_user_id' => $userid];
        
        $this->response($data, REST_Controller::HTTP_OK);
        $this->output->_display();
        exit;
    }

    public function isloggedin_get() {
        $data = ['Login: ' => $this->ion_auth->logged_in()];
        $this->ion_auth->logged_in() ? $this->response($data, REST_Controller::HTTP_OK) : $this->response($data, REST_Controller::HTTP_UNAUTHORIZED);
        $this->output->_display();
        exit;
    }

    public function logout_post() {
        $this->ion_auth->logout();
        $data = ['message' => 'Logged out successfully'];
        $this->response($data, REST_Controller::HTTP_OK);
        $this->output->_display();
        exit;
    }
    
    public function deviceDetails_get(){
        $data = (array) json_decode(file_get_contents('php://input'), TRUE);
        trigger_error('Device Input Data');
        trigger_error(json_encode($data));
        // $device_id = $this->get('device_id');
        // $device_type = $this->get('device_type');
        // $device_os = $this->get('device_os');
        // $device_os_version = $this->get('device_os_version');
        // $device_manufacturer =  $this->get('device_manufacturer');
        // $device_model = $this->get('device_model');
        // $device_brand = $this->get('device_brand');
        // $device_sdk_version = $this->get('device_sdk_version');
        // $device_resolution = $this->get('device_resolution');
        // $device_density = $this->get('device_density');
        // $device_ip = $this->get('device_ip');
        // $device_mac = $this->get('device_mac');
        // $device_imei = $this->get('device_imei');        
    }

}