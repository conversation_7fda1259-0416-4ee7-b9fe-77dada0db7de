<?php
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of student_controller
 *
 * <AUTHOR>
 */
class Circular_view_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if ($this->authorization->isModuleEnabled('CIRCULAR') && ($this->authorization->isAuthorized('CIRCULAR.STAFF_CIRCULAR'))) {
            $this->load->model('staff/Staff_Model');
        } else {
            redirect('dashboard', 'refresh');
        }
        $this->load->library('filemanager');
    }

    public function circular(){ 
        $staffId = $this->authorization->getAvatarStakeHolderId();
        //$staffDetails = $this->Staff_Model->getStaffDetailById($staffId);
        //echo '<pre>'; print_r($staffDetails); die();
        $data['staff_circular'] =  $this->Staff_Model->get_dairyStaffIdwise($staffId);

        $categories = json_decode($this->settings->getSetting('circular_categories'));
        $data['count'] = array();
        $data['new'] = array();
        foreach ($categories as $key => $category) {
        $data['count'][$category] = $this->Staff_Model->getCircularCount($category,$staffId);
        $data['new'][$category] = $this->Staff_Model->newCount($category,$staffId);
        }
        //echo '<pre>'; print_r($data); die();
        $data['main_content']    = 'staff/notification/circular/index';
        $this->load->view('inc/template', $data);
    }

    public function circulars($category){
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $data['staff_circular'] =  $this->Staff_Model->getStaffCircularCategoryWise($staffId,$category);
        $data['category'] = $category;
        $data['main_content']    = 'staff/notification/circular/category_wise_titles';
        $this->load->view('inc/template', $data);
        //echo "<pre>"; print_r($data); die();
    }

    public function view_circular($category,$id){
        $data['category'] = $category;
        $data['circular_id'] = $id;
        $data['circularData'] = $this->Staff_Model->getStaffCircularData($id);
        $data['main_content']    = 'staff/notification/circular/circular';
        $this->load->view('inc/template', $data);
        //echo "<pre>"; print_r($data); die();
    }
}