<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  12 March 2018
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_Master
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Fee_Master extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isSuperAdmin()) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('fees/Fee_Master_Model');
    $this->load->library('fee_library');
  }

  //Landing function for Fee Master
  public function index () {
    $data['fee_structures'] = $this->Fee_Master_Model->getFeeStructures();
    $data['main_content'] = 'fees/master/fee_master';
    $this->load->view('inc/template', $data);
  }

  //Landing function for add a new Fee Structure
  public function add () {
    //Get the filter criteria for the school and populate the data
    $fcArr = $this->settings->getSetting('fees')['filter_criteria'];
    //echo "<pre>"; print_r($fcArr); die();
    foreach ($fcArr as $fc) {
      switch ($fc) {
        case 'academic_year_of_joining':
          $data['showAcadJoining'] = TRUE;
          $data['acadJoiningLabel'] = 'Joining Academic year';
          $data['acadColumnName'] = $fc;
          $data['acadJoiningOptions'] = $this->Fee_Master_Model->getAcadJoiningOptions();
          break;
        case 'rte':
          $data['showRTE'] = TRUE;
          $data['rteLabel'] = 'RTE';
          $data['rteColumnName'] = $fc;
          $data['rteOptions'] = $this->Fee_Master_Model->getRTEOptions();
          break;
        case 'class':
          $data['showClass'] = TRUE;
          $data['classLabel'] = 'Class';
          $data['classColumnName'] = $fc;
          $data['classData'] = $this->Fee_Master_Model->getClassList();
        //echo "<pre>";print_r($data['classData']);die();
          break;
        case 'medium':
          $data['showMedium'] = TRUE;
          $data['mediumLabel'] = 'Medium';
          $data['mediumColumnName'] = $fc;
          $data['mediumOptions'] = $this->Fee_Master_Model->getMediumOptions();
          break;
        case 'category':
          $data['showCategory'] = TRUE;
          $data['categoryLabel'] = 'Category';
          $data['categoryColumnName'] = $fc;
          $data['categoryOptions'] = $this->Fee_Master_Model->getCategoryOptions();
          break;
        case 'admission_type':
          $data['showAdmissionType'] = TRUE;
          $data['admissionTypeLabel'] = 'Admission';
          $data['admissionTypeColumnName'] = $fc;
          $data['admissionTypeOptions'] = $this->Fee_Master_Model->getAdmissionTypeOptions();
          break;
        case 'board':
          $data['showBoards'] = TRUE;
          $data['boardsLabel'] = 'Boards';
          $data['boardsColumnName'] = $fc;
          $data['boardList'] = $this->Fee_Master_Model->getBoardsList();
          break;
        case 'boarding':
          $data['showBoarding'] = TRUE;
          $data['boardingLabel'] = 'Boarding';
          $data['boardingColumnName'] = $fc;
          $data['boardingOptions'] = $this->Fee_Master_Model->getBoardingOptions();
          break;
      }
    }

    //Get the fee components and prepare the data
    $data['fcomponentsArr'] = $this->settings->getSetting('fees')['components'];

    //echo '<pre>';print_r($data['fcomponentsArr']);die();
    $data['main_content'] = 'fees/master/add_fee_structure';
    $this->load->view('inc/template', $data);
  }

  //Landing function for editing Fee Master Record
  public function edit_fee_structure () {
    //echo '<pre>';print_r($this->input->post());
    $data['main_content'] = 'fees/master/edit_fee_structure';
    $this->load->view('inc/template', $data);
  }

  //Add the Fee Master Record
  public function submit_fee_structure () {
    //echo '<pre>';print_r($this->input->post());die();

    //Construct the filter 
    $filter = $this->fee_library->construct_filter($this->input->post());
 
    $result = $this->Fee_Master_Model->insertFeeStructure($filter);

    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Submit Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
	  }

    redirect('fees/Fee_Master');
  }

  //Update the Fee Master Record
  public function update_fee_structure () {
    redirect('fees/Fee_Master');
  }

  //Delete the Fee Master Record
  public function delete_fee_structure () {
    redirect('fees/Fee_Master');
  }
}