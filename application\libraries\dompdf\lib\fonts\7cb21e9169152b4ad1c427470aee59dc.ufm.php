<?php return array (
  'codeToName' => 
  array (
    13 => 'CR',
    32 => 'space',
    33 => 'exclam',
    34 => 'quotedbl',
    35 => 'numbersign',
    36 => 'dollar',
    37 => 'percent',
    38 => 'ampersand',
    39 => 'quotesingle',
    40 => 'parenleft',
    41 => 'parenright',
    42 => 'asterisk',
    43 => 'plus',
    44 => 'comma',
    45 => 'hyphen',
    46 => 'period',
    47 => 'slash',
    48 => 'zero',
    49 => 'one',
    50 => 'two',
    51 => 'three',
    52 => 'four',
    53 => 'five',
    54 => 'six',
    55 => 'seven',
    56 => 'eight',
    57 => 'nine',
    58 => 'colon',
    59 => 'semicolon',
    60 => 'less',
    61 => 'equal',
    62 => 'greater',
    63 => 'question',
    64 => 'at',
    65 => 'A',
    66 => 'B',
    67 => 'C',
    68 => 'D',
    69 => 'E',
    70 => 'F',
    71 => 'G',
    72 => 'H',
    73 => 'I',
    74 => 'J',
    75 => 'K',
    76 => 'L',
    77 => 'M',
    78 => 'N',
    79 => 'O',
    80 => 'P',
    81 => 'Q',
    82 => 'R',
    83 => 'S',
    84 => 'T',
    85 => 'U',
    86 => 'V',
    87 => 'W',
    88 => 'X',
    89 => 'Y',
    90 => 'Z',
    91 => 'bracketleft',
    92 => 'backslash',
    93 => 'bracketright',
    94 => 'asciicircum',
    95 => 'underscore',
    96 => 'grave',
    97 => 'a',
    98 => 'b',
    99 => 'c',
    100 => 'd',
    101 => 'e',
    102 => 'f',
    103 => 'g',
    104 => 'h',
    105 => 'i',
    106 => 'j',
    107 => 'k',
    108 => 'l',
    109 => 'm',
    110 => 'n',
    111 => 'o',
    112 => 'p',
    113 => 'q',
    114 => 'r',
    115 => 's',
    116 => 't',
    117 => 'u',
    118 => 'v',
    119 => 'w',
    120 => 'x',
    121 => 'y',
    122 => 'z',
    123 => 'braceleft',
    124 => 'bar',
    125 => 'braceright',
    126 => 'asciitilde',
    161 => 'exclamdown',
    162 => 'cent',
    163 => 'sterling',
    164 => 'currency',
    165 => 'yen',
    166 => 'brokenbar',
    167 => 'section',
    168 => 'dieresis',
    169 => 'copyright',
    170 => 'ordfeminine',
    171 => 'guillemotleft',
    172 => 'logicalnot',
    174 => 'registered',
    175 => 'macron',
    176 => 'degree',
    177 => 'plusminus',
    180 => 'acute',
    182 => 'paragraph',
    183 => 'periodcentered',
    184 => 'cedilla',
    186 => 'ordmasculine',
    187 => 'guillemotright',
    188 => 'onequarter',
    189 => 'onehalf',
    190 => 'threequarters',
    191 => 'questiondown',
    192 => 'Agrave',
    193 => 'Aacute',
    194 => 'Acircumflex',
    195 => 'Atilde',
    196 => 'Adieresis',
    197 => 'Aring',
    198 => 'AE',
    199 => 'Ccedilla',
    200 => 'Egrave',
    201 => 'Eacute',
    202 => 'Ecircumflex',
    203 => 'Edieresis',
    204 => 'Igrave',
    205 => 'Iacute',
    206 => 'Icircumflex',
    207 => 'Idieresis',
    208 => 'Eth',
    209 => 'Ntilde',
    210 => 'Ograve',
    211 => 'Oacute',
    212 => 'Ocircumflex',
    213 => 'Otilde',
    214 => 'Odieresis',
    215 => 'multiply',
    216 => 'Oslash',
    217 => 'Ugrave',
    218 => 'Uacute',
    219 => 'Ucircumflex',
    220 => 'Udieresis',
    221 => 'Yacute',
    222 => 'Thorn',
    223 => 'germandbls',
    224 => 'agrave',
    225 => 'aacute',
    226 => 'acircumflex',
    227 => 'atilde',
    228 => 'adieresis',
    229 => 'aring',
    230 => 'ae',
    231 => 'ccedilla',
    232 => 'egrave',
    233 => 'eacute',
    234 => 'ecircumflex',
    235 => 'edieresis',
    236 => 'igrave',
    237 => 'iacute',
    238 => 'icircumflex',
    239 => 'idieresis',
    240 => 'eth',
    241 => 'ntilde',
    242 => 'ograve',
    243 => 'oacute',
    244 => 'ocircumflex',
    245 => 'otilde',
    246 => 'odieresis',
    247 => 'divide',
    248 => 'oslash',
    249 => 'ugrave',
    250 => 'uacute',
    251 => 'ucircumflex',
    252 => 'udieresis',
    253 => 'yacute',
    254 => 'thorn',
    255 => 'ydieresis',
    305 => 'dotlessi',
    338 => 'OE',
    339 => 'oe',
    710 => 'circumflex',
    730 => 'ring',
    732 => 'tilde',
    8211 => 'endash',
    8212 => 'emdash',
    8216 => 'quoteleft',
    8217 => 'quoteright',
    8218 => 'quotesinglbase',
    8220 => 'quotedblleft',
    8221 => 'quotedblright',
    8222 => 'quotedblbase',
    8226 => 'bullet',
    8230 => 'ellipsis',
    8249 => 'guilsinglleft',
    8250 => 'guilsinglright',
    8260 => 'fraction',
    8364 => 'Euro',
    8722 => 'minus',
  ),
  'isUnicode' => true,
  'EncodingScheme' => 'FontSpecific',
  'FontName' => 'Exo Light',
  'FullName' => 'Exo Light',
  'Version' => 'Version 1.500; ttfautohint (v1.6)',
  'PostScriptName' => 'Exo-Light',
  'Weight' => 'Medium',
  'ItalicAngle' => '0',
  'IsFixedPitch' => 'false',
  'UnderlineThickness' => '50',
  'UnderlinePosition' => '-75',
  'FontHeightOffset' => '0',
  'Ascender' => '1002',
  'Descender' => '-327',
  'FontBBox' => 
  array (
    0 => '-502',
    1 => '-270',
    2 => '1329',
    3 => '1096',
  ),
  'StartCharMetrics' => '235',
  'C' => 
  array (
    13 => 274.0,
    32 => 274.0,
    33 => 263.0,
    34 => 250.0,
    35 => 673.0,
    36 => 609.0,
    37 => 957.0,
    38 => 665.0,
    39 => 142.0,
    40 => 335.0,
    41 => 334.0,
    42 => 459.0,
    43 => 609.0,
    44 => 189.0,
    45 => 341.0,
    46 => 189.0,
    47 => 384.0,
    48 => 658.0,
    49 => 359.0,
    50 => 595.0,
    51 => 587.0,
    52 => 587.0,
    53 => 638.0,
    54 => 628.0,
    55 => 540.0,
    56 => 648.0,
    57 => 628.0,
    58 => 189.0,
    59 => 189.0,
    60 => 609.0,
    61 => 609.0,
    62 => 609.0,
    63 => 531.0,
    64 => 870.0,
    65 => 671.0,
    66 => 596.0,
    67 => 579.0,
    68 => 666.0,
    69 => 584.0,
    70 => 563.0,
    71 => 649.0,
    72 => 675.0,
    73 => 221.0,
    74 => 379.0,
    75 => 593.0,
    76 => 531.0,
    77 => 821.0,
    78 => 676.0,
    79 => 682.0,
    80 => 599.0,
    81 => 682.0,
    82 => 602.0,
    83 => 575.0,
    84 => 560.0,
    85 => 673.0,
    86 => 671.0,
    87 => 991.0,
    88 => 611.0,
    89 => 615.0,
    90 => 556.0,
    91 => 330.0,
    92 => 384.0,
    93 => 330.0,
    94 => 401.0,
    95 => 609.0,
    96 => 210.0,
    97 => 543.0,
    98 => 576.0,
    99 => 490.0,
    100 => 572.0,
    101 => 542.0,
    102 => 360.0,
    103 => 574.0,
    104 => 586.0,
    105 => 221.0,
    106 => 221.0,
    107 => 492.0,
    108 => 288.0,
    109 => 954.0,
    110 => 586.0,
    111 => 577.0,
    112 => 576.0,
    113 => 572.0,
    114 => 409.0,
    115 => 498.0,
    116 => 361.0,
    117 => 586.0,
    118 => 528.0,
    119 => 788.0,
    120 => 497.0,
    121 => 533.0,
    122 => 491.0,
    123 => 308.0,
    124 => 253.0,
    125 => 308.0,
    126 => 459.0,
    160 => 274.0,
    161 => 263.0,
    162 => 487.0,
    163 => 609.0,
    164 => 609.0,
    165 => 609.0,
    166 => 253.0,
    167 => 554.0,
    168 => 417.0,
    169 => 850.0,
    170 => 407.0,
    171 => 608.0,
    172 => 609.0,
    173 => 341.0,
    174 => 850.0,
    175 => 379.0,
    176 => 333.0,
    177 => 609.0,
    178 => 456.0,
    179 => 456.0,
    180 => 210.0,
    181 => 586.0,
    182 => 572.0,
    183 => 263.0,
    184 => 261.0,
    185 => 456.0,
    186 => 433.0,
    187 => 608.0,
    188 => 1275.0,
    189 => 1275.0,
    190 => 1275.0,
    191 => 531.0,
    192 => 671.0,
    193 => 671.0,
    194 => 671.0,
    195 => 671.0,
    196 => 671.0,
    197 => 671.0,
    198 => 958.0,
    199 => 579.0,
    200 => 584.0,
    201 => 584.0,
    202 => 584.0,
    203 => 584.0,
    204 => 221.0,
    205 => 221.0,
    206 => 221.0,
    207 => 221.0,
    208 => 666.0,
    209 => 676.0,
    210 => 682.0,
    211 => 682.0,
    212 => 682.0,
    213 => 682.0,
    214 => 682.0,
    215 => 609.0,
    216 => 682.0,
    217 => 673.0,
    218 => 673.0,
    219 => 673.0,
    220 => 673.0,
    221 => 615.0,
    222 => 553.0,
    223 => 577.0,
    224 => 543.0,
    225 => 543.0,
    226 => 543.0,
    227 => 543.0,
    228 => 543.0,
    229 => 543.0,
    230 => 890.0,
    231 => 490.0,
    232 => 542.0,
    233 => 542.0,
    234 => 542.0,
    235 => 542.0,
    236 => 218.0,
    237 => 218.0,
    238 => 218.0,
    239 => 218.0,
    240 => 577.0,
    241 => 586.0,
    242 => 577.0,
    243 => 577.0,
    244 => 577.0,
    245 => 577.0,
    246 => 577.0,
    247 => 609.0,
    248 => 577.0,
    249 => 586.0,
    250 => 586.0,
    251 => 586.0,
    252 => 586.0,
    253 => 533.0,
    254 => 576.0,
    255 => 533.0,
    305 => 218.0,
    338 => 1064.0,
    339 => 934.0,
    710 => 401.0,
    730 => 334.0,
    732 => 459.0,
    8211 => 524.0,
    8212 => 747.0,
    8216 => 174.0,
    8217 => 174.0,
    8218 => 174.0,
    8220 => 278.0,
    8221 => 278.0,
    8222 => 278.0,
    8226 => 318.0,
    8230 => 816.0,
    8249 => 358.0,
    8250 => 358.0,
    8260 => 208.0,
    8308 => 456.0,
    8364 => 609.0,
    8722 => 609.0,
    8725 => 609.0,
  ),
  'CIDtoGID_Compressed' => true,
  'CIDtoGID' => 'eJzt0FWvGEQQgNEvuBeKu1Pc3d2laHB3d3nCNcEpVqB4gru7uzuvOL+ChtwHQii9SXuhSc9Jdmczu5md3RqnKca99RdTdkt3dluv9mmfd1cP9XCjeqube6wx3dO5ndf5XdCFXdTFXdKlXdZN3d0Hvdf73d5nTdVMzdywZmuu5m6e5m2hFm6RFm2xlmj5VmylVm6VVm2t1m6d1m39Huj6Huz77u2XNmiLtmzrtm2nRrZzu7RXe7dP+7ZfB3RER3V0x3Rcx3dKp3Zap3dW9/Vt9/fRoF74717q1l7u9V7pjb7ry37uq87uiT7u8b7u177pw67oyn7ok77ohn7s8s7pya7tmq7rjqZr6qZphqZt+mZsluZseLM3Rws2X/O3QLO2eEu3ZEu1bCN6p2Vas9VavTVarxU6tk3asI3arI3btM3bqh3bru3boT3brd3bo23av0M6sIM6rIN7t0M7uRM6sZM6oyM7cyL8wPjsOsEVluvwidAHTNp+GuS538aO34eyEQAAAAAAAAAAAAAAAAAAJnGP9sjA6vle6MWx8dme65mB3I0DcfSgaj3V0/+QvXrCGhyvq4a4/t+99h/fB0Pl7T/nN//nLgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAycQfJAJfSg==',
  '_version_' => 6,
);