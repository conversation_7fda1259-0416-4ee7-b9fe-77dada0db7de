<?php

class Student_counselling_controller extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENET_COUNSELLING')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT_COUNSELLING.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('staff/Staff_Model');
    $this->load->library('filemanager');
    $this->load->model('class_section');
    $this->load->model('student_counselling/Student_counselling_model');

  }

  public function index() {
    $site_url = site_url();
    $data['adm_tiles'] = array(
      [
        'title' => 'Manage Counselling',
        'sub_title' => 'View From Parentside',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_counselling/Student_counselling_controller/view_counselling',  
        'permission' => $this->authorization->isAuthorized('STUDENT_COUNSELLING.MODULE')
      ]
      
    );
        
    $data['adm_tiles'] = checkTilePermissions($data['adm_tiles']);
    $data['report_tiles'] = array( 
      [
        'title' => 'Student-Wise Report',
      'sub_title' => 'student report',
      'icon' =>'svg_icons/assessment.svg',
      'url' => $site_url.'student_counselling/Student_counselling_controller/student_wise_report',  
      'permission' => $this->authorization->isAuthorized('STUDENT_COUNSELLING.STUDENT_WISE_REPORT')
      ],
      [
        'title' => 'Counselling Report',
        'sub_title' => 'Counselling Report',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_counselling/Student_counselling_controller/student_report',  
        'permission' => $this->authorization->isAuthorized('STUDENT_COUNSELLING.COUNCELLING_REPORT')
      ],
      [
        'title' => 'My Weekly Report',
        'sub_title' => 'Counselling Report',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_counselling/Student_counselling_controller/weekly_report',  
        'permission' => $this->authorization->isAuthorized('STUDENT_COUNSELLING.MY_WEEKLY_REPORT')
      ],
      [
        'title' => 'My Comments Report',
        'sub_title' => 'Counselling Report',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_counselling/Student_counselling_controller/comments_report',  
        'permission' => $this->authorization->isAuthorized('STUDENT_COUNSELLING.COMMENTS_REPORT')
      ],
      [
        'title' => 'Grade-wise Counselling Summary',
        'sub_title' => 'Grade-wise Counselling Summary',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_counselling/Student_counselling_controller/counselling_report',  
        'permission' => $this->authorization->isAuthorized('STUDENT_COUNSELLING.GRADE_WISE_REPORT')
      ],
      [
        'title' => 'Counselling Analytics',
      'sub_title' => 'count report',
      'icon' =>'svg_icons/assessment.svg',
      'url' => $site_url.'student_counselling/Student_counselling_controller/counselling_analytics',  
      'permission' => $this->authorization->isAuthorized('STUDENT_COUNSELLING.COUNSELLING_ANALYTICS')
      ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['admin_tiles'] = array( 
      [
        'title' => 'Access Control (Student)',
        'sub_title' => '',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_counselling/Student_counselling_controller/access_controller',  
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Access Control (Staff)',
        'sub_title' => '',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_counselling/Student_counselling_controller/access_controller_staff',  
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Manage Area of Concerns',
        'sub_title' => '',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_counselling/Student_counselling_controller/add_area_of_concern',  
        'permission' => $this->authorization->isAuthorized('STUDENT_COUNSELLING.ADMINISTRATION')
      ],
      [
        'title' => 'Manage Codes',
        'sub_title' => '',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_counselling/Student_counselling_controller/add_color_code',  
        'permission' => $this->authorization->isAuthorized('STUDENT_COUNSELLING.ADMINISTRATION')
      ],
      [
        'title' => 'Manage Mode of Session',
        'sub_title' => '',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_counselling/Student_counselling_controller/add_mode_of_session',  
        'permission' => $this->authorization->isAuthorized('STUDENT_COUNSELLING.ADMINISTRATION')
      ],
      [
        'title' => 'Manage Status',
        'sub_title' => '',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_counselling/Student_counselling_controller/add_status',  
        'permission' => $this->authorization->isAuthorized('STUDENT_COUNSELLING.ADMINISTRATION')
      ],
      [
        'title' => 'Manage Referral Type',
        'sub_title' => '',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'student_counselling/Student_counselling_controller/add_referal_type',  
        'permission' => $this->authorization->isAuthorized('STUDENT_COUNSELLING.ADMINISTRATION')
      ]
    );
    $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);
    $data['main_content']    = 'student_counselling/student_counselling_menu';
    $this->load->view('inc/template', $data);
  }

  public function create_counselling(){

    $data['staffid'] = $this->authorization->getAvatarStakeHolderId();
    $clas_type = $this->Student_counselling_model->getview_counselling_data_by_classtype($data['staffid']);
    $data['classSectionList'] = $this->Student_counselling_model->getClassSectionNames($clas_type);
    $data['classes'] = $this->Student_counselling_model->getClassNames($clas_type);
    $data['areaofconcern'] = $this->Student_counselling_model->getconcernList_createpage();
    $data['colorcode'] = $this->Student_counselling_model->getcolorlist_createpage();
    $data['staff_add_access'] = $this->Student_counselling_model->add_staff_access_control($data['staffid']);
    $data['student_add_access'] = $this->Student_counselling_model->add_student_access_control($data['staffid']);
    $data['modeofsession'] = $this->Student_counselling_model->getmodeofsession_createpage_staff_access($data['staff_add_access'], $data['student_add_access']);
    $data['staff_list'] = $this->Student_counselling_model->list_staffbyuserId();
    $data['referral_type'] = $this->Student_counselling_model->getreferaltypelist_createpage();
    $data['all_names'] = $this->Student_counselling_model->get_all_staff_student_names($clas_type);

    $data['status'] = $this->Student_counselling_model->getstatuslist_createpage();
    $data['main_content']    = 'student_counselling/create_new_counselling';
    $this->load->view('inc/template', $data);
  }

  public function get_class_section_wise_std_data(){
    $cls_section = $this->input->post('cls_section');
    list($class_id,$sectionId) = explode('_',$cls_section);
    $result = $this->Student_counselling_model->get_class_section_student_data($class_id,$sectionId);
    echo json_encode($result);
  }

  public function add_area_of_concern(){
    $data['main_content']    = 'student_counselling/area_of_concern';
    $this->load->view('inc/template', $data);
  }

  public function getconcernlist() {
    $result = $this->Student_counselling_model->getconcernlist();
    echo json_encode($result);
  }

  public function savecounsellingData() {
     // echo "<pre>"; print_r($_POST); die();
   echo $this->Student_counselling_model->save_counselling();

  }

  public function saveareaofconcern() {
   $result = $this->Student_counselling_model->insertareaofconcern();
   if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Added Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    redirect('student_counselling/student_counselling_controller/add_area_of_concern');
  }

  public function view_counselling() {
    $data['staffid'] = $this->authorization->getAvatarStakeHolderId();
    $data['counselor_name'] = $this->Student_counselling_model->getcounselor_name_createpage();
    $data['boards'] = $this->settings->getSetting('board');
    $data['status'] = $this->Student_counselling_model->getstatuslist_createpage();
    $data['main_content']    = 'student_counselling/view_counselling';
    $this->load->view('inc/template', $data);
  }

  public function getcounselling_report() {
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $date_type_select = $_POST['date_type_select'];
    $staffid = $_POST['staffid'];
    $counselor_name_search = $_POST['counselor_name_search'];
    
    $result = $this->Student_counselling_model->getcounselling_report($from_date, $to_date, $date_type_select, $staffid, $counselor_name_search);
    echo json_encode($result);
  }

  public function counselling_report() {
    $data['staffid'] = $this->authorization->getAvatarStakeHolderId();
    $clas_type = $this->Student_counselling_model->getview_studentwise_counselling_data_by_classtype($data['staffid']);
    $data['class_report'] = $this->Student_counselling_model->get_class_wise_student_list_data($clas_type);
    $data['main_content']    = 'student_counselling/counselling_count_report';
    $this->load->view('inc/template', $data);
  }

  public function get_counselling_count_by_cls_id() {
    $class_section_id = $_POST['class_section_id'];
    $data = $this->Student_counselling_model->getcounselling_count_byclsid($class_section_id);
    echo json_encode($data);
  }

  public function add_color_code() {
    $data['main_content']    = 'student_counselling/color_code';
    $this->load->view('inc/template', $data);
  }

  public function savecolorcode() {
   $result = $this->Student_counselling_model->insertcolorcode();
   if ($result) {
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    redirect('student_counselling/student_counselling_controller/add_color_code');
  }

  public function add_mode_of_session() {
    $data['main_content']    = 'student_counselling/mode_of_session';
    $this->load->view('inc/template', $data);
  }

  public function savemodeofsession() {
    $result = $this->Student_counselling_model->insertmodeofsession();
   if ($result) {
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    redirect('student_counselling/student_counselling_controller/add_mode_of_session');
  }

  public function deactivate_areaof_conconern_id() {
    $areaofconcern_id = $_POST['areaofconcern_id'];
    $success = $this->Student_counselling_model->deactivate_areaof_conconern_id($areaofconcern_id);
    echo json_encode($success);
  }

  public function activate_areaof_concern() {
    $areaofconcern_id = $_POST['areaofconcern_id'];
    $success = $this->Student_counselling_model->activate_areaof_concern($areaofconcern_id);
    echo json_encode($success);
  }

  public function getmodeofsession() {
    $result = $this->Student_counselling_model->getmodeofsession();
    echo json_encode($result);
  }

  public function deactivate_modeof_session() {
    $modeofsession_id = $_POST['modeofsession_id'];
    $success = $this->Student_counselling_model->deactivate_modeof_session($modeofsession_id);
    echo json_encode($success);
  }

  public function activate_modeof_session() {
    $modeofsession_id = $_POST['modeofsession_id'];
    $success = $this->Student_counselling_model->activate_modeof_session($modeofsession_id);
    echo json_encode($success);
  }

  public function getcolorlist() {
    $result = $this->Student_counselling_model->getcolorlist();
    echo json_encode($result);
  }

  public function deactivate_color_code() {
    $color_code_id = $_POST['color_code_id'];
    $success = $this->Student_counselling_model->deactivate_color_code($color_code_id);
    echo json_encode($success);
  }

  public function activate_color_code() {
    $color_code_id = $_POST['color_code_id'];
    $success = $this->Student_counselling_model->activate_color_code($color_code_id);
    echo json_encode($success);
  }

  public function add_status() {
    $data['main_content']    = 'student_counselling/add_status';
    $this->load->view('inc/template', $data);
  }

  public function savestatus() {
   $result = $this->Student_counselling_model->insertstatus();
   if ($result) {
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    redirect('student_counselling/student_counselling_controller/add_status');
  }

  public function getstatuslist() {
    $result = $this->Student_counselling_model->getstatuslist();
    echo json_encode($result);
  }

  public function deactivate_status_dec_id() {
    $status_id = $_POST['status_id'];
    $success = $this->Student_counselling_model->deactivate_status_dec_id($status_id);
    echo json_encode($success);
  }

   public function activate_stastus_id() {
    $status_id = $_POST['status_id'];
    $success = $this->Student_counselling_model->activate_status($status_id);
    echo json_encode($success);
  }

   public function add_referal_type() {
    $data['main_content']    = 'student_counselling/referral_type';
    $this->load->view('inc/template', $data);
  }

  public function savereferral_type() {
   $result = $this->Student_counselling_model->insertreferral_type();
   if ($result) {
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    redirect('student_counselling/student_counselling_controller/add_referal_type');
  }

  public function getreferral_type_list() {
    $result = $this->Student_counselling_model->getreferaltypelist();
    echo json_encode($result);
  }

  public function deactivate_referral_type() {
    $referraltype_id = $_POST['referraltype_id'];
    $success = $this->Student_counselling_model->deactivate_referral_type($referraltype_id);
    echo json_encode($success);
  }

  public function activate_referral_type() {
    $referraltype_id = $_POST['referraltype_id'];
    $success = $this->Student_counselling_model->activate_referral_type($referraltype_id);
    echo json_encode($success);
  }

  public function update_area_of_concern(){
   $id = $_POST['id'];
    $area_of_concern = $_POST['area_of_concern'];
    echo $this->Student_counselling_model->update_area_of_concern($id,$area_of_concern);
  }

  public function update_mode_of_session() {
    $id = $_POST['id'];
    $modeof_session = $_POST['modeof_session'];
    echo $this->Student_counselling_model->update_mode_of_session($id,$modeof_session);
  }

  public function update_addstatus() {
    $id = $_POST['id'];
    $addstatus_name = $_POST['addstatus_name'];
    echo $this->Student_counselling_model->update_addstatus($id,$addstatus_name);
  }

  public function update_referraltype() {
    $id = $_POST['id'];
    $referral_type = $_POST['referral_type'];
    echo $this->Student_counselling_model->update_referraltype($id,$referral_type);
  }

  public function update_color_code() {
    $id = $_POST['id'];
    $color_code_name = $_POST['color_code_name'];
    $colorcode_id = $_POST['colorcode_id'];
    echo $this->Student_counselling_model->update_color_code($id,$color_code_name,$colorcode_id);
  }

  public function student_report() {
    $data['staffid'] = $this->authorization->getAvatarStakeHolderId();
    $data['clas_type'] = $this->Student_counselling_model->getview_studentwise_counselling_data_by_classtype($data['staffid']);

    $configClassType = $this->settings->getSetting('classType');
    $classTypes = [];
   
    foreach ($configClassType as $key => $value) {
      foreach ($data['clas_type'] as $key => $val) {
        if($value->value == $val){
          array_push($classTypes, $value);
        }
      }
    }

    $data['classTypes'] = $classTypes;
    // echo "<pre>"; print_r($data['classTypes']); die();
    $data['modeofsession'] = $this->Student_counselling_model->getmodeofsession_createpage();
    $data['areaofconcern'] = $this->Student_counselling_model->getconcernList_createpage();
    $data['status'] = $this->Student_counselling_model->getstatuslist_createpage();
    $data['colorcode'] = $this->Student_counselling_model->getcolorlist_createpage();
    $data['referral_type'] = $this->Student_counselling_model->getreferaltypelist_createpage();
    $data['main_content']    = 'student_counselling/student_report';
    $this->load->view('inc/template', $data);
  }

  public function get_student_counselling_report() {
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $status_add = $_POST['status_add'];
    $program_wise = $_POST['program_wise'];
    $staffid = $_POST['staffid'];
    $area_of_concern = $_POST['area_of_concern'];
    $color_code = $_POST['color_code'];
    $referral_type = $_POST['referral_type'];
    $result = $this->Student_counselling_model->get_student_counselling_report($from_date, $to_date, $status_add, $program_wise, $staffid, $area_of_concern, $color_code, $referral_type);
    echo json_encode($result);
  }

  public function counselling_analytics() {
    $data['configClassType'] = $this->settings->getSetting('classType');
    $data['program_wise'] = $this->Student_counselling_model->getprogramwise_count();
    $data['main_content']    = 'student_counselling/counselling_report';
    $this->load->view('inc/template', $data);
  }

  public function student_wise_report() {
    $data['staffid'] = $this->authorization->getAvatarStakeHolderId();
    $clas_type = $this->Student_counselling_model->getview_studentwise_counselling_data_by_classtype($data['staffid']);
    $data['classSectionList'] = [];
    if(!empty($clas_type)){
     $data['classSectionList'] = $this->Student_counselling_model->getClassSectionNames($clas_type);
    }
    $data['all_names'] = $this->Student_counselling_model->get_all_staff_student_names($clas_type);

   
    $data['main_content']    = 'student_counselling/student_wise_report';
    $this->load->view('inc/template', $data);
  }

 public function get_studentwise_report() {
    $student_id = $_POST['student_id'];
    $staffid = $_POST['staffid'];
    $sectionId = $_POST['sectionId'];

    if (strpos($sectionId, '_') !== false) {
        list($class_id, $sectionId) = explode('_', $sectionId);

        $result = $this->Student_counselling_model->get_studentwise_report($student_id, $staffid, $class_id, $sectionId);

        if (!empty($result)) {
            foreach ($result as $key => $val) {
                if (!empty($val->boarding_type)) {
                    $val->boarding_type = $this->settings->getSetting('boarding')[$val->boarding_type];
                }
            }
        }

        echo json_encode($result);
    } else {
       $result = $this->Student_counselling_model->get_studentwise_report($student_id, $staffid, 0, 0);

        if (!empty($result)) {
            foreach ($result as $key => $val) {
                if (!empty($val->boarding_type)) {
                    $val->boarding_type = $this->settings->getSetting('boarding')[$val->boarding_type];
                }
            }
        }

        echo json_encode($result);
    }
}


  public function get_counselling_by_student_id() {
    $id = $_POST['id'];
    $staffid = $_POST['staffid'];
    $data = $this->Student_counselling_model->getcounselling_student_id($id, $staffid);

    echo json_encode($data);
  }

  public function access_controller() {
    $data['staff_designations'] = $this->Student_counselling_model->staff_designations();
    $data['classTypes']       =  $this->settings->getSetting('classType');
    $data['main_content']    = 'student_counselling/access_controller';
    $this->load->view('inc/template', $data);
  }

  public function save_access_control() {
   $result = $this->Student_counselling_model->insert_access_control();
   if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Added Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    redirect('student_counselling/student_counselling_controller/access_controller');
  }

  public function get_access_control() {
    $result = $this->Student_counselling_model->get_access_control();
    $config = $this->settings->getSetting('classType');
    foreach ($result as $key => $val) {
        foreach ($config as $k => $v) {
          if($val->class_type == $v->value){
            $val->class_type = $v->name;
          }
        }
    }
    echo json_encode($result);
  }

  public function delete_access_control_id() {
    $access_control_id = $_POST['access_control_id'];
    $success = $this->Student_counselling_model->delete_access_control_id($access_control_id);
    echo json_encode($success);
  }

   public function get_access_control_by_id() {
    $id = $_POST['id'];
    $result = $this->Student_counselling_model->get_access_control_by_id($id);
    echo json_encode($result);
  }

  public function update_access_control_id(){
    $edit_access_control_id = $_POST['edit_access_control_id'];
    $edit_class_type = $_POST['edit_class_type'];
    $edit_staff_designation = $_POST['edit_staff_designation'];
    $edit_counselling_view = $_POST['edit_counselling_view'];
    $edit_counselling_add = $_POST['edit_counselling_add'];
    $edit_counselling_comments = $_POST['edit_counselling_comments'];
    $edit_counselling_comments_view = $_POST['edit_counselling_comments_view'];
    echo $this->Student_counselling_model->update_access_control($edit_access_control_id,$edit_class_type,$edit_staff_designation,$edit_counselling_view,$edit_counselling_add,$edit_counselling_comments, $edit_counselling_comments_view);
  }

  public function check_staff_permission(){
    $staff_id = $_POST['staffid'];
    $result = $this->Student_counselling_model->check_staff_permission($staff_id);
    echo json_encode($result);

  }

  public function check_staff_comment_permission(){
    $staff_id = $_POST['staffid'];
    $result = $this->Student_counselling_model->check_staff_comment_permission($staff_id);
    echo json_encode($result);

  }

  public function check_staff_comment_view_permission(){
    $staff_id = $_POST['staffid'];
    $result = $this->Student_counselling_model->check_staff_comment_view_permission($staff_id);
    echo json_encode($result);

  }

   public function getcounselling_clone(){
    $result = $this->Student_counselling_model->get_clone_copy_createpage();
    // echo "<pre>"; print_r($result); die();
    echo json_encode($result);

  }

  public function get_clone_byId(){
    $counselling_id = $_POST['counselling_id'];
    // echo "<pre>"; print_r($counselling_id); die();
    echo json_encode($this->Student_counselling_model->get_counselling_ById($counselling_id));
  }

  public function downloadcounselling_Attachment(){
    $link = $_POST['file_path'];
    $file = explode("/", $link);
    $file_name =$_POST['file_name'];
    $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
    // echo '<pre>'; print_r($fname); die();
    $url = $this->filemanager->getFilePath($link);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download($fname, $data, TRUE);
  }

  public function getStudentDetails(){
    $data['staffid'] = $this->authorization->getAvatarStakeHolderId();
    $clas_type = $this->Student_counselling_model->getview_counselling_data_by_classtype($data['staffid']);

    $name = $_POST['name'];
    $stdData = $this->Student_counselling_model->getstdDataByName($name, $clas_type);
    echo json_encode($stdData);
  }

  public function weekly_report(){
    $data['staffid'] = $this->authorization->getAvatarStakeHolderId();
    $data['counselor_name'] = $this->Student_counselling_model->getcounselor_name_createpage();
    $clas_type = $this->Student_counselling_model->getview_counselling_data_by_classtype($data['staffid']);
    $data['main_content']    = 'student_counselling/weekly_report';
    $this->load->view('inc/template', $data);
  }

  public function get_weekly_report(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $staffid = $_POST['staffid'];
    
    $result = $this->Student_counselling_model->getweekly_report($from_date, $to_date, $staffid);
    echo json_encode($result);
  }

  public function delete_counselling_id(){
    $id = $_POST['id'];
    $result = $this->Student_counselling_model->delete_counselling_id($id);
   echo json_encode($result);
  }

  public function get_counselling_comments_status(){
    $id = $_POST['id'];
    $data = $this->Student_counselling_model->get_counselling_comments_status($id);
    echo json_encode($data);
  }

  public function save_counselling_comments()  {
     $result = $this->Student_counselling_model->save_counselling_comments();
   if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Added Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    redirect('student_counselling/student_counselling_controller/view_counselling');
  }

  public function comments_report(){
    $data['staffid'] = $this->authorization->getAvatarStakeHolderId();
    $data['counselor_name'] = $this->Student_counselling_model->getcounselor_name_createpage();
    $clas_type = $this->Student_counselling_model->getview_counselling_data_by_classtype($data['staffid']);
    $data['main_content']    = 'student_counselling/comments_report';
    $this->load->view('inc/template', $data);
  }

  public function generate_comments_report(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $staffid = $_POST['staffid'];
    
    $result = $this->Student_counselling_model->generate_comments_report($from_date, $to_date, $staffid);
    echo json_encode($result);
  }

  public function analytics_areaof_concern_count(){
    $program_wise_class = $_POST['program_wise_class'];
    $result = $this->Student_counselling_model->areaofconcern_count($program_wise_class);
    echo json_encode($result);
  }

  public function analytics_mode_of_session_count(){
    $program_wise_class = $_POST['program_wise_class'];
    $result = $this->Student_counselling_model->modeofsession_count($program_wise_class);
    echo json_encode($result);
  }

  public function analytics_status_count(){
    $program_wise_class = $_POST['program_wise_class'];
    $result = $this->Student_counselling_model->statuswise_count($program_wise_class);
    echo json_encode($result);
  }

  public function analytics_color_code_count(){
    $program_wise_class = $_POST['program_wise_class'];
    $result = $this->Student_counselling_model->colorcode_count($program_wise_class);
    echo json_encode($result);
  }

  public function get_class_section_wise_std_data_per_student(){
    $cls_section = $this->input->post('cls_section');
    list($class_id,$sectionId) = explode('_',$cls_section);

    $result = $this->Student_counselling_model->get_class_section_student_data_per_student($class_id,$sectionId);

    echo json_encode($result);
  }

  public function access_controller_staff() {
    $data['staff_designations'] = $this->Student_counselling_model->staff_designations();
    $data['classTypes']       =  $this->settings->getSetting('classType');
    $data['main_content']    = 'student_counselling/access_controller_staff';
    $this->load->view('inc/template', $data);
  }

  public function save_access_control_staff() {
   $result = $this->Student_counselling_model->insert_access_control_staff();
   if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Added Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    redirect('student_counselling/student_counselling_controller/access_controller_staff');
  }

  public function get_access_control_staff() {
    $result = $this->Student_counselling_model->get_staff_access_control();
    echo json_encode($result);
  }

  public function get_access_control_by_staff_id() {
    $id = $_POST['id'];
    $result = $this->Student_counselling_model->get_access_control_by_staff_id($id);
    echo json_encode($result);
  }

  public function update_access_control_staff_id(){
    $edit_access_control_id = $_POST['edit_access_control_id'];
    $edit_staff_designation = $_POST['edit_staff_designation'];
    $view_all_staff_sessions_edit = $_POST['view_all_staff_sessions_edit'];
    $view_staff_added_by_me_edit = $_POST['view_staff_added_by_me_edit'];
    $add_staff_counselling_edit = $_POST['add_staff_counselling_edit'];
    $add_comments_staff_edit = $_POST['add_comments_staff_edit'];
    $view_comments_staff_edit = $_POST['view_comments_staff_edit'];
    echo $this->Student_counselling_model->update_staff_access_control($edit_access_control_id,$edit_staff_designation,$view_all_staff_sessions_edit,$view_staff_added_by_me_edit,$add_staff_counselling_edit, $add_comments_staff_edit, $view_comments_staff_edit);
  }

  public function delete_staff_access_control_id() {
    $access_control_id = $_POST['access_control_id'];
    $success = $this->Student_counselling_model->delete_staff_access_control_id($access_control_id);
    echo json_encode($success);
  }


}?>