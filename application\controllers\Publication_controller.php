<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  08 April 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Publication_controller extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->load->model('publication_model');
    $this->config->load('form_elements');
    $this->load->library('filemanager');
  }

 // Publications
  public function publications(){
    $data['publishData'] = $this->publication_model->getPublishData();
    $data['main_content']    = 'publications/index';
    $this->load->view('inc/template', $data);  
  }

  public function publications_add(){ 
    $this->load->model('class_section');
    $data['classes']  =$this->class_section->getAllClassess();
    $data['class_section'] = $this->class_section->getAllClassSections();
    //echo "<pre>";print_r($data['class_section']);die();
    $data['main_content']    = 'publications/add';
    $this->load->view('inc/template', $data);
  }

  //s3bucket for uploading photo
  public function s3FileUpload($file) {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'profile');
  }

  public function submit_publications(){
		$publications = $this->publication_model->insertPublication($this->s3FileUpload($_FILES['photo']));
		if ($publications) {
			 $this->session->set_flashdata('flashSuccess', 'Successful Published.');
          	 redirect('publication_controller/publications');
		}else{
			$this->session->set_flashdata('flashError', 'Something Wrong.');
            redirect('publication_controller/publications');
		}
  }
  
  public function viewPublication($id){
    $data['publishData'] = $this->publication_model->getPublicationById($id);
    $data['Students'] = array();
    $data['Classes'] = array();
    if($data['publishData']->user_type == "Student") {
      $data['Students'] = $this->publication_model->getStudents($id);
    } else if($data['publishData']->user_type == "Class"){
      $data['Classes'] = $this->publication_model->getClasses($id);
    }
    $data['main_content']    = 'publications/view';
    $this->load->view('inc/template', $data);
  }

  public function deletePublication($id){
    $status =(int) $this->publication_model->deletePublication($id);
		if ($status) {
			 $this->session->set_flashdata('flashSuccess', 'Successful Deleted Publication.');
          	 redirect('publication_controller/publications');
		}else{
			$this->session->set_flashdata('flashError', 'Something Went Wrong.');
            redirect('publication_controller/publications');
		}
  }

}