  <?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  12 March 2018
 *
 * Description: Model class for Class Master table
 *
 * Requirements: PHP5 or above
 *
 */

require_once APPPATH.'third_party/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;


class Fee_report extends CI_Controller {
	function __construct() {
		  parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      $this->load->model('report/fee_report_model');
      $this->load->model('report/fee_summary_model');
      $this->load->model('report/transportation_report_model');
	}


  public function index() {
    $postData = $this->input->post();
    $filterMode = empty($postData);
    //Supported filters by daily report
    $reportFilters = [
      'class', 'from_to_date', 'payment_mode',  'admission_type',  'isRTE', 'collectedBy', 'donors', 'medium', 'board'
    ];

    //Supported filters by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedFilters = $feeConfig['supportedFilters'];
    $data['fee_type'] = $feeConfig['fee_type'];
    $finalFilters = array();
    foreach ($reportFilters as $rf) {
      foreach ($schoolSupportedFilters as $ssf) {
        if ($rf == $ssf) {
          $finalFilters[] = $rf;
          break;
        }
      }
    }
    $data['reportColumns'] = [
        ['name' => 'slNo', 'displayName' => 'Sl #'],
        ['name' => 'date','displayName' => 'Date'],
        ['name' => 'receiptNo','displayName' => 'Receipt No'],
        ['name' => 'stdName','displayName' => 'Name'],
        ['name' => 'className','displayName' => 'Class'],
        // ['name' => 'installment','displayName' => 'Installments'],
        ['name' => 'paidAmount','displayName' => 'Collected Amount'],
        ['name' => 'concession','displayName' => 'Concession'],
        ['name' => 'pos','displayName' => 'POS'],
        ['name' => 'fine','displayName' => 'Fine'],
        ['name' => 'medium','displayName' => 'Medium'],
        ['name' => 'discount','displayName' => 'Discount'],
        ['name' => 'remarks','displayName' => 'Remarks'],
        ['name' => 'pType','displayName' => 'Payment Type'],
        ['name' => 'collectedBy','displayName' => 'Collected By'],
      ];
  
    //Supported columns by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedColumns = $feeConfig['supportedColumns'];
    $finalColumns = array();
    foreach ($data['reportColumns'] as $rc) {
      foreach ($schoolSupportedColumns as $ssc) {
        if ($rc['name'] == $ssc) {
          $finalColumns[] = $rc;
          break;
        }
      }
    }

    $data['classes'] =  $this->fee_report_model->get_Allclasses();   
    $data['medium'] = $this->settings->getSetting('medium');
    $data['boarding'] = $this->settings->getSetting('boarding');
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['board'] = $this->settings->getSetting('board');
    $data['donors'] = $this->fee_report_model->get_Alldonors();
    $data['payment_mode'] = $feeConfig['allowed_payment_modes'];
    $data['feeCollectedBy'] = $this->fee_report_model->get_feeCollectedName();
    $data['finalFilters'] = $finalFilters;
    $data['SelectedFtype'] = $data['fee_type'];
    $data['selectedColumns'] = $this->input->post('selectedColumns');
    $classId = $this->input->post('class_name');
    $data['finalColumns'] = $finalColumns;
    $data['finalColumnsJSON'] = json_encode($finalColumns);
    //$challansDetails = $this->fee_report_model->serachGeneration($classId);
    //$data['result_Data'] = $challansDetails;
    $data['main_content'] = 'reports/fees/daily';
    $this->load->view('inc/template', $data);
  }

  public function searchFeeTypewiseDaily(){
    $feeType = $this->input->post('fee_type');
    if ($feeType == 1) {
      $this->index($feeType);
    }elseif($feeType == 2){
      $this->_TransportwiseDaily($feeType);
    }elseif($feeType == 3){
      $this->_FacilitiesDaily($feeType);
    }
  }

  public function generate_dailyReport(){
  
    $selectedColumns = $this->input->post('selectedColumns'); 
    $finalColumns = json_decode($this->input->post('finalColumnsJSON'));
    $viewColumns = array ();
    if (!empty($selectedColumns)) {
      foreach ($selectedColumns as $sc) {
        foreach ($finalColumns as $rc) {
          if ($rc->name == $sc) {
            $viewColumns[] = $rc;
            break;
          }
        }
      }   
    } else {
      $viewColumns = $finalColumns;
    }

   
    $clsId = $this->input->post('clsId');
    $admission_type = $this->input->post('admission_type');
    $paymentModes = $this->input->post('paymentModes');
    $fee_type = $this->input->post('fee_type');
    $mediumId = $this->input->post('mediumId');
    $donorsId = $this->input->post('donorsId');
   // $boardingId = $this->input->post('boardingId');
    $rte_nrteId = $this->input->post('rte_nrteId');
    $created_byId = $this->input->post('created_byId');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $stopId = $this->input->post('stopId');
    $routeId = $this->input->post('routeId');
    $itemId = $this->input->post('itemId');

    $medium = $this->settings->getSetting('medium');
    if ($fee_type == 1) {
      $dailyAcademicCollection = $this->fee_report_model->serachGeneration($clsId,$admission_type,$paymentModes,$mediumId,$donorsId,$rte_nrteId,$created_byId,$from_date,$to_date);
      $dailyFeeCollection = $dailyAcademicCollection;
       $feeConfig = $this->settings->getSetting('fees');
      $payment_mode = $feeConfig['allowed_payment_modes'];
      $padiAmount = 0;
      $totalConcession = 0;
      $fAmount = 0;
      $pos = 0;
      $dAmount = 0;
      $cAmount = 0;
      foreach ($dailyFeeCollection as $result){
        $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
          if (!empty($reqTemp)) {
          $padiAmount +=$result->amount_paid;
          $fAmount +=$result->fine_amount;
          $pos +=$result->card_charge_amount;
          $dAmount +=$result->discount_amount;
          $cAmount = $padiAmount + $fAmount + $pos - $dAmount;
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'concession');
          if (!empty($reqTemp)) {
          $totalConcession +=$result->total_concession;
        }
      } 
      
    }elseif ($fee_type == 2) {
      $transport = $this->fee_report_model->get_Student_TransportationDailyfeeDetails($clsId,$stopId,$routeId,$from_date,$to_date); 
      $dailyFeeCollection = $transport;

      $feeConfig = $this->settings->getSetting('fees');
      $payment_mode = $feeConfig['allowed_payment_modes'];
      $padiAmount = 0;
      $totalConcession = 0;
      $fAmount = 0;
      $pos = 0;
      $dAmount = 0;
      $cAmount = 0;
      foreach ($dailyFeeCollection as $result){
        $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
          if (!empty($reqTemp)) {
          $padiAmount +=$result->amount_paid;
          $cAmount = $padiAmount;
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'concession');
          if (!empty($reqTemp)) {
          $totalConcession +=$result->total_concession;
        }
      } 
    }elseif ($fee_type == 3) {
      $padiAmount = 0;
      $cAmount = 0;
      $totalConcession = 0;
      $facility = $this->fee_report_model->get_Student_FacilityDailyFeeDetails($clsId,$itemId,$from_date,$to_date);
      $dailyFeeCollection = $facility;
      foreach ($dailyFeeCollection as $key => $fac) {
        $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
          if (!empty($reqTemp)) {
          $padiAmount +=$fac->amount_paid;
          $cAmount = $padiAmount;
        }
      }
    }
    
     
    
    $template = "";

    $template .= '<div>
      <label>Total Collected Amount : '.$cAmount.'</label><br>
      <label>Total Concession : '.$totalConcession.'</label><br>
    </div>';
    $template.="<span class='help-block pull-right'>Collected amount includes discount, POS charge and fine amount. </span>";
    $template .="<table class='table table-bordered'>";
    $template .="<thead>";
    $template .="<tr>";

    $reqTemp  = $this->requireColumns($viewColumns, 'slNo');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'date');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'receiptNo');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'stdName');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'className');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'medium');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'stop');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'items');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }


    $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'concession');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'pos');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'fine');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'discount');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'pType');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'remarks');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'collectedBy');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $template .="</tr>";
    $template .="</thead>";
    $template .="<tbody>";
    $template .="</tbody>";
    $i = 1;
    foreach ($dailyFeeCollection as $result){
      $template .="<tr>";

      $reqTemp  = $this->requireColumns($viewColumns, 'slNo');
        if (!empty($reqTemp)) {
        $template .='<td>'.$i++.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'date');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result->date.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'receiptNo');
        if (!empty($reqTemp)) {
         $template .='<td>'.$result->receipt_number.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'stdName');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result->student_name.'</td>';
      }


      $reqTemp  = $this->requireColumns($viewColumns, 'className');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result->class_name.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'medium');
        if (!empty($reqTemp)) {
        foreach ($medium as $key => $med) {
          if ($result->medium == $key) { 
          $template .='<td>'.$med.'</td>';
          }
        }
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'stop');
      if (!empty($reqTemp)) {
        $template .='<td>'.$result->stopName.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'items');
      if (!empty($reqTemp)) {
        $template .='<td>'.$result->item_name.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
        if (!empty($reqTemp)) {
        // $cAmount = $result->amount_paid + $result->fine_amount +  $result->card_charge_amount - $result->discount_amount;
        $template .='<td>'.$result->amount_paid.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'concession');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result->total_concession.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'pos');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result->card_charge_amount.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'fine');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result->fine_amount.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'discount');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result->discount_amount.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'pType');
        if (!empty($reqTemp)) {
          foreach ($payment_mode as $key => $type) {
            if ($type['value'] == $result->payment_type) {
              if ($type['reconcilation_reqd'] == TRUE) {
                if ($result->reconciliation_status == 1){
                 $template .='<td>'.strtoupper($type['name']) . ' <span style="color:red; font-weight:bold"> (N/C)</span>'.'</td>';
                }elseif($result->reconciliation_status == 2){
                  $template .='<td>'.strtoupper($type['name']) . ' <span style="font-weight:bold"> (C)</span>'.'</td>';
                }
              }else{
                $template .='<td>'.strtoupper($type['name']).'</td>';
              } 
              
            }
          } 
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'remarks');
        if (!empty($reqTemp)) {
          $template .='<td>'.$result->remarks.'</td>';
        }

      $reqTemp  = $this->requireColumns($viewColumns, 'collectedBy');
      if (!empty($reqTemp)) {
        $template .='<th>'.$result->staff_name.'</th>';
      }

    }
    $template .="</tr>";
    $template .="</tbody>";
    $template .="</table";  
    print($template);
    // echo json_encode($challansDetails);
  }

  private function _TransportwiseDaily($feeType){
    $postData = $this->input->post();
    $filterMode = empty($postData);
    //Supported filters by daily report
    $reportFilters = [
      'class', 'from_to_date','stop', 'route'
    ];
    //Supported filters by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedFilters = $feeConfig['supportedFilters'];
    $data['fee_type'] = $feeConfig['fee_type'];
    $finalFilters = array();
    foreach ($reportFilters as $rf) {
      foreach ($schoolSupportedFilters as $ssf) {
        if ($rf == $ssf) {
          $finalFilters[] = $rf;
          break;
        }
      }
    }
    $data['reportColumns'] = [
        ['name' => 'slNo', 'displayName' => 'Sl #'],
        ['name' => 'stdName','displayName' => 'Name'],
        ['name' => 'className','displayName' => 'Class'],
        ['name' => 'stop','displayName' => 'Stop'],
        ['name' => 'route','displayName' => 'Route'],
        // ['name' => 'feeAmount','displayName' => 'Fee Amount'],
        ['name' => 'paidAmount','displayName' => 'Collected Amount'],
        // ['name' => 'balance','displayName' => 'Balance'],
      ];
    //Supported columns by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedColumns = $feeConfig['supportedColumns'];
    $finalColumns = array();
    foreach ($data['reportColumns'] as $rc) {
      foreach ($schoolSupportedColumns as $ssc) {
        if ($rc['name'] == $ssc) {
          $finalColumns[] = $rc;
          break;
        }
      }
    }
    $data['classes'] =  $this->fee_report_model->get_Allclasses();   
    $data['stops'] = $this->transportation_report_model->getAllStops();
    $data['routes'] = $this->transportation_report_model->getAllRouts();
    $data['finalFilters'] = $finalFilters;
    $data['selectedColumns'] = $this->input->post('selectedColumns');
    $classId = $this->input->post('class_name');
    $data['finalColumns'] = $finalColumns;
    $data['SelectedFtype'] = $feeType;
    $data['finalColumnsJSON'] = json_encode($finalColumns);
    $data['main_content'] = 'reports/fees/daily';
    $this->load->view('inc/template', $data);
  }


  private function _FacilitiesDaily($feeType){
    $postData = $this->input->post();
    $filterMode = empty($postData);
    //Supported filters by daily report
    $reportFilters = [
      'class', 'items','from_to_date'
    ];
    //Supported filters by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedFilters = $feeConfig['supportedFilters'];
    $data['fee_type'] = $feeConfig['fee_type'];
    $finalFilters = array();
    foreach ($reportFilters as $rf) {
      foreach ($schoolSupportedFilters as $ssf) {
        if ($rf == $ssf) {
          $finalFilters[] = $rf;
          break;
        }
      }
    }
    $data['reportColumns'] = [
        ['name' => 'slNo', 'displayName' => 'Sl #'],
        ['name' => 'stdName','displayName' => 'Name'],
        ['name' => 'className','displayName' => 'Class'],
        ['name' => 'items','displayName' => 'Item'],
        ['name' => 'paidAmount','displayName' => 'Paid'],
      ];
    //Supported columns by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedColumns = $feeConfig['supportedColumns'];
    $finalColumns = array();
    foreach ($data['reportColumns'] as $rc) {
      foreach ($schoolSupportedColumns as $ssc) {
        if ($rc['name'] == $ssc) {
          $finalColumns[] = $rc;
          break;
        }
      }
    }
    $data['classes'] =  $this->fee_report_model->get_Allclasses();   
    $data['items'] =  $this->fee_report_model->getAllItems();
    $data['finalFilters'] = $finalFilters;
    $data['selectedColumns'] = $this->input->post('selectedColumns');
    $classId = $this->input->post('class_name');
    $data['finalColumns'] = $finalColumns;
    $data['SelectedFtype'] = $feeType;
    $data['finalColumnsJSON'] = json_encode($finalColumns);
    $data['main_content'] = 'reports/fees/daily';
    $this->load->view('inc/template', $data);
  }


  private function requireColumns($filterArray, $filter) {
    foreach ($filterArray as $f) {
      if ($f->name == $filter)
        return $f;
    }
    return array();
  }


// Student wise
  public function searchFeeTypewise(){
    $feeType = $this->input->post('fee_type');
    if ($feeType == 1) {
      $this->studentWiseAggregate($feeType);
    }elseif($feeType == 2){
      $this->_TransportwiseAggregate($feeType);
    }elseif($feeType == 3){
      $this->_FacilitiesAggregate($feeType);
    }
  }

  public function studentWiseAggregate($feeType=""){
    $postData = $this->input->post();
    $filterMode = empty($postData);
    //Supported filters by daily report
    $reportFilters = [
      'class',  'payment_options', 'admission_type',  'isRTE', 'collectedBy', 'donors', 'medium', 'board'
    ];
    //Supported filters by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedFilters = $feeConfig['supportedFilters'];
    $data['fee_type'] = $feeConfig['fee_type'];
    $finalFilters = array();
    foreach ($reportFilters as $rf) {
      foreach ($schoolSupportedFilters as $ssf) {
        if ($rf == $ssf) {
          $finalFilters[] = $rf;
          break;
        }
      }
    }
    $data['reportColumns'] = [
        ['name' => 'slNo', 'displayName' => 'Sl #'],
        ['name' => 'stdName','displayName' => 'Name'],
        ['name' => 'className','displayName' => 'Class'],
        ['name' => 'feeAmount','displayName' => 'Amount to be collected'],
        ['name' => 'installment','displayName' => 'Installments'],
        ['name' => 'paidAmount','displayName' => 'Collected Amount'],
        ['name' => 'concession','displayName' => 'Concession'],
        ['name' => 'pos','displayName' => 'POS'],
        ['name' => 'fine','displayName' => 'Fine'],
        ['name' => 'medium','displayName' => 'Medium'],
        ['name' => 'discount','displayName' => 'Discount'],
        ['name' => 'balance','displayName' => 'Balance'],
      ];
  
    //Supported columns by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedColumns = $feeConfig['supportedColumns'];
    $finalColumns = array();
    foreach ($data['reportColumns'] as $rc) {
      foreach ($schoolSupportedColumns as $ssc) {
        if ($rc['name'] == $ssc) {
          $finalColumns[] = $rc;
          break;
        }
      }
    }

    $data['classes'] =  $this->fee_report_model->get_Allclasses();   
    $data['medium'] = $this->settings->getSetting('medium');
    $data['boarding'] = $this->settings->getSetting('boarding');
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['board'] = $this->settings->getSetting('board');
    $data['donors'] = $this->fee_report_model->get_Alldonors();
    $data['payment_mode'] = $feeConfig['allowed_payment_modes'];
    $data['feeCollectedBy'] = $this->fee_report_model->get_feeCollectedName();
    $data['finalFilters'] = $finalFilters;
    $data['selectedColumns'] = $this->input->post('selectedColumns');
    $classId = $this->input->post('class_name');
    $data['finalColumns'] = $finalColumns;
    $data['SelectedFtype'] = $feeType;
    $data['finalColumnsJSON'] = json_encode($finalColumns);
    $data['main_content'] = 'reports/fees/aggregate';
    $this->load->view('inc/template', $data);
  }

  public function generate_aggregateStudentReport(){

    $fee_type = $this->input->post('fee_type');
    $selectedColumns = $this->input->post('selectedColumns'); 
    $finalColumns = json_decode($this->input->post('finalColumnsJSON'));
    $viewColumns = array ();
    if (!empty($selectedColumns)) {
      foreach ($selectedColumns as $sc) {
        foreach ($finalColumns as $rc) {
          if ($rc->name == $sc) {
            $viewColumns[] = $rc;
            break;
          }
        }
      }   
    } else {
      $viewColumns = $finalColumns;
    }
  
    $clsId = $this->input->post('clsId');
    $admission_type = $this->input->post('admission_type');
    $paymentModes = $this->input->post('paymentModes');
    $fee_type = $this->input->post('fee_type');
    $mediumId = $this->input->post('mediumId');
    $donorsId = $this->input->post('donorsId');
   // $boardingId = $this->input->post('boardingId');
    $rte_nrteId = $this->input->post('rte_nrteId');
    $created_byId = $this->input->post('created_byId');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $stopId = $this->input->post('stopId');
    $routeId = $this->input->post('routeId');
    $itemId = $this->input->post('itemId');
    $payment_options = $this->input->post('payment_options');
    $medium = $this->settings->getSetting('medium');
    $feeConfig = $this->settings->getSetting('fees');
    $feeInstallments = $feeConfig['fee_installment'];
  
    if ($fee_type == 1) {
      $feeData = $this->fee_report_model->get_Student_feeDetails($clsId,$admission_type,$paymentModes,$mediumId,$donorsId,$rte_nrteId,$created_byId);
      $stdWiseFeeTotal = $this->fee_report_model->getAllStudentwisefAmount();
      foreach ($feeData as $stdId => &$val) {
        $val['tFeeAmount'] = !empty($stdWiseFeeTotal[$stdId]->total_fee)? $stdWiseFeeTotal[$stdId]->total_fee : 0;
      }
   
      $fullPaid = array();
      $partial = array();
      $balance = array();
      foreach ($feeData as $key => $data) {
        if ($data['pStatus'] == 1 && !empty($data['recon_status'])) {
          $fullPaid[] = $data;
          $totalstdCount = count($data);
        }elseif ($data['pStatus'] == 2) {
          $partial[] = $data;
          $totalstdCount = count($data);
        }elseif($data['pStatus'] == '' || $data['recon_status'] == '') {
          $balance[] = $data;
          $totalstdCount = count($data);
        }
      }
 
      if ($payment_options == '1') {
        $data['aggregate'] = $fullPaid;
        $TotalNumberStd = count($fullPaid);
      }elseif($payment_options == '2'){
        $data['aggregate'] = $partial;
        $TotalNumberStd = count($partial);
      }elseif($payment_options == ''){
        $data['aggregate'] = $balance;
        $TotalNumberStd = count($balance);
      }elseif($payment_options == '0'){
        $data['aggregate'] = $feeData;
        $TotalNumberStd = count($feeData);
      }

    }elseif ($fee_type == 2) {
      $transport = $this->fee_report_model->get_Student_TransportationfeeDetails($clsId,$stopId,$routeId);
      foreach ($transport as $key => &$trans) {
        $trans['balAmount'] = $trans['tFeeAmount'] - $trans['collceted_amount'];
      }
      $data['aggregate'] =$transport;
      $TotalNumberStd = count($transport);

    }elseif ($fee_type == 3) {
      $facility = $this->fee_report_model->get_Student_FacilityfeeDetails($clsId,$itemId);
      $data['aggregate'] = $facility; 
      $TotalNumberStd = count($facility);
    }
    //echo "<pre>"; print_r($data['aggregate']); die();
    $template = "";
    $template .="Total Number of Student <strong>". $TotalNumberStd."</strong>";
    $template.="<span class='help-block'>Collected amount includes discount, POS charge and fine amount. </span>";
    $template .="<table class='table table-condensed table-bordered'>";
    $template .= "<thead>";
    $template .= "<tr>";
    $reqTemp  = $this->requireColumns($viewColumns, 'feeAmount');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

   
    $reqTemp  = $this->requireColumns($viewColumns, 'installment');
    if (!empty($reqTemp)) {
      foreach ($feeInstallments as $insName) {
       $template .='<th>'.$insName['name'].'</th>';
      }
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'concession');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'discount');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'pos');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'fine');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }


    $reqTemp  = $this->requireColumns($viewColumns, 'balance');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }
    $template .= "<tr>";
    $template .= "</thead>";
    $template .= "<tbody>";
    $template .= "<tr>";

    $tFeeAmount = 0;
    $padiAmount = 0;
    $totalConcession = 0;
    $totalBalance = 0;
    $totalFineAmount = 0;
    $totalDiscount = 0;
    $totalCardAmount = 0;
    $installments = 0;

    $insTAmt = array();
    $reqTemp  = $this->requireColumns($viewColumns, 'installment');
    if (!empty($reqTemp)) {
      foreach ($feeInstallments as $ColumnsName) {
        if (!array_key_exists($ColumnsName['name'], $insTAmt)) {
          $insTAmt[$ColumnsName['name']]= 0;
        }
        foreach ($data['aggregate'] as $result) {
          if (!empty($result['installment'])) {
            foreach ($result['installment'] as $columns => $value) {
              if ($ColumnsName['name'] == $columns) {
                $insTAmt[$ColumnsName['name']] +=$value;
              }
            }
          }else{
            if ($payment_options !=0)
            $insTAmt[$ColumnsName['name']] = 0;
          }
        }
      }
    }
    $cAmount ="";
    foreach ($data['aggregate'] as  $result){

      if (!empty($result)) {
        $reqTemp  = $this->requireColumns($viewColumns, 'feeAmount');
        if (!empty($reqTemp)) {
           $tFeeAmount += $result['tFeeAmount'];
        }

       
        $reqTemp  = $this->requireColumns($viewColumns, 'concession');
        if (!empty($reqTemp)) {
          $totalConcession +=$result['tConc'];
        }
        
        $reqTemp  = $this->requireColumns($viewColumns, 'discount');
        if (!empty($reqTemp)) {
          $totalDiscount += $result['dAmount'];
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'pos');
        if (!empty($reqTemp)) {
          $totalCardAmount +=$result['cardAmount'];
        }

        $reqTemp  = $this->requireColumns($viewColumns, 'fine');
        if (!empty($reqTemp)) {
          $totalFineAmount += $result['fineAmt'];
        }
        
        $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
        if (!empty($reqTemp)) {

          $padiAmount +=$result['collceted_amount'];
          //$cAmount = $padiAmount + $totalFineAmount + $totalCardAmount - $totalDiscount;
        }

        $totalBalance = ($tFeeAmount + $totalCardAmount + $totalFineAmount)   - $padiAmount - $totalConcession - $totalDiscount;
        // $TotalNumberStd = count($result);
      }
    }

      $reqTemp  = $this->requireColumns($viewColumns, 'feeAmount');
      if (!empty($reqTemp)) {
        $template .='<td>'.$tFeeAmount.'</td>';
      }

      
      $reqTemp  = $this->requireColumns($viewColumns, 'installment');
      if (!empty($reqTemp)) {
        foreach ($insTAmt as $insName => $amt) {
         $template .='<td>'.$amt.'</td>';
        }   
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'concession');
      if (!empty($reqTemp)) {
        $template .='<td>'.$totalConcession.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'discount');
      if (!empty($reqTemp)) {
        $template .='<td>'.$totalDiscount.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'pos');
      if (!empty($reqTemp)) {
        $template .='<td>'.$totalCardAmount.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'fine');
      if (!empty($reqTemp)) {
        $template .='<td>'.$totalFineAmount.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
      if (!empty($reqTemp)) {
          $template .='<td>'.$padiAmount.'</td>';
      }


      $reqTemp  = $this->requireColumns($viewColumns, 'balance');
      if (!empty($reqTemp)) {
        $template .='<td>'.$totalBalance.'</td>';
      }
           
    $template .= "<tr>";
    $template .= "</tbody>";
    $template .="</table>";

    
    $template .="<table class='table table-bordered'>";
   
    $template .="<thead>";
    $template .="<tr>";
    
    $reqTemp  = $this->requireColumns($viewColumns, 'slNo');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'stdName');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'className');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'stop');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'items');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'medium');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'feeAmount');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'installment');
    if (!empty($reqTemp)) {
      foreach ($feeInstallments as $ColumnsName) {
        $template .='<th>'.$ColumnsName['name'].'</th>';
      }
    }

   
    $reqTemp  = $this->requireColumns($viewColumns, 'concession');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'discount');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'pos');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'fine');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $reqTemp  = $this->requireColumns($viewColumns, 'balance');
    if (!empty($reqTemp)) {
      $template .='<th>'.$reqTemp->displayName.'</th>';
    }

    $template .="</tr>";
    $template .="</thead>";
    $template .="<tbody>";
    $i = 1;
    foreach ($data['aggregate'] as $result){
      //echo "<pre>"; print_r($result); die();
      $template .="<tr>";

      $reqTemp  = $this->requireColumns($viewColumns, 'slNo');
        if (!empty($reqTemp)) {
        $template .='<td>'.$i++.'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'stdName');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['stdName'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'className');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['clsName'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'stop');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['stopName'].'</td>';
      }
      
      $reqTemp  = $this->requireColumns($viewColumns, 'items');
        if (!empty($reqTemp)) {
          $template .='<td>';
          foreach ($result['feeFacility'] as $key => $itemsName) {
           $template .=$itemsName['item_name'].', ';
          }
          $template .= '</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'medium');
        if (!empty($reqTemp)) {
        foreach ($medium as $key => $med) {
          if ($result['medium'] == $key) { 
          $template .='<td>'.$med.'</td>';
          }
        }
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'feeAmount');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['tFeeAmount'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'installment');
      if (!empty($reqTemp)) {
        foreach ($feeInstallments as $ColumnsName) {
          if (!empty($result['installment'])) {
            foreach ($result['installment'] as $columns => $value) {
              if ($ColumnsName['name'] == $columns) {
                $template .='<td>'.$value.'</td>';
              }
            }
          }else{
            $template .='<td>'.'0'.'</td>';
          }
        }
      }
      $reqTemp  = $this->requireColumns($viewColumns, 'concession');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['tConc'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'discount');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['dAmount'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'pos');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['cardAmount'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'fine');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['fineAmt'].'</td>';
      }

     

      $reqTemp  = $this->requireColumns($viewColumns, 'paidAmount');
        if (!empty($reqTemp)) {
        $template .='<td>'.$result['collceted_amount'].'</td>';
      }

      $reqTemp  = $this->requireColumns($viewColumns, 'balance');
        if (!empty($reqTemp)) {

        $totalBalance =($result['tFeeAmount'] + $result['cardAmount'] + $result['fineAmt'])  -  $result['collceted_amount']  - $result['tConc']- $result['dAmount'] ;

        $template .='<td>'.$totalBalance.'</td>';
      }
    }

    $template .='</tr>';
    $template .='</tbody>';
    $template .="</table";  
    print($template);
  }



  private function _TransportwiseAggregate($feeType){
    $postData = $this->input->post();
    $filterMode = empty($postData);
    //Supported filters by daily report
    $reportFilters = [
      'class', 'stop', 'route'
    ];
    //Supported filters by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedFilters = $feeConfig['supportedFilters'];
    $data['fee_type'] = $feeConfig['fee_type'];
    $finalFilters = array();
    foreach ($reportFilters as $rf) {
      foreach ($schoolSupportedFilters as $ssf) {
        if ($rf == $ssf) {
          $finalFilters[] = $rf;
          break;
        }
      }
    }
    $data['reportColumns'] = [
        ['name' => 'slNo', 'displayName' => 'Sl #'],
        ['name' => 'stdName','displayName' => 'Name'],
        ['name' => 'className','displayName' => 'Class'],
        ['name' => 'stop','displayName' => 'Stop'],
        ['name' => 'route','displayName' => 'Route'],
        ['name' => 'feeAmount','displayName' => 'Fee Amount'],
        ['name' => 'paidAmount','displayName' => 'Paid'],
        ['name' => 'balance','displayName' => 'Balance'],
      ];
    //Supported columns by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedColumns = $feeConfig['supportedColumns'];
    $finalColumns = array();
    foreach ($data['reportColumns'] as $rc) {
      foreach ($schoolSupportedColumns as $ssc) {
        if ($rc['name'] == $ssc) {
          $finalColumns[] = $rc;
          break;
        }
      }
    }
    $data['classes'] =  $this->fee_report_model->get_Allclasses();   
    $data['stops'] = $this->transportation_report_model->getAllStops();
    $data['routes'] = $this->transportation_report_model->getAllRouts();
    $data['finalFilters'] = $finalFilters;
    $data['selectedColumns'] = $this->input->post('selectedColumns');
    $classId = $this->input->post('class_name');
    $data['finalColumns'] = $finalColumns;
    $data['SelectedFtype'] = $feeType;
    $data['finalColumnsJSON'] = json_encode($finalColumns);
    $data['main_content'] = 'reports/fees/aggregate';
    $this->load->view('inc/template', $data);
  }


  private function _FacilitiesAggregate($feeType){
    $postData = $this->input->post();
    $filterMode = empty($postData);
    //Supported filters by daily report
    $reportFilters = [
      'class', 'items'
    ];
    //Supported filters by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedFilters = $feeConfig['supportedFilters'];
    $data['fee_type'] = $feeConfig['fee_type'];
    $finalFilters = array();
    foreach ($reportFilters as $rf) {
      foreach ($schoolSupportedFilters as $ssf) {
        if ($rf == $ssf) {
          $finalFilters[] = $rf;
          break;
        }
      }
    }
    $data['reportColumns'] = [
        ['name' => 'slNo', 'displayName' => 'Sl #'],
        ['name' => 'stdName','displayName' => 'Name'],
        ['name' => 'className','displayName' => 'Class'],
        ['name' => 'items','displayName' => 'Item'],
        ['name' => 'paidAmount','displayName' => 'Paid'],
      ];
    //Supported columns by school
    $feeConfig = $this->settings->getSetting('fees');
    $schoolSupportedColumns = $feeConfig['supportedColumns'];
    $finalColumns = array();
    foreach ($data['reportColumns'] as $rc) {
      foreach ($schoolSupportedColumns as $ssc) {
        if ($rc['name'] == $ssc) {
          $finalColumns[] = $rc;
          break;
        }
      }
    }
    $data['classes'] =  $this->fee_report_model->get_Allclasses();   
    $data['items'] =  $this->fee_report_model->getAllItems();
    $data['finalFilters'] = $finalFilters;
    $data['selectedColumns'] = $this->input->post('selectedColumns');
    $classId = $this->input->post('class_name');
    $data['finalColumns'] = $finalColumns;
    $data['SelectedFtype'] = $feeType;
    $data['finalColumnsJSON'] = json_encode($finalColumns);
    $data['main_content'] = 'reports/fees/aggregate';
    $this->load->view('inc/template', $data);
  }


  public function print_denomination(){
    $data['main_content'] = 'reports/fees/denomination';
    $this->load->view('inc/template', $data);
  }



}

