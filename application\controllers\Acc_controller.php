<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Acc_controller extends CI_Controller {

  public function __construct() {
    parent::__construct();
    $this->load->model('Acc_model', 'acc');
  }

  public function index() {
    $data['data']=$this->acc->getName();
    $data['vendor_codes'] = $this->acc->get_vendor_details();
    $data['main_content'] = 'account/index';
    $this->load->view('inc/template', $data);
  }

  /**
   * Store Data from this method.
   */
  public function store() {
    $input=$this->input->post();
    $this->acc->insert_name($input);     
    redirect('Acc_controller/index');
  }

  public function delete_account($id){
    $this->acc->delete_name($id);     
    redirect('Acc_controller/index');
  }
 }

?>
