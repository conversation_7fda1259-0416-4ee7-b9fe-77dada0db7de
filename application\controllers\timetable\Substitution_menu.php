<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>ulusu
 *          <EMAIL>
 *
 * Created:  25 March 2018
 *
 * Description: Controller for Substitution Master
 *
 * Requirements: PHP5 or above
 *
 */

class Substitution_menu extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('SUBSTITUTION')) {
      redirect('dashboard', 'refresh');
    }
  }

  public function index() {
    $data['permit_substitution'] = $this->authorization->isAuthorized('SUBSTITUTION.MODULE');
    $site_url = site_url();
    $data['tiles'] = array(
      [
        'title' => 'Staff Duties',
        'sub_title' => 'Add Staff duties so that they get included in substitution',
        'icon' => 'svg_icons/staffworkload.svg',
        'url' => $site_url.'staff/staff_duties',
        'permission' => $data['permit_substitution']
      ],
      [
        'title' => 'Substitution Input',
        'sub_title' => 'Choose whom to include for Substitution',
        'icon' => 'svg_icons/substitutioninput.svg',
        'url' => $site_url.'timetable/substitution_dd/subInput',
        'permission' => $data['permit_substitution']
      ],
      [
        'title' => 'Substitution Master',
        'sub_title' => 'Perform substitution',
        'icon' => 'svg_icons/substitutionmaster.svg',
        'url' => $site_url.'timetable/substitution_dd',
        'permission' => $data['permit_substitution']
      ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
      [
        'title' => 'Substitution Output',
        'sub_title' => 'View/print Subsitution Output',
        'icon' => 'svg_icons/substitutionoutput.svg',
        'url' => $site_url.'timetable/substitution_dd/subOutput',
        'permission' => $data['permit_substitution']
      ],
      [
        'title' => 'Substitution Report',
        'sub_title' => 'View/print Subsitution Report',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'timetable/substitution_dd/subReport',
        'permission' => $data['permit_substitution']
      ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['other_tiles'] = array(
      [
        'title' => 'Substitution Helper',
        'sub_title' => 'View Substitution Helper',
        'icon' => 'svg_icons/substitution.svg',
        'url' => $site_url.'timetable/substitution_timetables',
        'permission' => $data['permit_substitution']
      ],
      [
        'title' => 'Init Substitution Cache',
        'sub_title' => 'Initialize substitution cache',
        'icon' => 'svg_icons/initsubstitutioncache.svg',
        'url' => $site_url.'build_cache/initSubstitutionCache',
        'permission' => $this->authorization->isSuperAdmin()
      ]
    );
    $data['other_tiles'] = checkTilePermissions($data['other_tiles']);

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'substitution_dd/menu/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'substitution_dd/menu/index_mobile';
    }else{
      $data['main_content']    = 'substitution_dd/menu/index';  	
    }

    
    $this->load->view('inc/template', $data);
  }
}


