<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Subjects extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE') || (!$this->authorization->isAuthorized('TIMETABLE.SUBJECTS'))) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('timetablev2/subjects_model','subject_model');
  }

  //Landing function to show the timetable menu
  public function index() {

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'timetablev2/subjects/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'timetablev2/subjects/index_mobile';
    }else{
      $data['main_content']    = 'timetablev2/subjects/index';
    }

    $this->load->view('inc/template', $data);
  }

  public function add_or_update() {
    $result = $this->subject_model->add_or_update_subject($_POST);

    if (!$result) {
      $data['result'] = 0;
    } else {
      $data['result'] = 1;
      $data['subject'] = $this->subject_model->get_subject_by_id($result);
    }

    echo json_encode($data);
  }

  public function delete() {
    echo $this->subject_model->delete_subject($_POST['subject_id']);
  }

  public function subject_list() {
    $data['subjects'] = $this->subject_model->get_subject_list();
    echo json_encode($data);
  }

}