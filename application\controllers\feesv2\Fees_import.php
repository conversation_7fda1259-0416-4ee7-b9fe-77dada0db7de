<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  12 Jan 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_Master
 */
class Fees_import extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isSuperAdmin()) {
      redirect('dashboard', 'refresh');
    }
     $this->load->model('feesv2/fees_collection_model');
    $this->load->model('feesv2/Fees_blueprint_model','fbm');
    $this->load->model('feesv2/Fees_inst_model','finst_model');
    $this->load->model('feesv2/Fees_import_model');
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_cohorts_model');
    $this->load->library('fee_library');
    $this->config->load('form_elements');
    $this->load->library('csvimport'); 
    $this->currentYear = $this->acad_year->getAcadYearId();
    $this->yearIds = $this->_formYearData();
  }


  private function _formYearData() {
    $years = $this->acad_year->getAllYearData();
    $yearIds = array();
    foreach ($years as $key => $value) {
      $yearIds[$value->acad_year] = $value->id;
    }
    return $yearIds;
  }

  public function index(){
    $data['blueprint'] = $this->fees_student_model->get_blueprints();
    $data['main_content'] = 'feesv2/import';
    $this->load->view('inc/template', $data);
  }

  public function upload($filename = '', $upload_path) {
    $config['upload_path'] = $upload_path;
    $config['allowed_types'] = 'csv|CSV';
    $config['remove_spaces'] = true;
    $config['overwrite'] = false;
    
    $this->load->library('upload', $config);
    $this->upload->initialize($config);
    if (!$this->upload->do_upload($filename)) {
      $error = array('status' => 'error', 'data' => $this->upload->display_errors());
      $this ->session ->set_flashdata('flashError', 'Failed to upload - ' . $filename);
      return $error;
    } else {
      $image = $this->upload->data();
      $success = array('status' => 'success', 'data' => $image);
      return $success;
    }

  }


    public function fees_csv_submit(){
      $creDir = 'uploads/';
      // if (!is_dir($creDir)) {
      //   mkdir($creDir, 0777, TRUE);
      // }

      if ($_FILES['csv_file']['name'] != "") {
        $ret_val = $this->upload('csv_file', $creDir);
        if ($ret_val['status'] == 'success') {
          $file_data = $ret_val['data'];
        } else {
          $file_data = $ret_val['data'];
        }
      }else{
        $file_data="";
      }
      $file_path = 'uploads/'.$file_data['file_name'];

      if ($this->csvimport->get_array($file_path)) {
        $fees_arr = $this->csvimport->get_array($file_path);
        $input = $this->input->post();
        $data['fee_data'] = $this->Fees_import_model->match_student_names($fees_arr, $input);
      }
      $data['main_content'] = 'feesv2/import';
      $this->load->view('inc/template', $data);
    }

  

    public function get_installments_blueprintwise(){
      $blueprintId = $_POST['blueprintId'];
      $result = $this->Fees_import_model->get_installmentbyBlueprintId($blueprintId);
      echo json_encode($result);
    }

    public function import_fee(){
      echo "<pre>"; print_r($this->input->post());
      die();
    }

  public function import_fee_data_one_by_one(){
    $input = $this->input->post();

    $this->db->trans_begin();

    $fTrans = $this->fees_collection_model->insert_fee_transcation($input);
    if (empty($fTrans)) 
    {
      $this->db->trans_rollback();
    }
    else
    {
      // generate and update receipt number after transcation

      // Exits receipt number update query
      $this->db->where('id',$fTrans);
      $this->db->update('feev2_transaction',array('receipt_number'=>$input['receipt_no']));

      // update collect by 
      $this->db->where('id',$fTrans);
      $this->db->update('feev2_transaction',array('collected_by'=> $input['collected_by']));

      // $this->fees_collection_model->update_receipt_transcation_wise($fTrans);

      // Check Reconcilication Status if 1 pending... not allowed updated student schedule table
      $rconStatus = $this->fees_collection_model->lastFeeTranscationReconCheck($fTrans);
      if ($rconStatus->reconciliation_status == 1) {
        $this->db->trans_commit();
        $this->session->set_flashdata('flashInfo', 'Fee collected Successfully. Reconcilication pending');
      }
      $result =  $this->fees_collection_model->update_student_schedule_all_table($fTrans);
      if (empty($result)) 
      {
        $this->db->trans_rollback();
      }
      if ($this->db->trans_status()) 
      {
        $this->db->trans_commit();
        $this->session->set_flashdata('flashSuccess', 'Fee collected Successfully');
      }
      else
      {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
    }
    return 1;
  }

  public function auto_generate_fee_receipt_update(){
    $data['blueprint'] = $this->fees_student_model->get_blueprints();
    $data['main_content'] = 'feesv2/reciept_mass_update';
    $this->load->view('inc/template', $data);
  }

  public function update_receipt_number(){
    /*$blueprint_id = $this->input->post('blueprint_id');

    $get_all_transactionBlueprintWise = $this->fees_student_model->get_transactionsIds_for_receipts($blueprint_id);

    $sql = "select frb.* from feev2_blueprint fb left join feev2_receipt_book frb on fb.receipt_book_id=frb.id where fb.id=$blueprint_id for update";
    $receipt_book = $this->db->query($sql)->row();

    $receipt_number = $this->fee_library->receipt_format_get_update($receipt_book);
    foreach ($get_all_transactionBlueprintWise as $key => $val) {

      $this->db->trans_start();
      $sql = "select frb.* from feev2_blueprint fb left join feev2_receipt_book frb on fb.receipt_book_id=frb.id where fb.id=$blueprint_id for update";
      $receipt_book = $this->db->query($sql)->row();

      $this->db->where('id',$receipt_book->id);
      $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
      $this->db->trans_complete();

      $receipt_number = $this->fee_library->receipt_format_get_update($receipt_book);
      $this->db->where('id',$val->transId);
      $this->db->update('feev2_transaction', array('receipt_number'=>$receipt_number));

    }*/
    redirect('feesv2/fees_import/auto_generate_fee_receipt_update');
  }


  public function update_schedule_amount(){
    $schId = $_POST['schId'];  
    $schedule_amount = $_POST['schedule_amount'];  
    $schedule_status = $_POST['schedule_status'];
    $schedule_discount = $_POST['schedule_discount'];
    $this->db->where('id',$schId);
    return $this->db->update('feev2_student_schedule',array('total_fee_paid'=>$schedule_amount, 'payment_status'=>$schedule_status,'discount'=>$schedule_discount));
  }
  public function insert_trans_comp_details(){
    $fee_transaction_id = $_POST['fee_transaction_id'];
    $blueprint_component_id = $_POST['blueprint_component_id'];
    $blueprint_installments_id = $_POST['blueprint_installments_id'];
    $trans_missing_amount = $_POST['trans_missing_amount'];
    $fee_student_installments_components_id = $_POST['fee_student_installments_components_id'];
    $fee_student_installments_id = $_POST['fee_student_installments_id'];
    $dataarray= array(
      'fee_transaction_id'=> $fee_transaction_id,
      'blueprint_component_id'=> $blueprint_component_id,
      'blueprint_installments_id'=> $blueprint_installments_id,
      'amount_paid'=> $trans_missing_amount,
      'concession_amount'=> '0.00',
      'fee_student_installments_components_id'=> $fee_student_installments_components_id,
      'fee_student_installments_id'=> $fee_student_installments_id,
      'refund_amount'=>'',
      'fine_amount'=> '0.00',
      'adjustment_amount'=> '0.00'
    );

    $this->db->insert('feev2_transaction_installment_component',$dataarray);
  }

  public function update_installment_amount(){
    $insId = $_POST['insId'];  
    $installment_amount = $_POST['installment_amount'];  
    $installment_status = $_POST['installment_status'];

    $this->db->where('id',$insId);
    return $this->db->update('feev2_student_installments',array('installment_amount_paid'=>$installment_amount, 'status'=>$installment_status));
  }


  public function update_installment_component_amount(){
    $compId = $_POST['compId'];  
    $component_amount = $_POST['component_amount'];  

    $this->db->where('id',$compId);
    return $this->db->update('feev2_student_installments_components',array('component_amount_paid'=>$component_amount));
  }

  public function update_trans_component_amount(){
    $transCompId = $_POST['transCompId'];  
    $trans_component_amount = $_POST['trans_component_amount'];  

    $this->db->where('id',$transCompId);
    return $this->db->update('feev2_transaction_installment_component',array('amount_paid'=>$trans_component_amount));
  }

  public function update_trans_amount(){
    $transId = $_POST['transId'];  
    $trans_amount = $_POST['trans_amount'];  
    $trans_discount = $_POST['trans_discount'];

    $this->db->where('id',$transId);
    return $this->db->update('feev2_transaction',array('amount_paid'=>$trans_amount,'discount_amount'=>$trans_discount));
  }

  public function fees_assing_fee_csv(){
    $data['blueprint'] = $this->fees_student_model->get_blueprints();
    $data['main_content'] = 'feesv2/import_fees_assign';
    $this->load->view('inc/template', $data);
  }

  public function fees_excess_fee_csv(){
    $data['main_content'] = 'feesv2/import_excess_fees';
    $this->load->view('inc/template', $data);
  }

  public function fees_csv_assing_submit(){
    $creDir = 'uploads/';
    // if (!is_dir($creDir)) {
    //   mkdir($creDir, 0777, TRUE);
    // }

    if ($_FILES['csv_file']['name'] != "") {
      $ret_val = $this->upload('csv_file', $creDir);
      if ($ret_val['status'] == 'success') {
        $file_data = $ret_val['data'];
      } else {
        $file_data = $ret_val['data'];
      }
    }else{
      $file_data="";
    }
    $file_path = 'uploads/'.$file_data['file_name'];

    if ($this->csvimport->get_array($file_path)) {
      $fees_arr = $this->csvimport->get_array($file_path);
      $input = $this->input->post();
      $data['fee_data'] = $this->Fees_import_model->get_fees_assing_amount($fees_arr, $input);
    }
    // echo "<pre>"; print_r($data['fee_data']); die();
    $data['main_content'] = 'feesv2/import_fees_assign';
    $this->load->view('inc/template', $data);
  }

  public function import_assing_fee_data_one_by_one(){
    $input = json_decode($this->input->post('fees_blueprint_data'), true);
    $totalAmount = 0;
    foreach ($input['comp_amount'] as $amounts) {
        $totalAmount += array_sum($amounts);
    }
    if($totalAmount == 0){
      echo 0;
      exit();
    }else{
      $rdata = $this->fees_student_model->insert_cohort_details_mass_assign($input['blueprint_id'], $input['cohort_id'], 'CUSTOM', $input['blueprint_installment_type_id'], $input['comp_amount'], $input['concession_amount'], $input['student_id'], $input['concession_name'], $input['fine_amount'], 0, '','');
      echo 1;
    }
  }

  public function fees_csv_excess_submit(){
    $creDir = 'uploads/';
    // if (!is_dir($creDir)) {
    //   mkdir($creDir, 0777, TRUE);
    // }

    if ($_FILES['csv_file']['name'] != "") {
      $ret_val = $this->upload('csv_file', $creDir);
      if ($ret_val['status'] == 'success') {
        $file_data = $ret_val['data'];
      } else {
        $file_data = $ret_val['data'];
      }
    }else{
      $file_data="";
    }
    $file_path = 'uploads/'.$file_data['file_name'];

    if ($this->csvimport->get_array($file_path)) {
      $fees_arr = $this->csvimport->get_array($file_path);
      $data['fee_data'] = $this->Fees_import_model->get_fees_excess_amount($fees_arr);
    }
    // echo "<pre>"; print_r($data['fee_data']); die();
    $data['main_content'] = 'feesv2/import_excess_fees';
    $this->load->view('inc/template', $data);
  }

  public function import_excess_fee_data_one_by_one(){
    $input = json_decode($this->input->post('fees_excess_data'), true);
    if($input['additional_amount'] == 0){
      echo 0;
      exit();
    }else{
      $last_insert_id =  $this->fees_collection_model->insert_additional_amount_details($input['student_id'], $input['additional_amount'], $input['additional_amount_remarks'], $input['paymentmodes'], $input['excess_bank_name'], $input['excess_branch'], $input['excess_cheque_dd_nb_cc_dd_number'], $input['excess_bank_date'], $input['excess_amount_created_date']);
      if($last_insert_id != 0){
        $this->fees_collection_model->update_excess_amount_receipt_number($last_insert_id);
      }
      echo 1;
    }
  }

}

?>
