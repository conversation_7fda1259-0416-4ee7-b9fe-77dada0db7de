<?php
/**
 * Name:    OxygenV2
 * Author:  Anish
 *          <EMAIL>
 *
 * Created:  26 april 2023
 *
 * Description: Controller for Inentory Module. Entry point for Inentory Module
 *
 * Requirements: PHP5 or above
 *
 */

class Vendor_controller_v2 extends CI_Controller {
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isAuthorized('PROCUREMENT_VENDORS.MODULE')) {
        redirect('dashboard', 'refresh');
        }
        $this->load->library('filemanager');
        $this->load->model('procurement/Vendor_model_v2');
        $this->config->load('form_elements');
    }

    // vender fns
    public function vendor_master(){
        $data['vendors'] = $this->Vendor_model_v2->getVendors();
        $data['main_content'] = 'procurement/vendor_view_v2/vendor_master';
        $this->load->view('inc/template', $data);
    }

    public function new_vendor() {
        $data['main_content'] = 'procurement/vendor_view_v2/new_vendor';
        $this->load->view('inc/template', $data);
    }

    public function view_vendors() {
        $data['vendors'] = $this->Vendor_model_v2->getVendors();
        $data['main_content'] = 'procurement/vendor_view_v2/view_vendors';
        $this->load->view('inc/template', $data);
    }

    public function vendor_details($vendor_id) {
        if (!$this->authorization->isAuthorized('PROCUREMENT_VENDORS.MODULE')) {
            redirect('dashboard', 'refresh');
        }
        $data['permitVendorAdd'] = $this->authorization->isAuthorized('PROCUREMENT_VENDORS.CRUD');
        $data['permitVendorModule'] = $this->authorization->isAuthorized('PROCUREMENT_VENDORS.MODULE');
        $data['vendor_id'] = $vendor_id;
        $vendorAddress = $this->Vendor_model_v2->checkVendorAddress($vendor_id);
        $data['flag'] = 0;//address not present
        if(!empty($vendorAddress)) {
            $data['flag'] = 1;
        }
        $data['vendor'] = $this->Vendor_model_v2->get_vendor_basic_info($vendor_id);
        $data['main_content'] = 'procurement/vendor_view_v2/vendor_details';
        $this->load->view('inc/template', $data);
    }

    public function submitVendor(){
        $status = (int) $this->Vendor_model_v2->addNewVendor();

        if($status == -1){
            $this->session->set_flashdata('flashError', 'Vendor Name Already Exist. Please Try Again.');
        } else if($status != 0){
            $this->session->set_flashdata('flashSuccess', 'Vendor Details Added Successfully.');
        } else{
            $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        }

        redirect('procurement/vendor_controller_v2/vendor_master');
    }

    //Edit vendor information
    public function editVendorInfo($vendor_id){
        $data['vendor_id'] = $vendor_id;
        $data['vendorData'] = $this->Vendor_model_v2->get_vendor_details($vendor_id);
        $data['main_content'] = 'procurement/vendor_view_v2/editVendor';
        $this->load->view('inc/template', $data);
    }

    //update the vendor
    public function updateVendor($vendor_id){
        $this->db->trans_begin();
        $status = (int) $this->Vendor_model_v2->updateVendorInfo($vendor_id);

        if($status != 0){
            $this->db->trans_commit();
            $this->session->set_flashdata('flashSuccess', 'Vendor Details Updated Successfully.');
        } else{
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        }

        redirect('procurement/vendor_controller_v2/vendor_details/'.$vendor_id);
    }

    //vendor address information
    public function vendorInfo($vendor_id){
        if (!$this->authorization->isAuthorized('PROCUREMENT_VENDORS.MODULE')) {
            redirect('dashboard', 'refresh');
        }
        $data['vendor_id'] = $vendor_id;
        $vendorAddress = $this->Vendor_model_v2->checkVendorAddress($vendor_id);
        $payData = $this->Vendor_model_v2->getPaymentInsData($vendor_id);
        $data['categories'] = $this->Vendor_model_v2->getUnassignedCategories($vendor_id);
        // echo "<pre>"; print_r($data); die();
        $data['payData'] = array();
        foreach ($payData as $key => $pay) {
            $pay_info = '';
            $type = ucwords(str_replace('_', ' ', $pay->payment_type));
            if($type == 'Cash') {
                $pay_info = 'Payment by '.ucwords($type);
            } else if($type == 'Cheque') {
                $pay_info = '<b>Cheque in favour of :</b>'.$pay->cheque_in_favor_of;
            } else {
                $pay_info = '<b>Bank Name : </b>'.$pay->bank_name.'<br>';
                $pay_info .= '<b>Branch Name : </b>'.$pay->branch.'<br>';
                $pay_info .= '<b>Account Number : </b>'.$pay->account_number.'<br>';
                $pay_info .= '<b>IFSC Code : </b>'.$pay->ifsc_code.'<br>';
            }
            $name = $pay->name;
            array_push($data['payData'], array('name' => $name, 'type' => $type, 'info' => $pay_info));
        }
        // echo "<pre>"; print_r($data); die();
        $data['office_address'] = 'Not Added';
        $data['warehouse_address'] = 'Not Added';
        $data['flag'] = 0;//address not present
        if(!empty($vendorAddress)) {
            $data['flag'] = 1; //address present
            foreach ($vendorAddress as $key => $address) {
                $addr = '';
                if($address->address_line1 != '') {
                    $addr .= $address->address_line1.'<br>';
                }
                if($address->address_line2 != '') {
                    $addr .= $address->address_line2.'<br>';
                }
                if($address->area != '') {
                    $addr .= $address->area.'<br>';
                }
                if($address->district != '') {
                    $addr .= $address->district.'<br>';
                }
                if($address->state != '') {
                    $addr .= $address->state.'<br>';
                }
                if($address->country != '') {
                    $addr .= $address->country.'<br>';
                }
                if($address->pin_code != '') {
                    $addr .= $address->pin_code;
                }
                
                if($address->address_type == 0) {
                    $data['office_address'] = $addr;
                } else {
                    $data['warehouse_address'] = $addr;
                }
            }
        }
        $data['vendor'] = $this->Vendor_model_v2->get_vendor_details($data['vendor_id']);
        $data['main_content'] = 'procurement/vendor_view_v2/vendorInfo';
        $this->load->view('inc/template', $data);
    }

    public function get_categories() {
        $vendor_id = $_POST['vendor_id'];
        $categories = $this->Vendor_model_v2->getVendorCategories($vendor_id);
        echo json_encode($categories);
    }

    public function add_vendor_category() {
        $vendor_id = $_POST['vendor_id'];
        $category_id = $_POST['category_id'];
        $status = $this->Vendor_model_v2->addVendorCategories($vendor_id, $category_id);
        echo $status;
    }

    //add vendor address
    public function addVendorAddress($vendor_id){
        $data['vendor_id'] = $vendor_id;
        $data['vendor'] = $this->Vendor_model_v2->get_vendor_basic_info($vendor_id);
        $data['main_content'] = 'procurement/vendor_view_v2/addVendorAddress';
        $this->load->view('inc/template', $data);
    }

    //Delete vendor info
    public function deleteVendorInfo($vendor_id){
        $this->db->trans_begin();
        $status =(int) $this->Vendor_model_v2->deleteVendor($vendor_id);
        if($status != 0){
            $this->db->trans_commit();
            $this->session->set_flashdata('flashSuccess', 'Vendor Details Deleted Successfully.');
        } else{
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        }

        redirect('procurement/vendor_controller_v2/vendor_master');
    }

    //submit vendor details
    public function submitVendorAddress($vendor_id){
        $address = $this->Vendor_model_v2->generateAddressData($vendor_id);
        $addressData = array();
        array_push($addressData, $address['office']);
        array_push($addressData, $address['warehouse']);
        $status = $this->Vendor_model_v2->submitVendorAddressInfo($addressData);
        if($status) {
            $this->session->set_flashdata('flashSuccess', 'Vendor Address Added Successfully.');
        } else {
            $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        }
        redirect('procurement/vendor_controller_v2/vendor_details/'.$vendor_id);
    }

    //edit vendor address
    public function editVendorAddress($vendor_id){
        $data['vendorAddress'] = $this->Vendor_model_v2->checkVendorAddress($vendor_id);
        $data['vendor_id'] = $vendor_id;
        $data['vendor'] = $this->Vendor_model_v2->get_vendor_basic_info($vendor_id);
        $data['main_content'] = 'procurement/vendor_view_v2/editVendorAddress';
        $this->load->view('inc/template', $data);
    }

    //update vendor address
    public function updateVendorAddress($vendor_id){
        $address = $this->Vendor_model_v2->generateAddressData($vendor_id); 
        $status1 = $this->Vendor_model_v2->updateVendorAddressInfo($vendor_id,0,$address['office']);
        $status2 = $this->Vendor_model_v2->updateVendorAddressInfo($vendor_id,1,$address['warehouse']);
        if($status1 && $status2) {
            $this->session->set_flashdata('flashSuccess', 'Vendor Address Updated Successfully.');
        } else {
            $this->session->set_flashdata('flashError', 'Something Went Wrong!');
        }

        redirect('procurement/vendor_controller_v2/vendor_details/'.$vendor_id);
    }

    public function payment_instruments($vendor_id){
        $data['vendor_id'] = $vendor_id;
        $data['vendor'] = $this->Vendor_model_v2->get_vendor_basic_info($vendor_id);
        $data['payData'] = $this->Vendor_model_v2->getPaymentInsData($vendor_id);
        $data['main_content'] = 'procurement/vendor_view_v2/payment_instruments';
        $this->load->view('inc/template', $data);
    }

    public function addPaymentInstrument($vendor_id){
        $data['vendor_id'] = $vendor_id;
        $status = $this->Vendor_model_v2->addPaymentInstrument($vendor_id);

        if($status === -1) {
            $this->session->set_flashdata('flashInfo', 'Already exist');
        }
        else if($status){
            $this->session->set_flashdata('flashSuccess', 'Added Successfully.');
        }
        else {
            $this->session->set_flashdata('flashError', 'Something went wrong!');
        }
        redirect('procurement/vendor_controller_v2/payment_instruments/'.$vendor_id);
    }

    public function remove_payment_instruments() {
        $payInsId = $_POST['pay_ins_id'];
        $status = $this->Vendor_model_v2->removePaymentInstrument($payInsId);
        echo $status;
    }

    public function add_observation_rating() {
        $observation = $_POST['observation'];
        $rating = $_POST['rating'];
        $vendor_id = $_POST['vendor_id'];
        $status = $this->Vendor_model_v2->addObservationRating($observation, $rating, $vendor_id);
        echo $status;
    }

    public function get_observations() {
        $vendor_id = $_POST['vendor_id'];
        $observations = $this->Vendor_model_v2->getObservations($vendor_id);
        echo json_encode($observations);
    }

    public function change_vendor_status() {
        $vendor_id = $_POST['vendor_id'];
        $status = $_POST['status'];
        $status = $this->Vendor_model_v2->changeVendorStatus($status, $vendor_id);
        echo $status;
    }

    public function vendor_product_categories($vendor_id) {
        $data['vendor_id'] = $vendor_id;
        $data['vendor'] = $this->Vendor_model_v2->get_vendor_basic_info($vendor_id);
        $data['categories'] = $this->Vendor_model_v2->getUnassignedCategories($vendor_id);
        $data['assignedCategories'] = $this->Vendor_model_v2->getVendorCategoriesData($vendor_id);
        $data['main_content'] = 'procurement/vendor_view_v2/vendor_categories';
        $this->load->view('inc/template', $data);
    }

    public function vendor_observations_ratings($vendor_id) {
        $data['vendor_id'] = $vendor_id;
        $data['vendor'] = $this->Vendor_model_v2->get_vendor_basic_info($vendor_id);
        $data['main_content'] = 'procurement/vendor_view_v2/vendor_observations';
        $this->load->view('inc/template', $data);
    }
    // end vendor

    public function assign_unAssign_category() {
        $proc_im_category_id= $_POST['category_id'];
        $vendor_id= $_POST['vendor_id'];
        $is_call_for_delete= $_POST['is_call_for_delete'];
        echo $this->Vendor_model_v2->assign_unAssign_category($vendor_id, $proc_im_category_id, $is_call_for_delete);
    }

}
?>