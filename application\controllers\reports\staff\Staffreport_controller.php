<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  05 April 2018
 *
 * Description: Model class for Class Master table
 *
 * Requirements: PHP5 or above
 *
 */

class Staffreport_controller extends CI_Controller {

    public $columnList = [
    // [
    //   'displayName'=>'Staff Type',
    //   'columnNameWithTable' => 'staff_type',
    //   'columnName'=>'staff_type',
    //   'varName'=>'sType',
    //   'table'=>'staff_master',
    //   'index'=>'1',
    //   'displayType'=>'text',
    //   'dataType'=>'string'
    // ],
    [
      'displayName'=>'Gender',
      'columnNameWithTable'=>'sm.gender',
      'columnName'=>'gender',
      'varName'=>'gender',
      'table'=>'staff_master',
      'index'=>'2',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Date of Birth',
      'columnNameWithTable' => 'DATE_FORMAT(sm.dob, "%d-%m-%Y")',
      'columnName'=>'dob',
      'varName'=>'sDOB',
      'table'=>'staff_master',
      'index'=>'3',
      'displayType'=>'text',
      'dataType'=>'date',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Staff Age',
      'columnNameWithTable'=>'sm.staff_age',
      'columnName'=>'staff_age',
      'varName'=>'staff_age',
      'table'=>'staff_master',
      'index'=>'4',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Spouse Name',
      'columnNameWithTable'=>'concat(ifnull(sm.spouse_name,""), " ", ifnull(sm.spouse_last_name,""))',
      'columnName'=>'spouse_name',
      'varName'=>'spouseName',
      'table'=>'staff_master',
      'index'=>'5',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Spouse Gender',
      'columnNameWithTable' => 'IF(sm.spouse_gender IS NULL, "-", IF(sm.spouse_gender = "M", "Male", IF(sm.spouse_gender = "F", "Female", "-")))',
      'columnName'=>'spouse_gender',
      'varName'=>'spouse_gender',
      'table'=>'staff_master',
      'index'=>'6',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Spouse DOB',
      'columnNameWithTable' => 'DATE_FORMAT(sm.spouse_dob, "%d-%m-%Y")',
      'columnName'=>'spouse_dob',
      'varName'=>'spouse_dob',
      'table'=>'staff_master',
      'index'=>'7',
      'displayType'=>'text',
      'dataType'=>'date',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Spouse Age',
      'columnNameWithTable'=>'sm.spouse_age',
      'columnName'=>'spouse_age',
      'varName'=>'spouse_age',
      'table'=>'staff_master',
      'index'=>'8',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Spouse Is Dependent',
      'columnNameWithTable' => 'IF(sm.spouse_is_dependent = 0, "No", "Yes")',
      'columnName'=>'spouse_is_dependent',
      'varName'=>'spouse_is_dependent',
      'table'=>'staff_master',
      'index'=>'9',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Include Spouse Insurance',
      'columnNameWithTable'=>'sm.include_spouse_insurance',
      'columnName'=>'include_spouse_insurance',
      'varName'=>'include_spouse_insurance',
      'table'=>'staff_master',
      'index'=>'10',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child1 Name',
      'columnNameWithTable'=>'concat(ifnull(sm.child1_first_name,""), " ", ifnull(sm.child1_last_name,""))',
      'columnName'=>'child1_name',
      'varName'=>'child1_name',
      'table'=>'staff_master',
      'index'=>'11',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child1 Gender',
      'columnNameWithTable' => 'IF(sm.child1_gender IS NULL, "-", IF(sm.child1_gender = "M", "Male", IF(sm.child1_gender = "F", "Female", "-")))',
      'columnName'=>'child1_gender',
      'varName'=>'child1_gender',
      'table'=>'staff_master',
      'index'=>'12',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child1 DOB',
      'columnNameWithTable' => 'IFNULL(DATE_FORMAT(sm.child1_dob, "%d-%m-%Y"), "-")',
      'columnName'=>'child1_dob',
      'varName'=>'child1_dob',
      'table'=>'staff_master',
      'index'=>'13',
      'displayType'=>'text',
      'dataType'=>'date',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child1 Age',
      'columnNameWithTable'=>'sm.child1_age',
      'columnName'=>'child1_age',
      'varName'=>'child1_age',
      'table'=>'staff_master',
      'index'=>'14',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child1 Is Dependent',
      'columnNameWithTable' => 'IF(sm.child1_is_dependent = 0, "No", "Yes")',
      'columnName'=>'child1_is_dependent',
      'varName'=>'child1_is_dependent',
      'table'=>'staff_master',
      'index'=>'15',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Include Child1 Insurance',
      'columnNameWithTable'=>'sm.include_child1_insurance',
      'columnName'=>'include_child1_insurance',
      'varName'=>'include_child1_insurance',
      'table'=>'staff_master',
      'index'=>'16',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child2 Name',
      'columnNameWithTable'=>'concat(ifnull(sm.child2_first_name,""), " ", ifnull(sm.child2_last_name,""))',
      'columnName'=>'child2_name',
      'varName'=>'child2_name',
      'table'=>'staff_master',
      'index'=>'17',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child2 Gender',
      'columnNameWithTable' => 'IF(sm.child2_gender IS NULL, "-", IF(sm.child2_gender = "M", "Male", IF(sm.child2_gender = "F", "Female", "-")))',
      'columnName'=>'child2_gender',
      'varName'=>'child2_gender',
      'table'=>'staff_master',
      'index'=>'18',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child2 DOB',
      'columnNameWithTable' => 'DATE_FORMAT(sm.child2_dob, "%d-%m-%Y")',
      'columnName'=>'child2_dob',
      'varName'=>'child2_dob',
      'table'=>'staff_master',
      'index'=>'19',
      'displayType'=>'text',
      'dataType'=>'date',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child2 Age',
      'columnNameWithTable'=>'sm.child2_age',
      'columnName'=>'child2_age',
      'varName'=>'child2_age',
      'table'=>'staff_master',
      'index'=>'20',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child2 Is Dependent',
      'columnNameWithTable' => 'IF(sm.child2_is_dependent = 0, "No", "Yes")',
      'columnName'=>'child2_is_dependent',
      'varName'=>'child2_is_dependent',
      'table'=>'staff_master',
      'index'=>'21',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Include Child2 Insurance',
      'columnNameWithTable'=>'sm.include_child2_insurance',
      'columnName'=>'include_child2_insurance',
      'varName'=>'include_child2_insurance',
      'table'=>'staff_master',
      'index'=>'22',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child3 Name',
      'columnNameWithTable'=>'concat(ifnull(sm.child3_first_name,""), " ", ifnull(sm.child3_last_name,""))',
      'columnName'=>'child3_name',
      'varName'=>'child3_name',
      'table'=>'staff_master',
      'index'=>'23',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child3 Gender',
      'columnNameWithTable' => 'IF(sm.child3_gender IS NULL, "-", IF(sm.child3_gender = "M", "Male", IF(sm.child3_gender = "F", "Female", "-")))',
      'columnName'=>'child3_gender',
      'varName'=>'child3_gender',
      'table'=>'staff_master',
      'index'=>'24',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child3 DOB',
      'columnNameWithTable' => 'DATE_FORMAT(sm.child3_dob, "%d-%m-%Y")',
      'columnName'=>'child3_dob',
      'varName'=>'child3_dob',
      'table'=>'staff_master',
      'index'=>'25',
      'displayType'=>'text',
      'dataType'=>'date',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child3 Age',
      'columnNameWithTable'=>'sm.child3_age',
      'columnName'=>'child3_age',
      'varName'=>'child3_age',
      'table'=>'staff_master',
      'index'=>'26',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Child3 Is Dependent',
      'columnNameWithTable' => 'IF(sm.child3_is_dependent = 0, "No", "Yes")',
      'columnName'=>'child3_is_dependent',
      'varName'=>'child3_is_dependent',
      'table'=>'staff_master',
      'index'=>'27',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Include Child3 Insurance',
      'columnNameWithTable'=>'sm.include_child3_insurance',
      'columnName'=>'include_child3_insurance',
      'varName'=>'include_child3_insurance',
      'table'=>'staff_master',
      'index'=>'28',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Father Name',
      'columnNameWithTable'=>'concat(ifnull(sm.father_first_name,""), " ", ifnull(sm.father_last_name,""))',
      'columnName'=>'father_name',
      'varName'=>'sFatherName',
      'table'=>'staff_master',
      'index'=>'29',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info',
    ],
    [
      'displayName'=>'Father DOB',
      'columnNameWithTable'=>'DATE_FORMAT(sm.father_dob, "%d-%m-%Y")',
      'columnName'=>'father_dob',
      'varName'=>'sFatherDOB',
      'table'=>'staff_master',
      'index'=>'30',
      'displayType'=>'text',
      'dataType'=>'date',
      'info'=>'family_info',
    ],
    [
      'displayName'=>'Father Age',
      'columnNameWithTable'=>'sm.father_age',
      'columnName'=>'father_age',
      'varName'=>'father_age',
      'table'=>'staff_master',
      'index'=>'31',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Father Is Dependent',
      'columnNameWithTable'=>'IF(sm.father_is_dependent = 0, "No", "Yes")',
      'columnName'=>'father_is_dependent',
      'varName'=>'sFatherIsDependent',
      'table'=>'staff_master',
      'index'=>'32',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info',
    ],
    [
      'displayName'=>'Include Father Insurance',
      'columnNameWithTable'=>'sm.include_father_insurance',
      'columnName'=>'include_father_insurance',
      'varName'=>'include_father_insurance',
      'table'=>'staff_master',
      'index'=>'33',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Mother Name',
      'columnNameWithTable'=>'concat(ifnull(sm.mother_first_name,""), " ", ifnull(sm.mother_last_name,""))',
      'columnName'=>'mother_name',
      'varName'=>'smotherName',
      'table'=>'staff_master',
      'index'=>'34',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Mother DOB',
      'columnNameWithTable'=>'DATE_FORMAT(sm.mother_dob, "%d-%m-%Y")',
      'columnName'=>'mother_dob',
      'varName'=>'sMotherDOB',
      'table'=>'staff_master',
      'index'=>'35',
      'displayType'=>'text',
      'dataType'=>'date',
      'info'=>'family_info',
    ],
    [
      'displayName'=>'Mother Age',
      'columnNameWithTable'=>'sm.mother_age',
      'columnName'=>'mother_age',
      'varName'=>'mother_age',
      'table'=>'staff_master',
      'index'=>'36',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Mother Is Dependent',
      'columnNameWithTable'=>'IF(sm.mother_is_dependent = 0, "No", "Yes")',
      'columnName'=>'mother_is_dependent',
      'varName'=>'sMotherIsDependent',
      'table'=>'staff_master',
      'index'=>'37',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info',
    ],
    [
      'displayName'=>'Include Mother Insurance',
      'columnNameWithTable'=>'sm.include_mother_insurance',
      'columnName'=>'include_mother_insurance',
      'varName'=>'include_mother_insurance',
      'table'=>'staff_master',
      'index'=>'38',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ], 
    [
      'displayName'=>'Designation',
      'columnNameWithTable'=>'degn.designation',
      'columnName'=>'designation',
      'varName'=>'designation',
      'table'=>'staff_designations',
      'index'=>'39',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Department',
      'columnNameWithTable'=>'dept.department',
      'columnName'=>'department',
      'varName'=>'department',
      'table'=>'staff_departments',
      'index'=>'40',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],

    [
      'displayName'=>'Marital Status',
      'columnNameWithTable'=>'sm.marital_status',
      'columnName'=>'marital_status',
      'varName'=>'marital_status',
      'table'=>'staff_master',
      'index'=>'41',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],

    [
      'displayName'=>'Nationality',
      'columnNameWithTable'=>'sm.nationality',
      'columnName'=>'nationality',
      'varName'=>'nationality',
      'table'=>'staff_master',
      'index'=>'42',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
     [
      'displayName'=>'Aadhar Number',
      'columnNameWithTable'=>'sm.aadhar_number',
      'columnName'=>'aadhar_number',
      'varName'=>'AdNumber',
      'table'=>'staff_master',
      'index'=>'43',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],

     [
      'displayName'=>'Qualification',
      'columnNameWithTable'=>'sm.qualification',
      'columnName'=>'qualification',
      'varName'=>'qualification',
      'table'=>'staff_master',
      'index'=>'44',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],

     [
      'displayName'=>'Subject Specialization',
      'columnNameWithTable'=>'sm.subject_specialization',
      'columnName'=>'subject_specialization',
      'varName'=>'subject_specialization',
      'table'=>'staff_master',
      'index'=>'45',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],

    [
      'displayName'=>'Total Experience',
      'columnNameWithTable'=>'sm.total_experience',
      'columnName'=>'total_experience',
      'varName'=>'total_experience',
      'table'=>'staff_master',
      'index'=>'46',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],

    [
      'displayName'=>'Total Education Experience',
      'columnNameWithTable'=>'sm.total_education_experience',
      'columnName'=>'total_education_experience',
      'varName'=>'total_eductation_experience',
      'table'=>'staff_master',
      'index'=>'47',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
   
    [
      'displayName'=>'Alternative Number',
      'columnNameWithTable'=>'sm.alternative_number',
      'columnName'=>'alternative_number',
      'varName'=>'alternative_number',
      'table'=>'staff_master',
      'index'=>'48',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],

    [
      'displayName'=>'Spouse Contact Number',
      'columnNameWithTable'=>'sm.spouse_contact_no',
      'columnName'=>'spouse_contact_no',
      'varName'=>'spouse_contact_number',
      'table'=>'staff_master',
      'index'=>'49',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],

    [
      'displayName'=>'Date of Joining',
      'columnNameWithTable' => 'DATE_FORMAT(sm.joining_date, "%d-%m-%Y")',
      'columnName'=>'joining_date',
      'varName'=>'staffDOJ',
      'table'=>'staff_master',
      'index'=>'50',
      'displayType'=>'text',
      'dataType'=>'date',
      'info'=>'school_info'
    ],

     [
      'displayName'=>'User Name',
      'columnNameWithTable' => 'u.username',
      'columnName'=>'username',
      'varName'=>'uName',
      'table'=>'users',
      'index'=>'51',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Email',
      'columnNameWithTable' => 'u.email',
      'columnName'=>'email',
      'varName'=>'email',
      'table'=>'users',
      'index'=>'52',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName' => 'Personal Email',
      'columnNameWithTable' => 'sm.personal_mail_id',
      'columnName' => 'personal_mail_id',
      'varName' => 'personal_mail_id',
      'table' => 'staff_master',
      'index' => '53',
      'displayType' => 'text',
      'dataType' => 'string',
      'info'=>'personal_info'
    ],
     [
      'displayName'=>'Profile Confirmed',
      'columnNameWithTable' => 'profile_confirmed',
      'columnName'=>'profile_confirmed',
      'varName'=>'profile_confirmed',
      'table'=>'staff_master',
      'index'=>'54',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'ESI Number',
      'columnNameWithTable' => 'npm.esi_number',
      'columnName'=>'esi_number',
      'varName'=>'esi_number',
      'table'=>'new_payroll_master npm',
      'index'=>'55',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'UAN Number',
      'columnNameWithTable' => 'npm.uan_number',
      'columnName'=>'uan_number',
      'varName'=>'uan_number',
      'table'=>'new_payroll_master npm',
      'index'=>'56',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
     [
      'displayName'=>'PF Number',
      'columnNameWithTable' => 'npm.pf_number',
      'columnName'=>'pf_number',
      'varName'=>'pf_number',
      'table'=>'new_payroll_master npm',
      'index'=>'57',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'PAN Details',
      'columnNameWithTable' => 'npm.pan_number',
      'columnName'=>'pan_number',
      'varName'=>'pan_number',
      'table'=>'new_payroll_master npm',
      'index'=>'58',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Account Number',
      'columnNameWithTable' => 'npm.account_number',
      'columnName'=>'account_number',
      'varName'=>'account_number',
      'table'=>'new_payroll_master npm',
      'index'=>'59',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Bank Name',
      'columnNameWithTable' => 'npm.bank_name',
      'columnName'=>'bank_name',
      'varName'=>'bank_name',
      'table'=>'new_payroll_master npm',
      'index'=>'60',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Bank Branch Name',
      'columnNameWithTable' => 'npm.branch_name',
      'columnName'=>'branch_name',
      'varName'=>'branch_name',
      'table'=>'new_payroll_master npm',
      'index'=>'61',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'IFS Code',
      'columnNameWithTable' => 'npm.ifsc_code',
      'columnName'=>'ifsc_code',
      'varName'=>'ifsc_code',
      'table'=>'new_payroll_master npm',
      'index'=>'62',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Blood Group',
      'columnNameWithTable'=>'sm.blood_group',
      'columnName'=>'blood_group',
      'varName'=>'blood_group',
      'table'=>'staff_master',
      'index'=>'63',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Staff Type',
      'columnNameWithTable' => 'sm.staff_type',
      'columnName'=>'staff_type',
      'varName'=>'staff_type',
      'table'=>'staff_master',
      'index'=>'64',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    //new
    [
      'displayName'=>'Emergency Info',
      'columnNameWithTable' => 'sm.emergency_info',
      'columnName'=>'emergency_info',
      'varName'=>'emergency_info',
      'table'=>'staff_master',
      'index'=>'65',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Math high grade',
      'columnNameWithTable' => 'math_high_grade',
      'columnName'=>'math_high_grade',
      'varName'=>'math_high_grade',
      'table'=>'staff_master',
      'index'=>'66',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'English high grade',
      'columnNameWithTable' => 'sm.english_high_grade',
      'columnName'=>'english_high_grade',
      'varName'=>'english_high_grade',
      'table'=>'staff_master',
      'index'=>'67',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Social high grade',
      'columnNameWithTable' => 'social_high_grade',
      'columnName'=>'social_high_grade',
      'varName'=>'social_high_grade',
      'table'=>'staff_master',
      'index'=>'68',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Trained to teach',
      'columnNameWithTable' => 'sm.trained_to_teach',
      'columnName'=>'trained_to_teach',
      'varName'=>'trained_to_teach',
      'table'=>'staff_master',
      'index'=>'69',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
     [
      'displayName'=>'Appointed subject',
      'columnNameWithTable' => 'sm.appointed_subject',
      'columnName'=>'appointed_subject',
      'varName'=>'appointed_subject',
      'table'=>'staff_master',
      'index'=>'70',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
     [
      'displayName'=>'Classes taught',
      'columnNameWithTable' => 'sm.classes_taught',
      'columnName'=>'classes_taught',
      'varName'=>'classes_taught',
      'table'=>'staff_master',
      'index'=>'71',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
     [
      'displayName'=>'Main sub taught',
      'columnNameWithTable' => 'sm.main_sub_taught',
      'columnName'=>'main_sub_taught',
      'varName'=>'main_sub_taught',
      'table'=>'staff_master',
      'index'=>'72',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Add sub taught',
      'columnNameWithTable' => 'sm.add_sub_taught',
      'columnName'=>'add_sub_taught',
      'varName'=>'add_sub_taught',
      'table'=>'staff_master',
      'index'=>'73',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Boarding',
      'columnNameWithTable' => 'sm.boarding',
      'columnName'=>'boarding',
      'varName'=>'boarding',
      'table'=>'staff_master',
      'index'=>'74',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Last working day',
      'columnNameWithTable' => 'DATE_FORMAT(sm.last_working_day, "%d-%m-%Y")',
      'columnName'=>'last_working_day',
      'varName'=>'last_working_day',
      'table'=>'staff_master',
      'index'=>'75',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Voter id',
      'columnNameWithTable' => 'sm.voter_id',
      'columnName'=>'voter_id',
      'varName'=>'voter_id',
      'table'=>'staff_master',
      'index'=>'76',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Height',
      'columnNameWithTable' => 'sm.height',
      'columnName'=>'height',
      'varName'=>'height',
      'table'=>'staff_master',
      'index'=>'77',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Weight',
      'columnNameWithTable' => 'sm.weight',
      'columnName'=>'weight',
      'varName'=>'weight',
      'table'=>'staff_master',
      'index'=>'78',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Allergies',
      'columnNameWithTable' => 'sm.allergies',
      'columnName'=>'allergies',
      'varName'=>'allergies',
      'table'=>'staff_master',
      'index'=>'79',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Medical issues',
      'columnNameWithTable' => 'sm.medical_issues',
      'columnName'=>'medical_issues',
      'varName'=>'medical_issues',
      'table'=>'staff_master',
      'index'=>'80',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Identification mark',
      'columnNameWithTable' => 'sm.identification_mark',
      'columnName'=>'identification_mark',
      'varName'=>'identification_mark',
      'table'=>'staff_master',
      'index'=>'81',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Person with disability',
      'columnNameWithTable' => 'sm.person_with_disability',
      'columnName'=>'person_with_disability',
      'varName'=>'person_with_disability',
      'table'=>'staff_master',
      'index'=>'82',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Passport number',
      'columnNameWithTable' => 'sm.passport_number',
      'columnName'=>'passport_number',
      'varName'=>'passport_number',
      'table'=>'staff_master',
      'index'=>'83',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Passport place of issue',
      'columnNameWithTable' => 'sm.passport_place_of_issue',
      'columnName'=>'passport_place_of_issue',
      'varName'=>'passport_place_of_issue',
      'table'=>'staff_master',
      'index'=>'84',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Passport date of issue',
      'columnNameWithTable' => 'DATE_FORMAT(sm.passport_date_of_issue, "%d-%m-%Y")',
      'columnName'=>'passport_date_of_issue',
      'varName'=>'passport_date_of_issue',
      'table'=>'staff_master',
      'index'=>'85',
      'displayType'=>'text',
      'dataType'=>'date',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Passport expiry date',
      'columnNameWithTable' => 'DATE_FORMAT(sm.passport_expiry_date, "%d-%m-%Y")',
      'columnName'=>'passport_expiry_date',
      'varName'=>'passport_expiry_date',
      'table'=>'staff_master',
      'index'=>'86',
      'displayType'=>'text',
      'dataType'=>'date',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Visa details',
      'columnNameWithTable' => 'sm.visa_details',
      'columnName'=>'visa_details',
      'varName'=>'visa_details',
      'table'=>'staff_master',
      'index'=>'87',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Religion',
      'columnNameWithTable' => 'sm.religion',
      'columnName'=>'religion',
      'varName'=>'religion',
      'table'=>'staff_master',
      'index'=>'88',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Nature of appointment',
      'columnNameWithTable' => 'sm.nature_of_appointment',
      'columnName'=>'nature_of_appointment',
      'varName'=>'nature_of_appointment',
      'table'=>'staff_master',
      'index'=>'89',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Has completed any B.Ed',
      'columnNameWithTable' => 'sm.has_completed_any_bed',
      'columnName'=>'has_completed_any_bed',
      'varName'=>'has_completed_any_bed',
      'table'=>'staff_master',
      'index'=>'90',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Father Occupation',
      'columnNameWithTable' => 'sm.father_occupation',
      'columnName'=>'father_occupation',
      'varName'=>'father_occupation',
      'table'=>'staff_master',
      'index'=>'91',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Father Contact Number',
      'columnNameWithTable' => 'sm.father_contact_no',
      'columnName'=>'father_contact_no',
      'varName'=>'father_contact_no',
      'table'=>'staff_master',
      'index'=>'92',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Spouse Occupation',
      'columnNameWithTable' => 'sm.spouse_occupation',
      'columnName'=>'spouse_occupation',
      'varName'=>'spouse_occupation',
      'table'=>'staff_master',
      'index'=>'93',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'family_info'
    ],
    [
      'displayName'=>'Staff Reference Code',
      'columnNameWithTable' => 'sm.staff_reference_code',
      'columnName'=>'staff_reference_code',
      'varName'=>'staff_reference_code',
      'table'=>'staff_master',
      'index'=>'94',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Previous Designation Name',
      'columnNameWithTable' => 'sm.previous_designation_name',
      'columnName'=>'previous_designation_name',
      'varName'=>'previous_designation_name',
      'table'=>'staff_master',
      'index'=>'95',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Salutation',
      'columnNameWithTable' => 'sm.salutation',
      'columnName'=>'salutation',
      'varName'=>'sSalutation',
      'table'=>'staff_master',
      'index'=>'96',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Caste',
      'columnNameWithTable' => 'sm.caste',
      'columnName'=>'caste',
      'varName'=>'sCaste',
      'table'=>'staff_master',
      'index'=>'97',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Category',
      'columnNameWithTable' => 'sm.category',
      'columnName'=>'category',
      'varName'=>'sCategory',
      'table'=>'staff_master',
      'index'=>'98',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Has Completed PGDEI',
      'columnNameWithTable' => 'sm.has_completed_any_pgdei',
      'columnName'=>'has_completed_any_pgdei',
      'varName'=>'shas_completed_any_pgdei',
      'table'=>'staff_master',
      'index'=>'99',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Staff Biometric Code',
      'columnNameWithTable' => 'sac.staff_code',
      'columnName'=>'staff_code',
      'varName'=>'staff_code',
      'table'=>'staff_attendance_code',
      'index'=>'100',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Staff Photo Url(Low)',
      'columnNameWithTable' => 'sm.picture_url',
      'columnName'=>'photo',
      'varName'=>'photo',
      'table'=>'staff_master',
      'index'=>'101',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    // New Fields For Family Information
    [
      'displayName'=>'Previous Employer Joining Date',
      'columnNameWithTable' => 'IFNULL(DATE_FORMAT(npm.previous_emplyoer_joining_date, "%d-%m-%Y"), "-")',
      'columnName'=>'previous_emplyoer_joining_date',
      'varName'=>'previous_emplyoer_joining_date',
      'table'=>'new_payroll_master npm',
      'index'=>'102',
      'displayType'=>'text',
      'dataType'=>'date',
    ],
    
    [
      'displayName'=>'Previous Employer Exit Date',
      'columnNameWithTable' => 'IFNULL(DATE_FORMAT(npm.previous_employer_exit_date, "%d-%m-%Y"), "-")',
      'columnName'=>'previous_employer_exit_date',
      'varName'=>'previous_employer_exit_date',
      'table'=>'new_payroll_master npm',
      'index'=>'103',
      'displayType'=>'text',
      'dataType'=>'date',
    ],
    [
      'displayName'=>'Is Primary Instance',
      'columnNameWithTable'=>'IF(sm.is_primary_instance = 1, "Yes", "No")',
      'columnName'=>'is_primary_instance',
      'varName'=>'isPrimaryInstance',
      'table'=>'staff_master',
      'index'=>'104',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Staff House',
      'columnNameWithTable'=>'ifnull(sm.staff_house,"-")',
      'columnName'=>'staff_house',
      'varName'=>'staff_house',
      'table'=>'staff_master',
      'index'=>'105',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    
    [
      'displayName'=>'Sum Insured Amount',
      'columnNameWithTable' => 'npm.sum_insured_amount',
      'columnName'=>'sum_insured_amount',
      'varName'=>'sum_insured_amount',
      'table'=>'new_payroll_master npm',
      'index'=>'106',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Staff Exit date',
      'columnNameWithTable' => 'IFNULL(DATE_FORMAT(sm.resignation_date, "%d-%m-%Y"), "-")',
      'columnName'=>'resignation_date',
      'varName'=>'resignation_date',
      'table'=>'staff_master',
      'index'=>'107',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Last date of work',
      'columnNameWithTable'=>'IFNULL(DATE_FORMAT(sm.last_date_of_work, "%d-%m-%Y"), "-")',
      'columnName'=>'last_date_of_work',
      'varName'=>'last_date_of_work',
      'table'=>'staff_master',
      'index'=>'108',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Resignation Date',
      'columnNameWithTable'=>'IFNULL(DATE_FORMAT(sm.resignation_date, "%d-%m-%Y"), "-")',
      'columnName'=>'resignation_date',
      'varName'=>'resignation_date',
      'table'=>'staff_master',
      'index'=>'109',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Exit Remarks',
      'columnNameWithTable' => 'sm.exit_remarks',
      'columnName'=>'exit_remarks',
      'varName'=>'exit_remarks',
      'table'=>'staff_master',
      'index'=>'110',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Exit updated Date',
      'columnNameWithTable'=>'IFNULL(DATE_FORMAT(sm.exit_update_on, "%d-%m-%Y"), "-")',
      'columnName'=>'exit_update_on',
      'varName'=>'exit_update_on',
      'table'=>'staff_master',
      'index'=>'111',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Exit updated By',
      'columnNameWithTable'=>'concat(ifnull(sm_exit_update_by.first_name,""), " ", ifnull(sm_exit_update_by.last_name,""))',
      'columnName'=>'exit_updated_by',
      'varName'=>'exit_updated_by',
      'table'=>'staff_master',
      'index'=>'112',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Resignation Document',
      'columnNameWithTable'=>'sm.resignation_letter_doc',
      'columnName'=>'resignation_letter_doc',
      'varName'=>'resignation_letter_doc',
      'table'=>'staff_master',
      'index'=>'113',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'school_info'
    ],
    [
      'displayName'=>'Staff Photo Url(High)',
      'columnNameWithTable' => 'sm.high_quality_picture_url',
      'columnName'=>'photo',
      'varName'=>'hight_quality_photo',
      'table'=>'staff_master',
      'index'=>'114',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    
  ];

  public $mandatoryFields = [
    [
      'displayName'=>'Id',
      'columnNameWithTable'=>'sm.id',
      'columnName'=>'id',
      'varName'=>'smId',
      'table'=>'staff_master',
      'displayType'=>'hidden',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Employee Code',
      'columnNameWithTable'=>'sm.employee_code',
      'columnName'=>'employee_code',
      'varName'=>'employee_code',
      'table'=>'staff_master',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
    [
      'displayName' => 'Staff Full Name',
      'columnNameWithTable' => 'concat(ifnull(sm.first_name,""), " ", ifnull(sm.last_name,""))',
      'columnName' => 'full_name',
      'varName' => 'staff_name',
      'table' => 'staff_master',
      'displayType' => 'text',
      'dataType' => 'string'
    ],
    [
      'displayName' => 'First Name',
      'columnNameWithTable' => 'ifnull(sm.first_name,"")',
      'columnName' => 'first_name',
      'varName' => 'sFirstName',
      'table' => 'staff_master',
      'displayType' => 'text',
      'dataType' => 'string',
      'info'=>'personal_info'
    ],
    [
      'displayName' => 'Last Name',
      'columnNameWithTable' => 'ifnull(sm.last_name,"")',
      'columnName' => 'last_name',
      'varName' => 'slastName',
      'table' => 'staff_master',
      'displayType' => 'text',
      'dataType' => 'string',
      'info'=>'personal_info'
    ],
    [
      'displayName'=>'Contact Number',
      'columnNameWithTable'=>'sm.contact_number',
      'columnName'=>'contact_number',
      'varName'=>'cNumber',
      'table'=>'staff_master',
      'displayType'=>'text',
      'dataType'=>'string',
      'info'=>'personal_info'
    ],
  ];


	function __construct() {
		  parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      $this->load->model('report/staffreport_model');
      $this->load->library('filemanager');
	} 

  	public function index(){
      $data['all_stafftype']= $this->staffreport_model->get_staff_details();
      //echo "<pre>"; print_r($data['all_stafftype']); die();
      $data['main_content']='staff/report/payslip/index';
      $this->load->view('inc/template',$data);
    }

   public function fetchstaffnamewise(){
    $staff_name = $this->input->post('staff_name');
      $month_name = $this->input->post('month_name');
      if (empty($staff_name)) {
        $staff_name = 0; //0 signifies show all staff
      } else {
        if ($staff_name == 'All') {
          $staff_name = 0; //0 signifies show all staff
        }
      }
      $data['staffwisslip']=$this->staffreport_model->getstaffnamewisemonth($staff_name,$month_name);
      $data['all_stafftype']= $this->staffreport_model->get_staff_details();
      $data['main_content']='staff/report/payslip/index';
      $this->load->view('inc/template',$data);
   }

  private function __prepareAddressFields() {
    $addresses = array('0' => 'Present Address', '1'=>'Permanent Address');
  
    $addArr = array();
    // $index = count($this->columnList);
    // echo "<pre>";print_r($index);die();
    $index = 200;
    foreach ($addresses as $addKey => $add) {
        $obj = array();
        $obj['displayName'] = $add;
        $obj['varName'] = $addKey .'_' . 'address';
        $obj['table'] = 'address_info';
        $obj['index'] = ++$index;
        $obj['displayType'] = 'text';
        $obj['dataType'] = 'string';
        $obj['addressType'] = $addKey;
        switch ($addKey) {
          case '0':
          $obj['addressOf'] = 'present_address';
            break;
          case '1':
          $obj['addressOf'] = 'permanent_address';
            break;
        }
        $addArr[] = $obj;    
    }
    return $addArr;

  }

   public function select($id){
      $data['print_selection']= $this->staffreport_model->printdetails($id);
      $data['main_content']='reports/staff_reports/print';
      $this->load->view('inc/template',$data);
   }
   private function __prepareStaffCustomFields(){
      $custom_fields =$this->settings->getSetting('staff_custom_fields');
      
      $customFIelds = array();
      if($custom_fields){
        $indexNumber = 200;
        foreach ($custom_fields as $displayName => $columnName) {
          $obj = array();
          $obj['displayName'] = $displayName;
          $obj['columnNameWithTable'] = $columnName;
          $obj['varName'] = $columnName;
          $obj['columnName'] = $columnName;
          $obj['table'] = 'staff_master';
          $obj['index'] = $indexNumber++;
          $obj['displayType'] = 'text';
          $obj['dataType'] = 'string';
          $customFIelds[] = $obj;
        }  
      }
      
      return $customFIelds;
   }

    public function staff_report() {

      $this->columnList = array_merge($this->columnList,$this->__prepareAddressFields(),$this->__prepareStaffCustomFields());
      // echo "<pre>";print_r($this->columnList);die();
      // $this->columnList = array_merge($this->columnList,$this->__prepareStaffCustomFields());
      
      $data['columnList_json'] = json_encode(array_merge($this->columnList,$this->__prepareAddressFields()));
      $data['columnList'] = array_merge($this->columnList, $this->__prepareAddressFields());
      $data['staffStatusArr'] = $this->settings->getSetting('staff_status');
      $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
      $default_enabled = ['salutation', 'first_name', 'last_name', 'father_name', 'mother_name', 'father_contact_no', 'marital_status', 'gender', 'dob', 'contact_number', 'blood_group', 'staff_type', 'department', 'designation', 'employee_code', 'has_completed_any_bed','email', 'spouse_name', 'spouse_contact_no', 'child1_name', 'child2_name', 'child3_name'];
      $selectedOptions = $this->staffreport_model->get_config_display_fields();
      $selected_enabled_fields = array();
      if(!empty($selectedOptions)){
        $selected_enabled_fields = json_decode($selectedOptions[0]->value, true);
      }
      $filteredColumns = [];
      foreach ($data['columnList'] as $column) {
          if (isset($column['columnName']) && 
              ((is_array($selected_enabled_fields) && in_array($column['columnName'], $selected_enabled_fields)) || 
                in_array($column['columnName'], $default_enabled))
          ) {
              $filteredColumns[] = $column;
          }
      }

      $data['columnList'] = array_merge($filteredColumns,$this->__prepareAddressFields(),$this->__prepareStaffCustomFields());
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'staff/staff_report/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']    = 'staff/staff_report/index_mobile';
      }else{
        $data['main_content']    = 'staff/staff_report/index';     	
      }
      $this->load->view('inc/template', $data);
    }
    
    public function staff_audit_log_report(){
    $data['main_content'] = 'staff/staff_report/staff_audit_log_report';
    $this->load->view('inc/template', $data);
    
    
    }
    public function staff_audit_report(){
      $from_date = $_POST['from_date'];
      $to_date = $_POST['to_date'];
      $result = $this->staffreport_model->staff_audit_report($from_date, $to_date);
      echo json_encode($result);
    }

    public function viewdownloadReport($staff) {
        $staffStatusArr = $this->settings->getSetting('staff_status');
        $data['staff'] = $staff;
        $fields = $this->input->post('fields');
        $status = $this->input->post('status');

        $field_string = implode(',', $fields);
        $report_view = $this->input->post('report_view');

        $fancy_fields = [];
        $data['return_data'] = json_encode(['fields' => $fields, 'report_view' => $report_view]);
        $data['title'] = 'Exported Pdf';
        foreach ($fields as $value) {
            $fancy_fields[] = ucwords(str_replace('_', ' ', $value));
        }
        $data['fancy_fields'] = $fancy_fields;
        $result = $this->staffreport_model->viewStaffReport($field_string,$report_view,$status);

        foreach ($result as &$res) {
          if (!empty($res->designation)) {
             $res->designation = ucwords($res->designation);
          }
          if (!empty($res->dob)) {
             $res->dob = date('d-m-Y',strtotime($res->dob));
          }
          if (!empty($res->joining_date)) {
             $res->joining_date = date('d-m-Y',strtotime($res->joining_date));
          }

          foreach ($staffStatusArr as $key => $val) {
            if (!empty($res->status)) {
              if ($res->status == $key) {
                $res->status = $val;
              }
            }
          }
        }
        $data['exportData'] = $result;
        $data['main_content']    = 'templates/exportView';
        $this->load->view('inc/template', $data);   


    }


    private function _prepareColumnFeildNames($columns) {
      $requiredcolumns = [];
      foreach ($columns as $column) {
        if ($column->Field != 'staffId') {
          $tmpcolumns['real'] = $column->Field;
          $tmpcolumns['fancy'] = ucwords(str_replace('_', ' ', $column->Field));
          $requiredcolumns[] = $tmpcolumns;
        }
      }
      return $requiredcolumns;
    }

    public function getColumns() {
        $view_name = $this->input->post('view_name');
        $raw_columns = $this->staffreport_model->getColumns($view_name);        
        $columns = $this->_prepareColumnFeildNames($raw_columns);
        echo json_encode($columns);
    }

    public function getStaffData(){
      $columnListJSON = $_POST['columnListJSON'];
      $selectedIndex = $_POST['selectedColumns'];
      $staff_status_selected_id= $_POST['staff_status_selected_id'];
      $staff_type= $_POST['staff_type'];
      $columnList = json_decode($columnListJSON);
      //Get the selected Columns
      $selectedColumns = array();
      $displayColumns = array();
      $addressColumns = array();
      foreach($selectedIndex as $fIndex) {
        foreach ($columnList as $col) {
          if ($col->index == $fIndex) {
            if ($col->table == 'address_info'){
              $temp = new stdClass();
              $temp->addressOf = $col->addressOf;
              $temp->addressType = $col->addressType;
              $addressColumns[] = $temp;
            }
            else
            $selectedColumns[] = (array)$col;
            $displayColumns[] = $col;
            break;
          }
        }
      }


      $allColumns = array_merge($this->mandatoryFields, $selectedColumns);
      $displayColumns = array_merge($this->mandatoryFields, $displayColumns);

      //Get column string
      $colString = '';
      foreach ($allColumns as $col) {
          if ($colString != '') {
            $colString .= ',';
          }
          $colString .= $col['columnNameWithTable'] . ' as ' . $col['varName'];
      }
      $staffData = $this->staffreport_model->getStaffData($colString, $staff_status_selected_id, $staff_type);
      foreach ($addressColumns as $addCol) {
        $staffData = $this->staffreport_model->getAndMergeAddresses($staffData,$addCol->addressOf,$addCol->addressType);
      }

      $staff_type_settings = $this->settings->getSetting('staff_type');
      $keys = [];

      if (!empty($staff_type_settings)) {
          $keys = array_keys($staff_type_settings);
      }
      foreach ($staffData as $key => &$val) {

        if (isset($val->staff_type)) {
          // foreach ($this->settings->getSetting('staff_type') as $value => $name) {
          //   if ($val->staff_type == $value) {
          //     $val->staff_type = $name;
          //   }
          // }
          if (empty($staff_type_settings)) {
              $val->staff_type = '<b>Not Enabled</b>';
          } elseif(!in_array($val->staff_type, $keys)){
              $val->staff_type = 'NA';
          }elseif ($val->staff_type == null || $val->staff_type == '') {
              $val->staff_type = '<b>Not Assigned </b> ';
          } else {
              $val->staff_type = $staff_type_settings[$val->staff_type];
          }
        }

        if (isset($val->sCategory)) {
          foreach ($this->settings->getSetting('category') as $value => $name) {
            if ($val->sCategory == $value) {
              $val->sCategory = $name;
            }
          }
        }

        if (isset($val->photo) && !empty($val->photo)) {
          $val->photo = $this->filemanager->getSignedUrlWithExpiry($val->photo,'+1 day');
        }
        if (isset($val->hight_quality_photo) && !empty($val->hight_quality_photo)) {
          $val->hight_quality_photo = $this->filemanager->getSignedUrlWithExpiry($val->hight_quality_photo,'+1 day');
        }
       
        if (isset($val->gender)) {
          if ($val->gender == 'F') {
            $val->gender ='Female';
          }else{
            $val->gender ='Male';
          }
        } 
        if (isset($val->marital_status)) {
          if ($val->marital_status == '1') {
            $val->marital_status ='Married';
          }else{
            $val->marital_status ='Single';
            $val->spouseName = '-';
            $val->spouse_gender = '-';
            $val->spouse_dob = '-';
          }
        }
        
        if(isset($val->spouse_dob) && $val->spouse_dob == '01-01-1970'){
          $val->spouse_dob = '-';
        }
        if (isset($val->include_father_insurance)) {
          if ($val->include_father_insurance == '1') {
            $val->include_father_insurance ='Yes';
          }else{
            $val->include_father_insurance ='No';
          }
        }
        if (isset($val->include_mother_insurance)) {
          if ($val->include_mother_insurance == '1') {
            $val->include_mother_insurance ='Yes';
          }else{
            $val->include_mother_insurance ='No';
          }
        }
        if (isset($val->include_spouse_insurance)) {
          if ($val->include_spouse_insurance == '1') {
            $val->include_spouse_insurance ='Yes';
          }else{
            $val->include_spouse_insurance ='No';
          }
        }
        if (isset($val->include_child1_insurance)) {
          if ($val->include_child1_insurance == '1') {
            $val->include_child1_insurance ='Yes';
          }else{
            $val->include_child1_insurance ='No';
          }
        }
        if (isset($val->include_child2_insurance)) {
          if ($val->include_child2_insurance == '1') {
            $val->include_child2_insurance ='Yes';
          }else{
            $val->include_child2_insurance ='No';
          }
        }
        if (isset($val->include_child3_insurance)) {
          if ($val->include_child3_insurance == '1') {
            $val->include_child3_insurance ='Yes';
          }else{
            $val->include_child3_insurance ='No';
          }
        }
      }

      $data['exportData'] = $staffData;
      $data['selectedColumns'] = $displayColumns;
      echo json_encode($data);
    }

    public function manage_staff_documents_types(){
      $data['main_content'] = 'staff/staff_report/manage_staff_documents_types';
      $this->load->view('inc/template', $data);
    
    }

    function staff_data_missing_report() {
      $data['main_content'] = 'staff/staff_report/staff_data_missing_report';
      $this->load->view('inc/template', $data);
    }

    function staff_edit_history_report() {
      $data['staff_names'] = $this->staffreport_model->get_staff_names();
      $data['main_content'] = 'staff/staff_report/staff_edit_history_report';
      $this->load->view('inc/template', $data);
    }

    public function get_staff_data_missing_details(){
      $fields = $this->db->list_fields('staff_master');
      $uncheckFields = ['id','first_name','last_name','status','staff_type','picture_url','created_on','modified_on','last_modified_by','active','isdummy','shift_id','webcam_avatar','joined_helium','joined_helium_on','profile_confirmed','profile_confirmed_date','oc_platform','oc_link','oc_last_modified_by','oc_additional_info','is_reporting_manager','reporting_manager_id','custom1','custom2','custom3','custom4','custom5','custom6','custom7','custom8','custom9','custom10','custom11','custom12','custom13','custom14','custom15','custom16','custom17','custom18','custom19','custom20'];
      $fData = [];
      $enbled_fields = json_decode($this->staffreport_model->get_config_display_fields()[0]->value);
      foreach ($fields as $field){
        if (!in_array($field, $uncheckFields) && in_array($field,$enbled_fields)) {
            array_push($fData, $field);
        }
      }
      $data['fields'] = $fData;
      $data['staff_data'] = $this->staffreport_model->get_staff_missing_data(implode(',',$fData));
      echo json_encode(array('fields'=> $data['fields'],'staff_data'=>$data['staff_data']));
    }

    public function get_staff_document_types() {
      $doc_name= $this->staffreport_model->get_staff_document_types();
      echo json_encode($doc_name);
    }

    public function add_staff_document_types() {
      $doc_name= $_POST['doc_name'];
      $visibility= $_POST['visibility'];
      echo $this->staffreport_model->add_staff_document_types($doc_name, $visibility);
    }

    public function edit_staff_document_types() {
      $visibility= $_POST['visibility'];
      $doc_name_old= $_POST['doc_name_old'];
      echo $this->staffreport_model->edit_staff_document_types($visibility, $doc_name_old);
    }

    public function delete_document_type() {
      $primary_id= $_POST['primary_id'];
      echo $this->staffreport_model->delete_document_type($primary_id);
    }

    public function  get_staff_history_data(){
      $result = $this->staffreport_model->get_staff_history_data($_POST['from_date'],$_POST['to_date'],$_POST['staff_name']);
      echo json_encode($result);
    }

    public function save_staff_report_template(){
      echo $this->staffreport_model->save_staff_report_template($_POST);
    }
      
    public function get_predefined_names(){
      $result = $this->staffreport_model->get_predefined_names();
      echo json_encode($result);
    }

    public function get_report_template_data(){
      $result = $this->staffreport_model->get_predefined_names($_POST['report_template_id']);
      echo json_encode($result);
    }
 }