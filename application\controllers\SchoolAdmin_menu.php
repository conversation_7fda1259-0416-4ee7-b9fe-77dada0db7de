<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class SchoolAdmin_menu extends CI_Controller {

	public function __construct() {		
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
	      redirect('auth/login', 'refresh');
	    }
	}

	public function index() {
		/*$data['isProvisionPermitted'] = $this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_STAFF');
		$data['permitRolesAccess'] = $this->authorization->isAuthorized('USER_MANAGEMENT.ASSIGN_ROLES_AND_PRIVILEGES');
		$data['permit_staff_sibling_add'] = $this->authorization->isAuthorized('USER_MANAGEMENT.CONNECT_SIBLINGS');
		$data['permitParentProvisioining'] = $this->authorization->isAuthorized ('USER_MANAGEMENT.PROVISION_PARENTS');
		$data['permitParentActivation'] = $this->authorization->isAuthorized ('USER_MANAGEMENT.ACTIVATE_PARENTS');
		$data['permitParentActivationReport'] = $this->authorization->isAuthorized ('USER_MANAGEMENT.ACTIVATE_PARENTS_REPORTS');
		$data['permitStaffProvisioning'] = $this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_STAFF');*/

		$site_url = site_url();
    $data['tiles'] = array(
      [
        'title' => 'Connect Siblings/Staff',
        'sub_title' => 'Connect Siblings & Staff',
        'icon' => 'svg_icons/connectingsibblingstaffs.svg',
        'url' => $site_url.'student/sibling_staff_controller',
        'permission' => $this->authorization->isAuthorized('USER_MANAGEMENT.CONNECT_SIBLINGS')
      ],
      // [
      //   'title' => 'Provision Parent Credentials',
      //   'sub_title' => 'Provision parent login',
      //   'icon' => 'svg_icons/talktous.svg',
      //   'url' => $site_url.'user_provisioning_controller/index',
      //   'permission' => $this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')
      // ],
      [
        'title' => 'Provision Parent Credentials',
        'sub_title' => 'Provision parent login',
        'icon' => 'svg_icons/talktous.svg',
        'url' => $site_url.'parent_activation',
        'permission' => $this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')
      ],
      [
        'title' => 'Parent Provision Report',
        'sub_title' => 'View user provision report',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'user_provisioning_controller/provision_report',
        'permission' => $this->authorization->isAuthorized('USER_MANAGEMENT.ACTIVATE_PARENTS_REPORTS')
      ],
      [
        'title' => 'Parent Login Report',
        'sub_title' => 'View parent login report',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'user_provisioning_controller/app_logins',
        'permission' => $this->authorization->isAuthorized('USER_MANAGEMENT.ACTIVATE_PARENTS_REPORTS')
      ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['staff_tiles'] = array(
      // [
      //   'title' => 'Provision Staff',
      //   'sub_title' => 'Activate & send credentials',
      //   'icon' => 'svg_icons/staff.svg',
      //   'url' => $site_url.'staff/Staff_controller/provisionStaff',
      //   'permission' => $this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_STAFF')
      // ],
      [
        'title' => 'Provision Staff',
        'sub_title' => 'Activate & send credentials',
        'icon' => 'svg_icons/staff.svg',
        'url' => $site_url.'staff/Staff_controller/provision_staff',
        'permission' => $this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_STAFF')
      ],
      [
        'title' => 'Manage Roles & Privileges',
        'sub_title' => 'Manage roles and privileges of staff',
        'icon' => 'svg_icons/staffrolesandpermissions.svg',
        'url' => $site_url.'roles',
        'permission' => $this->authorization->isAuthorized('USER_MANAGEMENT.ASSIGN_ROLES_AND_PRIVILEGES')
      ],
      [
        'title' => 'Veiw Staff-Wise Roles and Privileges',
        'sub_title' => 'Veiw Staff-Wise Roles and Privileges',
        'icon' => 'svg_icons/view.svg',
        'url' => $site_url.'roles/view_single_staff_privileges',
        'permission' => $this->authorization->isAuthorized('USER_MANAGEMENT.ASSIGN_ROLES_AND_PRIVILEGES')
      ],
      [
        'title' => 'View Role-Wise Privileges to Staff',
        'sub_title' => 'View Role-Wise Privileges to Staff',
        'icon' => 'svg_icons/view.svg',
        'url' => $site_url.'roles/view_role_privileges',
        'permission' => $this->authorization->isAuthorized('USER_MANAGEMENT.ASSIGN_ROLES_AND_PRIVILEGES')
      ]
    );
    $data['staff_tiles'] = checkTilePermissions($data['staff_tiles']);

    $data['reports'] = array(
      [
        'title' => 'Access Control History',
        'sub_title' => 'Access Control History',
        'icon' => 'svg_icons/staff.svg',
        'url' => $site_url.'roles/role_history_report',
        'permission' => $this->authorization->isAuthorized('USER_MANAGEMENT.ACCESS_CONTROL_HISTORY')
      ],
    );
    $data['report_tiles'] = checkTilePermissions($data['reports']);

    $data['pre_tiles'] = array(
      [
        'title' => 'Privilege Management',
        'sub_title' => 'Add/Edit Privileges',
        'icon' => 'svg_icons/privilagemanagement.svg',
        'url' => $site_url.'roles/privileges',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Manage Alerts',
        'sub_title' => 'Add/Edit Alerts',
        'icon' => 'svg_icons/privilagemanagement.svg',
        'url' => $site_url.'alert_mgmt/alerts',
        'permission' => $this->authorization->isSuperAdmin()
      ]
    );
    $data['pre_tiles'] = checkTilePermissions($data['pre_tiles']);

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'school_admin/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'school_admin/index_mobile';
    }else{
      $data['main_content'] = 'school_admin/index';    	
    }

		
    	$this->load->view('inc/template', $data);
	}
}
