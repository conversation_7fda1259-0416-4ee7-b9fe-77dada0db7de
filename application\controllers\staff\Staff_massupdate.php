<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>chhipulusu
 *          <EMAIL>
 *
 * Created:  26 June 2019
 *
 * Description: Controller for Mass Update.
 *
 * Requirements: PHP5 or above
 *
 */

 /**
 * Description of Student_controller
 *
 * <AUTHOR>
 */
class Staff_massupdate extends CI_Controller {
  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STAFF_MASTER')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STAFF_MASSUPDATE.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('staff/Staff_massupdate_model');
    $this->config->load('form_elements');
    // $this->config->load('form_elements');
  }   

  public function index() {
    $columnList = [
      [
        'displayName'=>'Salutation',
        'columnNameWithTable'=>'sm.salutation',
        'columnName'=>'salutation',
        'varName'=>'sSalutation',
        'table'=>'staff_master',
        'index'=>'69',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      // [
      //   'displayName'=>'Staff First Name',
      //   'columnNameWithTable'=>'sm.first_name',
      //   'columnName'=>'first_name',
      //   'varName'=>'sFirstName',
      //   'table'=>'staff_master',
      //   'index'=>'1',
      //   'displayType'=>'text',
      //   'dataType'=>'string'
      // ],
      [
        'displayName'=>'Staff Last Name',
        'columnNameWithTable'=>'sm.last_name',
        'columnName'=>'last_name',
        'varName'=>'sLastName',
        'table'=>'staff_master',
        'index'=>'2',
        'displayType'=>'text',
        'dataType'=>'string'
      ],      
      [
        'displayName'=>'Staff Short Name',
        'columnNameWithTable'=>'sm.short_name',
        'columnName'=>'short_name',
        'varName'=>'sShortName',
        'table'=>'staff_master',
        'index'=>'3',
        'displayType'=>'text',
        'dataType'=>'string'
      ],      
      [
        'displayName'=>'Staff Gender',
        'columnNameWithTable'=>'sm.gender',
        'columnName'=>'gender',
        'varName'=>'sGender',
        'optionArrName'=>'genderOptions',
        'table'=>'staff_master',
        'index'=>'5',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Date of Birth',
        'columnNameWithTable'=>'sm.dob',
        'columnName'=>'dob',
        'varName'=>'dob',
        'optionArrName'=>'dob',
        'table'=>'staff_master',
        'index'=>'19',
        'displayType'=>'datetimepicker',
        'dataType'=>'date'
      ],
      [
        'displayName'=>'Nationality',
        'columnNameWithTable'=>'sm.nationality',
        'columnName'=>'nationality',
        'varName'=>'nationality',
        'optionArrName'=>'nationality',
        'table'=>'staff_master',
        'index'=>'32',
        'required'=>false,'displayType'=>'combobox',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Religion',
        'columnNameWithTable'=>'sm.religion',
        'columnName'=>'religion',
        'varName'=>'religion',
        'optionArrName'=>'religion',
        'table'=>'staff_master',
        'index'=>'60',
        'required'=>false,'displayType'=>'combobox',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Marital Status',
        'columnNameWithTable'=>'sm.marital_status',
        'columnName'=>'marital_status',
        'varName'=>'marital_status',
        'optionArrName'=>'marital_statusOptions',
        'table'=>'staff_master',
        'index'=>'6',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Staff Contact Number',
        'columnNameWithTable'=>'sm.contact_number',
        'columnName'=>'contact_number',
        'varName'=>'sContactNumber',
        'table'=>'staff_master',
        'index'=>'100',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Alternative Number',
        'columnNameWithTable'=>'sm.alternative_number',
        'columnName'=>'alternative_number',
        'varName'=>'alternative_number',
        'optionArrName'=>'alternative_number',
        'table'=>'staff_master',
        'index'=>'38',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Staff Email',
        'columnNameWithTable'=>'us.email',
        'columnName'=>'email',
        'varName'=>'semail',
        'table'=>'users',
        'index'=>'4',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Employee Code',
        'columnNameWithTable'=>'sm.employee_code',
        'columnName'=>'employee_code',
        'varName'=>'sEmployeeCode',
        'table'=>'staff_master',
        'index'=>'17',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Emergency info',
        'columnNameWithTable'=>'sm.emergency_info',
        'columnName'=>'emergency_info',
        'varName'=>'emergency_info',
        'optionArrName'=>'emergency_info',
        'table'=>'staff_master',
        'index'=>'39',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Staff Type',
        'columnNameWithTable'=>'sm.staff_type',
        'columnName'=>'staff_type',
        'varName'=>'staff_type',
        'optionArrName'=>'staffType',
        'table'=>'staff_master',
        'index'=>'22',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Date Of Joining',
        'columnNameWithTable'=>'sm.joining_date',
        'columnName'=>'joining_date',
        'varName'=>'JoingDate',
        'optionArrName'=>'joingOptions',
        'table'=>'staff_master',
        'index'=>'14',
        'displayType'=>'datetimepicker',
        'dataType'=>'date'
      ],
      [
        'displayName'=>'Department',
        'columnNameWithTable'=>'sm.department',
        'columnName'=>'department',
        'varName'=>'department',
        'optionArrName'=>'departmentOptions',
        'table'=>'staff_master',
        'index'=>'20',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Designation',
        'columnNameWithTable'=>'sm.designation',
        'columnName'=>'designation',
        'varName'=>'designation',
        'optionArrName'=>'designationOptions',
        'table'=>'staff_master',
        'index'=>'21',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Staff Biometric Code',
        'columnNameWithTable'=>'sa.staff_code',
        'columnName'=>'staff_code',
        'varName'=>'staffCode',
        'table'=>'staff_attendance_code',
        'index'=>'12',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Shift Id',
        'columnNameWithTable'=>'sm.shift_id',
        'columnName'=>'shift_id',
        'varName'=>'shiftId',
        'optionArrName'=>'shiftOptions',
        'table'=>'staff_master',
        'index'=>'13',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Online Email ID',
        'columnNameWithTable'=>'sm.oc_mail_id',
        'columnName'=>'oc_mail_id',
        'varName'=>'sOCMailId',
        'table'=>'staff_master',
        'index'=>'15',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Online Password',
        'columnNameWithTable'=>'sm.oc_password',
        'columnName'=>'oc_password',
        'varName'=>'sOCPassword',
        'table'=>'staff_master',
        'index'=>'16',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Subject specialization',
        'columnNameWithTable'=>'sm.subject_specialization',
        'columnName'=>'subject_specialization',
        'varName'=>'subject_specialization',
        'optionArrName'=>'subject_specialization',
        'table'=>'staff_master',
        'index'=>'33',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Nature of appointment',
        'columnNameWithTable'=>'sm.nature_of_appointment',
        'columnName'=>'nature_of_appointment',
        'varName'=>'nature_of_appointment',
        'optionArrName'=>'nature_of_appointment',
        'table'=>'staff_master',
        'index'=>'61',
        'required'=>false,'displayType'=>'combobox',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Has completed any B.Ed',
        'columnNameWithTable'=>'sm.has_completed_any_bed',
        'columnName'=>'has_completed_any_bed',
        'varName'=>'has_completed_any_bed',
        'optionArrName'=>'has_completed_any_bed',
        'table'=>'staff_master',
        'index'=>'62',
        'required'=>false,'displayType'=>'combobox',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Total experience',
        'columnNameWithTable'=>'sm.total_experience',
        'columnName'=>'total_experience',
        'varName'=>'total_experience',
        'optionArrName'=>'total_experience',
        'table'=>'staff_master',
        'index'=>'34',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Total education experience',
        'columnNameWithTable'=>'sm.total_education_experience',
        'columnName'=>'total_education_experience',
        'varName'=>'total_education_experience',
        'optionArrName'=>'total_education_experience',
        'table'=>'staff_master',
        'index'=>'35',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Math high grade',
        'columnNameWithTable'=>'sm.math_high_grade',
        'columnName'=>'math_high_grade',
        'varName'=>'math_high_grade',
        'optionArrName'=>'math_high_grade',
        'table'=>'staff_master',
        'index'=>'40',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'English high grade',
        'columnNameWithTable'=>'sm.english_high_grade',
        'columnName'=>'english_high_grade',
        'varName'=>'english_high_grade',
        'optionArrName'=>'english_high_grade',
        'table'=>'staff_master',
        'index'=>'41',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Social high grade',
        'columnNameWithTable'=>'sm.social_high_grade',
        'columnName'=>'social_high_grade',
        'varName'=>'social_high_grade',
        'optionArrName'=>'social_high_grade',
        'table'=>'staff_master',
        'index'=>'42',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Appointed subject',
        'columnNameWithTable'=>'sm.appointed_subject',
        'columnName'=>'appointed_subject',
        'varName'=>'appointed_subject',
        'optionArrName'=>'appointed_subject',
        'table'=>'staff_master',
        'index'=>'43',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Classes taught',
        'columnNameWithTable'=>'sm.classes_taught',
        'columnName'=>'classes_taught',
        'varName'=>'classes_taught',
        'optionArrName'=>'classes_taught',
        'table'=>'staff_master',
        'index'=>'44',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Main sub taught',
        'columnNameWithTable'=>'sm.main_sub_taught',
        'columnName'=>'main_sub_taught',
        'varName'=>'main_sub_taught',
        'optionArrName'=>'main_sub_taught',
        'table'=>'staff_master',
        'index'=>'45',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Add sub taught',
        'columnNameWithTable'=>'sm.add_sub_taught',
        'columnName'=>'add_sub_taught',
        'varName'=>'add_sub_taught',
        'optionArrName'=>'add_sub_taught',
        'table'=>'staff_master',
        'index'=>'46',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Last working day',
        'columnNameWithTable'=>'sm.last_working_day',
        'columnName'=>'last_working_day',
        'varName'=>'last_working_day',
        'optionArrName'=>'last_working_day',
        'table'=>'staff_master',
        'index'=>'47',
        'displayType'=>'datetimepicker',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Qualification',
        'columnNameWithTable'=>'sm.qualification',
        'columnName'=>'qualification',
        'varName'=>'qualification',
        'optionArrName'=>'qualificationOption',
        'table'=>'staff_master',
        'index'=>'23',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Aadhar Number',
        'columnNameWithTable'=>'sm.aadhar_number',
        'columnName'=>'aadhar_number',
        'varName'=>'aadhar_number',
        'optionArrName'=>'aadharNumberOptions',
        'table'=>'staff_master',
        'index'=>'24',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'PAN Number',
        'columnNameWithTable'=>'npm.pan_number',
        'columnName'=>'pan_number',
        'varName'=>'pan_number',
        'optionArrName'=>'panNumber',
        'table'=>'new_payroll_master',
        'index'=>'25',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'UAN Number',
        'columnNameWithTable'=>'npm.uan_number',
        'columnName'=>'uan_number',
        'varName'=>'uan_number',
        'optionArrName'=>'uan_number',
        'table'=>'new_payroll_master',
        'index'=>'26',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'PF Number',
        'columnNameWithTable'=>'npm.pf_number',
        'columnName'=>'pf_number',
        'varName'=>'pf_number',
        'optionArrName'=>'pf_number',
        'table'=>'new_payroll_master',
        'index'=>'31',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Account Number',
        'columnNameWithTable'=>'npm.account_number',
        'columnName'=>'account_number',
        'varName'=>'account_number',
        'optionArrName'=>'account_number',
        'table'=>'new_payroll_master',
        'index'=>'27',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Bank Name',
        'columnNameWithTable'=>'npm.bank_name',
        'columnName'=>'bank_name',
        'varName'=>'bank_name',
        'optionArrName'=>'bank_name',
        'table'=>'new_payroll_master',
        'index'=>'30',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Branch Name',
        'columnNameWithTable'=>'npm.branch_name',
        'columnName'=>'branch_name',
        'varName'=>'branch_name',
        'optionArrName'=>'branch_name',
        'table'=>'new_payroll_master',
        'index'=>'29',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'IFSC Code',
        'columnNameWithTable'=>'npm.ifsc_code',
        'columnName'=>'ifsc_code',
        'varName'=>'ifsc_code',
        'optionArrName'=>'ifsc_code',
        'table'=>'new_payroll_master',
        'index'=>'28',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Voter id',
        'columnNameWithTable'=>'sm.voter_id',
        'columnName'=>'voter_id',
        'varName'=>'voter_id',
        'optionArrName'=>'voter_id',
        'table'=>'staff_master',
        'index'=>'48',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Passport number',
        'columnNameWithTable'=>'sm.passport_number',
        'columnName'=>'passport_number',
        'varName'=>'passport_number',
        'optionArrName'=>'passport_number',
        'table'=>'staff_master',
        'index'=>'55',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Passport place of issue',
        'columnNameWithTable'=>'sm.passport_place_of_issue',
        'columnName'=>'passport_place_of_issue',
        'varName'=>'passport_place_of_issue',
        'optionArrName'=>'passport_place_of_issue',
        'table'=>'staff_master',
        'index'=>'56',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Passport date of issue',
        'columnNameWithTable'=>'sm.passport_date_of_issue',
        'columnName'=>'passport_date_of_issue',
        'varName'=>'passport_date_of_issue',
        'optionArrName'=>'passport_date_of_issue',
        'table'=>'staff_master',
        'index'=>'57',
        'displayType'=>'datetimepicker',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Passport expiry date',
        'columnNameWithTable'=>'sm.passport_expiry_date',
        'columnName'=>'passport_expiry_date',
        'varName'=>'passport_expiry_date',
        'optionArrName'=>'passport_expiry_date',
        'table'=>'staff_master',
        'index'=>'58',
        'displayType'=>'datetimepicker',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Visa details',
        'columnNameWithTable'=>'sm.visa_details',
        'columnName'=>'visa_details',
        'varName'=>'visa_details',
        'optionArrName'=>'visa_details',
        'table'=>'staff_master',
        'index'=>'59',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Mother First Name',
        'columnNameWithTable'=>'sm.mother_first_name',
        'columnName'=>'mother_first_name',
        'varName'=>'sMotherFirstName',
        'table'=>'staff_master',
        'index'=>'7',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Mother Last Name',
        'columnNameWithTable'=>'sm.mother_last_name',
        'columnName'=>'mother_last_name',
        'varName'=>'sMotherLastName',
        'table'=>'staff_master',
        'index'=>'8',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Father First Name',
        'columnNameWithTable'=>'sm.father_first_name',
        'columnName'=>'father_first_name',
        'varName'=>'sFatherFirstName',
        'table'=>'staff_master',
        'index'=>'9',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Father Last Name',
        'columnNameWithTable'=>'sm.father_last_name',
        'columnName'=>'father_last_name',
        'varName'=>'sFatherLastName',
        'table'=>'staff_master',
        'index'=>'10',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Spouse First Name',
        'columnNameWithTable'=>'sm.spouse_name',
        'columnName'=>'spouse_name',
        'varName'=>'sSpouseName',
        'table'=>'staff_master',
        'index'=>'11',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Spouse Contact Number',
        'columnNameWithTable'=>'sm.spouse_contact_no',
        'columnName'=>'spouse_contact_no',
        'varName'=>'spouse_contact_no',
        'optionArrName'=>'spouse_contact_no',
        'table'=>'staff_master',
        'index'=>'37',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Height',
        'columnNameWithTable'=>'sm.height',
        'columnName'=>'height',
        'varName'=>'height',
        'optionArrName'=>'height',
        'table'=>'staff_master',
        'index'=>'49',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Weight',
        'columnNameWithTable'=>'sm.weight',
        'columnName'=>'weight',
        'varName'=>'weight',
        'optionArrName'=>'weight',
        'table'=>'staff_master',
        'index'=>'50',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Allergies',
        'columnNameWithTable'=>'sm.allergies',
        'columnName'=>'allergies',
        'varName'=>'allergies',
        'optionArrName'=>'allergies',
        'table'=>'staff_master',
        'index'=>'51',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Blood Group',
        'columnNameWithTable'=>'sm.blood_group',
        'columnName'=>'blood_group',
        'varName'=>'blood_group',
        'optionArrName'=>'blood_group',
        'table'=>'staff_master',
        'index'=>'18',
        'required'=>false,'displayType'=>'combobox',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Medical issues',
        'columnNameWithTable'=>'sm.medical_issues',
        'columnName'=>'medical_issues',
        'varName'=>'medical_issues',
        'optionArrName'=>'medical_issues',
        'table'=>'staff_master',
        'index'=>'52',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Identification mark',
        'columnNameWithTable'=>'sm.identification_mark',
        'columnName'=>'identification_mark',
        'varName'=>'identification_mark',
        'optionArrName'=>'identification_mark',
        'table'=>'staff_master',
        'index'=>'53',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Person with disability',
        'columnNameWithTable'=>'sm.person_with_disability',
        'columnName'=>'person_with_disability',
        'varName'=>'person_with_disability',
        'optionArrName'=>'person_with_disability',
        'table'=>'staff_master',
        'index'=>'54',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Father Occupation',
        'columnNameWithTable' => 'sm.father_occupation',
        'columnName'=>'father_occupation',
        'varName'=>'father_occupation',
        'optionArrName'=>'father_occupation',
        'table'=>'staff_master',
        'index'=>'63',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Father Contact Number',
        'columnNameWithTable' => 'sm.father_contact_no',
        'columnName'=>'father_contact_no',
        'varName'=>'father_contact_no',
        'optionArrName'=>'father_contact_no',
        'table'=>'staff_master',
        'index'=>'64',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
          'displayName'=>'Spouse Occupation',
          'columnNameWithTable' => 'sm.spouse_occupation',
          'columnName'=>'spouse_occupation',
          'varName'=>'spouse_occupation',
          'optionArrName'=>'spouse_occupation',
          'table'=>'staff_master',
          'index'=>'65',
          'displayType'=>'text',
          'dataType'=>'string'
      ],
      [
          'displayName'=>'Staff Reference Code',
          'columnNameWithTable' => 'sm.staff_reference_code',
          'columnName'=>'staff_reference_code',
          'varName'=>'staff_reference_code',
          'optionArrName'=>'staff_reference_code',
          'table'=>'staff_master',
          'index'=>'66',
          'displayType'=>'text',
          'dataType'=>'string'
      ],
      [
        'displayName'=>'Previous Designation Name',
        'columnNameWithTable' => 'sm.previous_designation_name',
        'columnName'=>'previous_designation_name',
        'varName'=>'previous_designation_name',
        'optionArrName'=>'previousDesignationOptions',
        'table'=>'staff_master',
        'index'=>'67',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Is Primary Instance',
        'columnNameWithTable' => 'sm.is_primary_instance',
        'columnName'=>'is_primary_instance',
        'varName'=>'is_primary_instance',
        'optionArrName'=>'isPrimaryOptions',
        'table'=>'staff_master',
        'index'=>'68',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'boolean'
      ],
      [
        'displayName' => ' Personal Mail Id',
        'columnNameWithTable' => 'sm.personal_mail_id',
        'columnName' => 'personal_mail_id',
        'varName' => 'personal_mail_id',
        'table' => 'staff_master',
        'index' => '70',
        'displayType' => 'text',
        'dataType' => 'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Caste',
        'columnNameWithTable' => 'caste',
        'columnName'=>'caste',
        'varName'=>'sCaste',
        'table'=>'staff_master',
        'index'=>'71',
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Category',
        'columnNameWithTable' => 'sm.category',
        'columnName'=>'category',
        'varName'=>'sCategory',
        'optionArrName'=>'category',
        'table'=>'staff_master',
        'index'=>'72',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Has completed any PGDEI',
        'columnNameWithTable'=>'sm.has_completed_any_pgdei',
        'columnName'=>'has_completed_any_pgdei',
        'varName'=>'has_completed_any_pgdei',
        'optionArrName'=>'has_completed_any_pgdei',
        'table'=>'staff_master',
        'index'=>'73',
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Trained to teach',
        'columnNameWithTable' => 'sm.trained_to_teach',
        'columnName'=>'trained_to_teach',
        'varName'=>'trained_to_teach',
        'varName'=>'trained_to_teach',
        'table'=>'staff_master',
        'index'=>'36',
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'ESI Number',
        'columnNameWithTable'=>'npm.esi_number',
        'columnName'=>'esi_number',
        'varName'=>'esi_number',
        'optionArrName'=>'esi_number',
        'table'=>'new_payroll_master',
        'index'=>'74',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Previous Employer Joining Date',
        'columnNameWithTable'=>'npm.previous_emplyoer_joining_date',
        'columnName'=>'previous_emplyoer_joining_date',
        'varName'=>'previous_emplyoer_joining_date',
        'optionArrName'=>'previous_emplyoer_joining_date',
        'table'=>'new_payroll_master',
        'index'=>'75',
        'displayType'=>'datetimepicker',
        'dataType'=>'date'
      ],
      [
        'displayName'=>'Previous Employer Exit Date',
        'columnNameWithTable'=>'npm.previous_employer_exit_date',
        'columnName'=>'previous_employer_exit_date',
        'varName'=>'previous_employer_exit_date',
        'optionArrName'=>'previous_employer_exit_date',
        'table'=>'new_payroll_master',
        'index'=>'76',
        'displayType'=>'datetimepicker',
        'dataType'=>'date'
      ],
      [
        'displayName'=>'Father DOB',
        'columnNameWithTable'=>'sm.father_dob',
        'columnName'=>'father_dob',
        'varName'=>'father_dob',
        'optionArrName'=>'father_dob',
        'table'=>'staff_master',
        'index'=>'77',
        'displayType'=>'datetimepicker',
        'dataType'=>'date'
      ],
      [
        'displayName'=>'Mother DOB',
        'columnNameWithTable'=>'sm.mother_dob',
        'columnName'=>'mother_dob',
        'varName'=>'mother_dob',
        'optionArrName'=>'mother_dob',
        'table'=>'staff_master',
        'index'=>'78',
        'displayType'=>'datetimepicker',
        'dataType'=>'date'
      ],
      [
        'displayName'=>'Father Is Dependent',
        'columnNameWithTable'=>'sm.father_is_dependent',
        'columnName'=>'father_is_dependent',
        'varName'=>'father_is_dependent',
        'optionArrName'=>'dependentOptions',
        'table'=>'staff_master',
        'index'=>'79',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Mother Is Dependent',
        'columnNameWithTable'=>'sm.mother_is_dependent',
        'columnName'=>'mother_is_dependent',
        'varName'=>'mother_is_dependent',
        'optionArrName'=>'dependentOptions',
        'table'=>'staff_master',
        'index'=>'80',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Spouse Last Name',
        'columnNameWithTable'=>'sm.spouse_last_name',
        'columnName'=>'spouse_last_name',
        'varName'=>'sSpouseLastName',
        'table'=>'staff_master',
        'index'=>'81',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Spouse DOB',
        'columnNameWithTable'=>'sm.spouse_dob',
        'columnName'=>'spouse_dob',
        'varName'=>'spouse_dob',
        'optionArrName'=>'spouse_dob',
        'table'=>'staff_master',
        'index'=>'82',
        'displayType'=>'datetimepicker',
        'dataType'=>'date'
      ],
      [
        'displayName'=>'Spouse Is Dependent',
        'columnNameWithTable'=>'sm.spouse_is_dependent',
        'columnName'=>'spouse_is_dependent',
        'varName'=>'spouse_is_dependent',
        'optionArrName'=>'dependentOptions',
        'table'=>'staff_master',
        'index'=>'83',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Spouse Gender',
        'columnNameWithTable'=>'sm.spouse_gender',
        'columnName'=>'spouse_gender',
        'varName'=>'spouse_gender',
        'optionArrName'=>'genderOptions',
        'table'=>'staff_master',
        'index'=>'84',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Child1 First Name',
        'columnNameWithTable'=>'sm.child1_first_name',
        'columnName'=>'child1_first_name',
        'varName'=>'child1_first_name',
        'table'=>'staff_master',
        'index'=>'85',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Child1 Last Name',
        'columnNameWithTable'=>'sm.child1_last_name',
        'columnName'=>'child1_last_name',
        'varName'=>'child1_last_name',
        'table'=>'staff_master',
        'index'=>'86',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Child1 DOB',
        'columnNameWithTable'=>'sm.child1_dob',
        'columnName'=>'child1_dob',
        'varName'=>'child1_dob',
        'optionArrName'=>'child1_dob',
        'table'=>'staff_master',
        'index'=>'87',
        'displayType'=>'datetimepicker',
        'dataType'=>'date'
      ],
      [
        'displayName'=>'Child1 Gender',
        'columnNameWithTable'=>'sm.child1_gender',
        'columnName'=>'child1_gender',
        'varName'=>'child1_gender',
        'optionArrName'=>'genderOptions',
        'table'=>'staff_master',
        'index'=>'88',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Child1 Is Dependent',
        'columnNameWithTable'=>'sm.child1_is_dependent',
        'columnName'=>'child1_is_dependent',
        'varName'=>'child1_is_dependent',
        'optionArrName'=>'dependentOptions',
        'table'=>'staff_master',
        'index'=>'89',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Child2 First Name',
        'columnNameWithTable'=>'sm.child2_first_name',
        'columnName'=>'child2_first_name',
        'varName'=>'child2_first_name',
        'table'=>'staff_master',
        'index'=>'90',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Child2 Last Name',
        'columnNameWithTable'=>'sm.child2_last_name',
        'columnName'=>'child2_last_name',
        'varName'=>'child2_last_name',
        'table'=>'staff_master',
        'index'=>'91',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Child2 DOB',
        'columnNameWithTable'=>'sm.child2_dob',
        'columnName'=>'child2_dob',
        'varName'=>'child2_dob',
        'optionArrName'=>'child2_dob',
        'table'=>'staff_master',
        'index'=>'92',
        'displayType'=>'datetimepicker',
        'dataType'=>'date'
      ],
      [
        'displayName'=>'Child2 Gender',
        'columnNameWithTable'=>'sm.child2_gender',
        'columnName'=>'child2_gender',
        'varName'=>'child2_gender',
        'optionArrName'=>'genderOptions',
        'table'=>'staff_master',
        'index'=>'93',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Child2 Is Dependent',
        'columnNameWithTable'=>'sm.child2_is_dependent',
        'columnName'=>'child2_is_dependent',
        'varName'=>'child2_is_dependent',
        'optionArrName'=>'dependentOptions',
        'table'=>'staff_master',
        'index'=>'94',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Child3 First Name',
        'columnNameWithTable'=>'sm.child3_first_name',
        'columnName'=>'child3_first_name',
        'varName'=>'child3_first_name',
        'table'=>'staff_master',
        'index'=>'95',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Child3 Last Name',
        'columnNameWithTable'=>'sm.child3_last_name',
        'columnName'=>'child3_last_name',
        'varName'=>'child3_last_name',
        'table'=>'staff_master',
        'index'=>'96',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Child3 DOB',
        'columnNameWithTable'=>'sm.child3_dob',
        'columnName'=>'child3_dob',
        'varName'=>'child3_dob',
        'optionArrName'=>'child3_dob',
        'table'=>'staff_master',
        'index'=>'97',
        'displayType'=>'datetimepicker',
        'dataType'=>'date'
      ],
      [
        'displayName'=>'Child3 Gender',
        'columnNameWithTable'=>'sm.child3_gender',
        'columnName'=>'child3_gender',
        'varName'=>'child3_gender',
        'optionArrName'=>'genderOptions',
        'table'=>'staff_master',
        'index'=>'98',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Child3 Is Dependent',
        'columnNameWithTable'=>'sm.child3_is_dependent',
        'columnName'=>'child3_is_dependent',
        'varName'=>'child3_is_dependent',
        'optionArrName'=>'dependentOptions',
        'table'=>'staff_master',
        'index'=>'99',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Staff House',
        'columnNameWithTable'=>'sm.staff_house',
        'columnName'=>'staff_house',
        'varName'=>'sHouse',
        'optionArrName'=>'staffHouse',
        'table'=>'staff_master',
        'index'=>'101',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],    
      [
        'displayName'=>'Include Father Insurance',
        'columnNameWithTable'=>'sm.include_father_insurance',
        'columnName'=>'include_father_insurance',
        'varName'=>'include_father_insurance',
        'optionArrName'=>'dependentOptions',
        'table'=>'staff_master',
        'index'=>'102',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Include Mother Insurance',
        'columnNameWithTable'=>'sm.include_mother_insurance',
        'columnName'=>'include_mother_insurance',
        'varName'=>'include_mother_insurance',
        'optionArrName'=>'dependentOptions',
        'table'=>'staff_master',
        'index'=>'103',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Include Spouse Insurance',
        'columnNameWithTable'=>'sm.include_spouse_insurance',
        'columnName'=>'include_spouse_insurance',
        'varName'=>'include_spouse_insurance',
        'optionArrName'=>'dependentOptions',
        'table'=>'staff_master',
        'index'=>'104',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Include Child1 Insurance',
        'columnNameWithTable'=>'sm.include_child1_insurance',
        'columnName'=>'include_child1_insurance',
        'varName'=>'include_child1_insurance',
        'optionArrName'=>'dependentOptions',
        'table'=>'staff_master',
        'index'=>'105',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Include Child2 Insurance',
        'columnNameWithTable'=>'sm.include_child2_insurance',
        'columnName'=>'include_child2_insurance',
        'varName'=>'include_child2_insurance',
        'optionArrName'=>'dependentOptions',
        'table'=>'staff_master',
        'index'=>'106',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Include Child3 Insurance',
        'columnNameWithTable'=>'sm.include_child3_insurance',
        'columnName'=>'include_child3_insurance',
        'varName'=>'include_child3_insurance',
        'optionArrName'=>'dependentOptions',
        'table'=>'staff_master',
        'index'=>'107',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string'
      ],
    ];

    
    $columnList = array_merge($columnList,$this->__prepareStaffCustomFields());
    $data['columnList_json'] = json_encode($columnList);
    $data['columnList'] = $columnList;
    $default_enabled = ['salutation', 'first_name', 'last_name', 'father_first_name', 'mother_first_name', 'father_contact_no', 'marital_status', 'gender', 'dob', 'contact_number', 'blood_group', 'staff_type', 'department', 'designation', 'employee_code','email','has_completed_any_bed','spouse_name','spouse_contact_no'];
    $selectedOptions = $this->Staff_massupdate_model->get_config_display_fields();
    $selected_enabled_fields = array_map('json_decode', array_column($selectedOptions, 'value'));
    $fields = [];
    foreach ($data['columnList'] as $key => $val) {
        if (isset($val['columnName'])) {
            foreach ($selected_enabled_fields as $selectedFields) {
                if (in_array($val['columnName'], $selectedFields) || in_array($val['columnName'], $default_enabled)) {
                    $fields[$key] = $val;
                    break;
                }
            }
        }
    }
    $fields = array_merge($fields, $this->__prepareStaffCustomFields());
    
    usort($fields, function($a, $b) {
        return $a['columnName'] <=> $b['columnName']; // Sort by columnName
    });
    $data['selectedEnabledFields'] = $fields;


    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/staff_massupdate/index_tablet_blocked';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/staff_massupdate/index_mobile_blocked';
    }else{
      $data['main_content'] = 'staff/staff_massupdate/index';     	
    }

    $this->load->view('inc/template', $data);
  }

  private function __prepareStaffCustomFields(){
     $custom_fields =$this->settings->getSetting('staff_custom_fields');
      $customFIelds = array();
      if($custom_fields){
        $indexNumber = 200;
        foreach ($custom_fields as $displayName => $columnName) {
          $obj = array();
          $obj['displayName'] = $displayName;
          $obj['columnNameWithTable'] = $columnName;
          $obj['varName'] = $columnName;
          $obj['columnName'] = $columnName;
          $obj['optionArrName'] = $columnName;
          $obj['table'] = 'staff_master';
          $obj['index'] = $indexNumber++;
          $obj['displayType'] = 'text';
          $obj['dataType'] = 'string';
          $customFIelds[] = $obj;
        }  
      }
      
      return $customFIelds;
  }

  public function startMassUpdate() {
    $mandatoryFields = [
      [
        'displayName'=>'Id',
        'columnNameWithTable'=>'sm.id',
        'columnName'=>'id',
        'varName'=>'smId',
        'table'=>'staff_master',
        'displayType'=>'hidden',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Staff Id',
        'columnNameWithTable'=>'sm.id',
        'columnName'=>'ID',
        'varName'=>'SMId',
        'table'=>'staff_master',
        'displayType'=>'label',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'User Id',
        'columnNameWithTable'=>'us.id',
        'columnName'=>'id',
        'varName'=>'usId',
        'table'=>'users',
        'displayType'=>'hidden',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'First Name',
        'columnNameWithTable'=>'sm.first_name',
        'columnName'=>'first_name',
        'varName'=>'sFirstName',
        'table'=>'staff_master',
        'displayType'=>'label',
        'dataType'=>'string'
      ]
    ];
    $selectedIndex = $this->input->post('fields');
    $columnList = json_decode($this->input->post('columnList_json'));

    //echo '<pre>';print_r($selectedIndex);die();

    //Get the selected Columns
    $selectedColumns = array();
    foreach($selectedIndex as $fIndex) {
      $found = 0;
      foreach ($columnList as $col) {
        if ($col->index == $fIndex) {
          $found = 1;
          $selectedColumns[] = (array)$col;
          break;
        }
      }
    }

    $allColumns = array_merge($mandatoryFields, $selectedColumns);

    //echo '<pre>';print_r($allColumns);die();

    //Get column string
    $colString = '';
    foreach ($allColumns as $col) {
      if ($colString != '') {
        $colString .= ',';
      }
      $colString .= $col['columnNameWithTable'] . ' as ' . $col['varName'];
    }


    $optionObjects = new stdClass();
    $optionObjects->genderOptions = $this->Staff_massupdate_model->getStaffDistinctColumn('gender','staff_master');
    $optionObjects->departmentOptions = $this->Staff_massupdate_model->getStaffDepartmentctColumn();
    $optionObjects->qualificationOption = $this->Staff_massupdate_model->getStaffDistinctColumn('qualification','staff_master');
    $optionObjects->dependentOptions = $this->Staff_massupdate_model->getDependentOptions();
    $optionObjects->isPrimaryOptions = $this->Staff_massupdate_model->getPrimaryInstanceOptions();
    $optionObjects->designationOptions = $this->Staff_massupdate_model->getStaffDesignationctColumn();
    $optionObjects->previousDesignationOptions = $this->Staff_massupdate_model->getStaffPreviousDesignationctColumn();
    $optionObjects->category = $this->Staff_massupdate_model->getCategoryOptions();
    $optionObjects->blood_group = $this->config->item('blood_groups');
    $optionObjects->religion = $this->config->item('religions');
    $optionObjects->nationality = $this->config->item('nationality');
    $optionObjects->staffType = $this->Staff_massupdate_model->get_staff_type();
    $optionObjects->staffHouse = $this->Staff_massupdate_model->getHouseList();
  
    $optionObjects->has_completed_any_bed = array('Yes','No');
    $optionObjects->nature_of_appointment = array('Full-time','Part-time','Contract basis');
    // $optionObjects->marital_statusOptions = array('Single','Married');
    $optionObjects->marital_statusOptions = array(
      (object)['name'=>'Married','value'=>'1'],
      (object)['name'=>'Single','value'=>'0']
    );
    //select shifts for staff attendance
    $optionObjects->shiftOptions = $this->Staff_massupdate_model->getShiftIds();
    // echo '<pre>';print_r($optionObjects);die();

    $data['field_objects'] = $allColumns;
    $data['optionObjects'] = $optionObjects;
    // echo '<pre>';print_r($optionObjects);die();
    $data['columnList_json'] = json_encode($allColumns);
    // $data['r_field_names'] = $r_field_names;
    $data['exportData'] = $this->Staff_massupdate_model->getStaffData($colString);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'staff/staff_massupdate/assign_values_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'staff/staff_massupdate/assign_values_mobile';
    }else{
      $data['main_content']    = 'staff/staff_massupdate/assign_values';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function submitMassUpdate () {
    //echo '<pre>';print_r($this->input->post());die();
    $this->Staff_massupdate_model->store_staff_edit_history();
    $status = $this->Staff_massupdate_model->submitMassUpdate();
    switch ($status) {
      case 0:
        $this->session->set_flashdata('flashError', 'No data was updated OR Something went wrong');
        break;
      case -10:
        $this->session->set_flashdata('flashError', 'Unique check failed');
        break;
      default:
        $this->session->set_flashdata('flashSuccess', 'Data Updated Successfully.');
        break;
    }
    redirect('staff/staff_massupdate');
  }

  public function update_staff_csv_data(){
    $file_path = $_FILES['upload_csv']['tmp_name'];
    // echo '<pre>';print_r($_FILES);
    $csv_data_arr = [];
    $this->load->library('csvimport');

    if ($this->csvimport->get_array($file_path)) {
      $csv_data_arr = $this->csvimport->get_array($file_path);
    }
    $csv_data=[];
    foreach($csv_data_arr as $key => $value) {
      if(isset($value['Date of Birth'])){
        $value['Date of Birth'] = date('Y-m-d',strtotime($value['Date of Birth']));
      }
      $csv_data[$value["Staff Id"]]['staff_id'] = $value["Staff Id"];
      $csv_data[$value["Staff Id"]]['data'][] = $value;
    }
    // echo "<pre>"; print_r($csv_data); die();
    echo json_encode($csv_data);
  }
}