<?php
	/**
	 * summary
	 */
	class Staff_profile_view_controller extends CI_Controller{
	    /**
	     * summary
	     */
	   	function __construct() {
				parent::__construct();
				if (!$this->ion_auth->logged_in()) {
						redirect('auth/login', 'refresh');
				}    
	      $this->load->model('staff/Staff_Model');
	      $this->load->model('student/Student_Model');
	      $this->load->model('staff/Payroll_Model');
	      $this->config->load('form_elements');
	      $this->load->library('filemanager');
	      $this->load->model('timetable/staff_tt');
	      $this->load->model('timetable/timetable');
	      $this->load->model('staff/staff_duties_model');
	      $this->load->model('staff/staffleave_model');
	      $this->load->model('staff/Staff_profile_view_model');
	      // $this->load->model('competition_model');
    }

    public $columnList = [
		[
			'dispaly_name'=>'Salutation',
			'data_input'=>'text',
			'column_name'=>'salutation',
			'tabs'=>'personal_info',
			'default'=>1
	  	],
	    [
	      	'dispaly_name'=>'short name',
	      	'data_input'=>'text',
	      	'column_name'=>'short_name',
	      	'tabs'=>'personal_info',
	      	'default'=>1
	    ],
	    [
	      	'dispaly_name'=>'designation',
	      	'data_input'=>'text',
	      	'column_name'=>'designation',
       		'tabs'=>'school_info',
       		'default'=>1
	    ],
	    [
	      	'dispaly_name'=>'previous designation name',
	      	'data_input'=>'text',
	      	'column_name'=>'previous_designation_name',
       		'tabs'=>'school_info',
       		'default'=>1
	    ],
     	[
	      	'dispaly_name'=>'department',
	      	'data_input'=>'text',
	      	'column_name'=>'department',
	      	'tabs'=>'school_info',
	      	'default'=>1
	    ],
	    [
	      'dispaly_name'=>'marital status',
	      'data_input'=>'dropdown',
	      'column_name'=>'marital_status',
	      'tabs'=>'personal_info',
	      'default'=>1
	    ],

		[
			'dispaly_name' => 'personal email-id',
			'data_input' => 'text',
			'column_name' => 'personal_mail_id',
			'tabs' => 'personal_info',
			'default' => 0
		],
	    [
	      'dispaly_name'=>'staff photo',
	      'data_input'=>'image',
	      'column_name'=>'picture_url',
	      'tabs'=>'personal_info',
	      'default'=>1
	    ],
	    [
	      'dispaly_name'=>'date of birth',
	      'data_input'=>'date',
	      'column_name'=>'dob',
	      'tabs'=>'personal_info',
	      'default'=>1
	    ],
	    [
	      'dispaly_name'=>'nationality',
	      'data_input'=>'dropdown',
	      'column_name'=>'nationality',
	      'tabs'=>'personal_info',
	      'default'=>1
	    ],
	    [
	      'dispaly_name'=>'gender',
	      'data_input'=>'dropdown',
	      'column_name'=>'gender',
	      'tabs'=>'personal_info',
	      'default'=>1
	    ],
	    [
	      'dispaly_name'=>'aadhar number',
	      'data_input'=>'text',
	      'column_name'=>'aadhar_number',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'category',
	      'data_input'=>'dropdown',
	      'column_name'=>'category',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],

	    [
	      'dispaly_name'=>'caste',
	      'data_input'=>'text',
	      'column_name'=>'caste',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    // [
	    //   'dispaly_name'=>'ESI Number',
	    //   'data_input'=>'text',
	    //   'column_name'=>'esi_number',
	    //   'tabs'=>'personal_info',
	    //   'default'=>0
	    // ],
	    // [
	    //   'dispaly_name'=>'UAN Number',
	    //   'data_input'=>'text',
	    //   'column_name'=>'uan_number',
	    //   'tabs'=>'personal_info',
	    //   'default'=>0
	    // ],
	    // [
	    //   'dispaly_name'=>'PF Number',
	    //   'data_input'=>'text',
	    //   'column_name'=>'pf_number',
	    //   'tabs'=>'personal_info',
	    //   'default'=>0
	    // ],
	    // [
	    //   'dispaly_name'=>'PAN Details',
	    //   'data_input'=>'text',
	    //   'column_name'=>'pan_number',
	    //   'tabs'=>'personal_info',
	    //   'default'=>0
	    // ],
	    // [
	    //   'dispaly_name'=>'Account Number',
	    //   'data_input'=>'text',
	    //   'column_name'=>'account_number',
	    //   'tabs'=>'personal_info',
	    //   'default'=>0
	    // ],
	    // [
	    //   'dispaly_name'=>'Bank Name',
	    //   'data_input'=>'text',
	    //   'column_name'=>'bank_name',
	    //   'tabs'=>'personal_info',
	    //   'default'=>0
	    // ],
	    // [
	    //   'dispaly_name'=>'Bank Branch Name',
	    //   'data_input'=>'text',
	    //   'column_name'=>'branch_name',
	    //   'tabs'=>'personal_info',
	    //   'default'=>0
	    // ],
	    // [
	    //   'dispaly_name'=>'IFS Code',
	    //   'data_input'=>'text',
	    //   'column_name'=>'ifsc_code',
	    //   'tabs'=>'personal_info',
	    //   'default'=>0
	    // ],
	    
	    [
	      'dispaly_name'=>'qualification',
	      'data_input'=>'text',
	      'column_name'=>'qualification',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'subject specialization',
	      'data_input'=>'text',
	      'column_name'=>'subject_specialization',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'total experience',
	      'data_input'=>'text',
	      'column_name'=>'total_experience',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'total education experience',
	      'data_input'=>'text',
	      'column_name'=>'total_education_experience',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
     	
	    [
	      'dispaly_name'=>'employee code',
	      'data_input'=>'text',
	      'column_name'=>'employee_code',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'math high grade',
	      'data_input'=>'text',
	      'column_name'=>'math_high_grade',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'english high grade',
	      'data_input'=>'text',
	      'column_name'=>'english_high_grade',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'social high grade',
	      'data_input'=>'text',
	      'column_name'=>'social_high_grade',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'trained to teach',
	      'data_input'=>'text',
	      'column_name'=>'trained_to_teach',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
     	[
	      'dispaly_name'=>'nature of appointment',
	      'data_input'=>'text',
	      'column_name'=>'nature_of_appointment',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'appointed subject',
	      'data_input'=>'text',
	      'column_name'=>'appointed_subject',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'classes taught',
	      'data_input'=>'text',
	      'column_name'=>'classes_taught',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
     	[
	      'dispaly_name'=>'main sub taught',
	      'data_input'=>'text',
	      'column_name'=>'main_sub_taught',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'add sub taught',
	      'data_input'=>'text',
	      'column_name'=>'add_sub_taught',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'has completed B.Ed',
	      'data_input'=>'text',
	      'column_name'=>'has_completed_any_bed',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'contact number',
	      'data_input'=>'text',
	      'column_name'=>'contact_number',
	      'tabs'=>'personal_info',
	      'default'=>1
	    ],
	    [
	      'dispaly_name'=>'alternative number',
	      'data_input'=>'text',
	      'column_name'=>'alternative_number',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'joining date',
	      'data_input'=>'date',
	      'column_name'=>'joining_date',
	      'tabs'=>'school_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'emergency info',
	      'data_input'=>'text',
	      'column_name'=>'emergency_info',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
     	[
	      'dispaly_name'=>'blood group',
	      'data_input'=>'dropdown',
	      'column_name'=>'blood_group',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'voter id',
	      'data_input'=>'text',
	      'column_name'=>'voter_id',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'height',
	      'data_input'=>'text',
	      'column_name'=>'height',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
     	[
	      'dispaly_name'=>'weight',
	      'data_input'=>'text',
	      'column_name'=>'weight',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'allergies',
	      'data_input'=>'text',
	      'column_name'=>'allergies',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'medical issues',
	      'data_input'=>'text',
	      'column_name'=>'medical_issues',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'identification mark',
	      'data_input'=>'text',
	      'column_name'=>'identification_mark',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'person with disability',
	      'data_input'=>'dropdown',
	      'column_name'=>'person_with_disability',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    
	    [
	      'dispaly_name'=>'landline number',
	      'data_input'=>'text',
	      'column_name'=>'landline_number',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'passport number',
	      'data_input'=>'text',
	      'column_name'=>'passport_number',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'passport place of issue',
	      'data_input'=>'text',
	      'column_name'=>'passport_place_of_issue',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'passport date of issue',
	      'data_input'=>'date',
	      'column_name'=>'passport_date_of_issue',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'passport expiry date',
	      'data_input'=>'date',
	      'column_name'=>'passport_expiry_date',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'visa details',
	      'data_input'=>'text',
	      'column_name'=>'visa_details',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'religion',
	      'data_input'=>'dropdown',
	      'column_name'=>'religion',
	      'tabs'=>'personal_info',
	      'default'=>0
	    ],
		[
	      'dispaly_name'=>'Father First Name',
	      'data_input'=>'text',
	      'column_name'=>'father_first_name',
	      'tabs'=>'family_info',
	      'default'=>1
	    ],
		[
	      'dispaly_name'=>'Father Last Name',
	      'data_input'=>'text',
	      'column_name'=>'father_last_name',
	      'tabs'=>'family_info',
	      'default'=>1
	    ],
		[
			'dispaly_name'=>'Father Contact Number',
			'data_input'=>'text',
			'column_name'=>'father_contact_no',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Father Date Of Birth',
			'data_input'=>'date',
			'column_name'=>'father_dob',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Father Occupation',
			'data_input'=>'text',
			'column_name'=>'father_occupation',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Is Father Dependent',
			'data_input'=>'dropdown',
			'column_name'=>'father_is_dependent',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Include Father Insurance',
			'data_input'=>'dropdown',
			'column_name'=>'include_father_insurance',
			'tabs'=>'family_info',
			'default'=>1
	    ],
		[
		  'dispaly_name'=>'Mother First Name',
		  'data_input'=>'text',
		  'column_name'=>'mother_first_name',
		  'tabs'=>'family_info',
		  'default'=>1
		],
		[
		  'dispaly_name'=>'Mother Last Name',
		  'data_input'=>'text',
		  'column_name'=>'mother_last_name',
		  'tabs'=>'family_info',
		  'default'=>1
		],
		[
			'dispaly_name'=>'Mother Date Of Birth',
			'data_input'=>'date',
			'column_name'=>'mother_dob',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Is Mother Dependent',
			'data_input'=>'dropdown',
			'column_name'=>'mother_is_dependent',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Include Mother Insurance',
			'data_input'=>'dropdown',
			'column_name'=>'include_mother_insurance',
			'tabs'=>'family_info',
			'default'=>1
	    ],
		[
			'dispaly_name'=>'Spouse First Name',
			'data_input'=>'text',
			'column_name'=>'spouse_name',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Spouse Last Name',
			'data_input'=>'text',
			'column_name'=>'spouse_last_name',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Spouse Contact No',
			'data_input'=>'text',
			'column_name'=>'spouse_contact_no',
			'tabs'=>'family_info',
			'default'=>1
	    ],
		[
			'dispaly_name'=>'Spouse Date Of Birth',
			'data_input'=>'date',
			'column_name'=>'spouse_dob',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Spouse Gender',
			'data_input'=>'dropdown',
			'column_name'=>'spouse_gender',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Spouse Occupation',
			'data_input'=>'text',
			'column_name'=>'spouse_occupation',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Is Spouse Dependent',
			'data_input'=>'dropdown',
			'column_name'=>'spouse_is_dependent',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Include Spouse Insurance',
			'data_input'=>'dropdown',
			'column_name'=>'include_spouse_insurance',
			'tabs'=>'family_info',
			'default'=>1
	    ],
		[
			'dispaly_name'=>'Child1 First Name',
			'data_input'=>'text',
			'column_name'=>'child1_first_name',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Child1 Last Name',
			'data_input'=>'text',
			'column_name'=>'child1_last_name',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Child1 Gender',
			'data_input'=>'dropdown',
			'column_name'=>'child1_gender',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Child1 Date Of Birth',
			'data_input'=>'date',
			'column_name'=>'child1_dob',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Is Child1 Dependent',
			'data_input'=>'dropdown',
			'column_name'=>'child1_is_dependent',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Include Child1 Insurance',
			'data_input'=>'dropdown',
			'column_name'=>'include_child1_insurance',
			'tabs'=>'family_info',
			'default'=>1
	    ],
		[
			'dispaly_name'=>'Child2 First Name',
			'data_input'=>'text',
			'column_name'=>'child2_first_name',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Child2 Last Name',
			'data_input'=>'text',
			'column_name'=>'child2_last_name',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Child2 Gender',
			'data_input'=>'dropdown',
			'column_name'=>'child2_gender',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Child2 Date Of Birth',
			'data_input'=>'date',
			'column_name'=>'child2_dob',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Is Child2 Dependent',
			'data_input'=>'dropdown',
			'column_name'=>'child2_is_dependent',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Include Child2 Insurance',
			'data_input'=>'dropdown',
			'column_name'=>'include_child2_insurance',
			'tabs'=>'family_info',
			'default'=>1
	    ],
		[
			'dispaly_name'=>'Child3 First Name',
			'data_input'=>'text',
			'column_name'=>'child3_first_name',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Child3 Last Name',
			'data_input'=>'text',
			'column_name'=>'child3_last_name',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Child3 Gender',
			'data_input'=>'dropdown',
			'column_name'=>'child3_gender',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Child3 Date Of Birth',
			'data_input'=>'date',
			'column_name'=>'child3_dob',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Is Child3 Dependent',
			'data_input'=>'dropdown',
			'column_name'=>'child3_is_dependent',
			'tabs'=>'family_info',
			'default'=>1
		],
		[
			'dispaly_name'=>'Include Child3 Insurance',
			'data_input'=>'dropdown',
			'column_name'=>'include_child3_insurance',
			'tabs'=>'family_info',
			'default'=>1
	    ],
	    [
	      'dispaly_name'=>'permanent address',
	      'data_input'=>'address',
	      'column_name'=>'permanent_address',
	      'tabs'=>'address',
	      'default'=>0
	    ],
     	[
	      'dispaly_name'=>'present address',
	      'data_input'=>'address',
	      'column_name'=>'present_address',
	      'tabs'=>'address',
	      'default'=>0
	    ],
	    [
	      'dispaly_name'=>'Documents',
	      'data_input'=>'',
	      'column_name'=>'document',
	      'tabs'=>'document',
	      'default'=>0
	    ],
	    [
	      	'dispaly_name'=>'Awards',
	      	'data_input'=>'',
	      	'column_name'=>'awards',
	      	'tabs'=>'awards',
	      	'default'=>0
	    ],
	    [
	      	'dispaly_name'=>'Qualification',
	      	'data_input'=>'',
	      	'column_name'=>'qualification',
	      	'tabs'=>'qualification',
	      	'default'=>0
	    ],
	    [
	      	'dispaly_name'=>'Experience',
	      	'data_input'=>'',
	      	'column_name'=>'experience',
	      	'tabs'=>'experience',
	      	'default'=>0
	    ],
     	[
     	 	'dispaly_name'=>'Workshop/ Training',
	      	'data_input'=>'',
	      	'column_name'=>'workshop_training',
	      	'tabs'=>'workshop',
	      	'default'=>0
	    ],
	    [
     	 	'dispaly_name'=>'Initiative',
	      	'data_input'=>'',
	      	'column_name'=>'initiative',
	      	'tabs'=>'initiative',
	      	'default'=>0
	    ],
	    [
     	 	'dispaly_name'=>'Publications / Citations',
	      	'data_input'=>'',
	      	'column_name'=>'publications_citations',
	      	'tabs'=>'publications_citations',
	      	'default'=>0
	    ],
     	[
     	 	'dispaly_name'=>'Interest',
	      	'data_input'=>'',
	      	'column_name'=>'interest',
	      	'tabs'=>'interest',
	      	'default'=>0
		],
		[
			'dispaly_name'=>'ESI Number',
			'data_input'=>'text',
			'column_name'=>'esi_number',
			'tabs'=>'payroll_details',
			'default'=>0
		],
		[
			'dispaly_name'=>'PAN Number',
			'data_input'=>'text',
			'column_name'=>'pan_number',
			'tabs'=>'payroll_details',
			'default'=>0
		],
		[
			'dispaly_name'=>'Bank Name',
			'data_input'=>'text',
			'column_name'=>'bank_name',
			'tabs'=>'payroll_details',
			'default'=>0
		],
		[
			'dispaly_name'=>'Bank Account Number',
			'data_input'=>'text',
			'column_name'=>'account_number',
			'tabs'=>'payroll_details',
			'default'=>0
		],
		[
			'dispaly_name'=>'IFSC Code',
			'data_input'=>'text',
			'column_name'=>'ifsc_code',
			'tabs'=>'payroll_details',
			'default'=>0
		],
		[
			'dispaly_name'=>'Branch Name',
			'data_input'=>'text',
			'column_name'=>'branch_name',
			'tabs'=>'payroll_details',
			'default'=>0
		],
		[
			'dispaly_name'=>'UAN Number',
			'data_input'=>'text',
			'column_name'=>'uan_number',
			'tabs'=>'payroll_details',
			'default'=>0
		],
		[
			'dispaly_name'=>'PF Number',
			'data_input'=>'text',
			'column_name'=>'pf_number',
			'tabs'=>'payroll_details',
			'default'=>0
		],
		[
			'dispaly_name'=>'Sum Insured Amount',
			'data_input'=>'text',
			'column_name'=>'sum_insured_amount',
			'tabs'=>'payroll_details',
			'default'=>0
		],
		[
			'dispaly_name'=>'Previous Employer Joining Date',
			'data_input'=>'date',
			'column_name'=>'previous_emplyoer_joining_date',
			'tabs'=>'payroll_details',
			'default'=>0
		],
		[
			'dispaly_name'=>'Previous Employer Exit Date',
			'data_input'=>'date',
			'column_name'=>'previous_employer_exit_date',
			'tabs'=>'payroll_details',
			'default'=>0
		],
		[
			'dispaly_name'=>'Staff House',
			'data_input'=>'text',
			'column_name'=>'staff_house',
			'tabs'=>'school_info',
			'default'=>0
		],
		[
			'dispaly_name'=>'Transport Request',
			'data_input'=>'text',
			'column_name'=>'Transport Request',
			'tabs'=>'transport_request',
			'default'=>0
		],
		[
			'dispaly_name'=>'Resignation Date',
			'data_input'=>'text',
			'column_name'=>'resignation_date',
			'tabs'=>'school_info',
			'default'=>0
		],
	];



 	public function staff_profile($staffId){
			 if (!$this->authorization->isAuthorized('STAFF_PROFILE.VIEW')) {
				 redirect('dashboard');
			 }

		$data['list_competition'] = $this->Staff_profile_view_model->getlistof_competitionbystaffWise($staffId);
		  //echo "<pre>";print_r($data['list_competition']);die();
	    $observed = $this->Staff_Model->getStaffObservation($staffId);
	    $staff = $this->staffcache->getStaffCache();
	    
	    $data['staffData'] = $this->Staff_Model->getStaff($staffId);
	    // echo "<pre>";print_r($data['staffData']);die();
	    $data['staffId'] = $staffId;
	    $obData = array();
	    foreach ($observed as $value){
	      $obData[$value->takenBy][] = array('date'=>date('d-m-Y', strtotime($value->created_on)), 'obs' => $value->observation);
	    }
	    $data['obData'] = $obData;
		$data['staff_duties'] = $this->staff_duties_model->get_staffDutiesDetails($staffId);
      $data['leave_details'] = $this->staffleave_model->get_leave_details_byId($staffId);
       // echo '<pre>';print_r($data['leave_details']);die();
      $data['staffDetail'] = $this->Staff_Model->getStaffDetailById($staffId);
      $data['departmentList'] = $this->Staff_Model->getStaffDistinctColumn('department');
      $data['designationList'] = $this->Staff_Model->getStaffDistinctColumn('designation');
      $data['qualificationList'] = $this->Staff_Model->getStaffDistinctColumn('qualification');
      $data['staffStatusArr'] = $this->settings->getSetting('staff_status');
      $data['staffTimetable'] = $this->Staff_Model->getStaffTimetableById($staffId);
      $data['staffAll'] = $this->staff_tt->get_allStaff();
      $data['paddress']=$this->Staff_profile_view_model->get_Present_address($staffId);
      $data['peraddress']=$this->Staff_profile_view_model->get_Peraddress_address($staffId);
      $data['staff_qualification']=$this->Staff_profile_view_model->get_staff_qualification($staffId);
      $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
       // echo '<pre>';print_r($data['staff_qualification']);die();
      $data['staff_uid'] = $this->uri->segment(4);
      $data['main_content'] = 'staff/staff_view_profile';
      $this->load->view('inc/template', $data);
	}

  private function __prepareStaffCustomFields(){
      $custom_fields =$this->settings->getSetting('staff_custom_fields');

      $customFIelds = array();
      if($custom_fields){
        foreach ($custom_fields as $displayName => $columnName) {
    	 $obj = array();
          $obj['dispaly_name'] = $displayName;
          $obj['data_input'] = 'text';
          $obj['column_name'] = $columnName;
          $obj['tabs'] = 'personal_info';
          $obj['default'] = 0;
          $customFIelds[] = $obj;
        }  
      }
      
      return $customFIelds;
   }

 	public function staff_profile_view(){

 		$data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
 		$data['photo']= $this->Staff_profile_view_model->get_staff_photo($data['staff_id']);
 		$tabs = [];
 		$default_fields =[];

 		$this->columnList = array_merge($this->columnList,$this->__prepareStaffCustomFields());

 		foreach ($this->columnList as $key => $tab) {
 			$tabs[$tab['tabs']][] = $tab;
 			if ($tab['default'] == 1) {
				$default_fields[$tab['tabs']][] = $tab['column_name'];
 			}
 		}

 		$staff_display_fields = $this->Staff_profile_view_model->get_config_staff_display_fileds();


 		$display_fields = array();
 		if (!empty($staff_display_fields)) {
 			foreach ($staff_display_fields as $key => $value) {
 				$display_fields = json_decode($value->value);
 			}
 		}
 		$displayFields = (array)$display_fields;

 		$data['displayfields'] = array_merge_recursive($default_fields,$displayFields);
		// echo "<pre>";print_r($data['displayfields']);die();
 		$enabled_config_fields = $this->Staff_profile_view_model->get_config_staff_enabled_value(); 		
 		$edit_enabled_fields = array();
 		if (!empty($enabled_config_fields)) {
 			foreach ($enabled_config_fields as $key => $value) {
 				$edit_enabled_fields = json_decode($value->value);
 			}
 		}

		foreach ($data['displayfields'] as $key => $value) {
			$displayKeys[] = $key;
		}
		foreach ($tabs as $key => $value) {
			if (!in_array($key, $displayKeys)) {
				unset($tabs[$key]);
			}
		}
		// echo "<pre>";print_r($tabs);die();
		$data['profile_lock_unlock_status']=$this->Staff_profile_view_model->get_profile_lock_unlock_status($data['staff_id']); 
 		$editFields = (array)$edit_enabled_fields;
 		$data['profileTabs'] = $tabs;
 		$data['showEditfields'] = $editFields;
 		$data['family_instruction'] = $this->settings->getSetting('staff_profile_family_details_instruction');
 		// $data['marital_status'] = $this->Staff_profile_view_model->get_staff_marital_status();
		// echo "<pre>";print_r($data['showEditfields']['family_info']);die();
		// echo "<pre>Edit"; print_r($data['showEditfields']['personal_info']); die();
		$school_name = $this->settings->getSetting('school_short_name');
		$data['stopList'] = $this->Staff_profile_view_model->get_fee_Stop_list();
		$areaRoutes = [];
		foreach ($data['stopList'] as $key => $area) {
			if(!in_array($area->route, $areaRoutes, true)){
			array_push($areaRoutes, $area->route);
			}
		}
		$data['route_area'] = $areaRoutes;
	 	if ($this->mobile_detect->isMobile()) {
			$data['main_content']    = 'staff/staff_profile_mobile_view/staff_profile_view_mobile';
		}else{
 			$data['main_content']    = 'staff/staff_profile_view';
		}
  		$this->load->view('inc/template', $data);
  	}

    public function save_staff_profile_photo(){
    	$input = $_POST;
		$file = $_FILES['file'];
		$sRealphoto = $this->Staff_profile_view_model->update_staff_profile_photo($input['staff_id'], $input['high_quality'], 'HIGH_QUALITY',$input['old_path']);
		if ($sRealphoto) {
			$min_size = $this->_resize_image($file['tmp_name'], 200, $file['type']);
			$picture = array('tmp_name' => $min_size, 'name' => $min_size);
			$sResigedPhoto = $this->s3FileUpload($picture);
			$this->Staff_profile_view_model->update_staff_profile_photo($input['staff_id'], $sResigedPhoto, 'LOW_QUALITY',$input['old_path']);
		}
		echo 1;
    }
    public function profile_mobile_view($pages){
    	$data['pages'] = str_replace('_', ' ', strtoupper($pages));
    	$data['main_content']    = 'staff/staff_profile_page_details';
      	$this->load->view('inc/template', $data);	
    }

    public function get_staff_data_tab_wise(){
    	$staff_tab= $_POST['staff_tab'];
    	$result = $this->Staff_profile_view_model->get_staff_details_tab_wise($staff_tab);
		// echo "<pre>";print_r($result);die();
		if ($result && $staff_tab == 'school_info') {
			$designation = '';
			$department = '';
			$previous_designation_name = '';
			if (isset($result->designation)) {
				if ($result->designation != "Not Assigned" && $result->designation != null && $result->designation != '') {
					$designation = $this->Staff_profile_view_model->get_staff_designation_name($result->designation);
				} else {
					$designation = $result->designation;
				}
			}
			
			if (isset($result->previous_designation_name)) {
				if ($result->previous_designation_name != "Not Assigned" && $result->previous_designation_name != null && $result->previous_designation_name != '') {
					$previous_designation_name = $this->Staff_profile_view_model->get_staff_designation_name($result->previous_designation_name);
				} else {
					$previous_designation_name = $result->previous_designation_name;
				}
			}
			
			if (isset($result->department)) {
				if ($result->department != 'Not Assigned' && $result->department != null && $result->department != '') {
					$department = $this->Staff_profile_view_model->get_staff_department_name($result->department);
				} else {
					$department = $result->department;
				}
			}
			
			$result->designation = $designation;
			$result->department = $department;
			$result->previous_designation_name = $previous_designation_name;
		}
    	echo json_encode($result);
    }

    public function upload_staff_documents_by_user($staff_id){
	    $staff_documents_folder = 'staff_documents';
	    echo $this->Staff_Model->addManagedDocuments($_POST, $staff_id, $this->s3FileUpload($_FILES['document_obj'], $staff_documents_folder));
  	}

  	public function upload_staff_awards_by_user($staff_id){
  		echo $this->Staff_Model->add_awards($staff_id,$_POST);
  	}

		public function upload_staff_experienceby_user($staff_id){
	    $staff_documents_folder = "staff_experience_doc";
	    echo $this->Staff_Model->add_experience($staff_id, $_POST, $this->s3FileUpload($_FILES['staff_experience_doc'], $staff_documents_folder));
	  }

	  public function upload_staff_training_workshop_by_user($staff_id){
 			echo $this->Staff_Model->stafftraning_workshop_insert_data($staff_id, $this->s3FileUpload($_FILES['certificate_name'], 'staff_work_shop_certificate'));
	  }

	  public function upload_staff_interest_by_user($staff_id){
 			 echo  $this->Staff_Model->staffinterest_insert_data($staff_id);
	  }

	  public function upload_staff_initiative_by_user($staff_id){
	  	echo $this->Staff_Model->insert_staff_initiative_details($staff_id, $_POST, $this->s3FileUpload($_FILES['staff_initiative_document'], 'staff_initiative_documents'));
	  }


	  public function upload_staff_publication_by_user($staff_id){
	  	echo $this->Staff_Model->staffpublications_citations_insert_data($staff_id);
	  }

  	public function upload_staff_qualification_by_user($staff_id)
  	{
  		echo $this->Staff_Model->add_qualifications($staff_id,$_POST,$this->s3FileUpload($_FILES['staff_qualification_doc'], 'qualification_documents'));
  	}

 	private function s3FileUpload($file, $folder_name = 'profile'){
	    if ($file['tmp_name'] == '' || $file['name'] == '') {
	      return ['status' => 'empty', 'file_name' => ''];
	    }
	    return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
  	}

  	public function save_staff_profile_by_user(){
  		echo $this->Staff_profile_view_model->save_staff_profile_by_user_by_id();
  	}

  	public function get_staff_address_by_user(){
  		$input_name = $_POST['input_name'];
  		$result = $this->Staff_profile_view_model->get_staff_address_by_user_id($input_name);
  		echo json_encode($result);
  	}

  	public function staff_profile_edit_enabled_fiedls(){
  		$this->columnList = array_merge($this->columnList,$this->__prepareStaffCustomFields());

  		$profile_conifg = [];
 		foreach ($this->columnList as $tabs => $value) {
 			$profile_conifg[$value['tabs']][] = $value['column_name'];
 		}

 		$profile_displayName = [];
 		foreach ($this->columnList as $tabs => $value) {
 			$profile_displayName[$value['tabs']][$value['column_name']] = $value['dispaly_name'];
 		}

 		$data['staff_fields_display_label'] = $profile_displayName;
 		$data['staff_fields'] = $profile_conifg;
  		$selectedOptions = $this->Staff_profile_view_model->get_config_staff_enabled_value();
  		$dbEnabed = [];
  		foreach ($selectedOptions as $key => $enabled) {
  			$dbEnabed = json_decode($enabled->value);
  		}
  		$data['selected_enabled_fields'] = (array) $dbEnabed;

  		$data['main_content']    = 'staff/staff_profile_profile_enabled_setting';
      	$this->load->view('inc/template', $data);	
  	}

  	public function staff_config_append(){
	 	$result = $this->Staff_profile_view_model->insert_staff_configure_fields();
	    if($result){
	      $this->session->set_flashdata('flashSuccess', 'Selected Modules Enabled Successfully.');
	      redirect('staff/Staff_profile_view_controller/staff_profile_edit_enabled_fiedls');
	    }else{
	      $this->session->set_flashdata('flashError', 'Something went Wrong.');
	      redirect('staff/Staff_profile_view_controller/staff_profile_edit_enabled_fiedls');
	    }
  	}

  	public function staff_profile_display_fiedls(){

  		$this->columnList = array_merge($this->columnList,$this->__prepareStaffCustomFields());
  		$profile_conifg = [];
 		foreach ($this->columnList as $tabs => $value) {
 			$profile_conifg[$value['tabs']][$value['column_name']] = $value['default'];
 		}

 		$profile_displayName = [];
 		foreach ($this->columnList as $tabs => $value) {
 			$profile_displayName[$value['tabs']][$value['column_name']] = $value['dispaly_name'];
 		}

 		$data['staff_fields'] = $profile_conifg;
 		$data['staff_fields_display_label'] = $profile_displayName;
 		// $data['staff_fields_default'] = $profile_default;
  		$selectedOptions = $this->Staff_profile_view_model->get_config_staff_display_fileds();
  		$dbEnabed = [];
  		foreach ($selectedOptions as $key => $enabled) {
  			$dbEnabed = json_decode($enabled->value);
  		}
  		$data['selected_enabled_fields'] = (array) $dbEnabed;

  		$data['main_content']    = 'staff/staff_profile_profile_display_setting';
      	$this->load->view('inc/template', $data);	
  	}

  	public function staff_profile_display_config_append(){
	 	$result = $this->Staff_profile_view_model->insert_staff_configure_fields();
	    if($result){
	      $this->session->set_flashdata('flashSuccess', 'Selected Modules Enabled Successfully.');
	      redirect('staff/Staff_profile_view_controller/staff_profile_display_fiedls');
	    }else{
	      $this->session->set_flashdata('flashError', 'Something went Wrong.');
	      redirect('staff/Staff_profile_view_controller/staff_profile_display_fiedls');
	    }
  	}

  	private function _resize_image($file, $max_resolution, $type){
		if (file_exists($file)) {
			if ($type == 'image/jpeg')
				$original_image = imagecreatefromjpeg($file);
			else
				$original_image = imagecreatefrompng($file);

			//check orientation 
			// $exif = exif_read_data($file);

			try {
				$exif = exif_read_data($file);
			} catch (Exception $exp) {
				$exif = false;
			}

			if ($exif) {
				if (!empty($exif['Orientation'])) {
					switch ($exif['Orientation']) {
						case 3:
							$original_image = imagerotate($original_image, 180, 0);
							break;

						case 6:
							$original_image = imagerotate($original_image, -90, 0);
							break;

						case 8:
							$original_image = imagerotate($original_image, 90, 0);
							break;
					}
				}
			}

			//resolution
			$original_width = imagesx($original_image);
			$original_height = imagesy($original_image);

			//try width first
			$ratio = $max_resolution / $original_width;
			$new_width = $max_resolution;
			$new_height = $original_height * $ratio;

			//if that dosn't work
			if ($new_height > $max_resolution) {
				$ratio = $max_resolution / $original_height;
				$new_height = $max_resolution;
				$new_width = $original_width * $ratio;
			}

			if ($original_image) {
				$new_image = imagecreatetruecolor($new_width, $new_height);
				imagecopyresampled($new_image, $original_image, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
				if ($type == 'image/jpeg')
					imagejpeg($new_image, $file);
				else
					imagepng($new_image, $file);
			}

			return $file;
			// echo '<br>Resized: ';
			// echo filesize($file); 

			// echo '<pre>'; print_r($file); die();
		}
	}

	public function staff_documents_download_by_user($staff_document_id, $staff_id){
    $document_obj = $this->Staff_Model->get_document_row($staff_document_id);
    $document_url = $this->filemanager->getFilePath($document_obj->document_url);
    $document_data = file_get_contents($document_url);
    $pdf_name = $this->Staff_Model->get_staff_name_by_id($staff_id) . "_" . $document_obj->document_type;
    $this->load->helper('download');
    force_download($pdf_name . '.pdf', $document_data, TRUE);
  }

  public function get_particular_award_by_user($staff_id){
    $result = $this->Staff_Model->get_particular_award($_POST);
    echo json_encode($result);
  }

  public function get_particular_qualification_by_user(){
    $result = $this->Staff_Model->get_particular_qualification($_POST);
    echo json_encode($result);
  }

  public function staff_qualifications_documents_download_by_user($staff_document_id, $staff_id){
    // echo $document->supporting_document; die();
    $document = $this->Staff_Model->get_qualification_document_row($staff_document_id);
    $document_url = $this->filemanager->getFilePath($document->supporting_document);
    $document_data = file_get_contents($document_url);
    $pdf_name = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $this->load->helper('download');
    force_download($pdf_name . '.pdf', $document_data, TRUE);
  }

 	public function get_particular_experience_by_user($staff_id){
    // echo "<pre>"; echo print_r($_POST); die();
    $result = $this->Staff_Model->get_particular_experience($staff_id, $_POST);
    echo json_encode($result);
  }

  public function staff_experiences_documents_download_by_user($staff_document_id, $staff_id){
    $document = $this->Staff_Model->get_experience_document_row($staff_document_id);
    $document_url = $this->filemanager->getFilePath($document->supporting_document);
    //echo "<pre>"; print_r($document_url);die();
    $document_data = file_get_contents($document_url);
    $pdf_name = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $this->load->helper('download');
    force_download($pdf_name . '.pdf', $document_data, TRUE);
  }

 	public function stafftraning_view_data_by_user(){
    $trainId = $_POST['trainId'];
    $result = $this->Staff_Model->stafftraning_view_dataId($trainId);
    echo json_encode($result);
  }

  public function stafftraning_workshop_documents_download_by_user($staffId, $workshop_staff_certificate_id){
    $staff_work_shop_certificate = $this->Staff_Model->get_workshop_certificate_row($workshop_staff_certificate_id);
    $document_url = $this->filemanager->getFilePath($staff_work_shop_certificate->certificate_path);
    $document_data = file_get_contents($document_url);
    $this->load->helper('download');
    force_download('certificates.pdf', $document_data, TRUE);
  }

 	public function staffpublication_view_data_by_user(){
    $trainId = $_POST['trainId'];
    $result = $this->Staff_Model->staffpublication_view_dataId($trainId);
    echo json_encode($result);
  }

  public function get_particular_staff_initiative_by_user($staff_id){
    //echo "<pre>"; echo print_r($_POST); die();
    $result = $this->Staff_Model->get_particular_staff_initiative($staff_id, $_POST);
    echo json_encode($result);
  }

  public function staff_initiative_documents_download_by_user($doc_id, $staff_id){
    $document = $this->Staff_Model->get_initiative_document_row($doc_id);
    $document_url = $this->filemanager->getFilePath($document->document);
    $document_data = file_get_contents($document_url);
    $pdf_name = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $this->load->helper('download');
    force_download($pdf_name . '.pdf', $document_data, TRUE);
  }

 	public function staffinterest_view_data_by_user(){
    $trainId = $_POST['trainId'];
    $result = $this->Staff_Model->staffinterest_view_dataId($trainId);
    echo json_encode($result);
  }
	public function view_staff_tab_wise_mobile($tabName){
		$data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
 		$tabs = [];
 		$default_fields =[];
 		$this->columnList = array_merge($this->columnList,$this->__prepareStaffCustomFields());
 		foreach ($this->columnList as $key => $tab) {
 			$tabs[$tab['tabs']][] = $tab;
 			if ($tab['default'] == 1) {
				$default_fields[$tab['tabs']][] = $tab['column_name'];
 			}
 		}
 		$staff_display_fields = $this->Staff_profile_view_model->get_config_staff_display_fileds();
 		$display_fields = array();
 		if (!empty($staff_display_fields)) {
 			foreach ($staff_display_fields as $key => $value) {
 				$display_fields = json_decode($value->value);
 			}
 		}
 		$displayFields = (array)$display_fields;

 		$data['displayfields'] = array_merge_recursive($default_fields,$displayFields);

 		$enabled_config_fields = $this->Staff_profile_view_model->get_config_staff_enabled_value(); 		
 		$edit_enabled_fields = array();
 		if (!empty($enabled_config_fields)) {
 			foreach ($enabled_config_fields as $key => $value) {
 				$edit_enabled_fields = json_decode($value->value);
 			}
 		}

 		$editFields = (array)$edit_enabled_fields;
 		
 		$data['profileTabs'] = $tabs;
 		$data['showEditfields'] = $editFields;
		$data['profile_lock_unlock_status']=$this->Staff_profile_view_model->get_profile_lock_unlock_status($data['staff_id']); 

		$data['tabName'] = str_replace('_', ' ', strtoupper($tabName));
		$data['getTabwise'] = $tabName;
		$data['family_instruction'] = $this->settings->getSetting('staff_profile_family_details_instruction');
		$data['stopList'] = $this->Staff_profile_view_model->get_fee_Stop_list();
		$areaRoutes = [];
		foreach ($data['stopList'] as $key => $area) {
			if(!in_array($area->route, $areaRoutes, true)){
			array_push($areaRoutes, $area->route);
			}
		}
		$data['route_area'] = $areaRoutes;
		$data['main_content']    = 'staff/staff_profile_mobile_view/staff_profile_view_mobile_tab';
		$this->load->view('inc/template', $data);
	}

	 public function view_add_forms($tabName){
		$data['profile_lock_unlock_status']=$this->Staff_profile_view_model->get_profile_lock_unlock_status($this->authorization->getAvatarStakeHolderId()); 
	 	if ($tabName == 'document') {
		 	$data['main_content']    = 'staff/staff_profile_mobile_view/documents';
	 	}else if ($tabName == 'awards') {
	 		$data['main_content']    = 'staff/staff_profile_mobile_view/awards';
	 	}else if ($tabName == 'qualification') {
	 		$data['main_content']    = 'staff/staff_profile_mobile_view/qualification';
	 	}else if ($tabName == 'experience') {
	 		$data['main_content']    = 'staff/staff_profile_mobile_view/experience';
	 	}else if ($tabName == 'workshop') {
	 		$data['main_content']    = 'staff/staff_profile_mobile_view/training';
	 	}else if ($tabName == 'initiative') {
	 		$data['main_content']    = 'staff/staff_profile_mobile_view/initiatives';
	 	}else if ($tabName == 'publications_citations') {
	 		$data['main_content']    = 'staff/staff_profile_mobile_view/publication';
	 	}else if ($tabName == 'interest') {
	 		$data['main_content']    = 'staff/staff_profile_mobile_view/interest';
	 	}
	 	$data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
	 	$data['tabName'] = $tabName;
 		$this->load->view('inc/template', $data);
	}
	// public function view_staff_awards_show_mobile(){
	// 	$data['main_content']    = 'staff/_show_awards_mobile';
	//  		$this->load->view('inc/template', $data);
	//  }
	// public function view_staff_tranning_details_mobile(){
	// 		$data['main_content']    = 'staff/staff_tranning_details_mobile';
	// 		$this->load->view('inc/template', $data);
	// }

	public function get_visible_staff_document_types(){
		$docs= $this->Staff_profile_view_model->get_visible_staff_document_types();
		echo json_encode($docs);
	}

	public function staff_profile_display_required_fields()
    {
        $columnNames = $this->db->list_fields('staff_master');
		// $data['custom_field'] = $this->settings->getSetting('staff_custom_fields');
        $cNames = [];
		
        foreach ($columnNames as $index) {
            if ($index != "custom1" && $index != "custom2" && $index != "custom3" && $index != "custom4" && $index != "custom5" &&
                $index != "custom6" && $index != "custom7" && $index != "custom8" && $index != "custom9" && $index != "custom10" &&
                $index != "custom11" && $index != "custom12" && $index != "custom13" && $index != "custom14" && $index != "custom15" &&
                $index != "custom16" && $index != "custom17" && $index != "custom18" && $index != "custom19" && $index != "custom20" &&
                $index != "id" && $index != "created_on" && $index != "modified_on" &&
                $index != "last_modified_by" && $index != "active" && $index != "isdummy" &&
                $index != "biometric_attendance_code" && $index != "identification_code" && $index != "shift_id" &&
                $index != "webcam_avatar" && $index != "joined_helium" && $index != "joined_helium_on" &&
                $index != "profile_confirmed" && $index != "profile_confirmed_date" && $index != "oc_platform" &&
                $index != "oc_link" && $index != "oc_last_modified_by" && $index != "oc_additional_info" &&
                $index != "is_reporting_manager" && $index != "reporting_manager_id" && $index != "oc_mail_id" &&
                $index != "oc_password" && $index != "mobile_number_2" && $index != "last_date_of_work" &&
                $index != "exit_remarks" && $index != "exit_update_on" && $index != "exit_updated_by" &&
                $index != "father_mother_tongue" && $index != "mother_mother_tongue" && $index != "resignation_letter_doc" && $index != "sports" && $index != "dramatics" &&
                $index != "literary_interests" && $index != "music" && $index != "dance" && $index != "status" && $index != "boarding" && $index != "last_working_day" && $index != "high_quality_picture_url" && $index !="landline_number") {
					if($index=="picture_url"){
						$index="photo";
					}
                array_push($cNames, $index);
            }
        }
        array_push($cNames, "staff_code");
        array_push($cNames, "esi_number");
        array_push($cNames, "pan_number");
        array_push($cNames, "account_number");
        array_push($cNames, "bank_name");
        array_push($cNames, "branch_name");
        array_push($cNames, "ifsc_code");
        array_push($cNames, "pf_number");
        array_push($cNames, "uan_number");
        array_push($cNames, "sum_insured_amount");
        array_push($cNames, "previous_emplyoer_joining_date");
        array_push($cNames, "previous_employer_exit_date");
        array_push($cNames, "staff_age");
        array_push($cNames, "father_age");
        array_push($cNames, "mother_age");
        array_push($cNames, "spouse_age");
        array_push($cNames, "child1_age");
        array_push($cNames, "child2_age");
        array_push($cNames, "child3_age");
        array_push($cNames, "last_date_of_work");
        array_push($cNames, "exit_remarks");
        array_push($cNames, "exit_update_on");
        array_push($cNames, "exit_updated_by");
        array_push($cNames, "resignation_letter_doc");
		// if(!empty($data['custom_field'])){
		// 	foreach ($data['custom_field'] as $customLabel => $customField) {
		// 		if (in_array($customField, $columnNames)) {
		// 			array_push($cNames, $customField);
		// 		}
		// 	}
		// }
        $data['staff_fields_display_label'] = $cNames;
		$default_enabled=['salutation', 'first_name', 'last_name','father_first_name', 'mother_first_name', 'father_contact_no', 'marital_status', 'gender', 'dob', 'contact_number', 'blood_group', 'staff_type', 'department', 'designation', 'employee_code', 'has_completed_any_bed','email', 'spouse_name', 'spouse_contact_no'];
        $required = $this->Staff_profile_view_model->get_congif_staff_required_fields();
        $requiredSchool = $this->Staff_profile_view_model->get_config_staff_required_for_school();
        $staffDisplay = $this->Staff_profile_view_model->get_congif_staff_profile_display_columns();
        $staffEdit = $this->Staff_profile_view_model->get_congif_staff_profile_edit_columns();

        $dbRequired = [];
        foreach ($required as $key => $enabled) {
            $dbRequired = json_decode($enabled->value);
        }
        $dbSchoolRequired = [];
        foreach ($requiredSchool as $key => $enabled) {
            $dbSchoolRequired = json_decode($enabled->value);
        }
        $dbStaffDisplay = [];
        foreach ($staffDisplay as $key => $enabled) {
            $dbStaffDisplay = json_decode($enabled->value);
        }
        $dbStaffEdit = [];
        foreach ($staffEdit as $key => $enabled) {
            $dbStaffEdit = json_decode($enabled->value);
        }

        $data['check_required_enabled'] = (array) $dbRequired;
        $data['check_school_required_enabled'] = (array) array_merge($dbSchoolRequired, $default_enabled);
        $data['check_staff_display_enabled'] = (array) $dbStaffDisplay;
        $data['check_staff_edit_enabled'] = (array) $dbStaffEdit;
		$data['default_enabled']=$default_enabled;
        // echo "<pre>";print_r($data['check_school_required_enabled']);die();

        $data['main_content'] = 'staff/staff_profile_display_required_fields';
        $this->load->view('inc/template', $data);
    }

    public function staff_required_config_append()
    {
        $result = $this->Staff_profile_view_model->insert_staff_required_configure_fields();
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Selected Modules Enabled Successfully.');
            redirect('staff/Staff_profile_view_controller/staff_profile_display_required_fields');
        } else {
            $this->session->set_flashdata('flashError', 'Something went Wrong.');
            redirect('staff/Staff_profile_view_controller/staff_profile_display_required_fields');
        }
    }

	public function savePayrollDetails(){
		$result = $this->Staff_profile_view_model->savePayrollDetails();
		if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Selected Modules Enabled Successfully.');
            redirect('staff/Staff_profile_view_controller/staff_profile_view');
        } else {
            $this->session->set_flashdata('flashError', 'Something went Wrong.');
            redirect('staff/Staff_profile_view_controller/staff_profile_view');
        }
		

	}

	public function save_family_member_details(){
		$input = $this->input->post();
		$this->Staff_profile_view_model->Store_edit_history($input['relation'].' Details : '.$input['family_old_value'],$input['relation'].' Details : '.$input['family_new_value']);
		echo $this->Staff_profile_view_model->save_family_member_details($input);
	}

	public function configure_staff_side_fields(){
		$staffProfileFields=$this->columnList;
		// Initialize arrays to store configuration and display names
		$profile_config = [];
		$profile_displayName = [];

		foreach ($staffProfileFields as $value) {
			// Extract relevant information
			$tabs = $value['tabs'];
			$column_name = $value['column_name'];
			$display_name = $value['dispaly_name'];
			$default = $value['default'];
  
			// Populate $profile_config with default values
			$profile_config[$tabs][$column_name] = $default;
  
			// Populate $profile_displayName with display names
			$profile_displayName[$tabs][$column_name] = $display_name;
		}
		$data['staff_fields'] = $profile_config;
		$data['staff_fields_display_label'] = $profile_displayName;
		$selectedOptions = $this->Staff_profile_view_model->get_config_staff_display_fileds();
		$dbEnabed = [];
		foreach ($selectedOptions as $key => $enabled) {
			$dbEnabed = json_decode($enabled->value);
		}
		$data['selected_enabled_fields'] = (array) $dbEnabed;

		$editOptions = $this->Staff_profile_view_model->get_config_staff_enabled_value();
		$dbEditable = [];
		foreach ($editOptions as $key => $enabled) {
			$dbEditable = json_decode($enabled->value);
		}
		$data['selected_editable_fields'] = (array) $dbEditable;

		$mandatoryOptions = $this->Staff_profile_view_model->get_config_staff_mandatory_value();
		$dbMandatory = [];
		foreach ($mandatoryOptions as $key => $enabled) {
			$dbMandatory = json_decode($enabled->value);
		}
		$data['selected_mandatory_fields'] = (array) $dbMandatory;

		$data['editMandatoryDisabledFields']= ['salutation','short_name','gender','designation','previous_designation_name','department','subject_specialization','employee_code','trained_to_teach','nature_of_appointment','appointed_subject','classes_taught','main_sub_taught','add_sub_taught','has_completed_any_bed','joining_date','staff_house'];

		$data['mandatoryOnlyDisabledFields']=['experience','awards','workshop_training','initiative','qualification'];

		$data['main_content'] = 'staff/configure_staff_side_fields_view';
		$this->load->view('inc/template', $data);
	}

	public function staff_profile_display_config(){
		$result = $this->Staff_profile_view_model->insert_staff_configure_fields();
		if($result){
			$this->session->set_flashdata('flashSuccess', 'Selected Modules Enabled Successfully.');
			redirect('staff/Staff_profile_view_controller/configure_staff_side_fields');
		  }else{
			$this->session->set_flashdata('flashError', 'Something went Wrong.');
			redirect('staff/Staff_profile_view_controller/configure_staff_side_fields');
		  }

	}

	public function update_profile_confirmedbyuser(){
		$result = $this->Staff_profile_view_model->update_profile_confirmedbyuser();
		echo json_encode($result);
	}

	public function configure_lock_unlock_staff_profile(){
		$data['main_content'] = 'staff/configure_lock_unlock_staff_profile_view';
		$this->load->view('inc/template', $data);
	}

	public function load_staff_data(){
		$result = $this->Staff_profile_view_model->load_staff_data();
		echo json_encode($result);
	}

	public function change_confirm_status_individual(){
		echo $this->Staff_profile_view_model->change_confirm_status($_POST);
	}

	public function submit_staff_transport(){
		$result = $this->Staff_profile_view_model->submit_staff_transport($_POST);
		$get_email_template_data = $this->Staff_profile_view_model->get_transport_email_data();
		if($result && !empty($get_email_template_data)){
			$send_email = $this->_transportation_request_email_to_staff($get_email_template_data);
		}
		echo $result;
	}

	private function _transportation_request_email_to_staff($email_data){
		$this->load->helper('email_helper');
		$emailIds = explode(',', $email_data['members_email']);
		$emailIds = array_filter($emailIds);
		
		if(empty($emailIds)){
			return 0;
		}
		$email_data['content'] = str_replace('%%staff_name%%',$email_data['to_email']->staff_name,$email_data['content']);
		$email_data['content'] = str_replace('%%employee_code%%',$email_data['to_email']->employee_code,$email_data['content']);

		$acad_year_id = $this->settings->getSetting('academic_year_id');

		
		$emails_data = [];
		foreach($emailIds as $key => $val){
			$email_master_data = array(
				'subject' => $email_data['email_subject'],
				'body' => $email_data['content'],
				'source' => 'Transportation Requested By Staff',
				'sent_by' => '',
				'recievers' => "Staff",
				'from_email' => $email_data['registered_email'],
				'files' => NULL,
				'acad_year_id' => $acad_year_id,
				'visible' => 1,
				'sender_list' => $val,
				'sending_status' => 'Completed'
			);
			$this->load->model('communication/emails_model');
			$email_master_id = $this->emails_model->saveEmail($email_master_data);

			$email_obj = new stdClass();
			$email_obj->stakeholder_id = '';
			$email_obj->avatar_type = 2;
			$email_obj->email = $val;
			$email_obj->email_master_id = $email_master_id;
			$email_obj->status = ($val)?'Awaited':'No Email';
			$emails_data[] = $email_obj;
		}

		$this->Staff_profile_view_model->save_sending_email_data($emails_data);
		return sendEmail($email_data['content'], $email_data['email_subject'], 0, $emailIds, $email_data['registered_email'], '');

	  }
}
?>