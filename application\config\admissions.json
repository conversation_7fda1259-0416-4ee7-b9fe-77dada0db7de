[{"id": 1, "form_name": "MONT-1 TO 2, <PERSON><PERSON><PERSON><PERSON><PERSON>, LKG, UKG, GRADE 1", "form_year": "2020-2021", "school_name": "NATIONAL PUBLIC SCHOOL, JAYANAGAR", "instructin_file": "mont", "school_short": "jayanagar", "school": "NPS - Jayanagar", "open_for_admissions": 1, "class_applied_for": "[\"MONTESSORI-1\",\"MONTESSORI-2\",\"NURSERY\",\"LKG\",\"UKG\",\"1\"]", "documents": "{\"Birth Certificate\":\"Birth Certificate\",\"<PERSON><PERSON><PERSON> card\":\"<PERSON><PERSON><PERSON> card\",\"Others\":\"Others\"}", "prev_eduction_info": "", "streams": "", "guidelines": "{\"name\":\"NATIONAL PUBLIC SCHOOL, JAYANAGAR\",\"line1\":\"#1\",\"0\":\"4th T Block East,\",\"1\":\"Jayanagar 3rd Block East\",\"line2\":\"Jayanagar\",\"2\":\"\",\"line3\":\"Bengaluru \\u2013 560011\",\"line4\":\"South India\",\"phone\":\"<strong>Phone: <\\/strong>080 4124 9369\",\"fax\":\"\",\"email\":\"<strong>Email: <\\/strong> <EMAIL>\",\"school_short\":\"NPS - JAYANAGAR\",\"school_full\":\"NATIONAL PUBLIC SCHOOL, JAYANAGAR')\"}", "application_no_gen": "{\"name\":\"NPSJNR\",\"prefix\":\"MNLU\",\"year\":\"2020\",\"digit_count\":5}"}, {"id": 2, "form_name": "Grade 2 to Grade 9", "form_year": "2020-2021", "school_name": "NATIONAL PUBLIC SCHOOL, JAYANAGAR", "instructin_file": "2-9", "school_short": "jayanagar", "school": "NPS - Jayanagar", "open_for_admissions": 1, "class_applied_for": "[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\"]", "documents": "{\"Birth Certificate\":\"Birth Certificate\",\"<PERSON><PERSON><PERSON> card\":\"<PERSON><PERSON><PERSON> card\",\"Others\":\"Others\"}", "prev_eduction_info": "{\"year\":[\"2017-18\",\"2018-19\",\"2019-20\"],\"result_types\":[\"Percent\",\"Grade\"],\"class\":{\"2\":{\"subject\":[{\"id\":\"1\",\"name\":\"English\",\"type\":\"label\"},{\"id\":\"2\",\"name\":\"Language 2\",\"type\":\"text\"},{\"id\":\"3\",\"name\":\"Maths\",\"type\":\"label\"},{\"id\":\"4\",\"name\":\"Science\",\"type\":\"label\"}]},\"3\":{\"subject\":[{\"id\":\"1\",\"name\":\"English\",\"type\":\"label\"},{\"id\":\"2\",\"name\":\"Language 2\",\"type\":\"text\"},{\"id\":\"3\",\"name\":\"Maths\",\"type\":\"label\"},{\"id\":\"4\",\"name\":\"Science\",\"type\":\"label\"}]},\"4\":{\"subject\":[{\"id\":\"1\",\"name\":\"English\",\"type\":\"label\"},{\"id\":\"2\",\"name\":\"Language 2\",\"type\":\"text\"},{\"id\":\"3\",\"name\":\"Maths\",\"type\":\"label\"},{\"id\":\"4\",\"name\":\"Science\",\"type\":\"label\"}]},\"5\":{\"subject\":[{\"id\":\"1\",\"name\":\"English\",\"type\":\"label\"},{\"id\":\"2\",\"name\":\"Language 2\",\"type\":\"text\"},{\"id\":\"3\",\"name\":\"Maths\",\"type\":\"label\"},{\"id\":\"4\",\"name\":\"Science\",\"type\":\"label\"}]},\"6\":{\"subject\":[{\"id\":\"1\",\"name\":\"English\",\"type\":\"label\"},{\"id\":\"2\",\"name\":\"Language 2\",\"type\":\"text\"},{\"id\":\"3\",\"name\":\"Language 3\",\"type\":\"text\"},{\"id\":\"4\",\"name\":\"Maths\",\"type\":\"label\"},{\"id\":\"5\",\"name\":\"Science\",\"type\":\"label\"}]},\"7\":{\"subject\":[{\"id\":\"1\",\"name\":\"English\",\"type\":\"label\"},{\"id\":\"2\",\"name\":\"Language 2\",\"type\":\"text\"},{\"id\":\"3\",\"name\":\"Language 3\",\"type\":\"text\"},{\"id\":\"4\",\"name\":\"Maths\",\"type\":\"label\"},{\"id\":\"5\",\"name\":\"Science\",\"type\":\"label\"}]},\"8\":{\"subject\":[{\"id\":\"1\",\"name\":\"English\",\"type\":\"label\"},{\"id\":\"2\",\"name\":\"Language 2\",\"type\":\"text\"},{\"id\":\"3\",\"name\":\"Language 3\",\"type\":\"text\"},{\"id\":\"4\",\"name\":\"Maths\",\"type\":\"label\"},{\"id\":\"5\",\"name\":\"Science\",\"type\":\"label\"}]},\"9\":{\"subject\":[{\"id\":\"1\",\"name\":\"English\",\"type\":\"label\"},{\"id\":\"2\",\"name\":\"Language 2\",\"type\":\"text\"},{\"id\":\"3\",\"name\":\"Language 3\",\"type\":\"text\"},{\"id\":\"4\",\"name\":\"Maths\",\"type\":\"label\"},{\"id\":\"5\",\"name\":\"Science\",\"type\":\"label\"}]}}}", "streams": "", "guidelines": "{\"name\":\"NATIONAL PUBLIC SCHOOL, JAYANAGAR\",\"line1\":\"#1\",\"0\":\"4th T Block East,\",\"1\":\"Jayanagar 3rd Block East\",\"line2\":\"Jayanagar\",\"2\":\"\",\"line3\":\"Bengaluru \\u2013 560011\",\"line4\":\"South India\",\"phone\":\"<strong>Phone: <\\/strong>080 4124 9369\",\"fax\":\"\",\"email\":\"<strong>Email: <\\/strong> <EMAIL>\",\"school_short\":\"NPS - JAYANAGAR\",\"school_full\":\"NATIONAL PUBLIC SCHOOL, JAYANAGAR')\"}", "application_no_gen": "{\"name\":\"NPSJNR\",\"prefix\":\"2-9\",\"year\":\"2020\",\"digit_count\":5}"}, {"id": 4, "form_name": "Grade 11", "form_year": "2020-2021", "school_name": "NATIONAL PUBLIC SCHOOL, JAYANAGAR", "instructin_file": "11", "school_short": "jayanagar", "school": "NPS - Jayanagar", "open_for_admissions": 1, "class_applied_for": "[\"11\"]", "documents": "{\"Birth Certificate\":\"Birth Certificate\",\"<PERSON><PERSON><PERSON> card\":\"<PERSON><PERSON><PERSON> card\",\"Others\":\"Others\"}", "prev_eduction_info": "{\"year\":[\"2017-18\",\"2018-19\",\"2019-20\"],\"result_types\":[\"Percent\",\"Grade\"],\"class\":{\"11\":{\"subject\":[{\"id\":\"1\",\"name\":\"English\",\"type\":\"label\"},{\"id\":\"2\",\"name\":\"Language 2\",\"type\":\"text\"},{\"id\":\"3\",\"name\":\"Language 3\",\"type\":\"text\"},{\"id\":\"4\",\"name\":\"Maths\",\"type\":\"label\"},{\"id\":\"5\",\"name\":\"Science\",\"type\":\"label\"}]}}}", "streams": "{\"Science\":[{\"id\":\"1\",\"name\":\"Math, Physics, Chemistry, Biology\"},{\"id\":\"2\",\"name\":\"Math, Physics, Chemistry, Computer Science\"},{\"id\":\"3\",\"name\":\"Math, Physics, Chemistry, Economics\"}],\"Commerce\":[{\"id\":\"1\",\"name\":\"Math, Accountancy, Business Studies, Economics\"},{\"id\":\"2\",\"name\":\"Entrepreneurship, Accountancy, Business Studies, Economics\"}]}", "guidelines": "{\"name\":\"NATIONAL PUBLIC SCHOOL, JAYANAGAR\",\"line1\":\"#1\",\"0\":\"4th T Block East,\",\"1\":\"Jayanagar 3rd Block East\",\"line2\":\"Jayanagar\",\"2\":\"\",\"line3\":\"Bengaluru \\u2013 560011\",\"line4\":\"South India\",\"phone\":\"<strong>Phone: <\\/strong>080 4124 9369\",\"fax\":\"\",\"email\":\"<strong>Email: <\\/strong> <EMAIL>\",\"school_short\":\"NPS - JAYANAGAR\",\"school_full\":\"NATIONAL PUBLIC SCHOOL, JAYANAGAR')\"}", "application_no_gen": "{\"name\":\"NPSJNR\",\"prefix\":\"11\",\"year\":\"2020\",\"digit_count\":5}"}]