<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Academic Year
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Download_controller extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
  		redirect('auth/login', 'refresh');
  	}
    $this->load->model('library_model');
    $this->load->library('filemanager');
    $this->load->library('ciqrcode');
    $this->config->load('form_elements');
    $this->load->model('parent_model');
    $this->load->library('zip');
	}

  public function generate_student_qr_codes(){
    $data['classes'] = $this->library_model->get_all_class();
    $data['main_content']='download/student_qr_code';
    $this->load->view('inc/template',$data); 
  }

  public function generate_staff_qr_codes(){
    $data['main_content']='download/staff_qr_code';
    $this->load->view('inc/template',$data); 
  }

  public function generate_student_photos(){
    $data['classes'] = $this->library_model->get_all_class();
    $data['main_content']='download/student_photo';
    $this->load->view('inc/template',$data); 
  }

  public function generate_parent_photos() {
    $data['classes'] = $this->library_model->get_all_class();
    $data['main_content']='download/parent_photos';
    $this->load->view('inc/template',$data);
  }

  public function getSectionStudents() {
    $section_id = $_POST['section_id'];
    $students = $this->library_model->get_student_all($section_id);
    echo json_encode($students);
  }

  public function getStudentParentPhotos() {
    $student_ids = $_POST['student_ids'];
    $profile_confirmed = $_POST['profile_confirmed'];
    $data = $this->library_model->GetStudentParentPhotos($student_ids, $profile_confirmed);
    echo json_encode($data);
  }

  public function download_student_parent_photos() {
    // echo '<pre>'; print_r($_POST); die();
    $student_photos = $_POST['student_photos'];
    $student_names = $_POST['names'];
    $sections = $_POST['section'];
    // $parent_photos = $_POST['parent_photos'];
    $school = $this->settings->getSetting('school_short_name');
    foreach ($student_photos as $student_id => $std_url) {
      $ext = pathinfo($std_url, PATHINFO_EXTENSION);
      $name = $student_names[$student_id].' - '.$student_id.'.'.$ext;
      $clsFolder = $sections[$student_id];
      $data = file_get_contents($std_url);
      $this->zip->add_data($name, $data);
      $this->zip->archive(FCPATH.'assets/student_photos/'.$clsFolder.'.zip');
      if(isset($_POST['parent_photos'])) {
        if (array_key_exists($student_id, $_POST['parent_photos'])) {
          $parents = $_POST['parent_photos'][$student_id];
          foreach ($parents as $relation => $parent_url) {
            $p_ext = pathinfo($parent_url, PATHINFO_EXTENSION);
            $p_name = $student_names[$student_id].' - '.$student_id.'-'.$relation.'.'.$p_ext;
            $p_data = file_get_contents($parent_url);
            $this->zip->add_data($p_name, $p_data);
            $this->zip->archive(FCPATH.'assets/student_photos/'.$clsFolder.'.zip');
          }
        }
       
      }
    } 
    $this->zip->download($clsFolder.'.zip');
  }

  public function generate_staff_photos(){
    $this->load->model('role');
    $data['staff'] = $this->role->get_all_staff();
    $data['main_content']='download/staff_photos';
    $this->load->view('inc/template',$data); 
  }

  public function GetStaffPhotos() {
    $staff_ids = $_POST['staff_ids'];
    $data['staff'] = $this->library_model->GetStaffPhotos($staff_ids);
    $data['prefix_url'] = $this->filemanager->getFilePath('');
    echo json_encode($data);
  }

  public function download_staff_photos() {
    $photos = $_POST['photos'];
    $names = $_POST['names'];
    $school = $this->settings->getSetting('school_short_name');
    foreach ($photos as $staff_id => $url) {
      $ext = pathinfo($url, PATHINFO_EXTENSION);
      $name = $names[$staff_id].' - '.$staff_id.'.'.$ext;
      $data = file_get_contents($url);
      $this->zip->add_data($name, $data);
      $this->zip->archive(FCPATH.'assets/staff_photos/'.$school.'.zip');
    } 
    $this->zip->download($school.'.zip');  
  }

  public function generate_parent_qr_codes(){
    $data['classes'] = $this->library_model->get_all_class();
    $data['main_content']='download/parent_qr_codes';
    $this->load->view('inc/template',$data);
  }

   public function generate_staff_qr_code_view(){
    $staffId = $this->input->post('staffId');
    $data['s_qr_code'] = $this->library_model->get_s_qrCodeDetails($staffId);
    $result = [];
    
    foreach ($data['s_qr_code'] as $code) {
      if(empty($code->qr_code)){
        continue;
      }
      $params['data'] = $code->qr_code;  // Data to encode in QR code
      $params['level'] = 'H';          // Error correction level
      $params['size'] = 4;
      
      ob_start();
        $this->ciqrcode->generate($params, false);
        $qrCodeBinary = ob_get_clean();

        // Encode the QR code as base64
        $base64 = base64_encode($qrCodeBinary);
        $dataUrl = 'data:image/png;base64,' . $base64;

        // Store the base64 data URL in the result array
        $result[] = [
            'staff_code' => $code->qr_code,
            'qr_code' => $dataUrl,
            'file_name'=>$code->name
        ];

    }
    echo json_encode($result);
  }

  public function generate_student_qr_code_view() {
    $stdId = $this->input->post('stdId');
    $class_sectionId = $this->input->post('class_sectionId');
    
    // Get QR code details from the model
    $data['st_qr_code'] = $this->library_model->get_stu_qrCodeDetails($stdId);

    $result = []; // Initialize an array to store base64 QR codes

    foreach ($data['st_qr_code'] as $code) {
        if(empty($code->iCode)){
          continue;
        }
        $params['data'] = $code->iCode;  // Data to encode in QR code
        $params['level'] = 'H';          // Error correction level
        $params['size'] = 4;             // QR code size

        // Generate QR code
        ob_start();
        $this->ciqrcode->generate($params, false);
        $qrCodeBinary = ob_get_clean();

        // Encode the QR code as base64
        $base64 = base64_encode($qrCodeBinary);
        $dataUrl = 'data:image/png;base64,' . $base64;

        // Store the base64 data URL in the result array
        $result[] = [
            'student_code' => $code->iCode,
            'qr_code' => $dataUrl,
            'file_name'=>$code->name.' - '.$code->cls_sec
        ];
    }
    // echo '<pre>';print_r($result);die();
    // Return the result as a JSON response
    echo json_encode($result);
}

  public function generate_student_photos_download(){
    $stdId = $this->input->post('stdId');
    $data['st_qr_code'] = $this->library_model->get_stu_qrCodeDetails($stdId);  
    foreach ($data['st_qr_code'] as $code) {
      if ($code->picture_url !='') {
        $adm = str_replace('/',' ',$code->admission_no);
        $url = $this->filemanager->getFilePath($code->picture_url);
        $ext = pathinfo($url, PATHINFO_EXTENSION);
        $name = $code->name.' - '.$adm.'.'.$ext;
        $clsFolder = $code->cls_sec;
        $data = file_get_contents($url);
        $this->zip->add_data($name, $data);
        $this->zip->archive(FCPATH.'assets/student_photos/'.$clsFolder.'.zip');
      }
    } 
    $this->zip->download($clsFolder.'.zip');  
  }

  public function get_classwise_parent_of_student(){
    $sectionId = $this->input->post('sectionId');
    $select_type = $this->input->post('select_type');
    $result = $this->library_model->get_section_parent_of_student($sectionId,$select_type);
    $std = array_chunk($result, 6);
    $template = "";
    foreach ($std as $value) {
      $template .= "<tr>";
      foreach ($value as $res) {
        $template .= "<td>".$res->pname.' ('.$res->name.')'.'<br>'.$res->cls_sec." <input type='hidden' name='pId[]' class='pId' value=".$res->pId."></td>";  
      }
      $template .= "</tr>";
    }
    print($template);
  }

  public function download_parent_qr_codes(){
    $pId = $this->input->post('pId');
    $class_sectionId = $this->input->post('class_sectionId');
    $relation_type = $this->input->post('relation_type');
    $parent_qr_codes = $this->library_model->get_parent_qrCodeDetails($pId);
    foreach ($parent_qr_codes as $code) {
      $data['image_name'] = $code->pname.' ( '.$code->name.'-'.$code->cls_sec.' )'.'.png';
      $params['data'] = bin2hex($code->identification_code);
      $params['level'] = 'H';
      $params['size'] = 4;
      $path = FCPATH.'assets/parent_qr_codes/'.$class_sectionId.'/';
      if (!file_exists ($path)){
        mkdir($path,0777,true);
      }
      $params['savename'] = $path.'/'.$data['image_name'];
      $this->ciqrcode->generate($params); 
      $savename = file_get_contents($params['savename']);
      $this->zip->add_data($data['image_name'], $savename);
      $this->zip->archive(FCPATH.'assets/parent_qr_codes/'.$relation_type.'_'.$class_sectionId.'.zip');
    }
    $this->load->helper("file"); // load the helper
    delete_files($path, true); // delete all files/folders
    rmdir($path);
    $this->zip->download($relation_type.'_'.$class_sectionId.'.zip');
  }

  public function generate_invidualstudent_qr_code_view(){
    $studentCode = $this->input->post('studentCode');
    $std_name = $this->input->post('std_name');
    $params['data'] = $studentCode;
    $params['level'] = 'H';
    $params['size'] = 4;
    ob_start();
    $this->ciqrcode->generate($params, false);
    $qrCodeBinary = ob_get_clean();
    $base64 = base64_encode($qrCodeBinary);
    $dataUrl = 'data:image/png;base64,' . $base64;
    echo $dataUrl;
  }

}