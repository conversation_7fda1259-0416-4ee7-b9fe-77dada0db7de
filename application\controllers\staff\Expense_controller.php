<?php
defined('BASEPATH') OR exit('No direct script access allowed');
/**
 *  oxygen v2
 */
class Expense_controller extends CI_Controller {
  public function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->load->model('ExpenseModel', 'exp');
    $this->load->library('filemanager'); 
  }

  public function index() {
    $data['main_content'] = 'staff/expense/index';
    $this->load->view('inc/template', $data);
  }

  public function addExpenseData() {
    //   echo  $this->authorization->getAvatarId(); die();
    $data['vendor_list'] = $this->exp->getVendorList();
    $data['categories_list'] = $this->exp->getCategoriesList();
    
    $data['main_content'] = 'staff/expense/expense_add';
    $this->load->view('inc/template', $data);
   }

   public function s3FileUpload($file) {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
     }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'Voucher');
    }

  /**
   * Store Data from this method.
   */
  public function saveExpenseData() {
    // echo "<pre>"; print_r($_POST); die();

    $filename = '';
    $_POST['date'] = date('Y-m-d', strtotime($_POST['date']));
    if (isset($_FILES['userfile'])) {
      $filepath = $this->s3FileUpload($_FILES['userfile']);
      $result = $this->exp->insertExpenseData($filepath['file_name']);
    } else {
      $result = $this->exp->insertExpenseData();
    }
    if($result)
        $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
    else
        $this->session->set_flashdata('flashError', 'Something went wrong.');
        // echo $file_path;
    // echo '<pre>'; print_r($_POST); die();

    redirect('staff/Expense_controller/addExpenseData');
   }


    public function my_expenses(){
        $data['my_expenses'] = $this->exp->my_expenses();
    //echo '<pre>'; print_r($data); die();
        $data['main_content'] = 'staff/expense/my_expenses';
        $this->load->view('inc/template', $data);
    }

    public function ExpenseEdit($expense_id){
        $data['expense_details'] = $this->exp->expense_details($expense_id);
        $data['vendor_list'] = $this->exp->getVendorList();
        $data['categories_list'] = $this->exp->getCategoriesList();
        //echo '<pre>'; print_r($data); die();
        $data['main_content'] = 'staff/expense/ExpenseEdit';
        $this->load->view('inc/template', $data);
    }

    public function updateExpenseData() {
        // echo "<pre>"; print_r($_POST); die();
    
        $filename = '';
        $_POST['date'] = date('Y-m-d', strtotime($_POST['date']));
        if (isset($_FILES['userfile'])) {
          $filepath = $this->s3FileUpload($_FILES['userfile']);
          $result = $this->exp->updateExpenseData($filepath['file_name']);
        } else {
          $result = $this->exp->updateExpenseData();
        }
        if($result)
            $this->session->set_flashdata('flashSuccess', 'Successfully Updated.');
        else
            $this->session->set_flashdata('flashError', 'Something went wrong.');
            // echo $file_path;
        // echo '<pre>'; print_r($_POST); die();
    
        redirect('staff/Expense_controller/my_expenses');
       }
}  