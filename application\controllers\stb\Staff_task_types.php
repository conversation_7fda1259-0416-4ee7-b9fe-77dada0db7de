<?php

class Staff_task_types extends CI_Controller {
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('STAFF_TASKS_BASKET')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('stb/Staff_task_type_model', 'task_type_model');
        $this->CI = &get_instance();
        $this->load->library('filemanager');
    }

    public function staff_task_types($type = 'task_basket'){
        $data['type'] = $type;
        $data['title'] = 'Manage Task Types';
        if($this->mobile_detect->isTablet()){
            $data['main_content']    = 'stb/manage_task_types/staff_task_types.php';
        }else if ($this->mobile_detect->isMobile()) {
            $data['main_content']    = 'stb/manage_task_types/staff_task_types_mobile.php';
        }else{
            $data['main_content']    = 'stb/manage_task_types/staff_task_types.php';
        }
        $this->load->view('inc_v2/template', $data);
    }

    public function admin_task_types($type = 'task_basket'){
        $data['type'] = $type;
        $data['title'] = 'Manage Task Types';
        if($this->mobile_detect->isTablet()){
            $data['main_content']    = 'stb/manage_task_types/admin_task_types.php';
        }else if ($this->mobile_detect->isMobile()) {
            $data['main_content']    = 'stb/manage_task_types/admin_task_types_mobile.php';
        }else{
            $data['main_content']    = 'stb/manage_task_types/admin_task_types.php';
        }
        $this->load->view('inc_v2/template', $data);
    }

    public function get_default_and_user_task_types(){
        $result = $this->task_type_model->get_default_and_user_task_types($_POST);
        echo json_encode($result);
    }

    public function get_user_task_types(){
        $result = $this->task_type_model->get_user_task_types($_POST);
        echo json_encode($result);
    }

    public function submit_task_type(){
        // echo "<pre>";print_r($_POST);die();
        $result = $this->task_type_model->submit_task_type($_POST);
        echo json_encode($result);
    }

    public function update_task_type(){
        $result = $this->task_type_model->update_task_type($_POST);
        echo json_encode($result);
    }

    public function delete_task_type(){
        $result = $this->task_type_model->delete_task_type($_POST['task_type_id']);
        echo json_encode($result);
    }
}