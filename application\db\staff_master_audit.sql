DROP TRIGGER IF EXISTS staff_master_audit;
DELIMITER $$
CREATE TRIGGER staff_master_audit
AFTER UPDATE ON staff_master FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.first_name, NEW.first_name, 'first_name');
    CALL merge_object(@oldJson, @newJson, OLD.last_name, NEW.last_name, 'last_name');
    CALL merge_object(@old<PERSON>son, @newJson, OLD.short_name, NEW.short_name, 'short_name');
    CALL merge_object(@oldJson, @newJson, OLD.status, NEW.status, 'status');
    CALL merge_object(@oldJson, @newJson, OLD.staff_type, NEW.staff_type, 'staff_type');
    CALL merge_object(@oldJson, @newJson, OLD.designation, NEW.designation, 'designation');
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.department, NEW.department, 'department');
    CALL merge_object(@old<PERSON><PERSON>, @new<PERSON>son, OLD.father_first_name, NEW.father_first_name, 'father_first_name');
    CALL merge_object(@oldJson, @newJson, OLD.mother_first_name, NEW.mother_first_name, 'mother_first_name');
    CALL merge_object(@oldJson, @newJson, OLD.marital_status, NEW.marital_status, 'marital_status');
    CALL merge_object(@oldJson, @newJson, OLD.spouse_name, NEW.spouse_name, 'spouse_name');
    CALL merge_object(@oldJson, @newJson, OLD.picture_url, NEW.picture_url, 'picture_url');
    CALL merge_object(@oldJson, @newJson, OLD.dob, NEW.dob, 'dob');
    CALL merge_object(@oldJson, @newJson, OLD.nationality, NEW.nationality, 'nationality');
    CALL merge_object(@oldJson, @newJson, OLD.gender, NEW.gender, 'gender');
    CALL merge_object(@oldJson, @newJson, OLD.aadhar_number, NEW.aadhar_number, 'aadhar_number');
    CALL merge_object(@oldJson, @newJson, OLD.qualification, NEW.qualification, 'qualification');
    CALL merge_object(@oldJson, @newJson, OLD.subject_specialization, NEW.subject_specialization, 'subject_specialization');
    CALL merge_object(@oldJson, @newJson, OLD.total_experience, NEW.total_experience, 'total_experience');
    CALL merge_object(@oldJson, @newJson, OLD.total_education_experience, NEW.total_education_experience, 'total_education_experience');
    CALL merge_object(@oldJson, @newJson, OLD.active, NEW.active, 'active');
    CALL merge_object(@oldJson, @newJson, OLD.isdummy, NEW.isdummy, 'isdummy');
    CALL merge_object(@oldJson, @newJson, OLD.contact_number, NEW.contact_number, 'contact_number');
    CALL merge_object(@oldJson, @newJson, OLD.alternative_number, NEW.alternative_number, 'alternative_number');

    CALL merge_object(@oldJson, @newJson, OLD.spouse_contact_no, NEW.spouse_contact_no, 'spouse_contact_no');
    CALL merge_object(@oldJson, @newJson, OLD.joining_date, NEW.joining_date, 'joining_date');
    CALL merge_object(@oldJson, @newJson, OLD.emergency_info, NEW.emergency_info, 'emergency_info');
    CALL merge_object(@oldJson, @newJson, OLD.math_high_grade, NEW.math_high_grade, 'math_high_grade');
    CALL merge_object(@oldJson, @newJson, OLD.english_high_grade, NEW.english_high_grade, 'english_high_grade');
    CALL merge_object(@oldJson, @newJson, OLD.social_high_grade, NEW.social_high_grade, 'social_high_grade');
    CALL merge_object(@oldJson, @newJson, OLD.trained_to_teach, NEW.trained_to_teach, 'trained_to_teach');
    CALL merge_object(@oldJson, @newJson, OLD.biometric_attendance_code, NEW.biometric_attendance_code, 'biometric_attendance_code');
    CALL merge_object(@oldJson, @newJson, OLD.appointed_subject, NEW.appointed_subject, 'appointed_subject');
    CALL merge_object(@oldJson, @newJson, OLD.classes_taught, NEW.classes_taught, 'classes_taught');
    CALL merge_object(@oldJson, @newJson, OLD.main_sub_taught, NEW.main_sub_taught, 'main_sub_taught');
    CALL merge_object(@oldJson, @newJson, OLD.add_sub_taught, NEW.add_sub_taught, 'add_sub_taught');
    CALL merge_object(@oldJson, @newJson, OLD.identification_code, NEW.identification_code, 'identification_code');

    CALL merge_object(@oldJson, @newJson, OLD.shift_id, NEW.shift_id, 'shift_id');
    CALL merge_object(@oldJson, @newJson, OLD.webcam_avatar, NEW.webcam_avatar, 'webcam_avatar');
    CALL merge_object(@oldJson, @newJson, OLD.joined_helium, NEW.joined_helium, 'joined_helium');
    CALL merge_object(@oldJson, @newJson, OLD.joined_helium_on, NEW.joined_helium_on, 'joined_helium_on');
    CALL merge_object(@oldJson, @newJson, OLD.profile_confirmed, NEW.profile_confirmed, 'profile_confirmed');
    CALL merge_object(@oldJson, @newJson, OLD.oc_platform, NEW.oc_platform, 'oc_platform');
    CALL merge_object(@oldJson, @newJson, OLD.oc_link, NEW.oc_link, 'oc_link');
    CALL merge_object(@oldJson, @newJson, OLD.oc_last_modified_by, NEW.oc_last_modified_by, 'oc_last_modified_by');
    CALL merge_object(@oldJson, @newJson, OLD.oc_link, NEW.oc_link, 'oc_link');
    CALL merge_object(@oldJson, @newJson, OLD.oc_additional_info, NEW.oc_additional_info, 'oc_additional_info');

    CALL merge_object(@oldJson, @newJson, OLD.is_reporting_manager, NEW.is_reporting_manager, 'is_reporting_manager');
    CALL merge_object(@oldJson, @newJson, OLD.reporting_manager_id, NEW.reporting_manager_id, 'reporting_manager_id');
    CALL merge_object(@oldJson, @newJson, OLD.reporting_manager_id, NEW.reporting_manager_id, 'reporting_manager_id');
    CALL merge_object(@oldJson, @newJson, OLD.oc_mail_id, NEW.oc_mail_id, 'oc_mail_id');
    CALL merge_object(@oldJson, @newJson, OLD.oc_password, NEW.oc_password, 'oc_password');

    CALL merge_object(@oldJson, @newJson, OLD.employee_code, NEW.employee_code, 'employee_code');
    CALL merge_object(@oldJson, @newJson, OLD.blood_group, NEW.blood_group, 'blood_group');
    CALL merge_object(@oldJson, @newJson, OLD.boarding, NEW.boarding, 'boarding');
    CALL merge_object(@oldJson, @newJson, OLD.last_working_day, NEW.last_working_day, 'last_working_day');
    CALL merge_object(@oldJson, @newJson, OLD.father_last_name, NEW.father_last_name, 'father_last_name');
    CALL merge_object(@oldJson, @newJson, OLD.voter_id, NEW.voter_id, 'voter_id');
    CALL merge_object(@oldJson, @newJson, OLD.height, NEW.height, 'height');
    CALL merge_object(@oldJson, @newJson, OLD.weight, NEW.weight, 'weight');
    CALL merge_object(@oldJson, @newJson, OLD.allergies, NEW.allergies, 'allergies');
    CALL merge_object(@oldJson, @newJson, OLD.medical_issues, NEW.medical_issues, 'medical_issues');
    CALL merge_object(@oldJson, @newJson, OLD.identification_mark, NEW.identification_mark, 'identification_mark');
    CALL merge_object(@oldJson, @newJson, OLD.person_with_disability, NEW.person_with_disability, 'person_with_disability');
    CALL merge_object(@oldJson, @newJson, OLD.sports, NEW.sports, 'sports');
    CALL merge_object(@oldJson, @newJson, OLD.dramatics, NEW.dramatics, 'dramatics');
    CALL merge_object(@oldJson, @newJson, OLD.music, NEW.music, 'music');
    CALL merge_object(@oldJson, @newJson, OLD.landline_number, NEW.landline_number, 'landline_number');
    CALL merge_object(@oldJson, @newJson, OLD.dance, NEW.dance, 'dance');
    CALL merge_object(@oldJson, @newJson, OLD.mobile_number_2, NEW.mobile_number_2, 'mobile_number_2');
    CALL merge_object(@oldJson, @newJson, OLD.passport_number, NEW.passport_number, 'passport_number');
    CALL merge_object(@oldJson, @newJson, OLD.passport_place_of_issue, NEW.passport_place_of_issue, 'passport_place_of_issue');
    CALL merge_object(@oldJson, @newJson, OLD.passport_date_of_issue, NEW.passport_date_of_issue, 'passport_date_of_issue');
    CALL merge_object(@oldJson, @newJson, OLD.passport_expiry_date, NEW.passport_expiry_date, 'passport_expiry_date');
    CALL merge_object(@oldJson, @newJson, OLD.visa_details, NEW.visa_details, 'visa_details');
    CALL merge_object(@oldJson, @newJson, OLD.religion, NEW.religion, 'religion');
    CALL merge_object(@oldJson, @newJson, OLD.high_quality_picture_url, NEW.high_quality_picture_url, 'high_quality_picture_url');
    CALL merge_object(@oldJson, @newJson, OLD.custom1, NEW.custom1, 'custom1');
    CALL merge_object(@oldJson, @newJson, OLD.custom2, NEW.custom2, 'custom2');
    CALL merge_object(@oldJson, @newJson, OLD.custom3, NEW.custom3, 'custom3');
    CALL merge_object(@oldJson, @newJson, OLD.custom4, NEW.custom4, 'custom4');
    CALL merge_object(@oldJson, @newJson, OLD.custom5, NEW.custom5, 'custom5');
    CALL merge_object(@oldJson, @newJson, OLD.custom6, NEW.custom6, 'custom6');
    CALL merge_object(@oldJson, @newJson, OLD.custom7, NEW.custom7, 'custom7');
    CALL merge_object(@oldJson, @newJson, OLD.custom8, NEW.custom8, 'custom8');
    CALL merge_object(@oldJson, @newJson, OLD.custom9, NEW.custom9, 'custom9');
    CALL merge_object(@oldJson, @newJson, OLD.custom10, NEW.custom10, 'custom10');
    CALL merge_object(@oldJson, @newJson, OLD.custom11, NEW.custom11, 'custom11');
    CALL merge_object(@oldJson, @newJson, OLD.custom12, NEW.custom12, 'custom12');
    CALL merge_object(@oldJson, @newJson, OLD.custom13, NEW.custom13, 'custom13');
    CALL merge_object(@oldJson, @newJson, OLD.custom14, NEW.custom14, 'custom14');
    CALL merge_object(@oldJson, @newJson, OLD.custom15, NEW.custom15, 'custom15');
    CALL merge_object(@oldJson, @newJson, OLD.custom16, NEW.custom16, 'custom16');
    CALL merge_object(@oldJson, @newJson, OLD.custom17, NEW.custom17, 'custom17');
    CALL merge_object(@oldJson, @newJson, OLD.custom18, NEW.custom18, 'custom18');
    CALL merge_object(@oldJson, @newJson, OLD.custom19, NEW.custom19, 'custom19');
    CALL merge_object(@oldJson, @newJson, OLD.custom20, NEW.custom20, 'custom20');
    CALL merge_object(@oldJson, @newJson, OLD.category, NEW.category, 'category');
    CALL merge_object(@oldJson, @newJson, OLD.caste, NEW.caste, 'caste');
    CALL merge_object(@oldJson, @newJson, OLD.has_completed_any_bed, NEW.has_completed_any_bed, 'has_completed_any_bed');
    CALL merge_object(@oldJson, @newJson, OLD.resignation_letter_doc, NEW.resignation_letter_doc, 'resignation_letter_doc');
    CALL merge_object(@oldJson, @newJson, OLD.last_date_of_work, NEW.last_date_of_work, 'last_date_of_work');
    CALL merge_object(@oldJson, @newJson, OLD.exit_remarks, NEW.exit_remarks, 'exit_remarks');
    CALL merge_object(@oldJson, @newJson, OLD.exit_update_on, NEW.exit_update_on, 'exit_update_on');
    CALL merge_object(@oldJson, @newJson, OLD.exit_updated_by, NEW.exit_updated_by, 'exit_updated_by');
    CALL merge_object(@oldJson, @newJson, OLD.nature_of_appointment, NEW.nature_of_appointment, 'nature_of_appointment');
    CALL merge_object(@oldJson, @newJson, OLD.personal_mail_id, NEW.personal_mail_id, 'personal_mail_id');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'staff_master',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        NEW.last_modified_by
    );
    END IF;
END;
$$
DELIMITER ;