<?php

class Consent_form_controller extends CI_Controller
{

	function __construct()
	{
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN') && !$this->settings->isParentModuleEnabled('CONSENT_FORM')) {
			redirect('dashboard', 'refresh');
		}
		$this->load->library('filemanager');
		$this->load->model('Consent_model');
		$this->load->model('parent_model');

	}

	public function index($callFrom = '')
	{
		$data['callFrom'] = $callFrom;
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$is_student_partially_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
		$deactivated_modules = json_decode($this->settings->getSetting('deactivation_modules'));

		$is_deactivated = 0;
		if ($is_student_partially_deactivated && in_array('Student_Consent_Forms', $deactivated_modules)) {
			$is_deactivated = 1;
		} else {
			$is_deactivated = 0;
		}

		if ($is_deactivated == 1) {
			$data["module_name"] = "Student Consent Forms";
			$data['main_content'] = 'parent/temporary_deactive_page.php';
		} else {
			$data['studentId'] = $studentId;
			$data['parent_id'] = $this->authorization->getAvatarStakeHolderId();
	
			$data['documents'] = $this->Consent_model->get_consent_form_templates($data['studentId']);
			foreach ($data['documents'] as &$val) {
				if ($val->status == 'Submitted') {
					$val->consent_form_path = $this->filemanager->getFilePath($val->consent_form_path);
				}
			}
	
			if ($this->mobile_detect->isTablet()) {
				$data['main_content'] = 'consent_form_tablet_view';
			} else if ($this->mobile_detect->isMobile()) {
				$data['main_content'] = 'consent_form_mobile_view';
			} else {
				$data['main_content'] = 'consent_form_view';
			}
		}

		$this->load->view('inc/template', $data);
	}

	public function save_consent_form()
	{
		$path = $_POST['path'];
		$std_id = $_POST['std_id'];
		$parent_id = $_POST['parent_id'];
		$student_consent_templates_id = $_POST['student_consent_templates_id'];

		echo $this->Consent_model->save_consent_form($path, $student_consent_templates_id, $std_id, $parent_id, '', '');
	}

	public function get_path_to_view()
	{
		$path = $_POST['path'];
		$url = $this->filemanager->getFilePath($path);

		echo json_encode($url);

	}

	public function s3FileUpload($file,$folder_name='Profile') {
        if($file['tmp_name'] == '' || $file['name'] == '') {
          return ['status' => 'empty', 'file_name' => ''];
        }        
        return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],$folder_name);
    }

	public function submit_agreed_consent_form()
	{
		$path = $_POST['path'];
		$std_id = $_POST['s_id'];
		$parent_id = $_POST['p_id'];
		$student_consent_templates_id = $_POST['t_id'];
		$agree_status = $_POST['agree_status'];
		$do_not_agree_reasons = $_POST['do_not_agree_reasons'];
		$need_signature = $_POST['need_signature'];
		if($need_signature != 'Yes'){
			$result = $this->Consent_model->save_consent_form($path, $student_consent_templates_id, $std_id, $parent_id, $agree_status, $do_not_agree_reasons,'');
		}else{
			$signature_path = '';
			if(isset($_FILES['signature'])){
				$signature_path = $this->s3FileUpload($_FILES['signature'],'signature');
			}
			$result = $this->Consent_model->save_consent_form($path, $student_consent_templates_id, $std_id, $parent_id, $agree_status, $do_not_agree_reasons, $signature_path);
		}
		$template_data = $this->Consent_model->get_template_data($student_consent_templates_id);
		if(!empty($template_data->email_template_id)){
			if(empty($template_data->notification_staff_ids)){
				$result = 'Email ids needs to be added to proceed';
			}else{
				$email_data = $this->Consent_model->get_data_toSend_email($template_data->email_template_id,$std_id,$template_data->notification_staff_ids);
				$sent_mail = $this->_email_to_parent_staff($path,$email_data);
			}
		}
		echo json_encode($result);
	}

	private function _email_to_parent_staff($path,$input){
		$this->load->helper('email_helper');
		$emailIds = $input['to_emails'];
		$memberEmail = [];
		foreach ($emailIds as $key => $val) {
			array_push($memberEmail, $val);
		}
		$input['template_content'] = str_replace('%%student_name%%',$input['to_email']->student_name,$input['template_content']);
		$input['template_content'] = str_replace('%%class_name%%',$input['to_email']->class_section_name,$input['template_content']);
		$input['template_content'] = str_replace('%%enrollment_number%%',$input['to_email']->enrollment_number,$input['template_content']);
		$input['template_content'] = str_replace('%%admission_number%%',$input['to_email']->admission_no,$input['template_content']);
		$files_array = array();

			if($path != '') {
			array_push($files_array, array('name' => 'Consent Form', 'path' => str_replace("https://s3.us-west-1.wasabisys.com/nextelement/", "", $path)));
			}

			$files_string = '';
			if(!empty($files_array)) {
			$files_string = json_encode($files_array);
			}
		return sendEmail($input['template_content'], $input['email_subject'], 0, $memberEmail, $input['registered_email'], json_decode($files_string));
	}

	public function get_student_consent_forms()
	{
		$consent_form_template_id = $_POST["consent_form_template_id"];

		$documents = $this->Consent_model->get_consent_form_templates_by_template_id($consent_form_template_id);
		echo json_encode($documents);
	}

	public function get_student_parents_details()
	{
		$student_id = $_POST["student_id"];
		$parentsDetail = $this->Consent_model->get_student_parents_details($student_id);
		echo json_encode($parentsDetail);
	}

	public function s3FileDocumentUpload($file){
	  if ($file['tmp_name'] == '' || $file['name'] == '') {
		return ['status' => 'empty', 'file_name' => ''];
	  }
	  return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], 'student_consent');
	}

	
	public function staff_side_consent_confirmation()
	{
		$path = $this->s3FileDocumentUpload($_FILES['uploadedFile']);
		echo $this->Consent_model->staff_side_consent_confirmation($path, $this->input->post());
	}

	public function get_parent_signature(){
		$avatar_id = $this->authorization->getAvatarId();
		$result = $this->Consent_model->get_parent_signature($avatar_id);
		echo json_encode($result);
	}


}

?>