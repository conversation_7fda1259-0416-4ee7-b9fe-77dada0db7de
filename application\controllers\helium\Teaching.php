<?php

class Teaching extends CI_Controller {
    private $avatar_type;
    private $helium_accessor;
    function __construct(){
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->avatar_type = $this->authorization->getAvatarType();
        //if not staf or super admin
        if($this->avatar_type != 3 && $this->avatar_type != 4) {
            redirect('dashboard');
        }
        $this->helium_accessor = 'school';
        if($this->settings->getSetting('helium_accessor')) {
            $this->helium_accessor = $this->settings->getSetting('helium_accessor');
        }
        $this->load->model('helium/Teaching_model');
        $this->load->model('helium/Learning_model');
        $this->load->library('filemanager');
    }

    private function generateJWT($data) {
      $this->load->library('JWT');
      $secret = "bmsjman09rsasaac1ezin_tx";
      $data['accessor'] = $this->helium_accessor;

      $payload = [
            "user" => $data,
            "iss" => "nextelement"
      ];

      // echo '<pre>';print_r($payload);die();
      $jwt = JWT::encode($payload, $secret, 'HS256');
      return $jwt;
    }

    private function __call_api($data, $end_point) {
        $end_point = CONFIG_ENV['helium'].$end_point;
        $curl_request = [
            CURLOPT_URL => $end_point,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                "content-type: application/json"
            ]
        ];

        $curl = curl_init();
        curl_setopt_array($curl, $curl_request);
        $response = curl_exec($curl);
        // echo "<pre>"; print_r($db_response); die();
        curl_close($curl);
        return $response;
    }

    public function bridge() {
        $data = $_POST;
        if(!empty($_FILES)) {
            // echo '<pre>'; print_r($_FILES); 
            if(isset($data['vendor']) || $data['vendor'] == '') {
                $vendor = $data['vendor'];
            } else {
                $vendor = 'heliumacademy';
            }
            foreach ($_FILES as $key => $file) {
                $data[$key] = null;
                $return = $this->filemanager->uploadFileToS3Helium($file, $vendor, 'resources');
                if($return['status'] != 'error') {
                    $data[$key] = $return['file_name'];
                }
            }
        }
        // echo '<pre>'; print_r($data); die();
        $response = $this->__call_api($data, $_POST['end_point']);
        echo $response;
    }

    private function __generate_token($additional=[]) {
        $staff_id = $this->authorization->getAvatarStakeHolderId();
        $school = $this->settings->getSetting('school_short_name');
        $helium_admin = ($this->authorization->isAuthorized('HELIUM.ADMIN'))?1:0;
        $staff = [
            "id" =>  $staff_id,
            "school" => $school,
            "is_admin" => $helium_admin
        ];
        // echo '<pre>'; print_r($staff); die();
        if(!empty($additional)) {
            $staff = array_merge($staff, $additional);
        }
        $jwt = $this->generateJWT($staff);
        return $jwt;
    }

    public function index() {
        $avatar_type = ($this->avatar_type==3)?'Super Admin':'Staff';
        $data['avatar_type'] = $avatar_type;
        $staff_id = $this->authorization->getAvatarStakeHolderId();
        $staff = $this->Teaching_model->getStaffInfo($staff_id);
        $helium_admin = ($this->authorization->isAuthorized('HELIUM.ADMIN'))?1:0;
        $data['staff_id'] = $staff_id;
        $data['staff'] = $staff;
        $school = $this->settings->getSetting('school_short_name');
        $is_vendor = $this->helium_accessor === 'vendor';
        $is_nextelement = $this->helium_accessor === 'nextelement';
        $is_school = $this->helium_accessor === 'school';
        if($avatar_type == 'Super Admin' || $staff->joined_helium) {
            $staff = [
                "id" => $staff_id,
                "school" => $school,
                "is_admin" => $helium_admin
            ];
            $data['jwt'] = $this->generateJWT($staff);

            $data['manage_courses'] = $this->authorization->isAuthorized('HELIUM.COURSE') && ($is_vendor || $is_nextelement);
            $data['schedules'] = $this->authorization->isAuthorized('HELIUM.VIEW_SESSIONS') && ($is_vendor || $is_nextelement);
            $data['view_registrations'] = $this->authorization->isAuthorized('HELIUM.VIEW_REGISTRATIONS');
            if ($this->mobile_detect->isTablet()) {
               $data['main_content']   = 'helium/staff/dashboard';
            }else if($this->mobile_detect->isMobile()){
              $data['main_content']    = 'helium/staff/dashboard';
            }else{
              $data['main_content']    = 'helium/staff/desktop/dashboard';
            }
            // $this->load->view($data['main_content'], $data);
            $this->load->view('helium/staff/inc/template', $data);
        } else {
            $staff = [
                "id" => $staff_id,
                "first_name" => $staff->first_name,
                "last_name" => $staff->last_name,
                "mobile_no" => $staff->mobile_no,
                "email" => $staff->email,
                "school" => $school
            ];
            //email, phone_no, dob 
            $data['jwt'] = $this->generateJWT($staff);
            if ($this->mobile_detect->isTablet()) {
               $data['main_content']   = 'helium/staff/starter';
            }else if($this->mobile_detect->isMobile()){
              $data['main_content']    = 'helium/staff/starter';
            }else{
              $data['main_content']    = 'helium/staff/desktop/starter';
            }
            $this->load->view($data['main_content'], $data);
        }
    }

    public function updateStaffAsJoined() {
        $staff_id = $this->authorization->getAvatarStakeHolderId();
        $status = $this->Teaching_model->updateStaffAsJoined($staff_id);
        echo $status;
    }

    // public function dashboard() {
    //     $this->load->view('helium/teacher/dashboard', $data);
    // }

    public function courses() {
        if(!$this->authorization->isAuthorized('HELIUM.COURSE')) redirect('helium/teaching');
        $data['modify_course'] = $this->authorization->isAuthorized('HELIUM.MODIFY_COURSE');
        $data['view_batch'] = $this->authorization->isAuthorized('HELIUM.BATCH');
        $data['jwt'] = $this->__generate_token();
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/staff/courses';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/staff/courses';
        }else{
          $data['main_content']    = 'helium/staff/desktop/courses';
        }
        $this->load->view('helium/staff/inc/template', $data);
    }

    public function course_form() {
        if(!$this->authorization->isAuthorized('HELIUM.MODIFY_COURSE')) redirect('helium/teaching');
        $data['jwt'] = $this->__generate_token();
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/staff/course_form';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/staff/course_form';
        }else{
          $data['main_content']    = 'helium/staff/desktop/course_form';
        }
        $this->load->view('helium/staff/inc/template', $data);
    }

    public function batches($courseId) {
        if(!$this->authorization->isAuthorized('HELIUM.BATCH')) redirect('helium/teaching');
        $data['modify_batch'] = $this->authorization->isAuthorized('HELIUM.MODIFY_BATCH');
        $data['modify_session'] = $this->authorization->isAuthorized('HELIUM.MODIFY_SESSION');
        $data['view_resources'] = $this->authorization->isAuthorized('HELIUM.VIEW_RESOURCES');
        $data['add_recording'] = $this->authorization->isAuthorized('HELIUM.ADD_RECORDING');
        $data['remove_recording'] = $this->authorization->isAuthorized('HELIUM.REMOVE_RECORDING');
        $data['jwt'] = $this->__generate_token(['course_id' => $courseId]);
        $data['course_id'] = $courseId;
        // $this->load->view('helium/teacher/batches',$data);
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/staff/batches';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/staff/batches';
        }else{
          $data['main_content']    = 'helium/staff/desktop/batches';
        }
        $this->load->view('helium/staff/inc/template', $data);
    }

    public function edit_batch() {
        if(!$this->authorization->isAuthorized('HELIUM.MODIFY_BATCH')) redirect('helium/teaching');
        $data['batch_id'] = $_POST['batch_id'];
        $data['course_id'] = $_POST['course_id'];
        // $this->load->view('helium/teacher/batches',$data);
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/staff/edit_batch';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/staff/edit_batch';
        }else{
          $data['main_content']    = 'helium/staff/desktop/edit_batch';
        }
        $this->load->view('helium/staff/inc/template', $data);
    }

    public function batch_form($courseId){
        if(!$this->authorization->isAuthorized('HELIUM.MODIFY_BATCH')) redirect('helium/teaching');
        $data['course_id'] = $courseId;
        $data['jwt'] = $this->__generate_token();
        // $this->load->view('helium/teacher/batch_form',$data);
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/staff/batch_form';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/staff/batch_form';
        }else{
          $data['main_content']    = 'helium/staff/desktop/batch_form';
        }
        $this->load->view('helium/staff/inc/template', $data);
    }
    
    public function view_schedules(){
        if(!$this->authorization->isAuthorized('HELIUM.VIEW_SESSIONS')) redirect('helium/teaching');
        // $this->load->view('helium/teacher/view_schedules');
        $data['jwt'] = $this->__generate_token();
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/staff/view_schedules';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/staff/view_schedules';
        }else{
          $data['main_content']    = 'helium/staff/desktop/view_schedules';
        }
        $this->load->view('helium/staff/inc/template', $data);
    }

    public function view_registrations() {
        if(!$this->authorization->isAuthorized('HELIUM.VIEW_REGISTRATIONS')) redirect('helium/teaching');
        $data['jwt'] = $this->__generate_token();
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/staff/view_registrations';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/staff/view_registrations';
        }else{
          $data['main_content']    = 'helium/staff/desktop/view_registrations';
        }
        $this->load->view('helium/staff/inc/template', $data);
    }

    public function course_resources($course_id) {
        if(!$this->authorization->isAuthorized('HELIUM.VIEW_RESOURCES')) {
            redirect('helium/teaching/batches/'.$course_id);
        }
        $data['course_id'] = $course_id;
        $data['add_resources'] = $this->authorization->isAuthorized('HELIUM.ADD_RESOURCES');
        $data['jwt'] = $this->__generate_token(['course_id' => $course_id]);
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helium/staff/course_resources';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helium/staff/course_resources';
        }else{
          $data['main_content']    = 'helium/staff/desktop/course_resources';
        }
        $this->load->view('helium/staff/inc/template', $data);
    }

    public function communication() {
        if($this->avatar_type != 3) redirect('helium/teaching');
        $data['main_content']    = 'helium/staff/desktop/communication';
        $this->load->view('helium/staff/inc/template', $data);
    }

    public function notification_sender() {
        if($this->avatar_type != 3) redirect('helium/teaching');
        $data['jwt'] = $this->__generate_token();
        $data['main_content']    = 'helium/staff/desktop/notification_sender';
        $this->load->view('helium/staff/inc/template', $data);
    }
    
}