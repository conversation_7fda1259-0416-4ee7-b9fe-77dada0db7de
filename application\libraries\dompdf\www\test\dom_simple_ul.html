<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">

</head>

<body>
<p>Here's a simple list from my favourite website:</p>

<ul>
  <li>The Zend Engine II  with a new object model and dozens of new features.</li>

  <li>XML support has been completely redone in PHP 5, all extensions are
  now focused around the excellent libxml2 library
  (http://www.xmlsoft.org/).</li>

  <li>A new SimpleXML extension for easily accessing and manipulating XML
  as PHP objects. It can also interface with the DOM extension and
  vice-versa.</li>

  <li>A brand new built-in SOAP extension for interoperability with Web Services.</li>

  <li>A new MySQL extension named MySQLi for developers using MySQL 4.1 and
  later. This new extension includes an object-oriented interface in
  addition to a traditional interface; as well as support for many of
  MySQL's new features, such as prepared statements.</li>

  <li>SQLite has been bundled with PHP. For more information on SQLite,
  please visit their website. </li>

  <li>Streams have been greatly improved, including the ability to access low-level socket operations on streams.</li>

  <li>And lots more...</li>

  <ul><li>Sublists</li><li>work</li><li>too!</li></ul>
</ul>

</body> </html>
