<?php

class Product_controller extends CI_Controller {
	function __construct() {
		  parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      //This should come up only for super admins
      if (!$this->authorization->isModuleEnabled('INVENTORY')) {
        redirect('dashboard', 'refresh');
    }
    $this->load->model('inventory/product_model');
    $this->load->model('sales/sales_model');
    $this->config->load('form_elements');
    $this->load->library('filemanager');
    $this->load->library('fee_library');
	}

  //Landing function to display Products
  public function index() {  
    $data['main_content'] = 'inventory/products/products_menu';
    $this->load->view('inc/template', $data); 
  }

  public function new_product() {
    $data['categories'] = $this->product_model->getProductCategories();
    $data['unit_types'] = $this->product_model->getUnitTypes();
    $data['main_content'] = 'inventory/products/new_product';
    $this->load->view('inc/template', $data);
  }

  public function submitNewProduct() {
    $product_id = $this->product_model->submitNewProduct();
    if($product_id){
      $this->session->set_flashdata('flashSuccess', 'Successfully added product.');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong!');
    }
    redirect('inventory/product_controller/addProductVariant/'.$product_id);
  }

  public function view_products() {
    $data['categories'] = $this->product_model->getProductCategories();
    // echo "<pre>";print_r($data);die();
    $data['main_content'] = 'inventory/products/view_products';
    $this->load->view('inc/template', $data);
  }

  public function updateCategoryValues() {
    $value = $_POST['value'];
    $ids = explode("-", $_POST['id']);
    $col_name = $ids[0];
    $cat_id = $ids[1];
    if($col_name == 'is_sellable') {
      if($_POST['value'] == 'Yes')
        $value = 1;
      else 
        $value = 0;
    }
    $this->product_model->updateCategoryValues($cat_id, $col_name, $value);
    echo $_POST['value'];
  }

  public function getCategoryWiseProducts() {
    $category_id = $_POST['category_id'];
    $products = $this->product_model->getProductsCategoryWise($category_id);
    $productArray = array();
    if(!empty($products)) {
      foreach ($products as $key => $product) {
        if(!array_key_exists($product->id, $productArray)) {
          $productArray[$product->id] = $product;
          $productArray[$product->id]->attributes = json_decode($product->attributes);
          $productArray[$product->id]->variants = array();
        }
        if($product->name != null)
          array_push($productArray[$product->id]->variants, $product->name);
      }
    }
    echo json_encode($productArray);
  }

  public function add_product_category() {    
    $receipt_book = $this->sales_model->get_sales_receipt_books();
    $data['recept_format'] = $this->fee_library->receipt_format_creation($receipt_book);
    $data['main_content'] = 'inventory/products/product_categories';
    $this->load->view('inc/template', $data);
  }

  public function new_product_category() {
    $category_name = $_POST['category_name'];
    $is_sellable = $_POST['is_sellable'];
    echo $this->product_model->submitProductCategory($category_name, $is_sellable);
  }

  public function getProductCategories() {
    $categories = $this->product_model->getProductCategories();
    $receipt_book = $this->sales_model->get_sales_receipt_books();
    $recept_format = $this->fee_library->receipt_format_creation($receipt_book);

    foreach ($categories as $key => &$val) {
      foreach ($recept_format as $key => $receipt) {
        if ($val->receipt_book_id == $receipt->id) {
          $val->receipt_format = $receipt->receipt;
        }
      }
    }

    echo json_encode($categories);
  }

  public function addProductInfo(){
    $data['brandList']=$this->product_model->get_brand_list();
    $data['colorList']=$this->product_model->get_color_list();
    $data['sizeList']=$this->product_model->get_size_list();
    $data['main_content'] = 'inventory/products/add/addProduct';
    $this->load->view('inc/template', $data);
  }

  //image upload
  public function s3FileUpload($file) {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'profile');
  }

  //submit product details
  public function submitProduct(){

    $product_code = $this->product_model->get_product_code();
    
      $input = $this->input->post();

    $this->db->trans_begin();

    if(isset($_FILES['product_photo'])){
      // $status =(int) $this->product_model->addProductInfo($this->s3FileUpload($_FILES['product_photo']), $product_code, $data);
      $status =(int) $this->product_model->addProductInfo($product_code, $this->s3FileUpload($_FILES['product_photo']));
    }
    else {
      // $status =(int) $this->product_model->addProductInfo(null, $product_code, $data,$attr);
      $status =(int) $this->product_model->addProductInfo($product_code);
    }

    if($status != 0){
      $this->db->trans_commit();
      $this->session->set_flashdata('flashSuccess', 'Successfully inserted product data.');
    } else {
      $this->db->trans_rollback();
      $this->session->set_flashdata('flashError', 'Something went wrong!');
    }
    redirect('inventory/product_controller');
  }

  public function addProductVariant($product_id){
    $data['product_id'] = $product_id;
    $data['productData'] = $this->product_model->getProductAttr($data['product_id']);
    $data['productData']->attributes = json_decode($data['productData']->attributes);
    $data['variants'] = $this->product_model->getVariants($data['product_id']);
      // echo "<pre>"; print_r($data['variants']); die();
    foreach($data['variants'] as $variant){
      $variant->attributes = json_decode($variant->attributes);
      $variant->sales_id = $this->product_model->check_map_product_sales_tx_id($variant->id);
    }
    $data['main_content'] = 'inventory/products/variants';
    $this->load->view('inc/template', $data); 
    //echo "<pre>"; print_r($data['product_id']); die();
  }

  public function submitProductVariant(){
    $type=$_POST['type'];
    $product_id = $this->input->post('product_id');
    if($type=='update'){
      $status=$this->product_model->updateProductVarient();
      if($status){
        $this->session->set_flashdata('flashSuccess', 'Variant Updated Successfully.');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong!');
      }
      redirect('inventory/product_controller/addProductVariant/'.$product_id);
    }
    else{
      $status = $this->product_model->newProductVarient();
      if($status){
        $this->session->set_flashdata('flashSuccess', 'New Variant added Successfully.');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong!');
      }
      redirect('inventory/product_controller/addProductVariant/'.$product_id);
    }
  }

  public function allocate_to_staff() {
    $data['staffList'] = $this->product_model->getStaffList();
    // echo "<pre>"; print_r($data); die();
    $data['categories'] = $this->product_model->getProductCategories();
    $data['purpose'] = $this->product_model->get_purpose_distinct();
    $data['main_content'] = 'inventory/products/allocate_to_staff';
    $this->load->view('inc/template', $data);
  }

  public function getProductNames() {
    $category_id = $_POST['category_id'];
    $products = $this->product_model->getProductNames($category_id);
    echo json_encode($products);
  }

  public function getVariantNames() {
    $product_id = $_POST['product_id'];
    $variants = $this->product_model->getVariantNames($product_id);
    echo json_encode($variants);
  }

  public function allocate_products() {
    $status = $this->product_model->allocate_products();
    if($status){
      $this->session->set_flashdata('flashSuccess', 'Items allocated successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong!');
    }
    redirect('inventory/product_controller/allocate_to_staff');
  }

  public function inventory_allocation_report() {
    $staffWise = $this->input->post('staffWise');
    if (!empty($staffWise)) {
      $selectStaff = $staffWise;
    }else{
      $selectStaff = '';
    }
    $data['allocations'] = $this->product_model->getInventoryAllocations($staffWise);
    $data['staffName'] = $this->product_model->get_Allocate_staff_names();
    $data['selectStaff'] = $selectStaff;
    $data['main_content'] = 'inventory/products/allocation_report';
    $this->load->view('inc/template', $data);
  }

  public function inventoty_transaction_report() {
    $data['categories'] = $this->product_model->getProductCategories();
    $data['main_content'] = 'inventory/products/inventory_transaction_report';
    $this->load->view('inc/template', $data);
  }

  public function getTransactionReport() {
    $category_id = $_POST['category_id'];
    $product_id = $_POST['product_id'];
    $variant_id = $_POST['variant_id'];
    $data = $this->product_model->getTransactionReport($category_id, $product_id, $variant_id);
    echo json_encode($data);
  }

  public function update_receipt_bookbyId(){
    $catId = $this->input->post('cate_id');
    $result = $this->product_model->update_receipt_book_in_category($catId);
    if($result){
      $this->session->set_flashdata('flashSuccess', 'Receipt book successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong!');
    }
    redirect('inventory/product_controller/add_product_category');
  }

  public function update_receipts_template(){
    $catId = $this->input->post('cate_id');
    $result = $this->product_model->update_receipt_html_template_in_category($catId);
    if($result){
      $this->session->set_flashdata('flashSuccess', 'Receipt book successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong!');
    }
    redirect('inventory/product_controller/add_product_category');
  }

  public function delete_variant(){
    $result=$this->product_model->delete_variant();
    echo $result;    
  }
  public function getvariantdetails_edit(){
    $result=$this->product_model->getvariantdetails_edit();
    // echo "<pre>";print_r($result);die();
    echo json_encode($result);
  }

  public function delete_product(){
    $result=$this->product_model->delete_product();
    if($result){
      $this->session->set_flashdata('flashSuccess', 'Deleted successfully');
    }
    else {
      $this->session->set_flashdata('flashError', 'Something went wrong!');
    }
    echo $result;
  }
}