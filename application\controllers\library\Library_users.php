<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Library_users extends CI_Controller {

	public function __construct() {		
    parent::__construct();
    
    $this->load->model('library_users_model' ,'usermodel');
	}

  public function index(){
      $data['title']="Library";
      $data['main_content'] = 'library_url/library_search_form';       
    $this->load->view('enquiry/inc/template', $data);
  }

  function load_books_page(){
    // $data['books'] = $this->usermodel->get_all_book_details();
    $data['categoryCounts'] = $this->usermodel->get_category_count();
    $data['publisherCounts'] = $this->usermodel->get_publishers_count();
    $data['authorCounts'] = $this->usermodel->get_authors_count();
    $this->load->view('library_url/books_search_page', $data);
  
}
public function load_more_books($page = 1) {
  $limit = 50;
  $offset = ($page - 1) * $limit;
  $books = $this->usermodel->get_books_by_page($limit, $offset);
  echo json_encode($books);
}


  public function get_books_filter_wise(){
    $type=$this->input->post("type");
    $name=$this->input->post("name");
    $filtered_books = $this->usermodel->get_books_filter_wise($type,$name);
    echo json_encode($filtered_books);
  }

  public function get_books_search_wise(){
    $query=$this->input->post("query");
    $filtered_books = $this->usermodel->get_books_search_wise($query);
    echo json_encode($filtered_books);

  }

  public function checkIn_Out(){
    $data['checkedDetails'] = $this->usermodel->get_lib_checked_details();
    // echo "<pre>"; print_r($data['checkedDetails']); die();
    $this->load->view('library_url/checkin_out_page',$data);
  }
   
  public function getCheckInDetails(){
    $rfid = $this->input->post("rfid");
    $getdetailsstff_stud = $this->usermodel->getcheckedindetails($rfid);
    // echo "<pre>"; print_r($getdetailsstff_stud); die();
    echo json_encode($getdetailsstff_stud);
  }

  public function getrfiddetails(){
    $rfid = $this->input->post("rfid");
    $get_rfid_details = $this->usermodel->getrfiddetails($rfid);
    echo json_encode($get_rfid_details);
  }
 
}