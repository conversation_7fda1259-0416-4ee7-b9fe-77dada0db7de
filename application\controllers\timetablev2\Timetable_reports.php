<?php

class Timetable_reports extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('timetablev2/timetable_reports_model','timetable_reports_model');  
    $this->load->model('timetable/staff_tt');
    $this->load->model('timetablev2/template_model','template_model');
  }

  public function staff_workload_report() {
    if (!$this->authorization->isAuthorized('TIMETABLE.STAFF_WORKLOAD_REPORT')) {
      redirect('dashboard', 'refresh');
    }

    $active_templates = $this->timetable_reports_model->get_active_template_details();

    if(count($active_templates)>0){
      $data['is_template_active']=1;
      for($i=0;$i<count($active_templates);$i++){
        $names[$i] = $active_templates[$i]->name;
      }
      $template_names = implode(",",$names);
      $data['active_templates']=$active_templates;
      $data['template_names']=$template_names;
      //echo '<pre>';print_r($template_names);
    }
    else{
      $data['is_template_active']=0;
    }
  
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'timetablev2/reports/staff_workload_report_tablet';
      $this->load->view('inc/template', $data);
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'timetablev2/reports/staff_workload_report_mobile';
      $this->load->view('inc/template', $data);
    }else {
      $data['main_content'] = 'timetablev2/reports/staff_workload_report';
      $this->load->view('inc_v2/template', $data);
    }    
    
  }
  public function get_staff_workload(){
    $templateId = $_POST['templateId'];
    $non_teaching_staff = $_POST['non_teaching_staff'];
    $data['staff_workload'] = $this->timetable_reports_model->get_staff_workload($templateId,$non_teaching_staff);
    //echo '<pre>';print_r($data['staff_workload']);
    echo json_encode($data);
  }

  public function display_day_section_timetables () {
    if (!$this->authorization->isAuthorized('TIMETABLE.SECTION_TIMETABLE_REPORT')) {
      redirect('dashboard', 'refresh');
    }
    $active_templates = $this->timetable_reports_model->get_active_template_details();

    if(count($active_templates)>0){
      $data['is_template_active']=1;
      for($i=0;$i<count($active_templates);$i++){
        $names[$i] = $active_templates[$i]->name;
      }
      $template_names = implode(",",$names);
      $data['active_templates']=$active_templates;
      // $data['template_names']=$template_names;
    }
    else{
      $data['is_template_active']=0;
    }
    
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'timetablev2/reports/day_section_tt_tablet';
      $this->load->view('inc/template', $data);
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'timetablev2/reports/day_section_tt_mobile';
      $this->load->view('inc/template', $data);
    }else {
      $data['main_content'] = 'timetablev2/reports/day_section_tt';
      $this->load->view('inc_v2/template', $data);
    }
  }

  public function display_day_staff_timetables () {
    if (!$this->authorization->isAuthorized('TIMETABLE.SECTION_TIMETABLE_REPORT')) {
      redirect('dashboard', 'refresh');
    }
    $active_templates = $this->timetable_reports_model->get_active_template_details();

    if(count($active_templates)>0){
      $data['is_template_active']=1;
      for($i=0;$i<count($active_templates);$i++){
        $names[$i] = $active_templates[$i]->name;
      }
      $template_names = implode(",",$names);
      $data['active_templates']=$active_templates;
      $data['template_names']=$template_names;
      //echo '<pre>';print_r($active_templates);
    }
    else{
      $data['is_template_active']=0;
    }
    
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'timetablev2/reports/day_staff_tt_tablet';
      $this->load->view('inc/template', $data);
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'timetablev2/reports/day_staff_tt_mobile';
      $this->load->view('inc/template', $data);
    }else {
      $data['main_content']    = 'timetablev2/reports/day_staff_tt';
      $this->load->view('inc_v2/template', $data);
    }

  }

  public function fetch_section_names(){
    $templateId = $_POST['templateId'];
    $data['section_list'] = $this->timetable_reports_model->fetch_section_names($templateId);
    echo json_encode($data);
  }

  public function getSectionTTWithSubs() {
    $csIdList = $_POST['csIdList'];
    $subDate = $_POST['subDate'];
    $weekDay = $_POST['weekDay'];
    $template_id = $_POST['template_id'];
    $date = $_POST['subDate'];

    $rDate = date('Y-m-d',strtotime($subDate));

    $data['template_obj'] = $this->template_model->get_template_by_id($template_id);

    $ttArr = array ();
    foreach ($csIdList as $csId) {
      $temp = new stdClass();
      $temp->csId = $csId;
      $temp->csName = $this->timetable_reports_model->getSectionName($csId);
      $temp->section_oc_links = $this->timetable_reports_model->get_section_oc_links($csId);

      
      $tta =  $this->timetable_reports_model->getSectionTTByWeekDay($csId, $weekDay, $rDate, $template_id, $date);

      if($tta){
        $temp->tt_exists = 1;
        $temp->tta =$tta;
      }
      else{
        $temp->tt_exists = 0;
      }
      $ttArr[] = $temp;
    }

    $data['TT'] = $ttArr;

    echo json_encode($data);
  }

  public function printSectionTimetables() {
    if (!$this->authorization->isAuthorized('TIMETABLE.SECTION_TIMETABLE_REPORT')) {
      redirect('dashboard', 'refresh');
    }
    $data['active_templates'] = $this->timetable_reports_model->get_active_template_details();

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'timetablev2/reports/print_section_tt_tablet';
      $this->load->view('inc/template', $data);
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'timetablev2/reports/print_section_tt_mobile';
      $this->load->view('inc/template', $data);
    }else {
      $data['main_content']    = 'timetablev2/reports/print_section_tt';
      $this->load->view('inc_v2/template', $data);
    }
  }

  public function getTTForClassSectionWithHeader() {
    if (!$this->authorization->isAuthorized('TIMETABLE.SECTION_TIMETABLE_REPORT')) {
      redirect('dashboard', 'refresh');
    }

    $csId = $_POST['csId'];
    $template_id = $_POST['template_id'];
    //echo '<pre>';print_r($csId);die();

    $data['csName'] = $this->timetable_reports_model->getSectionName($csId);
    $tta = $this->timetable_reports_model->getSectionTTByWeek($csId,$template_id);
    if($tta){
      $data['tt_exists']=1;
      $data['tta'] = $tta;
    }
    else{
      $data['tt_exists']=0;
    }
    
    //echo '<pre>';print_r($data['tta']);die();

    echo json_encode($data);
  }

  public function getClassTeacherId() {
    $csId = $_POST['csId'];
    //echo '<pre>';print_r($csId);die();
    $classTeacherId = $this->timetable_reports_model->getStaffId($csId);
    //$data = $this->timetable_reports_model->getFullTTForStaff($classTeacherId);
    //$data['csName'] = $this->timetable_reports_model->getSectionName($csId);
    //$data['tta'] = $this->timetable_reports_model->getSectionTTByWeek($csId);
    //echo '<pre>';print_r($classTeacherId);die();
    if($classTeacherId){
      $data['class_teacher_exists']=1;
      $data['classTeacherId'] = $classTeacherId;
    }
    else{
      $data['class_teacher_exists']=0;
    }

    echo json_encode($data);
  }

  public function getScholasticStaff(){
    if (!$this->authorization->isAuthorized('TIMETABLE.STAFF_TIMETABLE_REPORT')) {
      redirect('dashboard', 'refresh');
    }
    $csIds = $_POST['csIds'];
    $template_id = $_POST['template_id'];
    //echo '<pre>';print_r($csIds);die();
    $staffIds = $this->timetable_reports_model->getStaffIds($csIds,$template_id);
    // echo '<pre>';print_r($staffIds);die();
    $data['staffIds'] = $staffIds;

    echo json_encode($data);
  }

  public function printStaffTimetables(){
    if (!$this->authorization->isAuthorized('TIMETABLE.STAFF_TIMETABLE_REPORT')) {
      redirect('dashboard', 'refresh');
    }

    $active_templates = $this->timetable_reports_model->get_active_template_details();

    $selectedStaffId = $this->input->post('selectedStaffId');

    if(count($active_templates)>0){
      $data['is_template_active']=1;
      for($i=0;$i<count($active_templates);$i++){
        $names[$i] = $active_templates[$i]->name;
      }
      $template_names = implode(",",$names);
      $data['active_templates']=$active_templates;
      $data['template_names']=$template_names;
      //echo '<pre>';print_r($template_names);
      $data['staffAll'] = $this->timetable_reports_model->get_allStaff();
      $data['selectedStaffIds'] = $selectedStaffId;
    }
    else{
      $data['is_template_active']=0;
    }

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'timetablev2/reports/print_staff_tt_tablet';
      $this->load->view('inc/template', $data);
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'timetablev2/reports/print_staff_tt_mobile';
      $this->load->view('inc/template', $data);
    }else {
      $data['main_content'] = 'timetablev2/reports/print_staff_tt';
      $this->load->view('inc_v2/template', $data);
    }
  }

  public function getFullTTForStaff(){
    if (!$this->authorization->isAuthorized('TIMETABLE.STAFF_TIMETABLE_REPORT')) {
      redirect('dashboard', 'refresh');
    }
    $staffId = $this->input->post('staffId');
    $template_id = $this->input->post('template_id');
    //echo '<pre>';print_r($staffId);die();
    $data = $this->timetable_reports_model->getFullTTForStaff($staffId,$template_id);
    //echo '<pre>';print_r($data);die();
    echo json_encode($data);
   }

   public function displayAllStaffIndex(){
    if (!$this->authorization->isAuthorized('TIMETABLE.STAFF_TIMETABLE_REPORT')) {
      redirect('dashboard', 'refresh');
    }

    $selectedStaffId = $this->input->post('selectedStaffId');
    $data['selectedStaffIds'] = $selectedStaffId;
    // $data['staffAll'] = $this->timetable_reports_model->get_allStaff();
    $data['main_content'] = 'timetablev2/reports/day_staff_tt';
    $this->load->view('inc/template', $data);
  }

  public function fetch_staff_names(){
    $templateId = $_POST['templateId'];
    $data['staff_list'] = $this->timetable_reports_model->get_staff_by_template($templateId);
    //echo '<pre>';print_r($data['staff_list']);die();
    echo json_encode($data);
  }

   public function getDayTTForStaff() {
    $staffIdList = $this->input->post('staffIdList');
    $template_id = $this->input->post('template_id');
    $weekDay = $this->input->post('weekDay');
    $date = $this->input->post('date');
    // echo '<pre>';print_r($date);die();

    $data['tt_metadata'] = $this->timetable_reports_model->get_template_by_id($template_id);

    $ttArr = array ();
    foreach ($staffIdList as $staffId) {
      $temp = new stdClass();
      
      $tta = $this->timetable_reports_model->get_staff_template_periods_subjects($template_id,$staffId,$weekDay, $date);
      $temp->staff_obj = $this->timetable_reports_model->get_staff_oc_data($staffId);
      //echo '<pre>';print_r($temp->staff_obj);die();
      if($tta){
        $temp->tt_exists = 1;
        $temp->tta =$tta;
      }
      else{
        $temp->tt_exists = 0;
      }
      $ttArr[] = $temp;
    
    }
    $data['staffTT'] = $ttArr;
    echo json_encode($data);
  }

}