<?php

  defined('BASEPATH') OR exit('No direct script access allowed');

  class Schedule_controller extends CI_Controller {

    private $yearId;

    public function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isAuthorized('ONLINE_CLASS.MODULE')) {
        redirect('dashboard', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      // date_default_timezone_set('Asia/Kolkata');
      $this->load->model('online_class/Schedule_model', 'schedule');
      $this->load->model('class_section');
      $this->load->model('student/Student_Model');
      $this->load->library('filemanager');
    }

    public function index(){
      $data['schedules_of'] = isset($_POST['schedule_type'])?$_POST['schedule_type']:'todays';
      $data['schedule_id'] = isset($_POST['schedule_id'])?$_POST['schedule_id']:0;
      // echo "<pre>"; print_r($data); die();
      $data['is_admin'] = $this->authorization->isAuthorized('ONLINE_CLASS.ADMIN');
      $data['recording_enabled'] = $this->settings->getSetting('online_recording');
      $data['allow_quiz'] = 0;
      if($this->settings->getSetting('online_class_quiz')) {
        $data['allow_quiz'] = 1;
      }
      if(empty($data['recording_enabled'])){
        $data['recording_enabled'] = 0;
      }
      $data['sms_enabled'] = $this->settings->getSetting('online_attendance_SMS');
      if(empty($data['sms_enabled'])){
        $data['sms_enabled'] = 0;
      }
      $data['create_schedule'] = $this->authorization->isAuthorized('ONLINE_CLASS.CREATE_SCHEDULE');
      $data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
      $data['staff'] = $this->schedule->getAllStaff();
      $data['rooms'] = $this->schedule->getClassRooms();
      $data['grades'] = $this->class_section->getAllClassess();
      // $data['aws'] = $this->get_chunk_upload_secret();
      $this->load->library('aws_library');
		  $data['aws'] = $this->aws_library->getSignatureData('video/*');
      // echo '<pre>'; print_r($data['aws']); die();
      if ($this->mobile_detect->isMobile()) {
        $data['is_mobile'] = 1;
        $data['main_content'] = 'online_class/schedule/mobile_index.php';
      } else {
        $data['is_mobile'] = 0;
        $data['main_content']    = 'online_class/schedule/index.php';
      }
      $this->load->view('inc/template', $data);  
    }

    private function get_chunk_upload_secret() {
      $this->config->load('s3');
      $bucket = $this->config->item('s3_bucket');
      $accessKeyId = $this->config->item('access_key');
      $this->load->library('aws_library');
      $general = $this->aws_library->getPolicyAndSignature('video/*');
      return array('access' => $accessKeyId, 'signature' => $general['signature'], 'policy' => $general['policy'], 'bucket' => $bucket, 'subdomain' => CONFIG_ENV['main_folder'], 'short_date' => $general['short_date'], 'iso_date' => $general['iso_date'], 'region' => $general['region']);



      // prepare policy
      $policy = base64_encode(json_encode(array(
        // ISO 8601 - date('c'); generates uncompatible date, so better do it manually
        'expiration' => date('Y-m-d\TH:i:s.000\Z', strtotime('+1 day')),  
        'conditions' => array(
          array('bucket' => $bucket),
          array('acl' => 'public-read'),
          array('starts-with', '$key', ''),
          // for demo purposes we are accepting only images
          array('starts-with', '$Content-Type', 'video/*'),
          // Plupload internally adds name field, so we need to mention it here
          array('starts-with', '$name', ''),  
          array('success_action_status' => '201'),
          array('starts-with', '$chunk', ''),
          array('starts-with', '$chunks', ''),
          array('starts-with', '$Filename', ''),
        )
      )));

      // sign policy
      $signature = base64_encode(hash_hmac('sha1', $policy, $secret, true));
      return array('access' => $accessKeyId, 'signature' => $signature, 'policy' => $policy, 'bucket' => $bucket, 'subdomain' => CONFIG_ENV['main_folder']);
    }

    public function save_file_location() {
      $schedule_id = $_POST['schedule_id'];
      $location = $_POST['location'];
      $prefix = $this->filemanager->getFilePath('');
      $loc = str_replace($prefix, "", $location);
      echo $this->schedule->save_location($schedule_id, $loc);
    }

    /*public function initiate_upload() {
      $this->load->library('multifilemanager');
      $schedule_name = str_replace(" ", "_", $_POST['schedule_name']);
      $schedule_id = $_POST['schedule_id'];
      $file_name = $_POST['file_name'];
      $files = explode(".", $file_name);
      $ext = $files[count($files)-1];
      $keyname = $schedule_name.'_'.$schedule_id.'.'.$ext;
      // $keyname = CONFIG_ENV['main_folder'].'/recordings/'.$schedule_name.'_'.$schedule_id.'.'.$ext;
      $data = $this->multifilemanager->initiate_chunk_upload($keyname);
      echo json_encode($data);
    }

    public function upload_files_in_chunk() {
      $this->load->library('multifilemanager');
      // echo '<pre>'; print_r($_POST);
      // echo '<pre>'; print_r($_FILES);
      $file_content = file_get_contents($_FILES['file']['tmp_name']);
      $data = array(
        'uploadId' => $_POST['uploadId'],
        'partNumber' => $_POST['chunk'] + 1,
        'keyname' => $_POST['key'],
        'file_content' => $file_content
      );
      $data = $this->multifilemanager->upload_part($data);
      echo json_encode($data);
    }

    public function complete_upload() {
      $this->load->library('multifilemanager');
      $schedule_id = $_POST['schedule_id'];
      // echo "<pre>"; print_r($_POST); die();
      $data = array(
        'uploadId' => $_POST['uploadId'],
        'keyname' => $_POST['key'],
        'parts' => $_POST['etags']
      );
      $result = $this->multifilemanager->complete_upload($data);
      if($result) {
        echo $this->db->where('id', $schedule_id)->update('online_schedule', ['recording' => $result['key']]);
      } else {
        echo 0;
      }
    }*/

    public function add_schedule() {
      $data['is_admin'] = $this->authorization->isAuthorized('ONLINE_CLASS.ADMIN');
      $data['staff'] = $this->schedule->getAllStaff();
      $data['current_staff'] = $this->authorization->getAvatarStakeHolderId();
      $data['rooms'] = $this->schedule->getClassRooms();
      // $data['all_slots'] = $this->schedule->getAllSlots();
      if($this->mobile_detect->isMobile()) {
        $data['main_content'] = 'online_class/schedule/new_schedule_mobile';
      } else {
        $data['main_content'] = 'online_class/schedule/new_schedule';
      }

      $this->load->view('inc/template', $data);
    }

    public function getOccupiedHosts() {
      $slot_id = $_POST['slot_id'];
      $date = $_POST['date'];
      $schedule_ids = $this->schedule->getSchedulesWithSlot($slot_id, $date);
      $data = $this->schedule->getOccupiedParticipants($schedule_ids, 'staff');
      echo json_encode($data);
    }

    public function edit_schedule($schedule_id) {
      $data['is_admin'] = $this->authorization->isAuthorized('ONLINE_CLASS.ADMIN');
      $data['schedule'] = $this->schedule->getSchedule($schedule_id);
      $date = date('Y-m-d', strtotime($data['schedule']->start_time));
      $classroom_id = $data['schedule']->classroom_id;
      $data['selected_slots'] = $this->schedule->getSelectedSlots($schedule_id);
      $schedule_ids = $this->schedule->getSchedulesWithTimeRange($data['schedule']->start_time, $data['schedule']->end_time);
      $data['occupied_staff'] = $this->schedule->getOccupiedParticipants($schedule_ids, 'staff');
      $data['staff'] = $this->schedule->getAllStaff();
      $data['current_staff'] = $data['schedule']->created_by;
      $data['rooms'] = $this->schedule->getClassRooms();
      // $data['all_slots'] = $this->schedule->getAllSlots();
      $data['all_slots'] = $this->schedule->getAllSlotsInRoom($classroom_id);
      $occupied_slots = $this->schedule->getOccupiedSlots($date, $classroom_id);
      $data['occupied_slots'] = [];
      foreach ($occupied_slots as $key => $val) {
        $data['occupied_slots'][] = $val->id;
      }
      // echo "<pre>"; print_r($data); die();
      if($this->mobile_detect->isMobile()) {
        $data['main_content'] = 'online_class/schedule/edit_schedule_mobile';
      } else {
        $data['main_content'] = 'online_class/schedule/edit_schedule';
      }
      $this->load->view('inc/template', $data);
    }

    public function delete_slot() {
      $slot_id = $_POST['slot_id'];
      $status = $this->schedule->delete_slot($slot_id);
      echo $status;
    }

    public function save_schedule() {
      $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $slot_id = $input['slots'];
      $slot_ids = [$slot_id];
      $host_busy = $this->schedule->checkIsHostBusy($input['host_id'], $slot_ids, $input['schedule_date']);
      if($host_busy) {
        $this->session->set_flashdata('flashError', 'Staff already have class on selected slot');
        redirect('online_class/schedule_controller/add_schedule');
      } else {
        $slots = $this->schedule->getSlotDataByIds($slot_ids);
        if(empty($slots)) {
          $this->session->set_flashdata('flashError', 'Something went wrong!');
          redirect('online_class/schedule_controller/add_schedule');
        }
        $start = $input['schedule_date'].' '.$slots[0]->start_time;
        $end = $input['schedule_date'].' '.$slots[count($slots) - 1]->end_time;
        $start_time = date('Y-m-d H:i:s', strtotime($start));
        $end_time = date('Y-m-d H:i:s', strtotime($end));
        // echo "<pre>"; print_r($input);die();
        $data = array(
          'name' => $input['name'],
          'description' => $input['description'],
          'classroom_id' => $input['schedule_room'],
          'start_time' => $start_time,
          'end_time' => $end_time,
          'acad_year_id' => $this->yearId,
          'created_by' => $input['host_id']
        );

        $slot_data = [];
        foreach ($slot_ids as $slot_id) {
          $slot_data[] = array(
            'classroom_id' => $input['schedule_room'],
            'slot_id' => $slot_id,
            'date' => date('Y-m-d', strtotime($input['schedule_date'])),
            'status' => 1
          );
        }
        $status = $this->schedule->save_schedule($data, $slot_data);
        // echo "<pre>"; print_r($data); die();
        if($status) {
          $this->load->helper('texting_helper');

          //Send notification to assigned staff
          $message = "New online class schedule, Class:".$input['name'].", On ".$input['schedule_date']." from ".local_time($start, 'h:i a')." to ".local_time($end, 'h:i a');
          $input_arr = array();
          $input_arr['staff_ids'] = $input['host_id'];
          $input_arr['mode'] = 'notification';
          $input_arr['source'] = 'Online Class';
          $input_arr['message'] = $message;
          $input_arr['staff_url'] = site_url('online_class/schedule_controller');
          $response = sendText($input_arr);
          $this->session->set_flashdata('flashSuccess', 'Successful added schedule');
        } else {
          $this->session->set_flashdata('flashError', 'Something went wrong');
        }
        redirect('online_class/schedule_controller');
      }
    }

    public function update_schedule() {
      $input = $this->input->post();
      $schedule_id = $input['schedule_id'];
      $slot_id = $input['slots'];
      $slot_ids = [$slot_id];
      $host_busy = $this->schedule->checkIsHostBusy($input['host_id'], $slot_ids, $input['schedule_date'], $schedule_id);
      if($host_busy) {
        $this->session->set_flashdata('flashError', 'Staff already have class on selected slot');
        redirect('online_class/schedule_controller/edit_schedule/'.$schedule_id);
      }
      $slots = $this->schedule->getSlotDataByIds($slot_ids);
      $start = $input['schedule_date'].' '.$slots[0]->start_time;
      $end = $input['schedule_date'].' '.$slots[count($slots) - 1]->end_time;
      $start_time = date('Y-m-d H:i:s', strtotime($start));
      $end_time = date('Y-m-d H:i:s', strtotime($end));
      // echo "<pre>"; print_r($input);die();
      $data = array(
        'name' => $input['name'],
        'description' => $input['description'],
        'classroom_id' => $input['schedule_room'],
        'start_time' => $start_time,
        'end_time' => $end_time,
        'acad_year_id' => $this->yearId,
        'created_by' => $input['host_id']
      );

      $slot_data = [];
      foreach ($slot_ids as $slot_id) {
        $slot_data[] = array(
          'schedule_id' => $input['schedule_id'],
          'classroom_id' => $input['schedule_room'],
          'slot_id' => $slot_id,
          'date' => date('Y-m-d', strtotime($input['schedule_date'])),
          'status' => 1
        );
      }
      $status = $this->schedule->update_schedule($data, $slot_data, $schedule_id);
      // echo "<pre>"; print_r($data); die();
      if($status) {
        $this->load->helper('texting_helper');

        //Send notification to assigned staff
        $message = "Update to online class schedule, Class:".$input['name'].", On ".$input['schedule_date']." from ".local_time($start, 'd-M h:i a')." to ".local_time($end, 'd-M h:i a');
        $input_staff = array();
        $input_staff['staff_ids'] = $input['host_id'];
        $input_staff['mode'] = 'notification';
        $input_staff['source'] = 'Online Class';
        $input_staff['message'] = $message;
        $input_staff['staff_url'] = site_url('online_class/schedule_controller');
        $response = sendText($input_staff);//staff notification

        $schedule = $this->schedule->getSchedule($schedule_id);
        $invitees = $this->schedule->getScheduleParticipants($schedule_id, $this->yearId);
        $notify_array = array();
        $notify_array['mode'] = 'notification';
        $notify_array['send_to'] = 'Both';
        $notify_array['source'] = 'Online Class';
        $notify_array['student_url'] = site_url('online_class/student_join_controller');
        $notify_array['staff_url'] = site_url('online_class/schedule_controller');
        $notify_array['message'] = $schedule->name.' class on '.date('d-M h:i a', strtotime($schedule->start_time)).' to '.date('d-M h:i a', strtotime($schedule->end_time)).' is updated';
        if(!empty($invitees['students'])) {
          foreach ($invitees['students'] as $key => $std) {
            $notify_array['student_ids'] = $std->id;
          }
          // sendText($notify_array);
          unset($notify_array['student_ids']);
        }
        if(!empty($invitees['staff'])) {
          foreach ($invitees['staff'] as $key => $std) {
            $notify_array['staff_ids'] = $std->id;
          }
          // unset($notify_array['staff_ids']);
        }
        sendText($notify_array);
        $this->session->set_flashdata('flashSuccess', 'Successfully updated schedule');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('online_class/schedule_controller');
    }

    public function getSchedules() {
      $type = $_POST['type'];
      $host_id = $_POST['host_id'];
      $room_id = $_POST['room_id'];
      $from_date = $_POST['from_date'];
      $end_date = $_POST['end_date'];
      $is_admin = $this->authorization->isAuthorized('ONLINE_CLASS.ADMIN');
      /*$host_id = 0;
      if(!$is_admin) {
        $host_id = $this->authorization->getAvatarStakeHolderId();
      }*/
      $prefix = $this->filemanager->getFilePath('');
      $schedules = $this->schedule->getSchedules($host_id, $type, $room_id, $from_date, $end_date);
      $data = array();
      $now = gmdate('Y-m-d H:i:s');
      foreach ($schedules as $key => $sch) {
        $schedules[$key]->session_status = 'Not Started';
        $time = round((strtotime($sch->start_time) - strtotime($now)) / 60); //time diff in minutes
        if (strtotime($sch->end_time)<strtotime($now)) {
          $schedules[$key]->session_status = 'Completed';
        } else if(($time <= 30) && (strtotime($sch->end_time)>strtotime($now))) {
          $schedules[$key]->session_status = 'Active';
        }
        $sch->now = local_time($now);
        $sch->date = date('d-M Y', strtotime($sch->start_time));
        $sch->start_time = local_time($sch->start_time, 'h:i a');
        $sch->end_time = local_time($sch->end_time, 'h:i a');
        if($sch->recording) {
          // $sch->recording = $this->filemanager->getFilePath($sch->recording);
          $sch->recording = $prefix.''.$sch->recording;
        }

        $sch->paths = array();
        if($sch->files) {
          $paths = json_decode($sch->files);
          foreach ($paths as $path) {
            array_push($sch->paths, array('name' => $path->name, 'path' => $prefix.''.$path->path));
          }
        }
        $data[] = $sch;
      }
      echo json_encode($data);
    }

    public function cancelSchedule() {
      $schedule_id = $_POST['schedule_id'];
      $status = $this->schedule->cancelSchedule($schedule_id);
      if($status) {
        $schedule = $this->schedule->getSchedule($schedule_id);
        $message = $schedule->name.' on '.local_time($schedule->start_time, 'd-M h:i a').' to '.local_time($schedule->end_time, 'd-M h:i a'). ' is cancelled';
        $this->__notify_participants($schedule_id, $message);
      }
      echo $status;
    }

    public function add_participants($schedule_id=0, $schedule_type='') {
      $data['schedule_id'] = $schedule_id;
      if(isset($_POST['schedule_id'])) {
        $data['schedule_id'] = $_POST['schedule_id'];
      }
      $data['schedule_type'] = $schedule_type;
      if(isset($_POST['schedule_type'])) {
        $data['schedule_type'] = $_POST['schedule_type'];
      }
      $data['host'] = $host_id = $this->authorization->getAvatarStakeHolderId();
      $data['schedule'] = $this->schedule->getSchedule($data['schedule_id']);
      $data['room_details'] = $this->schedule->getSingleLicenceDetails($data['schedule']->classroom_id);
      
      $schedule_ids = $this->schedule->getSchedulesWithTimeRange($data['schedule']->start_time, $data['schedule']->end_time);
      $data['allotted_staff'] = $this->schedule->getOccupiedParticipants($schedule_ids, 'staff');
      $data['allotted'] = $this->schedule->getOccupiedParticipants($schedule_ids, 'class_section');

      $data['sections'] = $this->schedule->getAllClassSections();
      // $data['allotted'] = $this->schedule->getAllocatedClassSections($schedule_id);
      $data['staff'] = $this->schedule->getAllStaff();
      // $data['allotted_staff'] = $this->schedule->allottedStaff($schedule_id);
      // echo "<pre>"; print_r($data['allotted_staff']); die();
      $data['participants'] = $this->schedule->getScheduleParticipants($data['schedule_id'], $this->yearId);
      // echo "<pre>";print_r($data);die();
      if($this->mobile_detect->isMobile()) {
        $data['is_mobile'] = 1;
        $data['main_content'] = 'online_class/schedule/add_participants_mobile';
      } else {
        $data['is_mobile'] = 0;
        $data['main_content'] = 'online_class/schedule/add_participants';
      }
      $this->load->view('inc/template', $data);
    }

    public function getOccupiedSlots() {
      $date = date('Y-m-d', strtotime($_POST['date']));
      $classroom_code = $_POST['schedule_room'];
      $occupied = $this->schedule->getOccupiedSlots($date, $classroom_code);
      $all_slots = $this->schedule->getAllSlotsInRoom($classroom_code);
      $slots = [];
      foreach ($occupied as $key => $val) {
        $slots[] = $val->id;
      }
      $data = array(
        'all_slots' => $all_slots,
        'slots' => $slots
      );
      echo json_encode($data);
    }

    public function getSectionStudents() {
      $section_id = $_POST['section_id'];
      $schedule_id = $_POST['schedule_id'];
      $schedule = $this->schedule->getSchedule($schedule_id);
      $schedule_ids = $this->schedule->getSchedulesWithTimeRange($schedule->start_time, $schedule->end_time);
      $data['allotted'] = $this->schedule->getOccupiedParticipants($schedule_ids, 'student');
      // $data['allotted'] = $this->schedule->allottedStudents($schedule_id);
      $data['students'] = $this->schedule->getSectionStudents($section_id, $this->yearId);
      echo json_encode($data);
    }

    public function add_invitees() {
      $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $schedule_id = $input['schedule_id'];
      $type = $input['type'];
      $existing_ids = $this->schedule->getExistingInvitees($schedule_id, $type);
      $sending_ids = [];
      $invitees = [];
      $notify_array = [];
      $max_allowed = $input['max_allowed'];
      $cur_participants = $input['cur_participants'];
      $class_section_id = NULL;
      if($type == 'class_section') {
        $class_section_id = $input['class_section'];
        $students = $this->schedule->getSectionStudents($class_section_id, $this->yearId);
        if((count($students) + $cur_participants) > $max_allowed) {
          $this->session->set_flashdata('flashInfo', 'Selected '.(count($students) + $cur_participants - $max_allowed).' students more than the maximum allowed');
          redirect('online_class/schedule_controller/add_participants/'.$schedule_id.'/'.$input['schedule_type']);
        }
        foreach ($students as $key => $std) {
          if(!in_array($std->id, $existing_ids)) {
            $sending_ids[] = $std->id;
          }
        }
        $notify_array['student_ids'][] = $std->id;
      } else if($type == 'student') {
        $stdIds = $input['students'];
        if((count($stdIds) + $cur_participants) > $max_allowed) {
          $this->session->set_flashdata('flashInfo', 'Selected '.(count($stdIds) + $cur_participants - $max_allowed).' students more than the maximum allowed');
          redirect('online_class/schedule_controller/add_participants/'.$schedule_id.'/'.$input['schedule_type']);
        }
        $class_section_id = $input['std_sections'];
        foreach ($stdIds as $key => $std_id) {
          if(!in_array($std_id, $existing_ids)) {
            $sending_ids[] = $std_id;
          }
        }
        $notify_array['student_ids'] = $sending_ids;
      } else if($type == 'staff') {
        $staff_ids = $input['staff'];
        foreach ($staff_ids as $key => $staff_id) {
          if(!in_array($staff_id, $existing_ids)) {
            $sending_ids[] = $staff_id;
          }
        }
        $notify_array['staff_ids'] = $sending_ids;
      }

      if(!empty($sending_ids)) {
        foreach ($sending_ids as $key => $id) {
          $invitees[] = array(
            'schedule_id' => $schedule_id,
            'type' => $type,
            'stake_holder_id' => $id
          );
        }
        // $this->schedule->updateSectionsData($schedule_id);
        // echo '<pre>'; print_r($invitees); die();
        $status = $this->schedule->add_participants($invitees, $class_section_id, $schedule_id);
        if($status) {
          $this->schedule->updateSectionsData($schedule_id);
          $this->load->helper('texting_helper');
          $schedule = $this->schedule->getSchedule($schedule_id);
          $notify_array['mode'] = 'notification';
          $notify_array['send_to'] = 'Both';
          $notify_array['source'] = 'Online Class';
          $notify_array['student_url'] = site_url('online_class/student_join_controller');
          $notify_array['staff_url'] = site_url('online_class/schedule_controller');
          $notify_array['message'] = $schedule->name.' class has been scheduled on '.local_time($schedule->start_time, 'd-M h:i a').' to '.local_time($schedule->end_time, 'd-M h:i a');
          sendText($notify_array);
          $this->session->set_flashdata('flashSuccess', 'Successful added Invitees');
        } else {
          $this->session->set_flashdata('flashError', 'Something went wrong');
        }
        redirect('online_class/schedule_controller/add_participants/'.$schedule_id.'/'.$input['schedule_type']);
      }
      $this->session->set_flashdata('flashInfo', 'Selected Students Already Exists');
      redirect('online_class/schedule_controller/add_participants/'.$schedule_id.'/'.$input['schedule_type']);
    }

    public function remove_participant() {
      $schedule_id = $_POST['schedule_id'];
      $status = $this->schedule->remove_participant();
      if($status) {
          $this->schedule->updateSectionsData($schedule_id);
          $this->load->helper('texting_helper');
          $schedule_id = $_POST['schedule_id'];
          $stake_holder_id = $_POST['id'];
          $type = $_POST['type'];
          $schedule = $this->schedule->getSchedule($schedule_id);
          $notify_array = [];
          if($type == 'staff') {
            $notify_array['staff_ids'] = [$stake_holder_id];
          } else {
            $notify_array['student_ids'] = [$stake_holder_id];
          }
          $notify_array['mode'] = 'notification';
          $notify_array['send_to'] = 'Both';
          $notify_array['source'] = 'Online Class';
          $notify_array['student_url'] = site_url('online_class/student_join_controller');
          $notify_array['staff_url'] = site_url('online_class/schedule_controller');
          $notify_array['message'] = $schedule->name.' class on '.local_time($schedule->start_time, 'd-M h:i a').' to '.local_time($schedule->end_time, 'd-M h:i a').' is cancelled for you.';
          sendText($notify_array);
          echo 1;
      } else {
        echo 0;
      }
    }

    public function addNewQuestions(){
      $data['classList'] = $this->Student_Model->getClassNames();
      $data['main_content'] = 'online_class/schedule/add_questions';
      $this->load->view('inc/template', $data);
    }

    public function save_new_questions(){
      $data = $this->schedule->save_new_questions();
      echo $data;
    }

    public function getAddedQuestions(){
      $data = $this->schedule->getAddedQuestions();
      echo json_encode($data);
    }

    public function getSingleQuestionDetails(){
      $data = $this->schedule->getSingleQuestionDetails();
      echo json_encode($data);
    }

    public function update_questions(){
      $data = $this->schedule->update_questions();
      echo $data;
    }

    public function deleteSingleQuestion(){
      $data = $this->schedule->deleteSingleQuestion();
      echo json_encode($data);
    }

    public function getSubjects(){
      $data = $this->schedule->getSubjects();
      echo json_encode($data);
    }

    public function getLessons(){
      $data = $this->schedule->getLessons();
      echo json_encode($data);
    }

    public function getSubTopics(){
      $data = $this->schedule->getSubTopics();
      echo json_encode($data);
    }

    public function addNewLesson(){
      $data = $this->schedule->addNewLesson();
      echo json_encode($data);
    }

    public function getLessonandSubTopicDetails(){
      $data = $this->schedule->getLessonandSubTopicDetails();
      echo json_encode($data);
    }
    public function licences() {
      if (!$this->authorization->isAuthorized('ONLINE_CLASS.ADMIN')) {
        redirect('dashboard', 'refresh');
      }
      // $data['licences'] = $this->schedule->getLicences();
      $data['rooms'] = $this->schedule->getRoomSlots();
      $data['is_super_admin'] = $this->authorization->isSuperAdmin();
      // echo "<pre>"; print_r($data); die();
      $data['main_content'] = 'online_class/licences/index';
      $this->load->view('inc/template', $data);
    }

    public function update_slots() {
      $status = $this->schedule->updateSlot();
      if($status == 1) {
        $this->session->set_flashdata('flashSuccess', 'Successfully saved slot');
      } else if($status == 1) {
        $this->session->set_flashdata('flashWarning', 'Slot time clash, unable to add slot');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('online_class/schedule_controller/licences');
    }

    public function clone_slots() {
      // $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $status = $this->schedule->cloneSlots();
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully cloned');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('online_class/schedule_controller/licences');
    }

    public function release_classroom($classroom_id) {
      $status = $this->schedule->releaseClassRoom($classroom_id);
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully released room');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('online_class/schedule_controller/licences');
    }

    public function addNewLicence(){
      $data = $this->schedule->addNewLicence();
      echo json_encode($data);
    }

    public function manageSlots(){
      $data['all_slots'] = $this->schedule->getAllSlots();
      // echo "<pre>";print_r($data['all_slots']);die();
      $data['main_content'] = 'online_class/schedule/manage_slots';
      $this->load->view('inc/template', $data);
    }

    public function addNewSlot(){
      $data = $this->schedule->addNewSlot();
      echo json_encode($data);
    }

    public function getAllAvailableSlots_ajax(){
      $data = $this->schedule->getAllSlots();
      echo json_encode($data);
    }

    public function deleteSingleSlot(){
      $data = $this->schedule->deleteSingleSlot();
      echo json_encode($data);
    }

    public function getSingleSlotDetails(){
      $data = $this->schedule->getSingleSlotDetails();
      echo json_encode($data);
    }

    public function editSingleSlot(){
      $data = $this->schedule->editSingleSlot();
      echo json_encode($data);
    }

    public function addQuestionsToSchedule($schedule_id){
      $data['schedule_id'] = $schedule_id;
      // $data['sections'] = $this->schedule->getAvailableClassSections($schedule_id);
      // $data['staff'] = $this->schedule->getAllStaff();
      $data['schedule'] = $this->schedule->getSchedule($schedule_id);
      // $data['participants'] = $this->schedule->getScheduleParticipants($schedule_id, $this->yearId);
      $data['classList'] = $this->Student_Model->getClassNames();
      $data['scheduled_questions'] = $this->schedule->getSheduledQuestions($schedule_id);
      $data['main_content'] = 'online_class/schedule/questions_to_schedule';
      $this->load->view('inc/template', $data);
    }

    public function getQuestions(){
      $data = $this->schedule->getQuestions();
      echo json_encode($data);
    }

    public function insertQuestionsToSchedule(){
      $data = $this->schedule->insertQuestionsToSchedule();
      echo json_encode($data);
    }

    public function removeScheduledQuestion(){
      $data = $this->schedule->removeScheduledQuestion();
      echo json_encode($data);
    }

    public function getSingleLicenceDetails(){
      $room_id = $_POST['id'];
      $data = $this->schedule->getSingleLicenceDetails($room_id);
      echo json_encode($data);
    }

    public function update_licence_configuration(){
      $data = $this->schedule->update_licence_configuration();
      echo json_encode($data);
    }

    public function s3FileUpload($file, $folder) {
      if ($file['tmp_name'] == '' || $file['name'] == '') {
        return ['status' => 'empty', 'file_name' => ''];
      }
      $path = $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder);
      return $path;
    }

    public function saveRecording() {
      $path = $this->s3FileUpload($_FILES['recording'], 'recordings');
      if($path['file_name'] != '') {
        $schedule_id = $_POST['schedule_id'];
        $status = $this->schedule->save_recording($schedule_id, $path['file_name']);
        echo $status;
      } else {
        echo 0;
      }
    }

    public function update_student_permissions(){
      $data = $this->schedule->update_student_permissions();
      echo json_encode($data);
    }

    public function saveFiles() {
      $path = $this->s3FileUpload($_FILES['file'], 'class_files');
      if($path['file_name'] != '') {
        $file_name = $_FILES['file']['name'];
        $schedule_id = $_POST['schedule_id'];
        $status = $this->schedule->save_file($schedule_id, $file_name, $path['file_name']);
        echo $status;
      } else {
        echo 0;
      }
    }

    public function downloadFile() {
      $file_path = $_POST['file_path'];
      $file_name = $_POST['file_name'];
      $data = file_get_contents($file_path);
      $this->load->helper('download');
      force_download($file_name, $data, TRUE);
    }

    public function sendNotificationsConfirmation(){
      // $data['students_send'] = $this->schedule->getNotificationsSentStudents($this->yearId);
      $data['students_notsend'] = $this->schedule->getNotificationsNotSentStudents($this->yearId);
      // $data['staff_send'] = $this->schedule->getNotificationsSentStaff($this->yearId);
      $data['staff_notsend'] = $this->schedule->getNotificationsNotSentStaff($this->yearId);
      echo json_encode($data);
    }

    public function sendNotifications(){
      $schedule_id = $_POST['schedule_id'];
      $schedule = $this->schedule->getSchedule($schedule_id);
      /*$students_notsend = $this->schedule->getNotificationsNotSentStudents($this->yearId);
      $staff_notsend = $this->schedule->getNotificationsNotSentStaff($this->yearId);
      foreach ($students_notsend as $key => $value) {
        $sending_ids[] = $value->stu_id;
      }
      $notify_array['student_ids'] = $sending_ids;
      foreach ($staff_notsend as $key => $value) {
        $sending_ids1[] = $value->staff_id;
      }
      $notify_array['staff_ids'] = $sending_ids1;
      $this->load->helper('texting_helper');
      $schedule = $this->schedule->getSchedule($schedule_id);
      $notify_array['mode'] = 'notification';
      $notify_array['send_to'] = 'Both';
      $notify_array['source'] = 'Online Class';
      $notify_array['student_url'] = site_url('online_class/student_join_controller');
      $notify_array['staff_url'] = site_url('online_class/schedule_controller');
      $notify_array['message'] = $schedule->name.' class has been scheduled on '.local_time($schedule->start_time, 'd-M h:i a').' to '.local_time($schedule->end_time, 'd-M h:i a');
      sendText($notify_array);*/
      $message = $schedule->name.' class has been scheduled on '.local_time($schedule->start_time, 'd-M h:i a').' to '.local_time($schedule->end_time, 'd-M h:i a');
      $this->__notify_participants($schedule_id, $message);
      echo 1;
    }

    public function __notify_participants($schedule_id, $message) {
      $students_notsend = $this->schedule->getNotificationsNotSentStudents($this->yearId);
      $staff_notsend = $this->schedule->getNotificationsNotSentStaff($this->yearId);
      $student_ids = [];
      foreach ($students_notsend as $key => $value) {
        $student_ids[] = $value->stu_id;
      }
      $notify_array['student_ids'] = $student_ids;
      $staff_ids = [];
      foreach ($staff_notsend as $key => $value) {
        $staff_ids[] = $value->staff_id;
      }
      $notify_array['staff_ids'] = $staff_ids;
      $this->load->helper('texting_helper');
      $notify_array['mode'] = 'notification';
      $notify_array['send_to'] = 'Both';
      $notify_array['source'] = 'Online Class';
      $notify_array['student_url'] = site_url('online_class/student_join_controller');
      $notify_array['staff_url'] = site_url('online_class/schedule_controller');
      $notify_array['message'] = $message;
      sendText($notify_array);
    }

    public function saveQuiz() {
      $quiz_id = $_POST['quiz_id'];
      $schedule_id = $_POST['schedule_id'];
      $status = $this->schedule->saveQuiz($quiz_id, $schedule_id);
      echo $status;
    }

    public function getQuizData() {
      $quiz_instance_id = $_POST['quiz_instance_id'];
      $quiz = $this->schedule->getQuizData($quiz_instance_id);
      echo json_encode($quiz);
    }

    public function removeQuiz() {
      $schedule_id = $_POST['schedule_id'];
      $status = $this->schedule->removeQuiz($schedule_id);
      echo $status;
    }

    public function changeQuizStatus() {
      $schedule_id = $_POST['schedule_id'];
      $quiz_status = $_POST['status'];
      $status = $this->schedule->changeQuizStatus($schedule_id, $quiz_status);
      echo $status;
    }

    public function getQuizResults() {
      $schedule_id = $_POST['schedule_id'];
      $quiz_instance_id = $_POST['quiz_instance_id'];
      $data['template'] = $this->schedule->getQuizTemplate($quiz_instance_id);
      $data['result'] = $this->schedule->getQuizResults($quiz_instance_id, $schedule_id);
      echo json_encode($data);
    }

    public function disable_slot() {
      $slot_id = $_POST['slot_id'];
      $status = $this->schedule->disable_slot($slot_id);
      echo $status;
    }

    public function enable_slot() {
      $slot_id = $_POST['slot_id'];
      $room_id = $_POST['room_id'];
      $status = $this->schedule->enable_slot($slot_id, $room_id);
      echo $status;
    }
}?>