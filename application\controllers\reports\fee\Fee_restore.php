<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  07 June 2018
 *
 * Description: Model class for Class Master table
 *
 * Requirements: PHP5 or above
 *
 */

class Fee_restore extends CI_Controller {
	function __construct() {
	  	parent::__construct();
      	if (!$this->ion_auth->logged_in()) {
        	redirect('auth/login', 'refresh');
      	}
      	if (!$this->authorization->isAuthorized('FEE_SOFT_DELETE.VIEW')) {
	      redirect('dashboard', 'refresh');
	    }
      	$this->load->model('report/fee_report_model');
      	$this->load->model('report/fee_restore_model');
	}  

	public function index($classId =''){
		$data['classes'] =  $this->fee_report_model->get_Allclasses();
		$classId = $this->input->post('class_name');
      	if (empty($classId)) {
	        $classId = 0; //0 signifies show all classes
      	}else {
	        if ($classId == 'All') {
	          $classId = 0; //0 signifies show all classes
	        }
      	}
      	$data['SelectedClass'] = $classId;
      	$data['softDelete'] = $this->fee_restore_model->getStudentClassWise($classId);
      	//echo "<pre>"; print_r($data['softDelete']); die();
		$data['main_content'] = 'reports/fees/restore';
    	$this->load->view('inc/template', $data);
	}

	public function softdelete_receipt(){
		$feeNumber = $this->input->post('feeNumber');
		$stdId = $this->input->post('stdId');
		$result = $this->fee_restore_model->restoreSoftdeleteByFeeNumber($feeNumber,$stdId);
	    if ($result) {
	      echo "1";
	    }else{
	      echo "0";
	    }	
	}	


}