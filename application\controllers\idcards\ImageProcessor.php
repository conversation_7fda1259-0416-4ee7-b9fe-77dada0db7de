<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ImageProcessor extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->library('filemanager');
        $this->load->library('image_lib');
    }

    /**
     * Process multiple images in a single request
     * This reduces the number of AJAX calls needed
     */
    public function processImages() {
        // Disable error reporting to prevent HTML errors in JSON response
        ini_set('display_errors', 0);
        error_reporting(0);

        // Set JSON content type header
        header('Content-Type: application/json');

        try {
            $image_urls = $this->input->post('image_urls');
            $orientation = $this->input->post('orientation');

            if (empty($image_urls)) {
                $this->outputJson(['success' => false, 'message' => 'No image URLs provided']);
                return;
            }

            $results = [];

            // Process each image URL
            foreach ($image_urls as $index => $url) {
                try {
                    // Initialize cURL session
                    $ch = curl_init();

                    // Set cURL options
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 10 second timeout

                    // Execute cURL session
                    $image_data = curl_exec($ch);

                    // Check for cURL errors
                    if (curl_errno($ch)) {
                        throw new Exception(curl_error($ch));
                    }

                    // Get content type
                    $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

                    // Close cURL session
                    curl_close($ch);

                    // Check if we actually got image data
                    if (empty($image_data)) {
                        throw new Exception('Empty image data received');
                    }

                    // Create image from string data
                    $image = @imagecreatefromstring($image_data);
                    if (!$image) {
                        throw new Exception('Invalid image data');
                    }

                    // Process the image if needed
                    if ($orientation === 'landscape') {
                        $image = $this->rotateImageResource($image);
                    }

                    // Enhance image quality
                    $image = $this->enhanceImageResource($image);

                    // Output buffer to capture PNG data
                    ob_start();
                    imagepng($image, null, 0); // Use highest quality (0 compression)
                    $processed_image_data = ob_get_clean();

                    // Clean up
                    imagedestroy($image);

                    // Convert to base64 (always PNG for best quality)
                    $base64 = 'data:image/png;base64,' . base64_encode($processed_image_data);

                    $results[] = [
                        'index' => $index,
                        'base64' => $base64,
                        'cached' => false
                    ];

                } catch (Exception $e) {
                    // Create a placeholder image for errors
                    $error_image = $this->createErrorPlaceholder();
                    $results[] = [
                        'index' => $index,
                        'base64' => $error_image,
                        'error' => 'Error processing image: ' . $e->getMessage()
                    ];
                }
            }

            $this->outputJson([
                'success' => true,
                'results' => $results
            ]);

        } catch (Exception $e) {
            // Handle any unexpected errors
            $this->outputJson([
                'success' => false,
                'message' => 'Server error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Output JSON response safely
     */
    private function outputJson($data) {
        // Clear any previous output
        if (ob_get_length()) ob_clean();

        // Set JSON content type header again (in case it was lost)
        header('Content-Type: application/json');

        // Output JSON data
        echo json_encode($data);
        exit; // Ensure no additional output
    }

    /**
     * Rotate image 90 degrees with high quality preservation
     */
    private function rotateImage($image_data) {
        // Create a temporary file
        $temp_file = tempnam(sys_get_temp_dir(), 'img');
        file_put_contents($temp_file, $image_data);

        // Get image info
        $image_info = getimagesize($temp_file);

        // Create image resource based on file type
        switch ($image_info[2]) {
            case IMAGETYPE_JPEG:
                $image = imagecreatefromjpeg($temp_file);
                break;
            case IMAGETYPE_PNG:
                $image = imagecreatefrompng($temp_file);
                // Preserve transparency
                imagealphablending($image, false);
                imagesavealpha($image, true);
                break;
            case IMAGETYPE_GIF:
                $image = imagecreatefromgif($temp_file);
                break;
            default:
                unlink($temp_file);
                return $image_data; // Return original if unsupported type
        }

        // Get dimensions
        $width = imagesx($image);
        $height = imagesy($image);

        // Create a new true color image with swapped dimensions
        $rotated = imagecreatetruecolor($height, $width);

        // Preserve transparency for PNG
        if ($image_info[2] === IMAGETYPE_PNG) {
            imagealphablending($rotated, false);
            imagesavealpha($rotated, true);
            $transparent = imagecolorallocatealpha($rotated, 255, 255, 255, 127);
            imagefilledrectangle($rotated, 0, 0, $height, $width, $transparent);
        }

        // Rotate by copying pixels manually for better quality
        for ($x = 0; $x < $width; $x++) {
            for ($y = 0; $y < $height; $y++) {
                // Get pixel color
                $color = imagecolorat($image, $x, $y);

                // Set pixel in rotated image (90 degrees clockwise)
                // x becomes y, y becomes width-x-1
                imagesetpixel($rotated, $y, $width - $x - 1, $color);
            }
        }

        // Create a new temporary file for the rotated image
        $rotated_file = tempnam(sys_get_temp_dir(), 'rot');

        // Save the rotated image with maximum quality
        if ($image_info[2] === IMAGETYPE_PNG) {
            // For PNG, use maximum quality
            imagepng($rotated, $rotated_file, 0); // 0 = no compression
        } else {
            // For JPEG, use maximum quality
            imagejpeg($rotated, $rotated_file, 100); // 100 = maximum quality
        }

        // Read the rotated image data
        $rotated_data = file_get_contents($rotated_file);

        // Clean up
        imagedestroy($image);
        imagedestroy($rotated);
        unlink($temp_file);
        unlink($rotated_file);

        return $rotated_data;
    }

    /**
     * Enhance image quality using advanced image processing
     */
    private function enhanceImage($image_data) {
        // Create a temporary file
        $temp_file = tempnam(sys_get_temp_dir(), 'img');
        file_put_contents($temp_file, $image_data);

        // Get image info
        $image_info = getimagesize($temp_file);

        // If image is too small, resize it to improve quality
        $min_width = 500; // Minimum width for good quality
        $min_height = 500; // Minimum height for good quality
        $needs_resize = false;

        if ($image_info[0] < $min_width || $image_info[1] < $min_height) {
            $needs_resize = true;
        }

        // Create image resource based on file type
        switch ($image_info[2]) {
            case IMAGETYPE_JPEG:
                $image = imagecreatefromjpeg($temp_file);
                break;
            case IMAGETYPE_PNG:
                $image = imagecreatefrompng($temp_file);
                // Preserve transparency
                imagealphablending($image, false);
                imagesavealpha($image, true);
                break;
            case IMAGETYPE_GIF:
                $image = imagecreatefromgif($temp_file);
                break;
            default:
                unlink($temp_file);
                return $image_data; // Return original if unsupported type
        }

        // Resize if needed
        if ($needs_resize) {
            // Calculate new dimensions while maintaining aspect ratio
            $width = $image_info[0];
            $height = $image_info[1];
            $aspect_ratio = $width / $height;

            if ($width < $min_width) {
                $width = $min_width;
                $height = $width / $aspect_ratio;
            }

            if ($height < $min_height) {
                $height = $min_height;
                $width = $height * $aspect_ratio;
            }

            // Create resized image
            $resized = imagecreatetruecolor($width, $height);

            // Preserve transparency for PNG
            if ($image_info[2] === IMAGETYPE_PNG) {
                imagealphablending($resized, false);
                imagesavealpha($resized, true);
                $transparent = imagecolorallocatealpha($resized, 255, 255, 255, 127);
                imagefilledrectangle($resized, 0, 0, $width, $height, $transparent);
            }

            // Use high quality resampling
            imagecopyresampled($resized, $image, 0, 0, 0, 0, $width, $height, $image_info[0], $image_info[1]);
            imagedestroy($image);
            $image = $resized;
        }

        // Apply advanced image enhancements

        // 1. Apply unsharp mask for better sharpness
        $amount = 80; // Strength of the sharpening effect (0-100)
        $radius = 0.5; // Radius of the Gaussian blur (0.5-1.0 for ID cards)
        $threshold = 3; // Minimum brightness change to apply sharpening

        // Convert to percentage
        $amount = $amount * 0.016;

        // Apply Gaussian blur
        $blurred = imagecreatetruecolor(imagesx($image), imagesy($image));
        if ($image_info[2] === IMAGETYPE_PNG) {
            imagealphablending($blurred, false);
            imagesavealpha($blurred, true);
        }

        // Gaussian blur approximation
        for ($y = 0; $y < imagesy($image); $y++) {
            for ($x = 0; $x < imagesx($image); $x++) {
                $sum_r = $sum_g = $sum_b = $sum_a = 0;
                $count = 0;

                // Sample pixels in a small radius
                for ($j = max(0, $y - 1); $j <= min(imagesy($image) - 1, $y + 1); $j++) {
                    for ($i = max(0, $x - 1); $i <= min(imagesx($image) - 1, $x + 1); $i++) {
                        $rgb = imagecolorat($image, $i, $j);
                        $r = ($rgb >> 16) & 0xFF;
                        $g = ($rgb >> 8) & 0xFF;
                        $b = $rgb & 0xFF;
                        $a = ($rgb >> 24) & 0x7F;

                        $sum_r += $r;
                        $sum_g += $g;
                        $sum_b += $b;
                        $sum_a += $a;
                        $count++;
                    }
                }

                // Average the values
                $avg_r = $sum_r / $count;
                $avg_g = $sum_g / $count;
                $avg_b = $sum_b / $count;
                $avg_a = $sum_a / $count;

                $color = imagecolorallocatealpha($blurred, $avg_r, $avg_g, $avg_b, $avg_a);
                imagesetpixel($blurred, $x, $y, $color);
            }
        }

        // Apply unsharp mask
        for ($y = 0; $y < imagesy($image); $y++) {
            for ($x = 0; $x < imagesx($image); $x++) {
                $rgb1 = imagecolorat($image, $x, $y);
                $r1 = ($rgb1 >> 16) & 0xFF;
                $g1 = ($rgb1 >> 8) & 0xFF;
                $b1 = $rgb1 & 0xFF;
                $a1 = ($rgb1 >> 24) & 0x7F;

                $rgb2 = imagecolorat($blurred, $x, $y);
                $r2 = ($rgb2 >> 16) & 0xFF;
                $g2 = ($rgb2 >> 8) & 0xFF;
                $b2 = $rgb2 & 0xFF;

                // Calculate difference
                $diff_r = abs($r1 - $r2);
                $diff_g = abs($g1 - $g2);
                $diff_b = abs($b1 - $b2);

                // Apply threshold
                if ($diff_r > $threshold || $diff_g > $threshold || $diff_b > $threshold) {
                    $r = min(255, max(0, $r1 + ($r1 - $r2) * $amount));
                    $g = min(255, max(0, $g1 + ($g1 - $g2) * $amount));
                    $b = min(255, max(0, $b1 + ($b1 - $b2) * $amount));

                    $color = imagecolorallocatealpha($image, $r, $g, $b, $a1);
                    imagesetpixel($image, $x, $y, $color);
                }
            }
        }

        imagedestroy($blurred);

        // 2. Adjust brightness and contrast for better visibility
        imagefilter($image, IMG_FILTER_BRIGHTNESS, 5); // Slight brightness increase
        imagefilter($image, IMG_FILTER_CONTRAST, 10); // Moderate contrast increase

        // 3. Enhance colors
        imagefilter($image, IMG_FILTER_COLORIZE, 0, 0, 5, 0); // Slight blue tint for better appearance

        // Create a new temporary file for the enhanced image
        $enhanced_file = tempnam(sys_get_temp_dir(), 'enh');

        // Save the enhanced image with maximum quality
        if ($image_info[2] === IMAGETYPE_PNG) {
            // For PNG, use maximum quality
            imagepng($image, $enhanced_file, 0); // 0 = no compression
        } else {
            // For JPEG, use maximum quality
            imagejpeg($image, $enhanced_file, 100); // 100 = maximum quality
        }

        // Read the enhanced image data
        $enhanced_data = file_get_contents($enhanced_file);

        // Clean up
        imagedestroy($image);
        unlink($temp_file);
        unlink($enhanced_file);

        return $enhanced_data;
    }

    /**
     * Rotate image resource 90 degrees (memory-only version)
     */
    private function rotateImageResource($image) {
        // Get dimensions
        $width = imagesx($image);
        $height = imagesy($image);

        // Create a new true color image with swapped dimensions
        $rotated = imagecreatetruecolor($height, $width);

        // Preserve transparency
        imagealphablending($rotated, false);
        imagesavealpha($rotated, true);
        $transparent = imagecolorallocatealpha($rotated, 255, 255, 255, 127);
        imagefilledrectangle($rotated, 0, 0, $height, $width, $transparent);

        // Rotate by copying pixels manually for better quality
        for ($x = 0; $x < $width; $x++) {
            for ($y = 0; $y < $height; $y++) {
                // Get pixel color
                $color = imagecolorat($image, $x, $y);

                // Set pixel in rotated image (90 degrees clockwise)
                // x becomes y, y becomes width-x-1
                imagesetpixel($rotated, $y, $width - $x - 1, $color);
            }
        }

        // Clean up original image
        imagedestroy($image);

        return $rotated;
    }

    /**
     * Enhance image resource quality (memory-only version)
     */
    private function enhanceImageResource($image) {
        // Get dimensions
        $width = imagesx($image);
        $height = imagesy($image);

        // If image is too small, resize it to improve quality
        $min_width = 500; // Minimum width for good quality
        $min_height = 500; // Minimum height for good quality

        if ($width < $min_width || $height < $min_height) {
            // Calculate new dimensions while maintaining aspect ratio
            $aspect_ratio = $width / $height;

            if ($width < $min_width) {
                $new_width = $min_width;
                $new_height = $new_width / $aspect_ratio;
            } else {
                $new_width = $width;
                $new_height = $height;
            }

            if ($new_height < $min_height) {
                $new_height = $min_height;
                $new_width = $new_height * $aspect_ratio;
            }

            // Create resized image
            $resized = imagecreatetruecolor($new_width, $new_height);

            // Preserve transparency
            imagealphablending($resized, false);
            imagesavealpha($resized, true);
            $transparent = imagecolorallocatealpha($resized, 255, 255, 255, 127);
            imagefilledrectangle($resized, 0, 0, $new_width, $new_height, $transparent);

            // Use high quality resampling
            imagecopyresampled($resized, $image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
            imagedestroy($image);
            $image = $resized;

            // Update dimensions
            $width = $new_width;
            $height = $new_height;
        }

        // Apply unsharp mask for better sharpness
        $blurred = imagecreatetruecolor($width, $height);

        // Preserve transparency
        imagealphablending($blurred, false);
        imagesavealpha($blurred, true);

        // Apply Gaussian blur approximation
        for ($y = 0; $y < $height; $y++) {
            for ($x = 0; $x < $width; $x++) {
                $sum_r = $sum_g = $sum_b = $sum_a = 0;
                $count = 0;

                // Sample pixels in a small radius
                for ($j = max(0, $y - 1); $j <= min($height - 1, $y + 1); $j++) {
                    for ($i = max(0, $x - 1); $i <= min($width - 1, $x + 1); $i++) {
                        $rgb = imagecolorat($image, $i, $j);
                        $r = ($rgb >> 16) & 0xFF;
                        $g = ($rgb >> 8) & 0xFF;
                        $b = $rgb & 0xFF;
                        $a = ($rgb >> 24) & 0x7F;

                        $sum_r += $r;
                        $sum_g += $g;
                        $sum_b += $b;
                        $sum_a += $a;
                        $count++;
                    }
                }

                // Average the values
                $avg_r = $sum_r / $count;
                $avg_g = $sum_g / $count;
                $avg_b = $sum_b / $count;
                $avg_a = $sum_a / $count;

                $color = imagecolorallocatealpha($blurred, $avg_r, $avg_g, $avg_b, $avg_a);
                imagesetpixel($blurred, $x, $y, $color);
            }
        }

        // Apply unsharp mask
        $amount = 80; // Strength of the sharpening effect (0-100)
        $amount = $amount * 0.016; // Convert to percentage
        $threshold = 3; // Minimum brightness change to apply sharpening

        for ($y = 0; $y < $height; $y++) {
            for ($x = 0; $x < $width; $x++) {
                $rgb1 = imagecolorat($image, $x, $y);
                $r1 = ($rgb1 >> 16) & 0xFF;
                $g1 = ($rgb1 >> 8) & 0xFF;
                $b1 = $rgb1 & 0xFF;
                $a1 = ($rgb1 >> 24) & 0x7F;

                $rgb2 = imagecolorat($blurred, $x, $y);
                $r2 = ($rgb2 >> 16) & 0xFF;
                $g2 = ($rgb2 >> 8) & 0xFF;
                $b2 = $rgb2 & 0xFF;

                // Calculate difference
                $diff_r = abs($r1 - $r2);
                $diff_g = abs($g1 - $g2);
                $diff_b = abs($b1 - $b2);

                // Apply threshold
                if ($diff_r > $threshold || $diff_g > $threshold || $diff_b > $threshold) {
                    $r = min(255, max(0, $r1 + ($r1 - $r2) * $amount));
                    $g = min(255, max(0, $g1 + ($g1 - $g2) * $amount));
                    $b = min(255, max(0, $b1 + ($b1 - $b2) * $amount));

                    $color = imagecolorallocatealpha($image, $r, $g, $b, $a1);
                    imagesetpixel($image, $x, $y, $color);
                }
            }
        }

        imagedestroy($blurred);

        // Adjust brightness and contrast for better visibility
        imagefilter($image, IMG_FILTER_BRIGHTNESS, 5); // Slight brightness increase
        imagefilter($image, IMG_FILTER_CONTRAST, 10); // Moderate contrast increase

        return $image;
    }

    /**
     * Create a placeholder image for errors
     */
    private function createErrorPlaceholder() {
        // Create a 300x300 image with red background
        $img = imagecreatetruecolor(300, 300);
        $bg_color = imagecolorallocate($img, 255, 240, 240); // Light red background
        $text_color = imagecolorallocate($img, 200, 0, 0); // Red text
        $border_color = imagecolorallocate($img, 255, 0, 0); // Red border

        // Fill background
        imagefill($img, 0, 0, $bg_color);

        // Draw border
        imagerectangle($img, 0, 0, 299, 299, $border_color);

        // Add error text
        $font_size = 5; // Built-in font size
        $text = "Image Error";
        $text_width = imagefontwidth($font_size) * strlen($text);
        $text_height = imagefontheight($font_size);
        $x = (300 - $text_width) / 2;
        $y = (300 - $text_height) / 2;

        imagestring($img, $font_size, $x, $y, $text, $text_color);

        // Output to buffer
        ob_start();
        imagepng($img);
        $image_data = ob_get_clean();

        // Clean up
        imagedestroy($img);

        // Return as base64
        return 'data:image/png;base64,' . base64_encode($image_data);
    }
}
