<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Desginservey_Controller
 *
 * <AUTHOR>
 */
class Desginservey_Controller extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->load->model('student_feedback/Feedback_student');
    }

    public function surveyPage() {
        $data['surveyPageListResult'] = $this->Feedback_student->surveyPageListInfo();
        $data['main_content'] = 'student_feedback/Survey';
        $this->load->view('inc/template', $data);
    }

    public function designSurveyPage($id) {

        $data['editsurveyToStudent'] = $this->Feedback_student->editSurveyStudentListData($id);
        //echo "<pre>";print_r($data['editsurveyToStudent']);die();
        $data['designSurveyPageListResult'] = $this->Feedback_student->designSurveyPageList();


        $data['staffNameList'] = $this->Feedback_student->staffList();
        $data['QuestionList'] = $this->Feedback_student->QuestionList();
        //echo "<pre>";print_r($data['QuestionList']);die();
        $data['main_content'] = 'student_feedback/desginSurvey';
        $this->load->view('inc/template', $data);
    }

    public function addStaff() {
        $addStaffName = $this->Feedback_student->addStaffList();
        if ($addStaffName) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
        } else {

            $this->session->set_flashdata('flashSuccess', 'Failed To Insert .');
        }
        redirect('student_feedback/Desginservey_Controller/index');
    }

    public function addsurveyPage() {
        $surveyQuestions = $this->Feedback_student->addSurveyPageQuestions();
        if ($surveyQuestions) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
        } else {

            $this->session->set_flashdata('flashSuccess', 'Something went wrong .');
        }
        redirect('student_feedback/Desginservey_Controller/surveyPage');
    }

    public function surveyStudentList($id) {

        $data['classList'] = $this->Feedback_student->getClassList();
        $data['surveyId'] = $id;
        $data['surveyToStudentLists'] = $this->Feedback_student->surveyToStudentListData($id);

        // echo "<pre>";print_r( $data['surveyToStudentLists']);die();
        $data['main_content'] = 'student_feedback/SurveyStudentList';
        $this->load->view('inc/template', $data);
    }

    public function getClassesSections() {

        if (isset($_POST['classid'])) {
            $classid = $_POST['classid'];
            $getclassectioninfo = $this->Feedback_student->getsectionList($classid);
            echo json_encode($getclassectioninfo);
        }
    }

    public function getStudentListClasswise() {
        
        $classId = $_POST['classid'];
         $survey_id = $_POST['survey_id'];
        $class_section_id = $_POST['class_section_id'];
        $studentListInfo = $this->Feedback_student->getStudentListClasswiseData($classId, $class_section_id,$survey_id);
        echo json_encode($studentListInfo);
    }

    public function addsurveyToStudent($id) {
        $addsurveyToStudentList = $this->Feedback_student->surveyListToStudentLst($id);
        if ($addsurveyToStudentList) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
        } else {

            $this->session->set_flashdata('flashSuccess', 'Something went wrong .');
        }
        redirect('student_feedback/Desginservey_Controller/surveyPage');
    }

    public function surveyToStudentList() {

        $data['main_content'] = 'student_feedback/SurveyStudentList';
        $this->load->view('inc/template', $data);
    }

    public function updateSurveyStudentList($id) {
        $editsurveyToStudent = $this->Feedback_student->updateSurveyStudentListData($id);
        if ($editsurveyToStudent) {
            $this->session->set_flashdata('flashSuccess', 'Successfully updated.');
        } else {

            $this->session->set_flashdata('flashSuccess', 'Something went wrong .');
        }
        redirect('student_feedback/Desginservey_Controller/surveyPage');
    }

}
