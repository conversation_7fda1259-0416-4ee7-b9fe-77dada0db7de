<?php
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of student_controller
 *
 * <AUTHOR>
 */
class Staff_controller extends CI_Controller
{
  private $formElements;
  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if ($this->authorization->isModuleEnabled('STAFF_MASTER') && ($this->authorization->isAuthorized('STAFF.VIEW'))) {
      $this->load->model('staff/Staff_Model');
      $this->load->model('student/Student_Model');
      $this->load->model('staff/Payroll_Model');
      $this->config->load('form_elements');
      $this->formElements = $this->config->item('form_elements');
      $this->load->library('filemanager');
      $this->load->model('user_provisioning_model');
    } else {
      redirect('dashboard', 'refresh');
    }
  }

  public function index($staff_status = '', $staffType = '', $isPrimaryInstance = '')
  {
    $data['sStatus'] = $this->settings->getSetting('staff_status');
    if (!empty($staff_status)) {
      $status = $staff_status;
    } else {
      $status = '2';
    }
    $is_primary_instance = ($isPrimaryInstance != '') ? $isPrimaryInstance : '';
    $data['adSelected'] = $status;
    $data['isPrimaryInstance'] = $is_primary_instance;

    if (empty($staffType) && $staffType != '0') {
      $data['selectedStaffType'] = '-1';
    } else {
      $data['selectedStaffType'] = $staffType;
    }
    $data['staffDetails'] = $this->Staff_Model->getStaffDetails_staff_view($status, $data['selectedStaffType'], $is_primary_instance);
    $data['permitStaffAdd'] = $this->authorization->isAuthorized('STAFF.ADD_EDIT_DELETE');
    $data['permitStaffDetail'] = $this->authorization->isAuthorized('STAFF.VIEW_DETAILS');
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    $data['enable_is_primary_instance_filter'] = $this->settings->getSetting('staff_enable_primary_instance_filter_in_view');
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/staff_registration/index_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'staff/staff_registration/index_mobile';
    } else {
      $data['main_content'] = 'staff/staff_registration/index';
    }


    $this->load->view('inc/template', $data);
  }

  //Add Staff
  public function addStaff()
  {
    if (!$this->authorization->isAuthorized('STAFF.ADD_EDIT_DELETE')) {
      redirect('dashboard', 'refresh');
    }
    // $data['departmentList'] = $this->Staff_Model->getStaffDistinctColumn('department');
    // $data['designationList'] = $this->Staff_Model->getStaffDistinctColumn('designation');
    $data['departmentList'] = $this->Staff_Model->getStaffDepartmentList();
    $data['designationList'] = $this->Staff_Model->getStaffDesignationList();
    $data['qualificationList'] = $this->Staff_Model->getStaffDistinctColumn('qualification');
    $data['staffStatusArr'] = $this->settings->getSetting('staff_status');
    $data['staff_type'] = $this->settings->getSetting('staff_type');
    $data['houseList'] = $this->Staff_Model->getHouseList();
    // $data['caste'] = $this->config->item('CASTE');
    $data['caste'] = $this->Staff_Model->get_staff_caste();
    $data['custom_field'] = $this->settings->getSetting('staff_custom_fields');

    $display_fields = $this->Staff_Model->get_config_display_fields();
    $dbEnabed = [];
    foreach ($display_fields as $key => $enabled) {
        $dbEnabed = json_decode($enabled->value);
    }

    $data['display_enabled_fields'] = (array) $dbEnabed;
    // echo "<pre>";
    // print_r($data['display_enabled_fields']);
    // die();

    $display_rquired = $this->Staff_Model->get_congif_staff_required_fields();
    $dbRequired = [];
    foreach ($display_rquired as $key => $enabled) {
        $dbRequired = json_decode($enabled->value, true);
    }
    $data['display_required_fields'] = (array) $dbRequired;

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/staff_registration/add/index_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'staff/staff_registration/add/index_mobile';
    } else {
      $data['main_content'] = 'staff/staff_registration/add/index';
    }

    $this->load->view('inc/template', $data);
  }

  //Submit Staff
  public function submitStaff(){
    $this->load->helper('email_helper');
    if (!$this->authorization->isAuthorized('STAFF.ADD_EDIT_DELETE')) {
      redirect('dashboard', 'refresh');
    }
    $staff_employee_code_receipt_book_id = $this->settings->getSetting('staff_employee_code_receipt_book_id');

    $employee_id = '';
    if(!empty($staff_employee_code_receipt_book_id)){
      $employee_id = $this->Staff_Model->update_staff_employee_code_by_receipt_book($staff_employee_code_receipt_book_id);
    }
    // echo '<pre>';print_r($employee_id);die();

    $this->db->trans_begin();

    if (isset($_FILES['photograph'])) {
       $min_size = $this->_resize_image($_FILES['photograph']['tmp_name'], 200, $_FILES['photograph']['type']);

      $picture = array('tmp_name' => $min_size, 'name' => $min_size);
      $sResigedPhoto = $this->s3FileUpload($picture);

      $staff_uid = $this->Staff_Model->addStaffDetails($sResigedPhoto,$employee_id);
    } else {
      $staff_uid = $this->Staff_Model->addStaffDetails(null,$employee_id);
    }

    if ($staff_uid != 0) {
      if(!empty($staff_employee_code_receipt_book_id)){
        $employee_code_format = $this->Staff_Model->get_employee_code_format($staff_employee_code_receipt_book_id);
        if(!empty($employee_code_format)){
          $this->db->where('id',$staff_employee_code_receipt_book_id);
          $this->db->update('feev2_receipt_book', array('running_number'=>$employee_code_format->running_number+1));
        }
      }
      $sName = $this->settings->getSetting('school_short_name');
      $len = strlen((string)$staff_uid);
      $digits = 'ST';
      for ($i = 6 - $len;$i > 0; $i--) { 
        $digits .= '0';
      }
      $s_short_name = $sName;
      $digits .= $staff_uid;
      $qrCode = strtoupper($s_short_name).$digits;

      $this->Staff_Model->update_qr_code($staff_uid,$qrCode);

      $email_content = $this->Staff_Model->get_staff_profile_created_email_data($staff_uid);
      if(!empty($email_content)){
        $memberEmail = explode(',', $email_content['members_email']);
        $email_content['content'] = str_replace('%%staff_name%%',$email_content['staff_name'], $email_content['content']);
        $email_content['content'] = str_replace('%%employee_code%%',$email_content['employee_code'], $email_content['content']);
        $sent_email = sendEmail($email_content['content'],$email_content['email_subject'],0,$memberEmail,$email_content['registered_email']);
        if($sent_email){
          $this->load->model('communication/emails_model');
            $sent_by = $this->authorization->getAvatarStakeHolderId();
            $email_master_data = array(
              'subject' => $email_content['email_subject'],
              'body' => $email_content['content'],
              'source' => 'Staff Profile Added',
              'sent_by' => $sent_by,
              'recievers' => "Staff",
              'from_email' => $email_content['registered_email'],
              'files' => '' ,
              'acad_year_id' => $this->acad_year->getAcadYearID(),
              'visible' => 1,
              'sender_list' => NULL,
              'sending_status' => 'Completed'
            );

            $email_master_id = $this->emails_model->saveEmail($email_master_data);
            foreach($memberEmail as $key => $val){
              $email_data = [];
              $email_data['stakeholder_id'] = 0;
              $email_data['avatar_type'] = 0;
              $email_data['email'] = $val;
              $email_data['email_master_id'] = $email_master_id;
              $email_data['status'] = 'Awaited';
              $this->Staff_Model->save_sending_email_data($email_data);
            }
        }
      }
      $this->db->trans_commit();
      $this->session->set_flashdata('flashSuccess', 'Staff Details Successfully Inserted.');
    } else {
      $this->db->trans_rollback();
      $this->session->set_flashdata('flashError', 'Unable to Insert Staff Details.');
    }
    redirect('staff/Staff_controller');
  }

  //Edit Staff
  public function editStaff($staffId) {
    // echo "<pre>"; print_r($staffId); die();
    if (!$this->authorization->isAuthorized('STAFF.ADD_EDIT_DELETE')) {
      redirect('dashboard', 'refresh');
    }
    $data['employmentType'] = $this->config->item('employmentType');
    $data['staff_id'] = $staffId;
    $data['staffDetail'] = $this->Staff_Model->getStaffDetailById($staffId);
    $data['departmentList'] = $this->Staff_Model->getStaffDepartmentList();
    $data['designationList'] = $this->Staff_Model->getStaffDesignationList();
    $data['qualificationList'] = $this->Staff_Model->getStaffDistinctColumn('qualification');
    $data['staffStatusArr'] = $this->settings->getSetting('staff_status');
    $data['staff_name'] = $this->Staff_Model->getStaffName($staffId);
    $data['staff_type'] = $this->settings->getSetting('staff_type');
    $data['custom_field'] = $this->settings->getSetting('staff_custom_fields');
    $data['casteList'] = $this->Staff_Model->getCasteList();
    $data['salutationList'] = $this->Staff_Model->getsalutations();
    $data['houseList'] = $this->Staff_Model->getHouseList();
    // $data['caste'] = $this->config->item('CASTE');
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    $display_fields = $this->Staff_Model->get_config_display_fields();
    $dbEnabed = [];
    foreach ($display_fields as $key => $enabled) {
        $dbEnabed = json_decode($enabled->value);
    }
    $data['display_enabled_fields'] = (array) $dbEnabed;
    // echo "<pre>";
    // print_r($data['display_enabled_fields']);die();

    $display_rquired = $this->Staff_Model->get_congif_staff_required_fields();
    $dbRequired = [];
    foreach ($display_rquired as $key => $enabled) {
        $dbRequired = json_decode($enabled->value, true);
    }
    $data['display_required_fields'] = (array) $dbRequired;

    // echo "<pre>"; print_r($data['staff_type']); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/staff_registration/edit/index_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'staff/staff_registration/edit/index_mobile';
    } else {
      $data['main_content'] = 'staff/staff_registration/edit/index';
    }
    // $data['main_content'] = 'staff/staff_registration/edit/index';
    $this->load->view('inc/template', $data);
  }

  public function updateStaff($staffId) {
    if (!$this->authorization->isAuthorized('STAFF.ADD_EDIT_DELETE')) {
      redirect('dashboard', 'refresh');
    }
    // print_r($_FILES['staff_photo']); die();
    $input_form = $this->input->post();
    $sResigedPhoto = '';
    if (isset($_FILES['photograph'])) {
      $min_size = $this->_resize_image($_FILES['photograph']['tmp_name'], 200, $_FILES['photograph']['type']);
      $picture = array('tmp_name' => $min_size, 'name' => $min_size);
      $sResigedPhoto = $this->s3FileUpload($picture);
    } 
    $this->Staff_Model->store_staff_edit_history($staffId,$input_form['old_value'],$input_form['new_value']);
    $result = $this->Staff_Model->updateStaff($staffId, $input_form, $input_form['staffUserId'], $sResigedPhoto);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Staff Details Successfully updated.');
    } else {
      $this->session->set_flashdata('flashSuccess', 'Staff Details failed to update.');
    }
    redirect('staff/Staff_controller/addMoreStaffInfo/' . $staffId);
  }

  public function deleteStaff($id)
  {
    if (!$this->authorization->isAuthorized('STAFF.ADD_EDIT_DELETE')) {
      redirect('dashboard', 'refresh');
    }
    $result = $this->Staff_Model->deleteStaff($id);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Staff Details Successfully deleted.');
    } else {
      $this->session->set_flashdata('flashSuccess', 'Staff Details deletion failed.');
    }
    redirect('staff/Staff_controller');
  }

  private function s3FileUpload($file, $folder_name = 'profile')
  {
    if ($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }
    return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
  }

  public function addMoreStaffInfo()
  {
    if (!$this->authorization->isAuthorized('STAFF.VIEW_DETAILS')) {
      redirect('dashboard', 'refresh');
    }
    $data['staff_uid'] = $this->uri->segment(4);
    $data['staff_name'] = $this->Staff_Model->getStaffName($data['staff_uid']);
    $data['permitStaffCrud'] = $this->authorization->isAuthorized('STAFF.ADD_EDIT_DELETE');
    $data['permitStaffUserCreation'] = $this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_STAFF');
    $data['permitProfileView'] = $this->authorization->isAuthorized('STAFF_PROFILE.VIEW');
    $data['permit_staff_sms_report'] = $this->authorization->isAuthorized('STAFF.VIEW_SMS_REPORT');
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    $data['status']=$this->Staff_Model->getStaffStatus($data['staff_uid']);
    $status = $data['status']->status;
    //$status=$status->status;
    // echo "<pre>"; echo print_r($data['status']); die();

    $StaffStatus = '';
    if ($status =='2') {
      $StaffStatus = '<strong>Approved</strong>';
    } else {
      $StaffStatus = '<strong>Resigned</strong>';
    }
    $site_url = site_url();
    //echo $ ;die();
   
    
    if($data)
    $tiles = array(
      [
        'title' => 'Address',
        'sub_title' => '',
        'icon' => 'svg_icons/address.svg',
        'url' => $site_url . 'staff/Staff_controller/addStaffAddress/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.ADD_EDIT_DELETE')
      ],
      [
        'title' => 'Payroll Details',
        'sub_title' => '',
        'icon' => 'svg_icons/address.svg',
        'url' => $site_url . 'staff/Staff_controller/add_staff_payroll_details/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.PAYROLL_DETAILS')
      ],
      [
        'title' => 'Reset Password',
        'sub_title' => '',
        'icon' => 'svg_icons/resetpassword.svg',
        'url' => $site_url . 'staff/Staff_controller/resetPassword/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_STAFF'),
        'click_function' => "return confirm('Do you really want to reset password?');"
      ],
      [
        'title' => 'Provision User Login',
        'sub_title' => '',
        'icon' => 'svg_icons/username.svg',
        'url' => $site_url . 'staff/Staff_controller/changeusername/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.PROVISION_USER_LOGIN')
      ],
      [
        'title' => 'Staff Observations',
        'sub_title' => '',
        'icon' => 'svg_icons/view.svg',
        'url' => $site_url . 'staff/observation/viewObservations/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF_OBSERVATION.VIEW_SUMMARY')
      ],
      [
        'title' => 'Update Data',
        'sub_title' => '',
        'icon' => 'svg_icons/updatedata.svg',
        'url' => $site_url . 'staff/Staff_controller/editStaff/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.ADD_EDIT_DELETE')
      ],
      // [
      //   'title' => 'Profile',
      //   'sub_title' => '',
      //   'icon' => 'svg_icons/maleprofile.svg',
      //   'url' => $site_url . 'staff/Staff_profile_view_controller/staff_profile/' . $data['staff_uid'],
      //   'permission' => $this->authorization->isAuthorized('STAFF_PROFILE.VIEW')
      // ], 
      [
        'title' => 'SMS Report',
        'sub_title' => '',
        'icon' => 'svg_icons/smsreport.svg',
        'url' => $site_url . 'sms/staffSMSReport/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.VIEW_SMS_REPORT')
      ],
      [
        'title' => 'Reset Session',
        'sub_title' => '',
        'icon' => 'svg_icons/resetsession.svg',
        'url' => $site_url . 'staff/Staff_controller/resetSession/' . $data['staff_uid'],
        'permission' => $this->authorization->isSuperAdmin(),
        'click_function' => "return confirm('Do you really want to reset session?');"
      ],
      [
        'title' => 'Manage Staff Exit',
        'sub_title' => 'Current Status:' .$StaffStatus,
        'icon' => 'svg_icons/maleprofile.svg',
        'url' => $site_url . 'staff/Staff_controller/staffexit/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.STAFF_EXIT')
      ]
    );

    $manage_attributes_tiles = array (
      [
        'title' => 'Manage Documents',
        'sub_title' => 'Upload Documents',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url . 'staff/Staff_controller/manageDocuments/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.MANAGE_DOCUMENTS')
      ],
      [
        'title' => 'Manage Qualifications',
        'sub_title' => 'Qualifications',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url . 'staff/Staff_controller/qualifications/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.MANAGE_QUALIFICATION')
      ],
      [
        'title' => 'Manage Awards',
        'sub_title' => 'Awards',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url . 'staff/Staff_controller/awards/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.MANAGE_AWARDS')
      ],
      [
        'title' => 'Manage Experience',
        'sub_title' => 'Experience',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url . 'staff/Staff_controller/experience/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.MANAGE_EXPERIENCE')
      ],
      [
        'title' => 'Manage Staff Initiatives',
        'sub_title' => 'Staff Initiatives',
        'icon' => 'svg_icons/staff.svg',
        'url' => $site_url . 'staff/Staff_controller/initiative/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.STAFF_INITIATIVES')
      ],
      [  
        'title' => 'Manage Tranings / workshops',
        'sub_title' => '',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url . 'staff/Staff_controller/stafftraning_workshop/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.STAFF_TRANING_WORKSHOP')
      ],
      [
        'title' => 'Manage Publications / Citations',
        'sub_title' => '',
        'icon' => 'svg_icons/maleprofile.svg',
        'url' => $site_url . 'staff/Staff_controller/staffpublications_citations/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.STAFF_PUBLICATIONS_CITAT')
      ],
      [
        'title' => 'Manage Interests',
        'sub_title' => '',
        'icon' => 'svg_icons/maleprofile.svg',
        'url' => $site_url . 'staff/Staff_controller/staffinterests/' . $data['staff_uid'],
        'permission' => $this->authorization->isAuthorized('STAFF.STAFF_INTERESTS')
      ]
    );
    $data['tiles'] = checkTilePermissions($tiles);
    $data['manage_attributes_tiles'] = checkTilePermissions($manage_attributes_tiles);

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/staff_registration/more/index_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'staff/staff_registration/more/index_mobile';
    } else {
      $data['main_content'] = 'staff/staff_registration/more/index';
    }

    $this->load->view('inc/template', $data);
  }

  public function addStaffAddress($staff_uid)
  {
    if (!$this->authorization->isAuthorized('STAFF.ADD_EDIT_DELETE')) {
      redirect('dashboard', 'refresh');
    }
    $data['staff_uid'] = $staff_uid;
    $data['staff_name'] = $this->Staff_Model->getStaffName($data['staff_uid']);
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/staff_registration/more/address_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'staff/staff_registration/more/address_mobile';
    } else {
      $data['main_content'] = 'staff/staff_registration/more/address';
    }


    $this->load->view('inc/template', $data);
  }

  private function _prepareStaffAddressInput(&$input)
  {
    $return_data = [];
    $present_flag = 0;
    $permanent_flag = 0;

    foreach ($input as $k => $v) {
      $start_key = substr($k, 0, 2);
      if ($start_key == 'a_') {
        if (substr($k, 0, 9) == 'a_present') {
          if (!empty($v))
            $present_flag = 1;

          $key = str_replace("a_present_", "", $k);
          $return_data['address_present'][$key] = $v;
        } else {
          if (!empty($v))
            $permanent_flag = 1;

          $key = str_replace("a_permanent_", "", $k);
          if($key == 'add_land1'){
            $return_data['address_permanent']['land_mark1'] = $v;
          }else if($key == 'add_land2'){
            $return_data['address_permanent']['land_mark2'] = $v;
          }else{
            $return_data['address_permanent'][$key] = $v;
          }
        }
      }
    }

    $return_data['address_present']['flag'] = $present_flag;
    $return_data['address_permanent']['flag'] = $permanent_flag;

    //echo '<pre>';print_r($return_data);

    return $return_data;
  }

  public function submitStaffAddress()
  {
    $input_form = $this->input->post();
    $stakeholder_id = $input_form['staff_uid'];
    $avatar_type  = 4;
    $grouped_input = $this->_prepareStaffAddressInput($input_form);
    $old_data = str_replace('a_','',$input_form['old_value']);
    $new_data = str_replace('a_','',$input_form['new_value']);
    $this->Staff_Model->Store_edit_history($stakeholder_id,$old_data,$new_data);
    if ($grouped_input['address_present']['flag'] == 1)
      $this->Student_Model->addAddressInfo($grouped_input['address_present'], $stakeholder_id, $avatar_type, 0);

    if ($grouped_input['address_permanent']['flag'] == 1)
      $this->Student_Model->addAddressInfo($grouped_input['address_permanent'], $stakeholder_id, $avatar_type, 1);
    $this->session->set_flashdata('flashSuccess', 'Address Infomation Successfully added');
    redirect('staff/Staff_controller/addMoreStaffInfo/' . $input_form['staff_uid']);
  }


  // Salary details
  public function addSalaryDetails()
  {
    $data['staff_uid'] = $this->uri->segment(4);
    $data['staff_name'] = $this->Staff_Model->getStaffName($data['staff_uid']);
    $data['staff_salary'] = $this->Staff_Model->getStaffSalary($data['staff_uid']);

    $data['salaryDetails'] = $this->Staff_Model->get_SallaryDetailsbyStaffId($data['staff_uid']);
    $data['schoolName'] = $this->settings->getSetting('school_short_name');

    if ($data['schoolName'] == 'concorde') {
      $profileData = $this->Payroll_Model->getProfileData();
      $data['profileData'] = $profileData;
      $data['profileDataJSON'] = json_encode($profileData);
    }

    $data['main_content'] = 'staff/staff_registration/more/salary_details';
    $this->load->view('inc/template', $data);
  }

  public function submitStaffSalary()
  {

    $staffId = $this->input->post('staffId');
    $result = $this->Staff_Model->insert_staffSalaryDetailsy($staffId);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Staff Salary Details Successfully Added.');
    } else {
      $this->session->set_flashdata('flashError', 'Staff salary details insert failed.');
    }
    redirect('staff/Staff_controller/addMoreStaffInfo/' . $staffId);
  }

  public function updateStaffSalary($staffId)
  {
    $result = $this->Staff_Model->update_staffSalaryDetailsy($staffId);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Staff Salary Details Successfully updated.');
    } else {
      $this->session->set_flashdata('flashError', 'Staff salary details update failed.');
    }
    redirect('staff/Staff_controller/addMoreStaffInfo/' . $staffId);
  }

  public function payslip_staff()
  {
    $data['staffDetails'] = $this->Staff_Model->getApprovedStaffDetails();
    $data['main_content'] = 'staff/payslip_generation/index';
    $this->load->view('inc/template', $data);
  }
  public function add_payslip($staffId)
  {
    $data['schoolName'] = $this->settings->getSetting('school_short_name');
    $data['salaryDetails'] = $this->Staff_Model->get_SallaryDetailsbyStaffId($staffId);
    if ($data['schoolName'] == 'concorde') {
      $profileData = $this->Payroll_Model->getProfileDataById($staffId);
      $data['profileObj'] = $profileData;
    }
    if (empty($data['salaryDetails']->staff_id)) {
      $this->session->set_flashdata('flashInfo', 'Salary details for this staff not found. Add them from Staff Master');
      redirect('staff/Staff_controller/payslip_staff');
    }
    $data['paySlip'] = $this->Staff_Model->get_payslipGenereted($staffId);
    //echo "<pre>"; print_r($data['profileObj']); die();
    $data['main_content'] = 'staff/payslip_generation/add';
    $this->load->view('inc/template', $data);
  }

  public function generatePayslip()
  {
    $staffid = $this->input->post('staffid');
    $result = $this->Staff_Model->generate_staffSalaryDetailsy($staffid);
    if ($result) {
      $month_name = $this->input->post('month_name');
      $year = $this->input->post('year');
      $this->session->set_flashdata('flashSuccess', 'Staff Salary Details Successfully generated.');
      redirect('staff/Staff_controller/print_payslip/' . $staffid . '/' . $month_name . '/' . $year . '');
    } else {
      $this->session->set_flashdata('flashError', 'Staff salary details insert failed.');
      redirect('staff/Staff_controller/payslip_staff');
    }
  }

  public function updateGeneratePayslip()
  {
    $staffid = $this->input->post('staffid');
    $result = $this->Staff_Model->Update_staffPaySlipDetailsy($staffid);
    $month_name = $this->input->post('month_name');
    $year = $this->input->post('year');
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Staff Salary Details Successfully Updated.');
      redirect('staff/Staff_controller/print_payslip/' . $staffid . '/' . $month_name . '/' . $year . '');
    } else {
      $this->session->set_flashdata('flashError', 'Staff salary details update failed.');
    }
    redirect('staff/Staff_controller/payslip_staff');
  }

  public function print_payslip($staffid, $month_name, $year)
  {
    $data['slip_details'] = $this->Staff_Model->print_payslipGenerate($staffid, $month_name, $year);
    //echo "<pre>"; print_r($data['slip_details']); die();
    $data['main_content'] = 'staff/payslip_generation/print';
    $this->load->view('inc/template', $data);
  }

  public function resetPassword($staffId)
  {
    if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_STAFF')) {
      redirect('dashboard', 'refresh');
    }
    $Uid = $this->Staff_Model->getUid($staffId);
    // echo "<pre>"; print_r($Uid); die();
    if (empty($Uid)) {
      $this->session->set_flashdata('flashError', 'Something went wrong. Please contact admin.');
      redirect('staff/Staff_controller/addMoreStaffInfo/' . $staffId);
    }
    $data = array(
      'password' => 'welcome123',
    );
    $result = $this->ion_auth->update($Uid->user_id, $data);
    // $result = $this->ion_auth->update($Uid->user_id, $data);
    //$status = (int)$this->Staff_Model->resetPassword($staffId);
    if ($result) {
      $this->Staff_Model->Store_edit_history($staffId,'','The password has been reset.');
      $this->session->set_flashdata('flashSuccess', 'Password reset successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Failed to reset password.');
    }
    redirect('staff/Staff_controller/addMoreStaffInfo/' . $staffId);
  }

  public function changeusername($staffId)
  {
    if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_STAFF')) {
      redirect('dashboard', 'refresh');
    }
    $data['staff_uid'] = $this->uri->segment(4);
    $data['staff_name'] = $this->Staff_Model->getStaffName($data['staff_uid']);
    $data['staffUserName'] = $this->Staff_Model->getStaffuserDetails($staffId);
    $data['permit_temp_reset'] = $this->authorization->isSuperAdmin();
    if (empty($data['staffUserName'])) {
      $this->session->set_flashdata('flashError', 'Something went wrong. Please contact admin.');
      redirect('staff/Staff_controller/addMoreStaffInfo/' . $staffId);
    }
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    $data['main_content'] = 'staff/staff_registration/more/provision_user';
    $this->load->view('inc/template', $data);
  }

  public function checkUsername()
  {
    $username = $_POST['username'];
    $result = $this->Staff_Model->checkUsernamebystafwise($username);
    if ($result == '1') {
      echo '<span style="color:red;text-align:center;">Username already exists. Choose a different unique name.</span>';
    }
  }
  public function submitusername($user_id)
  {
    $userName_new = $this->input->post('username_new');
    $userName = $this->input->post('username_old');
    $staffId = $this->input->post('staff_id');

    //echo '<pre>';print_r($this->input->post());die();

    if (!empty($userName_new))
      $userName = $userName_new;

    $active = $this->input->post('active');
    if (empty($active))
      $active = 0;
    else
      $active = 1;

    $result = $this->ion_auth->update($user_id, array("username" => $userName, 'active' => $active));
    if ($result) {
      $this->Staff_Model->Store_edit_history($staffId,'User name : '.$this->input->post('username_old'),'User name : '.$this->input->post('username_new'));
      $this->session->set_flashdata('flashSuccess', 'User provisioned succesfully. UserName: ' . $userName . ' Password: welcome123');
    } else {
      $this->session->set_flashdata('flashError', 'Failed to provision user.');
    }
    redirect('staff/Staff_controller/addMoreStaffInfo/' . $staffId);
  }

  public function temp_reset_staff_password()
  {
    $staff_user_id = $this->input->post('staff_user_id');
    
    $cur_password = $this->db->select('password')
      ->where('id', $staff_user_id)
      ->get('users')->row()->password;

    $this->db->trans_start();

    $this->db->where('id', $staff_user_id)->update('users', array('restore_password' => $cur_password));
    
    $data = array(
      'password' => 'welcome123',
    );
    $result = $this->ion_auth->update($staff_user_id, $data);
    $this->db->trans_complete();

    if ($this->db->trans_status() === TRUE) {
      $this->db->trans_commit();
      echo 1;
    } else {
      $this->db->trans_rollback();
      echo 0;
    }
  }

  public function restore_staff_password()
  {
    $staff_user_id = $this->input->post('staff_user_id');

    $restore_password = $this->db->select('restore_password')
      ->where('id', $staff_user_id)
      ->get('users')->row()->restore_password;

    $this->db->trans_start();
    
    $this->db->where('id', $staff_user_id)->update('users', array('password' => $restore_password));
    
    $this->db->where('id', $staff_user_id)->update('users', array('restore_password' => null));
    
    $this->db->trans_complete();
    
    if ($this->db->trans_status() === TRUE) {
      $this->db->trans_commit();
      echo 1;
    } else {
      $this->db->trans_rollback();
      echo 0;
    }
  }

  //Create Staff Logs
  public function logs()
  {
    $this->load->model('competition_model');
    $data['taskslist'] = $this->Staff_Model->getLogsDistinctColumn();
    $data['staff_details'] = $this->competition_model->get_staff_detils();
    //echo "<pre>"; print_r($data['staff_details']); die();
    $data['main_content'] = 'staff/staff_logs';
    $this->load->view('inc/template', $data);
  }
  public function create_logs()
  {

    $status = $this->Staff_Model->create_log();
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Added Log.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('staff/staff_controller/staff_logs_index');
  }

  public function staff_logs_index()
  {
    $data['logs'] = $this->Staff_Model->getStaffLogs();
    $data['staff_names'] = array();
    foreach ($data['logs'] as $log) {
      $data['staff_names'][$log->id] = $this->Staff_Model->getStaffNames($log->id);
    }
    $data['from_date'] = "mm/dd/yyyy";
    $data['to_date'] = date("Y-m-d");
    //echo "<pre>"; print_r($data['staff_names']); die();
    $data['main_content'] = 'staff/logs';
    $this->load->view('inc/template', $data);
  }

  public function filter_logs()
  {
    $data['logs'] = $this->Staff_Model->filterStaffLogs();
    //echo "<pre>"; print_r($data['logs']); die();
    $data['staff_names'] = array();
    foreach ($data['logs'] as $log) {
      $data['staff_names'][$log->id] = $this->Staff_Model->getStaffNames($log->id);
    }
    $data['from_date'] = $this->input->post('from_date');
    $data['to_date'] = $this->input->post('to_date');
    $data['main_content'] = 'staff/logs';
    $this->load->view('inc/template', $data);
  }

  //   public function SMS(){

  //     $staffId = $this->authorization->getAvatarStakeHolderId();
  //     $data['staffwise_sms'] =  $this->Staff_Model->get_smsStaffIdwise($staffId);
  //     //echo "<pre>"; print_r($data['staffwise_sms']); die();
  //     $data['main_content']    = 'staff/notification/sms/index';
  //     $this->load->view('inc/template', $data);

  //   }

  //   public function view_sms($id){

  //   $data['sms'] =  $this->Staff_Model->getSMSDetail($id);
  //   // echo "<pre>"; print_r($data['sms']); die();
  //   $data['main_content']    = 'staff/notification/sms/sms_view';
  //   $this->load->view('inc/template', $data);
  // }

  public function provisionStaff()
  {
    $data['staffs'] = $this->Staff_Model->getStaffData();
    $data['counts'] = ['total' => 0, 'activated' => 0, 'loggedIn' => 0, 'not_activated' => 0];
    foreach ($data['staffs'] as $key => $value) {
      $value->number_exists = 1;
      if ($value->contact_number == '') {
        $value->contact_number = '----------';
        $value->number_exists = 0;
      }
      $data['counts']['total']++;
      if ($value->logged_in) {
        $data['counts']['loggedIn']++;
      } else {
        if ($value->status == 'Activated') {
          $data['counts']['activated']++;
        } else {
          $data['counts']['not_activated']++;
        }
      }
    }
    // echo "<pre>"; print_r($data);die();
    $data['main_content']    = 'staff/provision/index';
    $this->load->view('inc/template', $data);
  }

  private function generateRandomString($length = 10)
  {
    return substr(str_shuffle(str_repeat($x = 'abcdefghijkmnopqrstuvw', ceil($length / strlen($x)))), 1, $length);
  }

  public function getActivationPreview()
  {
    $staffIds = $_POST['staffIds'];
    $staffData = $this->Staff_Model->getActivationPreview($staffIds);
    $pData = array();
    // $part2 = $this->settings->getSetting('user_provisioning_manual_cridential_part_2');
    $appLinks = $this->settings->getSetting('app_links');
    $android_app = $appLinks->android_app;
    $ios_app = $appLinks->ios_app;

    $part1 = $this->settings->getSetting('user_provisioning_manual_cridential_part_1');
    // $part2 = $this->settings->getSetting('user_provisioning_manual_cridential_part_2');
    $message_for_credit_calculation = '';
    foreach ($staffData as $key => $staff) {
      $password = $this->generateRandomString(6);
      $upData = array(
        'password' => $password,
      );
      $this->ion_auth->update($staff->userId, $upData);
      $message = str_replace("%%username%%", $staff->username, $part1);
      $message = str_replace("%%password%%", $password, $message);
      $number_display = ($staff->contact_number) ? $staff->contact_number : '--------';
      $number = $staff->contact_number;
      $name = $staff->staffName;
      $staffId = $staff->staffId;
      $userId = $staff->userId;
      $pData[] = ['name' => $name, 'number_display' => $number_display, 'number' => $number, 'message' => $message, 'staffId' => $staffId, 'userId' => $userId];
      if (strlen($message_for_credit_calculation) < strlen($message)) {
        $message_for_credit_calculation = $message;
        //get the largest message for credits calculation
      }
    }


    $this->load->helper('texting_helper');
    $is_credits_available = checkCredits($message_for_credit_calculation, count($pData), 'parent');
    echo json_encode(array('preview' => $pData, 'credits_available' => $is_credits_available));
  }

  public function activateStaff()
  {
    $input = $this->input->post();
    $school = $this->settings->getSetting('school_short_name');
    if ($school == 'englishroots') {
      foreach ($input['numbers'] as $staff_id => $number) {
        $this->new_sms_sender($number, $input['messages'][$staff_id]);
      }
      $res = array('error' => '');
    } else {
      $user_type = 'Staff';
      $sent_to = 'Staff Individual';
      $source = 'Staff Provision';
      $message = '';
      $sent_by = $this->authorization->getAvatarId();
      foreach ($input['messages'] as $staffId => $msg) {
        $message = $msg;
        break;
      }

      $input_arr = array();
      $input_arr['staff_id_messages'] = $input['messages'];
      $input_arr['mode'] = 'sms';
      $input_arr['source'] = 'Staff Activation';
      $this->load->helper('texting_helper');
      $res = sendUniqueText($input_arr);
    }
    if ($res['error'] != '') {
      $this->session->set_flashdata('flashError', $res['error']);
    } else {
      $activationStatus = $this->Staff_Model->activateStaffAcount($input['userIds']);
      $this->session->set_flashdata('flashSuccess', 'Successfully Activated & Credentials Sent');
    }
    redirect('staff/staff_controller/provisionStaff');
  }

  private function new_sms_sender($number, $message)
  {
    $message = urlencode($message);
    $api_key = 'adwm0e5HYvQE3TNI';
    $senerid = 'NXTSMS';
    //http://promotional.mysmsbasket.com/V2/http-api.php?apikey=XXXXXXXXXXXXXXXX&senderid=XXXXXX&number=XXXXXXXXXXX,XXXXXXXXXXX,XXXXXXXXXXX&message=hello there&format=json
    $url = 'http://promotional.mysmsbasket.com/V2/http-api.php';

    // $get_url = "$url?apikey=$api_key&senderid=$senerid&number=$number&message=$message&format=json&";

    $curl = curl_init();
    curl_setopt_array($curl, array(
      CURLOPT_URL => $url,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => "",
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 30,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => "POST",
      CURLOPT_POSTFIELDS => "apikey=" . $api_key . "&senderid=" . $senerid . "&number=" . $number . "&message=" . $message . "&format=json",
      CURLOPT_HTTPHEADER => array(
        "Accept: application/json",
        "Cache-Control: no-cache",
        "Content-Type: application/x-www-form-urlencoded"
      ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);

    $result = json_decode($response);
    if ($result->status == 'OK') {
      return 1;
    }
    return 0;
  }

  public function deactivateStaff()
  {
    $userId = $_POST['userId'];
    $status = $this->Staff_Model->deactivateStaff($userId);
    echo $status;
  }

  public function resetSession($staffId)
  {
    $result = $this->Staff_Model->reset_session_by_staff($staffId);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Session reset successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Failed to reset Session.');
    }
    redirect('staff/Staff_controller/addMoreStaffInfo/' . $staffId);
  }

  public function staff_reportings()
  {
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/reportings/index_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'staff/reportings/index_mobile';
    } else {
      $data['main_content'] = 'staff/reportings/index';
    }
    $this->load->view('inc/template', $data);
  }

  public function getReportingStaffData()
  {
    $staff_type = $this->input->post('staff_type');
    $data['staffList'] = $this->Staff_Model->getStaffReportingData($staff_type);
    echo json_encode($data);
  }

  public function changeStaffManagementRole()
  {
    $staff_id = $_POST['staff_id'];
    $status = $_POST['status'];
    if($status == 1){
      $new_value = 'Added to Reporting Managers List';  
    }else{
      $new_value = 'Removed From Reporting Managers List';
    }
    
    $this->Staff_Model->Store_edit_history($_POST['staff_id'],'',$new_value);
    echo $this->Staff_Model->changeStaffManagementRole($staff_id, $status);
  }

  public function getReportingManagers()
  {
    $data['reportingManagers'] = $this->Staff_Model->getReportingManagers();
    echo json_encode($data);
  }

  public function assignReportingManager()
  {
    $staff_id = $_POST['staff_id'];
    $reporting_staff_id = $_POST['reporting_staff_id'];
    $reporting_staff_name = $_POST['reporting_staff_name'];
    $this->Staff_Model->updating_reporting_manger_history_tracking($staff_id,$reporting_staff_name);
    echo $this->Staff_Model->assignReportingManager($staff_id, $reporting_staff_id);
  }

  public function staffCodePage()
  {
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/staff_code/index_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'staff/staff_code/index_mobile';
    } else {
      $data['main_content'] = 'staff/staff_code/index';
    }
    $this->load->view('inc/template', $data);
  }

  public function mass_document_upload(){
    $data['document_names'] = $this->Staff_Model->get_document_names();
    $data['sStatus'] = $this->settings->getSetting('staff_status');
    $data['main_content'] = 'staff/mass_document_upload/mass_document_upload_page';
    $this->load->view('inc/template', $data);
  }

  public function staffCodeData()
  {
    $data['staffList'] = $this->Staff_Model->getStaffCodeData();
    echo json_encode($data);
  }

  public function singleStaffCodeData()
  {
    $staff_id = $_POST['staff_id'];
    $data['staffList'] = $this->Staff_Model->getSingleStaffCodeData($staff_id);
    echo json_encode($data);
  }
  public function assignNewStaffCode()
  {
    $staff_id = $_POST['staff_id'];
    $staff_code = $_POST['staff_code'];
    echo $this->Staff_Model->assignNewStaffCode($staff_id, $staff_code);
  }

  public function add_staff_payroll_details($staff_id)
  {
    $data['staff_id'] = $staff_id;
    $data['staff'] = $this->Payroll_Model->getStaffname($staff_id);
    $data['main_content'] = 'staff/payroll/staff_payroll_details';
    $this->load->view('inc/template', $data);
  }

  public function save_staff_payroll_details()
  {
    $this->Staff_Model->Store_edit_history($_POST['staff_id'],$_POST['old_value'],$_POST['new_value']);
    $result = $this->Payroll_Model->update_staff_payroll_details();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Update successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Failed to update.');
    }
    redirect('staff/Staff_controller/addMoreStaffInfo/' . $_POST['staff_id']);
  }

  public function manageDocuments($staff_id)
  {
    if (!$this->authorization->isAuthorized('STAFF.MANAGE_DOCUMENTS')) {
      redirect('dashboard', 'refresh');
    }
    $data["staff_id"] = $staff_id;
    $staff_documents = json_decode($this->settings->getSetting('staff_documents_name'));
    $data['staff_documents'] = array();
    if(!empty($staff_documents)){
      sort($staff_documents);  
      $data['staff_documents'] = $staff_documents;
    }
    $data['docs_list'] = $this->Staff_Model->get_docs_list($staff_id);
    $data['staff_name'] = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $data['main_content'] = 'staff/uploadDocuments/index';
    $this->load->view('inc/template', $data);
  }

  public function qualifications($staff_id)
  {
    $data["staff_id"] = $staff_id;
    $data['staffDetail'] = $this->Staff_Model->getStaffDetailById($staff_id);
    $data['staff_name'] = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $data['qualificationList'] = $this->Staff_Model->getStaffDistinctColumn('qualification');
    $data['main_content'] = 'staff/qualifications/index';
    $this->load->view('inc/template', $data);
  }

  public function add_qualifications($staff_id)
  {
    // echo "<pre>"; echo print_r($_POST); die();
    $result = $this->Staff_Model->add_qualifications($staff_id, $_POST, $this->s3FileUpload($_FILES['staff_qualification_doc'], 'qualification_documents'));
    // echo $result; die();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Insertion Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    redirect('staff/Staff_controller/qualifications/' . $staff_id);
  }

  public function get_qualifications($staff_id)
  {
    $result = $this->Staff_Model->get_qualifications($staff_id);
    echo json_encode($result);
  }

  public function edit_qualification($staff_id)
  {
    $result = $this->Staff_Model->edit_qualification($staff_id, $_POST, $this->s3FileUpload($_FILES['staff_qualification_doc'], 'qualification_documents'));
    if ($result) {
      $this->session->set_flashdata('flashSuccess', "Update Successful");
    } else {
      $this->session->set_flashdata('flashError', "Something Went Wrong");
    }
    redirect("staff/Staff_controller/qualifications/" . $staff_id);
  }

  public function get_particular_qualification()
  {
    $result = $this->Staff_Model->get_particular_qualification($_POST);
    echo json_encode($result);
  }

  public function disable_particular_qualification($staff_id)
  {
    // echo "<pre>"; print_r($_POST); die();
    echo $this->Staff_Model->disable_particular_qualification($staff_id, $_POST);
  }

  public function staff_qualifications_documents_download($staff_document_id, $staff_id)
  {
    // echo $document->supporting_document; die();
    $document = $this->Staff_Model->get_qualification_document_row($staff_document_id);
    $document_url = $this->filemanager->getFilePath($document->supporting_document);
    $document_data = file_get_contents($document_url);
    $pdf_name = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $this->load->helper('download');
    force_download($pdf_name . '.pdf', $document_data, TRUE);
  }

  public function awards($staff_id)
  {
    $data["staff_id"] = $staff_id;
    $data['staffDetail'] = $this->Staff_Model->getStaffDetailById($staff_id);
    $data["staff_id"] = $staff_id;
    $data['staff_name'] = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $data['qualificationList'] = $this->Staff_Model->getStaffDistinctColumn('qualification');
    $data['main_content'] = 'staff/awards/index';
    $this->load->view('inc/template', $data);
  }

  public function add_awards($staff_id)
  {
    // echo "<pre>"; echo print_r($_POST); die();
    $staff_awards_folder = "staff_awards";
    $result = $this->Staff_Model->add_awards($staff_id, $_POST);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Insertion Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong');
    }
    redirect("staff/Staff_controller/awards/" . $staff_id);
  }

  public function get_awards($staff_id)
  {
    $result = $this->Staff_Model->get_awards($staff_id);
    echo json_encode($result);
  }

  public function get_particular_award($staff_id)
  {
    $result = $this->Staff_Model->get_particular_award($_POST);
    echo json_encode($result);
  }

  public function disable_particular_award($staff_id)
  {
    echo $this->Staff_Model->disable_particular_award($staff_id, $_POST);
  }

  public function edit_award($staff_id)
  {
    // echo "<pre>"; echo print_r($_POST); die();
    $result = $this->Staff_Model->edit_award($_POST);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', "Update Successful");
    } else {
      $this->session->set_flashdata('flashError', "Something Went Wrong");
    }
    redirect("staff/Staff_controller/awards/" . $staff_id);
  }

  public function experience($staff_id)
  {
    $data["staff_id"] = $staff_id;
    $data['staffDetail'] = $this->Staff_Model->getStaffDetailById($staff_id);
    $data['staff_name'] = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $data['qualificationList'] = $this->Staff_Model->getStaffDistinctColumn('qualification');
    $data['main_content'] = 'staff/experience/index';
    $this->load->view('inc/template', $data);
  }

  public function add_experience($staff_id)
  {
    // echo "<pre>"; echo print_r($_POST); die();
    $staff_documents_folder = "staff_experience_doc";
    $result = $this->Staff_Model->add_experience($staff_id, $_POST, $this->s3FileUpload($_FILES['staff_experience_doc'], $staff_documents_folder));
    if ($result) {
      $this->session->set_flashdata('flashSuccess', "Insertion Successful");
    } else {
      $this->session->set_flashdata('flashError', "Something Went Wrong");
    }
    redirect("staff/Staff_controller/experience/" . $staff_id);
  }

  public function get_experiences($staff_id)
  {
    $result = $this->Staff_Model->get_experiences($staff_id);
    echo json_encode($result);
  }

  public function get_particular_experience($staff_id)
  {
    // echo "<pre>"; echo print_r($_POST); die();
    $result = $this->Staff_Model->get_particular_experience($staff_id, $_POST);
    echo json_encode($result);
  }

  public function disable_particular_experience($staff_id)
  {
    echo $this->Staff_Model->disable_particular_experience($staff_id, $_POST);
  }

  public function edit_experience($staff_id)
  {
    $staff_documents_folder = "staff_experience_doc";
    $result = $this->Staff_Model->edit_experience($_POST, $this->s3FileUpload($_FILES['staff_experience_doc'], $staff_documents_folder));
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Update Successful');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    redirect("staff/Staff_controller/experience/" . $staff_id);
  }

  public function staff_experiences_documents_download($staff_document_id, $staff_id)
  {
    $document = $this->Staff_Model->get_experience_document_row($staff_document_id);
    $document_url = $this->filemanager->getFilePath($document->supporting_document);
    //echo "<pre>"; print_r($document_url);die();
    $document_data = file_get_contents($document_url);
    $pdf_name = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $this->load->helper('download');
    force_download($pdf_name . '.pdf', $document_data, TRUE);
  }
  // public function staff_exit_documents_download($staff_id)
  // {
  //   $document_obj = $this->Staff_Model->get_exited_document_row($staff_id);
  //   $document_url = $this->filemanager->getFilePath($document_obj->last_date_of_work);
  //   $document_data = file_get_contents($document_url);
  //   $pdf_name = $this->Staff_Model->get_staff_name_by_id($staff_id) . "_";
  //   $this->load->helper('download');
  //   force_download($pdf_name . '.pdf', $document_data, TRUE);
  // }

  public function stafftraning_workshop($staff_id)
  {
    $data["staff_id"] = $staff_id;
    $data['staff_name'] = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $data['training_workshop'] = $this->Staff_Model->get_staff_workshop_training($staff_id);
    $data['main_content'] = 'staff/workshop/stafftraning_workshop';
    $this->load->view('inc/template', $data);
  }

  public function staffpublications_citations($staff_id)
  {
    $data["staff_id"] = $staff_id;
    $data['staff_name'] = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $data['main_content'] = 'staff/publications/staffpublications_citations';
    $this->load->view('inc/template', $data);
  }
  public function staffinterests($staff_id)
  {
    $data["staff_id"] = $staff_id;
    $data['staff_name'] = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $data['main_content'] = 'staff/staff_interests/staff_interests';
    $this->load->view('inc/template', $data);
  }
  public function staffexit($staff_id){
    $data["staff_id"] = $staff_id;
    $data["status"]= $this->Staff_Model->getStaffStatus($staff_id);
    $data['staff_name'] = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $data['staff_data'] = $this->Staff_Model->get_exit_details($staff_id);
    $data['main_content'] = 'staff/staff_exit/staff_exit';
    $this->load->view('inc/template', $data);
  }
  public function staff_exit_documents_download($staff_id)
  {
    $document_obj = $this->Staff_Model->get_exited_document_row($staff_id);
    if(!$document_obj) {
      return 0;
    }
    $document_url = $this->filemanager->getFilePath($document_obj->resignation_letter_doc);
    $document_data = file_get_contents($document_url);
    $pdf_name = $this->Staff_Model->get_staff_name_by_id($staff_id) . "_";
    $this->load->helper('download');
    force_download($pdf_name . '.pdf', $document_data, TRUE);
  }
  

  public function add_exit_document($staff_id=0)
  {

    // validation
    if( empty($staff_id) || $staff_id <=0){
      $this->session->set_flashdata('flashError', 'Something went wrong. Please try terminating the staff again.');
      redirect('staff/Staff_controller/'); 
    }
    $postData = $this->input->post();
    if (empty($postData) || empty($postData['staff_exit_date'])) {
        $this->session->set_flashdata('flashError', 'Staff exit date is required'); 
        redirect('staff/Staff_controller/staffexit/' . $staff_id);
    }

    // date validation
    $date = DateTime::createFromFormat('d-m-Y', $postData['staff_exit_date']);
    
    if ($date) {
        $staff_exit_date = $date->getTimestamp();
    } else {
      $this->session->set_flashdata('flashError', 'Invalid date format for staff exit date');
      redirect('staff/Staff_controller/staffexit/' . $staff_id);
    }
    $today = strtotime('tomorrow') - 1;
    $isExitDateNotFuture= ($staff_exit_date <= $today);
    
    // getting old data
    $staff_termination_old_data=$this->Staff_Model->get_staff_termination_old_data($staff_id);
    if(!empty($staff_termination_old_data)){
      $old_data = json_encode($staff_termination_old_data);
    }else{
      $old_data ='';
    }

    // document updation
    $doc= $this->s3FileUpload($_FILES['document_obj']);

    if (empty($doc) || !isset($doc['file_name'])) {
      $this->session->set_flashdata('flashError', 'Document upload failed');
      redirect('staff/Staff_controller/staffexit/' . $staff_id);
    }


    $isStaffTerminated = $this->Staff_Model->terminate_staff($postData, $staff_id, $doc['file_name'],$isExitDateNotFuture,$old_data,json_encode($postData));
    if($isStaffTerminated == 0){
      $this->session->set_flashdata('flashError', 'Something went wrong during staff exit process');
    }else{
      $this->session->set_flashdata('flashSuccess', 'Staff exit process successful');
    }

    redirect('staff/Staff_controller/staffexit/' . $staff_id);
  }

  public function stafftraning_workshop_insert($staff_id){
   echo $this->Staff_Model->stafftraning_workshop_insert_data($staff_id, $this->s3FileUpload($_FILES['certificate_name'], 'staff_work_shop_certificate'));
  }

  public function stafftraning_workshop_update(){
    echo $this->Staff_Model->stafftraning_workshop_update_data();
  }

  public function staffpublications_citations_insert($staff_id){
    echo $this->Staff_Model->staffpublications_citations_insert_data($staff_id);
  }

public function staffinterest_insert($staff_id){
    echo  $this->Staff_Model->staffinterest_insert_data($staff_id);
  }

  public function staffinterest_update(){
    echo $this->Staff_Model->staffinterest_update_data();
  }

  public function staffpublications_citations_update(){
    echo $this->Staff_Model->staffpublications_citations_update_data();
  }


   public function stafftraning_workshop_documents_download($staffId, $workshop_staff_certificate_id){
    $staff_work_shop_certificate = $this->Staff_Model->get_workshop_certificate_row($workshop_staff_certificate_id);
    $document_url = $this->filemanager->getFilePath($staff_work_shop_certificate->certificate_path);
    $document_data = file_get_contents($document_url);
    $this->load->helper('download');
    force_download('certificates.pdf', $document_data, TRUE);
  }


  public function addManagedDocuments($staff_id)
  {
    $staff_documents_folder = 'staff_documents';
    $this->Staff_Model->addManagedDocuments($_POST, $staff_id, $this->s3FileUpload($_FILES['document_obj'], $staff_documents_folder));
    redirect('staff/Staff_controller/manageDocuments/' . $staff_id);
  }

  public function delete_documentsbyid($staff_id, $docId)
  {
    $result = $this->Staff_Model->delete_documentsbyid($docId);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Delete succcessfully!');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('staff/Staff_controller/manageDocuments/' . $staff_id);
  }
  public function staff_documents_download($staff_document_id, $staff_id)
  {
    $document_obj = $this->Staff_Model->get_document_row($staff_document_id);
    $document_url = $this->filemanager->getFilePath($document_obj->document_url);
    $document_data = file_get_contents($document_url);
    $pdf_name = $this->Staff_Model->get_staff_name_by_id($staff_id) . "_" . $document_obj->document_type;
    $this->load->helper('download');
    force_download($pdf_name . '.pdf', $document_data, TRUE);
  }



  public function provision_staff() {
    // Only load the view, do not fetch all staff or counts here for faster UI load
    $data['main_content']    = 'staff/provision/credentials';
    $this->load->view('inc/template', $data);
  }

  public function getstaff_activation_preview() {
    $staffIds = $_POST['staffIds'];

    $staffData = array();
    // $part2 = $this->settings->getSetting('user_provisioning_manual_cridential_part_2');
    $appLinks = $this->settings->getSetting('app_links');
    $android_app = $appLinks->android_app;
    $ios_app = $appLinks->ios_app;

    $part1 = $this->settings->getSetting('user_provisioning_manual_cridential_part_1');
    $school_code = $this->settings->getSetting('user_provisioning_school_code');
    $school_name = $this->settings->getSetting('school_name');
    // $part2 = $this->settings->getSetting('user_provisioning_manual_cridential_part_2');
    foreach ($staffIds as $key => $val) {
      $explodeVal = explode('_', $val);
      $staff = $this->Staff_Model->get_staff_provision_Preview($explodeVal[0]);

      if ($explodeVal[1] == 'email') {
        $email_content = $this->user_provisioning_model->get_staff_provision_email_template();
      }

      if ($explodeVal[1] == 'sms')
        $message_by = $staff->contact_number;
      else if ($explodeVal[1] == 'email')
        $message_by = $staff->email;

      $message_for_credit_calculation = '';

      $password = $this->generateRandomString(6);
      $upData = array(
        'password' => $password,
      );
      $this->ion_auth->update($staff->userId, $upData);

      $message = str_replace("%%school_name%%", $school_name, $part1);
      $message = str_replace("%%school_code%%", $school_code, $message);
      $message = str_replace("%%username%%", $staff->username, $message);
      $message = str_replace("%%password%%", $password, $message);

      if ($explodeVal[1] == 'email') {
        $message = str_replace("%%staff_name%%", $staff->staffName, (empty($email_content)) ? '' : $email_content->content);
        $message = str_replace("%%username%%", $staff->username, $message);
        $message = str_replace("%%password%%", $password, $message);
      }

      if (strlen($message_for_credit_calculation) < strlen($message)) {
        $message_for_credit_calculation = $message;
      }

      $staffData[] = array(
        'staffId' => $staff->staffId,
        'userId' => $staff->userId,
        'name' => $staff->staffName,
        'message' => $message,
        'message_by' => $message_by,
        'send_type' => $explodeVal[1]
      );
      $is_credits_available = 1;
      if($explodeVal[1] == 'sms') {
          $this->load->helper('texting_helper');
          $is_credits_available = checkCredits($message_for_credit_calculation, count($staffData), 'parent');
      }
    }
    echo json_encode(array('preview' => $staffData, 'credits_available' => $is_credits_available));
  }

  public function send_sms_email_active_staff() {
    $input = $this->input->post();
    $staffEmails = [];
    $staffSMS = [];
    $userIds = array();
    foreach ($input['messages'] as $ids => $msg) {
      list($staffId, $user_id, $sent_type) = explode("_", $ids);
      if ($sent_type == 'email') {
        $staffEmails[$staffId] = $msg;
      } else {
        $staffSMS[$staffId] = $msg;
      }
      array_push($userIds, $user_id);
    }

    $activeResponse = $this->Staff_Model->activateStaffAcount($userIds);

    if ($activeResponse == 0) {
      $this->session->set_flashdata('flashError', 'Unable to activate users');
      redirect('parent_activation');
    }
    if (!empty($staffEmails)) {
      $emailStatus = $this->_communicateByEmail($staffEmails);
      if ($emailStatus == 1) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Email Sent');
      } else {
        $this->session->set_flashdata('flashError', 'Something Went Wrong..');
      }
    }
    if (!empty($staffSMS)) {
      $smsStatus = $this->_communicateBySMS($staffSMS);
      if ($smsStatus == 1) {
        $this->session->set_flashdata('flashSuccess', 'Successfully SMS Sent');
      } else if ($smsStatus == 0) {
        $this->session->set_flashdata('flashError', 'Something Went Wrong..');
      } else if ($smsStatus == -1) {
        $this->session->set_flashdata('flashError', 'Not enough credits available');
      }
    }
    redirect('staff/staff_controller/provision_staff');
  }

  private function _communicateByEmail($staffEmails) {
    $staffIds = array();
    $userIds = array();
    foreach ($staffEmails as $sId => $sEmail) {
      $staffIds[] = $sId;
    }
    $set = $this->user_provisioning_model->get_staff_provision_email_template();
    $emails = $this->Staff_Model->getActivationPreview($staffIds);

    $emailStore = array();
    foreach ($emails as $key => $email) {
        $emailStore[$email->staffId] = array(
            'email_body'=>$staffEmails[$email->staffId],
            'email'=>$email->email,
            'avatar_type'=>$email->avatar_type,
            'stakeholder_id'=>$email->staffId
        ) ;
    }
    $this->load->model('communication/emails_model');
    $this->load->helper('email_helper');
    foreach ($emailStore as $staffId => $email) {
        $email_ids = array();

        $email_master_data = array(
            'subject' => (empty($set)) ? '' : $set->email_subject,
            'body' => $email['email_body'],
            'source' => 'Provision Staff Credentails',
            'sent_by' => $this->authorization->getAvatarId(),
            'recievers' => 'Staffs',
            'from_email' => $set->registered_email,
            'files' => '',
            'acad_year_id' => $this->acad_year->getAcadYearId(),
            'visible' => 1,
            'sender_list' => NULL,
            'sending_status' => 'Completed',
        );
        $email_master_id = $this->emails_model->saveEmail($email_master_data);

        $email_obj = new stdClass();
        $email_obj->stakeholder_id = $email['stakeholder_id'];
        $email_obj->avatar_type = $email['avatar_type'];
        $email_obj->email = trim($email['email']);
        $email_data = [$email_obj];

        if(!empty(trim($email['email']))){
            $email_ids = [$email['email']];
        }
        $this->emails_model->save_sending_email_data($email_data, $email_master_id);
        if(!empty($email_ids)){
            sendEmail($email['email_body'], $set->email_subject, $email_master_id, $email_ids, $set->registered_email);
        }
    }
    return 1;
  }

  private function _communicateBySMS($staffMessages) {
    $input_arr = array();
    $input_arr['staff_id_messages'] = $staffMessages;
    $input_arr['mode'] = 'sms';
    $input_arr['source'] = 'User Staff Provisioning';
    $input_arr['send_to'] = 'Staff';
    $res = $this->_send_unique_texts($input_arr);
    if ($res['error'] != '') {
      return 0;
    }
    return 1;
  }

  private function _send_unique_texts($input) {
    $this->load->helper('texting_helper');
    return sendUniqueText($input);
  }

  public function deactivate_staff_provision_credentials_by_user_id()
  {
    $userIds = $_POST['userIds'];
    echo $this->Staff_Model->deactivate_provision_credentials_by_user_id($userIds);
  }

  public function reset_staff_default_password_user_id()
  {
    $userId = $_POST['userId'];
    $data = array(
      'password' => 'welcome123',
    );
    echo $this->ion_auth->update($userId, $data);
  }

  public function stafftraning_view_data()
  {
    $trainId = $_POST['trainId'];
    $result = $this->Staff_Model->stafftraning_view_dataId($trainId);
    echo json_encode($result);
  }


  public function staffpublication_view_data()
  {
    $trainId = $_POST['trainId'];
    $result = $this->Staff_Model->staffpublication_view_dataId($trainId);
    echo json_encode($result);
  }

  public function staffinterest_view_dataId()
  {
    $trainId = $_POST['trainId'];
    $result = $this->Staff_Model->staffinterest_view_dataId($trainId);
    echo json_encode($result);
  }

  // Staff Initiative controllers

  public function initiative($staff_id)
  {
    $data["staff_id"] = $staff_id;
    $data['staff_name'] = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $data['main_content'] = 'staff/staff_initiative/index';
    $this->load->view('inc/template', $data);
  }

  public function get_all_staff_initiative($staff_id)
  {
    $result = $this->Staff_Model->get_all_staff_initiative($staff_id);
    echo json_encode($result);
  }

  public function get_particular_staff_initiative($staff_id)
  {
    // echo "<pre>"; echo print_r($_POST); die();
    $result = $this->Staff_Model->get_particular_staff_initiative($staff_id, $_POST);
    echo json_encode($result);
  }

  public function insertStaffInitiative($staff_id)
  {
    // echo "<pre>"; echo print_r($_FILES['staff_initiative_document']); die();

    $result = $this->Staff_Model->insert_staff_initiative_details($staff_id, $_POST, $this->s3FileUpload($_FILES['staff_initiative_document'], 'staff_initiative_documents'));
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Updated.');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong');
    }
    redirect('staff/Staff_controller/initiative/' . $staff_id);
  }

  public function disable_particular_staff_initiative($staff_id)
  {
    $result = $this->Staff_Model->disable_particular_staff_initiative($staff_id, $_POST);
    redirect('staff/Staff_controller/initiative/' . $staff_id);
  }

  public function staff_initiative_documents_download($doc_id, $staff_id)
  {
    $document = $this->Staff_Model->get_initiative_document_row($doc_id);
    $document_url = $this->filemanager->getFilePath($document->document);
    $document_data = file_get_contents($document_url);
    $pdf_name = $this->Staff_Model->get_staff_name_by_id($staff_id);
    $this->load->helper('download');
    force_download($pdf_name . '.pdf', $document_data, TRUE);
  }
    public function staffpublication_view_dataId(){
      $trainId = $_POST['trainId'];
      $result = $this->Staff_Model->staffpublication_view_dataId($trainId);
      echo json_encode($result);
    }
    
    public function disable_staffinterest(){
      $postId = $_POST['postId'];
      $status = $_POST['status'];
      echo $this->Staff_Model->disable_staffinterest($postId, $status);
  }

  public function disable_staffpublications(){
      $postId = $_POST['postId'];
      $status = $_POST['status'];
      echo $this->Staff_Model->disable_staffpublications($postId, $status);
  }

  public function disable_stafftraning_workshop(){
      $postId = $_POST['postId'];
      $status = $_POST['status'];
      echo $this->Staff_Model->disable_stafftraning_workshop($postId, $status);
  }

  public function get_staff_interests_data(){
    $staffId = $_POST['staffId'];
    $result  = $this->Staff_Model->get_staff_interest($staffId);
    echo json_encode($result);
  }
   public function get_staff_publication_data(){
    $staffId = $_POST['staffId'];
   
    $result  = $this->Staff_Model->get_staff_publication_citations($staffId);
    echo json_encode($result);
  }
  public function get_staff_workshop_data(){
    $staffId = $_POST['staffId'];
    $result  = $this->Staff_Model->get_staff_workshop_training($staffId);
    echo json_encode($result);
  }

  public function get_staff_edit_approved_list(){
    $data['staff_list'] = $this->Staff_Model->getApprovedStaffDetails();
    // echo "<pre>"; print_r($data['staff_list']);die();
    $data['main_content']    = 'staff/staff_edit_approved';
    $this->load->view('inc/template', $data);
  }

  public function get_edit_staff_attribute_list(){
    $from_date =$_POST['from_date'];
    $to_date =$_POST['to_date'];
    $staff_approved_status =$_POST['staff_approved_status'];
    $staff_id =$_POST['staff_id'];
    $result = $this->Staff_Model->get_edit_staff_attribute_listbypost($from_date, $to_date, $staff_approved_status, $staff_id);
    echo json_encode($result);
  }

  public function get_document_detailsbyid(){
    $result = $this->Staff_Model->get_document_detailsbyid($_POST);
    echo json_encode($result);
  }

  public function update_approved_status_using_parameter(){
    $status = $_POST['status'];
    $tablename = $_POST['tablename'];
    $remarks = $_POST['remarks'];
    $attribute_id = $_POST['attribute_id'];
    echo $this->Staff_Model->update_approved_status_using_parameter($status, $tablename, $attribute_id, $remarks);
  }

  private function _resize_image($file, $max_resolution, $type){
    if (file_exists($file)) {
      if ($type == 'image/jpeg')
        $original_image = imagecreatefromjpeg($file);
      else
        $original_image = imagecreatefrompng($file);

      //check orientation 
      // $exif = exif_read_data($file);

      try {
        $exif = exif_read_data($file);
      } catch (Exception $exp) {
        $exif = false;
      }

      if ($exif) {
        if (!empty($exif['Orientation'])) {
          switch ($exif['Orientation']) {
            case 3:
              $original_image = imagerotate($original_image, 180, 0);
              break;

            case 6:
              $original_image = imagerotate($original_image, -90, 0);
              break;

            case 8:
              $original_image = imagerotate($original_image, 90, 0);
              break;
          }
        }
      }

      //resolution
      $original_width = imagesx($original_image);
      $original_height = imagesy($original_image);

      //try width first
      $ratio = $max_resolution / $original_width;
      $new_width = $max_resolution;
      $new_height = $original_height * $ratio;

      //if that dosn't work
      if ($new_height > $max_resolution) {
        $ratio = $max_resolution / $original_height;
        $new_height = $max_resolution;
        $new_width = $original_width * $ratio;
      }
      
      if ($original_image) {
        $new_image = imagecreatetruecolor($new_width, $new_height);
        imagecopyresampled($new_image, $original_image, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
        if ($type == 'image/jpeg')
          imagejpeg($new_image, $file);
        else
          imagepng($new_image, $file);
      }

      return $file;
      // echo '<br>Resized: ';
      // echo filesize($file); 

      // echo '<pre>'; print_r($file); die();
    }
  }

  public function staff_docs_report() {
    $data['all_staff']= $this->Staff_Model->get_all_staff();
    $data['main_content'] = 'staff/staff_report/staff_document_report';
    $this->load->view('inc/template', $data);
  }

  public function get_staff_docs_by_staff_id() {
    $selected_staff_id = $_POST['selected_staff_id'];
    $documents = $this->Staff_Model->get_staff_docs_by_staff_id($selected_staff_id);
    echo json_encode($documents);
  }

  public function get_staff_document_types() {
    $result= $this->Staff_Model->get_staff_document_types();
    // echo '<pre>'; print_r($result); die();
    echo json_encode($result);
  }

  public function manage_student_health_fields(){
    $fields = $this->db->list_fields('student_health');
    $fData = [];
    $exclude = ['id', 'student_id', 'created_on', 'modified_on', 'last_modified_by', 'academic_year_id'];
    foreach ($fields as $field){
      if (!in_array($field, $exclude)) {
        array_push($fData, $field);
      } 
    }
    // echo "<pre>"; print_r($fData); die();
    $data['fields'] = $fData;
    $data['selected_required_fields'] = $this->Staff_Model->get_health_required_fields();
    $data['selected_disabled_fields'] = $this->Staff_Model->get_health_disabled_fields();
    $data['main_content'] = 'staff/manage_student_health_fields';
    $this->load->view('inc/template', $data);
  }

  public function health_configure_fields(){
    $result = $this->Staff_Model->insert_health_configure_fields();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', "Successfully inserted");
    }else{
      $this->session->set_flashdata('flashError', "Something went wrong");
    }
    redirect('staff/Staff_controller/manage_student_health_fields');
  }

  function manage_staff_departments() {
    $data['staffs']= $this->Staff_Model->getStaffs();
    $data['isSuperAdmin']= $this->authorization->isSuperAdmin();
    $data['isPOModuleEnabled'] = $this->authorization->isModuleEnabled('PROCUREMENT') && $this->authorization->isAuthorized('PROCUREMENT.MODULE');
    $data['main_content'] = 'staff/staff_registration/manage_staff_departments_view.php';
    $this->load->view('inc/template', $data);
  }

  public function add_staff_department_types() {
    $department_name= $_POST['department_name'];
    $department_status= isset($_POST['department_status']) ? $_POST['department_status'] : 1;
    $hod= isset($_POST['hod']) ? $_POST['hod'] : 1;
    echo $this->Staff_Model->add_staff_department_types($department_name, $department_status, $hod);
  }

  public function get_staff_department_types() {
    echo json_encode($this->Staff_Model->get_staff_department_types());
  }

  public function edit_staff_department_status() {
    echo $this->Staff_Model->edit_staff_department_status();
  }

  public function delete_department_type() {
    echo $this->Staff_Model->delete_department_type();
  }

  function manage_staff_designations() {
    $data['isSuperAdmin']= $this->authorization->isSuperAdmin();
    $data['main_content'] = 'staff/staff_registration/manage_staff_designations_view.php';
    $this->load->view('inc/template', $data);
  }

  public function add_staff_designation_types() {
    $designation_name= $_POST['designation_name'];
    $designation_status= isset($_POST['designation_status']) ? $_POST['designation_status'] : 1;
    echo $this->Staff_Model->add_staff_designation_types($designation_name, $designation_status);
  }

  public function get_staff_designation_types() {
    echo json_encode($this->Staff_Model->get_staff_designation_types());
  }

  public function edit_staff_designation_status() {
    echo $this->Staff_Model->edit_staff_designation_status();
  }

  public function delete_designation_type() {
    echo $this->Staff_Model->delete_designation_type();
  }

  public function get_staff_names_docs_by_id(){
    echo json_encode($this->Staff_Model->get_staff_names_with_docs($_POST['document_name'],$_POST['staff_status']));
  }

  public function save_staff_document(){
    echo $this->Staff_Model->save_staff_document($_POST['document_name'],$_POST['staff_id'],$_POST['path']);
  }

  public function update_designation_type(){
    echo $this->Staff_Model->update_designation_type($_POST['designation_id'],$_POST['designation_type']);
  }
  
  public function update_department_name(){
    $hod_id= isset($_POST['hod']) && $_POST['hod'] != '' ? $_POST['hod'] : NULL;
    echo $this->Staff_Model->update_department_name($_POST['department_id'],$_POST['department_name'],$hod_id);
  }

  public function get_documents_by_name(){
    $result = $this->Staff_Model->get_staff_names_with_docs($_POST['document_name'],$_POST['staff_status']);
    $temp_arr = array();
    if(!empty($result)){
       foreach($result as $key => $val){
         if(empty($val->document_url) && !empty($val->employee_code)){
          array_push($temp_arr,$val);
         }
       }
    }
    echo json_encode($temp_arr);
  }

  public function getAddressInfo()
  {
    $info_details = $_POST['info_details'];
    list($stakeholder_id, $avatar_type) = explode('_', $info_details);
    $addInfo = $this->Student_Model->getAddressInfo($stakeholder_id, $avatar_type);
    echo json_encode($addInfo);
  }

  public function save_department_approvers(){
    echo $this->Staff_Model->save_department_approvers($this->input->post());
  }

  // Combined AJAX endpoint for both staff data and counts
  public function get_provision_staff_data_and_counts() {
      $staffs = $this->Staff_Model->getStaffData();
      $data = [];
      $counts = ['total' => 0, 'activated' => 0, 'loggedIn' => 0, 'not_activated' => 0];
      foreach ($staffs as $staff) {
          $data[] = [
              'staffId' => $staff->staffId,
              'userId' => $staff->userId,
              'staffName' => $staff->staffName,
              'username' => $staff->username,
              'contact_number' => $staff->contact_number,
              'email' => $staff->email,
              'active' => $staff->active,
              'loggedin_atleast_once' => $staff->loggedin_atleast_once,
          ];
          $counts['total']++;
          if ($staff->logged_in) {
              $counts['loggedIn']++;
          } else {
              if ($staff->status == 'Activated') {
                  $counts['activated']++;
              } else {
                  $counts['not_activated']++;
              }
          }
      }
      echo json_encode(['staffs' => $data, 'counts' => $counts]);
  }
}
