<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

$CI =& get_instance();
$CI->load->library('authorization');
$CI->authorization->getStaffPermissionsArray();

if(!function_exists('checkTilePermissions')) {
	function checkTilePermissions($tiles) {
		foreach ($tiles as $i => $tile) {
	        if(!$tile['permission']) {
	          unset($tiles[$i]);
	        }
	    }
	    return $tiles;
	}
}

if(!function_exists('formatAddress')) {
	function formatAddress($address) {
		if(empty($address)) return '';
		$formatted_address = $address->Address_line1.', ';
		if($address->Address_line2 != '') {
			$formatted_address .= $address->Address_line2.', ';
		}
		$formatted_address .= $address->area.', ';
		$formatted_address .= $address->district.', ';
		$formatted_address .= $address->state.', ';
		$formatted_address .= $address->country.' - ';
		$formatted_address .= $address->pin_code;
		$formatted_address = str_replace(", ,", ", ", $formatted_address);
		return $formatted_address;
	}
}