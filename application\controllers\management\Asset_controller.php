<?php
  class Asset_controller extends CI_Controller
  {
    function __construct()
    {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      //This should come up only for super admins
      // if (!$this->authorization->isSuperAdmin()) {
      //   redirect('dashboard', 'refresh');
      // }
      $this->load->model('asset_model');
      $this->load->model('library_model');
      $this->config->load('form_elements');
    }

    //Landing function to display Assets
    public function view_assets()
    {
      $data['assetList'] = $this->asset_model->get_asset_list();
      // echo '<pre>'; print_r($data); die();
      $data['main_content'] = 'management/assets/view_assets';
      $this->load->view('inc/template', $data);
    }

    //Add new Asset
    public function addAssetInfo()
    {
      $data['assetData'] = $this->asset_model->get_asset_list();
      $data['categoryData'] = $this->asset_model->get_category_list();
      $data['main_content'] = 'management/assets/addAsset';
      $this->load->view('inc/template', $data);
    }

    //submit the new asset
    public function submitAsset()
    {
      $status = (int)$this->asset_model->addNewAsset();
      if ($status) {
        $this->session->set_flashdata('flashSuccess', 'Asset Added Successfully');
      } else {
        $this->session->set_flashdata('flashError', 'Unable to Add Asset');
      }
      //echo $status;
      redirect('management/asset_controller/addAssetInfo');
    }

    //edit Asset Details
    public function EditAssetInfo($id)
    {
      $data['assetList'] = $this->asset_model->get_asset_list($id);
      // echo '<pre>'; print_r($data); die();
      $data['main_content'] = 'management/assets/editAsset';
      $this->load->view('inc/template', $data);
    }

    //update Asset Details
    public function updateAsset()
    {
      $this->db->trans_begin();
      $status = (int)$this->asset_model->updateAssetInfo();

      switch ($status) {
        case -1:
          $this->db->trans_rollback();
          $this->session->set_flashdata('flashError', 'Asset count should be more than Allocated assets');
          break;
        case 1:
          $this->db->trans_commit();
          $this->session->set_flashdata('flashSuccess', 'Asset Details Successfully Updated.');
          break;
        default:
          $this->db->trans_rollback();
          $this->session->set_flashdata('flashError', 'Something Went Wrong!');
          break;
      }

      redirect('management/asset_controller');
    }

    //Delete Asset Info
    public function deleteAssetInfo($id)
    {
      $status = (int)$this->asset_model->deleteAsset($id);

      switch ($status) {
        case -1:
          $this->db->trans_rollback();
          $this->session->set_flashdata('flashError', 'This asset is allocated to some room.');
          break;
        case 1:
          $this->db->trans_commit();
          $this->session->set_flashdata('flashSuccess', 'Asset Details Successfully Deleted.');
          break;
        default:
          $this->db->trans_rollback();
          $this->session->set_flashdata('flashError', 'Something Went Wrong!');
          break;
      }

      redirect('management/asset_controller');
    }

    public function collect_assets()
    {
      $data['staffAllocatedAssets'] = $this->asset_model->staff_allocated_assets();
      $data['roomAllocatedAssets'] = $this->asset_model->room_allocated_assets();

      $data['main_content'] = 'management/assets/collect_asset';
      $this->load->view('inc/template', $data);
    }
    public function getAllocationDetails()
    {
      //echo '<pre>'; print_r($_POST); die();

    }


    public function allocate_assets()
    {

      $data['blocksList'] = $this->asset_model->get_building_details(FALSE, FALSE, FALSE, 'block');
      $data['assetList'] = $this->asset_model->get_asset_details();
      $data['staffDetails']  = $this->asset_model->getStaffDetails();

      $data['staffAllocatedAssets'] = $this->asset_model->staff_allocated_assets();
      $data['roomAllocatedAssets'] = $this->asset_model->room_allocated_assets();
      // echo "<pre>"; print_r($data); die();

      $data['main_content'] = 'management/assets/allocateAssets';
      $this->load->view('inc/template', $data);
    }

    public function getFloorsByBlock()
    {
      $block = $_POST['block'];
      $floorData = $this->asset_model->get_building_details(FALSE, $block, FALSE, 'floor');

      echo json_encode($floorData);
    }

    public function getRoomsByFloor()
    {
      $floor = $_POST['floor'];
      $block = $_POST['block'];

      $roomData = $this->asset_model->get_building_room_no_details($floor, $block, FALSE, 'room_no');
      echo json_encode($roomData);
    }

    public function getAssetCountByid()
    {
      $asset_id = $_POST['asset_id'];
      // echo $asset_id; 
      $assetData = $this->asset_model->get_asset_details2($asset_id, 'available_assets');
      echo $assetData[0]->available_assets;
    }

    //allocate asset to room
    public function submitAssetAllocation($asset_id)
    {
      // echo "<pre>"; print_r($_POST); die();
      $result = $this->asset_model->submitAssetAllocation($asset_id);
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Asset(s) Allocated Successfully.');
      } else {
        $this->session->set_flashdata('flashError', 'Asset(s) Allocation failed as it is already alloted.');
      }
      redirect("management/asset_controller/auditLogThisAsset/" . $asset_id);



      // $block = $_POST['block'];
      // $floor = $_POST['floor'];
      // $room_no = $_POST['room_no'];
      // $asset = $_POST['asset_name'];
      // $required = $_POST['required'];

      // //get room_id 
      // $roomData = $this->asset_model->get_building_details($floor, $block, $room_no,'room_id');
      // $room_id = $roomData[0]->room_id;

      // //get asset_id and asset_count
      // $asset_idObj = $this->asset_model->get_asset_details($asset,'asset_id');
      // $asset_id = $asset_idObj[0]->asset_id;

      // $result = $this->asset_model->check_room_asset($room_id, $asset_id);
      // if(!empty($result)){
      //   $req = $required + $result[0]->asset_count;
      //   $status = $this->asset_model->updateAssetsToRooms($room_id, $asset_id,$req);
      // } else {
      //   $status = $this->asset_model->allocateAssetsToRooms($room_id, $asset_id,$required);
      // }

      // $assetCount = $this->asset_model->get_asset_details($asset,'available_assets,allocated_assets');

      // $available_count = $assetCount[0]->available_assets;
      // $allocated_count = $assetCount[0]->allocated_assets;
      // $available_count = $available_count - $required;
      // $allocated_count = $allocated_count + $required;
      // $remove = $this->asset_model->update_asset_count($asset_id, $available_count, $allocated_count);
      // redirect('management/Asset_controller/allocate_assets');

    }

    //Remove the allocated asset
    public function deleteAssetAllocation($asset_id, $room_id)
    {
      $result = $this->asset_model->check_room_asset($room_id, $asset_id);
      $asset_count = $result[0]->asset_count;

      $status = $this->asset_model->removeAssetAllocation($asset_id, $room_id, $asset_count);

      if ($status != 0) {
        $this->db->trans_commit();
        $this->session->set_flashdata('flashSuccess', 'Asset Allocation Data Successfully Removed.');
        redirect('management/Asset_controller/allocate_assets');
      } else {
        $this->db->trans_rollback();
        $this->session->set_flashdata('flashError', 'Unable to Remove Asset Allocation Data.');
        redirect('management/Asset_controller/allocate_assets');
      }
    }

    public function collect_back_asset($id, $asset_id, $asset_tag, $source)
    {
      $result = $this->asset_model->collect_back_asset($id, $asset_id, $asset_tag);
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Asset Collected Successfully.');
      } else {
        $this->session->set_flashdata('flashError', 'Asset Collection Failed.');
      }
      if ($source == 1)
        redirect('management/Asset_controller/allocate_assets');
      else
        redirect('management/Asset_controller/collect_assets');
    }
    public function collect_this_asset_back_details()
    {
      $id=$_POST['id'];
      $asset_id=$_POST['asset_id'];
      $asset_tag=$_POST['asset_tag'];
      // echo "<pre>";print_r($id);
      // echo "<pre>";print_r($asset_id);
      // echo "<pre>";print_r($asset_tag);

      // die();
      $result = $this->asset_model->collect_back_asset($id, $asset_id, $asset_tag);
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Asset Collected Successfully.');
      } else {
        $this->session->set_flashdata('flashError', 'Asset Collection Failed.');
      }
      echo $result;
    }
    public function collect_this_asset_back($id, $asset_id, $asset_tag, $source)
    {
      $result = $this->asset_model->collect_back_asset($id, $asset_id, $asset_tag);
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Asset Collected Successfully.');
      } else {
        $this->session->set_flashdata('flashError', 'Asset Collection Failed.');
      }
      
        redirect('management/Asset_controller/collectThisAsset/'.$asset_id);
      // if ($source == 1)
      //   //redirect('management/Asset_controller/allocate_assets');
      // else
      //   redirect('management/Asset_controller/collect_assets');
    }

    public function collectThisAsset($asset_id)
    {
      $data['assetCurrentStaffAllocation'] = $this->asset_model->assetCurrentStaffAllocation($asset_id);
      $data['assetCurrentRoomAllocation'] = $this->asset_model->assetCurrentRoomAllocation($asset_id);
      // echo "<pre>"; print_r($data['assetCurrentStaffAllocation'][0]->asset_name); die();

      $data['asset_id'] = $asset_id;
      // $data['asset_name'] = $this->asset_model->getAsset_name($asset_id);
      $data['main_content'] = 'management/assets/collectThisAsset';
      $this->load->view('inc/template', $data);
    }

    public function discard_asset()
    {
      $data['main_content'] = 'management/assets/discardAssets';
      $this->load->view('inc/template', $data);
    }
    public function search_asset()
    {
      $data['main_content'] = 'management/assets/searchAssets';
      $this->load->view('inc/template', $data);
    }

    public function getAssetDetails()
    {
      $assetDetails = $this->asset_model->getAssetTagDetails($_POST['assetTag']);
      // $assetDetails = $this->asset_model->getAssetDetails('npsagaast37');
      //echo "<pre>"; print_r($assetDetails); die();
      return json_encode($assetDetails);
    }

    public function assetDetails($asset_id)
    {
      $data['assetDetails'] = $this->asset_model->getAssetDetails($asset_id);
      $data['asset_tags'] = $this->asset_model->getAssetTagsTodDiscard($asset_id);
      $data['asset_name'] = $this->asset_model->getAsset_name($asset_id);
      $data['asset_id'] = $asset_id;
      // echo '<pre>'; print_r($data); die();
      
      $data['main_content'] = 'management/assets/assetDetails';
      $this->load->view('inc/template', $data);
    }

    public function allotThisAsset($asset_id,$asset_tag,$asset_name)
    {
      $data['blocksList'] = $this->asset_model->get_building_details(FALSE, FALSE, FALSE, 'block');
      // $data['assetList'] = $this->asset_model->get_asset_details();
      $data['staffDetails']  = $this->asset_model->getStaffDetails();
      $data['assetTags'] = $this->asset_model->getAssetTags($asset_id,$asset_tag);
      $data['asset_id'] = $asset_id;
      $data['asset_name'] = $this->asset_model->getAsset_name($asset_id);;

      $data['staffAllocatedAssets'] = $this->asset_model->staff_allocated_assets();
      $data['roomAllocatedAssets'] = $this->asset_model->room_allocated_assets();
      // echo "<pre>"; print_r($data); die();

      $data['main_content'] = 'management/assets/allocateThisAssets';
      $this->load->view('inc/template', $data);
      // echo "ho"; die();
    }

    public function assetCategory()
    {
      $data['categories_list'] = $this->asset_model->getCategoriesList();
      $data['main_content'] = 'management/assets/asset_category';
      $this->load->view('inc/template', $data);
    }
    
    public function addAssetCategory()
    {
      $cat_name= $_POST['category_name'];
      echo $this->asset_model->addAssetCategory($cat_name);
    }

    public function get_subassetCategory(){
        $categories= $this->asset_model->getCategoriesList();
        echo json_encode($categories);
    }

    public function edit_asset_category() {
        $cat_id= $_POST['id'];
        $cat_name= $_POST['name'];
       echo $this->asset_model->edit_asset_category($cat_id, $cat_name);

    }

    public function edit_asset_name() {
      $asset_id= $_POST['id'];
        $asset_name= $_POST['name'];
       echo $this->asset_model->edit_asset_name($asset_id, $asset_name);
    }
    
    public function move_asset_category() {
      $asset_id= $_POST['asset_id'];
        $category_id= $_POST['category_id'];
       echo $this->asset_model->move_asset_category($asset_id, $category_id);
    }

    public function deactivate_asset_category() {
      $cat_id= $_POST['id'];
      $is_deactive= $this->asset_model->deactivate_asset_category($cat_id);
      // echo '<pre>'; echo print_r($is_deactive); die();
      echo json_encode($is_deactive);
    }

    public function activate_asset_category() {
      $cat_id= $_POST['id'];
      $is_active= $this->asset_model->activate_asset_category($cat_id);
      echo json_encode($is_active);
    }
    
    public function old_add_asset(){
      $data['blocksList'] = $this->asset_model->get_building_details(FALSE, FALSE, FALSE, 'block');
      $data['categoryData'] = $this->asset_model->get_category_list();
      $data['staffDetails']  = $this->asset_model->getStaffDetails();
      $data['main_content'] = 'management/assets/old_add_assset';
      $this->load->view('inc/template',$data);
    }
    /*public function discardThisAsset($asset_id){
      $data['asset_tags'] = $this->asset_model->getAssetTagsTodDiscard($asset_id);
      $data['asset_name'] = $this->asset_model->getAsset_name($asset_id);
      //echo '<pre>'; print_r($data); die();
      $data['asset_id'] = $asset_id;
      $data['main_content'] = 'management/assets/discardThisAsset';
      $this->load->view('inc/template', $data);
    }*/

    public function old_assetsQRCode(){
      $data['main_content']='management/assets/assets_qr_print';
      $this->load->view('inc/template',$data); 
    }

    public function assetsQRCode($asset_id){
      $data['asset_tags']=$this->asset_model->getAssetTagsTodDiscard($asset_id);
      $data['asset_name'] = $this->asset_model->getAsset_name($asset_id);
      $data['asset_id'] = $asset_id;
      //echo "<pre>";print_r($data);die();
      $data['main_content'] = 'management/assets/assetsQRCode';
      $this->load->view('inc/template', $data);
    }

    public function assetsQRCodeGenerate($asset_id){
      $data['asset_tags']=$this->asset_model->getAssetTagsTodDiscard($asset_id);
      $data['asset_name'] = $this->asset_model->getAsset_name($asset_id);
      $data['asset_id'] = $asset_id;
      $input=$this->input->post();
      $data['qr_code']=$input['asset_tags'];
      //echo "<pre>";print_r($data);die();
      $data['main_content'] = 'management/assets/assetsQRCode';
      $this->load->view('inc/template', $data);
    }

    public function discard_this_asset_tag($asset_tag, $asset_id){
      $username=$this->authorization->getUsername();
      $reason=$_POST[$asset_tag];
      $result= $this->asset_model->discard_this_asset_tag($asset_tag, $asset_id,$username,$reason);
      if($result)
        $this->session->set_flashdata('flashSuccess', 'Successfully Discarded');
      else
        $this->session->set_flashdata('flashError', 'Something went wrong');
        redirect('management/asset_controller/assetDetails/'.$asset_id); 
    }
    
    public function asset_allocation_report(){
      $data['staffAllocatedAssets'] = $this->asset_model->current_staff_allocated_assets();
      $data['roomAllocatedAssets'] = $this->asset_model->current_room_allocated_assets();
      $data['main_content'] = 'management/assets/asset_allocation_report';
      $this->load->view('inc/template', $data);
    }
    
    public function asset_discard_report(){
      $data['discarded_assets'] = $this->asset_model->getDiscardedAssets();
      // echo '<pre>'; print_r($data); die();
      $data['main_content'] = 'management/assets/asset_discard_report';
      $this->load->view('inc/template', $data);
    }

    public function asset_depreciation_report(){
      $data['assetDepreciation'] = $this->asset_model->asset_depreciation_data();
      $data['main_content'] = 'management/assets/asset_depreciation_report';
      $this->load->view('inc/template', $data);
    }

    public function room_wise_allocation(){
      $data['blocksList']= $this->asset_model->get_building_details(FALSE, FALSE, FALSE, 'block');
      $data['roomAllocatedAssets'] = $this->asset_model->current_room_allocated_assets();
      $data['main_content'] = 'management/assets/room_wise_allocation';
      $this->load->view('inc/template', $data);
    }

    public function staff_wise_allocation(){
      $data['staff_details']=$this->asset_model->getStaffDetails();
      $data['staffAllocatedAssets'] = $this->asset_model->current_staff_allocated_assets();
      $data['main_content'] = 'management/assets/staff_wise_allocation';
      $this->load->view('inc/template', $data);
    }

    public function single_staff_wise_allocation_display(){
      $staff_id = $_POST['staffid'];
      if($staff_id== 0)
      {
        $data = $this->asset_model->current_staff_allocated_assets();
      }
      else
      {
        $data = $this->asset_model->single_staff_allocated_assets($staff_id);
      }
      echo json_encode($data);
    }
    
    public function single_staff_wise_allocation(){
      $data['staff_details']=$this->asset_model->getStaffDetails();
      $data['main_content']='management/assets/single_staff_wise_allocation';
      $this->load->view('inc/template',$data);
    }

    public function auditLogThisAsset($asset_id){
      // $data['asset_audit'] = $this->asset_model->getAssetHistory($asset_id);
      $assets = $this->asset_model->getAssetHistory($asset_id);
      $data['asset_audit'] = array();
      foreach($assets as $key => $asset) {
        if(!array_key_exists($asset->asset_tag, $data['asset_audit'])) {
          $data['asset_audit'][$asset->asset_tag] = array();
        }
        array_push($data['asset_audit'][$asset->asset_tag], $asset);
      }
      $data['asset_name'] = $this->asset_model->getAsset_name($asset_id);
      $data['asset_id'] = $asset_id;
      // echo '<pre>'; print_r($data); die();

      $data['main_content'] = 'management/assets/asset_audit';
      $this->load->view('inc/template', $data);


    }

    public function last_digit_qr(){
      $input = $this->input->post();

      if (empty($input['to'])) {
        redirect('library_controller/print_qr_code_books');
      }
      // $result = $this->library_model->get_last_number_to_generate($lastId);        
      for($i=$input['from']; $i <= $input['to'] ; $i++){
        $qrCode[] = $input['format'].sprintf("%'.0".$input['no_of_digit']."d",$i);
      }

      $lastId = $this->library_model->insert_last_running_number();
      $data['running_num'] = $this->library_model->get_last_number();
      // echo "<pre>"; print_r($data['running_num']); die();
      $data['print_cart'] = $qrCode;
      $data['copies'] = $input['no_of_copies'];
      $data['main_content']='management/assets/assets_qr_print';
      $this->load->view('inc/template',$data);
  }
    // improvements 

    // 3. In view asset have details for every asset and in detail have asset allocation, collection, discard, and history(audit log). Done and Few things are pending
    // 7. Have a radio button for room/staff allocation Done
    // 2. Get the available asset tags as drop down in allot asset Done


    // 4. add asset category tile  Done


    // 5. remove collect asset and discard asset from main view Done 

    // 6. add a search box in the header of asset management
    // 8. Have an audit log


    // 1. Forms as Parent Initiative in demo school parent login
    // 9. Remove asset availability report and reports should be as fee reports/ daily reports with filters 

    public function getAssetsByCategory(){
      $data = $this->asset_model->getAssetsByCategory();
      echo json_encode($data);
    }
    
    public function addNewCategory(){
      $data = $this->asset_model->addNewCategory();
      echo json_encode($data);
    }

    public function addNewAsset(){
      $data = $this->asset_model->Old_addNewAsset();
      echo json_encode($data);
    }
  
    public function old_submitAsset(){
      $data = $this->asset_model->old_submitAsset();
      if($data)
        $this->session->set_flashdata('flashSuccess', 'Successfully Added & Allocated');
      else
        $this->session->set_flashdata('flashError', 'Something went wrong');
      echo json_encode($data);
      // redirect('management/asset_controller/old_add_asset');
    }

    public function submit_new_tag_for_existing_asset() {
      $data['result'] = $this->asset_model->submit_new_tag_for_existing_asset($_POST['asset_id'], $_POST['asset_tag'], $_POST['asset_purchase_date'], $_POST['asset_cost'], $_POST['asset_deprecation_period'], $_POST['deprecation_percentage'], $_POST['asset_warranty_period']);

      $data['asset_id'] = $_POST['asset_id'];

      echo json_encode($data);
    }

    public function getExistingAssetsByStaff(){
      $staff_id = $_POST['staff_id'];
      $data['total_assets'] = $this->asset_model->getTotalAssetsAllotedByStaff($staff_id);
      $data['ExistingAssets'] = $this->asset_model->getExistingAssetsByStaff($staff_id);
      // echo "<pre>";print_r($data);die();
      echo json_encode($data);
    }

    public function getExistingUnallocateAssets(){
      $unAllocateAssets = $this->asset_model->getExistingUnAllocatedAsseteData();
      echo json_encode($unAllocateAssets);
    }
    public function getExistingAssetsByRoom(){
      $block = $_POST['block'];
      $floor = $_POST['floor'];
      $room =  $_POST['room'];
      $data['ExistingAssets'] = $this->asset_model->getExistingAssetsByRoom();
      $data['total_assets'] = $this->asset_model->getTotalAssetsAllotedByRoom($block,$floor,$room);
      // echo "<pre>";print_r($data);die();
      echo json_encode($data);
    }

    public function getExistingAssetTagsByStaff(){
      $asset_id = $_POST['asset_id'];
      $allocating_to = $_POST['allocating_to'];
      $data = $this->asset_model->getExistingAssetTagsByStaff($asset_id,$allocating_to);
      echo json_encode($data);
    }
    public function check_AssetTag(){
      $data = $this->asset_model->check_AssetTag();
      if(empty($data)){
        echo "0";
      }
      else{
        echo "1";
      }
    }

    public function category_wise_report(){
      $data['blocksList']= $this->asset_model->get_building_details(FALSE, FALSE, FALSE, 'block'); 
      $data['staffDetails']  = $this->asset_model->getStaffDetails();
      $data['categories_list'] = $this->asset_model->getCategoriesList();
      $data['assetList'] = $this->asset_model->get_categoryReport_list();
      // echo "<pre>";print_r($data);die();
      $data['main_content']='management/assets/category_wise_report_mod';
      $this->load->view('inc/template',$data);
    }

    public function getSelectedCategory(){
      $data = $this->asset_model->getSelectedCategory();
      echo json_encode($data);
    }

    public function available_assets_report(){
      $asset_id = $_POST['asset_id'];
      $data['assetDetails'] = $this->asset_model->getAssetDetails($asset_id);
      $data['asset_tags'] = $this->asset_model->getAssetTags_report($asset_id);
      $data['asset_name'] = $this->asset_model->getAsset_name($asset_id);
      echo json_encode($data);
    }

    public function allocated_assets_report(){
      $asset_id = $_POST['asset_id'];
      $data['assetDetails'] = $this->asset_model->getAssetDetails($asset_id);
      $data['asset_tags'] = $this->asset_model->getAssetTags_allocate_report($asset_id);
      $data['asset_name'] = $this->asset_model->getAsset_name($asset_id);
      echo json_encode($data);
    }

    public function auditLogThisAssetTag(){
      $asset_id = $_POST['asset_id'];
      $asset_tag = $_POST['asset_tag'];
      $data['assets'] = $this->asset_model->getAssetTagHistory($asset_id,$asset_tag);
      $data['asset_name'] = $this->asset_model->getAsset_name($asset_id);
      $data['asset_id'] = $asset_id;
      echo json_encode($data);
    }

    public function asset_details_report(){
      $asset_id = $_POST['asset_id'];
      $data['asset_details'] = $this->asset_model->asset_details_report($asset_id);
      $data['asset_tag_details'] = $this->asset_model->assetTag_details_report($asset_id);
      $data['asset_name'] = $this->asset_model->getAsset_name($asset_id);
      $data['is_super_admin'] = $this->authorization->isSuperAdmin();
      echo json_encode($data);
    }

    public function discard_asset_report(){
      $username=$this->authorization->getUsername();
      $reason=$_POST['reason'];
      $asset_id=$_POST['asset_id'];
      $asset_tag = $_POST['asset_tag'];
      $result=$this->asset_model->discard_this_asset_tag($asset_tag, $asset_id,$username,$reason);
      echo json_encode($data);
    }

    public function assets_roomwise_allocation(){
      $data = $this->asset_model->assets_roomwise_allocation();
      echo json_encode($data);
    }

    public function submitAssetAllocation_report()
    {
      $result = $this->asset_model->submitAssetAllocation_report();
      echo json_encode($data);
    }

    public function assetTag_Report(){
      $asset_tag =$_POST['asset_tag'];
      $data['asset_tag_details'] = $this->asset_model->getAssetTagDetails($asset_tag);
      $asset_id = $data['asset_tag_details']['asset_id'];
      $data['asset_tag_history'] = $this->asset_model->getAssetTagHistory($asset_id,$asset_tag);
      $data['blocksList']= $this->asset_model->get_building_details(FALSE, FALSE, FALSE, 'block');
      $data['staffDetails']  = $this->asset_model->getStaffDetails();
      $data['main_content']='management/assets/single_assetTag_Report';
      $this->load->view('inc/template',$data);
    }

    public function move_to_repair(){
      $data = $this->asset_model->move_to_repair();
      echo json_encode($data);
    }

    public function repair_done_update(){
      $data = $this->asset_model->repair_done_update();
      echo json_encode($data);
    }
    
    public function getAssetTagDetails(){
      $asset_id = $_POST['asset_id'];
      $asset_tag = $_POST['asset_tag'];
      $data['asset_name'] = $this->asset_model->getAsset_name($asset_id);
      $data['asset_tag_details']=$this->asset_model->getSingleAssetTagDetails($asset_tag);
      $repair_id = $data['asset_tag_details'][0]->repair_id;
      $data['repair_status'] = $this->asset_model->getRepairStatus($repair_id);
      $allotted_to= $this->asset_model->getAllocatedTo($asset_id,$asset_tag);
      if($allotted_to==''){
        $data['allotted_to'] = ' ';
      }
      else{
        $data['allotted_to'] = $allotted_to;
      }
      // echo "<pre>";print_r($data);die();
      echo json_encode($data);
    }

    public function getRepairLog(){
      $data = $this->asset_model->getRepairLog();
      echo json_encode($data);
    }

    public function get_asset_category_wise() {
      $selected_category_id = $_POST['selected_category_id'];
      $assets= $this->asset_model->get_asset_category_wise($selected_category_id);
      echo json_encode($assets);
    }

    public function move_asset_tag() {
      $new_asset_id = $_POST['new_asset_id'];
      $allocated = $_POST['allocated'];
      $asset_tag_id = $_POST['asset_tag_id'];
      $old_asset_id = $_POST['old_asset_id'];
      $asset_tag_name = $_POST['asset_tag_name'];
      
      $assets= $this->asset_model->move_asset_tag($new_asset_id, $asset_tag_id, $old_asset_id, $allocated, $asset_tag_name );
      echo $assets;
    }

     public function save_new_asset() {
      $selected_cat_id = $_POST['selected_cat_id'];
      $created_asset_name = $_POST['created_asset_name'];
      $asset= $this->asset_model->save_new_asset($selected_cat_id, $created_asset_name);
      echo $asset;
    }

  }
?>