<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

if(!function_exists('chatdata_helper')) {
    function getChatData($module='') {
    	$url = 'https://demoschool.localhost.in/oxygenv2/api/Help_data/getHelpData';
    	if (ENVIRONMENT === 'production') {
        	$url = 'https://demoschool.schoolelement.in/api/Help_data/getHelpData';
        }
        $url = 'https://demoschool.schoolelement.in/api/Help_data/getHelpData';
        $CI =& get_instance();
        $response = $CI->curl->simple_post($url,json_encode(['module' => $module]));
        return json_decode($response);
    }
}