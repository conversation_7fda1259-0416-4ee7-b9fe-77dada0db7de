<?php
/**
 * Dynamic Profile Section Component
 * Renders profile sections based on configuration
 */

if (!isset($profileDisplayConfig) || !isset($studentData)) {
    return;
}

$enabledFields = $profileDisplayConfig['enabled_fields'] ?? array();
$fieldLabels = $profileDisplayConfig['field_labels'] ?? array();
$fieldGroups = $profileDisplayConfig['field_groups'] ?? array();

// Helper function to check if field is enabled
function isFieldEnabled($field, $enabledFields) {
    return in_array($field, $enabledFields);
}

// Helper function to get field value
function getFieldValue($field, $studentData, $additionalData = array()) {
    switch ($field) {
        case 'STUDENT_NAME':
            return ucfirst($studentData->stdName ?? '');
        case 'STUDENT_PHOTO':
            $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
            if ($studentData->gender == 'M') {
                $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
            }
            return empty($studentData->picture_url) ? $picUrl : $studentData->picture_url;
        case 'ADMISSION_NO':
            return $studentData->admissionNo ?? 'Not available';
        case 'ENROLLMENT_NUMBER':
            return $studentData->enrollment_number ?? 'Not available';
        case 'ALPHA_ROLL_NUMBER':
            return $studentData->alpha_rollnum ?? 'Not available';
        case 'CLASS_SECTION':
            if (!empty($studentData->className) && !empty($studentData->sectionName)) {
                return $studentData->className . '/' . $studentData->sectionName;
            }
            return 'Not available';
        case 'STUDENT_DOB':
            return $studentData->dob ? date('d-M-Y', strtotime($studentData->dob)) : 'Not available';
        case 'STUDENT_GENDER':
            return $studentData->gender == 'M' ? 'Male' : ($studentData->gender == 'F' ? 'Female' : 'Not available');
        case 'STUDENT_EMAIL':
            return $studentData->student_email ?: 'Not available';
        case 'STUDENT_MOBILE_NUMBER':
            return $studentData->student_mobile_no ?: 'Not available';
        case 'PREFFERED_CONTACT_NUMBER':
            return $studentData->preferred_contact_no ?: 'Not available';
        case 'STUDENT_BLOOD_GROUP':
            return $studentData->blood_group ?: 'Not available';
        case 'STUDENT_NATIONALITY':
            return $studentData->nationality ?: 'Not available';
        case 'STUDENT_RELIGION':
            return ($studentData->religion && $studentData->religion != '0') ? $studentData->religion : 'Not available';
        case 'STUDENT_CASTE':
            return ($studentData->caste && $studentData->caste != '0') ? $studentData->caste : 'Not available';
        case 'CATEGORY':
            return ($studentData->category && $studentData->category != '0') ? $studentData->category : 'Not available';
        case 'STUDENT_MOTHER_TONGUE':
            return $studentData->mother_tongue ?: 'Not available';
        case 'STUDENT_STOP':
            if (isset($additionalData['stops']) && isset($studentData->stop) && !empty($additionalData['stops'][$studentData->stop])) {
                return $additionalData['stops'][$studentData->stop];
            }
            return 'Not available';
        case 'STUDENT_PICKUP_MODE':
            return $studentData->pickup_mode ?: 'Not available';
        case 'STUDENT_HOUSE':
            if ($studentData->student_house && $studentData->student_house != '0' && $studentData->student_house != null) {
                return ucwords($studentData->student_house);
            }
            return 'Not available';
        case 'COMBINATION':
            if ($studentData->combination_name && $studentData->combination_name != '0' && $studentData->combination_name != null) {
                return ucwords($studentData->combination_name);
            }
            return 'Not available';
        case 'STUDENT_AADHAR':
            return $studentData->aadhar_no ?: 'Not available';
        case 'NAME_AS_PER_AADHAR':
            return $studentData->name_as_per_aadhar ?: 'Not available';
        default:
            return 'Not available';
    }
}

// Helper function to render address
function renderAddress($addressData) {
    if (empty($addressData)) {
        return 'Not available';
    }
    
    $addressParts = array();
    foreach ($addressData as $type => $addresses) {
        foreach ($addresses as $address) {
            $parts = array_filter([
                $address->Address_line1,
                $address->Address_line2,
                $address->area,
                $address->district,
                $address->state,
                $address->country,
                $address->pin_code
            ]);
            if (!empty($parts)) {
                $addressParts[] = implode(', ', $parts);
            }
        }
    }
    
    return !empty($addressParts) ? implode('<br>', $addressParts) : 'Not available';
}

// Render each group
foreach ($fieldGroups as $groupKey => $group) {
    $groupFields = array_intersect($group['fields'], $enabledFields);
    
    if (empty($groupFields)) {
        continue; // Skip if no fields in this group are enabled
    }
    ?>
    
    <div class="profile-section">
        <div class="section-header">
            <h4><i class="fa <?php echo $group['icon']; ?> me-2"></i><?php echo $group['title']; ?></h4>
            <?php if ($groupKey == 'student_basic' && $studentData->profile_status == 'Unlock') { ?>
            <a href="<?php echo site_url('parent_controller/edit_profile_parent_mobile/'.$callFrom); ?>" class="edit-profile-btn">
                <i class="fa fa-pencil"></i>
            </a>
            <?php } ?>
        </div>
        <div class="section-content">
            <?php if ($groupKey == 'student_basic' && isFieldEnabled('STUDENT_PHOTO', $enabledFields) && isFieldEnabled('STUDENT_NAME', $enabledFields)) { ?>
            <!-- Student Header with Photo and Name -->
            <div class="student-header">
                <div class="student-photo">
                    <img src="<?php echo getFieldValue('STUDENT_PHOTO', $studentData); ?>" alt="Student Photo" class="student-avatar">
                </div>
                <div class="student-info">
                    <h3 class="student-name"><?php echo getFieldValue('STUDENT_NAME', $studentData); ?></h3>
                </div>
            </div>
            <?php } ?>
            
            <!-- Profile Details -->
            <div class="profile-details">
                <?php foreach ($groupFields as $field) {
                    if ($field == 'STUDENT_PHOTO' || $field == 'STUDENT_NAME') {
                        continue; // Already rendered in header
                    }
                    
                    $label = $fieldLabels[$field] ?? str_replace('_', ' ', $field);
                    $value = getFieldValue($field, $studentData, compact('stops'));
                    
                    if ($field == 'STUDENT_ADDRESS') {
                        $value = renderAddress($studentAddress ?? array());
                    }
                    ?>
                    <div class="detail-item">
                        <span class="detail-label"><?php echo $label; ?></span>
                        <span class="detail-value"><?php echo $value; ?></span>
                    </div>
                <?php } ?>
            </div>
        </div>
    </div>
    
<?php } ?>

<!-- Additional sections for family photo and remarks -->
<?php if (isFieldEnabled('FAMILY_PHOTO', $enabledFields)) { ?>
<div class="profile-section">
    <div class="section-header">
        <h4><i class="fa fa-camera me-2"></i>Family Photo</h4>
    </div>
    <div class="section-content">
        <?php 
          $family_pic = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/father.png';
          if (!empty($family_picture_url)) {
            $family_pic = $family_picture_url;
          }
        ?>
        <div class="family-photo-container">
            <img src="<?php echo $family_pic; ?>" alt="Family Photo" class="family-photo" />
        </div>
    </div>
</div>
<?php } ?>

<?php if (isFieldEnabled('STUDENT_REMARKS', $enabledFields)) { ?>
<div class="profile-section">
    <div class="section-header">
        <h4><i class="fa fa-comment me-2"></i>Remarks</h4>
    </div>
    <div class="section-content">
        <div class="remarks-text">
            <?php if (!empty($studentData->student_remarks)) {
              echo nl2br(htmlspecialchars($studentData->student_remarks));
            } else {
              echo '<span class="text-muted">No remarks available</span>';
            } ?>
        </div>
    </div>
</div>
<?php } ?>

<!-- Debug information (remove in production) -->
<?php if (ENVIRONMENT == 'development') { ?>
<div class="profile-section" style="background: #f8f9fa; border: 1px dashed #ccc;">
    <div class="section-header">
        <h4><i class="fa fa-bug me-2"></i>Debug Information</h4>
    </div>
    <div class="section-content">
        <div class="detail-item">
            <span class="detail-label">Enabled Fields</span>
            <span class="detail-value"><?php echo implode(', ', $enabledFields); ?></span>
        </div>
        <div class="detail-item">
            <span class="detail-label">Total Groups</span>
            <span class="detail-value"><?php echo count($fieldGroups); ?></span>
        </div>
    </div>
</div>
<?php } ?>
