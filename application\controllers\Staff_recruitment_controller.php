<?php 
// Career form implementaion
class Staff_recruitment_controller extends CI_Controller
{
    function __construct() {
       parent::__construct();
       $this->load->model('Staff_recruitment_model');
       $this->config->load('form_elements');
       $this->load->library('payment_application');
       $this->load->library('filemanager');

    } 

    public $columnList = [
        [
          'displayName'=>'Candidate_name',
          'columnNameWithTable'=>'src.first_name',
          'columnName'=>'first_name',
          'varName'=>'first_name',
          'table'=>'staff_recruitment_candidates',
          'index'=>'1',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Phone_number',
          'columnNameWithTable'=>'src.phone_number',
          'columnName'=>'phone_number',
          'varName'=>'phone_number',
          'table'=>'staff_recruitment_candidates',
          'index'=>'2',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Alt_Phone_num',
          'columnNameWithTable'=>'src.alt_phone_num',
          'columnName'=>'alt_phone_num',
          'varName'=>'alt_phone_num',
          'table'=>'staff_recruitment_candidates',
          'index'=>'3',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'email',
          'columnNameWithTable'=>'src.email',
          'columnName'=>'email',
          'varName'=>'email',
          'table'=>'staff_recruitment_candidates',
          'index'=>'4',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'linked_in_profile',
          'columnNameWithTable'=>'src.linked_in_profile',
          'columnName'=>'linked_in_profile',
          'varName'=>'linked_in_profile',
          'table'=>'staff_recruitment_candidates',
          'index'=>'5',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'resume',
          'columnNameWithTable'=>'src.resume',
          'columnName'=>'resume',
          'varName'=>'resume',
          'table'=>'staff_recruitment_candidates',
          'index'=>'6',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'Designation',
          'columnNameWithTable'=>'sd.designation',
          'columnName'=>'designation',
          'varName'=>'designation',
          'table'=>'staff_designations',
          'index'=>'7',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'candidate_status',
          'columnNameWithTable'=>'src.candidate_status',
          'columnName'=>'candidate_status',
          'varName'=>'candidate_status',
          'table'=>'staff_recruitment_candidates',
          'index'=>'9',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'gender',
          'columnNameWithTable'=>'src.gender',
          'columnName'=>'gender',
          'varName'=>'gender',
          'table'=>'staff_recruitment_candidates',
          'index'=>'10',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'marital_status',
          'columnNameWithTable'=>'src.marital_status',
          'columnName'=>'marital_status',
          'varName'=>'marital_status',
          'table'=>'staff_recruitment_candidates',
          'index'=>'10',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'addressLine1',
          'columnNameWithTable'=>'src.addressLine1',
          'columnName'=>'addressLine1',
          'varName'=>'addressLine1',
          'table'=>'staff_recruitment_candidates',
          'index'=>'11',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'pincode',
          'columnNameWithTable'=>'src.pincode',
          'columnName'=>'pincode',
          'varName'=>'pincode',
          'table'=>'staff_recruitment_candidates',
          'index'=>'12',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'city',
          'columnNameWithTable'=>'src.city',
          'columnName'=>'city',
          'varName'=>'city',
          'table'=>'staff_recruitment_candidates',
          'index'=>'13',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'state',
          'columnNameWithTable'=>'src.state',
          'columnName'=>'state',
          'varName'=>'state',
          'table'=>'staff_recruitment_candidates',
          'index'=>'14',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'country',
          'columnNameWithTable'=>'src.country',
          'columnName'=>'country',
          'varName'=>'country',
          'table'=>'staff_recruitment_candidates',
          'index'=>'15',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'pic',
          'columnNameWithTable'=>'src.pic',
          'columnName'=>'pic',
          'varName'=>'pic',
          'table'=>'staff_recruitment_candidates',
          'index'=>'16',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'dob',
          'columnNameWithTable'=>'src.dob',
          'columnName'=>'dob',
          'varName'=>'dob',
          'table'=>'staff_recruitment_candidates',
          'index'=>'17',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'curr_ctc',
          'columnNameWithTable'=>'src.curr_ctc',
          'columnName'=>'curr_ctc',
          'varName'=>'curr_ctc',
          'table'=>'staff_recruitment_candidates',
          'index'=>'18',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'expted_ctc',
          'columnNameWithTable'=>'src.expted_ctc',
          'columnName'=>'expted_ctc',
          'varName'=>'expted_ctc',
          'table'=>'staff_recruitment_candidates',
          'index'=>'18',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'availability',
          'columnNameWithTable'=>'src.availability',
          'columnName'=>'availability',
          'varName'=>'availability',
          'table'=>'staff_recruitment_candidates',
          'index'=>'19',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'organization_working',
          'columnNameWithTable'=>'src.organization_working',
          'columnName'=>'organization_working',
          'varName'=>'organization_working',
          'table'=>'staff_recruitment_candidates',
          'index'=>'20',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'notice_period',
          'columnNameWithTable'=>'src.notice_period',
          'columnName'=>'notice_period',
          'varName'=>'notice_period',
          'table'=>'staff_recruitment_candidates',
          'index'=>'21',
          'displayType'=>'text',
          'dataType'=>'string'
        ],
        [
          'displayName'=>'final_interview_status',
          'columnNameWithTable'=>'src.final_interview_status',
          'columnName'=>'final_interview_status',
          'varName'=>'final_interview_status',
          'table'=>'staff_recruitment_candidates',
          'index'=>'22',
          'displayType'=>'text',
          'dataType'=>'string'
        ]
      ];
    

    public function index() {

        //$data['main_content']= 'career';
        $site_url = site_url();
        $data['admin_tiles'] = array(
            [
                'title'=> 'Budgeted Headcount',
                'sub_title'=> 'Budgeted Headcount',
                'icon'=> 'svg_icons/add.svg',
                'url' => $site_url.'Staff_recruitment_controller/manage_approved_positions',
                'permission' => $this->authorization->isAuthorized('STAFF_RECRUITMENT.BUDGETED_HEADCOUNT')
            ],
            [
                'title'=> 'Vacancies',
                'sub_title'=> 'Vacancies',
                'icon'=> 'svg_icons/reportcard.svg',
                'url' => $site_url.'Staff_recruitment_controller/job_openings',
                'permission' => $this->authorization->isAuthorized('STAFF_RECRUITMENT.VACANCIES')
            ],
            [
                'title'=> 'Mass Upload Resumes',
                'sub_title'=> 'Mass Upload Resumes',
                'icon'=> 'svg_icons/staffpfreport.svg',
                'url' => $site_url.'Staff_recruitment_controller/mass_upload_resumes',
                'permission' => $this->authorization->isAuthorized('STAFF_RECRUITMENT.MASS_UPLOAD_RESUMES')
            ]
        );
        $data['master_tiles'] = array(
            [
                'title' => 'Manage Candidates',
                'sub_title' => 'All Candidates Information',
                'icon' => 'svg_icons/reportcard.svg',
                'url' => $site_url.'Staff_recruitment_controller/manage_recruitment',
                'permission' =>  $this->authorization->isAuthorized('STAFF_RECRUITMENT.MANAGE_CANDIDATES')
            ],[
                'title' => 'Manage Interview Process',
                'sub_title' => 'Manage Interviews',
                'icon' => 'svg_icons/management.svg',
                'url' => $site_url.'Staff_recruitment_controller/manage_interviews',
                'permission' =>  $this->authorization->isAuthorized('STAFF_RECRUITMENT.MANAGE_INTERVIEW_PROCESS')
            ]
            // ,[
            //     'title' => 'Take Interview',
            //     'sub_title' => 'Manage Interviews',
            //     'icon' => 'svg_icons/management.svg',
            //     'url' => $site_url.'Staff_recruitment_controller/interviewer_page',
            //     'permission' =>  $this->authorization->isAuthorized('STAFF_RECRUITMENT.TAKE_INTERVIEW')
            // ],
            // [
            //     'title' => 'Apply For Recruitment ',
            //     'sub_title' => 'Add a new candidate',
            //     'icon' => 'svg_icons/freshentry.svg',
            //     'url' => $site_url.'Staff_recruitment_controller/add_recruitment',
            //     'permission' => $this->authorization->isSuperAdmin()
            // ],
            // [
            //     'title' => 'Apply For Recruitment new',
            //     'sub_title' => 'Add a new candidate',
            //     'icon' => 'svg_icons/freshentry.svg',
            //     'url' => $site_url.'Staff_recruitment_controller/add_recruitment_new',
            //     'permission' => $this->authorization->isSuperAdmin()
            // ]
        );
        $data['settings_tiles'] = array(
            [
                'title' => 'Configure Form Fields',
                'sub_title' => 'Configure Form Fields',
                'icon' => 'svg_icons/configmanagement.svg',
                'url' => $site_url.'Staff_recruitment_controller/staff_recruitment_settings',
                'permission' =>  $this->authorization->isSuperAdmin()
            ],
            [
                'title' => 'Manage Interview Type',
                'sub_title' => 'Manage Interview Type',
                'icon' => 'svg_icons/configmanagement.svg',
                'url' => $site_url.'Staff_recruitment_controller/manage_interview_type',
                'permission' =>  $this->authorization->isSuperAdmin()
            ],
            [
                'title' => 'Manage Competencies',
                'sub_title' => 'Manage Competencies',
                'icon' => 'svg_icons/configmanagement.svg',
                'url' => $site_url.'Staff_recruitment_controller/manage_competencies',
                'permission' =>  $this->authorization->isSuperAdmin()
            ]
        );
        $data['report_tiles'] = array(
            [
                'title' => 'Candidate Reports',
                'sub_title' => 'Candidate Reports',
                'icon' => 'svg_icons/configmanagement.svg',
                'url' => $site_url.'Staff_recruitment_controller/candidate_reports',
                'permission' =>  $this->authorization->isSuperAdmin()
            ],
            [
                'title' => 'Staff recruitment analysis',
                'sub_title' => 'Staff recruitment analysis',
                'icon' => 'svg_icons/configmanagement.svg',
                'url' => $site_url.'Staff_recruitment_controller/staff_recruitment_analysis',
                'permission' =>  $this->authorization->isSuperAdmin()
            ]
        );
        $data['active_candidates'] = $this->Staff_recruitment_model->getActiveCandidates();
        $data['total_candidates'] = $this->Staff_recruitment_model->getTotalCandidates();
        $data['total_posting'] = $this->Staff_recruitment_model->getTotalOpeanings();
        $data['selected_candidates'] = $this->Staff_recruitment_model->getSelectedCandidates();
        $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);
        $data['master_tiles'] = checkTilePermissions($data['master_tiles']);
        $data['main_content']    = 'staff_recruitment/staff_recruitment_dashboard';
        $this->load->view('inc/template', $data);
    }
    public function add_career_details() {
        $this->Staff_recruitment_model->add_career_details($_POST);
        redirect('Staff_recruitment_controller/manage_recruitment');

    }

    public function add_career_details_new() {
        // echo "<pre>"; print_r($_POST); die();
        $result=$this->Staff_recruitment_model->add_career_details_new($_POST);
        echo $result;
    }

    public function reopen_form($email){
        // echo "<pre>"; print_r($email);
        $email_change=str_replace('999999999999999','@',$email);
        $email_change=str_replace('000000000000000','.',$email_change);
        // echo "<pre> latest:"; print_r($email_change); die();
        $data['form_fields'] = $this->Staff_recruitment_model->getPersonsForm($email_change);

    }

    public function personal_details_2() {
        $result=$this->Staff_recruitment_model->personal_details_2($_POST);
        echo $result;
    }

    public function thank_you_page() {
        $data['main_content'] = 'thank_you_page_view_career';
        $this->load->view('inc/template', $data);

    }
     
    public function add_recruitment() {
        $data['new_row_id']=$this->Staff_recruitment_model->newRow();
        // echo "<pre>"; print_r($data['new_row_id']); die();
        $data['postings'] = $this->Staff_recruitment_model->getOpenedStaffPostings();
        $data['main_content'] = 'career';
        $this->load->view('inc/template', $data);

    }

    public function add_recruitment_new($job_title='') {
        $data['postings'] = $this->Staff_recruitment_model->getOpenedStaffPostings();
        // $data['academic']=$this->Staff_recruitment_model->getAcademicDetails($data['new_row_id']);
        $data['job_title'] = $job_title;
        $data['main_content'] = 'staff_recruitment/add_recruitment_new';
        $this->load->view('inc/template', $data);

    }

    public function manage_recruitment () {
        $data['postings'] = $this->Staff_recruitment_model->getOpenedStaffPostings();
        $data['yesno_array']= $this->settings->getSetting('advanced_hr_custom_yesno_fields');
        $data['text_array']= $this->settings->getSetting('advanced_hr_custom_text_fields');
        $data['main_content'] = 'staff_recruitment/manage_recruitment';
        $this->load->view('inc/template',$data);

    }

    public function get_recrutable_staffs(){
    
        $id = $_POST['staff_id'];
        $recrutments =  $this->Staff_recruitment_model->get_recrutable_staffs($id);
        echo json_encode($recrutments);
    }
    public function get_recrutable_staffs_id(){
        $createdfrom_date = $_POST['createdfrom_date'];
        $createdto_date = $_POST['createdto_date'];
        $candidate_status = $_POST['candidate_status'];
        $job_title = $_POST['job_title'];
        $application_status = $_POST['application_status'];

        $staffs_id= $this->Staff_recruitment_model->get_recrutable_staffs_id($createdfrom_date,$createdto_date,$candidate_status,$job_title,$application_status);
        echo json_encode($staffs_id);
    }

    // public function get_recrutable_staffs_first(){
    
    //     $createdfrom_date = '';
    //     $createdto_date = '';
        
    //     $recrutments =  $this->Staff_recruitment_model->get_recrutable_staffs($createdfrom_date,$createdto_date);
    //     echo json_encode($recrutments);
    // }

    public function manage_approved_positions(){
        $data['staff'] = $this->Staff_recruitment_model->getStaff();
        // $data['position_code']=$this->Staff_recruitment_model->getPositionCode();
        // echo "<pre>"; print_r($data['position_code']);die();
        $data['position'] = $this->Staff_recruitment_model->getStaffPositions();
        $data['department'] = $this->Staff_recruitment_model->getStaffDepartment();
        $data['ReportingManager'] = $this->Staff_recruitment_model->getStaffReportingManager();
        $data['main_content'] = 'staff_recruitment/manage_approved_positions_view';
        $this->load->view('inc/template',$data);
    }

    public function get_data_approved_position(){
        $data['approved_position'] = $this->Staff_recruitment_model->get_data_approved_position();
        echo json_encode($data);
    }

    public function add_data_approved_position(){
        // echo "<pre>"; print_r($_POST); die();
        $position_id = $_POST['position_id'];
        $position_code = $_POST['position_code'];
        $unique_position_code=$this->Staff_recruitment_model->getPositionCode($position_code);
        // echo "<pre>"; print_r($unique_position_code);die();
        if($unique_position_code) {
           echo "0";
           return;
        }
        $department_id = $_POST['department_id'];
        $approved_on = date('Y-m-d');
        $status = $_POST['status_id'];
        $filled_by_id = $_POST['staff_name_id'];
        $success = $this->Staff_recruitment_model->add_data_approved_position($position_id,$department_id,$approved_on,$status,$position_code,$filled_by_id);
        echo json_encode($success);
    }
    
    public function job_openings(){
        $data['headcount_positions']=$this->Staff_recruitment_model->getHeadcountPositions();
        $data['position'] = $this->Staff_recruitment_model->getOpenedStaffPositions();
        $data['department'] = $this->Staff_recruitment_model->getStaffDepartment();
        $data['ReportingManager'] = $this->Staff_recruitment_model->getStaffReportingManager();
        $data['main_content'] = 'staff_recruitment/job_openings_view';
        $this->load->view('inc/template',$data);
    }

    public function get_job_openings(){
        $data['job_openings']= $this->Staff_recruitment_model->get_job_openings();
        // echo "<pre>"; print_r($data['job_openings'] ); die();
        echo json_encode($data);
    }

    public function add_job_opening(){
        // echo "<pre>"; print_r($_POST); die(); 
        $job_title = $_POST['job_title'];
        $hiring_manager_id = $_POST['hiring_manager_id'];
        $min_exp = $_POST['min_exp'];
        $max_exp = $_POST['max_exp'];
        $from_date = date('Y-m-d',strtotime($_POST['from_date']));
        $to_date = date('Y-m-d',strtotime($_POST['to_date']));
        $status =$_POST['status'];
        $job_desc =$_POST['job_desc'];
        $budgeted_heads =$_POST['budgeted_heads'];
        // echo "<pre>"; print_r(sizeof($budgeted_heads));die();
        $success = $this->Staff_recruitment_model->add_job_opening($job_title,$hiring_manager_id,$min_exp,$max_exp,$from_date,$to_date,$status,$job_desc,sizeof($budgeted_heads));
        $this->Staff_recruitment_model->add_vacancies_headcounts($success,$budgeted_heads);
        echo json_encode($success);

        
    }

    public function edit_job_opening(){
        // echo "<pre>"; print_r($_POST); die(); 
        $budgeted_heads =$_POST['budgeted_heads_edit'];
        $success = $this->Staff_recruitment_model->edit_job_opening($_POST);
        echo json_encode($success);   
    }

    public function add_career_education_details(){
        // echo "<pre>"; print_r($_POST);die();
        $this->Staff_recruitment_model->add_career_education_details($_POST);
        redirect('Staff_recruitment_controller/add_recruitment');
    } 

    public function get_career_education_details(){
        $new_row_id =$_POST['new_row_id'];
        $data['education_details']=$this->Staff_recruitment_model->get_career_education_details($new_row_id);
        echo json_encode($data);
    }

    public function get_career_experience_details(){
        $new_row_id =$_POST['new_row_id'];
        $data['experience_details']=$this->Staff_recruitment_model->get_career_experience_details($new_row_id);
        echo json_encode($data);
    }

    public function get_reference_details(){
        $new_row_id =$_POST['new_row_id'];
        $data['reff_details']=$this->Staff_recruitment_model->get_reference_details($new_row_id);
        echo json_encode($data);
    }

    public function add_additional_academic_info(){
        // echo "<pre>"; print_r($_POST);die();
        $result = $this->Staff_recruitment_model->add_additional_academic_info($_POST);
        echo $result;
    }

    public function remove_information(){
        $result = $this->Staff_recruitment_model->remove_information($_POST);
        echo $result;
    } 

    public function other_information() {
        
       $this->Staff_recruitment_model->other_information($_POST);
       redirect('Staff_recruitment_controller/manage_recruitment');
    }

    public function reffer_data() {
        // echo "<pre>"; print_r($_POST); die();
        $result=$this->Staff_recruitment_model->reffer_data($_POST);
        echo $result;

    }

    public function staff_recruitment_settings(){
        $data['main_content'] = 'staff_recruitment/staff_recruitment_settings';
        $this->load->view('inc/template',$data);
    }

    public function introPage(){
        $data['postings'] = $this->Staff_recruitment_model->getOpenedStaffPostings();
        $data['main_content'] = 'staff_recruitment/intro_page';
        $this->load->view('inc/template',$data); 
    }

    public function budgeted_headcount_change_status(){
        echo $this->Staff_recruitment_model->budgeted_headcount_change_status($_POST);
    }

    public function headcount_change_status(){
        echo $this->Staff_recruitment_model->headcount_change_status($_POST);
    }

    public function get_vacancies_headcounts(){
        $result = $this->Staff_recruitment_model->get_vacancies_headcounts($_POST['id']);
        echo json_encode($result);
    }

    public function delete_opening(){
        echo $this->Staff_recruitment_model->delete_opening($_POST);
    }

    public function delete_position(){
        echo $this->Staff_recruitment_model->delete_position($_POST);
    }

    public function headcount_change_staff(){
        // echo "<pre>"; print_r($_POST); die();
        echo $this->Staff_recruitment_model->headcount_change_staff($_POST);
    }

    public function headcount_delete_staff(){
        // echo "<pre>"; print_r($_POST); die();
        echo $this->Staff_recruitment_model->headcount_delete_staff($_POST);
    }

    public function get_view_vacancy_data(){
        $id =$_POST['id'];
        $data=$this->Staff_recruitment_model->get_view_vacancy_data($id);
        // echo "<pre>"; print_r($data); die();
        echo json_encode($data);
    }

    public function mass_upload_resumes(){
        $data['main_content'] = 'staff_recruitment/mass_upload_resumes';
        $this->load->view('inc/template',$data);
    }

    public function insert_data_into_staff_recruitment(){
        // echo "<pre>"; print_r($_FILES); die();
        // $data=$this->Staff_recruitment_model->insert_data_into_staff_recruitment($_POST,$this->s3FileUpload($_FILES['resume_path'], 'staff_recruitment'),$this->s3FileUpload($_FILES['school_marks_card_path'], 'staff_recruitment'),$this->s3FileUpload($_FILES['phd_documents'], 'staff_recruitment'),$this->s3FileUpload($_FILES['other_certificate'], 'staff_recruitment'),$this->s3FileUpload($_FILES['qualification_documents_1'], 'staff_recruitment'));
        $data=$this->Staff_recruitment_model->insert_data_into_staff_recruitment($_POST);
        echo $data;
    }
    
    private function s3FileUpload($file, $folder_name = 'staff_recruitment'){
        if ($file['tmp_name'] == '' || $file['name'] == '') {
            return ['status' => 'empty', 'file_name' => ''];
        }
        return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
    }
    
    public function update_candidate_follow_up(){
        $data=$this->Staff_recruitment_model->update_candidate_follow_up($_POST);
        echo $data;
    }

    public function get_history_data(){
        $id =$_POST['id'];
        $data=$this->Staff_recruitment_model->get_history_data($id);
        echo json_encode($data);
    }

    public function send_continu_mail(){
        $id =$_POST['id'];
        $this->Staff_recruitment_model->update_history($id);
        $this->Staff_recruitment_model->change_final_submit_status($id); // change the status of final submit from 1 to 0
        // $this->load->model('email_model');
        // $this->load->helper('email_helper');
        // $email_data= $this->Staff_recruitment_model->get_email_data($id);
        // $email_template2 = $this->email_model->get_email_template_to_send_visitor('staff recruitment continue email');
        // $emailBody2 =  $email_template2->content;
        // $encoded_id=($id*3952)-3299;

        // $emailBody2 = str_replace('%%schoolname%%',$this->settings->getSetting('school_name'), $emailBody2);
        // $emailBody2 = str_replace('%%position%%',$email_data->designation, $emailBody2);
        // $emailBody2 = str_replace('%%href%%', base_url().'Staff_recruitment_controller_osu/add_recruitment_new/' .$email_data->job_title .'/'. $encoded_id  , $emailBody2);
        // $memberEmail[]['email'] = $email_data->email;
        // $res=$this->__send_email($emailBody2,$email_template2->email_subject, $memberEmail, $email_template2->registered_email);
        // if($res) {
        //     echo json_encode(['status' => 'ok','msg' => 'Email Sent!']);
        // } else {
        //     echo json_encode(['status' => 'error','msg' => 'Unable to send Email please try again!' ]);
        // }
        echo json_encode(['status' => 'ok','msg' => 'Done!']);
    }

    public function update_job_title(){
        $data=$this->Staff_recruitment_model->update_job_title($_POST);
        echo $data;
    }

    public function change_status(){
        $data=$this->Staff_recruitment_model->change_status($_POST);
        echo $data;
    }
    
    public function manage_interviews () {
        $data['interview_types'] = $this->Staff_recruitment_model->get_interview_type();
        $data['competency_types'] = $this->Staff_recruitment_model->get_competency_types();
        $data['staffs'] = $this->Staff_recruitment_model->getStaffReportingManager();
        $data['postings'] = $this->Staff_recruitment_model->getOpenedStaffPostings();
        $data['main_content'] = 'staff_recruitment/manage_interviews';
        $this->load->view('inc/template',$data);

    }

    public function interviewer_page() {
        $data['show_my_interviews']= $this->Staff_recruitment_model->getInterviewerId($this->authorization->getAvatarStakeHolderId());
        $data['postings'] = $this->Staff_recruitment_model->getOpenedStaffPostings();
        $data['candidate_interview_data']= $this->Staff_recruitment_model->get_candidate_interview_data();
        $data['main_content'] = 'staff_recruitment/interviewer_page_view';
        $this->load->view('inc/template',$data);
    }

    public function get_selected_candidates_ids(){
        $job_title =$_POST['job_title'];
        $data=$this->Staff_recruitment_model->get_selected_candidates_ids($job_title);
        echo json_encode($data);
    }

    public function get_candidate_interview_information(){
        $id =$_POST['id'];
        $data=$this->Staff_recruitment_model->get_candidate_interview_information($id);
        echo json_encode($data);
    }

    public function add_candidate_interview(){
        $data=$this->Staff_recruitment_model->add_candidate_interview($_POST);
        echo json_encode($data);
    }

    public function publish_interviews(){
        $data=$this->Staff_recruitment_model->publish_interviews($_POST['id']);
        echo json_encode($data);
    }

    public function delete_interview(){
        $data=$this->Staff_recruitment_model->delete_interview($_POST['row_id'],$_POST['candidate_id']);
        echo json_encode($data);
    }

    public function final_status(){
        $data=$this->Staff_recruitment_model->final_status($_POST);
        echo json_encode($data);
    }

    public function history_page_data(){
        $data=$this->Staff_recruitment_model->history_page_data($_POST['candidate_id']);
        echo json_encode($data);
    }

    public function complete_interview(){
        // echo "<pre>"; print_r($_POST); die();
        $data=$this->Staff_recruitment_model->complete_interview($_POST);
        echo json_encode($data);
    }

    public function edit_interview_view_data(){
        $data=$this->Staff_recruitment_model->edit_interview_view_data($_POST['row_id']);
        echo json_encode($data);
    }

    public function update_interview(){
        $data=$this->Staff_recruitment_model->update_interview($_POST);
        echo json_encode($data);
    }

    public function get_competencies(){
        $data=$this->Staff_recruitment_model->get_competencies($_POST['srci_id']);
        echo json_encode($data);
    }

    public function manage_interview_type () {
        $data['main_content'] = 'staff_recruitment/manage_interview_type';
        $this->load->view('inc/template',$data);

    }

    public function manage_competencies () {

        $data['main_content'] = 'staff_recruitment/manage_competencies';
        $this->load->view('inc/template',$data);

    }

    public function add_interview_type(){
        $data=$this->Staff_recruitment_model->add_interview_type($_POST['interview_name']);
        echo json_encode($data);
    }

    public function get_interview_type(){
        $data=$this->Staff_recruitment_model->get_interview_type();
        echo json_encode($data);
    }

    public function delete_interview_type(){
        $data=$this->Staff_recruitment_model->delete_interview_type($_POST['interview_id']);
        echo json_encode($data);
    }

    public function add_competency_type(){
        $data=$this->Staff_recruitment_model->add_competency_type($_POST['competency_name']);
        echo json_encode($data);
    }

    public function get_competency_types(){
        $data=$this->Staff_recruitment_model->get_competency_types();
        echo json_encode($data);
    }

    public function delete_competency_type(){
        $data=$this->Staff_recruitment_model->delete_competency_type($_POST['competency_id']);
        echo json_encode($data);
    }

    public function get_my_interviews(){
        $data=$this->Staff_recruitment_model->get_my_interviews();
        echo json_encode($data);
    }

    public function take_candidate_personal_interview($candidate_id,$candidate_interview_id){
        $interview_done=$this->Staff_recruitment_model->is_interview_taken($candidate_interview_id);
        $data['show_my_interviews']= $this->Staff_recruitment_model->getInterviewerId($this->authorization->getAvatarStakeHolderId());
        $data['interview_done']=$interview_done;
        $data['candidate_interview_id']=$candidate_interview_id;
        $data['candidate_id']=$candidate_id;
        $data['interview_loc_link']=$this->Staff_recruitment_model->get_candidate_interview_loc_link($candidate_interview_id);
        $data['candidate_data']=$this->Staff_recruitment_model->get_recrutable_staffs($candidate_id)[0];
        $data['main_content'] = 'staff_recruitment/take_candidate_personal_interview_view';
        $this->load->view('inc/template',$data);
    }

    public function construct_pre_feedback_side_data(){
        $data=$this->Staff_recruitment_model->construct_pre_feedback_side_data($_POST['candidate_id']);
        echo json_encode($data);
    }

    public function get_detailed_interview_information(){
        $data=$this->Staff_recruitment_model->get_detailed_interview_information($_POST['id']);
        echo json_encode($data);
    }

    public function candidate_reports(){
        $colname= $this->columnList;
        $data['columnList'] = $colname; 
        $data['postings'] = $this->Staff_recruitment_model->getOpenedStaffPostings();
        $data['main_content'] = 'staff_recruitment/candidate_reports_view';
        $this->load->view('inc/template',$data);
    }

    public function get_staff_recruitment_report(){
        $data['columnList'] = $this->columnList;
        $selectedIndex = $this->input->post('field_names');

        $selectedColumns = array();
        $displayColumns = array();
        foreach($selectedIndex as $fIndex) { 
          foreach ($data['columnList'] as $col) {
            if ($col['index'] == $fIndex) {
                $selectedColumns[] = (array)$col;
                $displayColumns[] = $col;
            }
          }
        }

        $colString = '';
        $joinClauses = '';
        $joinedTables = [];
        foreach ($selectedColumns as $col) {
            if ($colString != '') {
                $colString .= ', ';
            }
            if ($col['columnName'] == 'first_name') {
                $colString .= 'CONCAT(IFNULL(src.first_name, ""), " ", IFNULL(src.last_name, "")) AS candidate_name';
            } elseif ($col['columnName'] == 'dob') {
                $colString .= 'IFNULL(DATE_FORMAT(' . $col['columnNameWithTable'] . ', "%d-%m-%Y"), "-") AS ' . $col['displayName'];
            } elseif (in_array($col['columnName'], ['curr_ctc', 'expted_ctc'])) {
                $colString .= 'IFNULL(CONCAT("₹", FORMAT(' . $col['columnNameWithTable'] . ', 2)), "-") AS ' . $col['displayName'];
            } else {
                // Default handling for other columns, including candidate_status
                $colString .= 'IFNULL(' . $col['columnNameWithTable'] . ', "-") AS ' . $col['displayName'];
            }

            if ($col['columnName'] == 'designation' && !in_array('staff_designations', $joinedTables)) {
                $joinClauses .= ' JOIN staff_designations sd ON sd.id = src.job_title';
                $joinedTables[] = 'staff_designations';
            }
        }


        $data=$this->Staff_recruitment_model->get_staff_recruitment_report($colString,$_POST['from_date'],$_POST['to_date'],$_POST['candidate_status'],$_POST['job_title'],$joinClauses);
        echo json_encode($data);

    }

    public function staff_recruitment_analysis(){
    $data['totals_postings'] = $this->Staff_recruitment_model->get_total_postings();
    $data['weekly_application_trend'] = $this->Staff_recruitment_model->get_weekly_application_trend();
    $data['postings'] = $this->Staff_recruitment_model->getOpenedStaffPostings();
    // echo "<pre>"; print_r($data['weekly_application_trend']); die();
    $data['main_content'] = 'staff_recruitment/staff_recruitment_analysis_view';
    $this->load->view('inc/template',$data);
    }

    public function get_interview_funnel(){
        $data= $this->Staff_recruitment_model->get_interview_funnel($_POST['job_title']);
        echo json_encode($data);
    }

}   
?>