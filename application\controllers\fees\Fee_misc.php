<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  20 April 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Fee_misc extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->load->model('fees/fee_transaction_misc');
  }

  //Landing function for Fee Transaction
  public function index ($classId = '') {

     if (empty($classId)){
        $classId = $this->input->post('stud_class');
      }
      if (empty($classId)){
        $classId = 1; 
      }

    $data['selectedClassId'] = $classId; 
    $data['clasess'] = $this->fee_transaction_misc->get_all_class();
    // student data
    $data['std_search'] = $this->fee_transaction_misc->get_classwise_student_data($classId);
    //echo "<pre>"; print_r($data['std_search']); die();
    $data['main_content'] = 'fees/miscellaneous/fee_index';
    $this->load->view('inc/template', $data);

  }

  public function fee_select($std_id,$classId){

    $data['variants'] = $this->fee_transaction_misc->get_varients_names();
    $feeConfig = $this->settings->getSetting('fees');
    $data['payment_modes'] = $feeConfig['allowed_payment_modes'];
    $data['selectedClassId'] = $classId;
    // fetch data from selected student Id;
    $data['std_details'] = $this->fee_transaction_misc->get_student_detail($std_id);
    $data['main_content'] = 'fees/miscellaneous/transaction/index';
    $this->load->view('inc/template', $data);
  }

  public function fetch_jsonformatVariants(){
    $result = $this->fee_transaction_misc->get_varients_names();
    echo json_encode($result);
  }
  public function serachitemsIdwiseOption(){

    $itemsId =  $this->input->post('itemsId');
    $stdId =  $this->input->post('stdId');
    $clasId =  $this->input->post('clasId');
    $result = $this->fee_transaction_misc->get_itemsOptionbyItmesId($itemsId,$stdId,$clasId);
    echo json_encode($result);
  }
  public function serachItmesOptionwiseAmount(){
    $optionItmesId =  $this->input->post('optionItmesId');
    $result = $this->fee_transaction_misc->get_amountByoptiontimesId($optionItmesId);
    echo json_encode($result);
  }

  public function submit_feethree_transaction($std_id,$classId){ 
    $final_amount = $this->input->post('final_amount');
    if ($final_amount != 0) {
      $receiptNo = $this->fee_transaction_misc->fee_variants_receiptNo();
      $result = $this->fee_transaction_misc->insert_variants($std_id,$receiptNo);
      if($result){ 
        redirect('fees/fee_misc/fee_receipt/'.$receiptNo.'/'.$classId.'/'.'1'.'/'.$std_id);
        $this->session->set_flashdata('flashSuccess', 'Successfully inserted');
      }
      else{
        $this->session->set_flashdata('flashError', 'No changes');
      }
      redirect('fees/fee_misc');
    }else{
      $this->session->set_flashdata('flashError', 'Check the final amount before submit');
      redirect('fees/fee_misc/fee_select/'.$std_id.'/'.$classId);
    }
  }

  public function fee_receipt($receiptNo,$traverse_to,$std_id){
    $data['traverse_to'] = $traverse_to;
    $data['std_id'] = $std_id;
    //$data['selectedClassId'] = $classId;
    $feeConfig = $this->settings->getSetting('fees');
    $rTemplate = $feeConfig['receipt_template_misc'];
    $data['misc_details'] = $this->fee_transaction_misc->get_misecellaneousFees($receiptNo);
    //echo "<pre>"; print_r($data['misc_details']); die();
   // TODO PDF Created
    // $this->load->library('pdfmanager');
    // $file = $this->pdfmanager->generatePdf($data,'exportPdf');
    
    // $this->load->library('filemanager');
    // $this->filemanager->uploadFile($file['path'],$file['filename']); 

    // $file_path = $this->filemanager->getFilePath($file['filename']); 
    // $this->fee_transaction_two->update_pdfpath_url($fee_id,$file_path);

    $data['main_content'] = 'receipts/'.$rTemplate;
    $this->load->view('inc/template', $data);
  }

  public function history($std_id, $traverse_to,$classId){
    $data['std_id'] = $std_id;
    $data['traverse_to'] = $traverse_to;
    $data['selectedClassId'] = $classId;
    $feeConfig = $this->settings->getSetting('fees');
    $rTemplate = $feeConfig['receipt_template_misc'];
    $data['misc_details'] = $this->fee_transaction_misc->get_misecellaneousFeesbyStdId($std_id);
    if (empty($data['misc_details'])) {
      $this->session->set_flashdata('flashInfo', 'Fees not paid yet. History not available.');
      redirect('fees/fee_misc/index/'.$classId);
    }
    $data['main_content'] = 'fees/miscellaneous/history';
    $this->load->view('inc/template', $data);
  }

}

