<?php
defined('BASEPATH') OR exit('No direct script access allowed');
	/**
	 * Name:    Oxygen
	 * Author:  <PERSON>
	 *          <EMAIL>
	 *
	 * Created:  27 April 2019
	 *
	 * Description:  .
	 *
	 * Requirements: PHP5 or above
	 *
 	*/
	class Purchase extends CI_Controller {
	  public function __construct() {
	    parent::__construct();
	    $this->load->model('inventory/purchase_model');
	    $this->load->library('filemanager');
	    if (!$this->ion_auth->logged_in()) {
	      redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isAuthorized('MANAGEMENT.MODULE'))
			redirect('dashboard');
		if (!$this->authorization->isAuthorized('INVENTORY.MODULE'))
			redirect('dashboard');
	  }

	public function index() {
		$site_url = site_url();
		$data['tiles'] = array(
          [
            'title' => 'Create',
            'sub_title' => 'Create products requirement',
            'icon' => 'svg_icons/freshentry.svg',
            'url' => $site_url.'management/purchase/create_purchase',
            'permission' => 1
          ],
          [
            'title' => 'View',
            'sub_title' => 'View purchase order',
            'icon' => 'svg_icons/view.svg',
            'url' => $site_url.'management/purchase/view_purchase',
            'permission' => 1
          ],
          [
            'title' => 'Reports',
            'sub_title' => 'View purchase ordered',
            'icon' => 'svg_icons/assessment.svg',
            'url' => $site_url.'management/purchase/reports_purchase',
            'permission' => 1
          ]          
      	);
      $data['tiles'] = checkTilePermissions($data['tiles']);
		$data['main_content'] = 'management/purchase/index';
		$this->load->view('inc/template', $data);
	}

	public function create_purchase(){
		$data['category'] = $this->purchase_model->category_list();
		$data['main_content'] = 'management/purchase/create';
		$this->load->view('inc/template', $data);
	}

	public function get_category_wise_products(){
		$catId = $_POST['catId'];
		$result = $this->purchase_model->category_wise_product($catId);
		echo json_encode($result);
	}

	public function get_products_variants(){
		$p_id = $_POST['p_id'];
		$result = $this->purchase_model->prodcut_wise_variant($p_id);
		echo json_encode($result);
	}

	public function get_variants_current_qty(){
		$variantId = $_POST['variantId'];
		$result = $this->purchase_model->variant_wise_curretn_qty($variantId);
		echo json_encode($result);
	}

	public function create_purchase_product(){
		$result = $this->purchase_model->insert_purchse_product();
		if ($result) {
			$this->session->set_flashdata('flashSuccess','Purchase successfully created');
		}else{
			$this->session->set_flashdata('flashError','Something went wrong');
		}
		redirect('management/purchase');
	}

	public function view_purchase(){
		$data['permit_purchase_approve'] = $this->authorization->isAuthorized('INVENTORY.PURCHASE_APPROVE');
		$data['permit_purchase_request'] = $this->authorization->isAuthorized('INVENTORY.PURCHASE_REQUEST');
		$data['po_list'] = $this->purchase_model->get_post_list();
		// echo "<pre>"; print_r($data); die();
		$data['main_content'] = 'management/purchase/view';
		$this->load->view('inc/template', $data);
	}

	public function purchase_order_viewbyid(){
		$purchase_order_id = $_POST['purchase_order_id'];
		$result = $this->purchase_model->purchase_orders_view_byId($purchase_order_id);
		echo json_encode($result);
	}

	public function submit_po_status(){
		$purchase_order_id = $_POST['purchase_order_id'];
		$po_remarks = $_POST['po_remarks'];
		$status_change = $_POST['status_change'];
		echo $this->purchase_model->insert_po_remarks($purchase_order_id, $po_remarks, $status_change);
	}

	public function submit_po_remarks_creater(){
		$purchase_order_id = $_POST['purchase_order_id'];
		$po_remarks = $_POST['po_remarks'];
		echo $this->purchase_model->insert_po_remarks_creater($purchase_order_id, $po_remarks);
	}
		
	public function edit_purchase_variant($purchase_order_id){		
		$data['purchase_edit'] = $this->purchase_model->edit_purchase_byid($purchase_order_id);
		$data['category'] = $this->purchase_model->category_list();
		$data['main_content'] = 'management/purchase/edit_purchase';
		$this->load->view('inc/template', $data);
	}

	public function update_purchase_product($po_id){
		$result = $this->purchase_model->update_purchse_product($po_id);
		if ($result) {
			$this->session->set_flashdata('flashSuccess','Successful Added');
		}else{
			$this->session->set_flashdata('flashError','Something went wrong');
		}
		redirect('management/purchase/view_purchase');
	}
	public function reports_purchase(){
		$category = $this->input->post('category');
		$vendor = $this->input->post('vendor');
		$status = $this->input->post('status');
		$data['purchase_result'] = $this->purchase_model->get_reports_for_purchase($category,$vendor,$status);
		$data['category'] = $this->purchase_model->category_list();
		$data['vendors'] = $this->purchase_model->vendor_list();
		$data['main_content'] = 'management/purchase/reports';
		$this->load->view('inc/template_fee', $data);
	}
}