<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  05 June 2018
 *
 * Description: Model class for Class Master table
 *
 * Requirements: PHP5 or above
 *
 */

class Competitionreport_controller extends CI_Controller {
	function __construct() {
		  parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      $this->load->model('report/competitionreport_model');
      $this->load->model('competition_model');
	} 

  	public function index($filterMode=''){
     
      if ($filterMode == '1')  {
        $data['selectedFromDate'] = '';
        $data['selectedToDate'] = '';
        $competitionReport = $this->competitionreport_model->getCurrentCompetition();
      }else{
        $from_date = $this->input->post('from_date');
        $to_date = $this->input->post('to_date');
        $competitionReport = $this->competitionreport_model->getDatewiseCompetition($from_date,$to_date);
        $data['selectedFromDate']= $from_date;
        $data['selectedToDate'] = $to_date;
      }
      $data['staff_details'] = $this->competition_model->get_staff_detils();
      $data['list_competition'] = $competitionReport;
      $data['main_content']='reports/competition/index';
      $this->load->view('inc/template_fee',$data);
    }

 
 }