<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  29 March 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Staff Request
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Request_controller extends CI_Controller {

	public function __construct() {
		parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('SUBSTITUTION')) {
      redirect('dashboard', 'refresh');
    }    
    $this->load->model('staff/staff_request_model');
  }

  public function index(){
    $data['request_data'] = $this->staff_request_model->get_Allrequeest_data();
    // echo "<pre>"; print_r($data['request_data']); die();
 	  $data['main_content'] = 'staff/staff_request/index';
    $this->load->view('inc/template', $data);
  }

  public function add(){
    $data['staff_list'] = $this->staff_request_model->getAll_Staff();
    //echo "<pre>"; print_r($data['staff_list']); die();
    $data['class_section'] = $this->staff_request_model->get_allCalssSection();
  	$data['main_content'] = 'staff/staff_request/add';
    $this->load->view('inc/template', $data);
  }

  public function edit($id){

    $data['staff_list'] = $this->staff_request_model->getAll_Staff();

    $data['class_section'] = $this->staff_request_model->get_allCalssSection();

    $data['request_edit'] = $this->staff_request_model->edit_rqst_databyId($id);
   //echo "<pre>"; print_r($data['request_edit']); die();
    $data['main_content'] = 'staff/staff_request/edit';
    $this->load->view('inc/template', $data);
  }

  public function submit_request(){
    $reuslt =$this->staff_request_model->insert_request();
     if ($reuslt) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Inserted.');
            redirect('staff/request_controller'); 
      }else{    
        $this->session->set_flashdata('flashSuccess', 'Staff Details failed to updated.');
        redirect('staff/request_controller/add');
      }
  }

  public function update($id)
  {
    $reuslt =$this->staff_request_model->update_request($id);
     if ($reuslt) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Inserted.');
            redirect('staff/request_controller'); 
      }else{    
        $this->session->set_flashdata('flashSuccess', 'Staff Details failed to updated.');
        redirect('staff/request_controller/edit/'.$id);
      }
  }

  public function delete($id)
  {
    $reuslt =$this->staff_request_model->delte_questbyId($id);
     if ($reuslt) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Deleted.');
      }else{    
        $this->session->set_flashdata('flashSuccess', 'Something went wrong');
      }
      redirect('staff/request_controller'); 
  }


}