<?php

class Subjects extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('EXAMINATION_V2')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('class_section');
        $this->load->model('examination_v2/Subjects_model', 'subjects_model');
    }

    public function index() {
    	$data['classes'] = $this->class_section->getAllClassess();
    	$data['main_content'] = 'examination_v2/subjects/index';
        $this->load->view('inc/template_without_top_nav', $data);
    }

    public function get_class_subjects() {
    	$class_id = $_POST['class_id'];
    	$data['subjects'] = $this->subjects_model->get_class_subjects($class_id);
    	$data['remaining_subjects'] = $this->subjects_model->get_master_subjects_not_added($class_id);
    	echo json_encode($data);
    }

    public function add_subjects() {
    	$class_id = $_POST['class_id'];
    	$subject_ids = $_POST['subject_ids'];
    	$status = $this->subjects_model->add_subjects($class_id, $subject_ids);
    	echo $status;
    }
}