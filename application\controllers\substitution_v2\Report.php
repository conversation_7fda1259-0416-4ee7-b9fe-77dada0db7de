<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Report extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('SUBSTITUTION') && $this->authorization->isAuthorized('SUBSTITUTION.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('timetablev2/Substitution_report_model', 'substitution_report');
  }

  public function index() {
    $data['main_content']    = 'substitution_v2/report/index.php';
    $data['source_type'] = $this->substitution_report->get_source_type();
    $this->load->view('inc/template', $data);
  }

  public function get_day_substitutions() {
    $date = date('Y-m-d', strtotime($_POST['date']));
    $data['sub_day'] = $this->substitution_report->get_substitution_day($date);
    $source_type = $this->input->post('source_type');
    if(empty($data['sub_day'])) {
      $data['exists'] = 0;
    } else {
      $staff_names = $this->substitution_report->get_staff_names();
      $data['substitutions'] = $this->substitution_report->get_day_substitutions($data['sub_day']->id, $source_type);
      $data['exists'] = 1;
    }
    echo json_encode($data);
  }

  public function substitution_load() {
    $data['main_content'] = 'substitution_v2/report/substitution_load.php';
    $this->load->view('inc/template', $data);
  }

  public function get_substitution_load() {
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));
    $source_type = $this->input->post('source_type');
    $data = $this->substitution_report->get_substitution_load($from_date, $to_date, $source_type);
    echo json_encode($data);
  }

  public function get_substitution_load_details() {
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));
    $staff_id = $this->input->post('staff_id');
    $data = $this->substitution_report->get_substitution_load_details($from_date, $to_date, $staff_id);
    echo json_encode($data);
  }

}