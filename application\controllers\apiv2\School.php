<?php
require APPPATH . 'libraries/REST_Controller.php';
 
class School extends REST_Controller {
    
    public function __construct() {
        parent::__construct();
    }

    public function index_get($schoolcode) {

        if (($schoolcode == null) || ($schoolcode == ''))  {
            $data = ['error' => ['message' => 'INVALID_SCHOOLCODE']];
            $this->response($data, REST_Controller::HTTP_OK);
            return;    
        }

        $isvalid_schoolcode = true;
        switch ($schoolcode) {
            case 'vec':
                $schoolName = 'Vivekananda Educational Center';
            break;
            case 'jet':
                $schoolName = 'JET Public School';
            break;
            case 'vinayaka':
                $schoolName = 'Vinayaka Public School';
            break;
            case 'pncc':
                $schoolName = 'PNC Cognitio School';
            break;
            case 'risehighv2':
                $schoolName = 'Risehigh Public School';
            break;
            case 'yashasvi':
                $schoolName = 'Yashasvi Public School';
            break;
            case 'northhill':
                $schoolName = 'NorthHills Public School';
            break;
            case 'nurture':
                $schoolName = 'Nurture Public School';
            break;
            case 'npsaga':
                $schoolName = 'NPS Agara Public School';
            break;
            case 'demoschool':
                $schoolName = 'Demo School';
            break;
            case 'cambridgeypr':
                $schoolName = 'Cambridge Public School';
            break;
            case 'newcarmel':
                $schoolName = 'New Carmel Public School';
            break;
            case 'ourschool':
                $schoolName = 'OurSchool';
            break;
            case 'landmark':
                $schoolName = 'Landmark Public School';
            break;
            case 'jnanamudra':
                $schoolName = 'Jnanamudra Public School';
            break;
            case 'jnanavikas':
                $schoolName = 'Jnanavikas Public School';
            break;
            case 'ses':
                $schoolName = 'Sunder Education Society';
            break;
            case 'valiant':
                $schoolName = 'Valiant Public School';
            break;
            case 'grei':
                $schoolName = 'GREI';
            break;
            case 'growdr':
                $schoolName = 'Growdr Public School';
            break;
            case 'maithry':
                $schoolName = 'Maithry Public School';
            break;
            case 'npsjnr':
                $schoolName = 'NPS Jayanagar';
            break;
            case 'bwisgunjur':
                $schoolName = 'Basil Woods International School Gunjur';
            break;
            case 'divine':
                $schoolName = 'Divine Public School';
            break;
            case 'prarthana':
                $schoolName = 'Prarthana Public School';
            break;
            case 'anikethanps':
                $schoolName = 'ANikethan Public School';
            break;
            case 'brvps':
                $schoolName = 'BRV Public School';
            break;
            case 'apsps':
                $schoolName = 'APS Public School';
            break;
            case 'apspuc':
                $schoolName = 'APS PUC';
            break;
            case 'gurukul':
                $schoolName = 'Gurukul Public School';
            break;
            case 'pvv':
                $schoolName = 'Poorna Vikasa Vidhyalaya';
            break;
            case 'pate':
                $schoolName = 'PATE';
            break;
            case 'jesps':
                $schoolName = 'JES Public School';
            break;
            case 'testserver':
                $schoolName = 'Test Server School';
            break;
            case 'englishroots':
                $schoolName = 'English Roots';
            break;
            case 'apsemps':
                $schoolName = 'APS EMPS';
            break;
            case 'apspusc':
                $schoolName = 'APS College for Science and Commerce';
            break;
            case 'surabhi':
                $schoolName = 'Surabhi Public School';
            break;
            case 'rosemount':
                $schoolName = 'Rosemount Public School';
            break;
            case 'jspuc':
                $schoolName = 'JS Public School';
            break;
            case 'mmvs':
                $schoolName = 'Mahila Mandail School';
            break;
            default:
                $schoolName = '';
                $isvalid_schoolcode = false;
            break;
        }       
 
        $data = ['isvalid_schoolcode' => $isvalid_schoolcode,'school_name' => $schoolName];
        $this->response($data, REST_Controller::HTTP_OK);
        $this->output->_display();
        exit;    

    }
}