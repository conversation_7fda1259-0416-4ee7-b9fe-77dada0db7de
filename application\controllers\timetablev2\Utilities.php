<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Utilities extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->load->model('timetablev2/utilities_model','utilities_model');
  }

  public function academic_year_list() {
    $data['academic_years'] = $this->utilities_model->get_academic_year_list();
    echo json_encode($data);
  }

}