DROP TRIGGER IF EXISTS avatar_audit;
DE<PERSON><PERSON><PERSON>ER $$
CREATE TRIGGER avatar_audit
AFTER UPDATE ON avatar FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.user_id, NEW.user_id, 'user_id');
    CALL merge_object(@oldJson, @newJson, OLD.avatar_type, NEW.avatar_type, 'avatar_type');
    CALL merge_object(@oldJson, @newJson, OLD.stakeholder_id, NEW.stakeholder_id, 'stakeholder_id');
    CALL merge_object(@old<PERSON>son, @newJson, OLD.friendly_name, NEW.friendly_name, 'friendly_name');
    CALL merge_object(@oldJson, @newJson, OLD.old_user_id, NEW.old_user_id, 'old_user_id');
    CALL merge_object(@oldJson, @newJson, OLD.dashboard_order, NEW.dashboard_order, 'dashboard_order');
      
    if(@old<PERSON><PERSON>!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'avatar',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        NEW.last_modified_by
    );
    END IF;
END;
$$
DELIMITER ;