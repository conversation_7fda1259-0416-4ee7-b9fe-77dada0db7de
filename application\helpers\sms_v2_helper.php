<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

function __callSmsStatusCheck($msgIdArray, $returnUpdateData, $doNotEcho = FALSE){
    if(empty($msgIdArray) || empty($returnUpdateData)){
        return;
    }
    $CI =& get_instance();
    $msgIds = implode(",", $msgIdArray);
    $smsint = $CI->settings->getSetting('smsintergration');
    $apiKey = $smsint->api_key;
    $apiUrl = 'http://promotional.mysmsbasket.com/V2/http-dlr.php';
    // For Testing Purpose In Local Env Only
    // $apiUrl = 'http://promotional.mysmsbasket.com/V2/http-dlr.php?apikey='.$apiKey.'&id='.$msgIds.'&format=json';
    $curl = curl_init();
    $username = CONFIG_ENV['job_server_username'];
    $password = CONFIG_ENV['job_server_password'];
    $return_url = site_url().'Callback_Controller/updateTextStatus';
    $data = [
        'api_url' => $apiUrl,
        'apiKey' => $apiKey,
        'msgIds' => $msgIds,
        'return_url' => $return_url,
        'returnUpdateData' => json_encode($returnUpdateData),
    ];
    $data = http_build_query($data);
    curl_setopt_array($curl, array(
        CURLOPT_URL => CONFIG_ENV['job_server_smsstatus_uri'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERPWD => $username . ":" . $password,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => array(
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Content-Type: application/x-www-form-urlencoded",
            "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
        ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);

    curl_close($curl);

    if ($err) {
        return 0;
    } else {
        return 1;
    }
}

if (!function_exists('sendTextSms')) {
    function sendTextSms($smsData, $message, $master_id, $isUnicode) {
        $CI =& get_instance();
        $CI->load->model('texting_model');
        if (ENVIRONMENT !== 'production') {
          //Do not allow send sms for local
            $CI->texting_model->updateErrorStatus($master_id);
            return -2;
        }
        $smsint = $CI->settings->getSetting('smsintergration');
        if(!isset($smsint->mode) || $smsint->mode == 'TEST') {
            $CI->texting_model->updateErrorStatus($master_id);
            return -3;
        }

        $response = $CI->sms_sender->sendTextMsg($smsData, $message, $isUnicode);
        //if sms is sent update the status
        if(!empty($response) && $response['status'] == 'OK'){
            $status =(int) $CI->texting_model->updateTextResponse($response, $master_id);
            $msgIdArray = array();
            $returnUpdateData = [];
            foreach ($response['data'] as $key => $value) {
                if ($value['status'] == 'SUBMITTED') {
                    array_push($msgIdArray, $value['id']);
                    $returnUpdateData[$value['id']] = $value['textsentToId'];
                }
            }
            if(!empty($msgIdArray) && !empty($returnUpdateData)) {
                __callSmsStatusCheck($msgIdArray, $returnUpdateData, TRUE);
            }
            return 1;
        } else {
            $CI->texting_model->updateErrorStatus($master_id);
            return 0;
        }
    }
}

if (!function_exists('sendUniqueTextSms')) {
    function sendUniqueTextSms($smsData, $master_ids, $isUnicode) {
        $CI =& get_instance();
        $CI->load->model('texting_model');
        if (ENVIRONMENT !== 'production') {
            //Do not allow send sms for local
            $CI->texting_model->updateUniqueTextErrorStatus($master_ids);
            return -2;
        }

        $credits = 0;
        foreach ($smsData as $k => $sms) {
            $credits += $CI->texting_model->_calculateCredits($sms['message'], $isUnicode);
        }
        $CI->texting_model->checkCredits($credits);

        if($credits == 0) {
            return -1;
        }

        $response = $CI->sms_sender->sendUniqueTextMsg($smsData, $isUnicode);
        //if sms is sent update the status
        if(!empty($response) && $response['status'] == 'OK'){
            $status =(int) $CI->texting_model->updateUniqueTextResponse($response, $master_ids);
            $msgIdArray = array();
            $returnUpdateData = [];
            foreach ($response['data'] as $key => $value) {
                if($value['status'] == 'SUBMITTED') {
                    array_push($msgIdArray, $value['id']);
                    $returnUpdateData[$value['id']] = $value['textSentToId'];
                }
            }
            if(!empty($msgIdArray) && !empty($returnUpdateData)) {
                __callSmsStatusCheck($msgIdArray, $returnUpdateData, TRUE);
            }
            return 1;
        } else {
            $CI->texting_model->updateUniqueTextErrorStatus($master_ids);
            return 0;
        }
    }
}

if (!function_exists('sendForgotOTP')) {
    function sendForgotOTP($mobileNumber, $message) {
        $CI =& get_instance();
        if (ENVIRONMENT !== 'production') {
            //Do not allow send sms for local
            return -2;
        }
        return $CI->sms_sender->send_otp($mobileNumber, $message);
    }
}