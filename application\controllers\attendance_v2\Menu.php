<?php

class Menu extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('STUDENT_ATTENDANCE_V2')) {
            redirect('dashboard', 'refresh');
        }
    }

    public function index(){
        $site_url = site_url();
        $subject_attendance_mode = $this->settings->getSetting('student_attendancev2_subject_attendance_mode');

        if ($subject_attendance_mode == 'attendance_with_periods') {
          $data['tiles'] = array(
            [
              'title' => 'Take Attendance (Period-wise)',
              'sub_title' => 'Take Attendance (Period-wise)',
              'icon' => 'svg_icons/attendance.svg',
              'url' => $site_url.'attendance_v2/Attendance/take_attendance_with_periods',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.TAKE_ATTENDANCE')
            ]
          );
        } else {
          $data['tiles'] = array(
            [
              'title' => 'Take Attendance',
              'sub_title' => 'Take Attendance',
              'icon' => 'svg_icons/attendance.svg',
              'url' => $site_url.'attendance_v2/Attendance/take_attendance',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.TAKE_ATTENDANCE')
            ],
          );
        }
        $data['tiles'] = checkTilePermissions($data['tiles']);

        $is_period_wise_attendance = ($subject_attendance_mode == 'attendance_with_periods');

        $data['report_tiles'] = array(
            [
              'title' => 'Per-Section, Per-Subject, Date range',
              'sub_title' => 'View section wise report',
              'icon' => 'svg_icons/attendance.svg',
              'url' => $site_url.'attendance_v2/reports/section_wise',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS')
            ],
            [
              'title' => 'Per-Section, All-Subjects, Per Date',
              'sub_title' => 'View section-subject report',
              'icon' => 'svg_icons/view.svg',
              'url' => $site_url.'attendance_v2/reports/section_subject',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS') && !$is_period_wise_attendance
            ],
            [
              'title' => 'Month-wise Attendance',
              'sub_title' => 'View section-subject report',
              'icon' => 'svg_icons/view.svg',
              'url' => $site_url.'attendance_v2/reports/month_wise',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS')
            ],
            [
              'title' => 'Per-Section, All-Subjects, Per Date (Period-wise)',
              'sub_title' => 'View section-subject report',
              'icon' => 'svg_icons/view.svg',
              'url' => $site_url.'attendance_v2/reports/section_subject_with_periods',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS') && $is_period_wise_attendance
            ],
            [
              'title' => 'Student Wise Report',
              'sub_title' => 'View student-wise report',
              'icon' => 'svg_icons/assessment.svg',
              'url' => $site_url.'attendance_v2/reports/student_wise',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS')
            ],
            [
              'title' => 'Section Wise Summary Report',
              'sub_title' => 'View section wise summary report',
              'icon' => 'svg_icons/classandsection.svg',
              'url' => $site_url.'attendance_v2/reports/section_summary',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS')
            ],
            [
              'title' => 'Summary Report',
              'sub_title' => 'View summary report',
              'icon' => 'svg_icons/summary.svg',
              'url' => $site_url.'attendance_v2/reports/summary_report',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS')
            ],
            [
              'title' => 'Day-Wise, Date range',
              'sub_title' => 'View day wise report',
              'icon' => 'svg_icons/summary.svg',
              'url' => $site_url.'attendance_v2/reports/day_wise_report',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS')
            ],
            [
              'title' => 'Send Notifications/Sms',
              'sub_title' => 'Based on date range',
              'icon' => 'svg_icons/summary.svg',
              'url' => $site_url.'attendance_v2/reports/notify_absentees',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS')
            ],
            [
              'title' => 'Overall Period Wise Attendance Summary Report',
              'sub_title' => 'Overall Period Wise Attendance Summary Report',
              'icon' => 'svg_icons/summary.svg',
              'url' => $site_url.'attendance_v2/reports/overall_period_wise_attendance_report',
              'permission' => $this->authorization->isSuperAdmin()
              // 'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS') && $is_period_wise_attendance
            ],
            [
              'title' => 'Section-wise Period Attendance Report',
              'sub_title' => 'Period Wise Attendance',
              'icon' => 'svg_icons/summary.svg',
              'url' => $site_url . 'attendance_v2/reports/period_wise_attendance_report',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS') && $is_period_wise_attendance
              // 'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS') && $is_period_wise_attendance
            ],
            [
              'title' => 'Send Notifications/Sms v2',
              'sub_title' => 'Per date with subject details',
              'icon' => 'svg_icons/summary.svg',
              'url' => $site_url . 'attendance_v2/reports/notifications_for_late_absentees',
              'permission' => $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.REPORTS')
            ]
        );
        $data['report_tiles'] = checkTilePermissions($data['report_tiles']);
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'attendance_v2/menu_tablet'; 
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'attendance_v2/menu_mobile'; 
        }else{
          $data['main_content'] = 'attendance_v2/menu';       	
        }      
        $this->load->view('inc/template_without_top_nav', $data);
    }

}