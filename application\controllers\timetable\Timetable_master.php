<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  17 March 2018
 *
 * Description: Controller for Timetable Master
 *
 * Requirements: PHP5 or above
 *
 */

class Timetable_master extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('timetable/timetable');
	}

  //Landing function to display classes
  public function index($classId = '') {
    if (empty($classId))
      $classId = $this->input->post('classId');
    $data['classList'] = $this->timetable->getClassList();

    if (empty($classId) && !empty($data['classList']))
      $classId = $data['classList'][0]->classId;

    $data['classSectionList'] = $this->timetable->getSectionList($classId);
    $data['selectedClassId'] = $classId;
    //echo '<pre>';print_r($data['classSectionList']);die();
    $data['main_content']    = 'timetable/index';
    $this->load->view('inc/template', $data); 
  }

  public function view_tt($csId) { 
    $data['csObj']=$this->timetable->getClassSectionDetailsById ($csId);
    $data['main_content'] = 'timetable/section_timetable/view_tt';
    $this->load->view('inc/template', $data);
  }

  public function submit_to_principal($csId) {
    redirect('Timetable_master');
  }

  public function publish($csId) {
    redirect('Timetable_master');
  }

}
?>
