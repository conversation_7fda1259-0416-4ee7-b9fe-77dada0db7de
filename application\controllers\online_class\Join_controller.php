<?php

  defined('BASEPATH') OR exit('No direct script access allowed');

  /**
   * Class Fee
   * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
   * @property CI_Form_validation      $form_validation The form validation library
   */
  class Join_controller extends CI_Controller {

    private $year_id;
    private $online_server_domain;

    public function __construct() {
      parent::__construct();
      // header('Access-Control-Allow-Origin: *');
      // header('Cross-Origin-Resource-Policy: cross-origin');
      // header('Access-Control-Allow-Headers: Authorization,Content-Type,Accept,Origin,User-Agent,DNT,Cache-Control,X-Mx-ReqToken,Keep-Alive,X-Requested-With,If-Modified-Since');

      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isAuthorized('ONLINE_CLASS.MODULE')) {
        redirect('dashboard', 'refresh');
      }

      if (CONFIG_ENV['domain'] === 'localhost') {
        $this->online_server_domain = 'meet.schoolelement.in';
      } else {
        $this->online_server_domain = $this->settings->getSetting('online_class_server');
      }

      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('online_class/Online_classroom_model','ocm');
      $this->load->library('filemanager');
    }

    public function index() {
      $data['schedule_id'] = $_POST['schedule_id'];
      $data['schedule_room'] = $this->ocm->getRoomByScheduleId($data['schedule_id']);
      $data['main_content']    = 'online_class/join_class/index.php';
      $this->load->view('inc/template', $data);
    }

    public function start_window() {
      $data['schedule_id'] = $_POST['schedule_id'];
      $data['server_date'] = date('d-m-Y');
      $data['schedule_room'] = $this->ocm->getRoomByScheduleId($data['schedule_id']);
      $data['host_id'] = $this->authorization->getAvatarStakeHolderId();
      if($data['schedule_room']->host_id != $data['host_id']) {
        $this->session->set_flashdata('flashError', 'Sorry, you cannot start the class.');
        redirect('online_class/schedule_controller');
      }
      $data['host_info'] = $this->ocm->getHostData($data['host_id']);
      $data['host_info']->webcam_avatar = ($data['host_info']->webcam_avatar)?$this->filemanager->getFilePath($data['host_info']->webcam_avatar):'';
      // $data['host_info']->webcam_avatar = 'asdf';
      // echo "<pre>"; print_r($data); die();
      $this->ocm->update_class_online_status($data['schedule_id'], 0);
      if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'online_class/start_class/mobile_start_window.php';
      } else {
        $data['main_content']    = 'online_class/start_class/start_window.php';
      }
      $this->load->view('inc/template', $data);
      
      /*$data['schedule_id'] = $_POST['schedule_id'];
      $data['schedule_room'] = $this->ocm->getRoomByScheduleId($data['schedule_id']);
      if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'online_class/join_class/mobile_join_window.php';
      } else {
        $data['main_content']    = 'online_class/join_class/join_window.php';
      }
      $this->load->view('inc/template', $data); */ 
    }

    public function troubleshoot() {
      $data['schedule_id'] = $_POST['schedule_id'];
      $type = $_POST['type'];
      $data['back_url'] = site_url('online_class/join_controller/start_window');
      if($type == 'join') {
        $data['back_url'] = site_url('online_class/join_controller/join_window');
      } else if($type == 'dashboard') {
        $data['back_url'] = site_url('online_class/dashboard');
      }
      if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'online_class/join_class/troubleshoot_mobile.php';
      } else {
        $data['main_content']    = 'online_class/join_class/troubleshoot.php';
      }

      // $data['main_content'] = 'online_class/join_class/troubleshoot.php';
      $this->load->view('inc/template', $data);
    }

    public function join_window() {
      if(!isset($_POST['schedule_id'])) {
        redirect('online_class/schedule_controller');
      }
      $data['schedule_id'] = $_POST['schedule_id'];
      $data['server_date'] = date('d-m-Y');
      $data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
      $data['schedule_room'] = $this->ocm->getRoomByScheduleId($data['schedule_id']);
      $data['staff_info'] = $this->ocm->getHostData($data['staff_id']);
      $data['host_id'] = $data['schedule_room']->host_id;
      $data['staff_info']->webcam_avatar = ($data['staff_info']->webcam_avatar)?$this->filemanager->getFilePath($data['staff_info']->webcam_avatar):'';
      // $data['staff_info']->webcam_avatar = 'asdf';
      $data["online_server_domain"] = $this->online_server_domain;
      if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'online_class/join_class/mobile_join_window.php';
      } else {
        $data['main_content']    = 'online_class/join_class/join_window.php';
      }
      $this->load->view('inc/template', $data);
    }

    public function update_class_online_status() {
      echo $this->ocm->update_class_online_status($_POST['schedule_id'], $_POST['status']);
    }

    public function start_class() {
      if(!isset($_POST['schedule_id'])) {
        redirect('online_class/schedule_controller');
      }
      $data['schedule_id'] = $_POST['schedule_id'];
      $data['schedule_details'] = $this->ocm->get_schedule_detail($data['schedule_id']);
      $data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
      $staff_data = $this->ocm->get_staff_data($data['staff_id']);
      $data['staff_name'] = $staff_data->name;
      $data['avatar'] = ($staff_data->webcam_avatar)?$this->filemanager->getFilePath($staff_data->webcam_avatar):'';
      $data["online_server_domain"] = $this->online_server_domain;
      $data['is_host'] = 1;
      if($data['schedule_details']->host_id != $data['staff_id']) {
        $this->session->set_flashdata('flashError', 'Sorry, you cannot start the class.');
        redirect('online_class/schedule_controller');
      }

      $data['schedule_room'] = $this->ocm->getRoomByScheduleId($data['schedule_id']);
      $this->ocm->update_class_online_status($data['schedule_id'], 1);

      // echo '<pre>';print_r($data);die();
      if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'online_class/start_class/mobile_index.php';
      } else {
        $data['main_content']    = 'online_class/start_class/index.php';
      }
      $this->load->view('inc/template_virtualClass', $data);
    }

    public function join_class() {
      if(!isset($_POST['schedule_id'])) {
        redirect('online_class/schedule_controller');
      }
      $data['schedule_id'] = $_POST['schedule_id'];
      $data['schedule_details'] = $this->ocm->get_schedule_detail($data['schedule_id']);
      $data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
      $staff_data = $this->ocm->get_staff_data($data['staff_id']);
      $data['staff_name'] = $staff_data->name;
      $data['avatar'] = ($staff_data->webcam_avatar)?$this->filemanager->getFilePath($staff_data->webcam_avatar):'';
      $data["online_server_domain"] = $this->online_server_domain;
      $data['is_host'] = 0;
      $data['is_mobile'] = $this->mobile_detect->isMobile();
      if($data['schedule_details']->host_id == $data['staff_id']) {
        $data['is_host'] = 1;
      }
      $data['schedule_room'] = $this->ocm->getRoomByScheduleId($data['schedule_id']);
      $this->ocm->update_class_online_status($data['schedule_id'], 1);

      // echo '<pre>';print_r($data);die();
      if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'online_class/join_class/mobile_index.php';
      } else {
        $data['main_content']    = 'online_class/join_class/index.php';
      }
      // $this->load->view('inc/template_virtualClass', $data);
      $this->load->view($data['main_content'], $data);
    }

    public function generateJWT() {
      $this->load->library('JWT');
      $secret = "bmsjman09rac1ezin_tx";

      $payload = [
        "context" => [
          "user" => [
            "avatar" => "",
            "name" => $_POST['staff_name'],
            "id" => $_POST['staff_id']
          ]
        ],
        "aud" => "nextelement",
        "iss" => "nextelement",
        "sub" => $this->online_server_domain,
        "room" => $_POST['session_id'],
        "exp" => time() + (1 * 24 * 60 * 60),
        "moderator" => (isset($_POST['moderator']) && ($_POST['moderator']) === '1') ? true : false
      ];

      // echo '<pre>';print_r($payload);die();
      $jwt = JWT::encode($payload, $secret, 'HS256');
      echo json_encode(["jwt" => $jwt]);
    }

    public function releave_classroom() {
      $classroom_id = $_POST['classroom_id'];
      $this->ocm->update_class_session_id($classroom_id, null);
    }

    public function s3FileUpload($file) {
      if ($file['tmp_name'] == '' || $file['name'] == '') {
        return ['status' => 'empty', 'file_name' => ''];
      }
      $path = $this->filemanager->uploadFile($file['tmp_name'], $file['name'], 'staff_avatar');
      return $path;
    }

    public function save_avatar() {
      $file = $_FILES['avatar'];
      $path = $this->s3FileUpload($file);
      if($path['file_name'] != '') {
        $staff_id = $this->authorization->getAvatarStakeHolderId();
        $status = $this->ocm->save_staff_avatar($staff_id, $path['file_name']);
        echo $status;
      } else {
        echo 0;
      }
    }

  }

?>