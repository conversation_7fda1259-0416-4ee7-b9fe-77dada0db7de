<?php

class Root_controller extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
    }
    $this->load->model('english_roots/root_model');
  }

  public function index() {
      $data['main_content'] = 'english_roots/dashboard';
      $this->load->view('inc/template', $data);
  }

  public function addProgramLevel() {
    $data['main_content'] = 'english_roots/add_program_details';
    $this->load->view('inc/template', $data);
  }

  public function getProgramName(){
    $data = $this->root_model->getProgramName();
    echo json_encode($data);
  }

  public function getPrograms(){
    $data['programs'] = $this->root_model->getPrograms();
    echo json_encode($data);
  }

  public function program_detail_insert(){
    $data = $this->root_model->programLevelData();
    echo ($data);
  }

  public function program_detail_update(){
    $data = $this->root_model->programLevelUpdateData();
    echo ($data);
  }

  public function getProgramLevelDetails(){
    $program_id = $_POST['program_id'];

    $data = $this->root_model->getProgramLevelDetails($program_id);
    echo json_encode($data);
  }

  public function addProgram(){
    $data = $this->root_model->addProgram();
    echo json_encode($data);
  }

  public function registration_form(){
    $data['programNames'] = $this->root_model->getProgramNames();
    $data['main_content'] = 'english_roots/registration_form';
    $this->load->view('inc/template', $data);

  }

  public function getLevels(){
    $program = $_POST['program'];
    $language = $_POST['language'];
    $data['level'] = $this->root_model->getLevels($program, $language);
    echo json_encode($data);
  }

  public function getLevelDetails(){
    $level = $_POST['level'];
    $program = $_POST['program'];
    $language = $_POST['language'];
    $data['batch'] = $this->root_model->getBatch($level, $program, $language);

    echo json_encode($data);
  }

  public function getDurationAmount(){
    $level = $_POST['level'];
    $program = $_POST['program'];
    $language = $_POST['language'];
    $data['durationAmount'] = $this->root_model->getDurationAmount($level, $program, $language);
    echo json_encode($data);
  }

  public function updateLevelStatusToInactive(){
      $level_id = $_POST['level_id'];
         
      $data = $this->root_model->updateLevelStatusToInactive($level_id);
      echo ($data);
    }
  
    public function updateLevelStatusToActive() {
      $level_id = $_POST['level_id'];
      $data = $this->root_model->updateLevelStatusToActive($level_id);
      echo($data);
    }

  public function submit_registration(){
    $status = $this->root_model->submit_registration();
    if($status){
			$this->session->set_flashdata('flashSuccess', 'Registered successfully.');
      redirect('english_roots/root_controller/display_summary/'.$status);
    }else{
			$this->session->set_flashdata('flashError', 'Failed to add.');
      redirect('english_roots/root_controller/registration_form');

    }
  }

  public function display_summary($id){
    $data['details'] = $this->root_model->getRegisteredDetails($id);
    $data['main_content'] = 'english_roots/display_summary';
    $this->load->view('inc/template', $data);
  }

  public function getLanguages(){
    $program = $_POST['program'];
    $data['language'] = $this->root_model->getLanguages($program);
    echo json_encode($data);
  }

  public function follow_up(){
    $data['main_content'] = 'english_roots/follow_up';
    $this->load->view('inc/template', $data);
  }

  public function getRegistrations(){
    $data['details'] = $this->root_model->getRegistrations();
    echo json_encode($data);
  }

  public function getRegistrationDetails(){
    $data['regDetails'] = $this->root_model->getRegistrationDetails();
    $data['selected_status'] = $this->root_model->getStatus();
    echo json_encode($data);
  }

  public function updateStatus(){
    $data = $this->root_model->updateStatus();
    echo ($data);
  }

  public function get_add_programme_details_by_id(){
    $level_id = $_POST['level_id'];
    $result = $this->root_model->get_programme_details_by_id($level_id);
    echo json_encode($result);
  }
  public function getBatchCodeDetails(){
    $level_id = $_POST['level_id'];
    $result = $this->root_model-> get_batch_code($level_id);
    echo json_encode($result);
  }

  public function generate_batch_code(){
    $level_id = $_POST['level_id'];
    $data=$this->root_model->generate_batch_code_insert($level_id);
    echo ($data);
  }


}
?>