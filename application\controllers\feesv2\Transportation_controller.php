<?php

class Transportation_controller extends CI_Controller
{

	function __construct()
	{
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
			redirect('dashboard', 'refresh');
		}
		$this->load->model('feesv2/transport_model');
		$this->load->model('feesv2/fees_transport_model');
		$this->load->model('parent_model');
		$this->load->model('feesv2/fees_student_model');
		$this->load->model('feesv2/fees_cohorts_model');
	 	$this->load->model('feesv2/fees_collection_model');
	}

	public function index($trans_blueprint_id){
		$data['trans_blueprint_id'] = $trans_blueprint_id;
	 	$data['trans_stop'] = $this->fees_transport_model->get_stops_all();
	 	$data['trans_route'] = $this->fees_transport_model->get_route_all();
		$data['main_content']    = 'parent/feesv2/transport';
		$this->load->view('inc/template', $data);
	}

	public function get_stagebyStopId(){
		$trasnStop = $_POST['trasnStop'];
	 	$transStage = $this->transport_model->stop_wise_get_stage_name($trasnStop);
    	echo json_encode($transStage);
	}

	public function get_routebyStopId(){
		$trasnRoute = $_POST['trasnRoute'];
	 	$transRoute = $this->transport_model->route_wise_get_stop_name($trasnRoute);
    	echo json_encode($transRoute);
	}
	public function insert_transportation_fees(){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$input = $this->input->post();
		$fbp = $this->transport_model->get_blueprint_details($input['trans_blueprint_id']);
		$result = $this->transport_model->update_transport_detailsTostudentYear($studentId, $input);
		if (!empty($result)) {

			$data['student'] = $this->fees_student_model->get_std_detailsbyId($studentId, $fbp->acad_year_id);

			if (empty($data['student'])) {
				redirect('feesv2/fees_collection/fee_collect');
			}

          	$cohort_id = $this->fees_student_model->determine_cohort($fbp->id, $data['student']);
	          	if(!empty($cohort_id)){
		          	$installments_types = $this->fees_collection_model->get_installment_types($fbp->id);
		          	foreach ($installments_types as $key => $val) {
		          		$fee_amount = $this->fees_collection_model->fee_cohort_component_structure($cohort_id, $val->feev2_blueprint_installment_types_id);
	          	}
          	}
	        $input = array();
	        foreach ($fee_amount['insData'] as $key => $value) {
	        	foreach ($value as $key => $val) {
        			$input['blueprint_installment_type_id'] = $val->feev2_blueprint_installment_types_id;
        			$input['comp_amount'][$val->feev2_installment_id][$val->feev2_blueprint_component_id] = $val->compAmount;
        			$input['concession_amount'][$val->feev2_installment_id][$val->feev2_blueprint_component_id] = 0;
        			$input['concession_name'] = '';
	        	}
	        }
         	foreach ($fee_amount['fine_amount'] as $key => $value) {
	        	foreach ($value as $insId => $val) {
        			$input['fine_amount'][$insId] = $val['fine_amount'];;
	        	}
	        }
	        
	        $this->db->trans_begin();

	        $rdata = $this->fees_student_model->insert_cohort_details($fbp->id, $cohort_id, 'STANDARD', $input['blueprint_installment_type_id'], $input['comp_amount'], $input['concession_amount'], $studentId, $input['concession_name'], $input['fine_amount']);

         	if (empty($rdata)){
		      $this->db->trans_rollback();
		    }else{

	    		$status_update = $this->transport_model->publish_transportation($rdata['cohort_student_id']);
	    	 	if (empty($status_update)){
		        	$this->db->trans_rollback();
		       	}
		      	if ($this->db->trans_status()){
		    		$this->db->trans_commit();
		        	$this->session->set_flashdata('flashSuccess', 'Transport stop successfully assigned.');
		        	redirect('parent_controller/display_fee_blueprints');
	   		 	}
	   		}
		}
		
		redirect('parent_controller/display_fee_blueprints');

	}
}