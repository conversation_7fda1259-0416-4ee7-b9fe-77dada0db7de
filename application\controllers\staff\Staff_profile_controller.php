<?php
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of student_controller
 *
 * <AUTHOR>
 */
class Staff_profile_controller extends CI_Controller {
  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STAFF_LOGIN')) {
      redirect('dashboard', 'refresh');
    }    
    $this->load->model('staff/Staff_profile_model');
    $this->load->model('student/Student_Model');
    $this->config->load('form_elements');
	  $this->load->library('filemanager');
  }
 
  public function index() {
    $staffObj = $this->Staff_profile_model->getStaffProfile();
    $data['staffObj'] = $staffObj;
    $data['addresses'] = array(0 => [], 1 => []);
    if(!empty($staffObj))
      $addresses = $this->Staff_profile_model->getStaffAddresses($staffObj->id);
    foreach ($addresses as $key => $add) {
      $data['addresses'][$add->address_type] = $add;
    }
    $data['payroll_data'] = $this->Staff_profile_model->get_payroll_data_staff($staffObj->id);
    $data['staffPayrollData'] = $this->settings->getSetting('enabled_staff_payroll_data');
    $data['permitProfileEdit'] = $this->settings->getSetting('staff_profile_enableStaffProfileEdit');
    $data['main_content'] = 'staff/profile/index';
    $this->load->view('inc/template', $data);
  }

  public function edit_profile() {
    $data['staffData'] = $this->Staff_profile_model->getStaffProfile();
    $data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
    $data['addresses'] = array(0 => [], 1 => []);
    $addresses = $this->Staff_profile_model->getStaffAddresses($data['staff_id']);
    foreach ($addresses as $key => $add) {
      $data['addresses'][$add->address_type] = $add;
    }
    $staffEditPayroll = $this->settings->getSetting('');
    $data['payroll_data'] = $this->Staff_profile_model->get_payroll_data_staff($data['staff_id']);
    // echo '<pre>'; print_r($data['payroll_data']); die();
    $data['staffPayrollData'] = $this->settings->getSetting('enabled_staff_payroll_data');
    $data['main_content'] = 'staff/profile/edit_profile';
    $this->load->view('inc/template', $data);
  }

  public function edit_profile_mobile() {
    $data['staffData'] = $this->Staff_profile_model->getStaffProfile();
    $data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
    $data['addresses'] = $this->Staff_profile_model->getStaffAddresses($data['staff_id']);
    // echo '<pre>'; print_r($data['addresses']); die();
    $data['main_content'] = 'staff/profile/edit_mobile';
    $this->load->view('inc/template', $data);
  }

  public function save_profile_data() {
    echo $this->Staff_profile_model->saveProfileData();
  }

  public function save_payroll_data(){
    echo $this->Staff_profile_model->savePayrollData();
  }
  public function save_address_data() {
    // echo '<pre>'; print_r($_POST); die();
    echo $this->Staff_profile_model->saveAddressData();
  }

  public function save_profile_photo() {
    $input = $_POST;
    $file = $_FILES['file'];
    $photo = $this->s3FileUpload($file);
    $status = 0;
    if($photo['file_name'] != '') {
      $status = $this->Staff_profile_model->updateProfilePhoto($input['id'], $photo);
    }
    echo $status;
  }

  public function s3FileUpload($file) {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'profile');
  }

  public function updateBasic(){
    $input = $_POST;

    if (isset($_FILES['photograph'])) {
      $status = (int)$this->Staff_profile_model->updateBasicDetails($input, $this->s3FileUpload($_FILES['photograph']));
    }
    else {
      $status = (int)$this->Staff_profile_model->updateBasicDetails($input);
    }
    if($status) {
      $staffObj = $this->Staff_profile_model->getStaffProfile();
      $staffObj->dob = date("d-m-Y", strtotime($staffObj->dob));
      echo json_encode($staffObj);
    }
  }

  public function updateOtherInfo(){
    $input = $_POST;
    // echo "<pre>"; print_r($input); die();
    $status = (int)$this->Staff_profile_model->updateOtherDetails($input);
    $staffObj = $this->Staff_profile_model->getStaffProfile();
    $staffObj->dob = date("d-m-Y", strtotime($staffObj->dob));
    $staffObj->joining_date = date("d-m-Y", strtotime($staffObj->joining_date));
    echo json_encode($staffObj);
  }

  private function _prepareStaffAddressInput(&$input) {
    $return_data = [];
    $present_flag = 0;
    $permanent_flag = 0;

    foreach ($input as $k => $v) {
      $start_key = substr($k, 0, 2);
      
      if ($start_key == 'a_') {
        if(substr($k, 0, 9) == 'a_present') {
          if(!empty($v)) 
            $present_flag = 1;

          $key = str_replace("a_present_","",$k);
          $return_data['address_present'][$key] = $v;
        }
        else {
          if(!empty($v)) 
            $permanent_flag = 1;

          $key = str_replace("a_permanent_","",$k);
          $return_data['address_permanent'][$key] = $v;
        }
      } 
    }

    $return_data['address_present']['flag'] = $present_flag;
    $return_data['address_permanent']['flag'] = $permanent_flag;

    //echo '<pre>';print_r($return_data);die();

    return $return_data;
  }

  public function updateAddressInfo(){
    $input_form = $_POST;
    $stakeholder_id = $input_form['staffId'];
    $grouped_input = $this->_prepareStaffAddressInput($input_form);
    $status = (int)$this->Student_Model->addAddressInfo($grouped_input['address_present'], $stakeholder_id, 4, 0);
    $status = (int)$this->Student_Model->addAddressInfo($grouped_input['address_permanent'], $stakeholder_id, 4, 1);
    $addresses = $this->Staff_profile_model->getStaffAddresses($stakeholder_id);  
    echo json_encode($addresses);
  }

  public function updateSchoolOff(){
    $input = $_POST;
    $status = (int)$this->Staff_profile_model->updateSchoolDetails($input);
    if($status) {
      $staffObj = $this->Staff_profile_model->getStaffProfile();
      $staffObj->dob = date("d-m-Y", strtotime($staffObj->dob));
    $staffObj->joining_date = date("d-m-Y", strtotime($staffObj->joining_date));
      echo json_encode($staffObj);
    }
  }

  public function update_profile_confirmedstaffbyId(){
    $result = $this->Staff_profile_model->update_staff_profile_confirmedbyid($_POST['staffId']);
    echo json_encode($result);
  }
}
