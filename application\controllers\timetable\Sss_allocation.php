<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  15 March 2018
 *
 * Description: Controller for Sections - CRUD class teacher, CRUD staff-class/section/subject assignment.
 *
 * Requirements: PHP5 or above
 *
 */

class Sss_allocation extends CI_Controller {
	function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      // if (!$this->authorization->isSuperAdmin()) {
      //   redirect('dashboard', 'refresh');
      // }	
      if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
        redirect('dashboard', 'refresh');
      }  
      $this->load->model('timetable/sss_allocation_model');
	}

  //Landing function to display classes
  public function index($classId = '') {
    if (empty($classId))
      $classId = $this->input->post('classId');
    $data['classList'] = $this->sss_allocation_model->getClassList();

    if (empty($classId) && !empty($data['classList']))
      $classId = $data['classList'][0]->classId;

    $data['selectedClassId'] = $classId;
    $data['classSectionList'] = $this->sss_allocation_model->getSectionList($classId);
    $data['main_content']    = 'timetable/sss_alloc/index';
    $this->load->view('inc/template', $data); 
  }

  public function updateclasstecher($classSectionId) {
    $result = $this->sss_allocation_model->updatClassTeacher($classSectionId);
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Class teacher assigned');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    }
    redirect('timetable/sss_allocation/index/'.$_POST['class_id']);
  }

  public function add_class_teacher($classSectionId) {
    $data['selectedCtId'] = $this->sss_allocation_model->getClassTeacherId($classSectionId);
    $data['selectedActId'] = $this->sss_allocation_model->getAssistatClassTeacherId($classSectionId);
    $data['class_data'] = $this->sss_allocation_model->getClassData($classSectionId);
    $data['className'] = $this->sss_allocation_model->getClassName($classSectionId);
    $data['classSectionId'] = $classSectionId;
    $data['teacherNames'] = $this->sss_allocation_model->getStaffDetails();

    $data['main_content'] = 'timetable/sss_alloc/add_class_teacher';
    $this->load->view('inc/template', $data);
  }
 
  public function crud_staff_subjects($csId) {
    $data['isSectionInCurrentYear'] = $this->sss_allocation_model->isSectionInCurrentYear($csId);

    //echo '<pre>';print_r($data['isSectionInCurrentYear']);die();

    if ($data['isSectionInCurrentYear'] == 1) {
      $data['staffDetails'] = $this->sss_allocation_model->getStaffDetails();
      $data['roomDetails'] = $this->sss_allocation_model->getRoomDetails();
      $data['subjectDetails'] = $this->sss_allocation_model->getSubjectDetails($this->sss_allocation_model->getClassId($csId), $csId);
      $data['csList'] = $this->sss_allocation_model->getClassSectionList($csId);
      $data['csAllList'] = $this->sss_allocation_model->getAllClassSectionList();
      $data['csDetails'] = $this->sss_allocation_model->getClassSectionDetailsById($csId);
      $data['sssArr'] = $this->sss_allocation_model->getSSSDetails($csId);
      
      $ctObj = $this->sss_allocation_model->getClassTeacherObj($csId);
      $data['classTeacherId'] = (empty($ctObj->smId)?'':$ctObj->smId);
      $data['classTeacherName'] = (empty($ctObj->staffName)?'Not Assigned':$ctObj->staffName);;
      $data['totalAllocations'] = $this->sss_allocation_model->getAllocationCount($csId);  
    }

    $data['main_content'] = 'timetable/sss_alloc/crud_staff_subjects/index';
    $this->load->view('inc/template', $data);
  }

  public function submit_staff_subjects($csId) {
    //echo '<pre>';print_r($this->input->post());die();
    $result = $this->sss_allocation_model->submit_staff_subjects($csId);

    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully inserted staff subject allocation');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }

    redirect('timetable/sss_allocation/crud_staff_subjects/'.$csId);
  }

  public function deleteAllocation($csId,$subId){    
    $result = (int)$this->sss_allocation_model->deleteAllocationbyId($csId,$subId);
    echo $result;

    if($result == -1) {
      $this->session->set_flashdata('flashInfo', 'Please unassign the respective allocation from timetable first in order to delete/modify this allocation');
    } else if ($result == 0) {
      $this->session->set_flashdata('flashError', 'Something went wrong. Error code number 101');
    } else {
      $this->session->set_flashdata('flashSuccess', 'Successfully deleted staff subject allocation');
    }

    redirect('timetable/sss_allocation/crud_staff_subjects/'.$csId);
  }

  public function deallocateAllocation() {
    $csId = $_POST['csId'];
    $subjectId = $_POST['subjectId'];
    $data['deallocStatus'] = $this->sss_allocation_model->deallocAllocation($csId, $subjectId);

    echo json_encode($data);
  }

  public function deleteAndEditAllocationDetails() {
    $csId = $_POST['csId'];
    $subjectId = $_POST['subjectId'];

    //Capture the data
    $data['allocData'] = $this->sss_allocation_model->getCloneSubjectAllocation($csId, $subjectId);
    //Delete the Allocation
    $data['delResult'] = (int)$this->sss_allocation_model->deleteAllocationbyId($csId,$subjectId);

    //Data to refresh the tables
    $data['sssArr'] = $this->sss_allocation_model->getSSSDetails($csId);
    $data['subjectDetails'] = $this->sss_allocation_model->getSubjectDetails($this->sss_allocation_model->getClassId($csId), $csId);

    echo json_encode($data);
  }

  public function getCopySubjectDetails() {
    $copyCSId = $_POST['copyCSId'];
    $pasteCSId = $_POST['pasteCSId'];

    $data['subjects'] = $this->sss_allocation_model->getCopySubjectDetails($copyCSId, $pasteCSId);
    echo json_encode($data);
  }

  public function getCloneSubjectDetails() {
    $cloneCSId = $_POST['cloneCSId'];

    $data['subjects'] = $this->sss_allocation_model->getCloneSubjectDetails($cloneCSId);
    echo json_encode($data);
  }

  public function getCloneAllocationDetails() {
    $cloneCSId = $_POST['cloneCSId'];
    $cloneSubjectId = $_POST['cloneSubjectId'];

    echo json_encode($this->sss_allocation_model->getCloneSubjectAllocation($cloneCSId,$cloneSubjectId));
  }

  public function copyStaffAllocation($pasteCSId) {
    $result = $this->sss_allocation_model->copyStaffAllocation($pasteCSId);

    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully copied the allocation');
    } else {
      $this->session->set_flashdata('flashError', 'Something went Wrong. Error Code 102');
    }

    redirect('timetable/sss_allocation/crud_staff_subjects/'.$pasteCSId);
  }

}
