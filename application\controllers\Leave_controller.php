<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Academic Year
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Leave_controller extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
  		redirect('auth/login', 'refresh');
  	}
    if (!$this->authorization->isModuleEnabled('LEAVE') && !$this->authorization->isModuleEnabled('LEAVE_V2')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('leave_model');
    $this->load->model('student/Student_leave_model');
    $this->load->model('avatar');
    $this->load->model('observation_model');
    $this->load->model('student/Student_Model');
    $this->load->model('parent_model');
    $this->load->model('observation_model');
    $this->load->library('filemanager');
	}

	public function index(){
    $data['apply_leave_v1'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPLY') && $this->authorization->isModuleEnabled('LEAVE');
    $data['approve_leave_v1'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPROVE') && $this->authorization->isModuleEnabled('LEAVE');
    $data['leave_report_v1'] = $this->authorization->isAuthorized('LEAVE.LEAVE_REPORT') && $this->authorization->isModuleEnabled('LEAVE');

    $data['apply_leave_v2'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPLY') && $this->authorization->isModuleEnabled('LEAVE_V2');
    $data['approve_leave_v2'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPROVE') && $this->authorization->isModuleEnabled('LEAVE_V2');
    $data['leave_report_v2'] = $this->authorization->isAuthorized('LEAVE.LEAVE_REPORT') && $this->authorization->isModuleEnabled('LEAVE_V2');
    $data['leave_category_v2'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_CATEGORY') && $this->authorization->isModuleEnabled('LEAVE_V2');
    $data['staff_leave_quota_v2'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_QUOTA') && $this->authorization->isModuleEnabled('LEAVE_V2');
    $data['staff_leave_year_v2'] = $this->authorization->isSuperAdmin();


    $data['permitStudentLeave'] = $this->authorization->isAuthorized('LEAVE.STUDENT_LEAVE_APPLY');
    $data['permit_student_leave_approval'] = $this->authorization->isAuthorized('LEAVE.STUDENT_LEAVE_APPROVE');
    $data['permit_staff_leave_approval'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPROVE');
    $data['permitStaffLeave'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPLY');
    $data['leave_permit'] = $this->authorization->isAuthorized('LEAVE.LEAVE_REPORT');

    $site_url = site_url();
    /*$data['staff_tiles'] = array(
        [
          'title' => 'Staff Leave',
          'sub_title' => 'Apply Leave for Staff',
	        'icon' => 'svg_icons/staff.svg',
          'url' => $site_url.'staff/staffleave_controller/leaveInfo',
          'permission' => $data['apply_leave_v1']
        ],
        [
          'title' => 'Staff Leaves',
          'sub_title' => 'View/apply leaves(v2)',
	        'icon' => 'svg_icons/applyleave.svg',
          'url' => $site_url.'staff/leaves/staff_leaves',
          'permission' => $data['apply_leave_v2']
          // 'permission' => $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPLY')
        ],
        [
          'title' => 'Approve Leaves',
          'sub_title' => 'Approve staff leave',
	        'icon' => 'svg_icons/provisionstaff.svg',
          'url' => $site_url.'staff/staffleave_controller/leave_approval',
          'permission' => $data['approve_leave_v1']
        ],
        [
          'title' => 'Leave Categories',
          'sub_title' => 'Define Leave Categories',
	        'icon' => 'svg_icons/categoryreports.svg',
          'url' => $site_url.'staff/leaves/leave_categories',
          'permission' => $data['leave_category_v2']
        ],
        [
          'title' => 'Staff Quota',
          'sub_title' => 'Add Staff Leave Quota',
	        'icon' => 'svg_icons/daywiseclassattendance.svg',
          'url' => $site_url.'staff/leaves/staff_quota',
          'permission' => $data['staff_leave_quota_v2']
        ],
        [
          'title' => 'Staff Leave Year',
          'sub_title' => 'Manage staff leave years',
	        'icon' => 'svg_icons/monthwiseclassattendance.svg',
          'url' => $site_url.'staff/leaves/staff_leave_year',
          'permission' => $data['staff_leave_year_v2']
        ]
      
    );
    $data['staff_tiles'] = checkTilePermissions($data['staff_tiles']);*/

    $data['student_tiles'] = array(
        [
          'title' => 'Student Leaves',
          'sub_title' => 'Manage Leave of individual students',
	        'icon' => 'svg_icons/student.svg',
          'url' => $site_url.'leave_controller/student_leave',
          'permission' => $this->authorization->isAuthorized('LEAVE.STUDENT_LEAVE_APPLY')
        ],
        [
          'title' => 'Approve Leaves',
          'sub_title' => 'Approve student leaves',
	        'icon' => 'svg_icons/provisionstaff.svg',
          'url' => $site_url.'student/studentleave_admin_controller',
          'permission' => $this->authorization->isAuthorized('LEAVE.STUDENT_LEAVE_APPROVE')
        ]
    );
    $data['student_tiles'] = checkTilePermissions($data['student_tiles']);

    /*$data['report_tiles'] = array(
        [
          'title' => 'Staff Leaves',
          'sub_title' => 'View Staff Leave',
	        'icon' => 'svg_icons/applyleave.svg',
          'url' => $site_url.'staff/staffleave_controller/getAggregateReport',
          'permission' => $data['leave_report_v1']
        ],
        [
          'title' => 'Staff Leaves Report',
          'sub_title' => 'Staff Leave(v2) Report',
	        'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'staff/leaves/staff_leave_report',
          'permission' => $data['leave_report_v2']
        ]
        
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);*/
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'leave_management/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'leave_management/index_mobile';
    }else{
      $data['main_content'] = 'leave_management/index';    	
    }

    
    $this->load->view('inc/template', $data);
	}

  public function student_leave(){
    // $AvatarId = $this->authorization->getAvatarId();
    // $avatar = $this->avatar->getAvatarById($AvatarId);
    // $staffId = $avatar->stakeholderId;
    $data['leavesApplied'] = $this->Student_leave_model->get_Leaves(1);
    $data['main_content'] = 'student/student_leave/admin_applied/index';
    $this->load->view('inc/template', $data);
  }

   public function get_Leaves(){
        $val = $_POST['val'];
        $result = $this->Student_leave_model->get_Leaves($val);
        $isAllowed = $this->authorization->isModuleEnabled('STUDENT_LEAVE');
        $AvatarId = $this->authorization->getAvatarId();
        if(!$isAllowed) {
            $data['avatar'] = $this->avatar->getAvatarById($AvatarId);
            $data['classSection'] = $this->Student_leave_model->getClassSection($data['avatar']->stakeholderId);
        }
        foreach ($result as $key => $val) {
            $val->action = '<p>'.$val->description.'</p>';
            if ($val->status == 'Pending' || $val->status == 'Auto Approved') {
                $disabled = "";
                if($val->leave_filed_by != $AvatarId) {
                    $disabled = 'disabled';
                }
                $val->action = '<a '. $disabled .' style="margin:2px;" href='.site_url("leave_controller/student_leave_edit/" . $val->id). ' class="btn btn-warning " data-placement="top"  data-toggle="tooltip" data-original-title="Edit Leave"><i class="fa fa-edit"></i></a><a '. $disabled .' style="margin:2px;" href='.site_url("leave_controller/delete_student_leave/" . $val->id). ' class="btn btn-warning " data-placement="top"  data-toggle="tooltip" data-original-title="Delete Leave"><i class="fa fa-trash-o"></i></a>';
            }
        }
        echo json_encode($result);
    }

    public function applyStudentLeave(){
      $applyStudentLeaveForPreviousDates = $this->authorization->isAuthorized('LEAVE.APPLY_STUDENT_LEAVE_FOR_PREVIOUS_DATES');
      $data['applyStudentLeaveForPreviousDates'] = $applyStudentLeaveForPreviousDates;
      $data['getclassinfo'] = $this->Student_Model->getclass();
      $data['main_content'] = 'student/student_leave/admin_applied/apply_leave';
      $this->load->view('inc/template', $data);
    }

    public function get_section(){
      $class_id =$this->input->post('className');
      $section = $this->observation_model->get_classwiseSection($class_id);
      echo json_encode(array('section'=>$section));
    }
    public function get_student() {
      $sectionId = $this->input->post('section');
      echo json_encode($this->Student_leave_model->getStudentsBySection($sectionId));
    }

    public function submit_student_Leave_for_staff(){
      $status = (int)$this->Student_leave_model->submit_student_Leave_for_staff($this->s3FileUpload($_FILES['file_upload']));
      if ($status == 1) {
        $this->session->set_flashdata('flashSuccess', 'Leave Applied Successfully');
      } else {
        $this->session->set_flashdata('flashError', 'Something Went Wrong..');
      }
      redirect('leave_controller/student_leave');
    }

    public function s3FileUpload($file) {
      if ($file['tmp_name'] == '' || $file['name'] == '') {
        return ['status' => 'empty', 'file_name' => ''];
      }
      $this->load->library('filemanager');
      return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], 'leave');
    }

    public function student_leave_edit($id){
      $applyStudentLeaveForPreviousDates = $this->authorization->isAuthorized('LEAVE.APPLY_STUDENT_LEAVE_FOR_PREVIOUS_DATES');
      $data['applyStudentLeaveForPreviousDates'] = $applyStudentLeaveForPreviousDates;
      $data['edit_student_leave'] = $this->Student_leave_model->get_apply_student_leave_by_id($id);
      // echo "<pre>"; print_r($data['edit_student_leave']); die();
      $data['getclassinfo'] = $this->Student_Model->getclass();
      $data['main_content'] = 'student/student_leave/admin_applied/apply_leave_edit';
      $this->load->view('inc/template', $data);
    }

    public function update_student_Leave_for_staff($id){
      $status = (int)$this->Student_leave_model->update_student_Leave_for_staff_by_id($this->s3FileUpload($_FILES['file_upload']), $id);
      if ($status == 1) {
          $this->session->set_flashdata('flashSuccess', 'Leave edit successfully updated.');
      } else {
          $this->session->set_flashdata('flashError', 'Something Went Wrong..');
      }
       redirect('leave_controller/student_leave');
    }

    public function delete_student_leave($id){
      $status = (int) $this->Student_leave_model->deleteLeaveInfo($id);
      if ($status == 1) {
        $this->session->set_flashdata('flashSuccess', 'Leave Deleted Successfully');
      } else {
        $this->session->set_flashdata('flashError', 'Something Wrong..');
      }
      redirect('leave_controller/student_leave');
    }

}