<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  21 October 2022
 *
 * Description: Controller for Infirmary Module. Entry point for Infirmary Module
 *
 * Requirements: PHP5 or above
 *
 */

class Menu extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('INFIRMARY') || !$this->authorization->isAuthorized('INFIRMARY.MODULE')) {
      redirect('dashboard', 'refresh');
    }
  }

  //Landing function to show Infirmary Menu
  public function index() {
    $site_url = site_url();

    $data['tiles'] = array(
      // [
      //   'title' => 'Manage Visits',
      //   'sub_title' => 'Manage Visits',
      //   'icon' => 'svg_icons/subjects.svg',
      //   'url' => $site_url.'infirmary/visits/view_visits',
      //   'permission' => $this->authorization->isAuthorized('INFIRMARY.CREATE')
      // ],
      [
        'title' => 'Create Visits',
        'sub_title' => 'Create Visits',
        'icon' => 'svg_icons/infirmary_create_visit.svg',
        'url' => $site_url.'infirmary/visits/create_form',
        'permission' => $this->authorization->isAuthorized('INFIRMARY.CREATE')
      ],
      [
        'title' => 'Manage Lab Tests',
        'sub_title' => 'Manage Lab Tests',
        'icon' => 'svg_icons/manage_lab_test.svg',
        'url' => $site_url.'infirmary/visits/test_master_page',
        'permission' => $this->authorization->isAuthorized('INFIRMARY.CREATE')
      ],
      [
        'title' => 'Manage Activity',
        'sub_title' => 'Manage Activity',
        'icon' => 'svg_icons/manage_activity.svg',
        'url' => $site_url.'infirmary/visits/manage_activity_page',
        'permission' => $this->authorization->isAuthorized('INFIRMARY.INFIRMARY_ADMIN')
      ],
      [
        'title' => 'Guest Checkup',
        'sub_title' => 'Guest Visits',
        'icon' => 'svg_icons/guest_checkup.svg',
        'url' => $site_url.'infirmary/visits/create_form_for_guest',
        'permission' => $this->authorization->isAuthorized('INFIRMARY.CREATE')
      ],
      [
        'title' => 'Medical Expense Mass Upload',
        'sub_title' => 'Medical Expense Mass Upload',
        'icon' => 'svg_icons/medical_expense_mass_upload.svg',
        'url' => $site_url.'infirmary/visits/medical_expense_mass_upload',
        'permission' => $this->authorization->isAuthorized('INFIRMARY.MEDICAL_EXPENSE_MASS')
      ]

    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
      [
        'title' => 'Visitor Report',
        'sub_title' => 'Visitor Report',
        'icon' => 'svg_icons/visitors_report.svg',
        'url' => $site_url.'infirmary/visits/visitor_report',
        'permission' =>  $this->authorization->isAuthorized('INFIRMARY.VIEW_REPORTS')
      ],
      [
        'title' => 'Visitor Summary Report',
        'sub_title' => 'Visitor Summary Report',
        'icon' => 'svg_icons/visitor_summary_report.svg',
        'url' => $site_url.'infirmary/visits/visitor_summary_report',
        'permission' =>  $this->authorization->isAuthorized('INFIRMARY.VIEW_REPORTS')
      ],
       [
        'title' => 'Student and Staff Visit Report',
        'sub_title' => 'Student and Staff Visit Report',
        'icon' => 'svg_icons/student_staff_report.svg',
        'url' => $site_url.'infirmary/visits/student_staff_report',
        'permission' =>  $this->authorization->isAuthorized('INFIRMARY.VIEW_REPORTS')
       ],
      [
        'title' => 'Lab Test Reports',
        'sub_title' => 'Test Reports',
        'icon' => 'svg_icons/lab_test_reports.svg',
        'url' => $site_url.'infirmary/visits/lab_report',
        'permission' =>  $this->authorization->isAuthorized('INFIRMARY.VIEW_REPORTS')
      ],
      [
        'title' => 'Hospitalization Report',
        'sub_title' => 'Hospitalization Report',
        'icon' => 'svg_icons/hospitalization_report.svg',
        'url' => $site_url.'infirmary/visits/hospitalization_report',
        'permission' =>  $this->authorization->isAuthorized('INFIRMARY.VIEW_REPORTS')
      ],
      [
        'title' => 'Guest Visits Report',
        'sub_title' => 'Guest Visitor Report',
        'icon' => 'svg_icons/guest_visit_report.svg',
        'url' => $site_url.'infirmary/visits/guest_visitor_report',
        'permission' =>  $this->authorization->isAuthorized('INFIRMARY.VIEW_REPORTS')
      ],
      [
        'title' => 'Medical Expense Individual Report',
        'sub_title' => 'Medical Expense Individual Report',
        'icon' => 'svg_icons/medical_expense_individual_report.svg',
        'url' => $site_url.'infirmary/visits/medical_expense_individual_report',
        'permission' => $this->authorization->isAuthorized('INFIRMARY.VIEW_REPORTS')
      ],
      [
        'title' => 'Medical Expenses Report',
        'sub_title' => 'Medical Expenses Report',
        'icon' => 'svg_icons/medical_expense_report.svg',
        'url' => $site_url.'infirmary/visits/medical_expense_report',
        'permission' => $this->authorization->isAuthorized('INFIRMARY.VIEW_REPORTS')
      ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['admin_tiles'] = array(
      [
        'title' => 'Manage Symptoms',
        'sub_title' => 'Manage Symptoms',
        'icon' => 'svg_icons/manage_symptoms.svg',
        'url' => $site_url.'infirmary/visits/manage_symptoms_page',
        'permission' => $this->authorization->isAuthorized('INFIRMARY.INFIRMARY_ADMIN')
      ],
      
      [
        'title' => 'Manage Medicines',
        'sub_title' => 'Manage Medicines',
        'icon' => 'svg_icons/manage_medicines.svg',
        'url' => $site_url.'infirmary/visits/manage_medicines_page',
        'permission' => $this->authorization->isAuthorized('INFIRMARY.INFIRMARY_ADMIN')
      ],
      [
        'title' => 'Manage Diagnosis',
        'sub_title' => 'Manage Diagnosis',
        'icon' => 'svg_icons/manage_diagnosis.svg',
        'url' => $site_url.'infirmary/visits/manage_diagnosis_page',
        'permission' => $this->authorization->isAuthorized('INFIRMARY.INFIRMARY_ADMIN')
      ],
      [
        'title' => 'Manage Lab Tests Master',
        'sub_title' => 'Manage Tests Master',
        'icon' => 'svg_icons/manage_lab_test_master.svg',
        'url' => $site_url.'infirmary/visits/manage_test_page',
        'permission' => $this->authorization->isAuthorized('INFIRMARY.INFIRMARY_ADMIN')
      ]
      
      );
    
    $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);

    $data['main_content']    = 'infirmary/menu';
    $this->load->view('inc/template', $data);
  }

  

}