<?php

  defined('BASEPATH') OR exit('No direct script access allowed');

  class Tally extends CI_Controller {
    public function __construct() {
      parent::__construct();
    }

    //accepts json data and return json data
    public function index() {
      $data = file_get_contents('php://input');

      /*
      Recieved request
      {
         "ENVELOPE":{
            "Vouchers":{
               "Short Name Field":"Date From",
               "From":"1-Feb-2020",
               "Name Field":"Date To",
               "To":"2-Feb-2020"
            }
         }
      }*/
    
      // echo "<pre>"; print_r(json_encode($data1)); die();
      // echo "<pre>"; print_r($data); die();
      // $data = array(
      //       'errno' => 'Tester',
      //       'errtype' => 'Error',
      //       'errstr' => $data,
      //       'errfile' => 'Tester',
      //       'errline' => 1,
      //       'user_agent' => 'Tally',
      //       'ip_address' => '0.0.0.0',
      //       'time' => date('Y-m-d H:i:s')
      //   );

      // echo "<pre>"; print_r($data1); die();
      // $this->db->insert('logs', $data);
      
      /*$rData = json_decode($data);
      $vouchers = array();
      $this->acad_year->loadAcadYearDataToSession();
      $this->load->model('feesv2/reports_model');
      $fromDate = $rData->ENVELOPE->Vouchers->From;
      $toDate = $rData->ENVELOPE->Vouchers->To;
      $transData = $this->reports_model->get_trans_details_by_date_wise($fromDate, $toDate);
      foreach ($transData as $key => $val) {
        $voucher = array(
              'voucher_number'=>$val->receipt_number,
              'voucher_type'=>'Receipt',
              'narration'=>$val->student_name.' ( '.$val->classSection.' ) '.$val->remarks,
              'amount'=>$val->total_amount,
              'date'=>$val->receipt_date,
              'party_ledger_name' => 'Cash',
              'party_name'        => 'Cash',
              'persisted_view'=>'Accounting Voucher View',
              'ledgers' => []
        );
        foreach ($val->comp as $key => $val) {
          $voucher['ledgers'][] =array(
                'led_name'    => $val['comp_name'],
                'led_amount'  => $val['comp_amount']
          );
            
        }
        $vouchers['data'][] = $voucher;
      }
      echo json_encode($vouchers);*/
      
      $voucher = array(
        'data' => [
                [
                  'voucher_number'    => 'AJAY B',
                  'voucher_type'      => 'Receipt',
                  'narration'         => 'Fees payed by AJAY B',
                  'amount'            => '1469',
                  'date'              => '2-Apr-2019',
                  'party_ledger_name' => 'Cash',
                  'party_name'        => 'Cash',
                  'persisted_view'    => 'Accounting Voucher View',
                  'ledgers' => [
                    [
                      'led_name'    => 'Tution Fees Collected -Pu',
                      'led_amount'  => '1330'
                    ],
                    [
                      'led_name'    => 'KSSBF - PU',
                      'led_amount'  => '25'
                    ],
                    [
                      'led_name'    => 'KSTBF - PU',
                      'led_amount'  => '25'
                    ],
                    [
                      'led_name'    => 'NSS-PU',
                      'led_amount'  => '5'
                    ],
                    [
                      'led_name'    => 'Sports Fees Collected - Pu',
                      'led_amount'  => '84'
                    ]
                  ]
                ]
              ]
      );
      echo json_encode($voucher);

    }

  } 
?>