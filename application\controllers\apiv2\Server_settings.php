<?php
require APPPATH . 'libraries/REST_Controller.php';
 
class Server_settings extends REST_Controller {
    
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
            $data = ['error' => 'User not logged in'];
            $this->response($data, REST_Controller::HTTP_UNAUTHORIZED);
			$this->output->_display();
			exit;
		}
		$this->load->model('parentv2/Circular_model', 'circular');
	}

    public function index_get () {
        $data = [
            'online_class_server' => $this->settings->getSetting('online_class_server'),
            'online_recording' => $this->settings->getSetting('online_recording'),
        ];

        $this->response($data, REST_Controller::HTTP_OK);
        $this->output->_display();
        exit;    
    }
}