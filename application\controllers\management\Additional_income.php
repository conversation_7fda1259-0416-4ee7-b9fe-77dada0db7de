<?php
    defined('BASEPATH') OR exit('No direct script access allowed');
    /**
     *  oxygen v2
     */
    class Additional_income extends CI_Controller { 
        public function __construct() {
            parent::__construct();
            if (!$this->ion_auth->logged_in()) {
                redirect('auth/login', 'refresh');
            }
            if (!$this->authorization->isModuleEnabled('ADDITIONAL_INCOME') || !$this->authorization->isAuthorized('ADDITIONAL_INCOME.MODULE')) {
                redirect('dashboard', 'refresh');      
            }
          
            $this->load->model('Additional_income_model', 'aim');
            $this->load->library('filemanager'); 

        }

        public function index() {
            $data['permit_add_category'] = $this->authorization->isAuthorized('ADDITIONAL_INCOME.ADD_CATEGORY');
            $data['permit_add_income'] = $this->authorization->isAuthorized('ADDITIONAL_INCOME.ADD_INCOME');
            $data['permit_view_reports'] = $this->authorization->isAuthorized('ADDITIONAL_INCOME.VIEW_REPORTS');
            
            $site_url = site_url();
            $data['tiles'] = array(
              [
                'title' => 'Add Income',
                'sub_title' => 'Add a New Additional Income',
                'icon' => 'svg_icons/add.svg',
                'url' => $site_url.'management/Additional_income/add_additional_income',
                'permission' => $this->authorization->isAuthorized('ADDITIONAL_INCOME.ADD_INCOME')
              ],
              [
                'title' => 'Income Category',
                'sub_title' => 'Add new Income Category',
                'icon' => 'svg_icons/expensecategory.svg',
                'url' => $site_url.'management/Additional_income/income_category/',
                'permission' => $this->authorization->isAuthorized('ADDITIONAL_INCOME.ADD_CATEGORY')
              ]          
            );
            $data['tiles'] = checkTilePermissions($data['tiles']);

            $data['report_tiles'] = array(
              [
                'title' => 'Income Report',
                'sub_title' => 'Additional Income Report',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url.'management/Additional_income/additional_income_report',
                'permission' => $this->authorization->isAuthorized('ADDITIONAL_INCOME.VIEW_REPORTS')
              ],
              [
                'title' => 'Category Reports',
                'sub_title' => '',
                'icon' => 'svg_icons/categoryreports.svg',
                'url' => $site_url.'management/Additional_income/income_category_reports',
                'permission' => $this->authorization->isAuthorized('ADDITIONAL_INCOME.VIEW_REPORTS')
              ]          
            );
            $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

            $data['main_content'] = 'management/aim/index';
            $this->load->view('inc/template', $data);
        }

        public function add_additional_income(){
            $data['categories_list'] = $this->aim->getCategoriesList();
            $data['main_content'] = 'management/aim/add';
            $this->load->view('inc/template', $data);
        }
        public function income_category(){
            if (!$this->authorization->isAuthorized('ADDITIONAL_INCOME.ADD_CATEGORY')) {
                redirect('dashboard', 'refresh');
            }
            $data['categories_list'] = $this->aim->getCategoriesList();
            
            $data['main_content'] = 'management/aim/category';
            $this->load->view('inc/template', $data);
        }
        
        public function add_income_category(){
            if (!$this->authorization->isAuthorized('ADDITIONAL_INCOME.ADD_INCOME')) {
                redirect('dashboard', 'refresh');
            }
            $data['categories_list'] = $this->aim->getCategoriesList();
            $name=$this->input->post('name');
            $this->aim->add_income_categoty($name);
            redirect('management/Additional_income/income_category');

        }

        public function saveIncomeData(){
            $filename = '';
            $_POST['date'] = date('Y-m-d', strtotime($_POST['date']));
            if (isset($_FILES['userfile'])) {
            $filepath = $this->s3FileUpload($_FILES['userfile']);
            // echo $filepath; die();
            $result = $this->aim->saveIncomeData($filepath['file_name']);
            } else {
            $result = $this->aim->saveIncomeData();
            }
            if($result)
                $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
            else
                $this->session->set_flashdata('flashError', 'Something went wrong.');
                // echo $file_path;
            // echo '<pre>'; print_r($_POST); die();

            redirect('management/Additional_income/add_additional_income');
        }
        public function s3FileUpload($file) {
            if($file['tmp_name'] == '' || $file['name'] == '') {
              return ['status' => 'empty', 'file_name' => ''];
             }        
            return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'Voucher');
            }
        
            public function additional_income_report(){
                if (!$this->authorization->isAuthorized('ADDITIONAL_INCOME.VIEW_REPORTS')) {
                    redirect('dashboard', 'refresh');
                }
                $data['data']=$this->aim->additional_income_report();
                $data['daterange'] = 3;
                //  echo "<pre>"; print_r($data); die();
                $data['main_content'] = 'management/aim/reports';
                $this->load->view('inc/template', $data);
            }
            
            public function additional_income_report1(){
                if (!$this->authorization->isAuthorized('ADDITIONAL_INCOME.VIEW_REPORTS')) {
                    redirect('dashboard', 'refresh');
                }
                if(!isset($_POST['daterange']))
                    redirect('management/Additional_income/income_category_reports');
                $from = (isset($_POST['from_date'])? $_POST['from_date']: "0");  
                $to = (isset($_POST['to_date'])? $_POST['to_date']: "0"); 
                
                $data['data']=$this->aim->additional_income_report1($_POST['daterange'], $from, $to);
                $data['daterange'] = $_POST['daterange'];
                $data['from_date'] = $from;
                $data['to_date'] = $to;


                $data['main_content'] = 'management/aim/reports';
                $this->load->view('inc/template', $data);


            }
            public function income_category_reports(){
                if (!$this->authorization->isAuthorized('ADDITIONAL_INCOME.VIEW_REPORTS')) {
                    redirect('dashboard', 'refresh');
                }
                $data['cat_total']=$this->aim->income_category_reports();
                $data['daterange'] = 3;
                $data['main_content'] = 'management/aim/income_category_reports';
                $this->load->view('inc/template', $data);
            }

            public function income_category_reports1(){
                if (!$this->authorization->isAuthorized('ADDITIONAL_INCOME.VIEW_REPORTS')) {
                    redirect('dashboard', 'refresh');
                }
                if(!isset($_POST['daterange']))
                    redirect('management/Additional_income/income_category_reports');
                $from = (isset($_POST['from_date'])? $_POST['from_date']: "0");  
                $to = (isset($_POST['to_date'])? $_POST['to_date']: "0"); 
                
                $data['cat_total']=$this->aim->income_category_reports1($_POST['daterange'], $from, $to);
                $data['daterange'] = $_POST['daterange'];
                $data['from_date'] = $from;
                $data['to_date'] = $to;


                $data['main_content'] = 'management/aim/income_category_reports';
                $this->load->view('inc/template', $data);

            }



    }

