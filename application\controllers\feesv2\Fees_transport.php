<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  02 April 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_Master
 */
class Fees_transport extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    // if (!$this->authorization->isSuperAdmin()) {
    //   redirect('dashboard', 'refresh');
    // }
    $this->load->model('feesv2/fees_transport_model');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/fees_cohorts_model');
  }

  public function transport($stdId){
    $data['student_uid'] = $stdId;
    $data['stopList'] = $this->fees_cohorts_model->get_fee_Stop_list();
    $data['stdData'] = $this->Student_Model->getStdDataById($stdId);

    // $data['edit_assignStop'] = $this->fees_transport_model->assinged_stop_std($stdId);
    
    $areaRoutes = [];
    foreach ($data['stopList'] as $key => $area) {
        if(!in_array($area->route, $areaRoutes, true)){
           array_push($areaRoutes, $area->route);
        }
    }
    $data['route_area'] = $areaRoutes;

    $data['edit_transport'] = $this->fees_transport_model->get_assigned_transport($stdId);
    $data['kilometer'] = $this->fees_cohorts_model->get_fee_km_list();
    // echo "<pre>"; print_r($data['edit_transport']); die();
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
    $data['pickup_end_points'] = $this->settings->getSetting('transport_picup_and_end_point_enabled');
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'feesv2/transportation/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'feesv2/transportation/index_mobile';
    }else{
      $data['main_content'] = 'feesv2/transportation/index';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function insert_student_assign($stdId){
    $result = $this->fees_transport_model->assignStudentStop($stdId);
    if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Inserted.');
    }else{
        $this->session->set_flashdata('flashError', 'Failed to update .');
    }
    redirect('student/Student_controller/addMoreStudentInfo/'.$stdId);
  }

  public function stops(){
    $data['allstops'] = $this->fees_transport_model->get_stops_all();
    $routes = [];
    foreach ($data['allstops'] as $key => $val) {
      if(!in_array($val->route, $routes, true)){
        array_push($routes, $val->route);
      }
    }
    $data['routes'] = $routes;;
    $data['kms'] = $this->fees_transport_model->get_kms_all();
    $data['main_content'] = 'feesv2/transportation/stop/index';
    $this->load->view('inc/template', $data);
  }

  public function update_routesby_id(){
    $input = $_POST;
    $value =  $_POST['value'];
    $stop_route_id =  $_POST['id'];
    $this->fees_transport_model->update_routes_data_by_id($stop_route_id, $value);
    echo $value;

  }

  public function add_stop(){
    $result = $this->fees_transport_model->add_stop_name();
    if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Inserted.');
    }else{
        $this->session->set_flashdata('flashError', 'Failed to update .');
    }
    redirect('feesv2/fees_transport/stops');
  }

  public function delete_stop($id){
    $result = $this->fees_transport_model->delete_stop_name($id);
    if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Deleted.');
    }else{
        $this->session->set_flashdata('flashError', 'Failed to update .');
    }
    redirect('feesv2/fees_transport/stops');
  }

  public function get_stop_wise_stage(){
    $stops = $_POST['stops'];
    $result = $this->fees_transport_model->get_stop_wise_stage($stops);
    echo json_encode($result);
  }

  public function get_route_wise_stop(){
    $routearea = $_POST['routearea'];
    $result = $this->fees_transport_model->get_route_area_wise_stage($routearea);
    echo json_encode($result);
  }
}