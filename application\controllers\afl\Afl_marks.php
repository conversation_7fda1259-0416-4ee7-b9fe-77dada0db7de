<?php
class Afl_marks extends CI_Controller {
    private $yearId;
    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
       redirect('auth/login', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('afl/Afl_marks_model','marks_model');
      $this->load->model('afl/Afl_assessment_model','assessment_model');
      $this->load->model('afl/Afl_rubrics_model','rubric_model');
      $this->load->model('afl/Afl_grading_model','grading_model');
      $this->load->model('Class_section', 'class_section');
    }

    public function index($class_id=0){
      $data['class_id'] = $class_id;
      $data['classess'] = $this->class_section->getAllClassess();
      $data['main_content'] = 'afl/marks/index';
      $this->load->view('inc/template', $data);
    }

    public function marksEntry($assessment_id) {
      $data['assessment'] = $this->assessment_model->getAssessmentDetail($assessment_id);
      $data['sections'] = $this->class_section->getSectionsByClassId($data['assessment']->class_id);
      $data['subjects'] = $this->assessment_model->getSubjects($assessment_id);
      $data['topics'] = array();
      foreach ($data['subjects'] as $key => $subject) {
        $data['topics'][$subject->subject_id] = '';
        $i = 1;
        foreach ($subject->topics as $key => $topic) {
          $data['topics'][$subject->subject_id] .= $i++.'. '.$topic->topic.'<br>';
        }
      }
      // echo '<pre>'; print_r($data); die();
      $data['main_content'] = 'afl/marks/marks_entry';
      $this->load->view('inc/template', $data);
    }

    public function marks_entry() {
      $data = $this->input->post();
      /*$data['templates'] = [
        ['name' => 'Remarks', 'value' => "<div class='col-md-12 text-center'><h2>Student Development Plan</h2></div><table class='table table-bordered std-table' style='border: 1px solid #000;'><tr><th style='width:40%;'><b>Month-Year; Congnitio E-Learning Day:</b></th><td></td></tr><tr><th><b>Student Attendance / No of Classes Attended</b></th><td></td></tr><tr><th><b>Subject - Sessions - Total No of Assignments given</b></th><td></td></tr><tr><th><b>No of Assignments submitted</b></th><td></td></tr><tr><th><b>No of Assignments Due to be submitted</b></th><td></td></tr><tr><th><b>Complete Assignment support given in PPT Session no</b></th><td></td></tr><tr><th><b>Incomplete Assignments Submission due on</b></th><td></td></tr><tr><th><b>Subject Skillsets that need improvement</b></th><td></td></tr><tr><th><b>Congnitio individual student improvement support</b></th><td></td></tr><tr><th><b>Teacher's Name</b></th><td></td></tr><tr><th><b>Teacher's Email ID</b></th><td></td></tr><tr><th><b>Date Of Reporting</b></th><td></td></tr></table>"]
      ];*/
      $data['templates'] = [['name' => 'Remarks', "value" => ""]];
      if($this->settings->getSetting('school_short_name') === 'pncc') {
          $data['templates'] = [
            ['name' => 'Remarks', 'value' => "<div class='col-md-12 text-center'><h2>Cognitio Student Development Plan</h2></div><table class='table table-bordered std-table'><tbody><tr><th style='width:40%;'><b>Subject</b></th><td></td></tr>
    <tr><th><b>Student Attendance / No of Classes Conducted till D92 - SYNCHRONOUS METHOD OF TEACHING & LEARNING</b></th><td></td>
    </tr><tr><th><b>Subject - Sessions - Total No of Assignments given - ASYNCHRONOUS METHOD OF TEACHING & LEARNING</b></th><td></td></tr>
    <tr><th><b>No of Assignments submitted as on D92 - 31.8.2020 / Monday</b></th><td></td></tr>
    <tr><th><b>No of Assignments Due to be submitted</b></th><td></td></tr><tr><th><b>Complete Assignment support given in PPT Session no</b></th><td></td></tr><tr><th><b>Incomplete Assignments Submission due on</b></th><td></td></tr><tr><th><p><b>Student Participation Observations:</b></p><p><b>1. Non-Interactive / 2. Does not answer to participate in Conceptual Discussions when called by name / 3. Often complains of Network &amp; System Speaker Issues / 4. Active Participant - Good</b></p></th><td></td></tr><tr><th><p><b>Student Behaviour Observations Recommended for Correction / Good</b></p></th><td></td></tr><tr><th><b>Subject Skillsets that need improvement</b></th><td></td></tr><tr><th><b>Cognitio Student Development Support with Task Submission Date</b></th><td></td></tr><tr><th><p><b>Student Subject Domain as Recommended by the Teacher -&nbsp;</b></p><p><b>CORE - EXTENDED / STANDARD LEVEL - Applicable ONLY for Grades 9 &amp; 10</b></p></th><td></td></tr><tr><th><p><b>Student Forecast Grades as Recommended by the Teacher based on&nbsp;</b></p><p><b>Student's Participation - Applicable ONLY for Grades 9 &amp; 10</b></p></th><td></td></tr><tr><th><b>Teacher's Name</b></th><td></td></tr><tr><th><b>Teacher's Email ID</b></th><td></td></tr><tr><th><b>Date Of Reporting</b></th><td></td></tr><tr><th><b></b></th><td></td></tr></tbody></table>"]
          ];
      }
      $data['is_admin'] = $this->authorization->isAuthorized('AFL.ADMIN');
      $data['assessment'] = $this->assessment_model->getAssessmentDetail($data['assessment_id']);
      $data['subject'] = $this->marks_model->getSubjectDetails($data['subject_id'], $data['assessment_id']);
      // echo '<pre>'; print_r($data); die();
      $data['section'] = $this->class_section->getSectionDetail($data['section_id']);
      $data['main_content'] = 'afl/marks/add_marks';
      $this->load->view('inc/template', $data);
    }

    public function saveRemarks() {
      echo $this->marks_model->saveRemarks();
    }

    public function getStudentRemarks() {
      $assessment_subject_id = $_POST['assessment_subject_id'];
      $student_id = $_POST['student_id'];
      $data = $this->marks_model->getStudentRemarks($student_id, $assessment_subject_id);
      echo json_encode($data);
    }

    public function unlockMarksEntry() {
      $std_perf_ids = $_POST['std_perf_ids'];
      $show_self_evaluation = $_POST['show_self_evaluation'];
      echo $this->marks_model->unlockMarksEntry($std_perf_ids, $show_self_evaluation);
    }

    public function changePublishStatus() {
      $std_perf_ids = $_POST['std_perf_ids'];
      $status = $_POST['status'];
      echo $this->marks_model->changePublishStatus($std_perf_ids, $status);
    }

    public function addMarks($assessment_id, $assessment_subject_id) {
      $data['assessment'] = $this->assessment_model->getAssessmentDetail($assessment_id);
      $data['subjects'] = $this->assessment_model->getSubjects($assessment_id, $assessment_subject_id);
      $data['is_admin'] = $this->authorization->isAuthorized('AFL.ADMIN');
      $data['topics'] = array();
      $data['subject_id'] = 0;
      foreach ($data['subjects'] as $key => $subject) {
        $data['topics'][$subject->subject_id] = '';
        $i = 1;
        $data['subject_id'] = $subject->subject_id;
        foreach ($subject->topics as $key => $topic) {
          $data['topics'][$subject->subject_id] .= $i++.'. '.$topic->topic.'<br>';
        }
      }
      $data['sections'] = $this->assessment_model->getPermittedSections($data['subject_id'], $data['assessment']->class_id, $data['is_admin']);
      // echo '<pre>'; print_r($data); die();
      // $data['main_content'] = 'afl/marks/marks_entry';
      $data['main_content'] = 'afl/marks/marks_entry_new';
      $this->load->view('inc/template', $data);
    }

    public function getSectionStudents() {
      $section_id = $_POST['section_id'];
      $assessment_subject_id = $_POST['assessment_subject_id'];
      $students = $this->marks_model->getSectionStudents($section_id, $assessment_subject_id);
      echo json_encode($students);
    }

    public function getSubjectRubric() {
      $assessment_id = $_POST['assessment_id'];
      $student_id = $_POST['student_id'];
      $subject_id = $_POST['subject_id'];
      $assessment_subject = $this->marks_model->getPerfPointersAssessment($assessment_id, $subject_id);
      if(!empty($assessment_subject)) {
        $assessment = $this->assessment_model->getAssessmentDetail($assessment_id);
        $data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($assessment->afl_grading_scale_id);
        $data['parameters'] = $this->assessment_model->getSubjectPerfParameters($assessment_subject->id);
        $data['marks'] = $this->marks_model->getStudentMarks($assessment_subject->id, $student_id);
        // $data['marks'] = [];
        $data['assessment_subject_id'] = $assessment_subject->id;
        $data['perf_added'] = 1;
      } else {
        $data['perf_added'] = 0;
        $data['assessment_subject_id'] = 0;
      }
      // echo "<pre>"; print_r($data); die();
      echo json_encode($data);
    }

    public function getSubjectRubricOld() {
      $assessment_id = $_POST['assessment_id'];
      $student_id = $_POST['student_id'];
      $subject_id = $_POST['subject_id'];
      $rubric = $this->marks_model->getRubricByAssessment($assessment_id, $subject_id);
      // echo '<pre>'; print_r($rubric); die();
      if(!empty($rubric)) {
        $data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($rubric->afl_grading_scale_id);
        $data['parameters'] = $this->rubric_model->getRubricParameters($rubric->rubric_id);
        $data['marks'] = $this->marks_model->getStudentMarks($rubric->assessment_rubric_id, $student_id);
        $data['rubric'] = $rubric;
        $data['perf_added'] = 1;
      } else {
        $data['perf_added'] = 0;
      }
      echo json_encode($data);
    }

    public function saveMarks() {
      $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $status = $this->marks_model->saveMarks($input);
      echo $status;
    }

    public function marksReport() {
      $data = $this->input->post();
      // echo '<pre>'; print_r($data); die();
      $assessment_id = $data['assessment_id'];
      $subject_id = $data['subject_id'];
      $student_id = $data['student_id'];
      $data['assessment'] = $this->assessment_model->getAssessmentDetail($assessment_id);
      $data['student'] = $this->assessment_model->getStudentDetail($student_id);
      $data['subject'] = $this->marks_model->getSubjectDetails($subject_id, $assessment_id);
      $assessment_subject = $this->marks_model->getPerfPointersAssessment($assessment_id, $subject_id);
      $data['marks'] = array();
      $data['remarks'] = '';
      if(!empty($assessment_subject)) {
        $data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($data['assessment']->afl_grading_scale_id);
        $data['parameters'] = $this->assessment_model->getSubjectPerfParameters($assessment_subject->id);
        $data['marks'] = $this->marks_model->getStudentMarks($assessment_subject->id, $student_id);
        $data['assessment_subject_id'] = $assessment_subject->id;
        $data['perf_added'] = 1;
        $remarks = $this->marks_model->getStudentRemarks($student_id, $assessment_subject->id);
        if(!empty($remarks)) {
          $data['remarks'] = $remarks->remarks;
        }
      } else {
        $data['perf_added'] = 0;
        $data['assessment_subject_id'] = 0;
      }

      $school = $this->settings->getSetting('school_short_name');
      $data['main_content'] = 'afl/report_templates/'.$school;
      $this->load->view('inc/template', $data);
    }

    public function marksReportOld() {
      $input = $this->input->post();
      // echo '<pre>'; print_r($input); die();
      $assessment_id = $input['assessment_id'];
      $subject_id = $input['subject_id'];
      $student_id = $input['student_id'];
      $data['assessment'] = $this->assessment_model->getAssessmentDetail($assessment_id);
      $data['student'] = $this->assessment_model->getStudentDetail($student_id);
      $data['subject'] = $this->marks_model->getSubjectDetails($subject_id, $assessment_id);
      $rubric = $this->marks_model->getRubricByAssessment($assessment_id, $subject_id);
      $data['marks'] = array();
      if(!empty($rubric)) {
        $data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($rubric->afl_grading_scale_id);
        $data['parameters'] = $this->rubric_model->getRubricParameters($rubric->rubric_id);
        $data['marks'] = $this->marks_model->getStudentMarks($rubric->assessment_rubric_id, $student_id);
        $data['rubric'] = $rubric;
        $data['rubric_added'] = 1;
      } else {
        $data['rubric_added'] = 0;
      }
      // echo '<pre>'; print_r($data); die();
      $school = $this->settings->getSetting('school_short_name');
      $data['main_content'] = 'afl/report_templates/'.$school;
      $this->load->view('inc/template', $data);
    }

    public function getHistoryBySubject() {
      $assessment_subject_id = $_POST['assessment_subject_id'];
      $student_id = $_POST['student_id'];
      $history = $this->marks_model->getHistoryBySubject($assessment_subject_id, $student_id);
      echo json_encode($history);
    }

}