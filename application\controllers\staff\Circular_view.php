<?php
class Circular_view extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if ($this->authorization->isModuleEnabled('CIRCULARS_V2')) {
            $this->load->model('staff/Staff_Model');
        } else {
            redirect('dashboard', 'refresh');
        }
        $this->load->library('filemanager');
        $this->load->model('communication/circular_inbox_model');
    }

    public function index_old() {
        $data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
        $data['acadYears'] = $this->circular_inbox_model->get_acad_year_id_from_circular();
        $avatar_type = 4;
        $data['category'] = 'latest';
        if(isset($_POST['category_name'])) {
            $data['category'] = $_POST['category_name'];
        }
        $data['cid'] = 0;
        if(isset($_POST['cid'])) {
            $data['cid'] = $_POST['cid'];
        }
        
        if (isset($_POST['academic_year'])) {
            $data['academic_year'] = $_POST['academic_year'];
        }else{
            $data['academic_year'] = $data['acadYears'][0]->acad_year_id;
        }
        $this->load->model('communication/circular_model', 'circular');
        $data['categories'] = $this->circular->getCircularCategories();
        $data['circulars'] = $this->circular_inbox_model->getCircularsAndEmails($data['staff_id'], $avatar_type, $data['category'], $data['academic_year']);
        $data['main_content'] = 'staff/notification/circularsv2/inbox';
        $this->load->view('inc/template', $data);
    }

    public function index(){
        $data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
		$this->load->model('communication/circular_model', 'circular');
		$data['categories'] = $this->circular->getCircularCategories();
		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'staff/notification/circularsv2/tablet_inbox_new.php';
		}else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'staff/notification/circularsv2/mobile_inbox_new.php';
		}else{
			$data['main_content'] = 'staff/notification/circularsv2/desktop_inbox_new.php';  	
		}
		$this->load->view('inc/template', $data);
	}

	public function getCirculars() {
		$staff_id = $this->input->post('staff_id');
		$category = $this->input->post('category');
		$start_date = $this->input->post('from_date');
		$end_date = $this->input->post('to_date');
		$show_unread_only = $this->input->post('show_unread');
		$avatar_type = 4;
		$circulars = $this->circular_inbox_model->getCircularsAndEmailsByDateRange($staff_id, $avatar_type, $category, $start_date, $end_date, $show_unread_only);
		echo json_encode($circulars);
	}

    public function view() {
        $data['circular_id'] = $_POST['circular_id'];
        $data['is_read'] = $_POST['is_read'];
        $data['circular'] = 'new';
        $data['category'] = $_POST['category'];
        $data['staff_circular_id'] = isset($_POST['staff_circular_id'])?$_POST['staff_circular_id']:'';
        $data['circularData'] = $this->circular_inbox_model->getCircularDeatils($data['circular_id'], 0);
        // if($data['is_read'] == 0) {
        //     $this->circular_inbox_model->makeCircularAsRead($data['staff_circular_id']);
        // }
        // echo "<pre>"; print_r($data); die();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content']    = 'staff/notification/circularsv2/view_tablet'; 
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'staff/notification/circularsv2/view_mobile'; 
        }else{
          $data['main_content']    = 'staff/notification/circularsv2/view';    	
        }
        $this->load->view('inc/template', $data);
    }
    
    public function downloadCircular($id, $type)
	{
        if($type == 'new') {
            $table = 'circularv2_master';
        } else {
            $table = 'circular_master';
        }
        $link = $this->circular_inbox_model->downloadCircularAttachment($id, $table);
        $file = explode("/", $link);
		$file_name = $file[count($file)-1];
		$url = $this->filemanager->getFilePath($link);
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download($file_name, $data, TRUE);
	}

    public function view_old() {
        $data['circular_id'] = $_POST['circular_id'];
        $data['is_read'] = 1;
        $data['circular'] = 'old';
        $data['category'] = $_POST['category_name'];
        $data['circularData'] = $this->circular_inbox_model->getOldCircularDeatils($data['circular_id']);
        // echo "<pre>"; print_r($data); die();
        $data['main_content']    = 'staff/notification/circularsv2/view';
        $this->load->view('inc/template', $data);
    }

    public function makeRead() {
        $staff_circular_id = $_POST['staff_circular_id'];
        echo $this->circular_inbox_model->makeCircularAsRead($staff_circular_id);
    }

    public function circular(){ 

        if (!$this->settings->isParentModuleEnabled('CIRCULARS_V2')) {
            redirect('dashboard', 'refresh');
        }
        $data['staffId'] = $this->authorization->getAvatarStakeHolderId();

        $all =  $this->Staff_Model->getCountOfCirculars($data['staffId']);
        $latest =  $this->Staff_Model->latestCirculars($data['staffId']);

        $old_all =  $this->Staff_Model->getCountOfOldCirculars($data['staffId']);

        $categories = json_decode($this->settings->getSetting('circular_categories'));
        $data['categories'] = $categories;
        $data['all'] = array();
        $data['latest'] = array();
        foreach ($categories as $key => $category) {
            $data['all'][$category] = 0;
            $data['latest'][$category] = 0;
            if(array_key_exists($category, $all)) {
                $data['all'][$category] = $all[$category];
            }
            if(array_key_exists($category, $latest)) {
                $data['latest'][$category] = $latest[$category];
            }
            if(array_key_exists($category, $old_all)) {
                $data['all'][$category] += $old_all[$category];
            }
        }
        $data['main_content']    = 'staff/notification/circulars_new/index';
        $this->load->view('inc/template', $data);
    }

    public function circulars(){
        $data['category'] = $_POST['category'];
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $data['staff_circular'] =  $this->Staff_Model->getCircularsAndEmails($data['category'], $staffId);
        $data['staff_old_circular'] =  $this->Staff_Model->getOldCirculars($data['category'], $staffId);
        // echo '<pre>'; print_r($data); die();
        $data['main_content']    = 'staff/notification/circulars_new/category_wise_titles';
        $this->load->view('inc/template', $data);
    }

    public function view_circular(){
        $data['category'] = $_POST['category'];
        $data['circular_id'] = $_POST['circular_id'];
        $data['circularData'] = $this->Staff_Model->getCircularDeatils($data['circular_id']);
        $data['main_content']    = 'staff/notification/circulars_new/circular';
        $this->load->view('inc/template', $data);
    }

    public function view_old_circular(){
        $data['category'] = $_POST['category'];
        $data['circular_id'] = $_POST['circular_id'];
        $data['circularData'] = $this->Staff_Model->getOldCircularDeatils($data['circular_id']);
        $data['main_content']    = 'staff/notification/circulars_new/circular';
        $this->load->view('inc/template', $data);
    }
    public function get_circular_details_old(){
        $circular_id = $_POST['circularId'];
        $is_read = $_POST['is_read'];
        $staff_circular_id = $_POST['staff_circular_id'];
        $stakeholder_id = $this->authorization->getAvatarStakeHolderId();
        if($is_read == 0) {
            $this->circular_inbox_model->makeCircularAsRead($staff_circular_id);
        }
        $avatar_type = 4;
        $result = $this->Staff_Model->getCircularDeatils($circular_id);
        echo json_encode($result);
    }
    public function downloadMobileCircularAttachment($cid, $index, $type) {
        if($type == 'new') {
            $table = 'circularv2_master';
        } else {
            $table = 'circular_master';
        }
        $file_link = $this->circular_inbox_model->downloadCircularAttachment($cid, $table, $type);
        if($file_link->is_file_path_json) {
            $paths = json_decode($file_link->file_path);
            $link = $paths[$index]->path;
            $fname = $paths[$index]->name;
        } else {
            $paths = explode(",", $file_link->file_path);
            $link = $paths[$index];
            $file = explode("/", $link);
            $file_name = 'circular'.($index+1);
            $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
        }
        // echo '<pre>'; print_r($fname); die();
        $url = $this->filemanager->getFilePath($link);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download($fname, $data, TRUE);
        $this->load->library('user_agent');
        redirect($this->agent->referrer());
    }

    public function get_circular_details(){
		$circular_id = $_POST['circularId'];
		$staff_circular_id = $_POST['staff_circular_id'];
		$result = $this->circular_inbox_model->getCircularDeatils($circular_id, $staff_circular_id);
		echo json_encode($result);
	}
}