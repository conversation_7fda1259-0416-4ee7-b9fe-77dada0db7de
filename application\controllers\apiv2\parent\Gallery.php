<?php

require APPPATH . 'libraries/REST_Controller.php';

class Gallery extends REST_Controller
{
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
            $data = ['error' => 'User not logged in'];
            $this->response($data, REST_Controller::HTTP_UNAUTHORIZED);
			$this->output->_display();
			exit;
		}
		$this->load->model('parentv2/Gallery_model');
		
	}

	public function galleries_get()
	{
		$class_name = $this->get('class_name');
		$section_name = $this->get('section_name');
		$this->load->library('filemanager');
		
		$data['base_path'] = $this->filemanager->getFilePath('');
		$data['gallery_info'] = $this->Gallery_model->load_galleries($class_name,$section_name);
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}


	public function view_gallery_get()
	{
		$gallery_id = $this->get('gallery_id');
		$this->load->library('filemanager');
		
		$data['base_path'] = $this->filemanager->getFilePath('');

		$data['image_info'] = $this->Gallery_model->get_images_info($gallery_id);
		// echo '<pre>';print_r($data['image_info']);

        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

}
?>