<?php

class Sms extends CI_Controller {
               
    private $smsStatusCodes;
    private $yearId;
	function __construct()
	{
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
			}
			if(!$this->authorization->isAuthorized('COMMUNICATION.MODULE')) {
				redirect('dashboard', 'refresh');
			}
			$this->yearId = $this->acad_year->getAcadYearId();
			$this->load->library('sms_sender');
      $this->load->model('sms_model');
      $this->load->model('payment_model');
      $this->load->model('staff/Staff_Model');
      $this->load->library('filemanager');
      $this->load->library('payment');
      $this->load->helper('sms_helper');
      $this->smsStatusCodes = array(
			      'AWAITED-DLR' => 'Awaited delivery',
			      'DELIVRD' => 'Delivered',
			      'DNDNUMB' => 'DND number',
			      'OPTOUT-REJ' => 'Opt out from subscription',
			      'INV-NUMBER' => 'Invalid Number',
			      'INVALID-NUM' => 'Invalid Number',
			      'SENDER-ID-NOT-FOUND' => 'SENDER-ID-NOT-FOUND',
			      'INV-TEMPLATE-MATCH' => 'Invalid template',
			      'MAX-LENGTH' => 'Message length exceeded 100 charactes',
			      'NO-CREDITS' => 'No credits',
			      'SERIES-BLOCK' => 'Mobile number series blocked',
			      'SERIES-BLK' => 'Series blocked by operator',
			      'SERVER-ERR' => 'Server error',
			      'SPAM' => 'Spam SMS',
			      'BLACKLIST' => 'Blacklisted number',
			      'BLACKLST' => 'Blacklisted number',
			      'TEMPLATE-NOT-FOUND' => 'Template not found',
			      'NOT-OPTIN' => 'Not subscribed for opt-in group',
			      'TIME-OUT-PROM' => 'Time out for promotional SMS',
			      'INVALID-SUB' => 'Number does not exist',
			      'ABSENT-SUB' => 'Mobile Subscriber not reachable',
			      'HANDSET-ERR' => 'Problem with Handset',
			      'BARRED' => 'Message barred by user',
			      'NET-ERR' => 'Subscriber’s operator not supported',
			      'MEMEXEC' => 'Handset memory full',
			      'FAILED' => 'Failed to send',
			      'MOB-OFF' => 'Mobile handset in switched off mode',
			      'HANDSET-BUSY' => 'Subscriber is in busy condition',
			      'EXPIRED' => 'SMS expired after multiple re-try',
			      'REJECTED' => 'SMS Rejected as the number is blacklisted by operator',
			      'REJECTD' => 'SMS Rejected as the number is blacklisted by operator',
			      'OUTPUT-REJ' => 'Unsubscribed from the group',
			      'REJECTED-MULTIPART' => 'Validation fail',
			      'UNDELIV' => 'Failed due to network errors',
			      'NO-DLR-OPTR' => 'Status not acknowledged',
			      '0' => 'Status not acknowledged',
			      '' => 'Status not acknowledged'
			);
	}

	private function _checkSmsStatus($msgIdArray) {
		if (ENVIRONMENT !== 'production') {
		  //Do not allow check sms status
		  return 1;
		}
	    $msgIds = implode(",", $msgIdArray);
	    // $customIds = implode(",", $customIdArray);
	    $smsint =$this->settings->getSetting('smsintergration');
	    $apiUrl = $smsint->url;
	    $apiKey = $smsint->api_key;

	    $curl = curl_init();

        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
	    $return_url = site_url().'Callback_Controller/updateSMSStatus';

	    curl_setopt_array($curl, array(
	        CURLOPT_URL => CONFIG_ENV['job_server_smsstatus_uri'],
	        CURLOPT_RETURNTRANSFER => true,
	        CURLOPT_ENCODING => "",
	        CURLOPT_MAXREDIRS => 10,
	        CURLOPT_TIMEOUT => 30,
	        CURLOPT_USERPWD => $username . ":" . $password,
	        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
	        CURLOPT_CUSTOMREQUEST => "POST",
	        CURLOPT_POSTFIELDS => "msg_ids=".$msgIds."&api_url=".$apiUrl."&api_key=".$apiKey."&return_url=".$return_url,
	        CURLOPT_HTTPHEADER => array(
	            "Accept: application/json",
	            "Cache-Control: no-cache",
	            "Content-Type: application/x-www-form-urlencoded",
	            "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
	        ),
	    ));

	    $response = curl_exec($curl);
	    $err = curl_error($curl);

	    curl_close($curl);
	}

	public function get_section(){
		$class_id =$this->input->post('className');
		$acad_year =$this->input->post('acad_year');
		$batch =$this->input->post('batch');
		$section = $this->sms_model->get_classwiseSection($class_id, $acad_year);
		$stdName = $this->sms_model->get_studentclassSectionwise($class_id, 0, $acad_year, $batch);
		echo json_encode(array('section'=>$section,'stdname'=>$stdName));
	}
	public function get_student(){
		$class_id =$this->input->post('className');
		$section_id =$this->input->post('section');
		$acad_year =$this->input->post('acad_year');
		$batch =$this->input->post('batch');
		$result = $this->sms_model->get_studentclassSectionwise($class_id,$section_id, $acad_year, $batch);
		echo json_encode($result);
	}
	
	// sms for Class wise and All
	public function sms_submit(){
		if (!$this->authorization->isAuthorized('SMS.SEND_SMS') && !$this->authorization->isModuleEnabled('SMS')) {
			redirect('dashboard');
		}
		$result = $this->sms_model->get_studentparentphonenumber();
		$mobil_aray=array('**********','**********');
		$sms_sontent='Test Messsage';
		$send_sms =$this->sms_sender->send_sms($mobil_aray,$sms_sontent);
     	redirect('sms');
	}

	// sms for fee challan generation and bank remited amount

	public function dailyGenerationReport(){

		$date = $this->input->post('date');
		$cAmount = $this->input->post('cAmount');
		$bAmount = $this->input->post('bAmount');
		$mobil_aray=array('**********');
	 	$sms_sontent =  urlencode('Dear Madam collection report as on '.$date.' Total Challan Generation Amount '.$cAmount.' Total Bank Remitted '.$bAmount.'');
		$sms =$this->sms_sender->send_sms($mobil_aray,$sms_sontent);
     	redirect('sms');
	}

	// SMS for Others
	public function sms(){
		if (!$this->authorization->isAuthorized('SMS.SEND_SMS') || !$this->authorization->isModuleEnabled('SMS')) {
			redirect('dashboard', 'refresh');
		}
		$data['credits'] = $this->sms_model->getRemainingSMSCredits();
		$data['classes'] = $this->sms_model->get_class();
		$data['batches'] = $this->sms_model->get_batches();
		// echo "<pre>"; print_r($data); die();
		$data['class_section'] = $this->sms_model->get_AllClassandSection($this->yearId);
		$data['staff_details'] = $this->sms_model->get_Approvedstaff_All();
		$data['sms_templates'] = $this->sms_model->get_Templates(1);//get templates applicable to students
		// $data['acad_years'] = $this->acad_year->getAllYearData();

	    $data['acad_years'] = array(
	    	[
	    		'name'=> $this->acad_year->getAcadYear(),
	    		'value'=> $this->yearId
	    	],
	    	[
	    		'name'=> $this->acad_year->getPromotionAcadYear(),
	    		'value'=> $this->acad_year->getPromotionAcadYearId()
	    	]
	    );
		$data['currentYear'] = $this->yearId;
		if ($this->mobile_detect->isMobile()) { 
            $data['main_content']    = 'sms_nh/mobile_index';
        } else { 
            $data['main_content']    = 'sms_nh/index';
        }
        $this->load->view('inc/template', $data);
   	}

   	public function getClasses() {
   		$acad_year = $_POST['acad_year'];
   		$classes = $this->sms_model->get_class($acad_year);
   		echo json_encode($classes);
   	}

   	public function getClassSections() {
   		$acad_year = $_POST['acad_year'];
   		$classes = $this->sms_model->get_AllClassandSection($acad_year);
   		echo json_encode($classes);
   	}

   	public function getSections() {
   		$classId = $_POST['classId'];
   		$acad_year = $this->yearId;
   		if(isset($_POST['acad_year']))
   			$acad_year = $_POST['acad_year'];
   		$sections = $this->sms_model->get_classwiseSection($classId, $acad_year);
   		echo json_encode($sections);
   	}

    public function sms_add(){
      $admin_no = $this->input->post('admin_no');
      $curr_date = $this->input->post('curr_date');
      $resId = $this->input->post('resId');
   
	}

	public function getSectionStudents() {
		$sectionId = $_POST['sectionId'];
		$batch = $_POST['batch'];
		$students = $this->sms_model->getSectionStudents($sectionId, $batch);
		echo json_encode($students);
	}
	
	public function getPreviewData(){
		$msg = $_POST['msg'];
		$type = $_POST['type'];
		$batch = $_POST['batch'];
		$acad_year = $this->yearId;
		if(isset($_POST['acad_year']))
   			$acad_year = $_POST['acad_year'];
		$membersData = $this->sms_model->getPreview($acad_year, $batch);
		$html_str = '<thead><tr><th>#</th><th>Name</th><th>Number</th></tr></thead><tbody>';
		$i = 1;
		if(!empty($membersData)) {
			foreach ($membersData as $key => $member){
				$html_str .= '<tr>';
				$html_str .= '<td>'.$i.'</td>';
				$html_str .= '<td>'.$member->Name.'</td>';
				// if($member->id == -1)
				// 	$html_str .= '<td>'.$member->Name.'</td>';
				// else if($type == "Student" || $type == "Class")
				// 	$html_str .= '<td>'.$member->Name.'(Parent of '. $member->stdName .')</td>';
				// else 
				// 	$html_str .= '<td>'.$member->Name.'</td>';
				$mobile_no = $member->mobile_no;
				if(empty($mobile_no)) $mobile_no = 'No Number';
				$html_str .= '<td>'.$mobile_no.'</td>';
				// $html_str .= '<td>'.$msg.'</td>';
				$html_str .= '</tr>';
				$i++;
			}
			$html_str .= "</tbody>";
			echo json_encode($html_str);
		} else {
			echo json_encode('empty');
		}
	}

	public function sendTestSMS(){
		if (!$this->authorization->isAuthorized('SMS.SEND_SMS') || !$this->authorization->isModuleEnabled('SMS')) {
			redirect('dashboard','refresh');
		}
		$custom_numbers = [$_POST['to']];
		$smsId = 0; //default
		$status = sendToCustomNumbers($custom_numbers,'SMS_UI', $_POST['msg'], $smsId, $_POST['isUnicode']);
		echo $status;
	}

    public function sms_insert(){
			if (!$this->authorization->isAuthorized('SMS.SEND_SMS') || !$this->authorization->isModuleEnabled('SMS')) {
				redirect('dashboard','refresh');
			}
		$sms_content = $this->input->post('sms_content');
		$send_to = $this->input->post('send_to');
		$custom_str = $this->input->post('custom_numbers');
		$isUnicode = $this->input->post('unicode');
		$acad_year_id = $this->yearId;
		$input = $this->input->post();
		if(isset($input['acad_year'])) {
			$acad_year_id = $input['acad_year'];
		}
		
		if(empty($isUnicode))
			$isUnicode = 0;
		$sent_by = $this->authorization->getAvatarId();
		$smsData = $this->sms_model->getStakeholderIds($this->yearId);
		$insId = 0;
		// echo "<pre>"; print_r($smsData->stakeholder_ids);die();
		if(!empty($smsData->stakeholder_ids))
			$insId = sendCommonSMS($smsData->stakeholder_ids, $smsData->user_type, 'SMS_UI', $sms_content, $sent_by, $smsData->sent_to, $send_to, $isUnicode, $acad_year_id);
		if($custom_str != '' && $insId != -1) {
      		$custom_numbers = array_map('trim', explode(',', $custom_str));
      		if(!empty($custom_numbers))
      			$insId = sendToCustomNumbers($custom_numbers,'SMS_UI', $sms_content, $insId, $isUnicode, $acad_year_id); //update custom numbers with same smsId and send message
		}

		if($insId == -1) {
			$this->session->set_flashdata('flashError', 'Not enough credits available');
		}
		else if($insId) {
			$this->session->set_flashdata('flashSuccess', 'Successfully SMS Sent');
		} else {
			$this->session->set_flashdata('flashError', 'Something Went Wrong..');
		}
		redirect('sms/sms');
    }


    // Competition sms
	public function getPreviewDataforCompetition(){
		$msg = $_POST['msg'];
		$staff = $_POST['staff'];
		$student = $_POST['student'];
		$preview = $_POST['preview'];
		if ($preview == 1) {
			$membersData = $this->sms_model->getPreviewforCompetition($staff,$student,$preview);
		}
		
		$html_str = '<thead><tr><th>#</th><th style="width:20%;">Name</th><th>Number</th><th>Message</th></tr></thead><tbody>';
		$i = 1;
		if(!empty($membersData)) {
			foreach ($membersData as $type => $member){
				$html_str .= '<tr>';
				$html_str .= '<td>'.$i.'</td>';
				$html_str .= '<td>'.$member->Name.'</td>';
				$html_str .= '<td>'.$member->mobile_no.'</td>';
				$html_str .= '<td>'.$msg.'</td>';
				$html_str .= '</tr>';
				$i++;
			}
		}
		$html_str .= "</tbody>";
		echo json_encode($html_str);
	}

	public function sendCompetitionSms(){
		$sms_content = $this->input->post('msg');
		$staff = $_POST['staff'];
		$student = $_POST['student'];
		$preview = $_POST['preview'];
		$sent_by = $this->authorization->getAvatarId();
		
		$insId = sendCommonSMS($staff, 'Staff', 'Competition', $sms_content, $sent_by, 'Staff Individual', "");
		if($insId != -1) {
			$insId = sendCommonSMS($student, 'Student', 'Competition', $sms_content, $sent_by, 'Student Individual', "");
		}
		
		if($insId == -1) {
			$this->session->set_flashdata('flashError', 'Not enough credits available.');
		}
		else if($insId) {
			$this->session->set_flashdata('flashSuccess', 'Successfully SMS Sent');
		} else {
			$this->session->set_flashdata('flashError', 'Something Went Wrong..');
		}
		redirect('competition');
	}

 	
 	public function s3FileUpload($file) {
	    if($file['tmp_name'] == '' || $file['name'] == '') {
	      return ['status' => 'empty', 'file_name' => ''];
	    }        
	    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'profile');
	}

	public function getPreviewDataforCompetitionEmail(){
	 	$files =$_FILES['emailAttFile'];	 	
		$staff = explode(',',$_POST['staff']);
		$student = explode(',',$_POST['student']);

		$preview = '1';
		if ($preview == 1) {
			$membersData = $this->sms_model->getPreviewforCompetitionEmail($this->s3FileUpload($_FILES['emailAttFile']),$staff,$student,$preview);
		}
		$html_str = '<thead><tr><th>#</th><th style="width:20%;">Name</th><th>Number</th></tr></thead><tbody>';
		$i = 1;
		if(!empty($membersData)) {
			foreach ($membersData as $type => $member){
				$html_str .= '<tr>';
				$html_str .= '<td>'.$i.'</td>';
				$html_str .= '<td>'.$member->Name.'</td>';
				$html_str .= '<td>'.$member->email.'</td>';
				$html_str .= '</tr>';
				$i++;
			}
		}
		$html_str .= "</tbody>";
		echo json_encode($html_str);
	}

	public function addNewTemplate(){
		$data['main_content'] = 'sms_nh/addSMSTemplate';
        $this->load->view('inc/template', $data);
	}

	public function addTemplate(){
		$status = (int) $this->sms_model->addTemplate();
		if($status)
			$this->session->set_flashdata('flashSuccess', 'Successfully Added Template');
		else 
			$this->session->set_flashdata('flashError', 'Something Went Wrong!');
		redirect('sms/sms');
	}

	public function getTemplates(){
		$applicable = $_POST['applicable_to'];
		$sms_templates = $this->sms_model->get_Templates($applicable);//get templates applicable to particular
		echo json_encode($sms_templates);
	}
	
	public function sms_report(){
		if (!$this->authorization->isAuthorized('SMS.SMS_REPORT') || !$this->authorization->isModuleEnabled('SMS')) {
			redirect('dashboard','refresh');
		}
		$msgIds = $this->sms_model->getUnDeliveredMessages();
		if(!empty($msgIds)) {
			$this->_checkSmsStatus($msgIds);
		}
        $data['credits'] = $this->sms_model->getRemainingSMSCredits();
        $data['main_content'] = 'sms_nh/sms_report';
        $this->load->view('inc/template', $data);
	}

	public function smsAggregateReport(){
		if (!$this->authorization->isAuthorized('SMS.AGGREGATE_REPORT') && !$this->authorization->isModuleEnabled('SMS')) {
			redirect('dashboard','refresh');
		}
		$data['aggregateReport'] = $this->sms_model->getAggregateReport($this->yearId);
		$data['statusCodes'] = $this->smsStatusCodes;
		$data['credits'] = $this->sms_model->getRemainingSMSCredits();
		$msgIds = $this->sms_model->getUnDeliveredMessages();
		if(!empty($msgIds)) {
			$this->_checkSmsStatus($msgIds);
		}
		$data['main_content'] = 'sms_nh/sms_agg_report';
        $this->load->view('inc/template', $data);
	}

	public function studentSmsReport($stdId){
		if (!$this->authorization->isAuthorized('STUDENT.VIEW_SMS_REPORT')) {
		redirect('dashboard', 'refresh');
		}
		$data['stdId'] = $stdId;
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'student/student_registration/more/sms_report_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'student/student_registration/more/sms_report_mobile';
        }else{
          $data['main_content'] = 'student/student_registration/more/sms_report';     	
        }
        $this->load->view('inc/template', $data);
	}

	public function getStdSMSReport(){
		if(!empty($_POST['from']) && !empty($_POST['to'])) {
            $from = date('Y-m-d', strtotime($_POST['from']));
            $to = date('Y-m-d', strtotime($_POST['to']));
            $stdId = $_POST['stdId'];
            $result = $this->sms_model->getStdSMSReport($from, $to, $stdId);
            $data = "";
            if(empty($result)) $data = '<h4 class="no-data-display">No SMS sent on these dates.</h4>';
            else {
                $data .= '<table class="table table-bordered datatable dataTable" id="myTable"><thead><tr><th style="width:2%;"># </th><th style="width:10%;">Sent Date</th><th style="width:60%;">Message</th><th style="width:4%;">Relation</th><th style="width:16%;">Status</th><th style="width:8%;">Source</th></tr></thead><tbody>';
                $i=1;
                foreach ($result as $key => $val) {
					$data .= '<tr><td>'. $i++ .'</td>';
					$data .= '<td>'. date("d-m-Y", strtotime($val->sms_date)) .'</td>';
					$data .= '<td>'. $val->sms_content .'</td>';
					$data .= '<td>'. $val->relation_type .'</td>';
					$data .= '<td>'.$this->smsStatusCodes[$val->status].'</td>';
					$data .= '<td>'. ucwords(str_replace("_", " ", $val->source)) .'</td>';
					$data .= '</tr>';

                }
                $data .= '</tbody></table>';
            }
            echo json_encode($data);
        }
	}

	function staffSMSReport($staffId){
		if (!$this->authorization->isAuthorized('STAFF.VIEW_SMS_REPORT')) {
			redirect('dashboard', 'refresh');
		}
		$data['staffId'] = $staffId;
		$data['staff_name'] = $this->Staff_Model->getStaffName($staffId);
		$data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
		$data['main_content'] = 'staff/staff_registration/more/sms_report';
        $this->load->view('inc/template', $data);
	}

	function getSMSReportForStaff(){
		if(!empty($_POST['from']) && !empty($_POST['to'])) {
            $from = date('Y-m-d', strtotime($_POST['from']));
            $to = date('Y-m-d', strtotime($_POST['to']));
            $staffId = $_POST['staffId'];
            $result = $this->sms_model->getStaffSMSReport($from, $to, $staffId);
            // echo "<pre>"; print_r($result); die();
            $data = "";
            if(empty($result)) $data = '<h4>No SMS sent on these dates.</h4>';
            else {
                $data .= '<table class="table table-bordered datatable dataTable" id="myTable"><thead><tr><th style="width:2%;"># </th><th style="width:10%;">Sent Date</th><th style="width:60%;">Message</th><th style="width:20%;">Status</th><th style="width:8%;">Source</th></tr></thead><tbody>';
                $i=1;
                foreach ($result as $key => $val) {
					$data .= '<tr><td>'. $i++ .'</td>';
					$data .= '<td>'. date("d-m-Y", strtotime($val->sms_date)) .'</td>';
					$data .= '<td>'. $val->sms_content .'</td>';
					$data .= '<td>'.$this->smsStatusCodes[$val->status].'</td>';
					$data .= '<td>'. ucwords(str_replace("_", " ", $val->source)) .'</td>';
					$data .= '</tr>';

                }
                $data .= '</tbody></table>';
            }
            echo json_encode($data);
        }
	}

	public function refreshStatus(){
		$msgIds = $this->sms_model->getUnDeliveredMessages();
		if(!empty($msgIds)) {
			$this->_checkSmsStatus($msgIds);
		}
		echo 1;
	}

	public function getSMSReport(){
        if(!empty($_POST['from']) && !empty($_POST['to'])) {
            $from = date('Y-m-d', strtotime($_POST['from']));
            $to = date('Y-m-d', strtotime($_POST['to']));
            
            /*$start_date = strtotime($from);
            $end_date = strtotime($to);
            $datediff = $end_date - $start_date;
            $Days = ($datediff / (60 * 60 * 24)) + 1;
            $dates = array();
            for($i = 0; $i < $Days; $i++){
                $date = date("Y-m-d", strtotime($from." +".$i." days"));
                array_push($dates, $date);
            }*/
            $result = $this->sms_model->getSMSReport($from, $to);
            $data = $this->_buildSMSReport($result);
            echo json_encode($data);
        }
    }

    public function getFullReport(){
    	$smsId = $_POST['smsId'];
    	$userType = $_POST['userType'];
    	$fullData = $this->sms_model->getFullSMSReport($smsId,$userType);
    	$data = "";
		if(!empty($fullData)) {
			$data .= '<thead><tr><th># </th><th>Name</th><th>Mobile Number</th><th>Status</th></tr></thead><tbody>';
            $i=1;
            foreach ($fullData as $key => $val) {
            	$name = $val->Name;
            	if(isset($val->relation_type)) $name = $val->pName. "(".$val->relation_type." of " . $val->Name . ")";
 				/*$status = "Error";
                if($val->status == 1) $status = "Delivered";
                else if($val->status == 2) $status = "Delivered";
                else if($val->status == 3) $status = "Delivered";
                else if($val->status == 4) $status = "Failed";
                else if($val->status == 5) $status = "Empty Number";
		     	else if($val->status == 5) $status = "Retry Failed";*/

		     	$mobile_no = $val->mobile_no;
		     	if(empty($mobile_no)) $mobile_no="No Number";

		     	$status = (array_key_exists($val->status, $this->smsStatusCodes))?$this->smsStatusCodes[$val->status]:'Unknown';

		     	$data .= '<tr><td>'. $i++ .'</td>';
		     	$data .= '<td>'. $name .'</td>';
		     	$data .= '<td>'. $mobile_no .'</td>';
		     	$data .= '<td>'. $status .'</td></tr>';
            }
            $data .= '</tbody>';
        }
        echo json_encode($data);
    }

    public function detailedStatusReport(){
    	$userType = $_POST['type'];
    	$status = $_POST['status'];
    	$detailedReport = $this->sms_model->getStatusWiseReport($userType, $status);
    	$table = $this->_buildStatusData($userType, $detailedReport);
    	echo json_encode($table);
    }

    private function _buildStatusData($userType, $data) {
    	if(empty($data)) {
    		return '<tr><td>No data</td></tr>';
    	}

    	$table = '<thead><tr><th>#</th>';
    	if($userType == 1) {
    		$table .= '<th>Student</th><th>Grade</th><th>Parent</th>';
    	}
    	if($userType == 2) {
    		$table .= '<th>Staff</th>';
    	}
    	$table .= '<th>Mobile Number</th><th>Date</th><th>Count</th></tr></thead><tbody>';
    	$i=1;
    	foreach ($data as $key => $value) {
    		$table .= '<tr>';
    		$table .= '<td>'.$i++.'</td>';
    		if($userType == 2) {
    			$table .= '<td>'.$value->name.'</td>';
    		}
    		if($userType == 1) {
    			$table .= '<td>'.$value->name.'</td>';
	    		$table .= '<td>'.$value->csName.'</td>';
	    		$table .= '<td>'.$value->parentName.' ('. $value->relation .')</td>';
	    	}
    		$table .= '<td>'.$value->mobile_no.'</td>';
    		$table .= '<td>'.$value->smsDate.'</td>';
    		$table .= '<td>'.$value->count.'</td>';
    		$table .= '</tr>';
    	}
    	$table .= '</tbody>';
    	return $table;
    }

    public function getSMSReportByContent(){
    	$content = $_POST['content'];
    	$contentReport = $this->sms_model->getContentWiseReport($content);
    	$data = $this->_buildSMSReport($contentReport);
    	echo json_encode($data);
    }

    private function _buildSMSReport($result){
    	if(empty($result)) { 
    		$data = '<h4>No SMS sent on these dates.</h4>';
    	} else {
	    	$data = '<table class="table table-bordered datatable dataTable" id="myTable"><thead><tr><th style="width:3%;"># </th><th style="width:11%;">Sent To</th><th style="width:9%;">Sent Date</th><th style="width:40%;">Message</th><th style="width:11%;">Status</th><th style="width:11%;">Source</th><th style="width:5%;">SMS Count</th><th style="width:9%;">Full Report</th></tr></thead><tbody>';
	        $i=1;
	        foreach ($result as $key => $val) {
	        	$sentTo = $val->sent_to;
	            if(preg_match('/^\[/', $sentTo)){
	                $sentTo = json_decode($sentTo);
	                $sentTo = implode($sentTo);
	            }
	            $processed = 0;
	            $awaited = 0;
	            if($val->sms_count != 0) {
	            	$processed = round(($val->processed/$val->sms_count)*100);
	            	$awaited = round(($val->awaited/$val->sms_count)*100);
	            }
	            
				$data .= '<tr><td>'. $i++ .'</td>';
				$data .= '<td>'. $sentTo .'</td>';
				$data .= '<td>'. date("d-m-Y", strtotime($val->sms_date)) .'</td>';
				$data .= '<td>'. $val->sms_content .'</td>';
				$data .= '<td>Processed : '.$processed.'%<br>Awaited : '.$awaited.'%</td>';
				$data .= '<td>'. ucwords(str_replace("_", " ", $val->source)) .'</td>';
				$data .= '<td>'. $val->sms_count .'</td>';
				$data .= '<td><a onclick="getFullReport('.$val->id.',\''.$val->user_type.'\')" data-toggle="modal" data-target="#summary" class="btn btn-xs btn-ifo"><i class="fa fa-plus-circle" style="font-size: 22px;"></i></a></td></tr>';

	        }
	        $data .= '</tbody></table>';
    	}
        return $data;
    }

    public function Report(){
    	// $data['usageReport'] = $this->sms_model->getSMSUsagereport();
    	$data['usageReport'] = $this->sms_model->getReport($this->yearId);
    	$data['startDate'] = '';
    	$data['endDate'] = '';
    	if(!empty($data['usageReport'])) {
    		$data['endDate'] = date('d-m-Y', strtotime($data['usageReport'][0]->date));
    		$last = count($data['usageReport']) - 1;
    		$data['startDate'] = date('d-m-Y', strtotime($data['usageReport'][$last]->date));
    	}
    	$data['credits'] = $this->sms_model->getRemainingSMSCredits();
		$data['main_content'] = 'sms_nh/sms_usage_report';
        $this->load->view('inc/template', $data);
    }

    public function getDateSMSReport() {
    	$from = date('Y-m-d',strtotime($_POST['from']));
    	$to = date('Y-m-d',strtotime($_POST['to']));
    	$data = $this->sms_model->getSMSDateWise($from, $to, $this->yearId);
    	echo json_encode($data);
    }

  //   public function smsUsageReport(){
  //   	$data['usageReport'] = $this->sms_model->getSMSUsagereport();
  //   	$data['credits'] = $this->sms_model->getRemainingSMSCredits();
		// $data['main_content'] = 'sms_nh/sms_usage_report';
  //       $this->load->view('inc/template', $data);
  //   }

    public function getMsgReport() {
    	$date = date('Y-m-d', strtotime($_POST['date']));
        $data['report'] = $this->sms_model->getSMSReportv2($date, $this->yearId);
        $data['main_content'] = 'sms_nh/sms_report_v2';
        $this->load->view('inc/template', $data);
    }

    public function smsloader(){
		$data['permit_sms'] = $this->authorization->isAuthorized('SMS.SEND_SMS') && $this->authorization->isModuleEnabled('SMS');
		$data['credits'] = $this->sms_model->getRemainingSMSCredits();
		$data['sms_history'] = $this->sms_model->get_sms_history();
		$data['main_content'] = 'sms_nh/smsloader';
		$this->load->view('inc/template', $data);
	}

	public function sms_loaded($newcount, $response_code, $msg){
		if($response_code == 0){
			$result = $this->sms_model->new_SMS_load($newcount);
			if($result){
				$msg = $newcount.' new SMS credits are added.';
				$this->session->set_flashdata('flashSuccess', $msg);
			} else {
				$this->session->set_flashdata('flashError', 'Something went wrong.');
			}
			redirect('sms/smsloader');
			
		}else{
			$this->session->set_flashdata('flashError', $msg);
			redirect('sms/smsloader');

		}
	



	}

	public function delete_sms_trans(){
	    $id = $_POST['id'];
	    $result = $this->sms_model->delete_sms_trans($id);
	    echo $result;
	}

	
	public function init_payment_transaction(){
		$source = 'sms_load';
		$credit_count = $_POST['credit_count'];
		$amount = $_POST['amount'];

		if ($credit_count <= 0 || $amount <= 0) {
			$this->session->set_flashdata('flashError', 'Amount or SMS Credit count is 0 or less. Payment cannot be initiated.');
			redirect('sms/smsloader');
		}

		$result = $this->payment->init_payment_to_nextelement($amount, $source, $credit_count);

		if (!$result) {
			$this->session->set_flashdata('flashError', 'Unable to connect to the payment gateway. Try after sometime');
			redirect('sms/smsloader');

		} else {
			$this->sesion->set_flashdata('flashError', 'Something happened. Contact System administrator');
			redirect('sms/smsloader');
			//Code should not come here as there is a redirect call if it is successful
		}
	}


}