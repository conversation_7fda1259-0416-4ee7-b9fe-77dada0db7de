<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  25 March 2018
 *
 * Description: Controller for Substitution Master
 *
 * Requirements: PHP5 or above
 *
 */

class Substitution_master extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('SUBSTITUTION')) {
      redirect('dashboard', 'refresh');
    }

    $this->load->model('timetable/substitution_model');
    $this->load->model('staff/staffleave_model');
    $this->load->model('competition_model');
    $this->load->model('timetable/staff_tt');
    $this->load->library('building_cache');
  }
  
  public function index($subDate = null) {
    //echo '<pre>';print_r($substId);die();
    if ($subDate == null) {
    //Get today's day (1=Mon, 2=Tue, etc)
      $subDate = date('Y-m-d');
    }

    $data['subDate'] = $subDate;
    $data['main_content']    = 'substitution/master/index';
    $this->load->view('inc/template', $data);
  }

  public function getSubSectionsData() {
    $date = $_POST['date'];
    $result = $this->substitution_model->getSubSectionsData($date);
    $data = json_encode($result);
    echo $data;
  }

  public function getSubstData() {
    $date = $_POST['date'];
    $result = $this->substitution_model->getStaffToBeSubstituted($date);
    $data = json_encode($result);
    echo $data;
  }

  public function getSubstOptions() {
    $csId = $_POST['csId'];
    $subDate = $_POST['subDate'];
    $weekDay = $_POST['weekDay'];
    $pSeqEB = $_POST['pSeqEB'];
    $weekDay = 5; //Todo: remove this for production

    $csName = $this->substitution_model->getSectionName($csId);
    $result = $this->substitution_model->getSubstituteFromSameSection($csId, $weekDay, $pSeqEB, $subDate,$csName);
    $data = json_encode($result);
    echo $data;
  }

  public function getCount() {
    $date = $_POST['subDate'];
    $result = $this->substitution_model->getCount($date);
    $data = json_encode($result);
    echo $data;
  }

  public function getStaffRequestData() {
    $date = $_POST['date'];
    $result = $this->substitution_model->getStaffRequestData($date);
    $data = json_encode($result);
    echo $data;
  }

  public function printSubStaff() {
    $subDate = $this->input->post('subDate');
    $data['subPrint'] = $this->substitution_model->getStaffSubstitueDataforprint($subDate);
    $data['main_content']    = 'substitution/print_subs';
    $data['subDate'] = $subDate;
    $this->load->view('inc/template', $data);
  }

  public function resolve_staff_request() {
    //Step 0: Get the required data to do the substitution!!!
    if (empty($this->input->post())) {
      redirect('timetable/substitution_master/index/' . $subDate );    
    }

    $subDate = $this->input->post('subDate');
    $reqId = $this->input->post('reqId');
    $weekDay = date("w", strtotime($subDate));
    $reqDetails = $this->substitution_model->getRequestDetails($reqId);
    $timedoutPSeq = $this->substitution_model->getTimedoutPeriod($reqDetails->className, $weekDay);
    $sectionTT = $this->substitution_model->makeTTForRequest($reqDetails->csId, $weekDay, $reqDetails->staffId);
    //echo '<pre>';print_r($sectionTT);die();

    //Get Timetables for all sections the request staff takes
    $otherCSObjs = $this->substitution_model->getSections($reqDetails->staffId, $weekDay);
   
    //Get the class timetables and the periods that needs to be substituted
    //Also construct the csId array to display the other staff's timetable from javascript
    //Add the req staff's csid if not already present
    $allcsIds = $reqDetails->csId;
    foreach ($otherCSObjs as &$csObj) {
      //Exclude the section TT if it is the same as request section TT
      if ($csObj->csId != $reqDetails->csId) {
        //Get the sectionTT for display
        if ($allcsIds != '') $allcsIds .= ',';
        $allcsIds .= $csObj->csId;
        $tt = $this->substitution_model->makeTTArray($csObj->csId, $weekDay, $reqDetails->staffId, $subDate);
        $csObj = (object) array_merge((array)$csObj, array('tt'=>($tt)));
      }
    }

    //Step 1: Check if there are any leave periods in the section. If so, populate options
    //Get the periods that are taken by teachers on leave on that day
    $leavePeriods = $this->substitution_model->findLeavePeriods($reqDetails->csId, $subDate, $weekDay);
    $this->substitution_model->appendStaffAvailability($leavePeriods, $reqDetails->staffId, $weekDay);

    //Fill the options array
    $options = array();
    if (!empty($leavePeriods)) {
      foreach ($leavePeriods as $lp) {
        if ($lp->reqStaffAvailable == 1) {
          $lp->reason = 'Substitute for Leave/Competition';
          $lp->req_fulfill_type = '1'; //signifies that this is for providing additional period against a substitute(leave/competition,etc)
          $options[] = $lp;
        } else {
          $swapPotentials[] = $lp;
        }
      }
    }

    //Step 2: Check if there are any activities in the section. If so, populate options
    $activityPeriods = $this->substitution_model->findActivityPeriods($reqDetails->csId, $subDate, $weekDay, $reqDetails->staffId);
    $this->substitution_model->appendStaffAvailability($activityPeriods, $reqDetails->staffId, $weekDay);

    if (!empty($activityPeriods)) {
      foreach ($activityPeriods as $ap) {
        if ($ap->reqStaffAvailable == 1) {
          $ap->reason = 'Substitute an Activity';
          $ap->req_fulfill_type = '2'; //signifies that this is for providing additional period against an activity
          $options[] = $ap;
        } else {
          $swapPotentials[] = $ap;
        }
      }
    }

    //Step 3: Prepare Swap potentials for same/different class
    //If swapPotentials' staff is the same as requesting staff, remove them
    if (isset($swapPotentials)) {
      foreach ($swapPotentials as $k => $swp) {
        if ($swp->staffId == $reqDetails->staffId) {
          unset($swapPotentials[$k]);
        }
      }
    }

    //Rule 3a: Swaps from same class!!
    $swapPotentialsSameCS = array();
    if (!empty($swapPotentials)) {
      $swapPotentialsSameCS = $swapPotentials;
      $swapOptionsSameCS = new stdClass();
      $this->__prepareSwaps($swapPotentialsSameCS, $swapOptionsSameCS, $reqDetails, $weekDay, $subDate, 1, $timedoutPSeq);
    }

    //Rule 3b: Swaps from other class!!
    if (!empty($swapPotentials)) {
      //Step 3b.a: Fill Swap Potentials with the occupied period details
      foreach ($swapPotentials as &$swp) {
        $occPeriod = $this->substitution_model->getOccupiedPeriod($reqDetails->staffId, $swp);
        $swp->occPeriod = $occPeriod;
      }

      //Step 3b.b: For each Free Period, get the staff who is taking the class in question
      $swapOptions = new stdClass();
      $this->__prepareSwaps($swapPotentials, $swapOptions, $reqDetails, $weekDay, $subDate, 0, $timedoutPSeq);
    }

    //Rule 4: Prepare Force Substitute periods
    $sectionTTForFPS = $this->__prepareFPS($sectionTT, $leavePeriods, $activityPeriods, $reqDetails, $weekDay);
    //echo '<pre>';print_r($sectionTTForFPS);die();

    //Get total Number of additional periods pending
    $fulfilledAddPeriods = $this->substitution_model->getfulfilledAddPeriods($reqDetails->reqId);

    //Flag the periods already substituted
    $subbedPeriods = $this->substitution_model->getSubbedPeriods($subDate);
    $this->substitution_model->appendSubbedPeriods($subbedPeriods, $sectionTT);
    $this->substitution_model->appendLeavePeriods($leavePeriods, $sectionTT);
    $this->substitution_model->appendSubbedPeriods($subbedPeriods, $options);
    $this->substitution_model->appendSubbedPeriods($subbedPeriods, $sectionTTForFPS);

    //Flag the periods unable to substitute due to time
    $this->substitution_model->appendTimeOut($timedoutPSeq, $sectionTT, $subDate);
    $this->substitution_model->appendTimeOut($timedoutPSeq, $options, $subDate);
    $this->substitution_model->appendTimeOut($timedoutPSeq, $sectionTTForFPS, $subDate);

    // echo '<pre>';print_r($sectionTT);
    // echo '<pre>';print_r($options);
    // echo '<pre>';print_r($swapOptions);
    // echo '<pre>';print_r($sectionTTForFPS);die();    
    
    $data['subbedPeriods'] = $subbedPeriods;
    $data['periodsTBA'] = $reqDetails->numPeriods - $fulfilledAddPeriods;
    $data['subDate'] = $subDate;
    $data['sTimeTable'] = $sectionTT;
    $data['sTimeTableForForce'] = $sectionTTForFPS;
    $data['sTimeTableForForce_json'] = json_encode($sectionTTForFPS);
    $data['otherCSObjs'] = $otherCSObjs;
    $data['allcsIds'] = $allcsIds;
    $data['reqObj'] = $reqDetails;
    $data['reqObj_json'] = json_encode($reqDetails);
    if (!empty($options)) $data['options'] = $options;
    if (!empty($swapPotentials)) $data['swapObjArr'] = json_encode($swapPotentials);
    if (!empty($swapOptions)) $data['swapOptions'] = $swapOptions;
    if (!empty($swapPotentialsSameCS)) $data['swapObjSameCSArr'] = json_encode($swapPotentialsSameCS);
    if (!empty($swapOptionsSameCS)) $data['swapOptionsSameCS'] = $swapOptionsSameCS;
    $data['weekDay'] = $weekDay;
    $data['subDate'] = $subDate;
    $data['main_content']    = 'substitution/requests/index.php';
    $this->load->view('inc/template', $data);
  }

  public function __prepareSwaps(&$swapPotentials, &$swapOptions, $reqDetails, $weekDay, $subDate, $sameSection, $timedoutPSeq) {
    //TODO: Check why are the swap options always equal in number? 
    //Step 2: For each Free Period, get the staff who is taking the class in question.
    foreach ($swapPotentials as &$swp) {
      if ($sameSection)
        $occPeriod = $swp;
      else
        $occPeriod = $swp->occPeriod;
      
      $swapOptions->csName = $occPeriod->csName;

      //echo '<pre>';print_r($swp);die();
      $freePeriods = $this->substitution_model->getFreePeriods($reqDetails->staffId, $weekDay, $occPeriod->csId);
      $periodOptionArr = array();

      //echo '<pre>';print_r($freePeriods);die();
      foreach ($freePeriods as $k => $fp) {
        //Check first if the free period teacher is available
        $isOnLeave = $this->substitution_model->isStaffOnLeave($fp->potStaff, $subDate);
        if ($isOnLeave) continue;

        $swapPossible = $this->substitution_model->isSwapPossible($occPeriod, $fp);
        if ($swapPossible) {
          $swp->potPeriod[] = $fp;

          $periodOption = new stdClass();
          //$periodOption->ttaId = $swp->ttaId;

          //Mark the period as substituted if either the swapPotential period is substituted or the freePeriod is substituted
          if ($this->substitution_model->isSubbedPeriod($swp->ttaId, $subDate) || $this->substitution_model->isSubbedPeriod($fp->ttaId, $subDate)) {
            $periodOption->subbedPeriod = 1;
          } else {
            $periodOption->subbedPeriod = 0;
          }

          //Mark the period as substituted if either the swapPotential period is substituted or the freePeriod is substituted
          if ($this->substitution_model->isTimedoutPeriod($timedoutPSeq, $swp->pSeq, $subDate) || $this->substitution_model->isTimedoutPeriod($timedoutPSeq, $fp->pSeq, $subDate)) {
            $periodOption->timedout = 1;
          } else {
            $periodOption->timedout = 0;
          }          

          if (!$sameSection) {
            $periodOption->leavePeriod = $swp->pSeq;
            $periodOption->leaveCS = $swp->csName;
          } 
          $periodOption->fromPeriod = $occPeriod->pSeq;
          $periodOption->toPeriod = $fp->pSeq;
          $periodOption->displayText = 'P' . $occPeriod->pSeq . ' with P' . $fp->pSeq; //TODO: Handle the case where both swaps happen to the same period!!
          $periodOption->value = $k;
          $periodOptionArr[] = $periodOption;
        }
      }
      $swapOptions->options[] = $periodOptionArr;
    }
  }

  private function __prepareFPS($sectionTT, $leavePeriods, $activityPeriods, $reqDetails, $weekDay) {
    //Remove leavePeriods, activityPeriods and periods taken by the requesting staff from regular periods to get the force substitute periods
    $sectionTTForFPS = $sectionTT;
    
    //Remove Leave periods
    if (!empty($leavePeriods)) {
      foreach ($sectionTTForFPS as $k => $period) {
        foreach ($leavePeriods as $lp) {
          if ($lp->ttaId == $period->ttaId) {
            unset($sectionTTForFPS[$k]);
            break;
          }
        }
      }
    }

    //echo '<pre>';print_r($sectionTTForFPS);
    //echo '<pre>';print_r($activityPeriods);
    //Remove Activity Periods
    foreach ($sectionTTForFPS as $k => $period) {
      foreach ($activityPeriods as $ap) {
        if ($ap->ttaId == $period->ttaId) {
          unset($sectionTTForFPS[$k]);
          break;
        }
      }
    }
    //echo '<pre>';print_r($sectionTTForFPS);die();

    //Remove periods taken by the requesting staff and also periods taken by more than 1 staff
    //echo '<pre>';print_r($reqDetails);die();
    foreach ($sectionTTForFPS as $k => $period) {
      if (strpos($period->staffIds, ',') == true) {
        unset($sectionTTForFPS[$k]);
        continue;
      }
      if ($period->staffIds == $reqDetails->staffId) {
        unset($sectionTTForFPS[$k]);
      }
    }

    //Remove periods occupied by the requesting staff
    $freePeriods = $this->substitution_model->getFreePeriods($reqDetails->staffId, $weekDay, $reqDetails->csId, 0);
    //echo '<pre>';print_r($freePeriods);die();
    foreach ($sectionTTForFPS as $k => $period) {
      $found = 0;
      foreach ($freePeriods as $fp) {
        if ($period->pSeq == $fp->pSeq) {
          $found = 1;
          break;
        }
      }
      if (!$found) {
        unset($sectionTTForFPS[$k]);
      }
    }

    //echo '<pre>';print_r($sectionTTForFPS);die();
    return $sectionTTForFPS;
  }

  public function handleSectionSubstitute() {
    //echo '<pre>';print_r($this->input->post());

    $csId = $this->input->post('csId');
    $subDate = $this->input->post('subDate');
    //$weekDay = date("w", strtotime($subDate));
    $weekDay = 5;

    //Display section timetable

    //Display class teacher of the section
    //Display other staff handling this section
    //Display other staff not handling this section

    $data['csId'] = $csId;
    $data['weekDay'] = $weekDay;
    $data['subDate'] = $subDate;
    //$data['sTimeTable'] = $this->substitution_model->getTT($csId, $weekDay);
    $data['main_content']    = 'substitution/sections/index.php';
    $this->load->view('inc/template', $data);
  }

  public function getSectionTTByWeekday() {
    $csId = $_POST['csId'];
    $subDate = $_POST['subDate'];
    $weekDay = $_POST['weekDay'];

    //Get the section TT and add if any period requires substitute
    $sectionTT = $this->substitution_model->getSectionTTByWeekday($csId,$weekDay);
    $subStaffIds = $this->substitution_model->getStaffToBeSubstituted($subDate);
    foreach ($sectionTT as &$period) {
      $found = 0;
      foreach ($subStaffIds as $sStaffObj) {
        if ($this->__isPresentInArray($period->staffIds, $sStaffObj->staffId)) {
          $found = 1;
          break;
        }
      }
      $period->needsSubstitute = new stdClass();
      if ($found) {
        $period->sStaffObj = $sStaffObj;
        $period->needsSubstitute = 1;
      }
      else {
        $period->needsSubstitute = 0;
      }
    }

    // echo '<pre>';print_r($sectionTT);
    // echo '<pre>';print_r($subStaffIds);die();

    $data['csName'] = $this->substitution_model->getSectionName($csId);
    $data['headerTT'] = $this->substitution_model->getPeriodHeader('40');
    $data['sectionTT'] = $sectionTT;

    echo json_encode($data);
  }

  private function __isPresentInArray ($arr, $item) {
    foreach ($arr as $ele) {
      if ($ele == $item) {
        return 1;
      }
    }
    return 0;
  }

  public function submitSubstitute() {
    echo '<pre>';print_r($this->input->post());die();
  }

  public function substitute() {
    if (empty($this->input->post())) {
      redirect('timetable/substitution_master/index/' . $subDate );    
    }
    //echo '<pre>';print_r($this->input->post());die();
    $subDate = $this->input->post('subDate');
    $substId = $this->input->post('substId');
    $weekDay = date("w", strtotime($subDate));

    $substObj = $this->substitution_model->getSubstDataByID($substId);

    //echo '<pre>';print_r($substObj);

    //What are the sections and periods he is handling?
    $csObjs = $this->substitution_model->getSections($substObj->staffId, $substObj->weekDay);

    //echo '<pre>';print_r($csObjs);

    //Get the class timetables and the periods that needs to be substituted
    foreach ($csObjs as &$csObj) {
      //Get the sectionTT for display
      $sectionTT = $this->substitution_model->makeTTArray($csObj->csId, $substObj->weekDay, $substObj->staffId, $subDate);
      $csObj = (object) array_merge((array)$csObj, array('sectionTT'=>($sectionTT)));
    }

    //echo '<pre>';print_r($csObjs);

    foreach ($csObjs as &$csObj) {
      foreach ($csObj->sectionTT as &$period) {
        $subStaff=[];
        if ($period->needsSubstitute) {
          $subStaff = $this->substitution_model->getSubstituteStaff($csObj->classId, $period->weekDay, $period->pSeq, $substObj->subDate);
          $subStaffWithObjects = $this->substitution_model->getSubstituteStaffDetails($subStaff);
          //echo '<pre>';print_r($subStafWithObjects);die();
          $period = (object)array_merge((array)$period, array('substituteStaff' => $subStaffWithObjects));
        }
      }
    }
    //echo '<pre>';print_r($csObjs);die();

    $data['substObj']=$substObj;
    $data['staffObj'] = //$this->substitution_model->getStaffDetailsById($abStaffId);
//    echo '<pre>';print_r($data['staffObj']);die();
    $data['sTimeTables'] = $csObjs;
    $data['main_content']    = 'substitution/leave/index.php';
    $this->load->view('inc/template', $data);
  }

  public function confirmSubstitutes() {
    //echo '<pre>';print_r($this->input->post());die();

    $subDate = $this->input->post('subDate');

    //echo '<pre>';print_r($subDate);
    $result = $this->substitution_model->submit_substitute();
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Substitution added successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('timetable/substitution_master/index/' . $subDate );    
  }

  public function getStaffTTById() {
    $staffId = $_POST['staffId'];

    echo json_encode($this->staff_tt->getStaffTTById($staffId));
  }

  public function getStaffTTByWeekDay() {
    $allcsId = $_POST['allcsId'];
    $weekDay = $_POST['weekDay'];
   
    echo json_encode($this->staff_tt->getCSStaffTTByWeekDay($allcsId,$weekDay));
  }

  public function submit_additional_period() {
    //echo '<pre>';print_r($this->input->post());die();

    $swapObjArr = json_decode($this->input->post('swapObjArr'));
    $swapObjSameCSArr = json_decode($this->input->post('swapObjSameCSArr'));
    $reqDetails = json_decode($this->input->post('reqObjJson'));
    $selectedSwapOptions = $this->input->post('selectedSwapOptions');
    $selectedSwapOptionsSameCS = $this->input->post('selectedSwapOptionsSameCS');
    $selectedOptions = $this->input->post('selectedOptions');
    $numPeriods = $this->input->post('numPeriods');
    $subDate = $this->input->post('subDate');
    $forcePeriod = $this->input->post('selectedForcePeriod');

    $this->db->trans_begin();

    $result1 = $this->substitution_model->submit_swap_periods($swapObjArr, $selectedSwapOptions, $reqDetails, $subDate);
    $result2 = $this->substitution_model->submit_additional_period();
    //$result3 = $this->substitution_model->update_request_count($reqDetails->reqId, $numPeriods);
    $result5 = $this->substitution_model->submit_swap_periods_samecs($swapObjSameCSArr, $selectedSwapOptionsSameCS, $reqDetails, $subDate);

    if (!empty ($forcePeriod)) {
      $result4 = $this->substitution_model->submit_force_period($subDate);
    } else {
      $result4 = 1;
    }

    if($result1 && $result2 && $result4 && $result5) {
      $this->db->trans_commit();
      if ($this->db->trans_status()) {
        $this->session->set_flashdata('flashSuccess', 'Substitution added successfully.');
      } else {
        $this->session->set_flashdata('flashError', 'Something Wrong..');
      }
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }

    redirect('timetable/substitution_master/index/'. $subDate);
  }

  public function getAllStaffTTByWdWithSub () {
    $subDate = $_POST['subDate'];
    $weekDay = date("w", strtotime($subDate));

    $staffAllo = $this->staff_tt->getAllStaffTTWithSubs($subDate);

    echo json_encode(array("staffTTs" =>$staffAllo, "weekDay" => $weekDay));

  }
}