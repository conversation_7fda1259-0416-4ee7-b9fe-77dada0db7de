<?php

  defined('BASEPATH') OR exit('No direct script access allowed');

  class Dashboard extends CI_Controller {

    private $year_id;

    public function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isAuthorized('ONLINE_CLASS_V2.MODULE')) {
        redirect('dashboard', 'refresh');
      }
      $this->load->library('filemanager');
      $this->load->model('Helpdesk_model', 'helpdesk');
      $this->yearId = $this->acad_year->getAcadYearId();
    }


    public function index(){
      $data['is_admin'] = $this->authorization->isAuthorized('ONLINE_CLASS_V2.ADMIN');
      $permit_reports = $this->authorization->isAuthorized('ONLINE_CLASS_V2.REPORTS');

      $site_url = site_url();
      $data['tiles'] = array(
        [
          'title' => 'Schedules',
          'sub_title' => 'Class schedules',
          'icon' => 'svg_icons/class.svg',
          'url' => $site_url.'online_class_v2/schedule',
          'permission' => 1
        ],
        [
          'title' => 'Clone Schedules',
          'sub_title' => 'Clone schedules',
          'icon' => 'svg_icons/clonetimetables.svg',
          'url' => $site_url.'online_class_v2/schedule/clone_schedules',
          'permission' => $this->authorization->isAuthorized('ONLINE_CLASS_V2.ADMIN')
        ]
      );
      $data['tiles'] = checkTilePermissions($data['tiles']);

      $data['report_tiles'] = array(
        [
          'title' => 'Schedules Report',
          'sub_title' => 'Schedules report',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'online_class_v2/schedule/report',
          'permission' => $permit_reports
        ],
        [
          'title' => 'Staff Schedules',
          'sub_title' => 'Satff wise schedules',
          'icon' => 'svg_icons/staff.svg',
          'url' => $site_url.'online_class_v2/schedule/staff_schedule_report',
          'permission' => $permit_reports
        ],
        [
          'title' => 'Section Schedules',
          'sub_title' => 'Section wise schedules',
          'icon' => 'svg_icons/classandsection.svg',
          'url' => $site_url.'online_class_v2/schedule/section_schedule_report',
          'permission' => $permit_reports
        ]
      );
      $data['report_tiles'] = checkTilePermissions($data['report_tiles']);
      
      // $this->load->helper('chatdata_helper');
      // $data['chatData'] = getChatData('Online Class');
      //echo "<pre>"; print_r($data); die();
      $data['path_prefix'] = $this->filemanager->getFilePath('');
      $data['super_admin'] = $this->authorization->isSuperAdmin();
      $data['main_content']    = 'online_class_v2/dashboard.php';
      $this->load->view('inc/template', $data);  
    }
  }

?>