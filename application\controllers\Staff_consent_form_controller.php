<?php 

class Staff_consent_form_controller extends CI_Controller
{
    function __construct(){
       parent:: __construct();
       $this->load->model('Staff_consent_form_model');
       $this->config->load('form_elements');
       $this->load->library('filemanager');
    }

    public function index(){
        $site_url=site_url();
        $data['main_content'] = 'staff_consent/staff_consent_form';
        $this->load->view('inc/template', $data);
    }

    public function get_staff_consent_forms(){
        $data = $this->Staff_consent_form_model->get_staff_consent_forms();
        echo json_encode($data);
    }

    public function submit_consent_form(){
        $this->load->model('communication/emails_model');
        $data = $this->Staff_consent_form_model->submit_consent_form($_POST);
        $email_send_data = $this->Staff_consent_form_model->send_agree_consent_email_data($_POST['template_id']);
        if(!empty($email_send_data)){
            $email_send_data->content = str_replace('%%consent_name%%',$email_send_data->consent_name,$email_send_data->content);
            $email_send_data->content = str_replace('%%staff_name%%',$email_send_data->staff_name,$email_send_data->content);
            $email_send_data->content = str_replace('%%employee_code%%',$email_send_data->employee_code,$email_send_data->content);
            $email_send_data->content = str_replace('%%status%%',$_POST['status'],$email_send_data->content);
            $sent_by = $this->authorization->getAvatarStakeHolderId();
            $email_send_data->to_emails = explode(',', $email_send_data->members_email);
            $member_email = [];
            array_push($member_email,$email_send_data->staff_email);
            $email_data = [];
                $email_obj = new stdClass();
                $email_obj->stakeholder_id = $this->authorization->getAvatarStakeHolderId();
                $email_obj->avatar_type = 4;
                $email_obj->email = $email_send_data->staff_email;
                $email_data[] = $email_obj;
    
                $email_master_data = array(
                    'subject' => $email_send_data->email_subject,
                    'body' => $email_send_data->content,
                    'source' => 'Staff Consent Form Agreed',
                    'sent_by' => $sent_by,
                    'recievers' => "Staff",
                    'from_email' => $email_send_data->registered_email,
                    'files' => json_encode($email_send_data->attachment),
                    'acad_year_id' => $this->acad_year->getAcadYearID(),
                    'visible' => 1,
                    'sender_list' => NULL,
                    'sending_status' => 'Completed'
                );
    
            $email_master_id = $this->emails_model->saveEmail($email_master_data);
            $this->emails_model->save_sending_email_data($email_data, $email_master_id);
    
            foreach($email_send_data->to_emails as $key => $val){
                array_push($member_email,$val);
                $email_data = [];
                $email_obj = new stdClass();
                $email_obj->stakeholder_id = '';
                $email_obj->avatar_type = '';
                $email_obj->email = $val;
                $email_data[] = $email_obj;
    
                $email_master_data = array(
                    'subject' => $email_send_data->email_subject,
                    'body' => $email_send_data->content,
                    'source' => 'Staff Consent Form Agreed',
                    'sent_by' => $sent_by,
                    'recievers' => "Staff",
                    'from_email' => $email_send_data->registered_email,
                    'files' => json_encode($email_send_data->attachment),
                    'acad_year_id' => $this->acad_year->getAcadYearID(),
                    'visible' => 1,
                    'sender_list' => NULL,
                    'sending_status' => 'Completed'
                );
    
                $this->load->model('communication/emails_model');
                $email_master_id = $this->emails_model->saveEmail($email_master_data);
                $this->emails_model->save_sending_email_data($email_data, $email_master_id);
                
            }
            $this->_send_mail($email_send_data->content, $email_send_data->email_subject, $member_email, $email_send_data->registered_email,array($email_send_data->attachment)); 
        }

        echo $data;
    }

    private function _send_mail($body,$title,$memberEmail,$fromemail,$files_array){
        $this->load->helper('email_helper');
    
          $files_string = '';
          if(!empty($files_array)) {
            $files_string = json_encode($files_array);
          }
        $result = sendEmail($body, $title, 0, $memberEmail, $fromemail, json_decode($files_string));
        return $result;
    }
    
}

?>