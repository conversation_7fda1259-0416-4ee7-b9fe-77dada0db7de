<?php

/*
* To change this license header, choose License Headers in Project Properties.
* To change this template file, choose Tools | Templates
* and open the template in the editor.
*/

/**
* Description of Attendance_controller
*
* <AUTHOR>
*/
class Year_calender_controller extends CI_Controller {

  function __construct() {
    parent::__construct();
    
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }

    if (!$this->authorization->isModuleEnabled('STAFF_MASTER')) {
      redirect('dashboard', 'refresh');
    }    

    date_default_timezone_set('Asia/Kolkata');

    $this->yearId = $this->acad_year->getAcadYearId();        
    $this->load->model('student/Student_Model','student');
    $this->load->model('calender/Calender_model','calender');

  }

  public function index() {
    //listing Page
    die('here');

  }

  public function create() {
    //listing Page

    $data['class'] = $this->student->getclass();
    $data['main_content'] = 'calender/create';

    //echo '<pre>'; print_r($data); die();
    $this->load->view('inc/template', $data);
    
  }

  public function getEvents() {

    $input = $this->input->post();
    $result =  $this->calender->getEvents($input['class_id']);
    //echo '<pre>'; print_r($result);
    //$result = $this->convertYearData($result);

    //echo $result;

    echo json_encode($result);

  }


  private function convertYearData(array $yearData)
    {
        if (empty($yearData)) {
            return 'null';
        }
        $data = '';
        foreach ($yearData as $event) {
            if (empty($data)) {
                $data =  "[{id:{$event['id']}, name:'{$event['name']}', description:'{$event['description']}', startDate: new Date('{$event['start_date']}'), endDate: new Date('{$event['end_date']}')}";
            } else {
                $data .=  ", {id:{$event['id']}, name:'{$event['name']}', description:'{$event['description']}', startDate: new Date('{$event['start_date']}'), endDate: new Date('{$event['end_date']}')}";
            }   
        }
        $data .= ']';
        return $data;
    }

  public function updateEvents() {

    $input = $this->input->post();
    $this->calender->create($input['class_id'],$input['action'],$input['type'],$input['others']);
    echo json_encode($this->calender->getEvents($input['class_id']));

  }



}
