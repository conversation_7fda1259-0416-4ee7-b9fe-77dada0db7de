<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  16 Jan 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_student
 */
class Fees_student extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_cohorts_model');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/fees_collection_model');
    $this->load->library('fee_library');
    $this->load->helper('fees_helper');
    $this->load->library('filemanager');
    $this->load->model('feesv2/Fees_blueprint_model','fbm');
  }

  //Landing function for fee assign student index
  public function index () {

    $data['fee_types'] = $this->fees_student_model->get_fee_types_all();

    $blueprint_id = $this->input->post('blueprint_id');
    if (empty($blueprint_id))
      $data['selectedBP'] = $data['fee_types'][0]->id;
    else
      $data['selectedBP'] = $blueprint_id;

    $data['student_assign'] = $this->fees_student_model->get_assign_student_classwise($data['selectedBP']);

    $data['main_content'] = 'feesv2/student/index';
    $this->load->view('inc/template', $data);
  }

  public function student_assign_publish($blueprint_id='', $selected_class_id = '')
  {
    $data['fee_types'] = $this->fees_student_model->get_fee_types_all();
    $blueprint_id = $this->input->post('blueprint_id');
    $classId = $this->input->post('class_id');
    if (empty($classId)) {
      $classId = $selected_class_id;
    }
    if (empty($blueprint_id))
      $data['selectedBP'] = $data['fee_types'][0]->id;
    else
      $data['selectedBP'] = $blueprint_id;

    $data['selected_class'] = $classId;
    // $data['blueprint_id'] = $blueprint_id;
    
   
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    // $data['studentNames'] = $this->Student_Model->getstudentNames();
    $data['main_content'] = 'feesv2/student/fees_details';
    $this->load->view('inc/template', $data);
  }

  // public function publish_details($blueprint_id, $classId = ''){

  //   $data['fee_types'] = $this->fees_student_model->get_fee_types_all();

  //   if (empty($blueprint_id))
  //     $data['blueprint_id'] = $data['fee_types'][0]->id;
  //   else
  //     $data['blueprint_id'] = $blueprint_id;
    
  //   $data['selected_class'] = $classId;
  //   $data['classList'] = $this->Student_Model->getClassNames();
  //   $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
  //   $data['studentNames'] = $this->Student_Model->getstudentNames();
  //   $data['main_content'] = 'feesv2/student/fees_details';
  //   $this->load->view('inc/template', $data);
  // }
  
  public function publish_fee_assigned(){
    $input = $this->input->post();

    $result = $this->fees_student_model->publshed_student_fee_structure($input);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Published successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    }
    redirect('feesv2/fees_student/student_assign_publish/'.$input['blueprint_id'].'/'.$input['class_id']);
  }

  public function un_publish_fee_assigned(){
    $std_cohort_id = $_POST['std_cohort_id'];
    echo  $this->fees_student_model->un_publshed_student_fee_structure($std_cohort_id);
  }

  public function publish_fee_assigned_student_details(){
    $std_cohort_id = $_POST['std_cohort_id'];
    echo  $this->fees_student_model->publshed_student_fee_structure_student_details($std_cohort_id);
  }

  public function online_payment_enabled_parent()
  {
    $std_cohort_id = $_POST['std_cohort_id'];
    echo  $this->fees_student_model->online_payment_enabled_parent_cohorts($std_cohort_id);
  }

  public function online_disabled_enabled_parent()
  {
    $std_cohort_id = $_POST['std_cohort_id'];
    echo  $this->fees_student_model->online_payment_disabled_parent_cohorts($std_cohort_id);
  }

  public function student_fee_publish(){
    $mode = $_POST['mode'];
    $blueprint_id = $_POST['blueprint_id'];
    switch ($mode) {
      case 'class_id':
        $classId = $_POST['classId'];
        $stdData = $this->fees_student_model->search_std_class_wise_fee($classId, $blueprint_id);
        break;
      case 'name':
        $name = $_POST['name'];
        $stdData = $this->fees_student_model->search_std_name_wise_fee($name, $blueprint_id);
        break;
      case 'ad_no':
        $adNo = $_POST['ad_no'];
        $stdData = $this->fees_student_model->search_std_adm_no_wise_fee($adNo, $blueprint_id);
        break;
    }
    echo json_encode($stdData);
  }


  private function _student_filter_columns($filter_columns, $student_data)
  { 

    $stdFilterColumns = array();
    if (!empty($filter_columns)){

    
    foreach ($filter_columns as $key => $val) {
      $temp = new stdClass();
      switch ($val) {
        case 'academic_year_of_joining':
        if (!empty($student_data->{$val})) {
          $stdFilterColumns[$val] = $this->acad_year->getAcadYearById($student_data->{$val});
        }
        break;
        case 'donor':
        if (!empty($student_data->{$val})) {
          $stdFilterColumns[$val] = $student_data->{$val};
        }
        break;
        case 'class_type':
        $type = 'classType';
        $stdFilterColumns[$val] = $this->fee_library->__get_class_types_value($type, $student_data->{$val});
        break;
        case 'category':
        $stdFilterColumns[$val] = $this->fee_library->__get_cateogry_value($val, $student_data->{$val});
        break;
        case 'is_rte':
        $type = 'rte';
        $stdFilterColumns[$val] = $this->fee_library->__get_cateogry_value($type, $student_data->{$val});
        break;
        case 'medium':
        case 'admission_type':
        case 'board':
        case 'boarding':
          $temp_settings = $this->settings->getSetting($val);
          if (!empty($temp_settings[$student_data->{$val}]))
            $stdFilterColumns[$val] = $temp_settings[$student_data->{$val}];  
          break;    
        case 'has_staff':
          if ($student_data->{$val} === '1')
            $stdFilterColumns['has_staff'] = 'Staff`s kid';
          else
            $stdFilterColumns['has_staff'] = 'Not a Staff`s kid';
          break;
        case 'has_sibling':
          if ($student_data->{$val} === '11')
            $stdFilterColumns['has_sibling'] = 'Doesn`t have a sibling';
          else
            $stdFilterColumns['has_sibling'] = 'Has a sibling';
          break;
        case 'has_transport':
          if ($student_data->{$val} === '1')
            $stdFilterColumns['has_transport'] = 'Yes';
          else
            $stdFilterColumns['has_transport'] = 'No';
          break;
      }
    }
    }
    // print_r($stdFilterColumns);die();
    return $stdFilterColumns;
  }

  public function view_student_fee_structure()
  {
    $input = $this->input->post();
    if(empty($input)) {
      $data = $this->session->userdata('inputData');
    } else {
      $data['blueprint_id'] =$input['blueprint_id'];
      $data['student_id'] = $input['student_id'];
      $data['class_id'] = $input['class_id'];
      $this->session->set_userdata('inputData', $data);
    }
    $data['blue_print'] = $this->fees_collection_model->ge_blueprint_details_by_id($data['blueprint_id']);
    $data['student'] = $this->fees_student_model->get_std_detailsbyId($data['student_id'],$data['blue_print']->acad_year_id);
    $conc_obj = $this->fee_library->compute_concession($data['student'], $data['blue_print']->concession_algo);
    $data['concession_alog'] = $conc_obj;

    $filter_columns = json_decode($data['blue_print']->display_fields);
    $data['filters'] = $this->_student_filter_columns($filter_columns, $data['student']);
    $data['cohort_details'] = $this->fees_collection_model->get_all_cohorts_filter($data['blueprint_id']);    
    if (empty($data['student'])) {
      $data['next_student_id'] = $this->fees_student_model->get_next_studentid($data['student_id'], $data['class_id']);
    }else{
        $fee_status = $this->fees_student_model->get_std_fee_cohort($data['student_id'], $data['blueprint_id']);
        $cohort_id = $this->fees_student_model->determine_cohort($data['blueprint_id'], $data['student']);
        if (!empty($fee_status)) {
            if ($fee_status->fee_collect_status === 'COHORT_CONFIRM' || $data['std_fee_details']->fee_collect_status === 'STARTED') {
            $std_id = $this->fees_student_model->get_next_studentid($data['student_id'], $data['class_id']);
            $data['student_id'] = $std_id;
            $this->session->set_userdata('inputData', $data);
            redirect('feesv2/fees_student/view_student_fee_structure');
          }
        }
      $installments_types = $this->fees_collection_model->get_installment_types($data['blueprint_id']);
      $default_ins = $this->fees_collection_model->get_default_ins_id($cohort_id);
      if (!empty($installments_types)) {
        foreach ($installments_types as $key => $val) {
          if (!empty($default_ins)) {
            if ($default_ins->default_ins != 0) {
              if ($default_ins->default_ins == $val->typeId) {
                $blueprint_installment_types_id = $val->feev2_blueprint_installment_types_id;
                $selectedId = $val->feev2_blueprint_installment_types_id;
              }
            }else{
              $blueprint_installment_types_id = $val->feev2_blueprint_installment_types_id;
              $selectedId = $val->feev2_blueprint_installment_types_id;
            }
          }else{
            $blueprint_installment_types_id = $val->feev2_blueprint_installment_types_id;
            $selectedId = $val->feev2_blueprint_installment_types_id;
          }
        }

        $fee_amount = $this->fees_collection_model->fee_cohort_component_structure($cohort_id, $blueprint_installment_types_id);
        $data['installments_types'] = $installments_types;
        $data['fee_component'] = $fee_amount;
        $data['selected_default'] = $blueprint_installment_types_id;
        $data['cohort_id'] = $cohort_id;
        $data['class_id'] = $data['class_id'];
        $this->session->unset_userdata('inputData');
      }
    }
    $data['main_content'] = 'feesv2/student/fees_structure';
    $this->load->view('inc/template', $data);
  }
  public function cohor_data_insert_fee_table()
  {
    $input = $this->input->post();
    $std_sch_id = $this->fees_student_model->insert_cohort_details($input['blueprint_id'], $input['cohort_id'], $input['cohort_status'], $input['blueprint_installment_type_id'], $input['comp_amount'], $input['concession_amount'], $input['student_id'], $input['concession_name']);
    if ($std_sch_id) {
      $std_id = $this->fees_student_model->get_next_studentid($input['student_id'], $input['class_id']);      
      $data['blueprint_id'] =$input['blueprint_id'];
      $data['student_id'] = $std_id;
      $data['class_id'] = $input['class_id'];
      $data['blueprint_id'] = $input['blueprint_id'];
      $this->session->set_userdata('inputData', $data);
      redirect('feesv2/fees_student/view_student_fee_structure');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
      redirect('feesv2/fees_student/publish_details/'.$input['blueprint_id']);
    }
  }

  public function cohor_data_insert_fee_table_assign_close()
  {
    $input = $this->input->post();
    $std_sch_id = $this->fees_student_model->insert_cohort_details($input['blueprint_id'], $input['cohort_id'], $input['cohort_status'], $input['blueprint_installment_type_id'], $input['comp_amount'], $input['concession_amount'], $input['student_id'], $input['concession_name']);
    if ($std_sch_id) {
      $this->session->set_flashdata('flashSuccess', 'Succesfully Assign');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    }
    redirect('feesv2/fees_student/student_assign_publish/'.$input['blueprint_id'].'/'.$input['class_id']);
  }


  public function publish_fee_to_parent($blueprint_id){
    $stdIds = $this->input->post('stdIds');
    $rstudentIds = $this->fees_student_model->check_fee_cohorts_student_exits($stdIds);
    foreach ($rstudentIds as $key => $std_id) {
      $student_data = $this->fees_student_model->get_std_detailsbyId($std_id);
      $result = $this->fees_student_model->publish_std_fee_cohort($blueprint_id, $student_data, $std_id);      
    }
    if($result){
      $this->session->set_flashdata('flashSuccess', 'Succesfully Published');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    }
    redirect('feesv2/fees_student/publish_details/'.$blueprint_id);
  }

 

  public function custom_cohor_data_insert_fee_table($blueprint_id, $student_id, $cohort_id){
    $input = $this->input->post();
    if (empty($input)) {
      $fee_component = $this->fees_student_model->get_fee_structurebyStdId($blueprint_id, $cohort_id);
      $input = $this->fees_student_model->construct_fee_components($fee_component);        
    }
    $std_sch_id = $this->fees_student_model->insert_cohort_details($input, $blueprint_id, $student_id, $cohort_id);
    if($std_sch_id){
      redirect('feesv2/fees_student/publish_details/'.$blueprint_id);
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
      redirect('feesv2/fees_student/publish_details/'.$blueprint_id);
    }
  }


  // // Edit Fee Structure by student wise 
  // public function custom_student_fee_structure($blueprintId, $stdId){
  //   $data['stdId'] = $stdId;
  //   $data['blueprintId'] = $blueprintId;
  //   // get student details 
  //   $data['std_details'] = $this->fees_student_model->get_std_detailsbyId($stdId);

  //   // get blueprint for student filter display purpose
  //   $data['blue_print'] = $this->fees_student_model->get_filter_blueprint($blueprintId);

  //   // get installment types name by blueprint id wise
  //   $data['installment_types'] = $this->fees_student_model->get_installmenttypebyblueprintId($data['blue_print']->id);

  //   $insTypeId = $this->input->post('ins_type_id');

  //   if (empty($insTypeId)) {
  //     $insTypeId = $data['installment_types'][0]->instypeId;
  //   }
  //   // get fee component installment type wise
    
  //   $cohortstd = $this->fees_student_model->custom_std_cohorts_assingedcheck($blueprintId, $stdId, $insTypeId);
  //   if ($cohortstd) {
  //     $fee_component = $this->fees_student_model->custom_std_cohorts_get_Data($cohortstd->cohortstdId, $insTypeId);
  //   }else{
  //    $fee_component = $this->fees_student_model->get_fee_structurebyStdId($data['std_details'], $insTypeId, $blueprintId);
  //   }
  //   $data['fee_component'] = $fee_component;
  //   $data['main_content'] = 'feesv2/student/fees_structure';
  //   $this->load->view('inc/template', $data);
  // }


  // Custom Fee structure student wise 
  public function insert_custom_fee_structure_to_student($stdId){
    $input = $this->input->post();
    $data['stdId'] = $stdId;
    $result = $this->fees_student_model->insert_custom_fee_structurebyStdId($stdId, $input,'Custom');
    if( $result){
      $this->session->set_flashdata('flashSuccess', 'Succesfully customized');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    }
    redirect('feesv2/fees_student/custom_student_fee_structure/'.$input['blueprintId'].'/'.$stdId);
  }


 

  public function custom_fee_publishupdate(){
    $stdId = $_POST['stdId'];
    echo $this->fees_student_model->publish_custom_feestructure_to_parent($stdId);

  }

  public function view_confirm_structure($cohort_student_id, $blueprint_id,$class_id){

    $data['blueprint_id'] = $blueprint_id;
    $data['cohort_student_id'] = $cohort_student_id;
    $data['class_id'] = $class_id;
    $data['blue_print'] = $this->fees_student_model->get_filter_blueprint($blueprint_id);
    $data['student_id'] = $this->fees_student_model->get_cohort_student_id($cohort_student_id, $blueprint_id);
    $data['fee_component'] = $this->fees_student_model->get_assign_fee_structure($cohort_student_id, $blueprint_id);
    $data['student'] = $this->fees_student_model->get_std_detailsbyId($data['student_id']->student_id, $data['blue_print']->acad_year_id);
    $filter_columns = json_decode($data['blue_print']->display_fields);
    $data['filters'] = $this->_student_filter_columns($filter_columns, $data['student']);
    $data['main_content'] = 'feesv2/student/view_fees_structure';
    $this->load->view('inc/template', $data);
  }

  public function reset_confirm_data($student_id, $cohort_student_id, $blueprint_id, $class_id)
  {
    $result = $this->fees_collection_model->reset_confirm_student_cohort_data($cohort_student_id);
    if ($result) {
      $data['blueprint_id'] =$blueprint_id;
      $data['student_id'] = $student_id;
      $data['class_id'] = $class_id;
      $this->session->set_userdata('inputData', $data);
      $this->session->set_flashdata('flashSuccess', 'Fee data reset successfully');
      redirect('feesv2/fees_student/view_student_fee_structure');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong');
      redirect('feesv2/fees_student/view_confirm_structure/'.$cohort_student_id.'/'.$blueprint_id.'/'.$class_id);
    }
  }

  public function assign_all_student(){
    $classid = $_POST['classid'];
    $blueprint = $_POST['blueprint'];
    echo $this->fees_student_model->assing_all_student_fee_structure($classid, $blueprint);
  }

  public function publish_all_student(){
    $classid = $_POST['classid'];
    $blueprint = $_POST['blueprint'];
    echo $this->fees_student_model->publish_all_student_fee_structure($classid, $blueprint);
  }

  public function online_all_student(){
    $classid = $_POST['classid'];
    $blueprint = $_POST['blueprint'];
    echo $this->fees_student_model->online_all_student_fee_structure($classid, $blueprint);
  }
  
  public function receipt_number_change(){

    // $stdData = $this->fees_student_model->search_std_class_wise_fee($classId, $blueprint_id);
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['payment_modes'] = json_decode($data['fee_blueprints'][0]->allowed_payment_modes);
    $data['card_charge_amount'] = json_encode($this->settings->getSetting('fee_payment_card_charge_amount'));
    $data['classes'] = $this->fees_student_model->get_all_class_section();
    $data['main_content'] = 'feesv2/receipts_change';
    $this->load->view('inc/template', $data);
  }

  public function transaction_amount_change(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['payment_modes'] = json_decode($data['fee_blueprints'][0]->allowed_payment_modes);
    $data['card_charge_amount'] = json_encode($this->settings->getSetting('fee_payment_card_charge_amount'));
    $data['classes'] = $this->fees_student_model->get_all_class_section();
    $data['main_content'] = 'feesv2/transaction_change';
    $this->load->view('inc/template', $data);
  }

  public function get_fee_recetipt_details(){
    $mode = $_POST['mode'];
    $fee_type = $_POST['fee_type'];
    $stdData = $this->fees_student_model->get_receipt_number_student_details($mode, $fee_type);
    echo json_encode($stdData);
  }

  public function update_receipt_number_date(){
    $transId = $_POST['transId'];
    $receiptNumber = $_POST['receiptNumber'];
    $receipts_date = $_POST['receipts_date'];
    // $payMode = $_POST['payMode'];
    // $cardCharge = $_POST['cardCharge'];
    $schId = $_POST['schId'];
    $student_id = $_POST['student_id'];
    $blueprint_id = $_POST['blueprint_id'];

    $auditDesc = 'Receipt Number Change  '.$receiptNumber;
    $this->fees_student_model->insert_fee_audit($student_id, 'Receipt Number Change', $auditDesc, $this->authorization->getAvatarId(), $blueprint_id);

    echo $this->fees_student_model->update_receipt_and_date_transId($transId,$receiptNumber,$receipts_date, $schId);
  }

  public function update_transaction_amount(){
    $transId = $_POST['transId'];
    $amount_paid = $_POST['amount_paid'];
    echo $this->fees_student_model->update_transaction_amount($transId,$amount_paid);
  }
  public function edit_fee_structre($blueprint_id='', $selected_class_id = '')
  {


    $data['fee_types'] = $this->fees_student_model->get_fee_types_all();
    $blueprint_id = $this->input->post('blueprint_id');
    $classId = $this->input->post('class_id');
    if (empty($classId)) {
      $classId = $selected_class_id;
    }
    if (empty($blueprint_id))
      $data['selectedBP'] = $data['fee_types'][0]->id;
    else
      $data['selectedBP'] = $blueprint_id;

    $data['selected_class'] = $classId;
    // $data['blueprint_id'] = $blueprint_id;
    
   
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['studentNames'] = $this->Student_Model->getstudentNames();
    $data['main_content'] = 'feesv2/student/edit_fees';
    $this->load->view('inc/template', $data);
  }

  public function view_confirm_structure_edit($cohort_student_id, $blueprint_id,$class_id, $traverse_to = 0){

    $data['blueprint_id'] = $blueprint_id;
    $data['cohort_student_id'] = $cohort_student_id;
    $data['class_id'] = $class_id;
    $data['traverse_to'] = $traverse_to;
    $data['blue_print'] = $this->fees_student_model->get_filter_blueprint($blueprint_id);
    $data['student_id'] = $this->fees_student_model->get_cohort_student_id($cohort_student_id, $blueprint_id);
    $data['fee_component'] = $this->fees_student_model->get_assign_fee_structure_edit($cohort_student_id, $blueprint_id);
    // echo "<pre>"; print_r($data['student_id']);die();
    $data['student'] = $this->fees_student_model->get_std_detailsbyId($data['student_id']->student_id, $data['blue_print']->acad_year_id);
    $filter_columns = json_decode($data['blue_print']->display_fields);
    $data['filters'] = $this->_student_filter_columns($filter_columns, $data['student']);
    $data['main_content'] = 'feesv2/student/edit_view_fee_structure';
    $this->load->view('inc/template', $data);
  }

  public function update_edit_fees_student(){
    $input = $this->input->post();
   
    foreach ($input['comp_amount'] as $schId => $ins) {
      $sumTotal = 0;
      foreach ($ins as $insId => $comp) {
        $sum = 0;
        foreach ($comp as $compId => $amount) {
          $sum += $amount;
        }
      }
    }
    $this->fees_collection_model->store_fees_edit_history($input['student_id'],$sum.'Rs '.' Fees Amount is Updated');
    $result = $this->fees_student_model->update_edit_fees_student_data($input);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Fee data updated successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong');
    }
    if ($input['traverse_to'] == 1) {
      redirect('student/Student_exit/exit_student/'.$input['student_id']);
    }else{ 
      redirect('feesv2/fees_student/edit_fee_structre');
    }
    
  }

  public function fine_amount($blueprint_id='', $selected_class_id = ''){

    $data['fee_types'] = $this->fees_student_model->get_fee_types_all_fine();
    $blueprint_id = $this->input->post('blueprint_id');
    $classId = $this->input->post('class_id');
    if (empty($classId)) {
      $classId = $selected_class_id;
    }
    if (empty($blueprint_id))
      if (!empty($data['fee_types'])) {
        $data['selectedBP'] = $data['fee_types'][0]->id;
      }
    else
      $data['selectedBP'] = $blueprint_id;

    $data['selected_class'] = $classId;
    // $data['blueprint_id'] = $blueprint_id;
    
   
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    // echo "<pre>"; print_r($data['classSectionList']); die();
    // $data['studentNames'] = $this->Student_Model->getstudentNames();

    $data['main_content'] = 'feesv2/student/fine_amount';
    $this->load->view('inc/template', $data);
  }

  public function fee_over_due_fine_amount(){
    $mode = $_POST['mode'];
    $blueprint_id = $_POST['blueprint_id'];
    switch ($mode) {
      case 'class_id':
        $classId = $_POST['classId'];
        $stdData = $this->fees_student_model->fine_amount_get($classId, $blueprint_id, 'class');
        break;
      case 'name':
        $name = $_POST['name'];
        $stdData = $this->fees_student_model->fine_amount_get($name, $blueprint_id, 'name');
        break;
      case 'ad_no':
        $adNo = $_POST['ad_no'];
        $stdData = $this->fees_student_model->fine_amount_get($adNo, $blueprint_id, 'adm_no');
        break;
    }
    echo json_encode($stdData);
  }

  public function class_wise_fee_assing_data(){
    $classId = $_POST['classId'];
    $studentIds = $this->fees_student_model->class_wise_fee_assing_data_by_id($classId);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function classSection_wise_fee_assing_data(){
    $classSectionId = $_POST['classSectionId'];
    $studentIds = $this->fees_student_model->class_section_wise_fee_assing_data_by_id($classSectionId);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function admission_wise_fee_assing_data(){
    $classId = $_POST['classId'];
    $studentIds = $this->fees_student_model->class_wise_fee_assing_data_by_id($classId);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function fine_amount_get_installment_wise_data(){
    $student_ids = $_POST['student_ids'];
    $blueprint = $_POST['blueprint'];
    $installment_id = $_POST['installment_id'];
    $result = $this->fees_student_model->fee_assing_student_fine_list($student_ids, $blueprint, $installment_id);
    // echo "<pre>"; print_r($result);die();
    echo json_encode($result);
  }

  public function fine_wavier_amount_get_installment_wise_data(){
    $student_ids = $_POST['student_ids'];
    $blueprint = $_POST['blueprint'];
    $installment_id = '';
    $result = $this->fees_student_model->fee_wavier_assing_student_fine_list($student_ids, $blueprint);
    // echo "<pre>"; print_r($result);die();
    echo json_encode($result);
  }

  public function save_fine_amount(){
    $shcId = $_POST['shcId'];
    $fsiId = $_POST['fsiId'];
    $fineAmount = $_POST['fineAmount'];
    $previousInsAmount = $_POST['previousInsAmount'];
    $previousSchAmount = $_POST['previousSchAmount'];
    $this->fees_collection_model->store_fees_edit_history($_POST['student_id'],$_POST['blueprint_type'].' - '.$_POST['installment_type'].' - '.$_POST['installment_name'].' '.$_POST['fineAmount'].'Rs '.' Fine Amount is Added');
    echo $this->fees_student_model->update_fine_amountbyinsId($shcId,$fsiId,$fineAmount, $previousInsAmount, $previousSchAmount);
  }

  public function save_wavier_amount(){
    $shcId = $_POST['shcId'];
    $fsiId = $_POST['fsiId'];
    $wavierAmount = $_POST['wavierAmount'];
    $cohort_student_id = $_POST['cohort_student_id'];
    $remarks = $_POST['remarks'];
    if(isset($_POST['student_id'])){
      $this->fees_collection_model->store_fees_edit_history($_POST['student_id'],$wavierAmount.'Rs '.' Wavier Fine Amount is Added');
    }
    echo $this->fees_student_model->update_wavier_amountbyinsId($shcId,$fsiId,$wavierAmount,$cohort_student_id, $remarks);
  }

  public function concession($blueprint_id='', $selected_class_id = ''){
    $data['fee_types'] = $this->fees_student_model->get_fee_types_all();
    $blueprint_id = $this->input->post('blueprint_id');
    $classId = $this->input->post('class_id');
    if (empty($classId)) {
      $classId = $selected_class_id;
    }
    if (empty($blueprint_id))
      $data['selectedBP'] = $data['fee_types'][0]->id;
    else
    $data['selectedBP'] = $blueprint_id;

    $data['friendly_name'] = $this->fees_cohorts_model->get_friendly_namebyId($data['selectedBP']);
    $fcArr = $this->fees_cohorts_model->get_filter_blueprint($data['selectedBP']);
    $bpDetails = $this->fees_student_model->get_filter_blueprint($data['selectedBP']);
    if ($fcArr[0] === 'none') {
      $display_filters = FALSE;
    } else {
      $display_filters = TRUE;
      foreach ($fcArr as $fc) {
        switch ($fc) {
          case 'academic_year_of_joining':
            $data['showAcadJoining'] = TRUE;
            $data['acadJoiningLabel'] = 'Joining Academic year';
            $data['acadColumnName'] = $fc;
            $data['acadJoiningOptions'] = $this->fees_cohorts_model->getAcadJoiningOptions();
            break;
          case 'is_rte':
            $data['showRTE'] = TRUE;
            $data['rteLabel'] = 'RTE';
            $data['rteColumnName'] = $fc;
            $data['rteOptions'] = $this->fees_cohorts_model->getRTEOptions();
            break;
          case 'class':
            $data['showClass'] = TRUE;
            $data['classLabel'] = 'Class';
            $data['classColumnName'] = $fc;
            $data['classData'] = $this->fees_cohorts_model->getClassList();
          //echo "<pre>";print_r($data['classData']);die();
            break;
          case 'medium':
            $data['showMedium'] = TRUE;
            $data['mediumLabel'] = 'Medium';
            $data['mediumColumnName'] = $fc;
            $data['mediumOptions'] = $this->fees_cohorts_model->getMediumOptions();
            break;
          case 'category':
            $data['showCategory'] = TRUE;
            $data['categoryLabel'] = 'Category';
            $data['categoryColumnName'] = $fc;
            $data['categoryOptions'] = $this->fees_cohorts_model->getCategoryOptions();
            break;
          case 'admission_type':
            $data['showAdmissionType'] = TRUE;
            $data['admissionTypeLabel'] = 'Admission';
            $data['admissionTypeColumnName'] = $fc;
            $data['admissionTypeOptions'] = $this->fees_cohorts_model->getAdmissionTypeOptions();
            break;
          case 'board':
            $data['showBoards'] = TRUE;
            $data['boardsLabel'] = 'Boards';
            $data['boardsColumnName'] = $fc;
            $data['boardList'] = $this->fees_cohorts_model->getBoardsList();
            break;
          case 'boarding':
            $data['showBoarding'] = TRUE;
            $data['boardingLabel'] = 'Boarding';
            $data['boardingColumnName'] = $fc;
            $data['boardingOptions'] = $this->fees_cohorts_model->getBoardingOptions();
            break;
          case 'class_type':
            $data['showClassType'] = TRUE;
            $data['classTypeLabel'] = 'Class Type';
            $data['classTypeName'] = $fc;
            $data['classTypeOptions'] = $this->fees_cohorts_model->getClassTypeOptions();
          break;
          case 'has_staff':
            $data['showStaff'] = TRUE;
            $data['staffLabel'] = 'Has Staff';
            $data['staffColumnName'] = $fc;
            $data['staffOptions'] = ['Not a Staff kid' => '0', 'Staff kid' => '1'];
            break;
          case 'has_sibling':
            $data['showSibling'] = TRUE;
            $data['siblingLabel'] = 'Has Sibling';
            $data['siblingColumnName'] = $fc;
            $data['siblingOptions'] = ['No Sibling' => '0', 'Sibling' => '1'];
            break;
          case 'has_transport':
            $data['showTransport'] = TRUE;
            $data['transportLabel'] = 'Has Transport';
            $data['transportColumnName'] = $fc;
            $data['transportOptions'] = ['No' => '0', 'Yes' => '1'];
            break;
          case 'has_transport_km':
            $data['showTransportKm'] = TRUE;
            $data['TransportKmLabel'] = 'Transport Km';
            $data['TransportKmName'] = $fc;
            $data['TransportKmOptions'] = $this->fees_cohorts_model->get_fee_km_list();
            break;
          case 'stop':
            $data['showTransportStop'] = TRUE;
            $data['TransportStopLabel'] = 'Stop';
            $data['TransportStopName'] = $fc;
            $data['TransportStopOptions'] = $this->fees_cohorts_model->get_fee_Stop_list();
            break;
          case 'pickup_mode':
            $data['showTransportPM'] = TRUE;
            $data['TransportPMLabel'] = 'Pickup Mode';
            $data['TransportPMName'] = $fc;
            $data['TransportPMOptions'] = $this->fees_cohorts_model->get_fee_pickup_mode_list();
            break;
          case 'gender':
            $data['showGenderPM'] = TRUE;
            $data['GenderPMLabel'] = 'Gender';
            $data['GenderPMName'] = $fc;
            $data['GenderPMOptions'] = ['M' => 'Male', 'F' => 'Female'];
            break;
          case 'physical_disability':
            $data['showPhysicalDisabilityPM'] = TRUE;
            $data['PhysicalDisabilityPMLabel'] = 'Physical Disability';
            $data['PhysicalDisabilityPMName'] = $fc;
            $data['PhysicalDisabilityPMOptions'] = ['0' => 'No', '1' => 'Yes'];
            break;
         case 'is_lifetime_student':
            $data['showIsLifeTimeFeePM'] = TRUE;
            $data['IsLifeTimeFeePMLabel'] = 'Is Life Time Student';
            $data['IsLifeTimeFeePMName'] = $fc;
            $data['IsLifeTimeFeePMOptions'] = ['0' => 'No', '1' => 'Yes'];
            break;
          case 'combination':
            $data['showcombination'] = TRUE;
            $data['combinationLabel'] = 'Combination';
            $data['combinationColumnName'] = $fc;
            $data['combinationData'] = $this->fees_cohorts_model->getCombinationOptions();
            break;
          case 'quota':
            $data['showquota'] = TRUE;
            $data['quotaLabel'] = 'Quota';
            $data['quotaColumnName'] = $fc;
            $data['quotaOptions'] = $this->fees_cohorts_model->getQuotaOptions();
            break;
          case 'attempt':
            $data['showattempt'] = TRUE;
            $data['attemptLabel'] = 'Attempt';
            $data['attemptColumnName'] = $fc;
            $data['attemptOptions'] = $this->fees_cohorts_model->getAttemptOptions();
          }
      }
    }
    $data['display_filters'] = $display_filters;
    if ($bpDetails->is_pre_defined_concession == 1) {
      $data['main_content'] = 'feesv2/student/concessionsV2';
    }else{
      $data['main_content'] = 'feesv2/student/concessions';
    }
    $this->load->view('inc/template', $data);
  }

  public function fee_over_due_consission_amount(){
    $result = $this->fees_student_model->concession_update($_POST);
    echo json_encode($result);

    // $mode = $_POST['mode'];
    // $blueprint_id = $_POST['blueprint_id'];
    // switch ($mode) {
    //   case 'class_id':
    //     $stdData = $this->fees_student_model->concession_update($mode, $blueprint_id);
    //     break;
    //   case 'name':
    //     $stdData = $this->fees_student_model->concession_update($mode, $blueprint_id);
    //     break;
    //   case 'ad_no':
    //     $stdData = $this->fees_student_model->concession_update($mode, $blueprint_id);
    //     break;
    // }
    // echo json_encode($stdData);
  }

  public function view_concession_details(){
    $schId = $_POST['schId'];
    $cohort_student_id = $_POST['cohort_student_id'];
    $result = $this->fees_student_model->concession_view($schId);
    $max_comp_count = max($result);
    $no_of_components = count($max_comp_count);
    $blue_print = $this->fees_collection_model->ge_blueprint_by_id($cohort_student_id);
    $preDefined = $this->fees_collection_model->pre_defined_concession($cohort_student_id);
    $preDefinedName = $this->fees_collection_model->pre_defined_concession_details();
    $checkDefined = [];
    foreach ($preDefined as $key => $val) {
      $checkDefined[$val->feev2_predefined_name] = $val;
    }

    $preDefinedArry = [];
    foreach ($preDefinedName as $key => $name) {
     if (!array_key_exists($name->name, $checkDefined)) {
       array_push($preDefinedArry, $name);
     }
    }

    $additionConcession = $this->settings->getSetting('addition_concession_amount');
    $addConAmount = 0;
    if ($additionConcession) {
      $addConAmount = $this->fees_collection_model->get_addition_concession_amount($blue_print->id, $blue_print->student_id);
    }

    echo json_encode(array('concession_amount'=>$result,'blue_print'=>$blue_print,'no_of_components'=>$no_of_components,'addConAmount'=>$addConAmount,'preDefined'=>$preDefined, 'preDefinedName'=>$preDefinedArry));
  }

  public function concession_update_mass(){
    $input = $this->input->post();
    $total_con = 0;
    foreach ($input['concession_amount'] as $key => $val) {
      $total_con += array_sum($val);
    }
    $this->fees_collection_model->store_fees_edit_history($input['student_id'],$total_con.'Rs '.' Concession Amount is Added');
    if (isset($input['addition_concession_amount'])) {
      $this->fees_student_model->update_concession_amount_addition($input['addition_concession_amount'], $input['blueprint_id'],$input['student_id']);
    }
    $result = $this->fees_student_model->update_concession_amount($input['std_sch_id'],$input['concession_amount'],$input['cohort_student_id'],$input['feev2_blueprint_installment_types_id'],$input['concession_name'],$input['concession_amount_add']);
    echo $result;
  }

  public function insert_pre_defined_concession(){
    $input = $this->input->post();
    $result = $this->fees_student_model->insert_pre_defined_concession($input);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Insert Successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong..');
    }
    redirect('feesv2/fees_student/pre_defined_concession');
  }

  public function concession_update_mass_v2(){
    $input = $this->input->post();
    $total_con = 0;
    foreach ($input['concession_amount'] as $key => $val) {
      $total_con += array_sum($val);
    }
    $this->fees_collection_model->store_fees_edit_history($input['student_id'],$total_con.'Rs '.' Concession Amount is Added');
    if (isset($input['addition_concession_amount'])) {
      if($input['addition_concession_amount'] != 0)
        $this->fees_student_model->update_concession_amount_addition($input['addition_concession_amount'], $input['blueprint_id'],$input['student_id']);
    }
    $result = $this->fees_student_model->update_concession_amount_v3($input['std_sch_id'],$input['concession_amount'],$input['cohort_student_id'],$input['feev2_blueprint_installment_types_id'],$input['concession_name'],$input['concession_amount_add'],$input['pre_defined_name'], $input['pre_defined_con_amount'], $input['remove_pre_defined']);
    $concessionFlow = $this->settings->getSetting('fees_concession_approval_flow');
    if($concessionFlow && $total_con > 0){
      $concessionEmail = $this->fees_collection_model->get_concession_email_template();
      if(!empty($concessionEmail)){
        $this->load->model('role');
        $staff_ids = $this->role->getStaffListByPrivilege('FEESV2','CONCESSIONS_APPROVAL');
        if(!empty($staff_ids) && $input['student_id']){
          $acad_year_id = $this->settings->getSetting('academic_year_id');
          $sendBy = $this->authorization->getAvatarStakeHolderId();
          $stdData = $this->fees_collection_model->get_student_data_for_jodoby_student_id($input['student_id'], $acad_year_id);
          foreach ($staff_ids as $key => $staff_id) {
            $staffData = $this->fees_collection_model->get_staff_email_concession_approval($staff_id);
            $this->_send_email_concession_approval($staff_id, $concessionEmail, $staffData, $stdData, $acad_year_id, $sendBy, $total_con);
          }
        }
      }
    }
    echo $result;
  }

  private function _send_email_concession_approval($staff_id, $email_template, $staff_data, $stdData, $acad_year_id, $sendBy, $total_con){
    $studentDetails = [];
    $email_content = $email_template->content;
    $subject = $email_template->email_subject;
    $email_content = str_replace('%%student_name%%', $stdData->student_name, $email_content);
    $email_content = str_replace('%%class_section%%', $stdData->grade, $email_content);
    $email_content = str_replace('%%admission_number%%', $stdData->admission_no, $email_content);
    $email_content = str_replace('%%concession_amount%%', $total_con, $email_content);
    $body = $email_content;
    $member_email = [];
    $member_email_array = explode(',', $email_template->members_email);
    foreach ($member_email_array as $email) {
        $member_email[] = trim($email);
    }
    $email_master_data = array(
      'subject' => $subject,
      'body' => $body,
      'source' => 'Fee Concession',
      'sent_by' => $sendBy,
      'recievers' => "Concession approval permission",
      'from_email' => $email_template->registered_email,
      'files' => NULL,
      'acad_year_id' => $acad_year_id,
      'visible' => 1,
      'sender_list' => NULL,
      'sending_status' => 'Completed'
    );
    $this->load->model('communication/emails_model');
    $email_master_id = $this->emails_model->saveEmail($email_master_data);

    $email_obj = new stdClass();
    $email_obj->stakeholder_id = $staff_id;
    $email_obj->avatar_type = $staff_data->avatar_type;
    $email_obj->email = $staff_data->email_id;
    $email_obj->status = ($staff_data->email_id)?'Awaited':'No Email';
    $emails_data[] = $email_obj;

    $this->emails_model->save_sending_email_data($emails_data, $email_master_id);

    $this->load->helper('email_helper');
    sendEmail($body, $subject, $email_master_id, $member_email, $email_template->registered_email, []);
  }

  public function change_concession_update_mass_v2(){
    $input = $this->input->post();
    echo $this->fees_student_model->changes_update_concession_amount_v2($input['std_sch_id'],$input['concession_amount']);
  }

  public function view_concession_details_after_update(){
    $cohort_student_id = $_POST['cohort_student_id'];
    $result = $this->fees_student_model->get_concession_applied_amount($cohort_student_id);
    echo json_encode($result);
  }
  public function card_charge_correction(){
    $stdId = $_POST['stdId'];
    $bpId = $_POST['bpId'];
    $result = $this->fees_student_model->get_student_schdule_amount_details($stdId, $bpId);
    echo json_encode($result);
  }

  public function update_student_schudle(){
    $schId = $_POST['schId'];
    $amount_paid = $_POST['amount_paid'];
    $payment_status = $_POST['payment_status'];
    $cardCharge = $_POST['cardCharge'];
    echo $this->fees_student_model->update_student_schd_amount_details($schId,$amount_paid, $payment_status, $cardCharge);
  }
  
  public function alumni_adjust_fee_amount(){
    $input = $this->input->post();
    $result =  $this->fees_student_model->update_edit_fees_student_data($input);
    if($result){
      $totalAdjustAmount = $this->calculateTotalAmount($input['adjust_amount']);
      $AdjustRemarks = '';
      if(!empty($input['adjustment_remarks'])){
        $AdjustRemarks = $input['adjustment_remarks'];
      }
      $status = 'The Total adjustment amount '. $totalAdjustAmount.' out of a total amount of ' . $input['total_fee_amount'].' ('.$AdjustRemarks.')';
      $this->fees_collection_model->store_fees_edit_history($input['exit_student_id'], $status);
      $this->fees_student_model->update_adjustment_amount_from_terminate_student($input['std_sch_id'], $input['adjust_amount'], $input['cohort_student_id'],$status);
    }
    echo $result;
}

  private function calculateTotalAmount($array) {
    $total = 0;
    foreach ($array as $value) {
        if (is_array($value)) {
            $total += $this->calculateTotalAmount($value);
        } else {
            $total += $value;
        }
    }
    return $total;
  }


  public function waiver_amount($blueprint_id='', $selected_class_id = ''){

    $data['fee_types'] = $this->fees_student_model->get_fee_types_all_waiver();
    if(!empty($data['fee_types'])){
      $blueprint_id = $this->input->post('blueprint_id');
      $classId = $this->input->post('class_id');
      if (empty($classId)) {
        $classId = $selected_class_id;
      }
      if (empty($blueprint_id))
        $data['selectedBP'] = $data['fee_types'][0]->id;
      else
        $data['selectedBP'] = $blueprint_id;

      $data['selected_class'] = $classId;
      // $data['blueprint_id'] = $blueprint_id;
      
     
      $data['classList'] = $this->Student_Model->getClassNames();
      $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
      // $data['studentNames'] = $this->Student_Model->getstudentNames();
    }
    
    $data['main_content'] = 'feesv2/student/waiver_amount';
    $this->load->view('inc/template', $data);
  }

  public function fee_audit_log(){
    $data['main_content'] = 'feesv2/student/fee_audit_log';
    $this->load->view('inc/template', $data);
  }
  
  public function pre_defined_concession(){
    $data['preDefined_con'] = $this->fees_student_model->get_pre_defined_concession_data();
    $data['main_content'] = 'feesv2/student/pre_defined_concession';
    $this->load->view('inc/template', $data);
  }

  public function active_pre_defined_concession(){
    $stngId = $_POST['stngId'];
    $value = $_POST['value'];
    echo $this->fees_student_model->status_active_pre_defined_concession($stngId,$value); 
  }

  public function addition_insstrans_fee_assigned(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['trans_amount'] = $this->fees_student_model->get_transport_fee_amount_details();
    $data['main_content'] = 'feesv2/student/add_trans_ins_amount';
    $this->load->view('inc/template', $data);
  }

  public function get_add_amount_data(){
    $data['details'] = $this->fees_student_model->get_transport_fee_amount_details();
  }

  public function get_additional_mount_popup(){
    $stdId = $_POST['stdId'];
    $cohort_id = $_POST['cohort_id'];
    $bpId = $_POST['bpId'];
    $result =  $this->fees_student_model->get_additional_mount_popup_details($stdId,$cohort_id,$bpId);
    echo json_encode($result);
  }

  public function get_additional_installment_data(){
    $blueprint_id = $_POST['blueprint_id'];
    $student_id = $_POST['student_id'];
    $result =  $this->fees_student_model->get_additional_installment_details($blueprint_id,$student_id);
    echo json_encode($result);
  }

  public function get_additional_student_installments_list(){
    $blueprint_id = $_POST['blueprint_id'];
    $student_id = $_POST['student_id'];
    $result =  $this->fees_student_model->get_additional_student_installment_details($blueprint_id,$student_id);
    echo json_encode($result);
  }

  public function get_additional_installment_component_data(){
    $blueprint_id = $_POST['blueprint_id'];
    $student_id = $_POST['student_id'];
    $student_installment_id = $_POST['student_installment_id'];
    $result =  $this->fees_student_model->get_additional_installment_component_data_details($blueprint_id,$student_id, $student_installment_id);
    echo json_encode($result);
  }

  public function insert_transport_additional_installments(){
    echo $this->fees_student_model->insert_additional_transport_installments_data();
  }

  public function insert_additional_installments_data(){
    echo $this->fees_student_model->insert_additional_installments_details();
  }

  public function insert_additional_installments_component_data(){
    echo $this->fees_student_model->insert_additional_installments_component_details();
  }


  public function refund_transaction(){
    $data['selectedClassId'] = $this->input->post('classId');
    if (empty($classId)){
      $classId = $this->input->post('classId');
    }
    if (empty($classId)){
      $classId = 0;
    }
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['puc_combination'] = $this->settings->getSetting('puc_combination');
    $data['is_semester_scheme'] = $this->settings->getSetting('is_semester_scheme');
    $data['main_content'] = 'feesv2/student/refund_tx';
    $this->load->view('inc/template', $data);
  }

  public function fee_refund_blueprints($std_id){
     $this->load->helper('fees_helper');
    $data['std_id'] = $std_id;
    $fee_blueprints = $this->fees_student_model->get_blueprints($std_id);
    foreach ($fee_blueprints as &$fbp) {
      //Assign cohorts if not assigned yet
      $data['student'] = $this->fees_student_model->get_std_detailsbyId($std_id, $fbp->acad_year_id);
      if (!empty($data['student'])) {
          $fbp->is_cohort_exists = $this->fees_student_model->determine_cohort($fbp->id, $data['student']);
          $fbp->std_fee_details = $this->fees_student_model->get_std_fee_cohort($std_id, $fbp->id);        
          $fbp->reconciliation = $this->fees_collection_model->check_reconcilation_status($std_id, $fbp->id);
          $fbp->concession_adjustment = $this->fees_student_model->get_concession_adjustment_amount($std_id, $fbp->id);
          $fbp->payment_modes = json_decode($fbp->allowed_payment_modes);
        }
    }
    // echo "<pre>"; print_r($fee_blueprints); die();
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_blueprints'] = $fee_blueprints;
    $data['main_content'] = 'feesv2/student/refund_blueprint';
    $this->load->view('inc/template', $data);
  }

  public function create_invoice(){
    $receipt_book = $this->fbm->get_receipt_books();
    $data['recept_format'] = $this->fee_library->receipt_format_creation($receipt_book);
    $data['invoice'] = $this->fees_student_model->get_all_invoices();
    // echo "<pre>"; print_r($data['recept_format']); die();
    $data['main_content'] = 'feesv2/student/create_inovice';
    $this->load->view('inc/template', $data);
  }

  public function insert_invoice_template(){
    $invoiceSignature = '';
    if (isset($_FILES['invoice_signature'])) {
     $invoiceSignature = $this->s3FileUpload($_FILES['invoice_signature']);
    }
    echo $this->fees_student_model->insert_invoice_template_details($invoiceSignature);
  }
  public function update_invoice_template() {
    $invoiceSignature = '';
    if (isset($_FILES['update_invoice_signature'])) {
      $invoiceSignature = $this->s3FileUpload($_FILES['update_invoice_signature']);
    }
    $result = $this->fees_student_model->update_invoice_template_details($invoiceSignature);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Updated successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    }
  } 
  public function invoice_html_format() {
    $id = $_POST['id'];
    $full_invoice_html = $_POST['full_invoice_html'];
    echo $this->fees_student_model->update_invoice_html_format($id, $full_invoice_html);
  }
  public function get_invoice_templae() {
    $id =$_POST['id'];
    $result = $this->fees_student_model->get_invoice_template_by_id($id);
    echo json_encode($result);
  }

  public function s3FileUpload($file) {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'fee_invoice_signature');
  }

  public function generate_invoice_details(){
    $std_id= $_POST['student_id'];
  
    $feesInvoice = $this->fees_student_model->get_std_fee_for_invoice($std_id);
    if(empty($feesInvoice)){
      echo '-1';
      exit();
    }
    $predefinedConcession = $this->fees_student_model->get_std_fee_pre_defined_conessionfor_invoice($std_id);
    $previous_balance =  $this->fees_student_model->check_invoice_previous_balance_amount($std_id);
    $excess_amount = $this->fees_student_model->get_excess_amount_by_std($std_id);

    $acadYearId =$this->acad_year->getAcadYearId();
    $student_data = $this->fees_student_model->get_std_detailsbyId($std_id, $acadYearId);
    
    $invoiceTemplate = $this->fees_student_model->get_invoice_template();
    if ($invoiceTemplate) {
      $template = $this->_create_fee_invoice_template($feesInvoice, $student_data, $invoiceTemplate, $previous_balance, $excess_amount, $predefinedConcession);
      $result = $this->__generatefeeinvoice_pdf_receipt($template, $invoiceTemplate->invoice_template_id, $std_id, 'portrait', $previous_balance, $excess_amount);
      echo json_encode($result);
    }else{
      echo 0;
    }
  }

  public function generate_invoice_details_ieljch(){
    $std_id= $_POST['student_id'];
    $feesInvoice = $this->fees_student_model->get_std_fee_for_invoice_ielcjh($std_id);
    if(empty($feesInvoice)){
      echo '-1';
      exit();
    }
    $predefinedConcession = $this->fees_student_model->get_std_fee_pre_defined_conessionfor_invoice($std_id);
    $previous_balance =  $this->fees_student_model->check_invoice_previous_balance_amount($std_id);
    $excess_amount = $this->fees_student_model->get_excess_amount_by_std($std_id);
   
    $acadYearId =$this->acad_year->getAcadYearId();
    $student_data = $this->fees_student_model->get_std_detailsbyId($std_id, $acadYearId);
    
    $invoiceTemplate = $this->fees_student_model->get_invoice_template();
    if ($invoiceTemplate) {
      $template = $this->_create_fee_invoice_template_ielcjh($feesInvoice, $student_data, $invoiceTemplate, $previous_balance, $excess_amount, $predefinedConcession);
      $result = $this->__generatefeeinvoice_pdf_receipt($template, $invoiceTemplate->invoice_template_id, $std_id, 'portrait', $previous_balance, $excess_amount);
      echo json_encode($result);
    }else{
      echo 0;
    }
  }

  public function check_pdf_generated_invoice(){
    $std_id= $_POST['student_id'];
    $invoice_type= $_POST['invoice_type'];
    $result =  $this->fees_student_model->check_pdf_generated_invoice($std_id, $invoice_type);
    echo json_encode($result);
  }

  private function __generatefeeinvoice_pdf_receipt($html, $invoicetemplateid, $std_id, $pdf_page_mode) {
    $school = CONFIG_ENV['main_folder'];
    $path = $school.'/fee_invoice/'.uniqid().'-'.time().".pdf";

    $bucket = $this->config->item('s3_bucket');

    $status = $this->fees_student_model->insert_invoice_student_details($invoicetemplateid, $path, $std_id);
    $page = $pdf_page_mode;
    $page_size = 'a4';
    $curl = curl_init();
    $postData = urlencode($html);
    $username = CONFIG_ENV['job_server_username'];
    $password = CONFIG_ENV['job_server_password'];
    $return_url = site_url().'Callback_Controller/updateFeeInvoicePdfLink';

    curl_setopt_array($curl, array(
        CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERPWD => $username . ":" . $password,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
        CURLOPT_HTTPHEADER => array(
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Content-Type: application/x-www-form-urlencoded",
            "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
        ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);
    return $response;
  }

  public function download_invoice($std_id, $templateType, $file_name){
    if($file_name == ''){
      $file_name = 'Student_Fee_'.$templateType.'.pdf';
    } else {
      $file_name = $file_name.'_Fee_'.$templateType.'.pdf';
      $file_name = str_replace('_', ' ', $file_name);
    }
    $link = $this->fees_student_model->download_invoice_fee_receipt($std_id, $templateType);
    $url = $this->filemanager->getFilePath($link->invoice_path);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download($file_name, $data, TRUE);
  }

  public function _create_fee_invoice_template($invoice_details, $student_data,$invoiceTemplate, $previous_balance, $excess_amount, $predefinedConcession){    
    $openingBalance = 0;
    $totalPrevBal = 0;
    $prevBal = 0;
    $label = '';
    if(!empty($previous_balance)){
      foreach ($previous_balance as $key => $val) {
        $openingBalance += $val->balance;
        $totalPrevBal += $val->balance;
      }
    }
    $total_excess_amount = 0;
    if(!empty($excess_amount)){
      if ($excess_amount->total_excess_amount !='0.00') {
        $label = '-';
        $openingBalance = $openingBalance - $excess_amount->total_excess_amount;
        $total_excess_amount = $excess_amount->total_excess_amount;
      }
    }
    if($openingBalance >= 0){
      $label = '';
    }
    // $output = '<strong>'.numbertoCurrencyWithouSymbol($openingBalance).'';
    // echo "<pre>"; print_r($output); die();
    $boarding = $this->settings->getSetting('boarding')[$student_data->boarding];

    $template = $invoiceTemplate->template;
    $template = str_replace('%%student_board_type%%',$boarding, $template);
    $template = str_replace('%%invoice_number%%',$invoiceTemplate->invoice_number, $template);
    $template = str_replace('%%invoice_date%%',date('d-m-Y'), $template);
    $template = str_replace('%%student_class_section%%',$student_data->className, $template);
    $template = str_replace('%%admission_no%%',$student_data->admission_no, $template);
    $template = str_replace('%%application_no%%',$student_data->application_no, $template);

    $template = str_replace('%%student_name%%',$student_data->stdName, $template);
    $template = str_replace('%%current_academic_year%%', $this->acad_year->getAcadYear(), $template);
    $invoice_part = '<table class="table table-bordered" style="border: 2px solid #000;">';
    $invoice_part .= '<tr>';
    $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid;" ></th>';
    $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid;">Amount in Rs.</th>';
    $invoice_part .= '</tr>';
    $totalpayableFee = 0;
    $totalReceivedFee = 0;
    foreach ($invoice_details as $key => $val) {
      $totalpayableFee += $val->component_amount;
      $totalReceivedFee += $val->component_paid;
      $invoice_part .= '<tr>';
      $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$val->blueprint_name.'</th>';
      $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right">'.numberToCurrency_withoutSymbol($val->component_amount).'</th>';
      $invoice_part .= '</tr>';
    }
    $preConcession = 0;
    if(!empty($predefinedConcession)){
      foreach ($predefinedConcession as $key => $con) {
        $preConcession += $con->concession_amount;
        $predefinedName = explode('_',$con->feev2_predefined_name);
        $invoice_part .= '<tr>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$predefinedName[0].'( Discount ) </th>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:right"> - '.numberToCurrency_withoutSymbol($con->concession_amount).'</th>';
        $invoice_part .= '</tr>';
      }
    }
    $invoice_part .='<tr>';
    $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Opening Balance </strong></th>';
    $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong> '.$label.' '.$this->numbertoCurrencyWithouSymbol($openingBalance).'</th>';
    $invoice_part .='</tr>';
    $invoice_part .='<tr>';
    $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Total Amount Payable</strong></th>';
    $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong>'.numberToCurrency_withoutSymbol($totalpayableFee + $totalPrevBal - $total_excess_amount - $preConcession).'</th>';
    $invoice_part .='</tr>';
    $invoice_part .='<tr>';
    $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Amount Received</strong></th>';
    $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong>'.numberToCurrency_withoutSymbol($totalReceivedFee).'</th>';
    $invoice_part .='</tr>';
   
    $invoice_part .='<tr>';
    $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong> Total Balance fee due amount</strong></th>';
    $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:right"><strong>'.numberToCurrency_withoutSymbol(($totalpayableFee + $totalPrevBal - $total_excess_amount- $totalReceivedFee - $preConcession)).'</th>';
    $invoice_part .='</tr>';
    $invoice_part .='</table>';
    $template = str_replace('%%invoice_data%%', $invoice_part, $template);
    
    $amountInWords = $this->get_indianCurrency_in_words($totalpayableFee + $totalPrevBal - $total_excess_amount- $totalReceivedFee - $preConcession);

    $template = str_replace('%%rupees_in_words%%',ucwords($amountInWords), $template);
    
    $signature = '';
    if ($invoiceTemplate->invoice_signature !='') {
      $getURL = $this->filemanager->getFilePath($invoiceTemplate->invoice_signature);
      $signature = '<img style="width:100px !important; height:60px!important" src="'.$getURL.'" />';
    }
    $template = str_replace('%%invoice_signature%%', $signature, $template);
    $template = str_replace('%%invoice_note%%', $invoiceTemplate->invoice_note, $template);

    return $template;
  }

  private function numbertoCurrencyWithouSymbol($number){
     $absNumber = abs($number);
     return number_format($absNumber, 2, '.', ',');
  }
  private function get_indianCurrency_in_words(float $number)
	{
		$schoolName = $this->settings->getSetting('school_short_name');
		$decimal = round($number - ($no = floor($number)), 2) * 100;
		$hundred = null;
		$digits_length = strlen($no);
		$i = 0;
		$str = array();
		$words = array(
			0 => '', 1 => 'one', 2 => 'two',
			3 => 'three', 4 => 'four', 5 => 'five', 6 => 'six',
			7 => 'seven', 8 => 'eight', 9 => 'nine',
			10 => 'ten', 11 => 'eleven', 12 => 'twelve',
			13 => 'thirteen', 14 => 'fourteen', 15 => 'fifteen',
			16 => 'sixteen', 17 => 'seventeen', 18 => 'eighteen',
			19 => 'nineteen', 20 => 'twenty', 30 => 'thirty',
			40 => 'forty', 50 => 'fifty', 60 => 'sixty',
			70 => 'seventy', 80 => 'eighty', 90 => 'ninety'
		);
		$digits = array('', 'hundred', 'thousand', 'lakh', 'crore');
		while ($i < $digits_length) {
			$divider = ($i == 2) ? 10 : 100;
			$number = floor($no % $divider);
			$no = floor($no / $divider);
			$i += $divider == 10 ? 1 : 2;
			if ($number) {
				$plural = (($counter = count($str)) && $number > 9) ? '' : null;
				$hundred = ($counter == 1 && $str[0]) ? ' and ' : null;
				$str[] = ($number < 21) ? $words[$number] . ' ' . $digits[$counter] . $plural . ' ' . $hundred : $words[floor($number / 10) * 10] . ' ' . $words[$number % 10] . ' ' . $digits[$counter] . $plural . ' ' . $hundred;
			} else $str[] = null;
		}
		$Rupees = implode('', array_reverse($str));
		$paise = ($decimal) ? "." . ($words[$decimal / 10] . " " . $words[$decimal % 10]) . ' Paise' : '';
	 	if ($schoolName === 'prarthana') {
	      return 'Rupees ' . ( $Rupees ? $Rupees . 'Only ' : ' ') . $paise ;
	    }
      return ' ' . ( $Rupees ? $Rupees . 'Only ' : ' ') . $paise ;
	}
  
  public function generate_statement_details(){
    $std_id= $_POST['student_id'];
    $feesStatement = $this->fees_student_model->get_std_fee_for_statement($std_id);
    
    $excess_amount = $this->fees_student_model->get_excess_amount_by_std($std_id);
    $previous_year_excess_amount = $this->fees_student_model->get_excess_amount_previous_ob_by_std($std_id);
   
    $acadYearId =$this->acad_year->getAcadYearId();
    $student_data = $this->fees_student_model->get_std_detailsbyId($std_id, $acadYearId);
    $student_Next_Yeardata = $this->fees_student_model->get_std_detailsNextyearbyId($std_id, $acadYearId);
    $invoiceTemplate = $this->fees_student_model->get_statement_template();
    $previousOpeningBalance =  $this->fees_student_model->check_invoice_opening_balance_amount($std_id);
    if ($invoiceTemplate) {
      $template = $this->fees_student_model->create_fee_statement_template($feesStatement, $student_data, $invoiceTemplate, $excess_amount, $student_Next_Yeardata, $previousOpeningBalance, $previous_year_excess_amount);
      // echo "<pre>"; print_r($template); die();
      $result = $this->__generatefeeinvoice_pdf_receipt($template, $invoiceTemplate->invoice_template_id, $std_id, 'portrait');
      echo json_encode($result);
    }else{
      echo 0;
    }
  }

  function generate_invoice_details_iish(){
    $std_id= $_POST['student_id'];
    $feesStatement = $this->fees_student_model->get_std_fee_for_statement_iish($std_id);
    $feesBreakup = $this->fees_student_model->get_std_fee_for_installment_breakup_iish($std_id);
    $previous_balance =  $this->fees_student_model->check_invoice_previous_balance_amount($std_id);
    $excess_amount = $this->fees_student_model->get_excess_amount_by_std($std_id);
    $acadYearId =$this->acad_year->getAcadYearId();
    $student_data = $this->fees_student_model->get_std_detailsbyId($std_id, $acadYearId);
    $invoiceTemplate = $this->fees_student_model->get_invoice_template();
    if ($invoiceTemplate) {
      $template = $this->fees_student_model->create_fee_statement_template_iish($feesStatement, $student_data, $invoiceTemplate, $excess_amount, $feesBreakup, $previous_balance);
      $result = $this->__generatefeeinvoice_pdf_receipt($template, $invoiceTemplate->invoice_template_id, $std_id, 'portrait');
      echo json_encode($result);
    }else{
      echo 0;
    }
  }

  function fees_invoice_statement(){
    // $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['fee_acad_year'] = $this->fees_student_model->get_acad_year_list_for_fees();
    // $data['fees'] = $this->fees_student_model->get_fees_invoice_statment_details(1135);
    // $data['student_data'] = $this->fees_student_model->get_std_details_invoice_statement_byId(1135, 22);
    // echo "<pre>"; print_r($data['fees']); die();
    $data['main_content'] = 'feesv2/student/fee_invoice_statement';
    $this->load->view('inc/template', $data);
  }

  public function class_section_wise_student_data(){
    $classSectionId = $_POST['classSectionId'];
    $studentIds = $this->fees_student_model->class_section_wise_student_data_fee_byId($classSectionId);
    $studentId = array_chunk($studentIds, 250);
    echo json_encode($studentId);
  }

  // public function get_fee_invoice_statement_report(){
  //   $student_ids = $_POST['student_ids'];
  //   $fee_acad_year = $_POST['fee_acad_year'];
  //   $result = $this->fees_student_model->fee_invoice_statement_student_list($student_ids, $fee_acad_year);
  //   echo json_encode($result);
  // }

  function invoice_status_report(){
    $data['main_content'] = 'feesv2/student/invoice_status_report';
    $this->load->view('inc/template', $data);
  }

  function inovice_statement_email_report(){
    $data['main_content'] = 'feesv2/student/inovice_statement_email_report';
    $this->load->view('inc/template', $data);
  }

  public function get_email_report(){
    $filter_type = $_POST['filter_type'];
      $id = $_POST['id'];
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
      // echo '<pre>'; print_r($filter_type); die();
    $result = $this->fees_student_model->get_email_report($from_date, $to_date);
    echo json_encode($result);
  }

  public function get_all_class() {
    echo json_encode( $this->fees_student_model->get_all_class() );
  }

  public function get_sections() {
    echo json_encode( $this->fees_student_model->get_sections() );
  }

  public function _create_fee_invoice_template_ielcjh($invoice_details, $student_data,$invoiceTemplate, $previous_balance, $excess_amount, $predefinedConcession){    
    $openingBalance = 0;
    $totalPrevBal = 0;
    $prevBal = 0;
    $label = '';
    if(!empty($previous_balance)){
      foreach ($previous_balance as $key => $val) {
        $openingBalance += $val->balance;
        $totalPrevBal += $val->balance;
      }
    }
    $total_excess_amount = 0;
    if(!empty($excess_amount)){
     
      if ($excess_amount->total_excess_amount !='0.00') {
        $label = '-';
        $openingBalance = $excess_amount->total_excess_amount + $openingBalance;
        $total_excess_amount = $excess_amount->total_excess_amount;
      }
    } 
    $boarding = $this->settings->getSetting('boarding')[$student_data->boarding];

    $template = $invoiceTemplate->template;
    $template = str_replace('%%student_board_type%%',$boarding, $template);
    $template = str_replace('%%invoice_number%%',$invoiceTemplate->invoice_number, $template);
    $template = str_replace('%%invoice_date%%',date('d-m-Y'), $template);
    $template = str_replace('%%student_class_section%%',$student_data->className, $template);
    $template = str_replace('%%admission_no%%',$student_data->admission_no, $template);

    $template = str_replace('%%student_name%%',$student_data->stdName, $template);
    $template = str_replace('%%current_academic_year%%', $this->acad_year->getAcadYear(), $template);
    $invoice_part = '<table class="table table-bordered" style="border: 2px solid #000;">';
    $invoice_part .= '<tr>';
    $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid;">PARTICULARS</th>';
    $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid;">Actual fee Payble</th>';
    $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid;">1st Term </th>';
    $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid;">2nd Term</th>';
    $invoice_part .= '</tr>';
    $totalpayableFee = 0;
    $totalReceivedFee = 0;
    foreach ($invoice_details as $bp_name => $components) {
      $invoice_part .= '<tr>';
      $invoice_part .= '<th colspan="4" style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$bp_name.'</th>';
      $invoice_part .= '</tr>';
      foreach ($components as $compName => $compAmount) {
        $totalComponentAmount = 0;
        foreach ($compAmount as $installment => $amount) {
          $totalComponentAmount +=$amount['component_amount'];
          $totalpayableFee += $amount['component_amount'];
          $totalReceivedFee += $amount['component_paid'];
        }
        $invoice_part .= '<tr>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$compName.'</th>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left">'.numberToCurrency_withoutSymbol($totalComponentAmount).'</th>';
        foreach ($compAmount as $ins => $value) {
          if(count($compAmount) > 1){
            $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left">'.numberToCurrency_withoutSymbol($value['component_amount']).'</th>';
          }else{
            $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left">'.numberToCurrency_withoutSymbol($value['component_amount']).'</th>';
            $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left"></th>';
          }
          
        }
        $invoice_part .= '</tr>';
      }
    }
    $preConcession = 0;
    if(!empty($predefinedConcession)){
      foreach ($predefinedConcession as $key => $con) {
        $preConcession += $con->concession_amount;
        $predefinedName = explode('_',$con->feev2_predefined_name);
        $invoice_part .= '<tr>';
        $invoice_part .= '<th style="background: #fff;color: #000;border: 2px solid; text-align:left ">'.$predefinedName[0].'( Discount ) </th>';
        $invoice_part .= '<th colspan="3" style="background: #fff;color: #000;border: 2px solid; text-align:left"> - '.numberToCurrency_withoutSymbol($con->concession_amount).'</th>';
        $invoice_part .= '</tr>';
      }
    }
    $invoice_part .='<tr>';
    $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Opening Balance </strong></th>';
    $invoice_part .='<th colspan="3" style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong> '.$label.' '.numberToCurrency_withoutSymbol($openingBalance).'</th>';
    $invoice_part .='</tr>';
    $invoice_part .='<tr>';
    $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Total Amount Payable</strong></th>';
    $invoice_part .='<th colspan="3" style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>'.numberToCurrency_withoutSymbol($totalpayableFee + $totalPrevBal - $total_excess_amount - $preConcession).'</th>';
    $invoice_part .='</tr>';
    $invoice_part .='<tr>';
    $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>Amount Received</strong></th>';
    $invoice_part .='<th colspan="3" style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>'.numberToCurrency_withoutSymbol($totalReceivedFee).'</th>';
    $invoice_part .='</tr>';
   
    $invoice_part .='<tr>';
    $invoice_part .='<th style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong> Total Balance fee due amount</strong></th>';
    $invoice_part .='<th colspan="3" style="background: #fff;color: #000;border: 2px solid; text-align:left"><strong>'.numberToCurrency_withoutSymbol(($totalpayableFee + $totalPrevBal - $total_excess_amount- $totalReceivedFee - $preConcession)).'</th>';
    $invoice_part .='</tr>';
    $invoice_part .='</table>';
    $template = str_replace('%%invoice_data%%', $invoice_part, $template);
    
    $amountInWords = $this->get_indianCurrency_in_words($totalpayableFee + $totalPrevBal - $total_excess_amount- $totalReceivedFee - $preConcession);

    $template = str_replace('%%rupees_in_words%%',ucwords($amountInWords), $template);
    
    $signature = '';
    if ($invoiceTemplate->invoice_signature !='') {
      $getURL = $this->filemanager->getFilePath($invoiceTemplate->invoice_signature);
      $signature = '<img style="width:100px !important; height:60px!important" src="'.$getURL.'" />';
    }
    $template = str_replace('%%invoice_signature%%', $signature, $template);
    $template = str_replace('%%invoice_note%%', $invoiceTemplate->invoice_note, $template);

    return $template;
  }


  public function view_concession_details_new(){
    $schedule_installment_id = $_POST['schedule_installment_id'];
    $cohort_student_id = $_POST['cohort_student_id'];
    $result = $this->fees_student_model->concession_view_new($schedule_installment_id);
    $max_comp_count = max($result);
    $no_of_components = count($max_comp_count);
    $blue_print = $this->fees_collection_model->ge_blueprint_by_id($cohort_student_id);
    $preDefined = $this->fees_collection_model->pre_defined_concession($cohort_student_id);
    $preDefinedName = $this->fees_collection_model->pre_defined_concession_details();
    $checkDefined = [];
    foreach ($preDefined as $key => $val) {
      $checkDefined[$val->feev2_predefined_name] = $val;
    }

    $preDefinedArry = [];
    foreach ($preDefinedName as $key => $name) {
     if (!array_key_exists($name->name, $checkDefined)) {
       array_push($preDefinedArry, $name);
     }
    }

    $additionConcession = $this->settings->getSetting('addition_concession_amount');
    $addConAmount = 0;
    if ($additionConcession) {
      $addConAmount = $this->fees_collection_model->get_addition_concession_amount($blue_print->id, $blue_print->student_id);
    }

    echo json_encode(array('concession_amount'=>$result,'blue_print'=>$blue_print,'no_of_components'=>$no_of_components,'addConAmount'=>$addConAmount,'preDefined'=>$preDefined, 'preDefinedName'=>$preDefinedArry));
  }

  public function concession_update_mass_new(){
    $input = $this->input->post();
    if (isset($input['addition_concession_amount'])) {
      if($input['addition_concession_amount'] != 0)
        $this->fees_student_model->update_concession_amount_addition($input['addition_concession_amount'], $input['blueprint_id'],$input['student_id']);
    }
    $result = $this->fees_student_model->update_concession_amount_new($input['std_sch_id'],$input['concession_amount'],$input['cohort_student_id'],$input['feev2_blueprint_installment_types_id'],$input['concession_name'],$input['concession_amount_add'],$input['pre_defined_name'], $input['pre_defined_con_amount'], $input['remove_pre_defined']);
    echo $result;
  }

  public function remove_applied_concession(){
    $preConId = $_POST['preConId'];
    $this->fees_collection_model->store_fees_edit_history($_POST['student_id'],$_POST['predefined_name'].' '.$_POST['concession_amt'].'Rs '.' Concession Amount is removed');
    echo $this->fees_student_model->remove_applied_concession_by_ids($preConId);
  }

  public function remove_applied_concession_admin(){
    $preConId = $_POST['preConId'];
    echo $this->fees_student_model->remove_applied_concession_admin_by_ids($preConId);
  }

  public function update_concession_details(){
    $predefinedId = $_POST['predefinedId'];
    $remarks = $_POST['remarks'];
    echo $this->fees_student_model->update_concession_details_by_id($predefinedId, $remarks);
  }

  public function download_invoice_statment($invoice_statement_id, $file_name){
    if($file_name == ''){
      $file_name = 'invoice_statment.pdf';
    } else {
      $file_name = urldecode($file_name);
    }
    $file_name = $file_name . ' invoice statement.pdf';
    $link = $this->fees_student_model->download_invoice_statment_pdf($invoice_statement_id);
    $url = $this->filemanager->getFilePath($link->invoice_path);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download($file_name, $data, TRUE);
  }

  public function download_all_statement(){
    $result = $this->fees_student_model->get_all_url_for_download($_POST);
    foreach($result as $res){
      $res->invoice_path = $this->filemanager->getFilePath($res->invoice_path);
    }
    echo json_encode($result);
  }
  
  public function get_fee_invoice_statement_report(){
    $student_ids = $_POST['student_ids'];
    $fee_acad_year = $_POST['fee_acad_year'];
      // echo "<pre>"; print_r($feesStatement);
      
    $fee_statement_data = [];
    foreach ($student_ids as $key => $std_id) {
      $feesStatement = $this->fees_student_model->get_std_fee_for_statement($std_id);
      if(empty($feesStatement)){
        continue;
      }
      $excess_amount = $this->fees_student_model->get_excess_amount_by_std($std_id);
      $previous_year_excess_amount = $this->fees_student_model->get_excess_amount_previous_ob_by_std($std_id);  
      $student_data = $this->fees_student_model->get_std_detailsbyId($std_id, $fee_acad_year);
      $student_Next_Yeardata = $this->fees_student_model->get_std_detailsNextyearbyId($std_id, $fee_acad_year);
      $invoiceTemplate = $this->fees_student_model->get_statement_template();
      $previousOpeningBalance =  $this->fees_student_model->check_invoice_opening_balance_amount($std_id);
      $temp = new stdClass();

      $current_total_payable_fee = 0;
      $current_total_concession = 0;

      $current_total_other_expenses = 0;
      $current_total_other_concession = 0;

      $current_total_expenses = 0;
      $current_total_expenses_concession = 0;

      $current_acad_year_data = [];
      $current_acad_year_other_data = [];
      $current_acad_year_expenses = [];
      if(!empty($feesStatement['current_acad_year_data'])){
        foreach ($feesStatement['current_acad_year_data'] as $acadYearId => $val) {
          if(empty($val->expense_type_mapping) && $val->blueprint_name != 'Other Expenses' || $val->expense_type_mapping =='pocket_money' || $val->expense_type_mapping =='infirmary'){
            $current_total_payable_fee += $val->component_amount;
            foreach ($val->concession as $key => $con) {
              $current_total_concession += $con->concession_amount;
            }
            array_push($current_acad_year_data, $val);
          }
  
          if($val->blueprint_name == 'Other Expenses'){
            $current_total_other_expenses += $val->component_amount;
            foreach ($val->concession as $key => $con) {
              $current_total_other_concession += $con->concession_amount;
            }
            array_push($current_acad_year_other_data, $val);
          }
          if (!empty($val->expense_type_mapping) && $val->expense_type_mapping != 'pocket_money' && $val->expense_type_mapping != 'infirmary') {
            $current_total_expenses += $val->component_amount;
            foreach ($val->concession as $key => $con) {
              $current_total_expenses_concession += $con->concession_amount;
            }
            array_push($current_acad_year_expenses, $val);       
          }  
        }
      }
      

      $current_acad_year_excess_data = [];
      $current_acad_year_excess_amount = 0;
      foreach ($feesStatement['current_acad_year_refund'] as $acadYearId => $val) {  
        $current_acad_year_excess_amount += $val->total_amount;
        array_push($current_acad_year_excess_data, $val);  
      }

      $next_year_full_fee_discount = 0;
      $next_year_total_payable_fee = 0;
      $next_year_concession = 0;

      $next_acad_year_data = [];
      $next_acad_year_con_data = [];
      foreach ($feesStatement['next_acad_year_data'] as $acadYearId => $nextyear) {      
        $next_year_total_payable_fee += $nextyear->component_amount;
        $next_year_full_fee_discount += $nextyear->discount;
        array_push($next_acad_year_data, $nextyear);
      }

      $preConcession_NextYear = 0;
      foreach ($feesStatement['next_acad_year_conc'] as $acadYearId => $con) {
        $next_year_concession += $con->concession_amount; 
        array_push($next_acad_year_con_data, $con);

      }

      $temp->className = $student_data->className;
      $temp->admission_no = $student_data->admission_no;
      $temp->stdName = $student_data->stdName;

      $openingBalance = 0;
      $openingBalanceRecived = 0;
      if(!empty($previousOpeningBalance)){
        foreach ($previousOpeningBalance as $key => $val) {
          $openingBalance += $val->balance;
          if($val->balance !=0){
            if(!empty($val->trans_amount)){
              $openingBalanceRecived += $val->trans_amount;
            }
          }
        }
      }
    
      $temp->openingBalance = round($openingBalance  - $previous_year_excess_amount->total_ob_excess_bal);
      $temp->openingBalanceReceived = $openingBalanceRecived;

      $temp->total_ob_excess_bal = $previous_year_excess_amount->total_ob_excess_bal;
      $temp->total_ob_excess_return = $previous_year_excess_amount->total_ob_excess_return;
      $temp->total_ob_excess_recived = $previous_year_excess_amount->total_ob_excess_recived;

      // $boarding = '';
      // $boarding = $this->settings->getSetting('boarding')[$student_data->boarding];
      $temp->totalReceivedFee = (!empty($feesStatement['transaction'])) ? $feesStatement['transaction']->amount_paid : 0;

      $temp->fullFeeDiscountFee = (!empty($feesStatement['transaction'])) ? $feesStatement['transaction']->full_fee_discount : 0;

      $temp->totalReceivedFee_nextYear = (!empty($feesStatement['transaction_next_year']->amount_paid_next_year)) ? $feesStatement['transaction_next_year']->amount_paid_next_year : 0;

      $temp->totalexcess_nextYear = (!empty($feesStatement['next_year_excess_amount'])) ? $feesStatement['next_year_excess_amount']->total_amount : 0;

      // current acad year
      $temp->current_acad_year_data = $current_acad_year_data;
      $temp->current_total_payable_fee = $current_total_payable_fee;
      $temp->current_total_concession = $current_total_concession;
      // current other expenses
      $temp->current_acad_year_other_data = $current_acad_year_other_data;
      $temp->current_total_other_expenses = $current_total_other_expenses;
      $temp->current_total_other_concession = $current_total_other_concession;

      // current expenses
      $temp->current_acad_year_expenses = $current_acad_year_expenses;
      $temp->current_total_expenses = $current_total_expenses;
      $temp->current_total_expenses_concession = $current_total_expenses_concession;

      // current excess
      $temp->current_acad_year_excess_data = $current_acad_year_excess_data;
      $temp->current_acad_year_excess_amount = $current_acad_year_excess_amount;
      // next year data
      $temp->next_year_full_fee_discount = $next_year_full_fee_discount;
      $temp->next_year_total_payable_fee = $next_year_total_payable_fee;
      $temp->next_year_concession = $next_year_concession;

      $temp->total_excess_amount = 0;
     
      if(!empty($excess_amount) && $excess_amount->total_excess_amount !='0.00'){
        $nextyearExcessAmount = 0;
        if(!empty($feesStatement['next_year_excess_amount'])){
          $nextyearExcessAmount = $feesStatement['next_year_excess_amount']->total_amount;
        }
        $temp->total_excess_amount = $excess_amount->total_excess_amount - $nextyearExcessAmount;
      }

      $fee_statement_data[$std_id] = $temp;
    }
    echo json_encode($fee_statement_data);
  }

  public function generate_expense_data_bulk(){
    $student_id = $this->input->post('student_id'); 
    $other_expenses = (array) $this->fees_collection_model->get_fees_bulk_other_expenses_data($student_id);
    echo json_encode($other_expenses);
  }
}