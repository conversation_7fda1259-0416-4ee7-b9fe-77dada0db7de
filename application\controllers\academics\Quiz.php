<?php

class Quiz extends CI_Controller {

  function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
      }
       $this->load->model('academics/lessonPlan_model', 'lessonplan_model');
       $this->load->model('academics/questions_model');
       $this->load->library('filemanager');
  }

  public function index() {
    $data['grades'] = $this->lessonplan_model->getAllClasses();
    // echo "<pre>";print_r('skjdf');die();
    $data['main_content'] = 'academics/quiz/quiz';
    $this->load->view('inc/template', $data);
  }

  public function questions() {
    $data['grades'] = $this->lessonplan_model->getAllClasses();
    $data['main_content'] = 'academics/quiz/questions';
    $this->load->view('inc/template', $data);
  }

  public function deleteSingleQuestion() {
      $question_id = $_POST['id'];
      echo $this->questions_model->deleteSingleQuestion($question_id);
    }

  public function getSubjectQuestions() {
    $subject_id = $_POST['subject_id'];
    $questions = $this->questions_model->getSubjectQuestions($subject_id);
    echo json_encode($questions);
  }

  public function getTopicQuestions() {
    $sub_topic_id = $_POST['sub_topic_id'];
    $questions = $this->questions_model->getTopicQuestions($sub_topic_id);
    foreach ($questions as $key => $question) {
      $question->question_json = $question->question;
      $q = json_decode($question->question);
      $question->question = $q->question;
      $question->options = $q->options;
      $question->answer = $q->answer;
      // echo "<pre>"; print_r($q); die();
    }
    echo json_encode($questions);
  }

  private function __constructQuestionJSON($input) {
    $question_data = [];
    switch ($input['type']) {
      case 'mcq':
        $question_data['question'] = $input['question'];
        $question_data['options'] = $input['options'];
        $question_data['answer'] = $input['answer'];
        break;

      case 'mcq-image':
        $file = $this->s3FileUpload($_FILES['question']);
        $prefix = $this->filemanager->getFilePath('');
        $question_data['question'] = $prefix.$file['file_name'];
        $question_data['options'] = $input['options'];
        $question_data['answer'] = $input['answer'];
        break;
    }

    return json_encode($question_data);
  }

  public function s3FileUpload($file) {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'questions');
  }

  public function save_new_questions() {
    $input = $_POST;
    // echo '<pre>'; print_r($input); die();
    $staff_id = $this->authorization->getAvatarStakeHolderId();
    $question_json = $this->__constructQuestionJSON($input);
    $data = array(
      'question_type' => $input['type'],
      'question' => $question_json,
      'sub_topic_id' => $input['sub_topic_id'],
      'points' => $input['points'],
      'negative_points' => $input['negative_points'],
      'status' => 1,
      'created_by' => $staff_id,
      'last_modified_by' => $staff_id
    );
    $status = $this->questions_model->saveQuestion($data);
    echo $status;
  }

  public function save_quiz() {
    $status = $this->questions_model->saveQuiz();
    echo $status;
  }

  public function getQuizData() {
    $subject_id = $_POST['subject_id'];
    $quiz = $this->questions_model->getQuizData($subject_id);
    echo json_encode($quiz);
  }

  public function getQuizQuestions() {
    $quiz_id = $_POST['quiz_id'];
    $questions = $this->questions_model->getQuizQuestions($quiz_id);
    echo json_encode($questions);
  }

  public function getSubjectLessons() {
    $quiz_id = $_POST['quiz_id'];
    $lessons = $this->questions_model->getSubjectLessons($quiz_id);
    echo json_encode($lessons);
  }

  public function getSubTopicQuestions() {
    $sub_topic_id = $_POST['sub_topic_id'];
    $questions = $this->questions_model->getSubTopicQuestions($sub_topic_id);
    echo json_encode($questions);
  }

  public function assignQuizQuestions() {
    $quiz_id = $_POST['quiz_id'];
    $questions = $_POST['questions'];
    $lessons = $this->questions_model->assignQuizQuestions($quiz_id, $questions);
    echo json_encode($lessons);
  }

  public function deleteQuizQuestion() {
    $question_id = $_POST['question_id'];
    $quiz_id = $_POST['quiz_id'];
    $points = $_POST['points'];
    $status = $this->questions_model->deleteQuizQuestion($question_id, $quiz_id, $points);
    echo $status;
  }

  public function finalizeQuiz() {
    $quiz_id = $_POST['quiz_id'];
    $status = $this->questions_model->finalizeQuiz($quiz_id);
    echo $status;
  }

  public function updateQuestionPoints() {
    $question_id = $_POST['question_id'];
    $points = $_POST['points'];
    $status = $this->questions_model->updateQuestionPoints($question_id, $points);
    echo $status;
  }

}