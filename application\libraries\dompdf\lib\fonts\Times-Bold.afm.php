<?php return array (
  'codeToName' => 
  array (
    32 => 'space',
    160 => 'space',
    33 => 'exclam',
    34 => 'quotedbl',
    35 => 'numbersign',
    36 => 'dollar',
    37 => 'percent',
    38 => 'ampersand',
    146 => 'quoteright',
    40 => 'parenleft',
    41 => 'parenright',
    42 => 'asterisk',
    43 => 'plus',
    44 => 'comma',
    45 => 'hyphen',
    173 => 'hyphen',
    46 => 'period',
    47 => 'slash',
    48 => 'zero',
    49 => 'one',
    50 => 'two',
    51 => 'three',
    52 => 'four',
    53 => 'five',
    54 => 'six',
    55 => 'seven',
    56 => 'eight',
    57 => 'nine',
    58 => 'colon',
    59 => 'semicolon',
    60 => 'less',
    61 => 'equal',
    62 => 'greater',
    63 => 'question',
    64 => 'at',
    65 => 'A',
    66 => 'B',
    67 => 'C',
    68 => 'D',
    69 => 'E',
    70 => 'F',
    71 => 'G',
    72 => 'H',
    73 => 'I',
    74 => 'J',
    75 => 'K',
    76 => 'L',
    77 => 'M',
    78 => 'N',
    79 => 'O',
    80 => 'P',
    81 => 'Q',
    82 => 'R',
    83 => 'S',
    84 => 'T',
    85 => 'U',
    86 => 'V',
    87 => 'W',
    88 => 'X',
    89 => 'Y',
    90 => 'Z',
    91 => 'bracketleft',
    92 => 'backslash',
    93 => 'bracketright',
    94 => 'asciicircum',
    95 => 'underscore',
    145 => 'quoteleft',
    97 => 'a',
    98 => 'b',
    99 => 'c',
    100 => 'd',
    101 => 'e',
    102 => 'f',
    103 => 'g',
    104 => 'h',
    105 => 'i',
    106 => 'j',
    107 => 'k',
    108 => 'l',
    109 => 'm',
    110 => 'n',
    111 => 'o',
    112 => 'p',
    113 => 'q',
    114 => 'r',
    115 => 's',
    116 => 't',
    117 => 'u',
    118 => 'v',
    119 => 'w',
    120 => 'x',
    121 => 'y',
    122 => 'z',
    123 => 'braceleft',
    124 => 'bar',
    125 => 'braceright',
    126 => 'asciitilde',
    161 => 'exclamdown',
    162 => 'cent',
    163 => 'sterling',
    165 => 'yen',
    131 => 'florin',
    167 => 'section',
    164 => 'currency',
    39 => 'quotesingle',
    147 => 'quotedblleft',
    170 => 'ordfeminine',
    139 => 'guilsinglleft',
    155 => 'guilsinglright',
    150 => 'endash',
    134 => 'dagger',
    135 => 'daggerdbl',
    183 => 'periodcentered',
    182 => 'paragraph',
    149 => 'bullet',
    130 => 'quotesinglbase',
    132 => 'quotedblbase',
    148 => 'quotedblright',
    187 => 'guillemotright',
    133 => 'ellipsis',
    137 => 'perthousand',
    191 => 'questiondown',
    96 => 'grave',
    180 => 'acute',
    136 => 'circumflex',
    152 => 'tilde',
    175 => 'macron',
    168 => 'dieresis',
    184 => 'cedilla',
    151 => 'emdash',
    198 => 'AE',
    216 => 'Oslash',
    140 => 'OE',
    186 => 'ordmasculine',
    230 => 'ae',
    248 => 'oslash',
    156 => 'oe',
    223 => 'germandbls',
    207 => 'Idieresis',
    233 => 'eacute',
    159 => 'Ydieresis',
    247 => 'divide',
    221 => 'Yacute',
    194 => 'Acircumflex',
    225 => 'aacute',
    219 => 'Ucircumflex',
    253 => 'yacute',
    234 => 'ecircumflex',
    220 => 'Udieresis',
    218 => 'Uacute',
    203 => 'Edieresis',
    169 => 'copyright',
    229 => 'aring',
    224 => 'agrave',
    227 => 'atilde',
    154 => 'scaron',
    237 => 'iacute',
    251 => 'ucircumflex',
    226 => 'acircumflex',
    231 => 'ccedilla',
    222 => 'Thorn',
    179 => 'threesuperior',
    210 => 'Ograve',
    192 => 'Agrave',
    215 => 'multiply',
    250 => 'uacute',
    255 => 'ydieresis',
    238 => 'icircumflex',
    202 => 'Ecircumflex',
    228 => 'adieresis',
    235 => 'edieresis',
    205 => 'Iacute',
    177 => 'plusminus',
    166 => 'brokenbar',
    174 => 'registered',
    200 => 'Egrave',
    142 => 'Zcaron',
    208 => 'Eth',
    199 => 'Ccedilla',
    193 => 'Aacute',
    196 => 'Adieresis',
    232 => 'egrave',
    211 => 'Oacute',
    243 => 'oacute',
    239 => 'idieresis',
    212 => 'Ocircumflex',
    217 => 'Ugrave',
    254 => 'thorn',
    178 => 'twosuperior',
    214 => 'Odieresis',
    181 => 'mu',
    236 => 'igrave',
    190 => 'threequarters',
    153 => 'trademark',
    204 => 'Igrave',
    189 => 'onehalf',
    244 => 'ocircumflex',
    241 => 'ntilde',
    201 => 'Eacute',
    188 => 'onequarter',
    138 => 'Scaron',
    176 => 'degree',
    242 => 'ograve',
    249 => 'ugrave',
    209 => 'Ntilde',
    245 => 'otilde',
    195 => 'Atilde',
    197 => 'Aring',
    213 => 'Otilde',
    206 => 'Icircumflex',
    172 => 'logicalnot',
    246 => 'odieresis',
    252 => 'udieresis',
    240 => 'eth',
    158 => 'zcaron',
    185 => 'onesuperior',
    128 => 'Euro',
  ),
  'isUnicode' => false,
  'FontName' => 'Times-Bold',
  'FullName' => 'Times Bold',
  'FamilyName' => 'Times',
  'Weight' => 'Bold',
  'ItalicAngle' => '0',
  'IsFixedPitch' => 'false',
  'CharacterSet' => 'ExtendedRoman',
  'FontBBox' => 
  array (
    0 => '-168',
    1 => '-218',
    2 => '1000',
    3 => '935',
  ),
  'UnderlinePosition' => '-100',
  'UnderlineThickness' => '50',
  'Version' => '002.000',
  'EncodingScheme' => 'WinAnsiEncoding',
  'CapHeight' => '676',
  'XHeight' => '461',
  'Ascender' => '683',
  'Descender' => '-217',
  'StdHW' => '44',
  'StdVW' => '139',
  'StartCharMetrics' => '317',
  'C' => 
  array (
    32 => 250,
    160 => 250,
    33 => 333,
    34 => 555,
    35 => 500,
    36 => 500,
    37 => 1000,
    38 => 833,
    146 => 333,
    40 => 333,
    41 => 333,
    42 => 500,
    43 => 570,
    44 => 250,
    45 => 333,
    173 => 333,
    46 => 250,
    47 => 278,
    48 => 500,
    49 => 500,
    50 => 500,
    51 => 500,
    52 => 500,
    53 => 500,
    54 => 500,
    55 => 500,
    56 => 500,
    57 => 500,
    58 => 333,
    59 => 333,
    60 => 570,
    61 => 570,
    62 => 570,
    63 => 500,
    64 => 930,
    65 => 722,
    66 => 667,
    67 => 722,
    68 => 722,
    69 => 667,
    70 => 611,
    71 => 778,
    72 => 778,
    73 => 389,
    74 => 500,
    75 => 778,
    76 => 667,
    77 => 944,
    78 => 722,
    79 => 778,
    80 => 611,
    81 => 778,
    82 => 722,
    83 => 556,
    84 => 667,
    85 => 722,
    86 => 722,
    87 => 1000,
    88 => 722,
    89 => 722,
    90 => 667,
    91 => 333,
    92 => 278,
    93 => 333,
    94 => 581,
    95 => 500,
    145 => 333,
    97 => 500,
    98 => 556,
    99 => 444,
    100 => 556,
    101 => 444,
    102 => 333,
    103 => 500,
    104 => 556,
    105 => 278,
    106 => 333,
    107 => 556,
    108 => 278,
    109 => 833,
    110 => 556,
    111 => 500,
    112 => 556,
    113 => 556,
    114 => 444,
    115 => 389,
    116 => 333,
    117 => 556,
    118 => 500,
    119 => 722,
    120 => 500,
    121 => 500,
    122 => 444,
    123 => 394,
    124 => 220,
    125 => 394,
    126 => 520,
    161 => 333,
    162 => 500,
    163 => 500,
    'fraction' => 167,
    165 => 500,
    131 => 500,
    167 => 500,
    164 => 500,
    39 => 278,
    147 => 500,
    170 => 300,
    139 => 333,
    155 => 333,
    'fi' => 556,
    'fl' => 556,
    150 => 500,
    134 => 500,
    135 => 500,
    183 => 250,
    182 => 540,
    149 => 350,
    130 => 333,
    132 => 500,
    148 => 500,
    187 => 500,
    133 => 1000,
    137 => 1000,
    191 => 500,
    96 => 333,
    180 => 333,
    136 => 333,
    152 => 333,
    175 => 333,
    'breve' => 333,
    'dotaccent' => 333,
    168 => 333,
    'ring' => 333,
    184 => 333,
    'hungarumlaut' => 333,
    'ogonek' => 333,
    'caron' => 333,
    151 => 1000,
    198 => 1000,
    'Lslash' => 667,
    216 => 778,
    140 => 1000,
    186 => 330,
    230 => 722,
    'dotlessi' => 278,
    'lslash' => 278,
    248 => 500,
    156 => 722,
    223 => 556,
    207 => 389,
    233 => 444,
    'abreve' => 500,
    'uhungarumlaut' => 556,
    'ecaron' => 444,
    159 => 722,
    247 => 570,
    221 => 722,
    194 => 722,
    225 => 500,
    219 => 722,
    253 => 500,
    'scommaaccent' => 389,
    234 => 444,
    'Uring' => 722,
    220 => 722,
    'aogonek' => 500,
    218 => 722,
    'uogonek' => 556,
    203 => 667,
    'Dcroat' => 722,
    'commaaccent' => 250,
    169 => 747,
    'Emacron' => 667,
    'ccaron' => 444,
    229 => 500,
    'Ncommaaccent' => 722,
    'lacute' => 278,
    224 => 500,
    'Tcommaaccent' => 667,
    'Cacute' => 722,
    227 => 500,
    'Edotaccent' => 667,
    154 => 389,
    'scedilla' => 389,
    237 => 278,
    'lozenge' => 494,
    'Rcaron' => 722,
    'Gcommaaccent' => 778,
    251 => 556,
    226 => 500,
    'Amacron' => 722,
    'rcaron' => 444,
    231 => 444,
    'Zdotaccent' => 667,
    222 => 611,
    'Omacron' => 778,
    'Racute' => 722,
    'Sacute' => 556,
    'dcaron' => 672,
    'Umacron' => 722,
    'uring' => 556,
    179 => 300,
    210 => 778,
    192 => 722,
    'Abreve' => 722,
    215 => 570,
    250 => 556,
    'Tcaron' => 667,
    'partialdiff' => 494,
    255 => 500,
    'Nacute' => 722,
    238 => 278,
    202 => 667,
    228 => 500,
    235 => 444,
    'cacute' => 444,
    'nacute' => 556,
    'umacron' => 556,
    'Ncaron' => 722,
    205 => 389,
    177 => 570,
    166 => 220,
    174 => 747,
    'Gbreve' => 778,
    'Idotaccent' => 389,
    'summation' => 600,
    200 => 667,
    'racute' => 444,
    'omacron' => 500,
    'Zacute' => 667,
    142 => 667,
    'greaterequal' => 549,
    208 => 722,
    199 => 722,
    'lcommaaccent' => 278,
    'tcaron' => 416,
    'eogonek' => 444,
    'Uogonek' => 722,
    193 => 722,
    196 => 722,
    232 => 444,
    'zacute' => 444,
    'iogonek' => 278,
    211 => 778,
    243 => 500,
    'amacron' => 500,
    'sacute' => 389,
    239 => 278,
    212 => 778,
    217 => 722,
    'Delta' => 612,
    254 => 556,
    178 => 300,
    214 => 778,
    181 => 556,
    236 => 278,
    'ohungarumlaut' => 500,
    'Eogonek' => 667,
    'dcroat' => 556,
    190 => 750,
    'Scedilla' => 556,
    'lcaron' => 394,
    'Kcommaaccent' => 778,
    'Lacute' => 667,
    153 => 1000,
    'edotaccent' => 444,
    204 => 389,
    'Imacron' => 389,
    'Lcaron' => 667,
    189 => 750,
    'lessequal' => 549,
    244 => 500,
    241 => 556,
    'Uhungarumlaut' => 722,
    201 => 667,
    'emacron' => 444,
    'gbreve' => 500,
    188 => 750,
    138 => 556,
    'Scommaaccent' => 556,
    'Ohungarumlaut' => 778,
    176 => 400,
    242 => 500,
    'Ccaron' => 722,
    249 => 556,
    'radical' => 549,
    'Dcaron' => 722,
    'rcommaaccent' => 444,
    209 => 722,
    245 => 500,
    'Rcommaaccent' => 722,
    'Lcommaaccent' => 667,
    195 => 722,
    'Aogonek' => 722,
    197 => 722,
    213 => 778,
    'zdotaccent' => 444,
    'Ecaron' => 667,
    'Iogonek' => 389,
    'kcommaaccent' => 556,
    'minus' => 570,
    206 => 389,
    'ncaron' => 556,
    'tcommaaccent' => 333,
    172 => 570,
    246 => 500,
    252 => 556,
    'notequal' => 549,
    'gcommaaccent' => 500,
    240 => 500,
    158 => 444,
    'ncommaaccent' => 556,
    185 => 300,
    'imacron' => 278,
    128 => 500,
  ),
  'CIDtoGID_Compressed' => true,
  'CIDtoGID' => 'eJwDAAAAAAE=',
  '_version_' => 6,
);