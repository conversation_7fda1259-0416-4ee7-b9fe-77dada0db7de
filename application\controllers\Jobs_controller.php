<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  17 May 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Jobs_controller extends CI_Controller {

	public function __construct() {
		parent::__construct();

		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }

    if (!$this->authorization->isAuthorized('CIRCULAR.MODULE')) {
      redirect('dashboard', 'refresh');
    }

    $this->load->library('mailer', 'mailer');
    $this->load->model('jobs_model');
  }

  //Landing function for Circular Index page from menu
  public function index() {

    $data['jobs'] = $this->jobs_model->getJobs();

    $data['main_content']    = 'jobs/index';
    $this->load->view('inc/template', $data);  
  
  }

  public function executeJob($job_id) {

    $job_details = $this->jobs_model->getJobDetails($job_id);
    // echo '<pre>'; print_r($job_details); die();

    if(empty($job_details)) {
      $this->session->set_flashdata('flashError', 'Job Details not Found');
      redirect('Jobs_controller');
    } else {
      $job_details = $job_details[0];

      if($job_details->status != 0) {
        $this->session->set_flashdata('flashError', 'Job has been already executed');
        redirect('Jobs_controller');
      } else {

        switch($job_details->type) {

          case 1 : {

            $circularData = $this->jobs_model->getCircularDetails($job_details->reference_id);
            $message = $circularData[0]->category . '<br>' . $circularData[0]->circular_title . "<br>" . $circularData[0]->circular_content;

            $this->prepareBulkEmail($message, $job_details->emails, $job_details->reference_id); 

            $this->jobs_model->updateJobStatus($job_id);

            $this->session->set_flashdata('flashSuccess', 'Job executed.');
            redirect('Jobs_controller');

          }

          break;


        }
      }
    }
  }

  public function prepareBulkEmail ($message, $emails, $circularId) {
    
    $emailConfig = $this->settings->getSetting('email');

    $this->mailer->sendBulkEmails($emails, 'demo', array(
        'username'  => 'Parent',
        'subject'   => $this->input->post('circular_category').'-'.$this->input->post('circular_title'),
        'message'   => $this->input->post('circular_content'),
        'mailtype'  => 'text',
    ));
  }


}