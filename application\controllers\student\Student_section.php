<?php 

class Student_section extends CI_Controller {
  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_MASTER')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT_MASSUPDATE.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('student/Student_section_model');
  }

  public function index(){
    $data['classList'] = $this->Student_section_model->getclass();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'student/student_section/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'student/student_section/index_mobile';
    }else{
      $data['main_content']    = 'student/student_section/index';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function getStudentsByClass(){
    $class_id = $_POST['class_id'];
    $data['studentData'] = $this->Student_section_model->getStudentDetails($class_id);
    $data['sectionData'] = $this->Student_section_model->getSectionDetails($class_id);

    echo json_encode($data);

  }

  public function submitSectionUpdate(){
    $input = $this->input->post();
    $this->Student_section_model->store_edit_history($input);
    $result = $this->Student_section_model->updateStudentSection();
    if ($result)
				$this->session->set_flashdata('flashSuccess', 'Sections Updated Successfully.');
    else
      $this->session->set_flashdata('flashError', 'Something went wrong.');
      redirect("student/student_section/index");
  }
}   
