<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Url_qr_codes_controller extends CI_Controller {

  public function __construct() {
    parent::__construct();
    $this->load->library('filemanager');
  }
  function index(){
    $guid = $this->uri->segment(2);
    $data['school_name'] = $this->settings->getSetting('school_name');
    $data['school_address1'] = $this->settings->getSetting('school_name_line1');
    $data['school_address2'] = $this->settings->getSetting('school_name_line2');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    
    $data['std_data'] = $this->db->select("sa.id as stdId,sa.admission_no,CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_Name,concat(ifnull(cs.class_name,'-'), ' ' , ifnull(cs.section_name,'')) as class_section,if(sa.gender = 'M','Male','Female') as gender,if(sa.religion = '0','-',sa.religion) as religion,if(sa.blood_group = 'Unknown ','-',sa.blood_group) as blood_group,sy.picture_url as std_photo, CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) as father_name,CONCAT(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) as mother_name,p.mobile_no,date_format(sa.dob,'%d-%m-%Y') as dob")
    ->from('student_admission sa')
    ->join('student_year sy',"sa.id=sy.student_admission_id")
    ->join('class_section cs','sy.class_section_id=cs.id')
    ->join('student_relation sr',"sa.id=sr.std_id")
    ->join('parent p','sr.relation_id=p.id')
    ->join('student_relation sr1',"sa.id=sr1.std_id")
    ->join('parent p1','sr1.relation_id=p1.id')
    ->where('sr.relation_type','Father')
    ->where('sr1.relation_type','Mother')
    ->like('sa.identification_code',$guid)
    ->order_by('sy.id','desc')
    ->limit(1)
    ->get()->row();

    if(!empty($data['std_data']->std_photo)){
      $data['std_data']->std_photo = $this->filemanager->getFilePath($data['std_data']->std_photo);
    }else{
      $data['std_data']->std_photo = 'https://iisb.localhost.in/oxygenv2/assets/img/sample_boy_image.png';
    }

    $std_address = $this->db->select("concat(ifnull(Address_line1,''),' ',ifnull(Address_line2,''),' ',ifnull(area,''),ifnull(district,''),' ',ifnull(state,''),' ',ifnull(country,''),' ',ifnull(country,''),' ',ifnull(pin_code,'')) as address")
    ->where('stakeholder_id', $data['std_data']->stdId)
    ->where('avatar_type', 1)
    ->where('address_type', 0)
    ->get('address_info')->row();

    $data['std_data']->address = '';
    if(!empty($std_address)){
      $data['std_data']->address = $std_address->address;
    }
    // echo '<pre>' ;print_r($data['std_data']->address->address);die();
    $this->load->view('download/view_student_url_qr_code',$data);
  }
}
?>