<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Observation extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->load->model('observation_model');
    $this->load->model('avatar');
    $this->load->model('staff/Staff_Model');

    $this->templates = [
      ["name" => "Blank Template", "value" => ""],
      ["name" => "Predefined", "value" => "<style type='text/css'>
      table tr th {
          vertical-align: middle;
          border: solid 1px #474747;
          text-align: center;
          border-collapse: collapse;
          word-wrap: break-word;
          background:#474747;
          color:#fff;
          padding:4px;
          font-size: 13px;
          border-color:#C21B31;
      }
      
      table tr td {
          vertical-align: middle;
          border: solid 1px #474747;
          border-collapse: collapse;
          word-wrap: break-word;
          padding:5px;
          font-size: 14px;
      }
      
      table{
          border: solid 1px #474747;
          border-collapse: collapse;
          width: 100%;
      }
  </style>
  <table>
      <tr>
          <td colspan='3'>Name of the Teacher: </td>
      </tr>
      <tr>
          <td>Grade and Section:</td>
          <td></td>
          <td>Date: </td>
      </tr>
      <tr>
          <td colspan='2'>Topic: </td>
          <td></td>
      </tr>
      <tr>
          <td></td>
          <td>Obtained Marks</td>
          <td>Scored Marks</td>
      </tr>
      <tr>
          <td>Content </td>
          <td></td>
          <td></td>
      </tr>
      <tr>
          <td colspan='3'></td>
          
      </tr>
      <tr>    
          <td>Connect</td>
          <td></td>
          <td></td>
      </tr>
      <tr>
          <td colspan='3'></td>
          
      </tr>
      <tr>
          <td>Control</td>
          <td></td>
          <td></td>
      </tr>
      <tr>
          <td colspan='3'></td>
          
      </tr>
      <tr>
          <td>Consolidation</td>
          <td></td>
          <td></td>
      </tr>
      <tr>
          <td colspan='3'></td>
          
      </tr>
      <tr>
          <td colspan='3'>Comments:</td>
          
      </tr>
      <tr>
          <td colspan='3'></td>
      </tr>
      <tr>
          <td colspan='3'>Suggestion:</td>
      </tr>
      <tr>
          <td colspan='3'></td>
      </tr>
      <tr>
          <td>Total Marks:</td>
          <td></td>
          <td></td>
      </tr>
      <tr>
          <td colspan='3'></td>
      </tr>
      <tr>
          <td>Signature of a teacher: </td>
      </tr>
  </table>"]
    ];
  }

  public function index(){
    if (!$this->authorization->isAuthorized('STAFF_OBSERVATION.VIEW') &&  !$this->authorization->isAuthorized('STAFF_OBSERVATION.VIEW_ALL')) {
      redirect('dashboard');
    }  
    $viewPrivilege = $this->authorization->isAuthorized('STAFF_OBSERVATION.VIEW_ALL');
    $staff = $this->staffcache->getStaffCache();
    $data['staff'] = $staff;
    if($viewPrivilege == 0){
      $data['observed'] = $this->Staff_Model->getObservedData($staff->staffId);
      // $counts = $this->observation_model->getClassWiseCount($staff->staffId);
    } else {
      $data['observed'] = $this->Staff_Model->getObservedData(0);
      // $counts = $this->observation_model->getClassWiseCount(0);
    }
    $Date = date('Y-m-d');
      $data['from_date'] = date('Y-m-d', strtotime($Date. ' - 7 day'));
    $data['to_date'] = date("Y-m-d");

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'staff/observation/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'staff/observation/index_mobile';
    }else{
      $data['main_content']    = 'staff/observation/index';     	
    }

    $this->load->view('inc/template', $data);
  }

  public function addObservation(){
    if (!$this->authorization->isAuthorized('STAFF_OBSERVATION.CREATE')) {
      redirect('dashboard');
    }  
    $data['departmentList'] = $this->Staff_Model->getStaffDepartmentList();
    $data['staff_type'] = $this->settings->getSetting('staff_type');

    // echo "<pre>"; print_r($data['departmentList']); die();

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'staff/observation/add_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'staff/observation/add_mobile';
    }else{
      $data['main_content']    = 'staff/observation/add';    	
    }

    $this->load->view('inc/template', $data);
  }

  public function get_staff_by_stafftype() {
    $staffType = $_POST['staffType'];
    $department_id = $_POST['department_id'];
    $staff = $this->Staff_Model->get_staff_by_stafftype($staffType, $department_id);
    echo json_encode($staff);
  }

  public function submitObservation(){
    if (!$this->authorization->isAuthorized('STAFF_OBSERVATION.CREATE')) {
      redirect('dashboard');
    }  
    $staffId = 0;
    $staff = $this->staffcache->getStaffCache();
    if(!empty($staff)){
      $staffId = $staff->staffId;
    }
    $status = (int) $this->Staff_Model->submitObservation($staffId,$this->s3FileUpload($_FILES['document']));
    if($status){
      $this->session->set_flashdata('flashSuccess', 'Successfully Added Observation.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('staff/observation/');
  }

  public function editObservation($id){
    if (!$this->authorization->isAuthorized('STAFF_OBSERVATION.CREATE')) {
      redirect('dashboard');
    }  
    $data['editData'] = $this->Staff_Model->getObservationData($id);
    $data['main_content']    = 'staff/observation/edit';
    $this->load->view('inc/template', $data);
  }

  public function UpdateObservation($id){
    if (!$this->authorization->isAuthorized('STAFF_OBSERVATION.CREATE')) {
      redirect('dashboard');
    }  
    $status = (int) $this->Staff_Model->updateObservation($id);
    if($status){
      $this->session->set_flashdata('flashSuccess', 'Successfully Updated Observation.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('staff/observation/');
  }

  public function deleteObservation($id){
    if (!$this->authorization->isAuthorized('STAFF_OBSERVATION.CREATE')) {
      redirect('dashboard');
    }  
    $status = (int) $this->Staff_Model->deleteObservation($id);
    if($status){
      $this->session->set_flashdata('flashSuccess', 'Successfully Deleted Observation.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('staff/observation/');
  }

  public function viewObservations($staffId){
    if (!$this->authorization->isAuthorized('STAFF_OBSERVATION.VIEW_SUMMARY')) {
      redirect('dashboard');
    }  
    $observed = $this->Staff_Model->getStaffObservation($staffId);
    $staff = $this->staffcache->getStaffCache();
    $data['staff'] = 0;
    if(!empty($staff)){
      $data['staff'] = $staff->staffId;
    }
    $data['staffData'] = $this->Staff_Model->getStaff($staffId);
    // echo "<pre>";print_r($data['staffData']);die();
    $data['staffId'] = $staffId;
    $obData = array();
    foreach ($observed as $value){
      $obData[$value->takenBy][] = array('date'=>date('d-m-Y', strtotime($value->created_on)), 'obs' => $value->observation);
    }
    //echo "<pre>";print_r($obData);die();
    $data['obData'] = $obData;
    $data['staff_name'] = $this->Staff_Model->getStaffName($staffId);
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
    $data['main_content']    = 'staff/staff_registration/more/observations';
    $this->load->view('inc/template', $data);
  }

  // public function observations(){
  //   $data['student_uid'] = $this->uri->segment(4);
  //   $data['selectedClassId'] = $this->uri->segment(5);
  //   $data['stdData'] = $this->Staff_Model->getStdData($data['student_uid']);
  //   $observed = $this->Student_Model->observations($data['student_uid']);
  //   $obData = array();
  //   foreach ($observed as $value){
  //     $obData[$value->staffName][] = array('date'=>date('d-m-Y', strtotime($value->created_on)), 'obs' => $value->observation);
  //   }
  //   //echo "<pre>";print_r($data['obData']);die();
  //   $data['obData'] = $obData;
  //   $data['main_content'] = 'student/student_registration/more/observations';
  //   $this->load->view('inc/template', $data);
  // }

  public function filterObservations(){
    $staff = $this->staffcache->getStaffCache();
    $data['staff'] = 0;
    if(!empty($staff)){
      $data['staff'] = $staff->staffId;
    }
    $viewPrivilege = $this->input->get('otype');
    $data['otype'] = $viewPrivilege;
     if($viewPrivilege == 0){
      //Can view only logged-in users' observations.
      if (!$this->authorization->isAuthorized('STAFF_OBSERVATION.VIEW')) {
        redirect('dashboard');
      }  
      $data['observed'] = $this->Staff_Model->filterObservedData($data['staff']);
    } else {
      //Can view all observations.
      if (!$this->authorization->isAuthorized('STAFF_OBSERVATION.VIEW_ALL')) {
        redirect('dashboard');
      }  
      $data['observed'] = $this->Staff_Model->filterObservedData(0);
    }
    $data['from_date'] = $this->input->get('from_date');
    $data['to_date'] = $this->input->get('to_date');
    $data['main_content']    = 'staff/observation/index';
    $this->load->view('inc/template', $data);
  }

  function getObjervationTemplate(){
    $templateName = $this->input->post('templateName');
    echo json_encode($this->__getTemplateByName($templateName));
  }
  private function __getTemplateByName ($templateName) {
    $templateValue = '';
    foreach ($this->templates as $temp) {
      if ($temp['name'] == $templateName) {
        $templateValue = $temp['value'];
        break;
      }
    }
    return $templateValue;
  }

  public function s3FileUpload($file,$folder_name = 'student_observation') {
    if ($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }
    $this->load->library('filemanager');
    return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
  }

}