<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  12 March 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Fee_standard extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('FEES_NH')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('fees/fee_transaction_standard');
  }

  //Landing function for Fee Transaction
  public function index ($classId = '') {
    $feeConcession = $this->authorization->isAuthorized('FEE_TRANSACTION_NH.PROVIDE_CONCESSION');
    $feeCollection = $this->authorization->isAuthorized('FEE_TRANSACTION_NH.COLLECT');
    $data['feeConcession'] = $feeConcession;
    $data['feeCollection'] = $feeCollection;

    // $classId = $this->input->post('stud_class');

    // if (empty($classId)) {
    //   $classId = 0; //0 signifies show all classes
    // } else {
    //   if ($classId == 'All') {
    //     $classId = 0; //0 signifies show all classes
    //   }
    // }

     if (empty($classId)){
        $classId = $this->input->post('stud_class');
      }
      if (empty($classId)){
        $classId = 1; 
      }

    $data['selectedClassId'] = $classId;  
    $data['clasess'] = $this->fee_transaction_standard->get_all_class();
    // student data
    $std_data = $this->fee_transaction_standard->get_classwise_student_data($classId);

      // check student wise record either pay or not 
    $fees_status = $this->fee_transaction_standard->check_fee_studentwise_status();
      
      // check student reconsilation
    $rec_status = $this->fee_transaction_standard->get_fee_transction_installment();

    foreach ($std_data as $key => $std) {
        $st_data[$std->id] = $std;
    }

    foreach ($rec_status as $key => $recStatus) {
        $rec_data[$recStatus->student_id] = $recStatus;
    }

    foreach ($fees_status as $key => $feeStatus) {
        $fee_status[$feeStatus->stdId] = $feeStatus;
    }
    if (!empty($st_data)) {
       foreach ($st_data as $k => &$st) {
        $st = (object) array_merge((array)$st, array('rec_status'=>isset($rec_data[$k]->reconciliation_status) ? $rec_data[$k]->reconciliation_status : null ));
        $st = (object) array_merge((array)$st, array('amount_paid'=>isset($rec_data[$k]->amount_paid) ? $rec_data[$k]->amount_paid : null ));
        $st = (object) array_merge((array)$st, array('receipt_number'=>isset($rec_data[$k]->receipt_number) ? $rec_data[$k]->receipt_number : null ));
        $st = (object) array_merge((array)$st, array('fee_status'=>isset($fee_status[$k]->status) ? $fee_status[$k]->status : null ));
      }
      $data['std_search'] = $st_data;
    }
     
    $data['main_content'] = 'fees/standard/fee_index';
    $this->load->view('inc/template', $data);

  }

  // Concession related set authorized 
  public function concession($std_id,$traverse_to,$classId){

    $feeConfig = $this->settings->getSetting('fees');
    $data['alloc_typeforConce'] = $feeConfig['cComponent_allocation_type_auto'];
    $data['discount_per'] = $feeConfig['discount'];
    $data['traverse_to'] = $traverse_to;
    $data['selectedClassId'] = $classId;
     // fetch data from selected student Id;
    $data['std_details'] = $this->fee_transaction_standard->get_student_detail($std_id);
    //echo '<pre>';print_r($data['fee_details']);die();

    // studetn dataArry pass the  filter_criteria 
    $filter = $this->fee_library->construct_filter($data['std_details']);
    
    // fetch fees master table according to filter criteria
    $fee_component = $this->fee_transaction_standard->get_feemasterFilter($filter);

     if (!empty($fee_component)) {
     $data['fee_master_id'] = $fee_component[0]->id;
     $data['fee_master_isFixed'] = $fee_component[0]->is_fixed;
     $data['noOfComponents'] = count($fee_component);
    }
    if (!empty($fee_component)) {
      // get if already paid 
      $previous_paid = $this->fee_transaction_standard->get_previousPaidBystdId($std_id);
      
      // get if cocession given 
      $previous_conc = $this->fee_transaction_standard->get_previousConcessionBystdId($std_id);
      
      // get if currenct cocession 
      $currenct_conc = $this->fee_transaction_standard->get_curretConcessionBystdId($std_id);
      
      foreach ($fee_component as $key => $component) {
        $mData[$component->fcm_id] = $component;
      }

      $ppData=array();
      foreach ($previous_paid as $key => $paid) {
        $ppData[$paid->fee_component_id] = $paid;
      }
    
      $pcData=array();
      foreach ($previous_conc as $key => $pConc) {
        $pcData[$pConc->fee_component_id] = $pConc;
      }

      $ccData=array();
      foreach ($currenct_conc as $key => $cConc) {
        $ccData[$cConc->fee_component_id] = $cConc;
      }

      foreach ($mData as $k => &$fc) {
        $fc = (object) array_merge((array)$fc, array('pAmount'=>isset($ppData[$k]->collected_amount) ? $ppData[$k]->collected_amount : null ));
        $fc = (object) array_merge((array)$fc, array('pConc'=> isset($pcData[$k]->receiveConcession) ? $pcData[$k]->receiveConcession : null ));
        $fc = (object) array_merge((array)$fc, array('cConc'=> isset($ccData[$k]->currentConcession) ? $ccData[$k]->currentConcession : null ));
      }

      $data['fee_component'] = $mData;

      $data['main_content'] = 'fees/standard/fee_concession/index';
      $this->load->view('inc/template', $data);
    } else {
      //We shouldn't reach this point as the structure should take care of all combinations. If we reach, this is a bug!!
      $this->session->set_flashdata('flashError', 'No matching Fee Structure exists for this student. Please contact your administrator with the Student Admission number');
      redirect('fees/fee_standard');
    }
  }

  public function submit_fee_concession($std_id, $fee_master_id,$classId){
    $traverse_to = $this->input->post('traverse_to');
    
    $result = $this->fee_transaction_standard->insert_concession($std_id, $fee_master_id);
    if($result){ 
      $this->session->set_flashdata('flashSuccess', 'Concession Applied Successfully');
    }
    else{
      $this->session->set_flashdata('flashError', 'No changes');
    }

    //Navigate back to the appropriate page
    if ($traverse_to == '0') {
      redirect('fees/fee_standard/index/'.$classId);
    }else{
      redirect('fees/fee_standard/select_fee/index/'.$std_id.'/'.$classId);
    }
  }

  // fee payment related
  // student wise fetch fee component record 
  public function select_fee($std_id,$classId){
    // get fee modes from config
    $feeConfig = $this->settings->getSetting('fees');
    $data['payment_modes'] = $feeConfig['allowed_payment_modes'];
    $data['alloc_type'] = $feeConfig['component_allocation_type_auto'];
    $data['discount_per'] = $feeConfig['discount'];
    $data['criteria'] = $feeConfig['filter_criteria'];
    $data['selectedClassId'] = $classId;
    // fetch data from selected student Id;
    $data['std_details'] = $this->fee_transaction_standard->get_student_detail($std_id);
    //echo "<pre>"; print_r($data['criteria']); die();
    
    // student dataArry pass the  filter_criteria 
    $filter = $this->fee_library->construct_filter($data['std_details']);

    // fetch fees component table according to filter criteria
    $fee_component = $this->fee_transaction_standard->get_feemasterFilter($filter);
    
    if (!empty($fee_component)) {
     $data['fee_master_id'] = $fee_component[0]->id;
     $data['fee_master_isFixed'] = $fee_component[0]->is_fixed;
     $data['noOfComponents'] = count($fee_component);
    }
    // get if already paid 
    $previous_paid = $this->fee_transaction_standard->get_previousPaidBystdId($std_id);
    
    // get if cocession given 
    $previous_conc = $this->fee_transaction_standard->get_previousConcessionBystdId($std_id);
    
    // get if currenct cocession 
    $currenct_conc = $this->fee_transaction_standard->get_curretConcessionBystdId($std_id);

    foreach ($fee_component as $key => $component) {
      $mData[$component->fcm_id] = $component;
    }
    $ppData=array();
    foreach ($previous_paid as $key => $paid) {
        $ppData[$paid->fee_component_id] = $paid;
      }
  
    $pcData=array();
    foreach ($previous_conc as $key => $pConc) {
      $pcData[$pConc->fee_component_id] = $pConc;
    }

    $ccData=array();
    foreach ($currenct_conc as $key => $cConc) {
      $ccData[$cConc->fee_component_id] = $cConc;
    }
    if (!empty($mData)) {
      foreach ($mData as $k => &$fc) {
      
      $fc = (object) array_merge((array)$fc, array('pAmount'=>isset($ppData[$k]->collected_amount) ? $ppData[$k]->collected_amount : null ));
      $fc = (object) array_merge((array)$fc, array('pConc'=> isset($pcData[$k]->receiveConcession) ? $pcData[$k]->receiveConcession : null ));
      $fc = (object) array_merge((array)$fc, array('cConc'=> isset($ccData[$k]->currentConcession) ? $ccData[$k]->currentConcession : null ));
      }

      $data['fee_component'] = $mData;
    }
 //echo "<pre>"; print_r($data['fee_component']); die();
    $data['main_content'] = 'fees/standard/transaction/index';
    $this->load->view('inc/template', $data);
  }

// submit fee
  public function submit_fee_transaction($std_id,$classId) {

    $fee_master_id = $this->input->post('fee_master_id');
    // check for amount paid and total balance  
    $feeConfig = $this->settings->getSetting('fees');
    $payment_modes = $feeConfig['allowed_payment_modes'];
    $isApDiscount = $feeConfig['discount'];
    $total_balance = (int) $this->input->post('total_balance');
    $amount_paid = (int) $this->input->post('amount_paid');
    if($this->input->post('final_amount')!='0'){
      if ($amount_paid <= $total_balance ) {
        //Update the fee tables...
        $this->db->trans_begin();
        $fee_id = $this->fee_transaction_standard->std_fee_collection($std_id,$fee_master_id,$payment_modes,$isApDiscount);
        if($fee_id == 0){
          $this->db->trans_rollback();
          $this->session->set_flashdata('flashError', 'Something went wrong');
          redirect('fees/fee_standard/select_fee/'.$std_id); 
        }else{
          $receipt_number = $this->fee_recipt($fee_id);
          $receipt_no = $this->fee_transaction_standard->update_receipt_number($fee_id,$receipt_number);
          if (empty($receipt_no)) {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something went wrong');
            redirect('fees/fee_standard/select_fee/'.$std_id); 
          }
          if ($this->db->trans_status()) {
              $result = $this->db->trans_commit();
              if ($result) {
                redirect('fees/fee_standard/print_receipt/'.$fee_id.'/'.$classId);
              }
          } else {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashSuccess', 'Something went wrong');
            redirect('fees/fee_standard/select_fee/'.$std_id); 
          }
        }
      
      }else{
        $this->session->set_flashdata('flashError', 'Please check. final amount');
        redirect('fees/fee_standard/select_fee/'.$std_id);
      }
   
    }
  }
  public function fee_recipt($fee_id){
    //Generate the Fee Receipt Number

    //Get config data
    $feeConfig = $this->settings->getSetting('fees');
    $receipt_number_gen = $feeConfig['recipt_number_gen'];
    
    $rTemplate = $feeConfig['receipt_template'];

    //get last id fee transcation installment generate receipt no
    switch ($receipt_number_gen['fee_generation_algo']) {
      case 'WPL':
        $receipt_number = $receipt_number_gen['infix'].sprintf("%'.0".$receipt_number_gen['digit_count']."d\n", $fee_id);
        break;
      case 'NHS':
        $receipt_number = $receipt_number_gen['infix'].sprintf("%'.0".$receipt_number_gen['digit_count']."d\n", $fee_id);
        break;
      default:
        $receipt_number = $fee_id;
        break;
    }
    return $receipt_number;
  }

  // Reconsilation Fee 

  public function reconsilation_fee() {
   $rec_paid = $this->fee_transaction_standard->update_recon_status_as_done();
   if ($rec_paid) {
     // echo "1";
      $this->session->set_flashdata('flashSuccess', 'Reconciliation Successful');
    }else{
      // echo "2";
      $this->session->set_flashdata('flashError', 'Something went wrong');
    } 
  }


  public function print_receipt($fee_id,$classId){
    $feeConfig = $this->settings->getSetting('fees');
    $rTemplate = $feeConfig['receipt_template'];
    $data['paymentType'] = $feeConfig['allowed_payment_modes'];
    $data['fee_details']= $this->fee_transaction_standard->get_feedetailsbyfee_id($fee_id);
    $data['selectedClassId'] = $classId;
    // $this->load->library('pdfmanager');
    // $file = $this->pdfmanager->generatePdf($data,'exportPdf');
    // $this->load->library('filemanager');
    // $this->filemanager->uploadFile($file['path'],$file['filename']); 

    // $file_path = $this->filemanager->getFilePath($file['filename']); 
    // $this->fee_transaction_standard->update_pdfpath_url($fee_id,$file_path);
   
    // $this->session->set_flashdata('flashSuccess', 'Fee Successfully added');
    $data['main_content'] = 'receipts/'.$rTemplate;
    $this->load->view('inc/template', $data);
  }
}

