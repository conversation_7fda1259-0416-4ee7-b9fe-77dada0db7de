<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Avatars extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			// redirect them to the login page
			redirect('auth/login', 'refresh');
		}
		$this->load->library('filemanager');
		$this->load->model('avatar');
	}

	/**
	 * Landing function called from login box.
	 */
	public function index() {
		//Manjukiran 28-7-2021: Commenting out for now to reduce write load on server.
		// $this->db->where('id', session_id());
		// $this->db->update('ci_sessions',array('user_id'=>$this->ion_auth->get_user_id()));

		//Load username for the mobile client to parse and know (required for notifications)
		$data['username'] = $this->authorization->getUsername();
		//Load current year into session
		$this->acad_year->loadAcadYearDataToSession();
		
		//Load the Parents avatar only if parent_module is enabled
		if ($this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
			$loadParentAvatar = 1;
		} else {
			$loadParentAvatar = 0;
		}

		//Collect all the avatars for the logged in user
		$avatars = $this->avatar->getAvatarDataForLoggedInUser($this->ion_auth->get_user_id(), $loadParentAvatar);
		// trigger_error("Avatar: " . json_encode($avatars));		
		if (empty($avatars)) {
			//TODO: This flash data doesn't come up. Need to check why.
			// session_destroy();
			$this->session->sess_destroy();
			delete_cookie('ci_session');
			$this->session->set_flashdata('flashInfo', 'There is no data associated with this login id. Contact School administrator');
			// $this->ion_auth->logout();

			redirect('auth/login', 'refresh');
		}
		$this->session->set_userdata('avatar_count', count($avatars));

		switch (count($avatars)) {
			case 0:
				$data['avatars'] = '';
				$this->load->view('auth/switch_avatar', $data);
				break;
			case 1:
				$selectedAvatar = $avatars[0];

				//Loading the avatar into the session
				$this->authorization->loadAvatar($selectedAvatar);
				// trigger_error(json_encode($this->session));

				//Loading the privileges into the session if it is staff
				if ($selectedAvatar->avatarType == 4) { //avatarType of Staff is 4
					//$privileges = $this->avatar->getPrivilegesForAvatarId($selectedAvatar->avatarId);
					//$this->authorization->loadPrivileges($privileges);

					$staffCache = $this->avatar->makeStaffCache($selectedAvatar->avatarId);
					$this->staffcache->setStaffCache($staffCache);
				}
				if ($selectedAvatar->avatarType == 2) {
					//Avatartype of Parent is 2
					$parentCache = $this->avatar->makeParentCache($selectedAvatar->avatarId);
					if (empty($parentCache)) {
						redirect('dashboard/parent_no_avatar');
					}
					
					if ($parentCache->admission_status != 2 && $parentCache->admission_status != 1) {
						redirect('dashboard/in_active_student');
					}

					/** 
					 * This is required to find out if the student is a next year student.
					 * For next year student, we show only FEES functionality and hide the others from parents.         
					 *  
					*/ 
					$parentCache->isNextYearStudent = $this->avatar->getIsNextYearStudent($parentCache->id);
					
					//If the student belongs to next year, then refresh the parent cache with information from next year.
					if ($parentCache->isNextYearStudent) {
						$acad_year_id = $parentCache->admission_acad_year_id;

						$studentRecord = $this->db->select('sa.*, sy.id as stdyearId, sy.acad_year_id, sy.picture_url, sy.class_section_id, sy.class_id, cs.class_name as className, cs.section_name as sectionName')
						->from('student_admission sa')
						->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=" . $acad_year_id)
						->where("sa.id in (select p.student_id from parent p left join avatar a on a.stakeholder_id=p.id where a.id=".$selectedAvatar->avatarId." )")
						->join('class_section cs','sy.class_section_id=cs.id','left')
						->get()->row();

						$parentCache = $studentRecord;
						$parentCache->isNextYearStudent = 1;
					}

					$this->parentcache->setParentCache($parentCache);
				}
				if ($selectedAvatar->avatarType == 5) {
					//Avatartype of driver is 5
					$driverCache = $this->avatar->makeDriverCache($selectedAvatar->avatarId);
					$this->drivercache->setDriverCache($driverCache);
				}

				redirect('dashboard');
				break;
			default:
				$data['sibilings'] = $this->avatar->getSibling_data($avatars);
				$data['avatars'] = $avatars;
				$this->load->view('auth/switch_avatar', $data);
				break;
		}
	}

	//Landing function from Switch Avatar Screen
	public function loadAvatar ($avatarId) {
		$selectedAvatar = $this->avatar->getAvatarById ($avatarId);

		//Loading the avatar into the session
		$this->authorization->loadAvatar($selectedAvatar);

		//Unset any old privileges
		$this->authorization->unsetPrivileges();

		//Unset any old parent cache
		$this->parentcache->unsetParentCache();
		
		//Loading the privileges into the session if it is staff
		if ($selectedAvatar->avatarType == 4) { //avatarType of Staff is 4
			// $privileges = $this->avatar->getPrivilegesForAvatarId($selectedAvatar->avatarId);
			// $this->authorization->loadPrivileges($privileges);

			$staffCache = $this->avatar->makeStaffCache($selectedAvatar->avatarId);
			$this->staffcache->setStaffCache($staffCache);
		}

		if ($selectedAvatar->avatarType == 2) {
			//Avatartype of Parent is 2
			$parentCache = $this->avatar->makeParentCache($selectedAvatar->avatarId);

			if (empty($parentCache)) {
				redirect('dashboard/parent_no_avatar');
			}
			
			if ($parentCache->admission_status != 2 && $parentCache->admission_status != 1) {
				redirect('previous_year_data');
			}
			/** 
			 * This is required to find out if the student is a next year student.
			 * For next year student, we show only FEES functionality and hide the others from parents.         
			 *  
			*/
			$parentCache->isNextYearStudent = $this->avatar->getIsNextYearStudent($parentCache->id);

			//If the student belongs to next year, then refresh the parent cache with information from next year.
			if ($parentCache->isNextYearStudent) {
				$acad_year_id = $parentCache->admission_acad_year_id;

				$studentRecord = $this->db->select('sa.*, sy.id as stdyearId, sy.acad_year_id, sy.picture_url, sy.class_section_id, sy.class_id, cs.class_name as className, cs.section_name as sectionName')
				->from('student_admission sa')
				->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=" . $acad_year_id)
				->where("sa.id in (select p.student_id from parent p left join avatar a on a.stakeholder_id=p.id where a.id=".$selectedAvatar->avatarId." )")
				->join('class_section cs','sy.class_section_id=cs.id','left')
				->get()->row();

				$parentCache = $studentRecord;
				$parentCache->isNextYearStudent = 1;
			}
			
			$this->parentcache->setParentCache($parentCache);
		}
		if ($selectedAvatar->avatarType == 5) {
			//Avatartype of driver is 5
			$driverCache = $this->avatar->makeDriverCache($selectedAvatar->avatarId);
			$this->drivercache->setDriverCache($driverCache);
		}
		redirect('dashboard');
	}
}
