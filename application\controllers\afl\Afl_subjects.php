<?php
class Afl_subjects extends CI_Controller {
    private $yearId;
    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
       redirect('auth/login', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('afl/Afl_subjects_model','subjects_model');
      $this->load->model('Class_section', 'class_section');
    }

  public function index($class_id=0){
    $data['is_exam_admin'] = $this->authorization->isAuthorized('AFL.ADMIN');
    $data['class_id'] = $class_id;
    $data['classess'] = $this->class_section->getAllClassess();
    if($class_id == 0) {
      $class_id = $data['classess'][0]->id;
    }
    $data['sections'] = $this->class_section->getSectionsByClassId($class_id);
    $data['subjects'] = $this->subjects_model->getSubjectsByClass($class_id);
    $data['subjectList'] = $this->subjects_model->getSubjectsListByClass($class_id);
    $data['staffList'] = $this->subjects_model->getAllStaffWithNoPermission($class_id);
    $data['subjectPermissions'] = $this->subjects_model->getSubjectPermissions($class_id);
    $data['subject_names'] = $this->subjects_model->getSubjectNames();
    $data['strand_names'] = $this->subjects_model->getStrandNames();
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'afl/subjects/index';
    $this->load->view('inc/template', $data);
  }

  public function addSubjects() {
    $input = $this->input->post();
    // echo '<pre>'; print_r($input); die();
    $status = $this->subjects_model->addSubjects();
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Subject Added Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_subjects/index/'.$input['class_id']);
  }

  public function editSubject() {
    $subject_id = $_POST['subject_id'];
    $class_id = $_POST['class_id'];
    $subject_name = $_POST['subject_name'];
    $status = $this->subjects_model->editSubjects($subject_id, $subject_name);
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Subject name updated Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_subjects/index/'.$class_id);
  }

  public function deleteSubject() {
    $subject_id = $_POST['subject_id'];
    $status = $this->subjects_model->deleteSubject($subject_id);
    echo $status;
  }

  public function changeStrandStatus() {
    $strand_id = $_POST['strand_id'];
    $status = $_POST['status'];
    $status = $this->subjects_model->changeStrandStatus($strand_id, $status);
    echo $status;
  }

  public function addSubjectStrands() {
    $input = $this->input->post();
    // echo '<pre>'; print_r($input); die();
    $status = $this->subjects_model->addSubjectStrands();
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Subject Strands Added Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_subjects/index/'.$input['class_id']);
  }

  public function addSubStrands() {
    $input = $this->input->post();
    // echo '<pre>'; print_r($input); die();
    $status = $this->subjects_model->addSubStrands();
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Sub-Strands Added Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_subjects/index/'.$input['class_id']);
  }

  public function updateStrand() {
    $status = $this->subjects_model->updateStrand();
    echo $status;
  }

  public function updateSubStrand() {
    $status = $this->subjects_model->updateSubStrand();
    echo $status;
  }

  public function deleteSubStrand() {
    $sub_strand_id = $_POST['sub_strand_id'];
    $status = $this->subjects_model->deleteSubStrand($sub_strand_id);
    echo $status;
  }

  public function addTopics() {
    $input = $this->input->post();
    // echo '<pre>'; print_r($input); die();
    $status = $this->subjects_model->addTopics();
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Topics Added Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_subjects/index/'.$input['class_id']);
  }

  public function getSubStrandTopics() {
    $sub_strand_id = $_POST['sub_strand_id'];
    $topics = $this->subjects_model->getSubStrandTopics($sub_strand_id);
    echo json_encode($topics);
  }

  public function deleteTopic() {
    $topic_id = $_POST['topic_id'];
    $status = $this->subjects_model->deleteTopic($topic_id);
    echo $status;
  }

  public function addAccessControl() {
    $input = $this->input->post();
    // echo "<pre>"; print_r($input); die();
    $status = $this->subjects_model->addAccessControl();
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Access Control Added Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    }
    redirect('afl/afl_subjects/index/'.$input['class_id']);
  }

}