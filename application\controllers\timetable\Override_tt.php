<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  15 March 2018
 *
 * Description: Controller class for Subjects
 *
 * Requirements: PHP5 or above
 *
 */
class Override_tt extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('OVERRIDE_TIMETABLE')) {
      redirect('dashboard', 'refresh');
    }  
    $this->load->model('timetable/Override_tt_model', 'ott');
    $this->load->model('timetable/Sss_allocation_model', 'sss_model');
    $this->load->model('timetable/timetable', 'timetable');
    $this->load->model('timetable/Substitution_dd_model', 'substitution_model');
  }

  /* Landing function for creating a new virtual timetable */
  public function index() {
    $data['ottList'] = $this->ott->getOTTList();
    $data['main_content'] = 'timetable/override/override_master/index';
    $this->load->view('inc/template', $data);
  }

  public function addNewOTT() {
    $data['classList'] = $this->ott->getClassList();
    $data['main_content'] = 'timetable/override/override_master/add';
    $this->load->view('inc/template', $data);
  }

  public function submitNewOTT() {
    $result = $this->ott->addNewOTT();

    if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Override timetable inserted successfully');
    } else {
        $this->session->set_flashdata('flashError', 'Something went wrong...');
    }
    redirect('timetable/override_tt/index');
  }

  public function constructOTT($toId) {
    $data['ottObj'] = $this->ott->getOTTDetailsById($toId);
    $data['classList'] = $this->ott->getClassListByToId($toId);
    $data['main_content'] = 'timetable/override/construct_tt/index';
    $this->load->view('inc/template', $data);
  }

  //Ajax call
  public function getOTTForClass() {
    $classId = $_POST['classId'];
    $toId = $_POST['toId'];
    $date = $_POST['date'];
    $weekDay = date("w", strtotime($date));

    $data['overrideTT'] = $this->ott->getOverrideTT($classId, $toId);
    $data['overrideTT_json'] = json_encode($data['overrideTT']);
    $data['ttHeader'] = $this->ott->getPeriodTemplate($classId, $weekDay);

    echo json_encode($data);
  }

  public function getSectionList() {
    $classId = $_POST['classId'];
    $data['sectionList'] = $this->ott->getSectionListByClassId($classId);
    echo json_encode($data);
  }

  public function getSubjectList() {
    $sectionId = $_POST['sectionId'];
    $data['subjectList'] = $this->sss_model->getSSSDetails($sectionId);
    echo json_encode($data);
  }

  public function getRegularSectionTT() {    
    $classId = $_POST['classId'];
    $weekDay = $_POST['weekDay'];
    // $rDate = date('Y-m-d',strtotime($date));
    // $weekDay = date("w", strtotime($rDate));

    $sectionList = $this->ott->getSectionListByClassId($classId);
    $data['ttHeader'] = $this->ott->getPeriodTemplate($classId, $weekDay);
    $data['weekDay'] = $weekDay;

    $sectionTTList = array();
    foreach ($sectionList as $csObj) {
      $temp = new stdClass();
      $temp->csId = $csObj->csId;
      $temp->csName = $this->timetable->getSectionName($csObj->csId);
      $temp->tta = $this->substitution_model->getSectionTTByWeekDay($csObj->csId, $weekDay);
      $sectionTTList[] = $temp;
    }
    $data['sectionTTList'] = $sectionTTList;
    $data['sectionTTList_json'] = json_encode($data['sectionTTList']);
    echo json_encode($data);
  }

  public function copyPeriods() {
    // $classId = $_POST['classId'];
    // $weekDay = $_POST['weekDay'];
    // $date = $_POST['date'];
    // $toId = $_POST['toId'];

    // $rDate = date('Y-m-d',strtotime($date));

    $sectionTTList = json_decode($_POST['sectionTTList_json']);
    $overrideTT = json_decode($_POST['overrideTT_json']);

    // echo '<pre>';print_r($sectionTTList_json);
    // echo '<pre>';print_r($overrideTT_json);die();

    echo $this->ott->copyPeriods($sectionTTList, $overrideTT);

    if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Override timetable inserted successfully');
    } else {
        $this->session->set_flashdata('flashError', 'Something went wrong...');
    }
    redirect('timetable/override_tt/index');
  }
}
