<?php
  /**
   * Name:    Oxygen
   * Author:  <PERSON>
   *          <EMAIL>
   *
   * Created:  17 May 2018
   *
   * Description:  
   *
   * Requirements: PHP5 or above
   *
   */

  defined('BASEPATH') OR exit('No direct script access allowed');

  /**
   * Class Fee
   * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
   * @property CI_Form_validation      $form_validation The form validation library
   */
  class Openvidu_controller extends CI_Controller {

    public function __construct() {
      parent::__construct();
      $this->load->library('openvidu');
      $this->load->model('virtual_classroom/openvidu_model');
    }

    public function insertData(){
        $json_data = file_get_contents('php://input');
        // $data =(array) json_decode(file_get_contents('php://input'),TRUE);
        // $return = $this->openvidu_model->insertData($data,$json_data);
        $data = $this->openvidu_model->insertData1($json_data);
    }

    public function getData(){
      $data['openvidu_data'] = $this->openvidu_model->getData();
      $data['main_content'] = 'virtual_classroom/openvidu_view';
      $this->load->view('inc/template', $data);
    }
  }

?>