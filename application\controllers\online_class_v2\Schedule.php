<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Schedule extends CI_Controller {
    private $yearId;
    public function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('ONLINE_CLASS_V2') || !$this->authorization->isAuthorized('ONLINE_CLASS_V2.MODULE')) {
            redirect('dashboard', 'refresh');
        }
        $this->yearId = $this->acad_year->getAcadYearId();
        $this->load->library('staff_getter');
        $this->load->model('online_class_v2/Schedule_model', 'schedule');
        $this->load->model('class_section');
    }

    public function index() {
        $data['is_admin'] = $this->authorization->isAuthorized('ONLINE_CLASS_V2.ADMIN');
        $data['permit_create_schedule'] = $this->authorization->isAuthorized('ONLINE_CLASS_V2.CREATE_SCHEDULE');
        $data['staff_list'] = $this->staff_getter->staff_list($status=2);
        $data['sections'] = $this->class_section->getAllClassSections();
        $data['subjects'] = $this->schedule->getSubjects();
        $data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
        $data['is_mobile'] = 0;
        if($this->mobile_detect->isMobile()) {
          $data['is_mobile'] = 1;
        }
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'online_class_v2/schedules_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'online_class_v2/schedules_mobile';
        }else{
          $data['main_content'] = 'online_class_v2/schedules';     	
        }

        
        $this->load->view('inc/template', $data);
    }

    public function get_schedules() {
        $schedule_date = date('Y-m-d', strtotime($_POST['schedule_date']));
        $host_id = $_POST['host_id'];
        $class_section_id = $_POST['class_section_id'];
        $subject_name = $_POST['subject_name'];
        $data['schedules'] = $this->schedule->get_schedules($schedule_date, $host_id, $class_section_id, $subject_name);
        echo json_encode($data);
    }

    public function new_schedule() {
        if(!$this->authorization->isAuthorized('ONLINE_CLASS_V2.CREATE_SCHEDULE')) {
            redirect('dashboard', 'refresh');
        }
        $data['is_admin'] = $this->authorization->isAuthorized('ONLINE_CLASS_V2.ADMIN');
        $data['class_duration'] = 60;
        $config_duration = $this->settings->getSetting('online_class_duration');
        if($config_duration) {
          $data['class_duration'] = (int)$config_duration;
        }
        $data['creator_id'] = $this->authorization->getAvatarStakeHolderId();
        $data['staff_list'] = $this->staff_getter->staff_list($status=2);
        $data['subjects'] = $this->schedule->getSubjects();
        // echo "<pre>"; print_r($data); die();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'online_class_v2/new_schedule_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'online_class_v2/new_schedule_mobile';
        }else{
          $data['main_content'] = 'online_class_v2/new_schedule';  	
        }

        
        $this->load->view('inc/template', $data);
    }

    public function edit_schedule() {
      $data['schedule_id'] = $_POST['schedule_id'];
      if(!$this->authorization->isAuthorized('ONLINE_CLASS_V2.CREATE_SCHEDULE')) {
        redirect('dashboard', 'refresh');
      }
      $data['class_duration'] = 60;
      $config_duration = $this->settings->getSetting('online_class_duration');
      if($config_duration) {
        $data['class_duration'] = (int)$config_duration;
      }
      $data['schedule'] = $this->schedule->getSchedule($data['schedule_id']);
      $schedule_date = date("Y-m-d", strtotime($data['schedule']->schedule_date));
      $data['allotted_host_schedules'] = $this->schedule->getAllottedSchedules($data['schedule']->host_id, $schedule_date, [$data['schedule_id']]);
      $data['allotted_participant_schedules'] = $this->schedule->getAllottedParticipantSchedules($data['schedule_id'], $schedule_date);
      // echo "<pre>"; print_r($data); die();
      $data['main_content'] = 'online_class_v2/edit_schedule.php';
      $this->load->view('inc/template', $data);
    }

    public function save_schedule() {
        $input = array();
        $input['host_id'] = $_POST['host_id'];
        $input['name'] = $_POST['name'];
        $input['description'] = $_POST['description'];
        $input['meeting_link'] = trim($_POST['meeting_link']);
        $input['subject_name'] = $_POST['subject_name'];
        $input['schedule_date'] = date('Y-m-d', strtotime($_POST['schedule_date']));
        $input['start_time'] = gmt_time($_POST['schedule_date'].' '.$_POST['start_time'], 'Y-m-d H:i:s');
        $input['end_time'] = gmt_time($_POST['schedule_date'].' '.$_POST['end_time'], 'Y-m-d H:i:s');
        $input['created_by'] = $input['last_modified_by'] = $this->authorization->getAvatarStakeHolderId();
        // echo '<pre>'; print_r($input); die();
        $status = $this->schedule->save_schedule($input);
        if($status) {
            $this->session->set_flashdata('flashSuccess', 'Schedule is saved');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong!');
        }
        redirect('online_class_v2/schedule/new_schedule');
    }

    public function update_schedule() {
      $input = array();
      $schedule_id = $_POST['schedule_id'];
      $input['name'] = $_POST['name'];
      $input['description'] = $_POST['description'];
      $input['meeting_link'] = $_POST['meeting_link'];
      $schedule_date = date('Y-m-d', strtotime($_POST['schedule_date']));
      $input['start_time'] = gmt_time($schedule_date.' '.$_POST['start_time'], 'Y-m-d H:i:s');
      $input['end_time'] = gmt_time($schedule_date.' '.$_POST['end_time'], 'Y-m-d H:i:s');
      $status = $this->schedule->update_schedule($schedule_id, $input);
        if($status) {
          $schedule = $this->schedule->getSchedule($schedule_id);
          $participants = $this->schedule->getScheduleParticipantsData($schedule_id);
          $notify_array = [];
          foreach ($participants as $k => $part) {
            if($part['stakeholder_type'] == 2) {
              $notify_array['staff_ids'] = $part['stakeholder_id'];
            } else {
              $notify_array['student_ids'] = $part['stakeholder_id'];
            }
          }
          $notify_array['message'] = $schedule->name.' class is rescheduled, new timings '.local_time($schedule->un_start_time, 'd-M h:i a').' to '.local_time($schedule->un_end_time, 'd-M h:i a');
          $notify_array['staff_ids'][] = $schedule->host_id;
          $this->_sendNotification($schedule_id, $notify_array);
            $this->session->set_flashdata('flashSuccess', 'Schedule is updated');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong!');
        }
      redirect('online_class_v2/schedule');
      // echo '<pre>'; print_r($input); die();
    }

    public function add_participants($schedule_id) {
        $data['schedule'] = $this->schedule->getSchedule($schedule_id);
        $data['host_id'] = $this->authorization->getAvatarStakeHolderId();
        $data['is_admin'] = $this->authorization->isAuthorized('ONLINE_CLASS_V2.ADMIN');
        if($data['schedule']->host_id != $data['host_id'] && !$data['is_admin']) {
            redirect('online_class_v2/schedule');
        }
        $data['staff_list'] = $this->staff_getter->staff_list($status=2);
        $data['sections'] = $this->class_section->getAllClassSections();
        $data['schedule_id'] = $schedule_id;
        $data['participants'] = $this->schedule->getScheduleParticipants($schedule_id);
        $data['groups'] = $this->schedule->getStudentStaffGroups();

        // $schedule_ids = $this->schedule->getSchedulesWithTimeRange($data['schedule']->un_start_time, $data['schedule']->un_end_time);
        // $data['allotted_staff'] = $this->schedule->getOccupiedParticipants($schedule_ids, 'staff');
        // $data['allotted'] = $this->schedule->getOccupiedParticipants($schedule_ids, 'class_section');
        $data['main_content'] = 'online_class_v2/add_participants';
        $this->load->view('inc/template', $data);
    }

    public function getInvitedSections() {
      $schedule_id = $_POST['schedule_id'];
      $schedule = $this->schedule->getSchedule($schedule_id);
      $schedule_ids = $this->schedule->getSchedulesWithTimeRange($schedule->un_start_time, $schedule->un_end_time);
      $data['allotted_sections'] = $this->schedule->getOccupiedParticipants($schedule_ids, 'class_section');
      echo json_encode($data);
    }

    public function getInvitedGroupMembers() {
      $schedule_id = $_POST['schedule_id'];
      $group_id = $_POST['group_id'];
      $schedule = $this->schedule->getSchedule($schedule_id);
      $schedule_ids = $this->schedule->getSchedulesWithTimeRange($schedule->un_start_time, $schedule->un_end_time);
      $data['group_members'] = $this->schedule->getStudentStaffGroupsMembers($group_id);
      $data['allotted_sections'] = $this->schedule->getOccupiedParticipants($schedule_ids, 'class_section');
      $data['allotted_staff'] = $this->schedule->getOccupiedParticipants($schedule_ids, 'staff');
      $data['allotted_students'] = $this->schedule->getOccupiedParticipants($schedule_ids, 'student');
      echo json_encode($data);
    }

    public function getInvitedStaff() {
      $schedule_id = $_POST['schedule_id'];
      $schedule = $this->schedule->getSchedule($schedule_id);
      $schedule_ids = $this->schedule->getSchedulesWithTimeRange($schedule->un_start_time, $schedule->un_end_time);
      $data['allotted_staff'] = $this->schedule->getOccupiedParticipants($schedule_ids, 'staff');
      echo json_encode($data);
    }

    public function getSectionStudents() {
      $section_id = $_POST['section_id'];
      $schedule_id = $_POST['schedule_id'];
      $schedule = $this->schedule->getSchedule($schedule_id);
      $schedule_ids = $this->schedule->getSchedulesWithTimeRange($schedule->un_start_time, $schedule->un_end_time);
      $data['allotted'] = $this->schedule->getOccupiedParticipants($schedule_ids, 'student');
      $data['students'] = $this->schedule->getSectionStudents($section_id);
      echo json_encode($data);
    }

    public function remove_participant() {
      $schedule_id = $_POST['schedule_id'];
      $status = $this->schedule->remove_participant();
      if($status) {
          $schedule_id = $_POST['schedule_id'];
          $stake_holder_id = $_POST['id'];
          $type = $_POST['type'];
          $notify_array = [];
          if($type == 2) {
            $notify_array['staff_ids'] = [$stake_holder_id];
          } else {
            $notify_array['student_ids'] = [$stake_holder_id];
          }
          $this->_sendNotification($schedule_id, $notify_array);
          echo 1;
      } else {
        echo 0;
      }
    }

    public function save_invitees() {
      $input = $this->input->post();
      $schedule_id = $input['schedule_id'];
      $type = $input['type'];
      if($type == 'group') {
        //divide the members on type
        // echo '<pre>'; print_r($input); die();
        $group_members = $input['group_members'];
        $members = array('student' => [], 'class_section' => [], 'staff' => []);
        foreach ($group_members as $member) {
          list($type, $id) = explode("-", $member);
          $members[$type][] = $id;
        }

        if(!empty($members['student'])) {
          $input['students'] = $members['student'];
          $input['type'] = 'student';
          $input['std_sections'] = 0;//don't know the section
          $this->_inviteSender($input);
        }
        if(!empty($members['class_section'])) {
          $input['type'] = 'class_section';
          foreach ($members['class_section'] as $section) {
            $input['class_section'] = $section;
            $this->_inviteSender($input);
          }
        }
        if(!empty($members['staff'])) {
          $input['staff'] = $members['staff'];
          $input['type'] = 'staff';
          $this->_inviteSender($input);
        }
      } else {
        //single type is provided
        $this->_inviteSender($input);
      }
      redirect('online_class_v2/schedule/add_participants/'.$schedule_id);
    }

    public function _inviteSender($input) {
      $schedule_id = $input['schedule_id'];
      $type = $input['type'];
      $existing_ids = $this->schedule->getExistingInvitees($schedule_id, $type);
      $sending_ids = [];
      $invitees = [];
      $notify_array = [];
      // $max_allowed = $input['max_allowed'];
      $cur_participants = $input['cur_participants'];
      $class_section_id = NULL;
      $stakeholder_type = 0;
      if($type == 'class_section') {
        $stakeholder_type = 1;
        $class_section_id = $input['class_section'];
        $students = $this->schedule->getSectionStudents($class_section_id);
        foreach ($students as $key => $std) {
          if(!in_array($std->id, $existing_ids)) {
            $sending_ids[] = $std->id;
          }
        }
        $notify_array['student_ids'] = $sending_ids;
      } else if($type == 'student') {
        $stakeholder_type = 0;
        $stdIds = $input['students'];
        $class_section_id = $input['std_sections'];
        $section_student_count = $this->schedule->getSectionStudentCount($class_section_id);
        if(count($stdIds) == $section_student_count) {
          //if all the students in the section are selected, then make it class section type
          $stakeholder_type = 1;
        }
        foreach ($stdIds as $key => $std_id) {
          if(!in_array($std_id, $existing_ids)) {
            $sending_ids[] = $std_id;
          }
        }
        $notify_array['student_ids'] = $sending_ids;
      } else if($type == 'staff') {
        $stakeholder_type = 2;
        $staff_ids = $input['staff'];
        foreach ($staff_ids as $key => $staff_id) {
          if(!in_array($staff_id, $existing_ids)) {
            $sending_ids[] = $staff_id;
          }
        }
        $notify_array['staff_ids'] = $sending_ids;
      }

      if(!empty($sending_ids)) {
        foreach ($sending_ids as $key => $id) {
          $invitees[] = array(
            'schedule_id' => $schedule_id,
            'stakeholder_type' => $stakeholder_type,
            'stakeholder_id' => $id
          );
        }
        // echo '<pre>'; print_r($invitees); die();
        $status = $this->schedule->add_participants($invitees);
        if($status) {
          $this->_sendNotification($schedule_id, $notify_array);
          $this->session->set_flashdata('flashSuccess', 'Successful added Invitees');
        } else {
          $this->session->set_flashdata('flashError', 'Something went wrong');
        }
        return 1;
      }
      $this->session->set_flashdata('flashInfo', 'Selected Students Already Exists');
      return 1;
    }

    public function save_invitees_old() {
      $input = $this->input->post();
      $schedule_id = $input['schedule_id'];
      $type = $input['type'];
      $existing_ids = $this->schedule->getExistingInvitees($schedule_id, $type);
      $sending_ids = [];
      $invitees = [];
      $notify_array = [];
      // $max_allowed = $input['max_allowed'];
      $cur_participants = $input['cur_participants'];
      $class_section_id = NULL;
      $stakeholder_type = 0;
      if($type == 'class_section') {
        $stakeholder_type = 1;
        $class_section_id = $input['class_section'];
        $students = $this->schedule->getSectionStudents($class_section_id);
        foreach ($students as $key => $std) {
          if(!in_array($std->id, $existing_ids)) {
            $sending_ids[] = $std->id;
          }
        }
        $notify_array['student_ids'] = $sending_ids;
      } else if($type == 'student') {
        $stakeholder_type = 0;
        $stdIds = $input['students'];
        $class_section_id = $input['std_sections'];
        $section_student_count = $this->schedule->getSectionStudentCount($class_section_id);
        if(count($stdIds) == $section_student_count) {
          //if all the students in the section are selected, then make it class section type
          $stakeholder_type = 1;
        }
        foreach ($stdIds as $key => $std_id) {
          if(!in_array($std_id, $existing_ids)) {
            $sending_ids[] = $std_id;
          }
        }
        $notify_array['student_ids'] = $sending_ids;
      } else if($type == 'staff') {
        $stakeholder_type = 2;
        $staff_ids = $input['staff'];
        foreach ($staff_ids as $key => $staff_id) {
          if(!in_array($staff_id, $existing_ids)) {
            $sending_ids[] = $staff_id;
          }
        }
        $notify_array['staff_ids'] = $sending_ids;
      }

      if(!empty($sending_ids)) {
        foreach ($sending_ids as $key => $id) {
          $invitees[] = array(
            'schedule_id' => $schedule_id,
            'stakeholder_type' => $stakeholder_type,
            'stakeholder_id' => $id
          );
        }
        // echo '<pre>'; print_r($invitees); die();
        $status = $this->schedule->add_participants($invitees);
        if($status) {
          $this->_sendNotification($schedule_id, $notify_array);
          $this->session->set_flashdata('flashSuccess', 'Successful added Invitees');
        } else {
          $this->session->set_flashdata('flashError', 'Something went wrong');
        }
        redirect('online_class_v2/schedule/add_participants/'.$schedule_id);
      }
      $this->session->set_flashdata('flashInfo', 'Selected Students Already Exists');
      redirect('online_class_v2/schedule/add_participants/'.$schedule_id);
    }

    public function sendNotificationsToParticipants() {
      $schedule_id = $_POST['schedule_id'];
      $participants = $this->schedule->getScheduleParticipantIds($schedule_id);
      $notify_array = array();
      if(!empty($participants['student_ids'])) {
        $notify_array['student_ids'] = $participants['student_ids'];
      }

      if(!empty($participants['staff_ids'])) {
        $notify_array['staff_ids'] = $participants['staff_ids'];
      }

      $this->_sendNotification($schedule_id, $notify_array);
      echo 1;
    }

    public function getAllottedSchedules() {
      $schedule_date = date('Y-m-d', strtotime($_POST['schedule_date']));
      $host_id = $_POST['host_id'];
      $data = $this->schedule->getAllottedSchedules($host_id, $schedule_date);
      echo json_encode($data);
    }

    public function getStaffClassLink() {
      $staff_id = $_POST['staff_id'];
      $class_link = $this->schedule->getStaffClassLink($staff_id);
      echo $class_link;
    }

    public function clone_schedules() {
      $data['main_content'] = 'online_class_v2/clone_schedules';
      $this->load->view('inc/template', $data);
    }

    public function get_clone_schedules() {
      $clone_from = date('Y-m-d', strtotime($_POST['clone_from']));
      $clone_to = date('Y-m-d', strtotime($_POST['clone_to']));

      //chek is there any schedules available in clone to date - if exists don't allow to clone
      $schedules_exist = $this->schedule->checkSchedulesExist($clone_to);
      if($schedules_exist) {
        $data['schedules_exist'] = 1;
      } else {
        $data['schedules_exist'] = 0;
        $data['schedules'] = $this->schedule->get_schedules($clone_from);
      }
      echo json_encode($data);
    }

    public function cloneSingleSchedule(){
      $schedule_id = $_POST['schedule_id'];
      $clone_date = date('Y-m-d',strtotime($_POST['clone_date']));

      //schedule data
      $prev_schedule = $this->schedule->getSchedule($schedule_id);
      $created_by = $this->authorization->getAvatarStakeHolderId();

      $schedule_data = array(
        'name' => $prev_schedule->name,
        'description' => $prev_schedule->description,
        'schedule_date' => $clone_date,
        'host_id' => $prev_schedule->host_id,
        'meeting_link' => $prev_schedule->meeting_link,
        'subject_name' => $prev_schedule->subject_name,
        'start_time' => $clone_date.' '.$prev_schedule->f_start_time,
        'end_time' => $clone_date.' '.$prev_schedule->f_end_time,
        'created_by' => $created_by,
        'last_modified_by' => $created_by
      );

      // echo "<pre>"; print_r($schedule_data); die();
      // $this->db->trans_start();
      $cloned_schedule_id = $this->schedule->save_schedule($schedule_data);

      $notify_array = [];
      $staff_ids = [$prev_schedule->host_id];
      if($cloned_schedule_id) {
        $participants = $this->schedule->getScheduleParticipantsData($schedule_id);
        $student_ids = [];
        foreach ($participants as $k => $part) {
          $participants[$k]['schedule_id'] = $cloned_schedule_id;
          if($part['stakeholder_type'] == 2) {
            $staff_ids[] = $part['stakeholder_id'];
          } else {
            $student_ids[] = $part['stakeholder_id'];
          }
        }

        if(!empty($participants)) {
          $status = $this->schedule->save_participants($participants);
          if($status) {
            if(!empty($student_ids)) $notify_array['student_ids'] = $student_ids;
            if(!empty($staff_ids)) $notify_array['staff_ids'] = $staff_ids;
          }
        }
        
        $this->_sendNotification($cloned_schedule_id, $notify_array);
        /*if($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
        } else {
          $this->db->trans_commit();
        }*/
      }

      echo $cloned_schedule_id;
    }

    private function _sendNotification($schedule_id, $notify_array) {
      $this->load->helper('texting_helper');
      if(empty($notify_array['student_ids'])) {
        unset($notify_array['student_ids']);
      }
      if(empty($notify_array['staff_ids'])) {
        unset($notify_array['staff_ids']);
      }
      $notify_array['mode'] = 'notification';
      $notify_array['send_to'] = 'Both';
      $notify_array['source'] = 'Online Class';
      $notify_array['student_url'] = site_url('online_class_v2/student_schedule');
      $notify_array['staff_url'] = site_url('online_class_v2/schedule');
      if(!array_key_exists('message', $notify_array)) {
        $schedule = $this->schedule->getSchedule($schedule_id);
        $notify_array['message'] = $schedule->name.' class has been scheduled on '.local_time($schedule->un_start_time, 'd-M h:i a').' to '.local_time($schedule->un_end_time, 'd-M h:i a');
      }
      sendText($notify_array);
      return 1;
    }

    public function report() {
      $data['main_content'] = 'online_class_v2/report';
      $this->load->view('inc/template', $data);
    }

    private function _getDatesByRange($from_date, $to_date, $format="Y-m-d") {
      $dates = [];
      $from = $from_date;
      while($from != $to_date) {
        $dates[] = array(
          'key' => date($format, strtotime($from)),
          'display' => date('d M Y', strtotime($from))
        );
        $from = date($format, strtotime("$from +1 days"));
      }
      $dates[] = array(
        'key' => date($format, strtotime($from)),
        'display' => date('d M Y', strtotime($from))
      );
      return $dates;
    }

    public function getStaffSchedules() {
      $from = date('Y-m-d', strtotime($_POST['from']));
      $to = date('Y-m-d', strtotime($_POST['to']));
      $staff_list = $this->staff_getter->staff_list($status=2);
      $schedules = $this->schedule->getStaffSchedules($from, $to);
      foreach ($staff_list as $key => $staff) {
        $staff_list[$key]->schedules = array();
        if(array_key_exists($staff->id, $schedules)) {
          $staff_list[$key]->schedules = $schedules[$staff->id];
        }
      }
      $data['schedules'] = $staff_list;
      $data['dates'] = $this->_getDatesByRange($from, $to);
      echo json_encode($data);
      // echo "<pre>"; print_r($data); die();
    }

    public function staff_schedule_report() {
      $data['staff_list'] = $this->staff_getter->staff_list($status=2);
      $data['main_content'] = 'online_class_v2/staff_wise_report';
      $this->load->view('inc/template', $data);
    }

    public function getStaffWiseSchedules() {
      $from = date('Y-m-d', strtotime($_POST['from']));
      $to = date('Y-m-d', strtotime($_POST['to']));
      $staff_id = $_POST['staff_id'];
      $data['staff'] = $this->staff_getter->staff_data($staff_id);
      $data['schedules'] = $this->schedule->getStaffWiseSchedules($from, $to, $staff_id);
      echo json_encode($data);
    }

    public function section_schedule_report() {
      $data['sections'] = $this->class_section->getAllClassSections();
      $data['main_content'] = 'online_class_v2/section_wise_report';
      $this->load->view('inc/template', $data);
    }

    public function getSectionWiseSchedules() {
      $from = date('Y-m-d', strtotime($_POST['from']));
      $to = date('Y-m-d', strtotime($_POST['to']));
      $class_section_id = $_POST['class_section_id'];
      $data['schedules'] = $this->schedule->getSectionWiseSchedules($from, $to, $class_section_id);
      echo json_encode($data);
    }

    public function cancelSchedule() {
      $schedule_id = $_POST['schedule_id'];
      $status = $this->schedule->cancelSchedule($schedule_id);
      if($status == 1) {
        $schedule = $this->schedule->getSchedule($schedule_id);
        $participants = $this->schedule->getScheduleParticipantsData($schedule_id);
        $notify_array = [];
        foreach ($participants as $k => $part) {
          if($part['stakeholder_type'] == 2) {
            $notify_array['staff_ids'] = $part['stakeholder_id'];
          } else {
            $notify_array['student_ids'] = $part['stakeholder_id'];
          }
        }
        $notify_array['message'] = 'Schedule Cancelled: '.$schedule->name.' class on '.local_time($schedule->un_start_time, 'd-M h:i a').' to '.local_time($schedule->un_end_time, 'd-M h:i a');
        $notify_array['staff_ids'][] = $schedule->host_id;
        $this->_sendNotification($schedule_id, $notify_array);
        echo 1;
      } else {
        echo 0;
      }
    }

    public function get_attendance_students() {
      $schedule_id = $_POST['schedule_id'];
      $attendance_status = $_POST['attendance_status'];
      if($attendance_status == 0) {
        $students = $this->schedule->getAttendanceData($schedule_id);
      } else {
        $students = $this->schedule->getAttendanceDataById($schedule_id, $attendance_status);
      }
      echo json_encode($students);
    }

    public function save_attndance() {
      // echo "<pre>"; print_r($_POST); die();
      $status = $this->schedule->save_attndance();
      if($status == 1) {
        $this->session->set_flashdata('flashSuccess', 'Attendance saved successfully');
      } else if($status == -1) {
        $this->session->set_flashdata('flashError', 'Subject not found!');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong!');
      }
      redirect('online_class_v2/schedule');
    }
}