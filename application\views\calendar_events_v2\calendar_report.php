<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
 <li><a href="<?php echo site_url('calendar_events_v2/Calendar_events_v2'); ?>">Calendar V2</a></li>
  <li>Report Calendar Events</li>
</ul> 

<div class="col-md-12">
  <div class="panel panel-default new-panel-style_3">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff"><a class="back_anchor" href="<?php echo site_url('calendar_events_v2/Calendar_events_v2') ?>"><span class="fa fa-arrow-left"></span></a>Report Calendar Events</h3>
        </div>
      </div>
    </div>
    <div class="panel-body" id="search">
  <form class="form-horizontal">
      <div class="col-xs-12 col-sm-6 col-md-2">
        <div class="form-group">
          <select name="payment_mode" id="payment_mode" required="" class="form-control select">
            <?php foreach ($calendarList as $key => $val) { ?>
              <option value=<?= $val->id ?> ><?= $val->calendar_name?></option>;
            <?php } ?>
            
          </select>
          <small class="col-md-12 help-text" style="padding:0;color:#aba6a6">Select Calendar Template</small>
        </div>
      </div> 

     

      <div class="col-xs-12 col-sm-6 col-md-2">
        <div id="reportrange" class="dtrange">  
          <span></span>                                          
          <input type="hidden" id="from_date">
          <input type="hidden" id="to_date">
        </div>
        <small class="col-md-12 help-text" style="padding:0;color:#aba6a6">Select date range for calendar report</small>
      </div>

      <div class="col-xs-12 col-sm-6 col-md-2">
        <input type="button" value="Get Report" onclick="generate_calendar_report()" id="get" class="input-md btn btn-primary">
      </div>
    </div>
  </form>
</div>

  

<div class="col-md-12" id="display" style="display:none">
  <div class="panel panel-default new-panel-style_3">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff">Calendar Reports</h3>
        </div>
      
    <div class="panel-body expense" id="printArea">
    </div>
  </div>
</div>
</div>

  <script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
  <script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
  
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
 

 $("#reportrange").daterangepicker({
    ranges: {
     'This Month': [moment().startOf('month'), moment().endOf('month')],
     'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
     'Last 3 Months': [moment().subtract(2, 'months').startOf('month'), moment().endOf('month')],
     'Last 6 Months': [moment().subtract(5, 'months').startOf('month'), moment().endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));

  function generate_calendar_report() {
    var calendar_id = $('#payment_mode').val();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();

    if (!calendar_id) {
      Swal.fire({
        icon: 'warning',
        title: 'Warning',
        text: 'Please select a calendar template'
      });
      return;
    }

    if (!from_date || !to_date) {
      Swal.fire({
        icon: 'warning',
        title: 'Warning',
        text: 'Please select a date range'
      });
      return;
    }

    // Show loading
    Swal.fire({
      title: 'Generating Report...',
      text: 'Please wait while we generate your calendar report',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    $.ajax({
      url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/generate_calendar_report'); ?>',
      type: 'POST',
      data: {
        calendar_id: calendar_id,
        from_date: from_date,
        to_date: to_date
      },
      dataType: 'json',
      success: function(response) {
        Swal.close();

        if (response.status === 'success') {
          displayReport(response.data);
          $('#display').show();
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: response.message || 'Failed to generate report'
          });
        }
      },
      error: function(xhr, status, error) {
        Swal.close();
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'An error occurred while generating the report'
        });
      }
    });
  }

  function displayReport(data) {
    var html = '<div class="table-responsive">';
    html += '<h4>Calendar Report: ' + data.calendar_name + '</h4>';
    html += '<p><strong>Calendar Template Date Range:</strong> ' + data.calendar_start_date + ' to ' + data.calendar_end_date + '</p>';
    // html += '<div class="alert alert-info">';
    // html += '<small><i class="fa fa-info-circle"></i> Working days exclude Sundays and holidays. If selected date range is outside the calendar template range, all columns will show 0.</small>';
    // html += '</div>';
    html += '<table class="table table-bordered table-striped">';
    html += '<thead>';
    html += '<tr>';
    html += '<th>Month</th>';
    html += '<th>Working Days</th>';
    html += '<th>Holiday Days</th>';
    html += '<th>Event Days</th>';
    html += '<th>Total Days in Month</th>';
    html += '</tr>';
    html += '</thead>';
    html += '<tbody>';

    data.months.forEach(function(month) {
      html += '<tr>';
      html += '<td>' + month.month + '</td>';
      html += '<td>' + month.working_days + '</td>';
      html += '<td>' + month.holiday_days + '</td>';
      html += '<td>' + month.event_days + '</td>';
      html += '<td>' + month.total_days + '</td>';
      html += '</tr>';
    });

    html += '</tbody>';
    html += '</table>';
    html += '</div>';

    $('#printArea').html(html);
  }

 </script>


