<?php

class Csv_upload extends CI_Controller { 
	private $yearIds = array(); 
	private $currentYear; 
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
	      redirect('auth/login', 'refresh');
	    }
	    if (!$this->authorization->isSuperAdmin()) {
	      redirect('dashboard', 'refresh');
	    }
	 	$this->load->library('csvimport'); 
	 	$this->currentYear = $this->acad_year->getAcadYearId();
	 	$this->yearIds = $this->_formYearData();
	}

	private function _formYearData() {
		$years = $this->acad_year->getAllYearData();
		$yearIds = array();
		foreach ($years as $key => $value) {
			$yearIds[$value->acad_year] = $value->id;
		}
		return $yearIds;
	}

	private $stdColumns = array(
		's_admission_no',
		's_roll_no',
		's_first_name',
		's_last_name',
		's_class',
    's_class_section',
		's_cources',
		's_boarding',
		's_board',
		's_medium',
		's_donor',
		's_student_house',
		's_dob',
		's_gender',
		's_nationality',
		's_religion',
		's_category',
		's_mother_tongue',
		's_contact_no',
		's_is_rte',
		's_date_of_joining',
		's_birth_taluk',
		's_birth_district',
		's_caste',
		's_aadhar_no',
		's_class_admitted_to',
		's_admission_year',
		's_emergency_info',
		's_email',
		's_admission_acad_year',
        's_semester',
        's_enrollment_number',
    's_address1',
    's_address2',
    's_blood_group',
    's_sts_number',
    'nick_name',
    'student_indian_visa_number',
    'student_indian_visa_expiry_date',
    'identification_mark1',
    'identification_mark2',
    'sibling1_name',
    'sibling1_occupation',
    'sibling1_mobile_num',
    'sibling2_name',
    'sibling2_occupation',
    'sibling2_mobile_num',
    'student_whatsapp_num',
    'is_single_child',
    'is_minority',
    'current_nearest_location',
    'passport_number',
    'passport_issued_place',
    'passport_validity',
    'parents_marriage_anniversary',
    'sibling3_name',
    'sibling3_occupation',
    'sibling3_mobile_num',
    'point_of_contact',
    'student_living_with',
    'last_tc_num',
    'last_hallticket_num',
    'hall_ticket_num',
    'registration_no'
	);
	private $fatherColumns = array(
		'f_first_name',
		'f_last_name',
		'f_qualification',
		'f_occupation',
		'f_mobile_no',
		'f_aadhar_no',
		'f_annual_income',
		'f_company',
		'f_mother_tongue',
		'f_email',
    'f_home_address',
    'f_office_address',
    'f_blood_group',
    'f_dob',
    'f_employee_id',
    'f_office_landline_number',
    'f_alternate_email_id',
    'f_whatsapp_num',
    'f_bank_account_num'
	);
	private $motherColumns = array(
		'm_first_name',
		'm_last_name',
		'm_qualification',
		'm_occupation',
		'm_mobile_no',
		'm_aadhar_no',
		'm_annual_income',
		'm_company',
		'm_mother_tongue',
		'm_email',
    'm_home_address',
    'm_office_address',
    'm_blood_group',
    'm_dob',
    'm_employee_id',
    'm_office_landline_number',
    'm_alternate_email_id',
    'm_whatsapp_num',
    'm_bank_account_num'
	);
    private $guardianColumns = array(
        'g_name',
        'g_relationship',
        'g_occupation',
        'g_mobile_no',
    );

	public function student() {
		$data['main_content']    = 'csv/student';
		$this->load->view('inc/template', $data);
	}

    // public function downloadDb(){
    //     $this->load->helper('file');
    //     $this->load->helper('download');
    //     $this->load->library('zip');
    //     $this->load->dbutil();
    //     $db_format=array('format'=>'zip','filename'=>'school.sql');
    //     $backup=& $this->dbutil->backup($db_format);
    //     $dbname='backup-on-'.date('d-m-y H:i').'.zip';
    //     force_download($dbname,$backup);
    // }

	public function downloadCsvFormat() {
		$this->_createTable('myt', []);
		$csvArray = array();
		foreach ($this->stdColumns as $column) {
			array_push($csvArray, $column);
		}
		foreach ($this->fatherColumns as $column) {
			array_push($csvArray, $column);
		}
		foreach ($this->motherColumns as $column) {
			array_push($csvArray, $column);
		}
        foreach ($this->guardianColumns as $column) {
            array_push($csvArray, $column);
        }
		// $csvHeader = implode(",", $csvArray);
		header("Content-type: application/csv");
        header("Content-Disposition: attachment; filename=\"student_data".".csv\"");
        header("Pragma: no-cache");
        header("Expires: 0");

        $handle = fopen('php://output', 'w');
		fputcsv($handle, $csvArray);
        fclose($handle);
        exit;
	}

	public function upload($filename = '', $upload_path) {
	    $config['upload_path'] = $upload_path;
	    $config['allowed_types'] = 'csv|CSV';
	    $config['remove_spaces'] = true;
	    $config['overwrite'] = false;
	    
	    $this->load->library('upload', $config);
	    $this->upload->initialize($config);
	    if (!$this->upload->do_upload($filename)) {
	      $error = array('status' => 'error', 'data' => $this->upload->display_errors());
	      $this ->session ->set_flashdata('flashError', 'Failed to upload - ' . $filename);
	      return $error;
	    } else {
	      $image = $this->upload->data();
	      $success = array('status' => 'success', 'data' => $image);
	      return $success;
	    }

  	}

  	public function student_csv_submit(){
  		$creDir = 'uploads/';
	    // if (!is_dir($creDir)) {
	    //   mkdir($creDir, 0777, TRUE);
	    // }

	    if ($_FILES['csv_file']['name'] != "") {
	      $ret_val = $this->upload('csv_file', $creDir);
	      if ($ret_val['status'] == 'success') {
	        $file_data = $ret_val['data'];
	      } else {
	        $file_data = $ret_val['data'];
	      }
	    }else{
        $file_data="";
	    }
	    $tableName = $_POST['db_table'];
        $file_path = 'uploads/'.$file_data['file_name'];

	    if ($this->csvimport->get_array($file_path)) {
            $student_arr = $this->csvimport->get_array($file_path);
	    	    $keys = array_keys($student_arr[0]);
            $this->_createTable($tableName, $keys);
            // echo "<pre>"; print_r(); die();
            foreach ($student_arr as $key => $std) {
            	$stdData = array();
            	foreach ($std as $column => $value) {
            		$stdData[$column] = $value;
            	}
            	$data[] = $stdData;
                // $data[] = array( 
                //     'std_first_name' => $std['std_first_name'], 
                //     'std_last_name' => $std['std_last_name'], 
                //     'admission_no' => $std['admission_no'], 
                //     'gender' => $std['gender'], 
                //     'father_first_name' => $std['father_first_name'],
                //     'father_mobile_no' => $std['father_mobile_no'],
                //     'join_acad_year' => $std['join_acad_year'],
                //     'class' => $std['class'],
                //     'section' => $std['section'],
                //     'previous_class' => $std['previous_class'],
                //     'previous_section' => $std['previous_section']
                // );

            }
            $result = $this->db->insert_batch($tableName,$data);
            if($result){ 
                $this->session->set_flashdata('flashSuccess', 'Data upload Successful');
              }
              else{
                $this->session->set_flashdata('flashError', 'Something Went Wrong');
            }
            redirect('csv/student');
     	}
  	}

  	private function _createTable($tableName, $keys) {
  		$this->load->dbforge();
  		$this->dbforge->drop_table($tableName, TRUE);
  		$fields = $this->_makeFields($keys);
  		$this->dbforge->add_key('id', TRUE);
  		$this->dbforge->add_field($fields);
  		$this->dbforge->create_table($tableName);
  	}

  	private function _makeFields($keys) {
  		
  		$fields = array(
  				'id' => array(
                                         'type' => 'INT',
                                         'constraint' => 11,
                                         'auto_increment' => TRUE,
                                         'primary' => TRUE
                                  ),
                's_admission_no' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => 100,
                                         'null' => TRUE
                                  ),
                's_roll_no' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_first_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_last_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_class' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_class_section' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '20',
                                         'null' => TRUE
                                  ),
               's_cources' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_boarding' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_board' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_medium' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_donor' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_student_house' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_dob' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_gender' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_nationality' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_religion' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_category' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_mother_tongue' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_contact_no' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_is_rte' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_date_of_joining' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_birth_taluk' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_birth_district' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_caste' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_aadhar_no' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_class_admitted_to' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_admission_year' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_admission_acad_year' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                's_emergency_info' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                's_email' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'f_first_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'f_last_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'f_qualification' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'f_occupation' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'f_desination' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'f_mobile_no' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'f_aadhar_no' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'f_annual_income' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'f_company' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'f_mother_tongue' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'f_email' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),

                'm_first_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'm_last_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'm_qualification' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'm_occupation' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'm_desination' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'm_mobile_no' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'm_aadhar_no' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'm_annual_income' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'm_company' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'm_mother_tongue' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'm_email' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => true
                                  ),
                's_address1' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                's_address2' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                's_blood_group' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '20',
                                         'null' => true
                                  ),
                'f_home_address' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                'f_office_address' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                'f_office_address_city' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                'f_office_address_state' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                'm_home_address' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),

                'm_office_address' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                'mother_home_city' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                'mother_home_pincode' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                'mother_home_state' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                'm_office_address_1' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                's_sts_number' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                'life_time_fee_mode' => array(
                                       'type' => 'INT',
                                       'constraint' => 11,
                                  ),
                'g_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'g_last_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'g_home_city' => array(
                                         'type' => 'TEXT',
                                         'null' => true
                                  ),
                'g_relationship' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'g_occupation' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '255',
                                         'null' => TRUE
                                  ),
                'g_mobile_no' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ), 
                's_semester' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ), 
                's_enrollment_number' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ), 
                'student_allergies_prohibtions' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'student_allergies_prohibtions' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'student_co_curricular_activities' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
        );

    foreach ($keys as $key) {
      if(!array_key_exists($key, $fields)) {
        $fields[$key] = array(
                               'type' => 'TEXT',
                               'null' => true
                        );
      }
    }

		return $fields;
  	}

  	private function __marshalCategory($id, $category) {
      $categories = $this->settings->getSetting('category');
      //echo '<pre>';print_r($category);
      //echo '<pre>';print_r($categories);
      
      foreach ($categories as $k => $cCat) {
        $found = 0;
        if ($cCat == $category) {
          $found = $k;
          break;
        }
      }
      if ($found==0) {
        echo 'Problem found in Category; ';
        echo 'ID : ' . $id . '; Input value: ' . $category;die();
      } else {
        return $found;
      }
    }

    private function __marshalMediumId($id, $iMedium) {
      $mediums = $this->settings->getSetting('medium');
      //echo '<pre>';print_r($category);
      //echo '<pre>';print_r($categories);
      
      foreach ($mediums as $k => $med) {
        $found = 0;
        if ($med == $iMedium) {
          $found = $k;
          break;
        }
      }
      if ($found==0) {
        echo 'Problem found in Medium; ';
        echo 'ID : ' . $id . '; Input value: ' . $iMedium;die();
      } else {
        return $found;
      }
    }

    private function __marshalClassId($id, $pClassName) {
      $classes = $this->db->select('id, class_name')
        ->from('class')
        ->where('acad_year_id', $this->currentYear)
        ->get()->result();
        
      $found = 0;
      // echo $pClassName;
      // echo "<pre>"; print_r($classes);die();
      foreach ($classes as $class) {
        if ($class->class_name == $pClassName) {
          $found = $class->id;
          break;
        }
      }

      if ($found==0) {
        echo 'Problem found in Class; ';
        echo 'ID : ' . $id . '; Input value: ' . $pClassName;die();
      }
      return $found; //Return the promoted class Id.
    }

    private function _getSection($class_id, $section_name) {
    	$sections = $this->db->select('id, section_name')->where('class_id', $class_id)->get('class_section')->result();
    	$section_id = null;
    	foreach ($sections as $key => $section) {
    		if($section_name == $section->section_name) {
    			$section_id = $section->id;
    			break;
    		}
    	}
    	return $section_id;
    }

    private function _addStdAdmissionData($result) {
    	$std_admission = array(
        	'admission_no' => null,
        	'first_name' => null,
        	'last_name' => null,
        	'dob' => null,
        	'gender' => null,
        	'nationality' => null,
        	'religion' => null,
        	'category' => null,
        	'mother_tongue' => null,
        	'contact_no' => null,
        	'is_rte' => null,
        	'admission_status' => null,
        	'date_of_joining' => null,
        	'birth_taluk' => null,
        	'birth_district' => null,
        	'caste' => null,
        	'aadhar_no' => null,
        	'class_admitted_to' => null,
        	'admission_year' => null,
        	'emergency_info' => null,
        	'identification_code' => null,
        	'admission_acad_year_id' => null,
        	'email' => null
        );

        foreach ($std_admission as $column => $value) {
        	$col = 's_'.$column;
        	if($column == 'category') {
        		if(isset($result->{$col}) && $result->{$col} != '') {
		        	$std_admission['category'] = $this->__marshalCategory($id, $result->{$col});
		        }
        	}
        	if($column == 'admission_acad_year_id') {
        		$std_admission[$column] = $this->yearIds[$result->{$col}];
        		continue;
        	}

        	if(isset($result->{$col}) && $result->{$col} != '') {
        		$std_admission[$column] = $result->{$col};
        	}
        }

        echo "<pre>"; print_r($std_admission); die();

        $this->db->insert('student_admission', $std_admission);
		return $this->db->insert_id();
    }

    private function _addStdYearData($result, $std_admission_id, $rawId) {
    	$std_year = array(
        	'roll_no' => null,
        	'class_id' => null,
        	'class_section_id' => null,
        	'board' => 2,
        	'admission_type' => 2,
        	'student_admission_id' => $std_admission_id,
        	'acad_year_id' => $this->currentYear,
        	'medium' => 1,
        	'donor' => null,
        	'student_house' => null,
        	'previous_class_id' => null,
        	'previous_class_section_id' => null,
        );

        foreach ($std_year as $column => $value) {
        	$col = 's_'.$column;
        	if($column == 'class_id') {
        		$std_year[$column] = $this->__marshalClassId($rawId, $result->s_class);
        	}
        	if($column == 'class_section_id') {
        		continue;
        	}
        	if(isset($result->{$col}) && $result->{$col} != '') {
        		$std_year[$column] = $result->{$col};
        	}
        }
        if(isset($result->s_class_section) && $result->s_class_section != '') {
        	if(isset($result->s_class) && $result->s_class != '') {
        		$std_year['class_section_id'] = $this->_getSection($std_year['class_id'], $result->s_class_section);
        	}
        }

        $this->db->insert('student_year', $std_year);
        echo '<pre>'; print_r($std_year); die();
    }

  	public function importStudentData () {
      $fromId = $this->input->post('from_id');
      $toId = $this->input->post('to_id');
      $tableName = $this->input->post('db_table');

      // echo "<pre>"; print_r($this->input->post()); die();

      for ($id = $fromId; $id <= $toId; $id++) {
        $result = $this->db->select('*')->from($tableName)->where('id',$id)->get()->row();
        if (empty($result)) continue;

        echo "<pre>"; print_r($result);
        $student_admission_id = 1;
        // $student_admission_id = $this->_addStdAdmissionData($result, $result->id);
        $this->_addStdYearData($result, $student_admission_id, $result->id);
        
        
        if($result->medium == '') {
        	$std_admission['medium'] = $this->__marshalMediumId($id, $result->medium);
        }
        $result->board = '1'; //1 is for State board
        $result->religion = ucwords(strtolower($result->religion));
        $result->caste = ucwords(strtolower($result->caste));
        $result->mother_tongue = ucwords(strtolower($result->mother_tongue));
        $result->donor_name = ucwords(strtolower($result->donor_name));

        //Send as empty. This signifies that the student is not a sibling.
        $result->f_userid = '';
        $result->m_userid = '';
        $result->student_lastname = '';
        $result->f_last_name = '';
        $result->m_last_name = '';
        $result->s_email = '';
        $result->m_email = '';
        $result->f_email = '';
        $result->nationality = '';
        $result->std_aadhar = '';
        $result->classsection = '';
        $result->roll_num=0;
        $result->haveSibling=-1;
        $result->f_aadhar='';
        $result->m_mobile_number='';
        $result->m_aadhar='';
        $result->contact_no='';
        $result->m_annual_income='';
        $result->m_mobile_no='';

        //Marshal class Id
        $newClassId = $this->__marshalClassId($id, $result->classid);
        if ($newClassId == 0) {
          $isAlumni = 1;
        } else {
          $isAlumni = 0;
          $result->classid = $newClassId;
        }

        //Admission Status
        if ($isAlumni) {
          $result->add_status = 4;
        } else {
          $result->add_status = 2;
        }

        //Admission Type
        switch ($result->admidType) {
          case 'Re-Admission':
            $result->admidType = 1;
            $result->rteid = 2;
            break;
          case 'Re-Admission RTE':
            $result->admidType = 1;
            $result->rteid = 1;
            break;
          case 'New Admission':
            $result->admidType = 2;
            $result->rteid = 2;
            break;
          case 'New Admission RTE':
            $result->admidType = 2;
            $result->rteid = 1;
            break;
          default:
            echo 'Problem found in admidType; ';
            echo 'ID : ' . $id . '; Input value: ' . $result->admidType;die();
            break;
        }
        
        $this->__submitStudent($result, 'import');
      }
      redirect('student/Student_controller');
      //echo 'All Good!!';die();
    }

    private function _prepareStudentInput(&$input) {
      $return_data = [];
      foreach ($input as $k => $v) {
        $start_key = substr($k, 0, 2);
        if ($start_key == 'f_') {
          $key = str_replace("f_","",$k);
          $return_data['father'][$key] = $v;
        }
        elseif ($start_key == 'm_') {
          $key = str_replace("m_","",$k);
          $return_data['mother'][$key] = $v;
        }
        else {
           $return_data['student'][$k] = $v;
        }
      }

      //echo '<pre>';print_r($return_data);

      return $return_data;

    }

    private function __submitStudent($input_form, $addMode, $classId) {
      if (!$this->authorization->isAuthorized('STUDENT.NEW_STUDENT_ADD')) {
        redirect('dashboard', 'refresh');
      }      
       
      $grouped_input = $this->_prepareStudentInput($input_form);
      //echo '<pre>';print_r($grouped_input);
          //admission_no
      if(!isset($grouped_input['student']['admission_no'])) {
        $config_admission_number = $this->settings->getSetting('admission_number');
        $lastRecord = $this->Student_Model->getLastStudentid(); 
        
        if (!$lastRecord) {
          $lastRecordId = $config_admission_number->index_offset + 1;
        } else {
          $lastRecordId = $config_admission_number->index_offset + ($lastRecord->id + 1);
        }
        $params['number'] = sprintf("%0".$config_admission_number->digit_count . "s", $lastRecordId);
        $params['year_of_joining'] = $grouped_input['student']['admission_acad_year'];
        $params['classid'] = $this->Student_Model->getClassByID($grouped_input['student']['classid']);

        $grouped_input['student']['admission_no'] = $this->_generateAdmissionNo($config_admission_number,$params);
      }

      //$this->db->trans_off();
      $this->db->trans_begin();

      if (isset($_FILES['student_photo'])) {
        $student_uid = $this->Student_Model->addStudentInfo($grouped_input['student'],$this->s3FileUpload($_FILES['student_photo']),$grouped_input['father']['userid']);
      } else {
        $student_uid = $this->Student_Model->addStudentInfo($grouped_input['student'],null,$grouped_input['father']['userid']);
      }

      if($student_uid == 0) {
        $this->db->trans_rollback();
        $this->session->set_flashdata('flashError', 'Failed to insert Student Details.');
        redirect('student/Student_controller/index/');
      } else {

      if (isset($_FILES['f_father_photo'])) {
        $father_uid = $this->Student_Model->addParentInfo($grouped_input['father'],$student_uid['stdAdmId'],'Father',$this->s3FileUpload($_FILES['f_father_photo']), $grouped_input['student']['student_firstname']);
      } else {
        $father_uid = $this->Student_Model->addParentInfo($grouped_input['father'],$student_uid['stdAdmId'],'Father',null, $grouped_input['student']['student_firstname']);
      }
      if (!$father_uid) {
        $this->db->trans_rollback();
        $this->session->set_flashdata('flashError', 'Failed to insert Father Details.');
        redirect('student/Student_controller/index/');
      }

      if (isset($_FILES['m_mother_photo'])) {
        $mother_uid = $this->Student_Model->addParentInfo($grouped_input['mother'],$student_uid['stdAdmId'],'Mother',$this->s3FileUpload($_FILES['m_mother_photo']), $grouped_input['student']['student_firstname']);
      } else {
        $mother_uid = $this->Student_Model->addParentInfo($grouped_input['mother'],$student_uid['stdAdmId'],'Mother',null, $grouped_input['student']['student_firstname']);
      }

        if (!$mother_uid) {
          $this->db->trans_rollback();
          $this->session->set_flashdata('flashError', 'Failed to insert Mother Details.');
          redirect('student/Student_controller/index/');
        }

        if ($this->db->trans_status()) {
          $this->db->trans_commit();
          $this->session->set_flashdata('flashSuccess', 'Student Successfully added');
        } else {
          $this->db->trans_rollback();
          $this->session->set_flashdata('flashError', 'Something went wrong');
        }
        if ($addMode == 'import')
          return;
        else
          redirect('student/student_menu');
      }
    }

    public function staff_csv_submit(){
        $creDir = 'uploads/';
        // if (!is_dir($creDir)) {
        //   mkdir($creDir, 0777, TRUE);
        // }

        if ($_FILES['csv_file']['name'] != "") {
          $ret_val = $this->upload('csv_file', $creDir);
          if ($ret_val['status'] == 'success') {
            $file_data = $ret_val['data'];
          } else {
            $file_data = $ret_val['data'];
          }
        }else{
        $file_data="";
        }
        $tableName = $_POST['db_table'];
        $file_path = 'uploads/'.$file_data['file_name'];

        if ($this->csvimport->get_array($file_path)) {
            $staff_arr = $this->csvimport->get_array($file_path);
            $keys = array_keys($staff_arr[0]);
            $this->_create_staff_table($tableName, $keys);
            // echo "<pre>"; print_r(); die();
            foreach ($staff_arr as $key => $staff) {
                $staffData = array();
                foreach ($staff as $column => $value) {
                    $staffData[$column] = $value;
                }
                $data[] = $staffData;
            }
            $result = $this->db->insert_batch($tableName,$data);
            if($result){ 
                $this->session->set_flashdata('flashSuccess', 'Data upload Successful');
              }
              else{
                $this->session->set_flashdata('flashError', 'Something Went Wrong');
            }
            redirect('csv/staff');
        }
    }

    private function _create_staff_table($tableName, $keys) {
        $this->load->dbforge();
        $this->dbforge->drop_table($tableName, TRUE);
        $fields = $this->_makeStaffFields($keys);
        $this->dbforge->add_key('id', TRUE);
        $this->dbforge->add_field($fields);
        $this->dbforge->create_table($tableName);
    }

    private function _makeStaffFields($keys){
        
        $fields = array(
                'id' => array(
                                         'type' => 'INT',
                                         'constraint' => 11,
                                         'auto_increment' => TRUE,
                                         'primary' => TRUE
                                  ),
                'first_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => 100,
                                         'null' => TRUE
                                  ),
                'last_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'short_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'dob' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'father_first_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'mother_first_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'gender' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '20',
                                         'null' => TRUE
                                  ),
                'contact_number' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'marital_status' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'mail_id' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'joining_date' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'adhaar_number' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'spouse_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'nationality' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'alternative_no' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'spouse_contact_no' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'emergency_info' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'staff_type' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'department_name' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'a_present_add' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'a_present_add2' => array(
                                         'type' => 'VARCHAR',
                                         'constraint' => '100',
                                         'null' => TRUE
                                  ),
                'a_present_area' => array(
                                     'type' => 'VARCHAR',
                                     'constraint' => '100',
                                     'null' => TRUE
                                  ),
                'a_present_district' => array(
                                     'type' => 'VARCHAR',
                                     'constraint' => '100',
                                     'null' => TRUE
                                  ),
                'a_present_country' => array(
                                     'type' => 'VARCHAR',
                                     'constraint' => '100',
                                     'null' => TRUE
                                  ),
                'a_present_state' => array(
                                     'type' => 'VARCHAR',
                                     'constraint' => '100',
                                     'null' => TRUE
                                  ),
                'a_present_pin_code' => array(
                                 'type' => 'VARCHAR',
                                 'constraint' => '100',
                                 'null' => TRUE
                                  ),
                'qualification_name' => array(
                                 'type' => 'VARCHAR',
                                 'constraint' => '100',
                                 'null' => TRUE
                                  ),
                'experience' => array(
                                     'type' => 'VARCHAR',
                                     'constraint' => '100',
                                     'null' => TRUE
                                  ),
                'education_exp' => array(
                                     'type' => 'VARCHAR',
                                     'constraint' => '255',
                                     'null' => TRUE
                                  ),
                'specialization' => array(
                                     'type' => 'VARCHAR',
                                     'constraint' => '100',
                                     'null' => TRUE
                                  ),
            );

            foreach ($keys as $key) {
                if(!array_key_exists($key, $fields)) {
                    $fields[$key] = array(
                                   'type' => 'TEXT',
                                   'null' => true
                                );
                }
            }

            return $fields;

    }

}
