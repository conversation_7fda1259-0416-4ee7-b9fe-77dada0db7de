<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON> C R   
 *          <PERSON><PERSON><PERSON>@nextelement.in
 *
 * Created: 07 January 2025
 *
 * Description: Controller for Daily Planner.
 *
 * Requirements: PHP5 or above
 *
 */

class Daily_planner extends CI_Controller {
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('DAILY_PLANNER') || !$this->authorization->isAuthorized('DAILY_PLANNER.MODULE')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('Daily_planner_model', 'planner_model');
        $this->CI = &get_instance();
        $this->load->library('filemanager');
    }

    public function daily_planner(){
        $data['task_types'] = $this->planner_model->get_all_task_types();
        $data['title'] = 'Manage My Tasks';
        if($this->mobile_detect->isTablet()){
            $data['main_content']    = 'daily_planner/index_tablet.php';
        }else if ($this->mobile_detect->isMobile()) {
            $data['main_content']    = 'daily_planner/index_mobile.php';
        }else{
            $data['main_content']    = 'daily_planner/index_desktop.php';
        }
        $this->load->view('inc_v2/template', $data);
    }

    public function get_monthly_tasks(){
        $input = $this->input->post();
        $result = $this->planner_model->get_monthly_tasks($input['start_date'], $input['end_date']);
        echo json_encode($result);
    }

    public function add_mytask(){
        $input=$this->input->post();
        $result = $this->planner_model->add_mytask($input);
        echo json_encode($result);
    }

    public function update_calendar_task_details(){
        $input=$this->input->post();
        $result = $this->planner_model->update_calendar_task_details($input);
        echo json_encode($result);
    }

    public function update_task_timings_calendar() {
        $input = $this->input->post();
        $result = $this->planner_model->update_task_timings_calendar($input);
        echo json_encode($result);
    }

    public function delete_calendar_task(){
        $input=$this->input->post();
        $result = $this->planner_model->delete_calendar_task($input);
        echo json_encode($result);
    }

    public function get_notes() {
        $date = $this->input->post('date');
        $showArchived = $this->input->post('showArchived') == 'true';
        $notes = $this->planner_model->get_notes_by_date($date, $showArchived);
        echo json_encode($notes);
    }

    public function save_note() {
        $date = $this->input->post('date');
        $content = $this->input->post('content');
        $color = $this->input->post('noteColor');
        
        $note_id = $this->planner_model->save_note($date, $content, $color);
        echo json_encode(['success' => $note_id]);
    }

    public function edit_note() {
        $id = $this->input->post('id');
        $content = $this->input->post('content');
        $color = $this->input->post('color');
        $color = strtoupper($color);
        $result = $this->planner_model->edit_note($id, $content, $color);
        echo json_encode(['success' => $result]);
    }

    public function delete_note() {
        $id = $this->input->post('id');
        $result = $this->planner_model->delete_note($id);
        echo json_encode(['success' => $result]);
    }

    public function archive_note() {
        $noteId = $this->input->post('id');
        
        $data = [
            'status' => 'archived'
        ];
        
        $this->planner_model->update_note_status($noteId, $data);
        echo json_encode(['status' => 'success']);
    }

    public function unarchive_note() {
        $noteId = $this->input->post('id');
        $data = [
            'status' => 'active'
        ];
        
        $this->planner_model->update_note_status($noteId, $data);
        echo json_encode(['status' => 'success']);
    }

    public function getDailyTasks(){
        $input = $this->input->post();
        $result = $this->planner_model->getDailyTasks($input);
        echo json_encode($result);
    }
}