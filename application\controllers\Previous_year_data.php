<?php defined('BASEPATH') or exit('No direct script access allowed');

class Previous_year_data extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('dashboard_model');
        $this->load->model('previous_year_model');
        $this->load->model('parent_model');
        $this->load->model('communication/circular_inbox_model');
        $this->load->library('filemanager');
        $this->load->model('feesv2/fees_student_model');
    }

    public function index(){
        $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['studentData'] = $this->previous_year_model->get_previous_year_student_details($studentId);

        $studentId   = $this->parent_model->getStudentIdOfLoggedInParent();
        $parentId = $this->authorization->getAvatarStakeHolderId();

        $data['history'] = $this->parent_model->get_fee_transaction_history($studentId);
        // echo "<pre>"; print_r($data['history']); die();
        $display_receipts = $this->settings->getSetting('display_receipts_in_parent');
        if ($display_receipts == 0) {
            $data['display_receipts'] = FALSE;
        } else {
            $data['display_receipts'] = TRUE;
        }
        $data['marksCards'] = $this->parent_model->getMarksCards($studentId);

        $data['showCircularsNew'] = $this->settings->isParentModuleEnabled('CIRCULARS_V2');
        $data['showSMSCount'] = $this->settings->isParentModuleEnabled('TEXTING');
        if ($data['showCircularsNew']) $data['circular_desktop_new'] = $this->dashboard_model->circular_data_desktop_new($parentId);

        if($this->settings->isParentModuleEnabled('CIRCULARS_V2')) {
            $data['circular_new'] = $this->parent_model->getCircularNew($parentId);
        }
        if($this->settings->isParentModuleEnabled('TEXTING')) {
            $data['texts'] = $this->parent_model->getTextsInfo($parentId);
        }
        if($this->settings->isParentModuleEnabled('FEESV2')) {
            $data['fee_status'] = $this->parent_model->get_status_fee_payment($studentId);
        }
        $data['dashboard'] = true;
        if ($this->mobile_detect->isTablet()) {
            $data['main_content']    = 'dashboard/parent/previous/index_mobile';
        }else if($this->mobile_detect->isMobile()) {
            $data['main_content']    = 'dashboard/parent/previous/index_mobile';
        }else{
            $data['main_content']    = 'dashboard/parent/previous/index_desktop';
        }
        $this->load->view('inc/template_previous', $data);  
    }

    public function previous_year_student_texts(){
        if (!$this->settings->isParentModuleEnabled('TEXTING')) {
            redirect('dashboard', 'refresh');
        }
        
        $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
        $parentId = $this->authorization->getAvatarStakeHolderId();
        $data['studentData'] = $this->previous_year_model->get_previous_year_student_details($studentId);
        $data['acadYears'] = $this->parent_model->get_acad_year_id_from_text();
        $data['unread_count'] = $this->parent_model->getTextsInfo($parentId)->textCount;
        // $data['old_sms'] =  $this->parent_model->get_smsStudentIdwise($studentId);
        $data['sid'] = 0;
        if(isset($_POST['sid'])) {
            $data['sid'] = $_POST['sid'];
        }
        $this->load->library('pagination');
        $uri = ($this->uri->segment(3)) ? $this->uri->segment(3) : 0;
        if ($uri != '0') {
            $data['category'] = $uri;
        }else{
            $data['category'] = 'all';
            if(isset($_POST['category_sms_notification'])) {
                $data['category'] = $_POST['category_sms_notification'];
            }
        }

        $uriAcadYear = ($this->uri->segment(4)) ? $this->uri->segment(4) : 0;
        if ($uriAcadYear != '0') {
            $data['academic_year'] = $uriAcadYear;
        }else{
            if (isset($_POST['academic_year'])) {
                $data['academic_year'] = $_POST['academic_year'];
            }else{
                $data['academic_year'] =$this->acad_year->getAcadYearId();
            }
        }
        $config = array();
        $config['_attributes'] =
        $config['base_url'] = site_url()."/previous_year_data/previous_year_student_texts/".$data['category'].'/'.$data['academic_year'];
        $config['total_rows'] = $this->parent_model->get_textsStudentIdwise_num_rows($studentId, $parentId, $data['category'], $data['academic_year']);
        $config["per_page"] = 20;
        $config['full_tag_open'] = "<ul class='pagination mt-4 d-flex justify-content-end'>";
        $config['full_tag_close'] ="</ul>";
        $config['num_tag_open'] = '<li>';
        $config['num_tag_close'] = '</li>';
        $config['cur_tag_open'] = "<li class='disabled'><li class='active'><a href='#'>";
        $config['cur_tag_close'] = "<span class='sr-only'></span></a></li>";
        $config['next_tag_open'] = "<li>";
        $config['next_tagl_close'] = "</li>";
        $config['prev_tag_open'] = "<li>";
        $config['prev_tagl_close'] = "</li>";
        $config['first_tag_open'] = "<li>";
        $config['first_tagl_close'] = "</li>";
        $config['last_tag_open'] = "<li>";
        $config['last_tagl_close'] = "</li>";
        $this->pagination->initialize($config);
        $data['studentwise_sms'] =  $this->parent_model->get_textsStudentIdwise($studentId, $parentId, $data['category'], $config["per_page"], $data['academic_year']);
        $data["links"] = $this->pagination->create_links();

        $data['main_content']    = 'dashboard/parent/previous/texts_index';
        $this->load->view('inc/template_previous', $data);   
    }

    public function previous_year_student_texts_circular($show_unread_only=0){
        $data['parent_id'] = $this->authorization->getAvatarStakeHolderId();
        $data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
        $modules = json_decode($this->settings->getSetting('deactivation_modules'));

        $is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($data['student_id']);
        $data['acadYears'] = $this->circular_inbox_model->get_acad_year_id_from_circular();
        $data['is_deactivated'] = 0;
        if($is_deactivated && in_array('Circulars', $modules)) {
            $data['is_deactivated'] = 1;
        }
        $avatar_type = 2;
        $data['category'] = 'latest';
        $data['cid'] = 0;
        if(isset($_POST['category_name'])) {
            $data['category'] = $_POST['category_name'];
        }
        if(isset($_POST['cid'])) {
            $data['cid'] = $_POST['cid'];
        }
        $data['academic_year'] = 0;
        if (isset($_POST['academic_year'])) {
            $data['academic_year'] = $_POST['academic_year'];
        }else if (!empty($data['acadYears'])) {
            $data['academic_year'] = $data['acadYears'][0]->acad_year_id;
        }
        $data['show_unread_only'] = $show_unread_only;
        $this->load->model('communication/circular_model', 'circular');

        $data['categories'] = $this->circular->getCircularCategories();
        $data['circulars'] = $this->circular_inbox_model->getCircularsAndEmails($data['parent_id'], $avatar_type, $data['category'], $data['academic_year']);
        $data['studentData'] = $this->previous_year_model->get_previous_year_student_details($data['student_id']);
        $data['main_content']    = 'dashboard/parent/previous/circular_index';
        $this->load->view('inc/template_previous', $data);   
    }

    public function previous_help_support(){
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'helptext/previous_about_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'helptext/previous_about_mobile';
        }else{
          $data['main_content']    = 'helptext/previous_about_desktop';          
        }
        $data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['studentData'] = $this->previous_year_model->get_previous_year_student_details($data['student_id']);
        $this->load->view('inc/template_previous', $data);
    }
    public function previous_notification_test(){
        $data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['studentData'] = $this->previous_year_model->get_previous_year_student_details($data['student_id']);
        $data['main_content']    = 'helptext/previous_notification_staff_mobile';
        $this->load->view('inc/template_previous', $data);
    }
    
    public function previous_submit_notification(){
        $this->load->helper('notification_helper');
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $title = 'Notification';
        $message = 'Notification test';
        $url = site_url('previous_year_data/previous_notification_test');
        $res = sendStaffNotifications([$staffId], $title, $message, $url);
        $response = $res['response'];
        trigger_error(json_encode($res));
        $data['status'] = '';
        if($response['status'] == 1) {
            $result = json_decode($response['message']);
            if($result->success == 1) {
                $data['status'] = 'Notification sent successfully';
                $this->session->set_flashdata('flashSuccess', 'Successfully sent');
            } else {
                $data['status'] = 'Failed';
                $this->session->set_flashdata('flashError', 'Sending notification failed');
            }
        } else {
            $data['status'] = 'Failed';
            $this->session->set_flashdata('flashError', 'Sending notification failed');
        }
        redirect('previous_year_data/previous_notification_test');
    }

    public function previous_year_fee_history(){
        $data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['studentData'] = $this->previous_year_model->get_previous_year_student_details($data['student_id']);
        $data['main_content']    = 'parent/feesv2/previous_index';
        $this->load->view('inc/template_previous', $data);
    }

    public function previous_fees_view_history(){
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['history'] = $this->parent_model->get_fee_transaction_history($student_id);
        $data['studentData'] = $this->previous_year_model->get_previous_year_student_details($student_id);
        $display_receipts = $this->settings->getSetting('display_receipts_in_parent');
        if ($display_receipts == 0) {
            $data['display_receipts'] = FALSE;
        } else {
            $data['display_receipts'] = TRUE;
        }
        $data['main_content'] = 'parent/feesv2/previous_view_history';
        $this->load->view('inc/template_previous', $data);
    }

    public function view_previous_report_card(){
        if (!$this->settings->isParentModuleEnabled('MARKS_CARD')) {
            redirect('dashboard', 'refresh');
        }
        $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
        $modules = json_decode($this->settings->getSetting('deactivation_modules'));
        $is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($studentId);
        // echo "<pre>"; print_r($is_deactivated); die();
        
        $data['is_deactivated'] = 0;
        if($is_deactivated && in_array('Report Cards', $modules)) {
            $data['is_deactivated'] = 1;
        }
        $data['marksCards'] = $this->parent_model->getMarksCards($studentId);
        $data['studentData'] = $this->previous_year_model->get_previous_year_student_details($studentId);
        $data['main_content'] = 'parent/marks_cards/previous_index';
        $this->load->view('inc/template_previous', $data);
    }

    public function previous_marksCardInfo($cardId){
        $data['cardDetails'] = $this->parent_model->getCardDetails($cardId);
        if ($this->mobile_detect->isTablet()) {
           $data['main_content']   = 'parent/marks_cards/previous_markscard_more_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'parent/marks_cards/previous_markscard_more';
        }
        $this->load->view('inc/template_previous', $data);
    }

    public function previous_view_text(){
            if (!$this->settings->isParentModuleEnabled('TEXTING')) {
            redirect('dashboard', 'refresh');
        }
        $id = $_POST['master_id'];
        $is_read = $_POST['is_read'];
        $parent_text_id = $_POST['parent_text_id'];
        // echo "<pre>"; print_r($_POST); die();
        $data['sms'] =  $this->parent_model->getTextDetail($id);
        $parent_id = $this->authorization->getAvatarStakeHolderId();
        $data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['studentData'] = $this->previous_year_model->get_previous_year_student_details($data['student_id']);
        if(!$is_read) {
            $this->parent_model->makeTextRead($parent_text_id);
        }

        if ($this->mobile_detect->isTablet()) {
          $data['main_content']    = 'parent/notification/texts/previous_tablet_sms_view';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content']    = 'parent/notification/texts/previous_mobile_sms_view';
        }else{
          $data['main_content']    = 'parent/notification/texts/previous_sms_view';      
        }
        $this->load->view('inc/template_previous', $data);
    }

    public function previous_year_circular_view() {
        if(!isset($_POST['circular_id'])) {
            redirect('parent/Circular_inbox');
        }
        $data['circular_id'] = $_POST['circular_id'];
        $data['parent_circular_id'] = isset($_POST['parent_circular_id'])?$_POST['parent_circular_id']:'';
        $data['is_read'] = $_POST['is_read'];
        $data['category'] = isset($_POST['category'])?$_POST['category']:'latest';
        $data['circular'] = 'new';
        $data['circularData'] = $this->circular_inbox_model->getCircularDeatils($data['circular_id'], 0);

        if ($this->mobile_detect->isTablet()) {
            $data['main_content']    = 'parent/notification/circularsv2/previous_view_tablet';   
        }else if($this->mobile_detect->isMobile()){
            $data['main_content']    = 'parent/notification/circularsv2/previous_view_mobile';   
        }else{
            $data['main_content']    = 'parent/notification/circularsv2/previous_view_desktop';      
        }
        $this->load->view('inc/template_previous', $data);
    }

}
