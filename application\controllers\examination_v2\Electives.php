<?php

class Electives extends CI_Controller {
	private $yearId;
  	function __construct() {
	    parent::__construct();
	    if (!$this->ion_auth->logged_in()) {
	      redirect('auth/login', 'refresh');
	    }
	    $this->yearId = $this->acad_year->getAcadYearId();
	    $this->load->model('student/Electives_model', 'elective_master');
	    $this->load->model('examination_v2/Elective_model', 'elective_model');
	    $this->load->model('class_section');
  	}

  	public function index() {
  		$data['classes'] = $this->class_section->getAllClassess();
  		$data['electives'] = $this->elective_model->getElectives($this->yearId);
  		$data['groups'] = $this->elective_master->getElectiveMasterGroupNames();
  		// echo "<pre>"; print_r($data['electives']); die();
  		$data['main_content'] = 'examination_v2/electives/index';
    	$this->load->view('inc/template', $data);
  	}

  	public function get_elective_subjects_of_class_group() {
  		$class_id = $_POST['class_id'];
  		$elective_group_id = $_POST['elective_group_id'];
  		$data['class_subjects'] = $this->elective_model->get_elective_subjects_of_class_group($class_id, $elective_group_id);
  		echo json_encode($data);
  	}

  	public function save_elective_group() {
  		$status = $this->elective_model->save_elective_group();
  		echo $status;
  	}

  	public function assign_elective_students() {
  		$elective_group_id = $_POST['elective_group_id'];
  		$data['elective'] = $this->elective_model->get_elective_group($elective_group_id);
  		$data['students'] = $this->elective_model->getClassStudents($data['elective']->class_id);
  		$master_electives = $this->elective_model->getElectiveMasterData($data['elective']->class_id, $data['elective']->master_subject_ids);
  		$assigned_electives = $this->elective_model->getAssignedElectives($elective_group_id);
  		$data['elective_subjects'] = $this->elective_model->getElectivesSubjects($elective_group_id);
  		foreach($data['students'] as $key => $value) {
  			$data['students'][$key]->assigned_elective_group_subject_id = 0;
  			$data['students'][$key]->assigned_elective_subject_name = '-';
  			$data['students'][$key]->assigned_elective_id = 0;
  			$data['students'][$key]->master_elective_subject_id = 0;
  			if(array_key_exists($value->student_id, $assigned_electives)) {
  				$data['students'][$key]->assigned_elective_group_subject_id = $assigned_electives[$value->student_id]->elective_group_subject_id;
  				$data['students'][$key]->assigned_elective_id = $assigned_electives[$value->student_id]->id;
  				$data['students'][$key]->assigned_elective_subject_name = $assigned_electives[$value->student_id]->subject_name;
  			}
  			if(array_key_exists($value->student_id, $master_electives)) {
  				$data['students'][$key]->master_elective_subject_id = $master_electives[$value->student_id];
  			}
  		}

  		// echo '<pre>'; print_r($data); die();
  		$data['main_content'] = 'examination_v2/electives/elective_students';
    	$this->load->view('inc/template', $data);
  	}

  	public function update_student_elective() {
  		$status = $this->elective_model->update_student_elective();
  		echo $status;
  	}
}