<?php

class Send_controller extends CI_Controller {

	function __construct() {
		parent::__construct();
	}

	// profile
	public function index() {
        $data['allMsgs'] = $this->db->select('*')->from('tbl_msg')->get()->result_array();
        $data['main_content'] = 'socket/index.php';
        $this->load->view('inc/template', $data);
    }

    public function send() {
        $arr['msg'] = $this->input->post('message');
        $arr['date'] = date('Y-m-d');
        $arr['status'] = 1;
        $this->db->insert('tbl_msg', $arr);

        $detail = $this->db->select('*')->from('tbl_msg')->where('id', $this->db->insert_id())->get()->row();
        $msgcount = $this->db->select('*')->from('tbl_msg')->get()->num_rows();

        $arr['message'] = $detail->msg;
        $arr['date'] = date('m-d-Y', strtotime($detail->date));
        $arr['msgcount'] = $msgcount;
        $arr['success'] = true;
        echo json_encode($arr);
    }
}
