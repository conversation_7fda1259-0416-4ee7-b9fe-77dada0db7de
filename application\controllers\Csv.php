<?php

class Csv extends CI_Controller {         
	function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isSuperAdmin()) {
      redirect('dashboard', 'refresh');
    }
     $this->load->library('csvimport'); 
	   $this->load->model('enquiry_model');
	   $this->load->model('library_model');
	}

	public function Kolkata_datetime()
 	{
	    $timezone = new DateTimeZone("Asia/Kolkata" );
	    $date = new DateTime();
	    $date->setTimezone($timezone );
	    $dtobj = $date->format('Y-m-d h:i:s');
	    return $dtobj;
 	}
	// Csv upload for Student index
	public function student() {
	
	 	$data['main_content']    = 'csv/student';
		$this->load->view('inc/template', $data);
	}

    public function staff() {
        $data['main_content']    = 'csv/staff';
        $this->load->view('inc/template', $data);
    }

	public function upload($filename = '', $upload_path) {
	    $config['upload_path'] = $upload_path;
	    $config['allowed_types'] = 'csv|CSV';
	    $config['remove_spaces'] = true;
	    $config['overwrite'] = false;
	    
	    $this ->load -> library('upload', $config);
	    $this ->upload -> initialize($config);
	    if (!$this ->upload-> do_upload($filename)) {
	      $error = array('status' => 'error', 'data' => $this ->upload-> display_errors());
	      $this ->session ->set_flashdata('flashError', 'Failed to upload - ' . $filename);
	      return $error;
	    } else {
	      $image = $this->upload -> data();
	      $success = array('status' => 'success', 'data' => $image);
	      return $success;
	    }

  	}

  

  	public function student_csv_submit(){
  		$creDir = 'uploads/';
	    if (!is_dir($creDir)) {
	      mkdir($creDir, 0777, TRUE);
	    }

	    if ($_FILES['csv_file']['name'] != "") {
	      $ret_val = $this -> upload('csv_file', $creDir);
	      if ($ret_val['status'] == 'success') {
	        $file_data = $ret_val['data'];
	      } else {
	        $file_data = $ret_val['data'];
	      }
	    }else{
	       $file_data="";
	    }
        $file_path = 'uploads/'.$file_data['file_name'];
	    if ($this->csvimport->get_array($file_path)) {
            $student_arr = $this->csvimport->get_array($file_path);

            foreach ($student_arr as $key => $std) {
                $data[] = array( 
                    'std_first_name' => $std['std_first_name'], 
                    'std_last_name' => $std['std_last_name'], 
                    'admission_no' => $std['admission_no'], 
                    'gender' => $std['gender'], 
                    'father_first_name' => $std['father_first_name'],
                    'father_mobile_no' => $std['father_mobile_no'],
                    'join_acad_year' => $std['join_acad_year'],
                    'class' => $std['class'],
                    'section' => $std['section'],
                    'previous_class' => $std['previous_class'],
                    'previous_section' => $std['previous_section']
                );

            }
            // echo "<pre>"; print_r($data); die(); 
            $result = $this->db->insert_batch('maithry',$data);
            if($result){ 
                $this->session->set_flashdata('flashSuccess', 'Data Upload Successful');
              }
              else{
                $this->session->set_flashdata('flashError', 'Something Went Wrong');
            }
            redirect('csv/student');
     	}
  	}

    public function lbr_books(){
        $data['main_content']    = 'csv/lbr_books';
        $this->load->view('inc/template', $data);
    }

    public function lbr_submit_csv_file($value=''){
        $creDir = 'uploads/';
        if (!is_dir($creDir)) {
          mkdir($creDir, 0777, TRUE);
        }

        if ($_FILES['csv_file']['name'] != "") {
          $ret_val = $this -> upload('csv_file', $creDir);
          if ($ret_val['status'] == 'success') {
            $file_data = $ret_val['data'];
          } else {
            $file_data = $ret_val['data'];
          }
        }else{
           $file_data="";
        }
        $file_path = 'uploads/'.$file_data['file_name'];
        if ($this->csvimport->get_array($file_path)) {
            $lbr_books = $this->csvimport->get_array($file_path);
            foreach ($lbr_books as $key => $books) {
                $data[] = array(
                    // 'book_type'           => (isset($books['book_type']))?$books['book_type']:'',
                    // 'category'            => (isset($books['category']))?$books['category']:'',
                    // 'language'            => (isset($books['language']))?$books['language']:'',
                    'author'              => (isset($books['author']))?$books['author']:'',
                    // 'year_of_publishing'  => isset($books['year_of_publishing'])?$books['year_of_publishing']:null,
                    'publisher'           => (isset($books['publisher_name']))?$books['publisher_name']:'',
                    'book_title'          => $books['book_title'],
                    // 'subject'             =>isset($books['subject'])? $books['subject']:null,
                    'isbn'                => isset($books['isbn'])?$books['isbn']:null,
                    'issn_no'             => isset($books['issn_no'])?$books['issn_no']:null,
                    // 'b_copies'              => (isset($books['b_copies']))?$books['b_copies']:'',
                    'access_code'         => isset($books['access_code'])? $books['access_code']:null,
                );
            }
            $result = $this->db->insert_batch('library_books_ttv',$data); 
            if($result){ 
                $this->session->set_flashdata('flashSuccess', 'Data Upload Successful');
            }else{
                $this->session->set_flashdata('flashError', 'Something Went Wrong');
            }
            redirect('csv/lbr_books');
        }
    }


    public function enquiry_csv_submit(){
        $creDir = 'uploads/';
        if (!is_dir($creDir)) {
          mkdir($creDir, 0777, TRUE);
        }

        if ($_FILES['csv_file']['name'] != "") {
          $ret_val = $this -> upload('csv_file', $creDir);
          if ($ret_val['status'] == 'success') {
            $file_data = $ret_val['data'];
          } else {
            $file_data = $ret_val['data'];
          }
        }else{
           $file_data="";
        }
        $file_path = 'uploads/'.$file_data['file_name'];
        if ($this->csvimport->get_array($file_path)) {
            $enquiry_arr = $this->csvimport->get_array($file_path);
            foreach ($enquiry_arr as $key => $enq) {
                $data = array( 
                    'student_name' => $enq['Student Name'], 
                    'gender' => $enq['Gender'], 
                    'student_dob' => date('Y-m-d',strtotime($enq['Dob'])), 
                    'class_name' => $enq['Class'], 
                    'parent_name' => $enq['Parent Name'],
                    'mobile_number' => $enq['Mobile Number'],
                    'alternate_mobile_number' => $enq['Alternate Number'],
                    'email' => $enq['email id'],
                    'status' => $enq['Status'],
                    'academic_year' => '20',
                    'created_by' => '0',
                    'board' => '',
                    'source' => ($enq['Source'] =='')? 'Unknown' :$enq['Source'],
                    'assigned_to' => $enq['Counselor Name'],
                    'student_current_school' => $enq['Student current school'],
                    'got_to_know_by' => $enq['How they know?'],
                    'next_follow_date'=>date('Y-m-d',strtotime(date('24-02-2020')))
                );
                $this->db->insert('enquiry',$data);
                $insertId = $this->db->insert_id();
                $followData = array( 
                    'follow_up_type' => 'Enquiry', 
                    'follow_up_action' => 'In-person', 
                    'source_id' => $insertId, 
                    'status' => $enq['Status'],
                    'remarks' => $enq['Combined Remarks'],
                    'delivery_status'=>'Delivered',
                    'next_follow_date'=>date('Y-m-d',strtotime(date('24-02-2020'))),
                    'created_by' => '0'
                );
              $result =  $this->db->insert('follow_up',$followData);
            }
            if($result){ 
                $this->session->set_flashdata('flashSuccess', 'Data Upload Successful');
              }
              else{
                $this->session->set_flashdata('flashError', 'Something Went Wrong');
            }
            redirect('enquiry/enquiry_staff/upload_enquiry_csv');
        }
    }

    public function upload_sub_event_file(){
        $file_path =$_FILES['upload_csv']['tmp_name'];
        $subevent_arr = [];
        if ($this->csvimport->get_array($file_path)) {
            $subevent_arr = $this->csvimport->get_array($file_path);
        }
        echo json_encode($subevent_arr);
    }
    
    
    
  

    private  $libraryColums = array(
							'book_type',
							'category',
							'language',
							'author',
							'yearof_publishing',
							'publisher_name',
							'created_on',
							'description',
							'modified_on',
							'last_modified_by',
							'book_title',
							'series',
							'volume',
							'subject',
							'location_book',
							'source',
							'contains',
							'edition',
							'acc_no',
							'isbn',
							'pages',
							'call_number',
							'supplier',
							'bill_no_date',
							'soft_delete',
							'libraries',
							'b_author2',
							'b_sub_title',
							'b_book_keyword',
							'b_copies',
							'shelf_no_of',
							'issn_no',
							'book_course',
							'bill_no',
							'placeof_publishing',
							'volume_number',
							'remarks',
							'costof_book',
							'date_of_accession',
							'status',
							'currency',
              'access_code'

    );


    public function downloadCsvFormat() {
      $csvArray = array();
      foreach ($this->libraryColums as $column) {
        array_push($csvArray, $column);
      }
      
      header("Content-type: application/csv");
          header("Content-Disposition: attachment; filename=\"library_csv".".csv\"");
          header("Pragma: no-cache");
          header("Expires: 0");
  
          $handle = fopen('php://output', 'w');
          fputcsv($handle, $csvArray);
          fclose($handle);
          exit;
    }
    
    function certificate_logos(){
      $data['main_content']    = 'csv/upload_img';
      $this->load->view('inc/template', $data);

    }
    

  
  
    
}
?>
