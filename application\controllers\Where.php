<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Where extends CI_Controller {

  public function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isSuperAdmin()) {
      redirect('dashboard', 'refresh');
    }	
    $this->load->model('timetable/staff_tt');
    $this->load->model('sms_model');
  }

  public function where_student() {
    $data['classes']  =$this->sms_model->get_class();

    
    $data['main_content'] = 'where/where_student';
    $this->load->view('inc/template', $data);
  }

  public function where_staff() {
    $data['staffAll'] = $this->staff_tt->get_allStaff();
    $data['main_content'] = 'where/where_staff';
    $this->load->view('inc/template', $data);
  }

  public function get_section(){
		$class_id =$this->input->post('className');
		$result = $this->sms_model->get_classwiseSection($class_id);
		echo json_encode($result);
  }
  
	public function get_student(){
		$class_id =$this->input->post('className');
		$section_id =$this->input->post('section');
		$result = $this->sms_model->get_studentclassSectionwise($class_id,$section_id);
		echo json_encode($result);
	}
}