<?php
class Afl_reports extends CI_Controller {
    private $yearId;
    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
       redirect('auth/login', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('parent_model');
      $this->load->model('class_section');
      $this->load->library('filemanager');
    }

  public function index(){
    $data['classSectionList'] = $this->class_section->getAllClassSections();
    $data['main_content'] = 'afl/reports/student_report_index';
    $this->load->view('inc/template', $data);
  }

  public function student_report($student_id) {
    $data['student_id'] = $student_id;
    $data['student_data'] = $this->parent_model->getStudentDataById($student_id);
    $data['class_id'] = $this->parent_model->getYearClassId($student_id, $this->yearId);
    $months = $this->parent_model->getAssessments($data['class_id']);
    $data['std_id'] = $student_id;
    $data['assessments'] = array();
    $data['current_month'] = '';
    $data['months'] = array();
    if(!empty($months)) {
      $i = 0;
      foreach ($months as $key => $ass) {
        $data['months'][] = $ass->schedule;
        $data['assessments'][$ass->schedule] = $ass->id;
        if($i++ == 0)
          $data['current_month'] = $ass->schedule;
      }
    }
    $data['main_content'] = 'afl/reports/student_details';
    $this->load->view('inc/template', $data);
  }

  public function cumulative_performance($student_id) {
    $data['student_id'] = $student_id;
    $data['student_data'] = $this->parent_model->getStudentDataById($student_id);
    $classId = $this->parent_model->getYearClassId($student_id, $this->yearId);
    $subjects = $this->parent_model->getAflApplicableSubjects($classId, $student_id);
    $data['subjects'] = array();
    $data['subject_id'] = (!empty($subjects))?$subjects[0]->id:0;
    $data['subject_name'] = (!empty($subjects))?$subjects[0]->subject_name:'';
    foreach ($subjects as $key => $subject) {
      $data['subjects'][] = $subject;
    }
    $data['main_content'] = 'afl/reports/student_cumulative_report';
    $this->load->view('inc/template', $data);
  }

  public function getSubjectPerformance() {
    $subject_id = $_POST['subject_id'];
    $data['subject'] = $this->parent_model->getSubjectName($subject_id);
    $student_id = $_POST['student_id'];
    $assessments = $this->parent_model->getAflAssessments($subject_id, $student_id);
    $data['grading_scale'] = array();
    if(!empty($assessments)) 
      $data['grading_scale'] = $this->parent_model->getGradingScale($assessments[0]->afl_grading_scale_id);
    $assessment_subject_ids = array();
    foreach ($assessments as $key => $ass) {
      array_push($assessment_subject_ids, $ass->assessment_subject_id);
    }
    $parameters = $this->parent_model->getSubjectPerfParameters($assessment_subject_ids, $student_id);

    //list of graph line colors to choose from
    $colors = ['#82b74b', '#3e4444', '#d64161', '#405d27', '#6b5b95', '#feb236', '#ff7b25', '#c1946a'];
    $gData = array();
    $data['xKeys'] = array();
    $data['yKeys'] = array();
    $data['pointer_names'] = array();
    $pointer_names = array();
    $data['colors'] = array();
    $perf_pointers = array();
    $marksArray = array();
    foreach ($assessments as $key => $assessment) {
      $i=1;
      list($month, $year) = explode(" - ", $assessment->schedule);
      // array_push($data['xKeys'], trim($month));
      array_push($data['xKeys'], trim($assessment->schedule));
      $pMarks = array();
      // $pMarks['month'] = $month;
      $pMarks['month'] = $assessment->schedule;
      foreach ($parameters as $key => $parameter) {
        if(!in_array($parameter['perf_pointer_id'], $perf_pointers)) {
          array_push($perf_pointers, $parameter['perf_pointer_id']);
          array_push($data['yKeys'], 'Parameter'.$i);
          $rand_color = $colors[$i-1];
          $pointer_names[$parameter['perf_pointer_id']] = array('short_name' => 'Parameter'.$i,'full_name' => $parameter['pointer_name'], 'color'=> $rand_color);
          array_push($data['colors'], $rand_color);
          $i++;
        }

        if($assessment->assessment_subject_id == $parameter['assessment_subject_id']) {
          $pMarks[$pointer_names[$parameter['perf_pointer_id']]['short_name']] = ($parameter['evaluated_marks'] == -2 || $parameter['evaluated_marks'] == -3)?null:$parameter['evaluated_marks'];
          $marksArray[$assessment->schedule][$pointer_names[$parameter['perf_pointer_id']]['short_name']] = array('self_marks' => ($parameter['self_marks'] == -2 || $parameter['self_marks'] == -3)?null:$parameter['self_marks'], 'evaluated_marks' => ($parameter['evaluated_marks'] == -2 || $parameter['evaluated_marks'] == -3)?null:$parameter['evaluated_marks']);
        }
      }
      $gData[] = $pMarks;
    }

    foreach ($pointer_names as $key => $pName) {
      array_push($data['pointer_names'], $pName);
    }
    $data['graph_data'] = $gData;
    $data['marks_data'] = $marksArray;
    echo json_encode($data);
  }

  public function month_summary($assessment_id, $student_id) {
    $data['assessment_id'] = $assessment_id;
    $data['student_id'] = $student_id;
    $this->load->model('afl/afl_assessment_model', 'assessment_model');
    $data['student_data'] = $this->parent_model->getStudentDataById($student_id);
    $data['assessment'] = $this->assessment_model->getAssessmentDetail($assessment_id);
    $data['grading_scale'] = $this->parent_model->getGradingScale($data['assessment']->afl_grading_scale_id);
    $subjects = $this->parent_model->getMonthSummary($assessment_id, $student_id);
    //list of graph line colors to choose from
      $colors = ['#82b74b', '#3e4444', '#d64161', '#405d27', '#6b5b95', '#feb236', '#ff7b25', '#c1946a'];
      $gData = array();
      $data['yKeys'] = array();
      $data['colors'] = array();
      $data['pointer_names'] = array();
      $pointer_names = array();
      $marksArray = array();
    foreach ($subjects as $subject_id => $subject) {
      $subject_name = $subject['subject_name'];
      $subMarks = array();
      $pointer_names[$subject_name] = array();
      $marksArray[$subject_name] = array();
      $subMarks['subject'] = $subject_name;
      $parameters = $subject['parameters'];
      $i=1;
      foreach ($parameters as $key => $parameter) {
        $para_name = 'Parameter'.$i;
        $rand_color = $colors[$i-1];
        if(!in_array($para_name, $data['yKeys'])) {
          array_push($data['yKeys'], $para_name);
          array_push($data['colors'], $rand_color);
        }
        $pointer_names[$subject_name][] = array('short_name' => $para_name,'full_name' => $parameter->pointer_name, 'color'=> $rand_color);
        $subMarks[$para_name] = ($parameter->evaluated_marks == -2 || $parameter->evaluated_marks == -3)?null:$parameter->evaluated_marks;
        $marksArray[$subject_name][$para_name] = array('self_marks' => ($parameter->self_marks == -2 || $parameter->self_marks == -3)?null:$parameter->self_marks, 'evaluated_marks' => ($parameter->evaluated_marks == -2 || $parameter->evaluated_marks == -3)?null:$parameter->evaluated_marks);
        $i++;
      }
      $gData[] = $subMarks;
    }
    $data['graph_data'] = $gData;
      $data['marks_data'] = $marksArray;
      $data['pointer_names'] = $pointer_names;
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'afl/reports/month_performance';
    $this->load->view('inc/template', $data);
  }

  public function performance($assessment_subject_id, $student_id) {
      // echo $assessment_subject_id; die();
      $data['assessment_subject_id'] = $assessment_subject_id;
      $data['student_id'] = $student_id;
      // $stdData = $this->parent_model->getClassIdOfLoggedInParent();
      $stdData = $this->parent_model->getStudentDataById($student_id);
      $data['assessment'] = $this->parent_model->getAssessmentDetailByAssSub($assessment_subject_id);
    $data['subject'] = $this->parent_model->getSubjectDetails($assessment_subject_id);
    $this->load->model('afl/afl_grading_model', 'grading_model');
    $this->load->model('afl/afl_assessment_model', 'assessment_model');
    $this->load->model('afl/afl_marks_model', 'marks_model');
      if($this->mobile_detect->isMobile()) {
        $garde_descriptions = $this->parent_model->getGradeDescriptions($assessment_subject_id);
        $data['marksData'] = $this->parent_model->getStudentMarksData($stdData->stdId, $assessment_subject_id);
        if(empty($data['marksData'])) {
          $data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($data['assessment']->afl_grading_scale_id);
        $data['parameters'] = $this->assessment_model->getSubjectPerfParameters($assessment_subject_id);
        foreach ($data['parameters'] as $para_id => $parameter) {
          $std = new stdClass();
            $std->self_marks = 'NA';
                      $std->perf_parameter_id = $para_id;
                      $std->evaluated_marks = 'NA';
                      $std->pointer_name = $parameter['pointer_name'];
                      $std->description = $parameter['parameter_description'];
                      $std->self_range = '';
                      $std->self_desc = '';
                      $std->evaluated_range = '';
                      $std->evaluated_desc = '';          
                      $data['marksData'][] = $std;
        }
        } else {
          foreach ($data['marksData'] as $key => $marks) {
            $descriptions = $garde_descriptions[$marks->perf_parameter_id];
            $marks->self_range = '';
            $marks->self_desc = '';
            $marks->evaluated_range = '';
            $marks->evaluated_desc = '';
            foreach ($descriptions as $key => $desc) {
              if($marks->self_marks >= (double)$desc->start_range && $marks->self_marks <= (double)$desc->end_range) {
                $marks->self_desc = $desc->grade_description;
                $marks->self_range = $desc->range_name;
              }
              if($marks->evaluated_marks >= (double)$desc->start_range && $marks->evaluated_marks <= (double)$desc->end_range) {
                $marks->evaluated_desc = $desc->grade_description;
                $marks->evaluated_range = $desc->range_name;
              }
              if($marks->self_marks == -2 || $marks->self_marks == -3) {
                $marks->self_marks = 'NA';
              } else {
                $marks->self_marks = (float) $marks->self_marks;
              }
              if($marks->evaluated_marks == -2 || $marks->evaluated_marks == -3) {
                $marks->evaluated_marks = 'NA';
              } else {
                $marks->evaluated_marks = (float) $marks->evaluated_marks;
              }
            }
          }
        }
      } else {
        $data['grading_scale_values'] = $this->grading_model->getGradingScaleValues($data['assessment']->afl_grading_scale_id);
        $data['parameters'] = $this->assessment_model->getSubjectPerfParameters($assessment_subject_id);
        $data['marks'] = $this->marks_model->getStudentMarks($assessment_subject_id, $stdData->stdId);
      }
      $data['student'] = $stdData;

      // echo "<pre>"; print_r($data); die();
      $school = $this->settings->getSetting('school_short_name');
      $data['main_content'] = "afl/report_templates/$school";
      $this->load->view('inc/template', $data);
    }

}