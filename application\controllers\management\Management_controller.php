<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Management_controller extends CI_Controller{
	
	public function __construct() {
       parent::__construct();
       if (!$this->ion_auth->logged_in()) {
         redirect('auth/login', 'refresh');
         }
         $this->load->model('Otherlinks_model');
	}

    public function index(){
       /*$data['isBulidingMasterPermitted'] = $this->authorization->isAuthorized('BUILDING_MASTER.MODULE');
       $data['isEventsPermitted'] = $this->authorization->isAuthorized('SCHOOL_CALENDAR.MODULE');
       $data['permitExpenses'] = $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.MODULE');
       $data['permitPayroll'] = $this->authorization->isModuleEnabled('PAYROLL') && $this->authorization->isAuthorized('PAYROLL.MODULE');
       $data['permitAdditionalIncome'] = $this->authorization->isModuleEnabled('ADDITIONAL_INCOME') && $this->authorization->isAuthorized('ADDITIONAL_INCOME.MODULE');
       $data['permitInventory'] = $this->authorization->isModuleEnabled('INVENTORY') && $this->authorization->isAuthorized('INVENTORY.MODULE');
       $data['permitPurchase'] = $this->authorization->isModuleEnabled('PURCHASE') && $this->authorization->isAuthorized('PURCHASE.MODULE');
       $data['isAssetPermitted'] = $this->authorization->isModuleEnabled('ASSETS') && $this->authorization->isAuthorized('ASSETS.MODULE');
       $data['permitVendor'] = $this->authorization->isModuleEnabled('VENDOR') && $this->authorization->isAuthorized('VENDOR.MODULE');
       $data['permitShifts'] = $this->authorization->isAuthorized('STAFF.ATTENDANCE_SHIFTS');
       $data['idcards']=$this->authorization->isAuthorized('IDCARDS.MODULE');*/

       $site_url = site_url();
       $temp = 0;
       if (strpos($site_url, 'demoschool') !== false) {
          $temp = 1;
       }
        $data['tiles'] = array(
          // [
          //   'title' => 'Payroll',
          //   'sub_title' => 'Manage paryoll of your staff',
          //   'icon' => 'svg_icons/payroll.svg',
          //   'url' => $site_url.'management/payroll',
          //   'permission' => $this->authorization->isModuleEnabled('PAYROLL') && $this->authorization->isAuthorized('PAYROLL.MODULE')
          // ],
          [
            'title' => 'Inventory',
            'sub_title' => 'Manage Inventory',
            'icon' => 'svg_icons/inventory.svg',
            'url' => $site_url.'management/Management_controller/inventory',
            'permission' => $this->authorization->isModuleEnabled('INVENTORY') && $this->authorization->isAuthorized('INVENTORY.MODULE')
          ],
          [
            'title' => 'Events / Holidays',
            'sub_title' => 'Manage Events/Holidays',
            'icon' => 'svg_icons/calendar.svg',
            'url' => $site_url.'calender_events/calenderevents/index',
            'permission' => $this->authorization->isAuthorized('SCHOOL_CALENDAR.MODULE')
          ],
          [
            'title' => 'Other Links',
            'sub_title' => 'Manage Other Links',
            'icon' => 'svg_icons/calendar.svg',
            'url' => $site_url.'management/management_controller/other_links',
            'permission' => $this->authorization->isAuthorized('OTHERLINKS.MODULE')
          ],
          [
            'title' => 'Purchase',
            'sub_title' => 'Purchase items',
            'icon' => 'svg_icons/purchase.svg',
            'url' => $site_url.'management/purchase',
            'permission' => $this->authorization->isAuthorized('BUILDING_MASTER.MODULE')
          ],
          [
            'title' => 'Additional Income',
            'sub_title' => 'Record anyadditional Income to the school',
            'icon' => 'svg_icons/additionalincome.svg',
            'url' => $site_url.'management/additional_income',
            'permission' => $this->authorization->isAuthorized('BUILDING_MASTER.MODULE')
          ],
          [
            'title' => 'Vendor Master',
            'sub_title' => 'Manage Vendor Data',
            'icon' => 'svg_icons/vendormaster.svg',
            'url' => $site_url.'inventory/vendor_controller',
            'permission' => $this->authorization->isModuleEnabled('VENDOR') && $this->authorization->isAuthorized('VENDOR.MODULE')
          ],
          [
            'title' => 'Attendance Shifts',
            'sub_title' => 'Manage attendance shifts',
            'icon' => 'svg_icons/attendance.svg',
            'url' => $site_url.'staff/staff_attendance/shifts',
            'permission' => $this->authorization->isAuthorized('BUILDING_MASTER.MODULE')
          ],
          [
            'title' => 'ID Cards',
            'sub_title' => 'Manage the Idcards',
            'icon' => 'svg_icons/visitor.svg',
            'url' => $site_url.'idcards/Idcards_controller',
            'permission' => $this->authorization->isAuthorized('BUILDING_MASTER.MODULE')
          ],
          [
            'title' => 'Help Desk',
            'sub_title' => 'Manage the Help Desk',
            'icon' => 'svg_icons/faq.svg',
            'url' => $site_url.'management/Helpdesk_controller',
            'permission' => $temp && $this->authorization->isSuperAdmin()
          ],
          [
            'title' => 'Server Storage',
            'sub_title' => 'View the Server Storage Details',
            'icon' => 'svg_icons/serverstorage.svg',
            'url' => $site_url.'s3_storage',
            'permission' => $this->authorization->isSuperAdmin()
            ]

      );
      $data['tiles'] = checkTilePermissions($data['tiles']);
      
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'management/menu_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'management/menu_mobile';
      }else{
        $data['main_content'] = 'management/menu';    	
      }

       
       $this->load->view('inc/template', $data);
    }

    public function inventory(){
      $site_url = site_url();
      $data['tiles'] = array(
          [
            'title' => 'Product Master',
            'sub_title' => 'Manage products data',
            'icon' => 'svg_icons/emptyapplicationform.svg',
            'url' => $site_url.'inventory/product_controller',
            'permission' => 1
          ],
          [
            'title' => 'Invoice',
            'sub_title' => 'Add / View Invoices',
            'icon' => 'svg_icons/receipttemplate.svg',
            'url' => $site_url.'inventory/invoice_controller',
            'permission' => 1
          ],
          [
            'title' => 'Allocate Products',
            'sub_title' => 'Allocate products to staff',
            'icon' => 'svg_icons/allocateproducts.svg',
            'url' => $site_url.'inventory/product_controller/allocate_to_staff',
            'permission' => 1
          ]
      );
      $data['tiles'] = checkTilePermissions($data['tiles']);

      $data['report_tiles'] = array(
          [
            'title' => 'Inventory Threshold',
            'sub_title' => 'View Inventory Threshold Report',
            'icon' => 'svg_icons/assessment.svg',
            'url' => $site_url.'inventory/stock_controller/stock_threshold',
            'permission' => 1
          ],
          [
            'title' => 'Product Allocation',
            'sub_title' => 'Report of product allocated to staff',
            'icon' => 'svg_icons/productallocation.svg',
            'url' => $site_url.'inventory/product_controller/inventory_allocation_report',
            'permission' => 1
          ],
          [
            'title' => 'Invoice',
            'sub_title' => 'Report of added invoices',
            'icon' => 'svg_icons/invoice.svg',
            'url' => $site_url.'inventory/invoice_controller/invoice_report',
            'permission' => 1
          ],
          [
            'title' => 'Inventory Ledger',
            'sub_title' => 'View Inventory Ledger',
            'icon' => 'svg_icons/inventoryledger.svg',
            'url' => $site_url.'inventory/product_controller/inventoty_transaction_report',
            'permission' => 1
          ]
      );
      $data['report_tiles'] = checkTilePermissions($data['report_tiles']);


       $data['main_content'] = 'management/inventory';
       $this->load->view('inc/template', $data);
    }

  public function Buliding_master() {
    $site_url = site_url();
      $data['tiles'] = array(
          [
            'title' => 'Building Info',
            'sub_title' => 'Add/Edit Building info',
            'icon' => 'svg_icons/buildinginfo.svg',
            'url' => $site_url.'building_map/building_controller',
            'permission' => 1
          ],
          [
            'title' => 'Allocate Rooms',
            'sub_title' => 'Allocate rooms to class sections',
            'icon' => 'svg_icons/school.svg',
            'url' => $site_url.'building_map/building_controller/allocate_rooms',
            'permission' => 1
          ]          
      );
      $data['tiles'] = checkTilePermissions($data['tiles']);

	  $data['main_content'] = 'school_admin/building_master';
  	$this->load->view('inc/template', $data);
	}

	public function Asset_management() {
    $site_url = site_url();
    $data['tiles'] = array(
          [
            'title' => 'Asset Category',
            'sub_title' => 'Add New Asset Category',
            'icon' => 'svg_icons/assestcategory.svg',
            'url' => $site_url.'management/asset_controller/assetCategory',
            'permission' => 1
          ],
          [
            'title' => 'Add assets',
            'sub_title' => 'Adding and allocating the assets',
            'icon' => 'svg_icons/add.svg',
            'url' => $site_url.'management/asset_controller/old_add_asset',
            'permission' => 1
          ],
          [
            'title' => 'View Asset',
            'sub_title' => 'View Current Assets',
            'icon' => 'svg_icons/view.svg',
            'url' => $site_url.'management/asset_controller/category_wise_report',
            'permission' => 1
          ]
        );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
          [
            'title' => 'Asset Allocation Report',
            'sub_title' => '',
            'icon' => 'svg_icons/usermanagement.svg',
            'url' => $site_url.'management/Asset_controller/asset_allocation_report',
            'permission' => 1
          ],
          [
            'title' => 'Asset Depreciation Report',
            'sub_title' => '',
            'icon' => 'svg_icons/usermanagement.svg',
            'url' => $site_url.'management/Asset_controller/asset_depreciation_report',
            'permission' => 1
          ],
          [
            'title' => 'Asset Discard Report',
            'sub_title' => '',
            'icon' => 'svg_icons/assignconcession.svg',
            'url' => $site_url.'management/asset_controller/asset_discard_report',
            'permission' => 1
          ],
          [
            'title' => 'Room wise allocation',
            'sub_title' => '',
            'icon' => 'svg_icons/school.svg',
            'url' => $site_url.'management/asset_controller/room_wise_allocation',
            'permission' => 1
          ],
          [
            'title' => 'Staff wise allocation',
            'sub_title' => '',
            'icon' => 'svg_icons/staff.svg',
            'url' => $site_url.'management/asset_controller/staff_wise_allocation',
            'permission' => 1
          ]
        );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['administration_tiles'] = array(
          [
            'title' => 'Generate QR Codes',
            'sub_title' => 'Generate the QRcodes here',
            'icon' => 'svg_icons/generateqrcode.svg',
            'url' => $site_url.'management/asset_controller/old_assetsQRCode',
            'permission' => 1
          ]
        );
    $data['administration_tiles'] = checkTilePermissions($data['administration_tiles']);

		$data['main_content'] = 'management/assets/menu';
    $this->load->view('inc/template', $data);
	}

  public function Old_Asset_management() {
    $data['main_content'] = 'management/assets/old_menu';
    $this->load->view('inc/template',$data);
  }

  public function other_links(){
    $data['class_sections']= $this->Otherlinks_model->get_classSection_list();
    $data['main_content'] = 'management/other_links/index';
  	$this->load->view('inc/template', $data);
  }

  public function get_other_links_data(){
    $result=$this->Otherlinks_model->getAllOtherLinks();
    echo json_encode($result);
  }

  public function add_update_other_links(){
    if($_POST["button"]=="add"){
      $result=$this->Otherlinks_model->add_other_links($_POST);
    }else{
      $result=$this->Otherlinks_model->update_other_links($_POST);
    }
    if($result){
      $this->session->set_flashdata("flashSuccess","Update Successful.");
    }else{
      $this->session->set_flashdata("flashError","Something went wrong.");
    }

    redirect("management/management_controller/other_links");
  }

  public function get_particular_other_links_data(){
    $result=$this->Otherlinks_model->get_particular_other_links_data($_POST);
    echo json_encode($result);
  }

  public function disable_particular_other_links_data(){
    echo $this->Otherlinks_model->disable_particular_other_links_data($_POST);
  }
  
} 