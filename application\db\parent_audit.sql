DROP TRIGGER IF EXISTS parent_audit;
DELIMITER $$
CREATE TRIGGER parent_audit
AFTER UPDATE ON parent FOR EACH ROW
BEGIN
    SET @oldJson = NULL;
    SET @newJson = NULL;
    CALL merge_object(@old<PERSON>son, @newJson, OLD.first_name, NEW.first_name, 'first_name');
    CALL merge_object(@old<PERSON>son, @newJson, OLD.last_name, NEW.last_name, 'last_name');
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.qualification, NEW.qualification, 'qualification');
    CALL merge_object(@old<PERSON>son, @newJson, OLD.occupation, NEW.occupation, 'occupation');
    CALL merge_object(@oldJson, @newJson, OLD.mobile_no, NEW.mobile_no, 'mobile_no');
    CALL merge_object(@oldJson, @newJson, OLD.aadhar_no, NEW.aadhar_no, 'aadhar_no');
    CALL merge_object(@old<PERSON><PERSON>, @newJson, OLD.student_id, NEW.student_id, 'student_id');
    CALL merge_object(@old<PERSON>son, @newJson, OLD.annual_income, NEW.annual_income, 'annual_income');
    CALL merge_object(@old<PERSON>son, @newJson, OLD.company, NEW.company, 'company');
    CALL merge_object(@oldJson, @newJson, OLD.mother_tongue, NEW.mother_tongue, 'mother_tongue');
    CALL merge_object(@oldJson, @newJson, OLD.email, NEW.email, 'email');
    CALL merge_object(@oldJson, @newJson, OLD.identification_code, NEW.identification_code, 'identification_code');
    CALL merge_object(@oldJson, @newJson, OLD.language_spoken, NEW.language_spoken, 'language_spoken');
    CALL merge_object(@oldJson, @newJson, OLD.designation, NEW.designation, 'designation');
    CALL merge_object(@oldJson, @newJson, OLD. picture_url, NEW. picture_url, ' picture_url');
    CALL merge_object(@oldJson, @newJson, OLD.high_quality_picture_url, NEW.high_quality_picture_url, 'high_quality_picture_url');
    CALL merge_object(@oldJson, @newJson, OLD.vaccination_name, NEW.vaccination_name, 'vaccination_name');
    CALL merge_object(@oldJson, @newJson, OLD.vaccination_status, NEW.vaccination_status, 'vaccination_status');
    CALL merge_object(@oldJson, @newJson, OLD.vaccination_last_submitted_date, NEW.vaccination_last_submitted_date, 'vaccination_last_submitted_date');
    CALL merge_object(@oldJson, @newJson, OLD.vaccination_verification_status, NEW.vaccination_verification_status, 'vaccination_verification_status');
    CALL merge_object(@oldJson, @newJson, OLD.vaccination_supporting_document, NEW.vaccination_supporting_document, 'vaccination_supporting_document');
    CALL merge_object(@oldJson, @newJson, OLD.pan_number, NEW.pan_number, 'pan_number');
    CALL merge_object(@oldJson, @newJson, OLD.home_city, NEW.home_city, 'home_city');
    CALL merge_object(@oldJson, @newJson, OLD.relation_ship, NEW.relation_ship, 'relation_ship');
    CALL merge_object(@oldJson, @newJson, OLD.signature, NEW.signature, 'signature');
    CALL merge_object(@oldJson, @newJson, OLD.aadhar_document_path, NEW.aadhar_document_path, 'aadhar_document_path');
    CALL merge_object(@oldJson, @newJson, OLD.aadhar_document_status, NEW.aadhar_document_status, 'aadhar_document_status');
    CALL merge_object(@oldJson, @newJson, OLD.aadhar_number, NEW.aadhar_number, 'aadhar_number');
    CALL merge_object(@oldJson, @newJson, OLD.aadhar_document_remarks, NEW.aadhar_document_remarks, 'aadhar_document_remarks');
    CALL merge_object(@oldJson, @newJson, OLD.aadhar_approved_status, NEW.aadhar_approved_status, 'aadhar_approved_status');
    CALL merge_object(@oldJson, @newJson, OLD.pan_card_document_path, NEW.pan_card_document_path, 'pan_card_document_path');
    CALL merge_object(@oldJson, @newJson, OLD.pan_card_document_status, NEW.pan_card_document_status, 'pan_card_document_status');
    CALL merge_object(@oldJson, @newJson, OLD.pan_card_document_remarks, NEW.pan_card_document_remarks, 'pan_card_document_remarks');
    CALL merge_object(@oldJson, @newJson, OLD.pan_card_number, NEW.pan_card_number, 'pan_card_number');
    CALL merge_object(@oldJson, @newJson, OLD.pan_card_approved_status, NEW.pan_card_approved_status, 'pan_card_approved_status');
    CALL merge_object(@oldJson, @newJson, OLD.blood_group, NEW.blood_group, 'blood_group');
    CALL merge_object(@oldJson, @newJson, OLD.dob, NEW.dob, 'dob');
    CALL merge_object(@oldJson, @newJson, OLD.employee_id, NEW.employee_id, 'employee_id');
    CALL merge_object(@oldJson, @newJson, OLD.office_landline_number, NEW.office_landline_number, 'office_landline_number');
    CALL merge_object(@oldJson, @newJson, OLD.alternate_email_id, NEW.alternate_email_id, 'alternate_email_id');
    CALL merge_object(@oldJson, @newJson, OLD.whatsapp_num, NEW.whatsapp_num, 'whatsapp_num');
    CALL merge_object(@oldJson, @newJson, OLD.bank_account_num, NEW.bank_account_num, 'bank_account_num');
    if(@oldJson!= @newJson) THEN
    INSERT INTO audit_log (
        table_name,
        source_id,
        old_values,
        new_values,
        event_type,
        modified_on,
        modified_by
    )
    VALUES(
        'parent',
        NEW.id,
        @oldJson,
        @newJson,
        'UPDATE',
        CURRENT_TIMESTAMP,
        NEW.last_modified_by
    );
    END IF;
END;
$$
DELIMITER ;
