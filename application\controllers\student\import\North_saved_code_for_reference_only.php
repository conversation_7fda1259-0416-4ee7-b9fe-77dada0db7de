<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Student_controller
 *
 * <AUTHOR>
 */
class north_import_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
      //This code is just for reference. Hence redirecting to auth/login if somebody tries to get in unintentionally.
        redirect('auth/login', 'refresh');
    }   
   private function __marshalClassId($id, $iClassName) {

      //Promote him
      switch ($iClassName) {
        case 'PREP-1':
          $pClassName = 'PREP-2';
          break;
        case 'PREP-2':
          $pClassName = 'PREP-3';
          break;
        case 'PREP-3':
          $pClassName = 'G1';
          break;
        case 'G-1':
          $pClassName = 'G-2';
          break;
        case 'G-2':
          $pClassName = 'G-3';
          break;
        case 'G-3':
          $pClassName = 'G-4';
          break;
        case 'G-4':
          $pClassName = 'G-5';
          break;
        case 'G-5':
          $pClassName = 'G-6';
          break;
        case 'G-6':
          $pClassName = 'G-7';
          break;
          case 'G-7':
          $pClassName = 'G-8';
          break;
        case 'G-8':
          $pClassName = 'G-9';
          break;
        case 'G-10':
          $pClassName = '0'; //This student's data should be retained as alumni
          return 0;
          break;
        default:
          echo 'Problem found in Class; ';
          echo 'ID : ' . $id . '; Input value: ' . $iClassName;die();
          break;
      }
    
      //Add class Id and also promote them!!
      $classes = $this->db->select('id, class_name')
        ->from('class')
        ->get()->result();
    
      $found = 0;
      foreach ($classes as $class) {
        if ($class->class_name == $pClassName) {
          $found = $class->id;
        }
      }

      if ($found==0) {
        echo 'Problem found in Class; ';
        echo 'ID : ' . $id . '; Input value: ' . $iClassName;die();
      }
      return $found; //Return the promoted class Id.
    }

    private function _getClassAdmittedId($id,$classAdmited_to){
      //Add class Id and also promote them!!
      $classes = $this->db->select('id, class_name')
        ->from('class')
        ->get()->result();
    
      $found = 0;
      foreach ($classes as $class) {
        if ($class->class_name == $classAdmited_to) {
          $found = $class->id;
        }
      }

      if ($found==0) {
        echo 'Problem found in Class; ';
        echo 'ID : ' . $id . '; Input value: ' . $iClassName;die();
      }
      return $found; //Return the promoted class Id.

    }

    private function __marshalCategory($id, $category) {

      if (!empty($category)) {
        $categories = $this->settings->getSetting('category');
    
        foreach ($categories as $k => $cCat) {
          $found = 0;
          if (strtolower($cCat) == strtolower($category) ) {
            $found = $k;
            break;
          }
        }
        if ($found==0) {
          echo 'Problem found in Category; ';
          echo 'ID : ' . $id . '; Input value: ' . $category;die();
        } else {
          return $found;
        }
      }
     
    }

    private function __marshalMediumId($id, $iMedium) {
      $mediums = $this->settings->getSetting('medium');
      //echo '<pre>';print_r($category);
      //echo '<pre>';print_r($categories);
      
      foreach ($mediums as $k => $med) {
        $found = 0;
        if ($med == $iMedium) {
          $found = $k;
          break;
        }
      }
      if ($found==0) {
        echo 'Problem found in Medium; ';
        echo 'ID : ' . $id . '; Input value: ' . $iMedium;die();
      } else {
        return $found;
      }
    }

    private function __marshalGender($id, $gender) {
      if ($gender == 'Male') return 'M';
      if ($gender == 'Female') return 'F';
    }

    // //submit student data for form input
    // public function submitStudent() {
    //   //echo '<pre>';print_r($this->input->post());
    //   $input_form = $this->input->post();
    //   $this->__submitStudent($input_form, 'form');
    // }

    //submit student data from a excel import
    public function importStudentData () {
      $fromId = $this->input->post('from_id');
      $toId = $this->input->post('to_id');

      for ($id = $fromId; $id <= $toId; $id++) {
        $result = $this->db->select('*')->from('north_hill_2017_18')->where('id',$id)->get()->row();
        if (empty($result)) continue;
        echo "<pre>"; print_r($result); die();
        //Marshal data
        $result->category = $this->__marshalCategory($id, $result->category);
        $result->gender = $result->gender;
        $result->medid = '1';
        $result->board = '2'; //1 is for State board
        $result->boardingid = '1';
        $result->religion = ucwords(strtolower($result->religion));
        //$result->caste = ucwords(strtolower($result->caste));
        //$result->mother_tongue = ucwords(strtolower($result->mother_tongue));
        //$result->donor_name = ucwords(strtolower($result->donor_name));

        //Send as empty. This signifies that the student is not a sibling.
        $result->f_userid = '';
        $result->m_userid = '';
        $result->student_lastname = '';
        $result->f_last_name = '';
        $result->m_last_name = '';
        $result->s_email = '';
        $result->m_email = '';
        $result->f_email = '';
        $result->nationality = '';
        $result->std_aadhar = '';
        $result->classsection = '';
        $result->roll_num=0;
        $result->haveSibling=-1;
        $result->f_aadhar='';
        $result->m_mobile_number='';
        $result->m_aadhar='';
        $result->contact_no='';
        $result->m_annual_income='';
        $result->m_mobile_no='';
        $result->birth_taluk='';
        $result->birth_district='';
        $result->donor_name='';
        $result->mother_tongue='';
        $result->admidType='';
        $result->rteid='';
        $result->caste='';
        $result->f_qualification='';
        $result->f_occupation='';
        $result->f_annual_income='';
        $result->admidType='1';
     
        //Marshal class Id
        $newClassId = $this->__marshalClassId($id, $result->classid);

        $clsAdmitted_Id = $this->_getClassAdmittedId($id,$result->class_admitted_to);

        if ($newClassId == 0) {
          $isAlumni = 1;
        } else {
          $isAlumni = 0;
        }
        $result->classid = $newClassId;
        $result->class_admitted_to = $clsAdmitted_Id;
        //Admission Status
        if ($isAlumni) {
          $result->add_status = 4;
        } else {
          $result->add_status = 2;
        }
    
        // //Admission Type
        // switch ($result->admidType) {
        //   case 'Re-Admission':
        //     $result->admidType = 1;
        //     $result->rteid = 2;
        //     break;
        //   case 'Re-Admission RTE':
        //     $result->admidType = 1;
        //     $result->rteid = 1;
        //     break;
        //   case 'New Admission':
        //     $result->admidType = 2;
        //     $result->rteid = 2;
        //     break;
        //   case 'New Admission RTE':
        //     $result->admidType = 2;
        //     $result->rteid = 1;
        //     break;
        //   default:
        //     echo 'Problem found in admidType; ';
        //     echo 'ID : ' . $id . '; Input value: ' . $result->admidType;die();
        //     break;
        // }
    
        $this->__submitStudent($result, 'import');
      }
      redirect('student/Student_controller');
      //echo 'All Good!!';die();
    }

}
