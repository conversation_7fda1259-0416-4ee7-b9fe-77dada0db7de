<?php

class Staff_mytransport extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->load->model('parent_model');
    $this->load->model('ptm_model');
    $this->config->load('form_elements');
    $this->load->library('filemanager');
    $this->load->library('payment');
    $this->load->library('payment_payu');
    $this->load->library('payment_jodo');
    $this->load->model('HomeworkModel');
    $this->load->model('flash_model');
    $this->load->model('fees/fee_transaction_concorde');
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_collection_model');
    $this->load->model('student_tasks/Tasks_model', 'task');
    $this->load->model('student/Certificates_Model');
    $this->load->library('fee_library');
    $this->load->model('timetablev2/template_model', 'template_model');
    $this->load->helper('text');
    $this->load->model('student_wallet_model');
    $this->load->model('classroom_chronicles/Classroom_chronicles_model');
    $this->load->model('academics/lessonPlan_model', 'lessonplan_model');
    $this->load->model('staff/Staff_Model');
  }

   public function index() {
    $studentId = $this->authorization->getAvatarStakeHolderId();
    // $rfid = $this->Staff_Model->getRFID($studentId);
    $rfid = ''; // Staff RFID Implement
    $data['journeys'] = $this->Staff_Model->getTodaysJourneyStaff($studentId);
   
    $data['attendance'] = array();
    if ($rfid) {
      $data['attendance'] = $this->Staff_Model->rfidJourneys($rfid);
    }
    $attendance = array();
    $current_time = strtotime($this->Staff_Model->timezone_setter(date('d-m-Y H:i:s')));
    $data['is_attendance'] = 0;
    if (empty($data['attendance']['PICKING'])) {
      $attendance = $this->makeStoredJourneys($data['journeys']['PICKING'], $attendance);
    } else {
      $data['is_attendance'] = 1;
      $stored_picking = $this->_getJourneyIds($data['journeys']['PICKING']);
      $attendance = $this->makeAttendanceJourneys($data['attendance']['PICKING'], $attendance, $stored_picking);
    }


    if (empty($data['attendance']['DROPPING'])) {
      $attendance = $this->makeStoredJourneys($data['journeys']['DROPPING'], $attendance);
    } else {
      $data['is_attendance'] = 1;
      $stored_dropping = $this->_getJourneyIds($data['journeys']['DROPPING']);
      $attendance = $this->makeAttendanceJourneys($data['attendance']['DROPPING'], $attendance, $stored_dropping);
    }

    $data['activities'] = $attendance;

    $dont_show = $this->settings->getSetting('dont_show_bus_tracking');
    $data['show_numbers'] = 'none';
    $show_numbers = $this->settings->getSetting('display_transportation_numbers');
    if ($show_numbers) {
      $data['show_numbers'] = $show_numbers;
    }
    $data['show_tracking'] = 1;
    if ($dont_show == '') {
      $data['show_tracking'] = 1;
    } else {
      $data['show_tracking'] = $dont_show;
    }

    $data['main_content'] = 'staff/my_transport/index.php';
    $this->load->view('inc/template', $data);
  }

  private function makeAttendanceJourneys($journeys, $attendance, $stored_journeys){
		
		// $current_time = strtotime($this->parent_model->timezone_setter(date('d-m-Y H:i:s')));
		$current_time = strtotime(date('d-m-Y H:i:s'));
		foreach ($journeys as $key => $att) {
			$att->journey_change = 'no';
			if (!in_array($att->journeyId, $stored_journeys)) {
				$att->journey_change = 'yes';
			}
			$start = strtotime($att->start_time);
			$end = strtotime($att->end_time);
			$att->tracking = 'Not Started';
			if ($start <= $current_time && $end >= $current_time) {
				$att->tracking = 'Running';
			} else if ($end < $current_time) {
				$att->tracking = 'Completed';
			}
			$attendance[] = $att;
		}		
		return $attendance;
	}

  private function makeStoredJourneys($journeys, $attendance)
  {
    $current_time = strtotime(date('d-m-Y H:i:s'));
    // $current_time = strtotime($this->parent_model->timezone_setter(date('d-m-Y H:i:s')));
    foreach ($journeys as $key => $journey) {
      $start = strtotime($journey->start_time);
      $end = strtotime($journey->end_time);
      $journey->tracking = 'Not Started';
      if ($start <= $current_time && $end >= $current_time) {
        $journey->tracking = 'Running';
      } else if ($end < $current_time) {
        $journey->tracking = 'Completed';
      }
      $attendance[] = $journey;
    }
    return $attendance;
  }


  private function _getJourneyIds($journeys)
  {
    $ids = [];
    foreach ($journeys as $key => $value) {
      array_push($ids, $value->journeyId);
    }
    return $ids;
  }

  public function track_bus()
  {
    $thingId = $_POST['thing_id'];
    $stopId = $_POST['stop_id'];
    $studentId = $this->authorization->getAvatarStakeHolderId();
    $url = $this->Staff_Model->getTrackingUrlByThing($thingId);
    $data['tracking_url'] = $url . '&stopId=' . $stopId;
    $data['main_content'] = 'parent/transport/track_bus';
    $this->load->view('inc/template', $data);
  }


}  