<div id="filtersContainer">
    <div class="card-body pt-3">
        <div class="row g-3 mb-3">
            <div class="col-md-3">
                <label class="form-label" id="reconLable">Receipt Date Range</label>
                <input type="text" id="reportrange" class="form-select" placeholder="Select Date Range" readonly />
                <span></span>
                <input type="hidden" id="from_date">
                <input type="hidden" id="to_date">
            </div>
            <div class="col-md-3">
                <label class="form-label">Class</label>
                <select name="class_name[]" id="classId" multiple="multiple" title="All" class="form-control classId select">
                    <?php
                    if (!isset($classes) || !is_array($classes)) {
                        $classes = array();
                    }
                    foreach ($classes as $key => $class) { ?>
                        <option value="<?php echo $class->classId; ?>"><?php echo $class->className; ?></option>
                    <?php } ?>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Payment Method</label>
                <?php
                $array = array();
                foreach ($payment_mode as $key => $mode) {
                    $array[$mode->value] = ucfirst(str_replace('_', ' ', $mode->name));
                    $array['10'] = 'Online Payment';
                    if ($additionalAmount) {
                        $array['999'] = 'Excess Amount';
                    }
                }
                echo form_dropdown("payment_type", $array, set_value("payment_type"),  "id='paymentModes' class='form-control select' multiple title='All' ");
                ?>
            </div>
            <div class="col-md-3">
                <label class="form-label">Fee Type</label>
                <select class="form-control select" multiple title='All' id="fee_type" name="fee_type">
                    <?php foreach ($fee_blueprints as $key => $val) { ?>
                        <option value="<?= $val->id ?>"><?php echo $val->name ?></option>
                    <?php } ?>
                    <?php if ($sales) {
                        echo '<option value="sales">Sales</option>';
                    } ?>
                    <?php if ($admission) {
                        echo '<option value="application">Applications</option>';
                    } ?>
                    <?php if ($additionalAmount) {
                        echo '<option value="excess_amount">Excess Amount</option>';
                    } ?>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Admission Type</label>
                <?php
                $array = array();
                $array[0] = 'Select Admission Type';
                foreach ($admission_type as $key => $admission) {
                    $array[$key] = ucfirst($admission);
                }
                echo form_dropdown("admission_type", $array, set_value("admission_type"), "id='admission_type' class='form-select'");
                ?>
            </div>
        </div>
        <hr />
        <div class="row mb-4">
            <div class="col-12">
                <span class="fw-semibold mb-3 d-block">More Options</span>
                <div class="row g-3">
                    <!-- <div class="col-lg-3 col-md-6 col-sm-12">
                        <label class="custom-checkbox-label d-flex align-items-center" for="showInstallments">
                            <input class="custom-checkbox me-2" type="checkbox" id="showInstallments" checked>
                            <span>Show Installments</span>
                        </label>
                    </div> -->
                    <div class="col-lg-3 col-md-6 col-sm-12">
                        <label class="custom-checkbox-label d-flex align-items-center" for="recon">
                            <input class="custom-checkbox me-2" type="checkbox" name="recon" id="recon">
                            <span>Consider Reconciliation</span>
                        </label>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-12">
                        <label class="custom-checkbox-label d-flex align-items-center" for="components_include">
                            <input class="custom-checkbox me-2" type="checkbox" name="components" id="components_include">
                            <span>Includes Components Summary</span>
                        </label>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-12">
                        <label class="custom-checkbox-label d-flex align-items-center" for="components_exclude">
                            <input class="custom-checkbox me-2" type="checkbox" checked name="components" id="components_exclude">
                            <span>Includes Components</span>
                        </label>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-12">
                        <label class="custom-checkbox-label d-flex align-items-center" for="components_v_h">
                            <input class="custom-checkbox me-2" type="checkbox" name="components_v_h" checked id="components_v_h">
                            <span>Display Components Horizontally</span>
                        </label>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-12">
                        <label class="custom-checkbox-label d-flex align-items-center" for="include_delete">
                            <input class="custom-checkbox me-2" type="checkbox" name="include_delete" id="include_delete">
                            <span>Include Cancelled</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <hr />
        <div class="row mb-4">
            <div class="col-12">
                <span class="fw-semibold mb-3 d-block">Report Type</span>
                <div class="d-flex flex-wrap align-items-center" style="gap: 0.5rem 1.5rem;">
                    <label class="custom-radio-label d-flex align-items-center mb-0 me-3" for="type-2" style="gap: 0.25rem;">
                        <input class="custom-radio me-2" type="radio" name="report_type" id="type-2" value="2" checked>
                        <span>Summary</span>
                    </label>
                    <label class="custom-radio-label d-flex align-items-center mb-0 me-3" for="type-3" style="gap: 0.25rem;">
                        <input class="custom-radio me-2" type="radio" name="report_type" id="type-3" value="3">
                        <span>Summary (With Parents Detail)</span>
                    </label>
                    <label class="custom-radio-label d-flex align-items-center mb-0 me-3" for="type-1" style="gap: 0.25rem;">
                        <input class="custom-radio me-2" type="radio" name="report_type" id="type-1" value="1">
                        <span>Detailed Report</span>
                    </label>
                    <label class="custom-radio-label d-flex align-items-center mb-0 me-3" for="type-4" style="gap: 0.25rem;">
                        <input class="custom-radio me-2" type="radio" name="report_type" id="type-4" value="4">
                        <span>Installment wise Report</span>
                    </label>
                </div>
            </div>
        </div>
        <?php $this->load->helper('reports_datatable');
        echo progress_bar(); ?>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#recon').change(function() {
            if ($(this).is(':checked')) {
                $('#reconLable').text('Reconciliation Date Range');
            } else {
                $('#reconLable').text('Receipt Date Range');
            }
        });
        // Ensure class filter uses Select2
        if ($.fn.select2) {
            $('#classId').select2({
                placeholder: "All Classes"
            });
        }
    });
</script>