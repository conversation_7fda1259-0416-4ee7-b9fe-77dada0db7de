<?php

class Health_controller extends CI_Controller
{

    function __construct()
    {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN') && !$this->settings->isParentModuleEnabled('STUDENT_HEALTH')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('parent_model');
        $this->load->model('Student_health_model');
        $this->load->model('student/Student_Model', 'stdModel');
        $this->load->model('student/Health_model', 'healthModel');
        $this->config->load('form_elements');
    }

    public function health($callFrom = ''){
        $data['callFrom'] = $callFrom;
        $data['studentId'] = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['parent_id'] = $this->authorization->getAvatarStakeHolderId();
        $data['vaccination_status']= $this->Student_health_model->get_vaccination_status($data['studentId']);
        $vac_master= $this->config->item('vaccine_master');
        $tem_arr = array();
        if(!empty($vac_master)){
            foreach($vac_master as $key=>$val){
                $tem_arr[$key]['vaccination_name'] = $val;
                $tem_arr[$key]['vaccination_status'] = 0;
                $tem_arr[$key]['description'] = '';
                $tem_arr[$key]['year_of_vaccination'] = '';
                if(!empty($data['vaccination_status'])){
                    foreach($data['vaccination_status'] as $k =>$v){
                        if(strtolower($v->vaccination_name) == strtolower($val)){
                            $tem_arr[$key]['vaccination_status'] = $v->vaccination_status;
                            $tem_arr[$key]['description'] = $v->description;
                            $tem_arr[$key]['year_of_vaccination'] = $v->year_of_vaccination;
                        }
                    }
                }
            }
        }
        $data['vaccination_details'] = $tem_arr;
        $data['acad_year_id'] = $this->acad_year->getAcadYearId();
        $data['check_health_record'] =$this->Student_health_model->check_health_detail($data['studentId']);
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'health/student_health_tablet_view';  	
            // $data['main_content'] = 'health/student_health_mobile_view';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'health/student_health_mobile_view';
        }else{
            $data['main_content'] = 'health/student_health';  	
        }
        $this->load->view('inc/template', $data);
    }
    public function hospitilization_details($callFrom = '') {
        $data['callFrom'] = $callFrom;
        $data['studentId'] = $this->parent_model->getStudentIdOfLoggedInParent();
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'health/hospitalization_tablet_view';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'health/hospitalization_mobile_view';
        }else{
            //$data['main_content'] = 'health/student_health';  
            $data['main_content'] = 'health/hospitilization';	
        }
        $this->load->view('inc/template', $data);
    }
    public function medical_history($callFrom = ''){
        $data['callFrom'] = $callFrom;
        // $stdcsId = $this->uri->segment(5);
        $data['student_uid'] = $this->uri->segment(4);
        $data['stdData'] = $this->stdModel->getStdDataById($data['student_uid']);
        $data['health_info'] = $this->healthModel->getStudentHealthInfo($data['student_uid']);
        $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
        $data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
        $required_fields = $this->healthModel->get_health_required_fields();
        $data['required_fields'] = $this->__construct_name_wise_health_required($required_fields);
        $data['disabled_fields'] = $this->healthModel->get_health_disabled_fields();
        // echo "<pre>";
        // print_r($data['disabled_fields']);
        // die();
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'health/medical_history_tablet_view';
        }else if($this->mobile_detect->isMobile()){
                $data['main_content'] = 'health/medical_history_mobile_view';
        }else{
                $data['main_content'] = 'health/medical_history';
        }
        $this->load->view('inc/template', $data);
    }

    private function __construct_name_wise_health_required($requiredData){
        $fields = $this->db->list_fields('student_health');
        $rData = [];
        foreach ($fields as $key => $val) {
            if (in_array($val, $requiredData)) {
                $rData[$val] = array('font' =>'TRUE', 'required' =>'required');
            }else{
                $rData[$val] = array('font' =>'', 'required' =>'');
            }
        }
        return $rData;
    }

    public function get_hospitilization_details()
    {
        $student_id = $_POST['student_id'];
        $result =$this->Student_health_model->get_hospitilization_details($student_id);
        echo json_encode($result);   
    }
    public function submit_hospitalization_details($callFrom = '') {
        $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
        $this->Student_health_model->submit_hospitalization_details($_POST, $studentId);
        redirect('parent/health_controller/health/'.$callFrom);
    }
    public function whole_hospitalization_details($id)
    {
        $data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['whole_details'] = $this->Student_health_model->whole_hospitalization_details($data['student_id'],$id);
        if ($this->mobile_detect->isTablet()) {

            //$data['main_content'] = 'health/student_health';  	
            // $data['main_content'] = 'health/student_health_mobile_view';
        }else if($this->mobile_detect->isMobile()){

            $data['main_content'] = 'health/whole_hospitalization_details_mobile_view';
        }else {
            $data['main_content'] = 'health/whole_hospitalization_details';


           
          //  $data['main_content'] = 'health/student_health';  
          	
        }
        
        $this->load->view('inc/template', $data);
    }

    public function add_vaccination_history() {
        $vaccine_name = $_POST['vaccin_name'];
        $vaccination_date = $_POST['vaccination_date'];
        $parent_id = $_POST['parent_id'];
        $studentId = $_POST['studentId'];
        $description = $_POST['description'];
        $result =$this->Student_health_model->add_vaccination_history($vaccine_name, $vaccination_date, $parent_id, $studentId, $description);
        echo $result;

    }
    public function add_new_medical_history($callFrom = '') {
        $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
        $this->Student_health_model->add_new_medical_history($_POST, $studentId);
        redirect('parent/health_controller/health/'.$callFrom);
    }
    public function get_medical_history () {
        $student_id = $_POST['student_id'];
        $result =$this->Student_health_model->get_medical_history($student_id);
        echo json_encode($result); 
    }
    public function whole_medical_history($callFrom = '') {
        $data['callFrom'] = $callFrom;
        $required_fields = $this->healthModel->get_health_required_fields();
        $data['required_fields'] = $this->__construct_name_wise_health_required($required_fields);
        $data['disabled_fields'] = $this->healthModel->get_health_disabled_fields();
        $data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['whole_medical_history']=$this->Student_health_model->whole_medical_history($data['student_id']);
        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'health/whole_medical_history_tablet_view';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'health/whole_medical_history_mobile_view';
        }else {
            $data['main_content'] = 'health/whole_medical_history';
        }
        $this->load->view('inc/template', $data);
    }
}
?>