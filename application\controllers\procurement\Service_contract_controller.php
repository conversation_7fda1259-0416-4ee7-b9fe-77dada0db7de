<?php
defined('BASEPATH') or exit('No direct script access allowed');
/**
 * Name:    OxygenV2
 * Author:  Anish
 *          <EMAIL>
 *
 * Created:  19 march 2025
 *
 * Description: Controller for Service Contract Module. Entry point for Service Contract Module
 *
 * Requirements: PHP5 or above
 *
 */

class Service_contract_controller extends CI_Controller {
    private $yearId;
	function __construct() {
	    parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearID();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('PROCUREMENT_SERVICE_CONTRACT') || !$this->authorization->isAuthorized('PROCUREMENT_SERVICE_CONTRACT.MODULE')) {
            redirect('dashboard', 'refresh');
        }

        $this->load->model('procurement/Service_contract_model');  
        $this->load->library('filemanager');
    }

    function service_contract_dashboard() {
        $data['main_content']= 'procurement/service_contract_view/service_contract_dashboard';
        $this->load->view('inc/template', $data);
    }

    function service_contracts() {
        $data['isSuperAdmin']= $this->authorization->isSuperAdmin() ? 'Yes' : 'No';
        $data['main_content']= 'procurement/service_contract_view/service_contracts';
        $this->load->view('inc/template', $data);
    }

    function add_service_contract() {
        $data['acadYear']= $this->Service_contract_model->getAcadYear();

        // $obj= new stdClass();
        // $obj->currentRunningId= 0;
        // $data['currentMaxCantractId']= $obj;

        $data['expenseCategory']= $this->Service_contract_model->getExpenseCategory();
        $data['currentMaxCantractId']= $this->Service_contract_model->currentMaxCantractId();
        $data['departments']= $this->Service_contract_model->getAllDepartments();
        $data['vendors']= $this->Service_contract_model->getAllActiveVendors();
        $data['requester']= $this->Service_contract_model->getLoggedInName();
        
        $data['main_content']= 'procurement/service_contract_view/add_service_contract';
        $this->load->view('inc/template', $data);
    }

    function save_service_contract_steps() {
        $data= $this->Service_contract_model->save_service_contract_steps();
        echo json_encode($data);
    }

    function get_vendor_details() {
        $data= $this->Service_contract_model->get_vendor_details();
        // echo '<pre>'; print_r($data); die();
        echo json_encode($data);
    }

    function save_vendor_details() {
        $data= $this->Service_contract_model->save_vendor_details();
        echo json_encode($data);
    }

    function submit_service_contract_line_items() {
        $data= $this->Service_contract_model->submit_service_contract_line_items();
        echo json_encode($data);
    }

    function save_payment_details() {
        $data= $this->Service_contract_model->save_payment_details();
        echo json_encode($data);
    }

    function add_document() {
        $data= $this->Service_contract_model->add_document();
        echo json_encode($data);
    }

    function remove_document() {
        $data= $this->Service_contract_model->remove_document();
        echo json_encode($data);
    }

    function getAllServiceContracts() {
        $data= $this->Service_contract_model->getAllServiceContracts();
        echo json_encode($data);
    }

    function service_contract_details_page($service_contract_master_id= 0) {
        if($service_contract_master_id == 0) {
            $this->session->set_flashdata('flashError', 'Something Went Wrong OR service contract not found');
            redirect('procurement/service_contract_controller/service_contracts');
        }
        $contract['contractBasicDetails']= $this->Service_contract_model->getContractBasicDetails($service_contract_master_id);
        $contract['contractLineItemPaymentDetails']= $this->Service_contract_model->getContractLineItemPaymentDetails($service_contract_master_id);
        // echo '<pre>'; print_r( $contract['contractBasicDetails']); die();
        $contract['contractAttachements']= $this->Service_contract_model->getContractAttachements($service_contract_master_id);
        $contract['contractApprovers']= $this->Service_contract_model->getContractApprovers($service_contract_master_id);
        $contract['contractHistory']= $this->Service_contract_model->getContractHistory($service_contract_master_id);

        $contract['loggedInId']= $this->authorization->getAvatarStakeholderId();
        $contract['service_contract_master_id']= $service_contract_master_id;
        $contract['main_content']= 'procurement/service_contract_view/service_contract_details_page';
        $this->load->view('inc/template', $contract);
    }

    function submit_service_contract() {
        $data= $this->Service_contract_model->submit_service_contract();
        echo json_encode($data);
    }

    function getExpenseSubCategory() {
        $data= $this->Service_contract_model->getExpenseSubCategory();
        echo json_encode($data);
    }

    function download_contract_attachement($contract_attachements_id= 0) {
        if($contract_attachements_id == 0) {
            $this->session->set_flashdata('flashError', 'Something Went Wrong OR Attachement not found');
            redirect('procurement/service_contract_controller/service_contracts');
        }

        $file_link = $this->Service_contract_model->getDocumentURL($contract_attachements_id);
        $signed_resource = $this->filemanager->getSignedUrlWithExpiry($file_link->document, '+5 minutes');
        $file = explode("/", $file_link->document);
        $file_name = 'service_contract_'.$contract_attachements_id;
        $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
        $data = file_get_contents($signed_resource);
        $this->load->helper('download');
        force_download($fname, $data, TRUE);
        $this->load->library('user_agent');
        redirect($this->agent->referrer());	
    }

    function getDataToAddServiceLineItems() {
        $data= $this->Service_contract_model->getDataToAddServiceLineItems();
        echo json_encode($data);
    }

    function submit_individual_service_contract_line_items() {
        $data= $this->Service_contract_model->submit_individual_service_contract_line_items();
        echo json_encode($data);
    }

    function reject_service_contract() {
        $data= $this->Service_contract_model->reject_service_contract();
        echo json_encode($data);
    }

    function send_service_contract_for_modify() {
        $data= $this->Service_contract_model->send_service_contract_for_modify();
        echo json_encode($data);
    }

    function approve_service_contract() {
        $data= $this->Service_contract_model->approve_service_contract();
        echo json_encode($data);
    }

    function get_all_comments() {
        $data= $this->Service_contract_model->get_all_comments();
        echo json_encode($data);
    }

    function remove_service_line_item() {
        $data= $this->Service_contract_model->remove_service_line_item();
        echo json_encode($data);
    }

    function markAsModified() {
        $data= $this->Service_contract_model->markAsModified();
        echo json_encode($data);
    }

    function activateServiceContract() {
        $data= $this->Service_contract_model->activateServiceContract();
        echo json_encode($data);
    }

    function deactivateServiceContract() {
        $data= $this->Service_contract_model->deactivateServiceContract();
        echo json_encode($data);
    }

    function deleteServiceContract() {
        $data= $this->Service_contract_model->deleteServiceContract();
        echo json_encode($data);
    }

}
?>