<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  14 May 2018
 *
 * Description:  Contains the configuration details for all school-specific settings.
 *
 * Requirements: PHP5 or above
 *
 */
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| School General Settings
| -------------------------------------------------------------------------
| All the meta data for the school is stored in this section.
*/

/*
| -------------------------------------------------------------------------
| School Meta data
|---------------------------------------------------------------------------
*/
$config['school_name'] = "Bapu Composite PU College";
$config['school_name_line1'] = "Bapu Composite";
$config['school_name_line2'] = "PU College";
$config['school_short_name'] = "bapuypr";
$config['school_image'] = 'assets/img/bapu.jpg';
$config['school_logo'] = 'assets/img/bapu_logo.png';
$config['company_logo'] = 'assets/img/nextelement.jpg';
$config['company_name'] = 'NextElement';
$config['login_background'] = 'assets/img/bg-02.jpg';
/*
| -------------------------------------------------------------------------------------
| Timetable
| -------------------------------------------------------------------------------------
| header_class_section_id: Provide the class_section id of the section timetable from which the timetable header should be used.
| -------------------------------------------------------------------------------------
*/
$config['timetable']['header_class_section_id'] = 4;

/*
| -------------------------------------------------------------------------
| Admission Number
| Config settings for generating admission number - 
| manual: (TRUE/FALSE) - If false, generate admission number automatically.
| admission_generation_algo - Algorithm to use to generate admission number. Vaues -
| 1) NEXTELEMENT - default
| 2) WPL
| 3) NH
| infix - The infix to use in the algo
| digit-count - ?
| index_offset - The starting number for admissions.
|---------------------------------------------------------------------------
*/
$config['admission_number'] = [ 
                                'manual' => FALSE, 
                                'admission_generation_algo' => 'WPL',
                                'infix' => 'BAPU',
                                'digit_count' => 6,
                                'index_offset' => 1
                              ];

/*
| --------------------------------------------------------------------------
| Modules
| Define all the modules enabled for the school. The ones that are not here aren't enabled for the school
|
| Basic modules allowed for all schools by default - 
| SCHOOL_ADMIN - Enable School Admin in sidemenu.
| ACTIVITY - Enable Activity in sidemenu.
| COMMUNCIATION - Enable Communication in sidemenu.
| PERMISSIONS - Manage roles, assign privileges.
| CLASS_MASTER - Add classes.
| STUDENT_MASTER - CRUD Student data.
| STAFF_MASTER - CRUD Staff data.
| SUBJECTS_MASTER - CRUD Subjects, add and assign elective subjects to students.
| STAFF_LOGIN - Staff console, Staff dashboard.
| 
| The following are optional modules and are enabled based on school requirements -
| REGISTER - Enable Register in sidemenu
| STUDENT_ATTENDANCE - Take student attendance.
| LEAVE_MANAGEMENT: Enable Leave management in sidemenu.
| STUDENT_LEAVE - Students can apply leave.
| STAFF_LEAVE - Staff can apply leave.
| STAFF_ATTENDANCE - Take Staff attendance.
| EXAMINATION - Create and publish Exam timetable and portions, Marks card generation.
| TIMETABLE - Add Staff, Subject and Section association, Construct Timetable.
| SUBSTITUTION - Substitute absent staff
| FEES - Create Fee Structure, Pay Fees.
| LIBRARY - CRUD Books, manage Library transacations.
| INVENTORY - CRUD Inventory, manage inventory transacations.
| PARENTS_LOGIN - Parents console, Parents dashboard.
| ONLINE_EXAM - Teachers create online examination. Students can take the exam under Parent's guidance.
| ASSIGNMENT - Teachers can create assignment. Parents can view assignment.
| PUBLICATIONS - Teachers can create publications for school, class, section and students.
| PTM - To handle Parent-Teachers' meeting.
| PARENT_INITIATIVE - Parents can take an initiative in the school.
| STAFF_INITIATIVE - Staff can take an initiative in the school.
| WORKSHOP - Similar to Events.
| COMPETITION - CRUD Competitions.
| EVENTS - CRUD Events.
| VISITOR - Track visitors.
| STUDENT_EMERGENCY_EXIT - Track students leaving the class in between the day.
| SCHOOL_CALENDAR - Enables entering holiday list, events, competitions, examination calendar, etc.
| TRANSPORT - CRUD Transport.
| SMS - Send SMS to School, Class, Section or Student.
| REPORTS - Access to reports side-menu bar.
| 
| --------------------------------------------------------------------------
*/
$config['modules'] = 
array('SCHOOL_ADMIN', 'PERMISSIONS', 'STUDENT_MASTER', 'STAFF_MASTER','REPORTS','TIMETABLE','STUDENT_ATTENDANCE', 'SMS','PUBLICATION','DIARY','STAFF_LOGIN','COMMUNICATION','SCHOOL_CALENDAR','ASSIGNMENT','STAFF_LEAVE');

/*
| --------------------------------------------------------------------------
| Board
| Define all the boards the school supports in this array.
|
| Allowed values are -
|  1 -> 'State', 2->CBSE, 3 -> 'ICSE', 4-> 'IGCSE'
| --------------------------------------------------------------------------
*/
$config['board'] = array(
  '1' => 'State',
  '2' => 'CBSE'
);

/*
| --------------------------------------------------------------------------
| Medium
| Define all the mediums the school supports in this array.
|
| Allowed values are -
| 1 -> English, 2 -> Kannada, 3 -> Hindi
| --------------------------------------------------------------------------
*/
$config['medium'] = array(
  '1' => 'English'
);

/*
| --------------------------------------------------------------------------
| classType
| Define the type of class per the school standards
|
| Values here will just show up when creating a class
| --------------------------------------------------------------------------
*/
$config['classType'] = array(
  '1' => 'Nursery (LKG, UKG)',
  '2' => 'Primary (1-5)',
  '3' => 'High School (6-10)',
  '4' => 'PU College'
);

/*
| --------------------------------------------------------------------------
| Admission Type
| Define all the admission types the school supports in this array.
|
| Allowed values are -
| 1 -> Re-admission, 2 -> New Admission
| --------------------------------------------------------------------------
*/
$config['admission_type'] = array(
  '1'=>'Re-admission',
  '2'=>'New Admission'
);

/*
| --------------------------------------------------------------------------
| Admission Status
| Define all the admission status the school supports in this array.
|
| Allowed values are -
|1 -> Pending, 2 -> Approved, 3 -> Rejected 4 -> Alumni
| --------------------------------------------------------------------------
*/

$config['admission_status'] = array(
  '1'=>'Pending',
  '2'=>'Approved',
  '3'=>'Rejected',
  '4'=>'Alumni'
);

/*
| --------------------------------------------------------------------------
| Staff Selection Status
| Define all the Staff status the school supports in this array.
|
| Allowed values are -
|1 -> Pending, 2 -> Approved, 3 -> Rejected 4 -> Resigned
| --------------------------------------------------------------------------
*/
$config['staff_status'] = array(
  '1'=>'Pending',
  '2'=>'Approved',
  '3'=>'Rejected',
  '4'=>'Resigned'
);

/*
| --------------------------------------------------------------------------
| RTE Options
| Define all the RTE Options the school supports in this array.
|
| Allowed values are -
| 1-> RTE, 2 -> Non-RTE
| --------------------------------------------------------------------------
*/
$config['rte'] = array(
  '2'=>'Non-RTE',
  '1'=>'RTE'
);

/*
| --------------------------------------------------------------------------
| Boarding
| Define all the boarding types the school supports in this array.
|
| Allowed values are -
| 1 -> Day School, 2 -> Residential
| --------------------------------------------------------------------------
*/
$config['boarding'] = array(
  '1'=>'Day School',
);

/*
| --------------------------------------------------------------------------
| Category
| Define all the student categories the school supports in this array.
| 
| Allowed values are -
| 1 -> 'General',
| 2 -> 'SC/ST 
|
| eg: $config['category'] = array('General', 'SC/ST');
| --------------------------------------------------------------------------
*/
$config['category'] = array(
  '1' => 'General',
  '2' => 'SC/ST'
);
/*
|-----------------------------------------------------------------------
| This setting is used to set the allocation template for online room booking. Set this value to an existing (i.e. already created) timetable_template->id.
|-----------------------------------------------------------------------
*/

$config['online_room_booking_template_id'] = '2';

/*
| --------------------------------------------------------------------------
| Address Types
| Define all the address types the school likes to collect from student and parents.
|
| Allowed values are -
|(1: Present Address and 2: Permanent Address) or (1: Office Address And 2: Home Address)
| --------------------------------------------------------------------------
*/

$config['address_types'] = array(
  'Student'=> array("1" => "Present Address", "2" => "Permanent Address"),
  'Father'=>  array("1" => "Office Address", "2" => "Home Address"),
  'Mother'=>  array("1" => "Present Address", "2" => "Permanent Address")
);

/*
| -------------------------------------------------------------------------
| Fees
| -------------------------------------------------------------------------
| Fee Specific settings
*/

/*
| --------------------------------------------------------------------------
| Filter Criteria
|
| This is used for creating Master Fee Structure.
|
| Different schools have different filter criteria. For eg: NPS RNR uses academic year
| when student joined and whether he is RTE candidate to determine Fee Structure.
| NPS CNP uses class the student is going into and RTE candidature.
| Specify here the filter criteria to be used.
| This should be the same table column name you use in Student.
|
| The currently supported school criteria are - academic_year_of_joining, rte, class,
| medium, category, admission_type, boards, boarding
| --------------------------------------------------------------------------
|   Allowed values are -
|   0 -> 'admission_type',
|   1 -> 'medium
|   2 -> 'rte
|   3 -> 'category
|   4 -> 'academic_year_of_joining
|   5 -> 'class
|   6 -> 'boards
|   7 -> 'boarding
*/
$config['fees']['filter_criteria'] = array('admission_type', 'medium', 'rte', 'category');

/*
| ------------------------------------------------------------------------------
| Fee Components
|
| This is used to create Fee Components in Master.
|
| Different schools have different fee components. We assume that the components are same across all
| fee Structures. If this is wrong, we have to handle this as a database table rather than config.
|
| Add all the different components in 'Allocation Order' (See Allocation algorithm).
|      
| --------------------------------------------------------------------------------
*/

$config['fees']['components'] = array(
  array ( 'name' => 'New Admission','concession_eligible' => TRUE ),
  array ( 'name' => 'Registration', 'concession_eligible' => TRUE ),
  array ( 'name' => 'Maintenance','concession_eligible' => TRUE ),
  array ( 'name' => 'Miscellaneous','concession_eligible' => TRUE ),
);

/*
| ----------------------------------------------------------------------------------
| Fee Payment modes
| 
| This is used in Fee payment.
| Add all the payment modes supported by this school
| Allowed values - 

            $config['allowed_payment_modes'] = array(
                              '1'=>'dd',
                              '2'=>'credit card',
                              '3'=>'debit card',
                              '4'=>'cheque'
                              '5'=>'wallet payment'
                              '6'=>'challan'
                              '7'=>'Card (POS)'
                              '8'=> Net Banking (POS)
                              '9'=>'cash',
                            );

| ------------------------------------------------------------------------------------
*/
$config['fees']['allowed_payment_modes'] = array(
  array ('name'=>'challan','value'=>'6', 'reconcilation_reqd' => TRUE),
  array ('name'=>'dd','value'=>'1', 'reconcilation_reqd' => FALSE),
  array ('name'=>'cheque','value'=>'4', 'reconcilation_reqd' => TRUE)
);

/*
| ----------------------------------------------------------------------------------
| Fee Receipt template
| 
| This is used to generate the fee receipt.
| Add the template file that should be used (probably customized) for this school.
| ------------------------------------------------------------------------------------
*/
$config['fees']['receipt_template'] = 'wpl';

/*
| ----------------------------------------------------------------------------------
| Fee Component Allocation Type
| 
| This is used in Fee transaction.
|
| If TRUE, the allocation algorithm will kick in to allocate the total amount entered across Fee Components.
| If FALSE, the allocation has to be manually keyed in by the user (cashier).
|
| ------------------------------------------------------------------------------------
*/
$config['fees']['component_allocation_type_auto'] = FALSE;

/*
| ----------------------------------------------------------------------------------
| Fee Concession Component Allocation Type
| 
| This is used in Fee transaction. (How does the fee components get allocated when
| the total concession is entered).
|
| If TRUE, the allocation algorithm will kick in to allocate the total amount entered
| across Fee Components.
| If FALSE, the allocation has to be manually keyed in by the user (cashier).
|
| ------------------------------------------------------------------------------------
*/
$config['fees']['cComponent_allocation_type_auto'] = FALSE;

/*
| --------------------------------------------------------------------------------------
| Fee Component Allocation algorithm
| 
| This is used in conjunction with Fee transaction.
|
| If Allocation algorithm is AUTO, then the specified algorithm is used to allocate total amount across Fee Components.
|
| Algo 1: 'ALLOCATE_FROM_LEFT_TO_RIGHT' - This algorithm allocates the total amount from left to right as specified in the fee component array above.
| It will allocate the full amount for the component before proceeding to the next.
|
| ----------------------------------------------------------------------------------------
*/
$config['fees']['component_allocation_algorithm_if_auto'] = 'ALLOCATE_FROM_LEFT_TO_RIGHT';

/*
| --------------------------------------------------------------------------------------
| Fee receipt generation
| ----------------------------------------------------------------------------------------
*/

$config['fees']['recipt_number_gen'] = [ 
                      'fee_generation_algo' => 'WPL',
                      'infix' => 'WPL',
                      'digit_count' => 6,
                      ];


/*
| --------------------------------------------------------------------------------------
| Discount for fully paid fee amount for north hill school only  
| ----------------------------------------------------------------------------------------
*/
$config['fees']['discount'] =[
  'percent'=> 0,
  'isProvided' => FALSE,
  'isManual' => FALSE
 ];

/*
| --------------------------------------------------------------------------------------
| SMS Integration
| ----------------------------------------------------------------------------------------
*/
if (ENVIRONMENT !== 'production') {
  //Do not allow SMS config in other environments!  
} else {
// $config['smsintergration']  = 
//           array('url' => 'alerts.jiffysms.com/api/v3/index.php',
//             'api_key' => 'A1c5a0b3759817b01785eaed3315b3e46',
//             'sender' => 'WOMENS');
}

// Enable Competition Attendance
$config['competition_attendance'] = false;

// Latecomer Attendance if set to true only absents students would be show else all class students would be displayed.
$config['latecomer_attendance'] = true;

// Emergency Exit requires Visitor entry?.
$config['emergency_exit_visitor'] = false;

//Forgot Password 
$config['forgot_password']['email'] = true;
$config['forgot_password']['mobile'] = true;