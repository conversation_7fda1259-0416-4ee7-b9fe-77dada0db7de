<?php

defined('BASEPATH') OR exit('No direct script access allowed');

//this controller handles the call from remote server (silicon) to upadte the database
class Suy_callback_controller extends CI_Controller 
{
    function __construct()
	{
		parent::__construct();
    $this->load->model('suycrm/root_model');
    $this->acad_year->loadAcadYearDataToSession();
	}
  
    public function suy_roots(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $insert_id = $this->root_model->insert_register_users($data);
      $success = 0;
      if ($insert_id) {
       $success = $insert_id;
      }
      echo json_encode($success);
    }

    public function suy_roots_users(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $success = [];
      $result = $this->root_model->update_regishter_users_details($data);
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function suy_roots_update_trans(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $success = [];
      $result = $this->root_model->english_roots_update_trans_by_id($data);
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function suy_roots_all_programme(){
      $result = $this->root_model->getProgramNames();
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function suy_roots_language(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $dataId = $data[0];
      $result = $this->root_model->getLanguages($dataId);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function suy_roots_get_register_user_byid(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $last_id = $data['insert_id'];
      $result = $this->root_model->getRegister_users_details($last_id);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function suy_roots_level(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $prgrId = $data['program'];
      $lang = $data['language'];
      $result = $this->root_model->getLevels($prgrId, $lang);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function suy_roots_getLevelDetails(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $prgrId = $data['program'];
      $lang = $data['language'];
      $level = $data['level'];
      $result = $this->root_model->getBatch($level,$prgrId, $lang);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function suy_roots_getBatchDetails(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $prgrId = $data['program'];
      $lang = $data['language'];
      $level = $data['level'];
      $batch_name = $data['batch_name'];
      $result = $this->root_model->getBatch_details($level,$prgrId, $lang, $batch_name);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }
    public function suy_roots_getDurationAmount(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $prgrId = $data['program'];
      $lang = $data['language'];
      $level = $data['level'];
      $result = $this->root_model->getDurationAmount($level,$prgrId, $lang);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function suy_roots_insert_online_application_details(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $insert_id = $this->root_model->insert_online_application_details($data);
      $success = 0;
      if ($insert_id) {
       $success = $insert_id;
      }
      echo json_encode($success);
    }

    public function get_suy_roots_progams_batch(){
      
      $programName = (array) json_decode(file_get_contents('php://input'), TRUE);
      $programName = 'Student';
      $result = $this->root_model->get_english_roots_prgram_batch_list($programName[0]);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function suy_roots_update_online_application_details(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $result = $this->root_model->update_online_application_details($data);
      $success = 0;
      if ($result) {
       $success = $result;
      }
      echo json_encode($success);
    }

    public function suy_roots_details_by_id(){
      $source_id = (array) json_decode(file_get_contents('php://input'), TRUE);
      $result = $this->root_model->get_user_registration_form_details($source_id[0]);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function suy_roots_email_enquire(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $sourceId = $data['insert_id'];
      $program_id = $data['program_id'];
      // $updateRes =  $this->root_model->update_program_id($sourceId, $program_id);
      $rData = $this->root_model->get_user_registration_form_details($sourceId);
      $result = $this->root_model->enquiry_email_register_users_data($rData);
      $success = 0;
      if ($result) {
       $success = $program_id;
      }
      echo json_encode($success);
    }
}

?>