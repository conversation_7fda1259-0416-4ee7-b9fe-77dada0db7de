<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Student_controller
 *
 * <AUTHOR>
 */
class wpl_import_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
      //This code is just for reference. Hence redirecting to auth/login if somebody tries to get in unintentionally.
        redirect('auth/login', 'refresh');
    }   

    private function __marshalClassId($id, $iClassName) {
      //Promote him
      switch ($iClassName) {
        case 'LKG':
          $pClassName = 'UKG';
          break;
        case 'UKG':
          $pClassName = '1';
          break;
        case '1st':
          $pClassName = '2';
          break;
        case '2nd':
          $pClassName = '3';
          break;
        case '3rd':
          $pClassName = '4';
          break;
        case '4th':
          $pClassName = '5';
          break;
        case '5th':
          $pClassName = '6';
          break;
        case '6th':
          $pClassName = '7';
          break;
        case '7th':
          $pClassName = '8';
          break;
          case '8th':
          $pClassName = '9';
          break;
        case '9th':
          $pClassName = '10';
          break;
        case '10th':
          $pClassName = '0'; //This student's data should be retained as alumni
          return 0;
          break;
        default:
          echo 'Problem found in Class; ';
          echo 'ID : ' . $id . '; Input value: ' . $iClassName;die();
          break;
      }
  
      //Add class Id and also promote them!!
      $classes = $this->db->select('id, class_name')
        ->from('class')
        ->get()->result();
        
      $found = 0;
      foreach ($classes as $class) {
        if ($class->class_name == $pClassName) {
          $found = $class->id;
        }
      }

      if ($found==0) {
        echo 'Problem found in Class; ';
        echo 'ID : ' . $id . '; Input value: ' . $iClassName;die();
      }
      return $found; //Return the promoted class Id.
    }

    private function __marshalCategory($id, $category) {
      $categories = $this->settings->getSetting('category');
      //echo '<pre>';print_r($category);
      //echo '<pre>';print_r($categories);
      
      foreach ($categories as $k => $cCat) {
        $found = 0;
        if ($cCat == $category) {
          $found = $k;
          break;
        }
      }
      if ($found==0) {
        echo 'Problem found in Category; ';
        echo 'ID : ' . $id . '; Input value: ' . $category;die();
      } else {
        return $found;
      }
    }

    private function __marshalMediumId($id, $iMedium) {
      $mediums = $this->settings->getSetting('medium');
      //echo '<pre>';print_r($category);
      //echo '<pre>';print_r($categories);
      
      foreach ($mediums as $k => $med) {
        $found = 0;
        if ($med == $iMedium) {
          $found = $k;
          break;
        }
      }
      if ($found==0) {
        echo 'Problem found in Medium; ';
        echo 'ID : ' . $id . '; Input value: ' . $iMedium;die();
      } else {
        return $found;
      }
    }

    private function __marshalGender($id, $gender) {
      if ($gender == 'Male') return 'M';
      if ($gender == 'Female') return 'F';
    }

    //submit student data for form input
    public function submitStudent() {
      //echo '<pre>';print_r($this->input->post());
      $input_form = $this->input->post();
      $this->__submitStudent($input_form, 'form');
    }

    //submit student data from a excel import
    public function importStudentData () {
      $fromId = $this->input->post('from_id');
      $toId = $this->input->post('to_id');

      for ($id = $fromId; $id <= $toId; $id++) {
        $result = $this->db->select('*')->from('wpl_2017_18_student_data')->where('id',$id)->where('status','1')->get()->row();
        if (empty($result)) continue;

        //Marshal data
        $result->category = $this->__marshalCategory($id, $result->category);
        $result->gender = $this->__marshalGender($id, $result->gender);
        $result->medid = $this->__marshalMediumId($id, $result->medid);
        $result->board = '1'; //1 is for State board
        $result->boardingid = '1';
        $result->religion = ucwords(strtolower($result->religion));
        $result->caste = ucwords(strtolower($result->caste));
        $result->mother_tongue = ucwords(strtolower($result->mother_tongue));
        $result->donor_name = ucwords(strtolower($result->donor_name));

        //Send as empty. This signifies that the student is not a sibling.
        $result->f_userid = '';
        $result->m_userid = '';
        $result->student_lastname = '';
        $result->f_last_name = '';
        $result->m_last_name = '';
        $result->s_email = '';
        $result->m_email = '';
        $result->f_email = '';
        $result->nationality = '';
        $result->std_aadhar = '';
        $result->classsection = '';
        $result->roll_num=0;
        $result->haveSibling=-1;
        $result->f_aadhar='';
        $result->m_mobile_number='';
        $result->m_aadhar='';
        $result->contact_no='';
        $result->m_annual_income='';
        $result->m_mobile_no='';

        //Marshal class Id
        $newClassId = $this->__marshalClassId($id, $result->classid);
        if ($newClassId == 0) {
          $isAlumni = 1;
        } else {
          $isAlumni = 0;
          $result->classid = $newClassId;
        }

        //Admission Status
        if ($isAlumni) {
          $result->add_status = 4;
        } else {
          $result->add_status = 2;
        }

        //Admission Type
        switch ($result->admidType) {
          case 'Re-Admission':
            $result->admidType = 1;
            $result->rteid = 2;
            break;
          case 'Re-Admission RTE':
            $result->admidType = 1;
            $result->rteid = 1;
            break;
          case 'New Admission':
            $result->admidType = 2;
            $result->rteid = 2;
            break;
          case 'New Admission RTE':
            $result->admidType = 2;
            $result->rteid = 1;
            break;
          default:
            echo 'Problem found in admidType; ';
            echo 'ID : ' . $id . '; Input value: ' . $result->admidType;die();
            break;
        }
        
        $this->__submitStudent($result, 'import');
      }
      redirect('student/Student_controller');
      //echo 'All Good!!';die();
    }

}
