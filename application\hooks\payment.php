<?php
defined('BASEPATH') OR exit('No direct script access allowed');


function payment($paramenter) {
  
    $CI =& get_instance();
  
   
    if(isset($CI->is_hookable)){
        $CI->load->model('feesv2/fees_collection_model');

        $CI->load->library('payment');

        $input = $CI->userdata;
        if($input =='Failed'){
            exit();
        }
        $blue_print = $CI->fees_collection_model->ge_blueprint_by_id($input['cohort_student_id']);

		$payment_split = $CI->fees_collection_model->get_split_amount($blue_print->id, $input['split_amount']);

		$fTrans = $CI->fees_collection_model->insert_fee_transcation($input);

        $CI->db->trans_commit();
        $totalFineAmount = 0;
        $totalDiscount = 0;
        if (isset($input['total_fine_amount'])) {
            $totalFineAmount = $input['total_fine_amount'];
        }
        if (isset($input['discount_amount'])) {
            $totalDiscount = $input['discount_amount'];
        }

        foreach ($payment_split->vendors as $key => &$value) {
            if ($key == 0) {
                $value->split_amount_fixed = $value->split_amount_fixed + $totalFineAmount - $totalDiscount;
            }
        }
        $transPayAmount = $input['total_amount'] + $totalFineAmount - $totalDiscount;
        $CI->payment->init_payment_to_school($transPayAmount, 'PARENT_FEE', $fTrans, 'user_payment/success_user_payment', $blue_print->is_split, json_encode($payment_split), 'REDIRECT');
    }

    // $CI->load->model('User_payment_model');
    // echo "<pre>"; print_r($CI->User_payment_model->payment());die();
}

function payment_success() {
    $CI =& get_instance();
    $CI->load->model('feesv2/fees_collection_model');
    $CI->load->library('payment');
    $response = $CI->success_data;
    if ($response['transaction_status'] === 'SUCCESS') {
        $is_receipt_generated = $CI->fees_collection_model->is_receipt_generated($response['source_id']);
        // if receipt is already generated, we have already updated the transaction and schedule table. No need to update again. Just show the already generated receipt.
        if ($is_receipt_generated) {
            redirect('parent_controller/fee_reciept/' . $response['source_id'] . '/' . $response['transaction_id'] . '/' . $response['transaction_date'] . '/' . $response['transaction_time']);
            return;
        }

        // generate and update receipt number after transcation
        $result =  $CI->fees_collection_model->update_trans_student_all_table($response['source_id']);
        if (!$result) {
            $this->session->set_flashdata('flashError', 'Something went wrong. Will get back to you shortly');
        } else {
            $this->session->set_flashdata('flashSuccess', 'Transcation successful');
        }
    } else {
        //Online payment failed
        $CI->fees_collection_model->update_transcation_status($response['source_id'], 'FAILED');
    }
    $data['response'] = $CI->success_data;
    $CI->load->view('payment/users_page',$data);
}

// function payment_redirect($student_id){
//     $CI =& get_instance();
//     $controller = $CI->router->class;
//     $method = $CI->router->method;
//     $logMessage = "Accessed: $controller->$method";

//     // // log_message('info', $logMessage);
//     // echo "<pre>"; print_r($controller);
//     // echo "<pre>"; print_r($method);
//     // echo "<pre>"; print_r($logMessage);
//     // die();
// }

// function payment_failed(){
//     $CI =& get_instance();
//     $CI->load->view('payment/users_page');
// }