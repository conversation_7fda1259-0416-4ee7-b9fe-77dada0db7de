<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  21 October 2022
 *
 * Description: Controller for Infirmary Module. Entry point for Infirmary Module
 *
 * Requirements: PHP5 or above
 *
 */

class Visits extends CI_Controller {
	function __construct() {
    parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isModuleEnabled('INFIRMARY')) {
        redirect('dashboard', 'refresh');
      }
      $this->load->model('infirmary/visits_model', 'visits_model');
      $this->load->model('Managemedicine_model');
      $this->load->model('Student_staff_visit_model');
      $this->config->load('form_elements');
      $this->load->library('filemanager');
  } 
  
  //Shows the Form for entering the Visit details
  public function create_form() {
    $this->session->set_flashdata('var1', 'Submited');
    $data['symptoms_list'] = $this->visits_model->get_symptoms_list_data();
    $data['visits_list'] = $this->visits_model->get_all_visit_details_2();
    $data['diagnosis_list'] = $this->visits_model->get_diagnosis_list_data();
    $data['medicine_list'] = $this->visits_model->getMedicine_list();

    $data['test_list'] = $this->visits_model->get_test_list_data();
    
    $data['main_content']    = 'infirmary/Create visit/create_form';
    
    $this->load->view('inc/template', $data);
  }

  public function store_new_visit(){
    echo $this->visits_model->create_visit($_POST);
  }
  public function store_visit(){
    $result = $this->visits_model->create_new_visit($_POST);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Submitted');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong');
    }
    echo $result;
  }

  public function store_hopital_visit(){
    $admit_file= $_FILES['admit_prescription_url'];
    $file= '';
    if(!empty($admit_file)) {
      $file= $this->s3FileUpload($_FILES['admit_prescription_url'], 'infirmary');
    }
    echo $this->visits_model->create_hospital_visit($_POST, $file);
  }
  public function store_followup_visit(){
    echo $this->visits_model->create_followup_visit($_POST);
  }

  public function store_test_visit(){
    echo $this->visits_model->create_test_visit($_POST);
  }


  public function update_inference_data() {
    echo $this->visits_model->update_inference_data($_POST,$this->s3FileUpload($_FILES['document'], 'infirmary'));
  }


  private function s3FileUpload($file, $folder_name = 'profile')
  {
      if ($file['tmp_name'] == '' || $file['name'] == '') {
          return ['status' => 'empty', 'file_name' => ''];
      }
      return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
  }

  public function test_report_download($test_id,$visitor_name){
    $document = $this->visits_model->get_test_report_row($test_id);
    $document_url = $this->filemanager->getFilePath($document->report);
    $document_data = file_get_contents($document_url);
    $this->load->helper('download');
    force_download($visitor_name.'.pdf', $document_data, TRUE);
  }


  public function store_repeat_visit() {
    echo $this->visits_model->create_repeat_visit($_POST);
  }

  public function update_discharge_data() {
    $discharge_file= $_FILES['discharge_prescription_url'];
    $file= '';
    if(!empty($discharge_file)) {
      $file= $this->s3FileUpload($_FILES['discharge_prescription_url'], 'infirmary');
    }

    echo $this->visits_model->update_discharge_data($_POST, $file);
  }

  public function store_follow_up_visit() {
    echo $this->visits_model->create_follow_up_visit($_POST);
  }


  public function get_staff_all(){
      $result = $this->visits_model->get_staff_all();
      echo json_encode($result);
  }

  public function get_staff_student_names(){
    $student_staff_names = $_POST['student_staff_names'];
    $result = $this->visits_model->get_staff_student_names($student_staff_names);
    echo json_encode($result);
}

  public function get_visitor_data()
  {
    $visitor_type = $_POST['visitor_type'];
    $visitor_id = $_POST['visitor_id'];
    $result = $this->visits_model->get_visitor_detils($visitor_type,$visitor_id);
    echo json_encode($result);
  }

    public function get_sec_all(){
      $result = $this->visits_model->get_class_section_all();
      echo json_encode($result);
  }
  public function get_class_section_wise_std_data(){
    $cls_section = $this->input->post('cls_section');
    list($class_id,$sectionId) = explode('_',$cls_section);
    $result = $this->visits_model->get_class_section_student_data($class_id,$sectionId);
    echo json_encode($result);
  }
 
  public function view_visits() {
    $data['title']='View Visits';
    $data['symptoms_list'] = $this->visits_model->get_symptoms_list_data();
    $data['visits_list'] = $this->visits_model->get_all_visit_details_2();
    $data['diagnosis_list'] = $this->visits_model->get_diagnosis_list_data();
    $data['medicine_list'] = $this->visits_model->getMedicine_list();

    // echo '<pre>';print_r($data['visits_list']);die();
    $data['main_content']='infirmary/manage_visits/index';
    $this->load->view('inc/template',$data);
  }

  public function lab_report(){
    $data['main_content']='infirmary/visits/lab_report';
    $this->load->view('inc/template',$data);
  }
  
    public function manage_diagnosis_page() {
      $data['main_content'] = 'infirmary/visits/manage_diagnosis_page';
      $this->load->view('inc/template', $data);
    }
    public function test_master_page() {
      $data['test_list'] = $this->visits_model->get_test_list_data();
      $data['main_content'] = 'infirmary/test_master/test_master_page';
      $this->load->view('inc/template', $data);
    }

    public function manage_test_page() {
      $data['main_content'] = 'infirmary/visits/manage_test_page';
      $this->load->view('inc/template', $data);
    }
    

    public function get_visit_details_for_the_day() {
      $data['visit_obj_arr']=$this->visits_model->get_visit_details_for_the_day();
    
      echo json_encode($data);
    } 

    public function get_test_visit_details() {
      $data['test_obj_arr']=$this->visits_model->get_test_visit_details();
    
      echo json_encode($data);
    } 
    public function get_symptom_names() {
      $result = $this->visits_model->get_symptom_names();
      // echo '<pre>';print_r($result);die();
        echo json_encode($result);
    } 
    public function get_diagnosis_name() {
      $result = $this->visits_model->get_diagnosis_name();
      // echo '<pre>';print_r($result);die();
        echo json_encode($result);
    }

  public function get_visit_details_by_id(){
    $visit_id = $_POST['visit_id'];
    $visitor_type = $_POST['visitor_type'];
    // echo '<pre>';print_r($visit_id); die();
    $visit_obj = $this->visits_model->get_visit_details_by_id($visit_id, $visitor_type);
    echo json_encode($visit_obj);
   
  }

  public function get_admit_discharge_details_by_id(){
    $id = $_POST['id'];
    $data_obj = $this->visits_model->get_admit_discharge_details_by_id($id);
    // foreach($data_obj as $key => $val) {
      if($data_obj->admit_prescription_url) {
        $data_obj->admit_prescription_url= $this->filemanager->getFilePath($data_obj->admit_prescription_url);
      }
      if($data_obj->discharge_prescription_url) {
        $data_obj->discharge_prescription_url= $this->filemanager->getFilePath($data_obj->discharge_prescription_url);
      }

    // }
    // echo '<pre>';print_r($data_obj);die();
    echo json_encode($data_obj);
   
  }

  public function get_test_details_by_id(){
    $visit_test_id = $_POST['visit_test_id'];
    $visitor_type = $_POST['visitor_type'];
    $test_id = $_POST['test_id'];
    $test_data = $this->visits_model->get_test_details_by_id($visit_test_id, $visitor_type,$test_id);
    echo json_encode($test_data);
  }
  
  public function manage_medicines_page(){
    $data['main_content']='infirmary/visits/manage_medicine_page';
    $this->load->view('inc/template',$data);
  }

  public function manage_activity_page(){
    $data['main_content']='infirmary/visits/manage_activity_page';
    $this->load->view('inc/template',$data);
  }
  
  public function manage_symptoms_page(){
    $data['main_content']='infirmary/visits/manage_symptoms_page';
    $this->load->view('inc/template',$data);
  }

 // to show all symptoms table
 public function get_symptom_types(){  
  $result = $this->visits_model->get_symptom_types();
  echo json_encode($result);
}
  public function get_diagnosis_names(){  
    $result = $this->visits_model->get_diagnosis_names();
    echo json_encode($result);
  }
  public function get_test_names(){  
    $result = $this->visits_model->get_test_names();
    echo json_encode($result);
  }

  // to add a symptoms
  public function add_symptom(){
    $symptom_name = $_POST['symptom_name'];
    echo $this->visits_model->add_symptom($symptom_name);
  }
  public function check_symptom_exist()
  {
    $symptom_name = $_POST['symptom_name'];
    $result = $this->visits_model->check_symptom_exist($symptom_name);
    echo $result;
  }
  public function check_diagnosis_exist()
  {
    $diagnosis_name = $_POST['diagnosis_name'];
    $result = $this->visits_model->check_diagnosis_exist($diagnosis_name);
    echo $result;
  }
  public function add_diagnosis(){
    $diagnosis_name = $_POST['diagnosis_name'];
    echo $this->visits_model->add_diagnosis($diagnosis_name);
  }
  public function get_medicine_types(){
    
    $result = $this->Managemedicine_model->get_medicine_types();
    echo json_encode($result);
  }
  public function get_activity_types(){
    $result = $this->visits_model->get_activity_types();
    echo json_encode($result);
  }
  public function add_medicine(){
    $medicine_name = $_POST['medicine_name'];
    $medicine_type = $_POST['medicine_type'];
    $medicine_price = $_POST['medicine_price'];
    echo $this->Managemedicine_model->add_medicine_types($medicine_name,$medicine_type,$medicine_price);
  }

  public function update_medicine(){
    $medicine_id = $_POST['medicine_id'];
    $medicine_name = $_POST['medicine_name'];
    $medicine_type = $_POST['medicine_type'];
    $medicine_price = $_POST['medicine_price'];
    echo $this->visits_model->update_medicine_types($medicine_id,$medicine_name,$medicine_type,$medicine_price);
  }

  public function activate_medicine(){
    $medicine_id = $_POST['medicine_id'];
    $success = $this->Managemedicine_model->activate_medicine($medicine_id);
    echo json_encode($success);
  }
  public function deactivate_medicine(){
    $medicine_id = $_POST['medicine_id'];
    $success = $this->Managemedicine_model->deactivate_medicine($medicine_id);
    echo json_encode($success);
  }
  public function submit_activity(){
    $activity_name = $_POST['activity_name'];
    $activity_disc = $_POST['activity_disc'];
    $activity_date = $_POST['activity_date'];
    echo $this->visits_model->add_activity_types($activity_name,$activity_date,$activity_disc);
  }
  public function student_staff_report(){
    $data['main_content']='infirmary/visits/student_staff_report';
    $this->load->view('inc/template',$data);
  }
  public function visit_report(){
    // $stakeholder= $_POST['stake_holder_type'];
    // $name= $_POST['user_selection'];

    $result=$this->Student_staff_visit_model->visit_report($_POST);
    echo json_encode($result);
  }
  public function get_hospital_details(){
    $data['visit_obj']=$this->visits_model->get_hospital_details($_POST);
    echo json_encode($data);
  }
  public function get_medical_history(){
    $result=$this->visits_model->get_medical_history($_POST['visitor_id'], $_POST['visitor_type']);
    echo json_encode($result);
  }
  public function test_details(){
    $result=$this->visits_model->test_details($_POST);
    echo json_encode($result);
  }
  public function test_report(){
    $result=$this->visits_model->test_report($_POST);
    echo json_encode($result);
  }
  public function get_staff_all_visit(){
    $result = $this->Student_staff_visit_model->get_staff_all_visit();
    echo json_encode($result);
  }
  public function get_class_all_visit(){
    $result = $this->Student_staff_visit_model->get_class_section_all();
    echo json_encode($result);
  }

  public function activate_test(){
    $test_id = $_POST['test_id'];
    $success = $this->visits_model->activate_test($test_id);
    echo json_encode($success);
  }

  public function deactivate_test(){
    $test_id = $_POST['test_id'];
    $success = $this->visits_model->deactivate_test($test_id);
    echo json_encode($success);
  }
  public function add_test(){
    $test_name = $_POST['test_name'];
    echo $this->visits_model->add_test($test_name);
  }



   // visitor report
   public function visitor_report() {
   
    $data['main_content'] = 'infirmary/visits/visitor_report';
    $this->load->view('inc/template', $data);
  }

  public function hospitalization_report() {
   
    $data['main_content'] = 'infirmary/visits/hospitalization_report';
    $this->load->view('inc/template', $data);
  }

  public function generate_reports() {
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $result = $this->visits_model->getVisitor_report($from_date, $to_date);
    echo json_encode($result);
  }

  public function get_hospitalization_reports() {
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $result = $this->visits_model->getHospitalization_report($from_date, $to_date);
    echo json_encode($result);
  }

  public function generate_test_reports() {
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $result = $this->visits_model->get_test_report($from_date, $to_date);
    echo json_encode($result);
  }

  public function create_reports() {
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $result = $this->visits_model->visitor_type_count_data_report($from_date, $to_date);
    echo json_encode($result);
  }

   //to activate a symptom
  public function activate_symptom(){
    $capacity_id = $_POST['capacity_id'];
    $success = $this->visits_model->activate_symptom($capacity_id);
    echo json_encode($success);
  }
  public function activate_diagnosis(){
    $capacity_id = $_POST['capacity_id'];
    $success = $this->visits_model->activate_diagnosis($capacity_id);
    echo json_encode($success);
  }
  

  // to deactivate of symptom
  public function deactivate_symptom(){
    $capacity_id = $_POST['capacity_id'];
    $success = $this->visits_model->deactivate_symptom($capacity_id);
    echo json_encode($success);
  }
  public function deactivate_diagnosis(){
    $capacity_id = $_POST['capacity_id'];
    $success = $this->visits_model->deactivate_diagnosis($capacity_id);
    echo json_encode($success);
  }
 
  public function visitor_summary_report() {
    //$data['symtoms_list'] = $this->visits_model->get_symptoms_list_data();
    $data['main_content']    = 'infirmary/visits/visitor_summary_report';
    $this->load->view('inc/template', $data);
  }

  public function delete_test_record(){
    echo $this->visits_model->delete_test_record($_POST['test_id']);
  }
  
  public function health_history_details(){
    $student_id = $_POST['student_id'];
    $visitor_type = $_POST['visitor_type'];
    $disabled_fields = $this->visits_model->get_health_disabled_fields();
    if($visitor_type == 'student'){
      $result=$this->visits_model->whole_medical_history_360($student_id);
    }else{
      $result = array();
    }
    echo json_encode(array('result'=>$result, 'enabled'=>$disabled_fields));
  }

  public function student_vaccination_history_details() {
    $student_id = $_POST['student_id'];
   $result=$this->visits_model->student_vaccination_history_details($student_id);


   echo json_encode($result);
   
 }

 public function check_if_rfid_mapped() {
  $rfidValue= $_POST['rfidValue'];
  echo json_encode( $this->visits_model->check_if_rfid_mapped($rfidValue) );
 }

 public function create_form_for_guest(){
  $data['medicine_list'] = $this->visits_model->getMedicine_list();
  $data['main_content']='infirmary/Create visit/create_form_for_guest';
  $this->load->view('inc/template',$data);
}

public function medical_expense_mass_upload(){
  $data['class_name'] = $this->visits_model->get_classes();
  $data['main_content']='infirmary/medical_expense_mass_upload_page';
  $this->load->view('inc/template',$data);
}

public function add_guest() {
  $file_data= $_FILES['file_data'];
  $file= $this->s3FileUpload($file_data, 'infirmary');
  echo $this->visits_model->add_guest($file);
}

public function get_guests_by_name() {
  echo json_encode( $this->visits_model->get_guests_by_name() );
}

public function get_and_construct_guest_visitors_table() {
  echo json_encode( $this->visits_model->get_and_construct_guest_visitors_table() );
}

public function guest_visitor_report() {
   
  $data['main_content'] = 'infirmary/visits/guest_visitor_report';
  $this->load->view('inc/template', $data);
}

public function medical_expense_individual_report(){
  $data['main_content'] = 'infirmary/visits/medical_expense_individual_report_page';
  $this->load->view('inc/template', $data);
}

public function medical_expense_report(){
  $data['main_content'] = 'infirmary/visits/medical_expense_report_page';
  $this->load->view('inc/template', $data);
}

public function store_medical_expense(){
  $files_array = array();
	$files_string = '';
  if(isset($_FILES['attachments'])) {
    foreach ($_FILES['attachments']['name'] as $key => $file_name) {
      $file = array(
        'name' => $file_name,
        'type' => $_FILES['attachments']['type'][$key],
        'tmp_name' => $_FILES['attachments']['tmp_name'][$key],
        'error' => $_FILES['attachments']['error'][$key],
        'size' => $_FILES['attachments']['size'][$key]
      );
      $path = $this->s3FileUpload($file,'medical_expense');
      if($path['file_name'] != '') {
        array_push($files_array, array('name' => $file_name, 'path' => $path['file_name']));
      }
    }
    if(!empty($files_array)) {
      $files_string = json_encode($files_array);
    }
  }
  echo $this->visits_model->store_medical_expense($files_string);
}

public function get_medical_expense_data(){
  $result = $this->visits_model->get_medical_expense_data($_POST);
  echo json_encode($result);
}

public function get_medical_expense_reports(){
  $from_date = $_POST['from_date'];
  $to_date = $_POST['to_date'];
  $visitor_type = $_POST['visitor_type'];
  $result = $this->visits_model->get_medical_expense_reports($from_date,$to_date,$visitor_type);
  echo json_encode($result);
}

public function get_section_names(){
  $result = $this->visits_model->get_section_names($_POST['classId']);
  echo json_encode($result);
}

public function downloadCsvFormat_medical_expense(){
  $row = array();
  foreach ($this->medical_expense_student_columns as $column) {
    $row[] = $column;
  }
  $csvArray[] = $row;
    

      // Output CSV file
      header("Content-type: application/csv");
      header("Content-Disposition: attachment; filename=\"Student-Medical-Expense-Mass_upload".".csv\"");
      header("Pragma: no-cache");
      header("Expires: 0");

      $handle = fopen('php://output', 'w');
      foreach ($csvArray as $row) {
          fputcsv($handle, $row);
      }
      fclose($handle);
}

private  $medical_expense_student_columns = array(
  'Admission_Number',
  'Purpose',
  'Amount',
  'Payment_Date',
  'Remarks'
);

private  $medical_expense_staff_columns = array(
  'staff_id',
  'staff_name',
  'purpose',
  'amount',
  'payment_date',
  'remarks'
);

public function upload_csv(){
  if(isset($_FILES['expense_data']) && $_FILES['expense_data']['error'] === UPLOAD_ERR_OK) {
    $file_path = $_FILES['expense_data']['tmp_name'];
    $expese_arr = [];
    $this->load->library('csvimport'); 
    if ($this->csvimport->get_array($file_path)) {
        $expese_arr = $this->csvimport->get_array($file_path);
    }
    echo json_encode($expese_arr);
    } else {
        echo json_encode(array('error' => 'File upload failed.'));
    }
}

public function submit_csv_expense_data(){
  echo $this->visits_model->submit_student_csv_expense_data();
}

public function download_staff_CsvFormat_medical_expense(){
  $staff_data = $this->visits_model->get_staff_details();

  if (!empty($staff_data) && is_array($staff_data)) {
    $csvArray = array();

    // Add column headers
    $csvArray[] = $this->medical_expense_staff_columns;
   
    // Add rows of data
    foreach ($staff_data as $data) {
        $row = array();
        foreach ($this->medical_expense_staff_columns as $column) {
          
            // Assuming the column names in $payrollData match the keys in $payrollColums
            $row[] = $data->{$column} ?? ''; // Use null coalescing operator to handle missing data
        }
        // echo '<pre>';print_r($row);die();
        $csvArray[] = $row;
    }

    // Output CSV file
    header("Content-type: application/csv");
    header("Content-Disposition: attachment; filename=\"Staff-Medical-Expense-Mass_upload".".csv\"");
    header("Pragma: no-cache");
    header("Expires: 0");

    $handle = fopen('php://output', 'w');
    foreach ($csvArray as $row) {
        fputcsv($handle, $row);
    }
    fclose($handle);
    exit;
    } else {
        echo "No data found."; // or handle the case where no data is found
    }
}

public function upload_staff_csv(){
  if(isset($_FILES['expense_data']) && $_FILES['expense_data']['error'] === UPLOAD_ERR_OK) {
    $file_path = $_FILES['expense_data']['tmp_name'];
    $expese_arr = [];
    $this->load->library('csvimport'); 
    if ($this->csvimport->get_array($file_path)) {
        $expese_arr = $this->csvimport->get_array($file_path);
    }
    // echo '<pre>';print_r($expese_arr);die();
    echo json_encode($expese_arr);
    } else {
        echo json_encode(array('error' => 'File upload failed.'));
    }
}

//  public function download_lab_tests_report($id) {
//   $document = $this->visits_model->get_test_report_row($id);
//   $document_url = $this->filemanager->getFilePath($document->report);
//   $document_data = file_get_contents($document_url);
//   $this->load->helper('download');
//   force_download('download.pdf', $document_data, TRUE);
//  }

}

?>












<?php
/**
 * Name:    Oxygen
 * Author:  Manjukiran Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  21 October 2022
 *
 * Description: Controller for Infirmary Module. Entry point for Infirmary Module
 *
 * Requirements: PHP5 or above
 *
 */

// class Visits extends CI_Controller {
// 	function __construct() {
//     parent::__construct();
    
//     if (!$this->ion_auth->logged_in()) {
//       redirect('auth/login', 'refresh');
//     }
//     if (!$this->authorization->isModuleEnabled('INFIRMARY')) {
//       redirect('dashboard', 'refresh');
//     }
     
//     $this->load->model('visits_model');
     
//   }


//   /* Create visit controllers */
//   //Shows the Form for entering the Visit details
//   public function create_form() {
//     $data['main_content'] = 'infirmary/visits/create_form';
//     $this->load->view('inc/template', $data);
//   }
  


//   /* view visits controllers */
//     // medical history by their id
//   public function view_visits() {
//     $this->visits_model->medical_history();   
//     $data['main_content'] = 'infirmary/visits/view_visits';
//     $data['medical_history']= $this->visits_model->medical_history();
//     $this->load->view('inc/template', $data);
//   }

  


//   /*  manage symptoms page controllers */
//   // to show the symptom manage page
// public function manage_symptoms_page() {
//   $data['main_content'] = 'infirmary/visits/manage_symptoms_page';
//   $this->load->view('inc/template', $data);
// }

//  // to show all symptoms table
//   public function get_symptom_types(){  
//     $result = $this->visits_model->get_symptom_types();
//     echo json_encode($result);
//   }

//  // to add a symptoms
//   public function add_symptom(){
//     $symptom_name = $_POST['capacity_name'];
//     echo $this->visits_model->add_symptom($symptom_name);
//   }

//  //to check activation of a symptom
// //   public function delete_symptom(){
// //     $capacity_id = $_POST['capacity_id'];
// //     $success = $this->Symptom_model->delete_symptom($capacity_id);
// //     echo json_encode($success);
// //  }

//   // to activate a symptom
//   public function activate_symptom(){
//     $capacity_id = $_POST['capacity_id'];
//     $success = $this->visits_model->activate_symptom($capacity_id);
//     echo json_encode($success);
//   }

//   // to deactivate of symptom
//   public function deactivate_symptom(){
//     $capacity_id = $_POST['capacity_id'];
//     $success = $this->visits_model->deactivate_symptom($capacity_id);
//     echo json_encode($success);
//   }



//   /*    Visitor Report controllers     */

//   // visitor report
//   public function visitor_report() {
   
//     $data['main_content'] = 'infirmary/visits/visitor_report';
//     $this->load->view('inc/template', $data);
//   }

//   public function generate_reports() {
//     $from_date = $_POST['from_date'];
//     $to_date = $_POST['to_date'];
//     $result = $this->visits_model->getCircularData_report($from_date, $to_date);
//     echo json_encode($result);
//   }

  


// }

?>