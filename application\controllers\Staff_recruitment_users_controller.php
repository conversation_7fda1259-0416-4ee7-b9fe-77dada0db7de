<?php 
// Career form implementaion
class Staff_recruitment_users_controller extends CI_Controller

{
    function __construct() {
       parent::__construct();
       $this->load->model('Career_model');
    } 

    public function index() {
        $data['main_content'] = 'staff_career_users_view';
        $this->load->view('enquiry/inc/template', $data);
    }
    
    public function add_career_details() {
        $this->Career_model->add_career_details($_POST);
        redirect('Staff_recruitment_users_controller/thank_you_page');
    }

    public function thank_you_page() {
        $data['main_content'] = 'thank_you_page_view_career';
        $this->load->view('enquiry/inc/template', $data);
    }
    
}

?>