<?php defined('BASEPATH') or exit('No direct script access allowed');

class Birthday_Notifications extends CI_Controller{
    
    public function __construct(){
        parent::__construct();
        $this->load->model('Birthday_Notifications_Model');
        $this->load->library('mailer', 'mailer');
        $this->load->helper('texting_helper');
        $this->acad_year->loadAcadYearDataToSession();
    }
    public function index_old(){
        // trigger_error("In Sending Birthday Wishes");
        $birthday_sms_send = $this->settings->getSetting('birthday_sms_send');
        $birthday_mail_send = $this->settings->getSetting('birthday_mail_send');
        $data= $this->Birthday_Notifications_Model->studentDataforBirthdayList();
        $message_staff='';
        if($birthday_sms_send){
            $message = $this->settings->getSetting('birthday_message');
            $bd_message_staff = $this->settings->getSetting('birthday_message_staff');
            $staff_ids = $this->settings->getSetting('management_staff_ids');
            $data_father =$this->Birthday_Notifications_Model->fatherDataforBirthdayList();
            $data_mother= $this->Birthday_Notifications_Model->motherDataforBirthdayList();
            $data_staff = $this->Birthday_Notifications_Model->staffDataforBirthdayList();
            $mesages_to_send_father = array();
            $mesages_to_send_mother = array();
            $mesages_to_send_staff = array();
            $mesages_to_send_staffBirthday = array();
            $std_ids = array();
            /*foreach($data as $key => $val){
                $message_staff.=$val->std_name.'('.$val->class_name.$val->section_name.'),';
            }
            */
            
            if(!empty($data_father)) {
                foreach ($data_father as $key => $value) {
                    array_push($std_ids, $value->student_id);
                    $message_staff.=$value->std_name.'('.$value->class_name.$value->section_name.'), ';
                    $temp=$message;
                    $temp=str_replace("%%student_name%%", $value->std_name, $temp);
                    $temp=str_replace("%%parent_name%%", $value->parent_name, $temp);
                    $mesages_to_send_father[$value->student_id] = $temp;
                }
                $result = $this->__sendSMSFather($mesages_to_send_father); 
            }
            if(!empty($data_mother)) {
                foreach ($data_mother as $key => $value1) {
                    if(!in_array($value1->student_id, $std_ids)) {
                        array_push($std_ids, $value1->student_id);
                        $message_staff.=$value1->std_name.'('.$value1->class_name.$value1->section_name.'), ';
                    }
                    $temp1=$message;
                    $temp1=str_replace("%%student_name%%", $value1->std_name, $temp1);
                    $temp1=str_replace("%%parent_name%%", $value1->parent_name, $temp1);
                    $mesages_to_send_mother[$value1->student_id] = $temp1;
                }
                $result1 = $this->__sendSMSMother($mesages_to_send_mother);
            }
            if(!empty($data_staff)) {
                foreach ($data_staff as $key => $value2) {
                    if($message_staff != '') {
                        $message_staff .= 'and ';
                    }
                    $message_staff .= $value2->staff_name.' (Staff), ';
                    $temp2=$bd_message_staff;
                    $temp2=str_replace("%%staff_name%%", $value2->staff_name, $temp2);
                    $mesages_to_send_staffBirthday[$value2->staff_id] = $temp2;
                }
                $result3=$this->__sendSMSStaffBirthday($mesages_to_send_staffBirthday);
            }
            if(!empty($staff_ids) && $message_staff != '') {
                $message_staff.=' are the students/staff who are wished for their birthday on '.date("d-m-Y");
                $staff_ids = $staff_ids;
                foreach($staff_ids as $key){
                    $mesages_to_send_staff[$key->id] = $message_staff;
                }
                $result2 = $this->__sendSMSStaff($mesages_to_send_staff);
            }
        }
        if($birthday_mail_send){
            $mail_template=$this->Birthday_Notifications_Model->getBirthdayEmailTemplate();
            if (!empty($mail_template)){
                $this->_birthday_email_send($mail_template, $data);            
            }
        }
    }

    private function __sendSMSFather ($mesages_to_send) {
        $sent_by = 1;
        $input_arr = array();
        $input_arr['student_id_messages'] = $mesages_to_send;
        $input_arr['source'] = 'Birthday SMS';
        $text_send_to = 'Father';
        $input_arr['mode'] = 'sms';
        $input_arr['send_to'] = $text_send_to;
        $success = sendUniqueText($input_arr);
        if($success['success'] != ''){
            $insId1 = 1;
        } else {
            $insId1 = 0;
        }
        return $insId1;
    }
    private function __sendSMSMother ($mesages_to_send) {
        $sent_by = 1;
        $input_arr = array();
        $input_arr['student_id_messages'] = $mesages_to_send;
        $input_arr['source'] = 'Birthday SMS';
        $text_send_to = 'Mother';
        $input_arr['mode'] = 'sms';
        $input_arr['send_to'] = $text_send_to;
        $success = sendUniqueText($input_arr);
        if($success['success'] != ''){
            $insId1 = 1;
        } else {
            $insId1 = 0;
        }
        return $insId1;
    }
    private function __sendSMSStaff ($mesages_to_send) {
        $sent_by = 1;
        $input_arr = array();
        $input_arr['staff_id_messages'] = $mesages_to_send;
        $input_arr['source'] = 'Birthday SMS';
        $input_arr['mode'] = 'sms';
        $success = sendUniqueText($input_arr);
        if($success['success'] != ''){
            $insId1 = 1;
        } else {
            $insId1 = 0;
        }
        return $insId1;
    }
    private function __sendSMSStaffBirthday ($mesages_to_send) {
        $sent_by = 1;
        $input_arr = array();
        $input_arr['staff_id_messages'] = $mesages_to_send;
        $input_arr['source'] = 'Birthday SMS';
        $input_arr['mode'] = 'sms';
        $success = sendUniqueText($input_arr);
        if($success['success'] != ''){
            $insId1 = 1;
        } else {
            $insId1 = 0;
        }
        return $insId1;
    }

    private function _birthday_email_send($mail_template, $stdData){
        $members = [];
        foreach ($stdData as $key => $data) {
            array_push($members, $data->f_email,$data->m_email);
        }
        $memberEmail = [];
        foreach ($members as $key => $val) {
            $memberEmail[]['email'] = $val;
        }
        return 0; 
    }

    public function send_birthday_notification_sms(){
        // trigger_error("In Sending Birthday Wishes");
        $birthday_sms_send = $this->settings->getSetting('birthday_sms_send');
        $birthday_send_mode = json_decode($this->settings->getSetting('birthday_send_mode'));
        $smsMode = 'notification';
        if (!empty($birthday_send_mode)) {
            $smsMode = $birthday_send_mode[0];
        }
        $birthday_mail_send = $this->settings->getSetting('birthday_mail_send');
        $message_staff='';
        if($birthday_sms_send){
            $student_data= $this->Birthday_Notifications_Model->studentDataforBirthdayList();
            $data_staff = $this->Birthday_Notifications_Model->staffDataforBirthdayList();
            $message = $this->settings->getSetting('birthday_message');
            $bd_message_staff = $this->settings->getSetting('birthday_message_staff');
            $staff_ids = $this->settings->getSetting('management_staff_ids');
            $school_name = $this->settings->getSetting('school_name');
            $acad_year_id = $this->settings->getSetting('academic_year_id');
            $student_notification = array();
            $staff_notification = array();
            $mangement__staff_notification = array();
            $stdIds = array();
            if (!empty($student_data)) {
                foreach ($student_data as $key => $value) {
                    $message_staff.=$value->std_name.'('.$value->class_name.$value->section_name.'), ';
                    $temp=$message;
                    $temp=str_replace("%%student_name%%", $value->std_name, $temp);
                    // required parent name ?
                    // $temp=str_replace("%%parent_name%%", $value->parent_name, $temp);
                    $temp=str_replace("%%parent_name%%", '', $temp);
                    $student_notification[$value->student_id] = $temp;
                }
            }
            if(!empty($data_staff)) {
                foreach ($data_staff as $key => $value2) {
                    if($message_staff != '') {
                        $message_staff .= 'and ';
                    }
                    $message_staff .= $value2->staff_name.' (Staff), ';
                    $temp2=$bd_message_staff;
                    $temp2=str_replace("%%staff_name%%", $value2->staff_name, $temp2);
                    $staff_notification[$value2->staff_id] = $temp2;
                }
            }

            $input_array = array(
                'mode' => $smsMode, 
                'title' => $school_name, 
                'source' => 'Birthday SMS',
                'student_url' => site_url('parent_controller/texts'),
                'staff_url' => site_url('communication/texting/staff_texts'),
                'visible' => 1,
                'send_to' => 'Both',
                'acad_year_id' => $acad_year_id
              );
                // echo "<pre>"; print_r($input_array); die();
              if(!empty($student_notification)) {
                $input_array['student_id_messages'] = $student_notification;
              }

            if(!empty($staff_ids) && $message_staff != '') {
                $message_staff.='are the students/staff who are wished for their birthday on '.date("d-m-Y");
                foreach($staff_ids as $key){
                    $mangement__staff_notification[$key->id] = $message_staff;
                }
            }
            if(!empty($staff_notification)) {
                $input_array['staff_id_messages'] = $staff_notification + $mangement__staff_notification;
            }

            $this->load->helper('texting_helper');
            $result = sendUniqueText($input_array);
        }
        if($birthday_mail_send){
            $mail_template=$this->Birthday_Notifications_Model->getBirthdayEmailTemplate();
            if (!empty($mail_template)){
                $this->_birthday_email_send($mail_template, $student_data);            
            }
        }
    }
}

?>