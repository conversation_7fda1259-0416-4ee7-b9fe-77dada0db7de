<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Period_Dealloc extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('SUBSTITUTION') && $this->authorization->isAuthorized('SUBSTITUTION.MODULE')) {
      redirect('dashboard', 'refresh');
    }
  }

  public function index() {
    $data['main_content']    = 'substitution_v2/period_dealloc/index.php';
    $this->load->view('inc/template', $data);
  }

}