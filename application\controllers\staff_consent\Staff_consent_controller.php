<?php 

class Staff_consent_controller extends CI_Controller
{
    private $yearId;
    function __construct(){
       parent:: __construct();
       $this->load->model('Staff_consent_model');
       $this->config->load('form_elements');
        $this->yearId =  $this->acad_year->getAcadYearId();
        $this->load->library('filemanager');
    }

    public function index(){
        $site_url=site_url();
        $data['staffs']=$this->Staff_consent_model->get_active_staff();
        $data['department']=$this->Staff_consent_model->get_department();
        $data['staff_type']=$this->Staff_consent_model->get_staff_type();
        $data['main_content'] = 'staff_consent/index';
        $this->load->view('inc/template', $data);
    }

    public function get_consents_data(){
        $data = $this->Staff_consent_model->get_consents_data();
        echo json_encode($data);
    }

    public function get_staff_table_data(){
        $data = $this->Staff_consent_model->get_staff_table_data($_POST['staff_type'],$_POST['staff_department']);
        echo json_encode($data);
    }

    public function update_staff_consent(){
        $data = $this->Staff_consent_model->update_staff_consent($_POST,$this->s3FileUpload($_FILES['file_input'], 'staff_consent'));
        echo json_encode($data);
    }

    private function s3FileUpload($file, $folder_name = 'staff_recruitment'){
        if ($file['tmp_name'] == '' || $file['name'] == '') {
            return ['status' => 'empty', 'file_name' => ''];
        }
        return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
    }

    public function unpublish_consent(){
        $data = $this->Staff_consent_model->unpublish_consent($_POST['consent_id']);
        echo json_encode($data);
    }

    public function publish_consent(){
        $data = $this->Staff_consent_model->publish_consent($_POST['consent_id']);
        $email_send_data = $this->Staff_consent_model->get_data_to_send_email($_POST['consent_id']);
        $sent_by = $this->authorization->getAvatarStakeHolderId();
        $member_email = [];
        foreach($email_send_data->to_emails as $key => $val){
            array_push($member_email,$val->email);
            $email_data = [];
            $email_obj = new stdClass();
            $email_obj->stakeholder_id = $val->staff_id;
            $email_obj->avatar_type = 4;
            $email_obj->email = $val->email;
            $email_data[] = $email_obj;

            $email_master_data = array(
                'subject' => $email_send_data->email_subject,
                'body' => $email_send_data->content,
                'source' => 'Staff Consent Form Publishing',
                'sent_by' => $sent_by,
                'recievers' => "Staff",
                'from_email' => $email_send_data->registered_email,
                'files' => json_encode($email_send_data->attachment),
                'acad_year_id' => $this->acad_year->getAcadYearID(),
                'visible' => 1,
                'sender_list' => NULL,
                'sending_status' => 'Completed'
            );

            $this->load->model('communication/emails_model');
            $email_master_id = $this->emails_model->saveEmail($email_master_data);
            $this->emails_model->save_sending_email_data($email_data, $email_master_id);
            $this->_send_mail($email_send_data->content, $email_send_data->email_subject, $member_email, $email_send_data->registered_email,array($email_send_data->attachment)); 
            
        }
        echo json_encode($data);
    }

    private function _send_mail($body,$title,$memberEmail,$fromemail,$files_array){
        $this->load->helper('email_helper');
    
          $files_string = '';
          if(!empty($files_array)) {
            $files_string = json_encode($files_array);
          }
          $emails_array = array_chunk($memberEmail,600);
          foreach($emails_array as $key =>$val){
            $result = sendEmail($body, $title, 0, $val, $fromemail, json_decode($files_string));
          }
        return $result;
      }

    public function delete_consent(){
        $data = $this->Staff_consent_model->delete_consent($_POST['consent_id']);
        echo json_encode($data);
    }

    public function view_consent_details(){
        $data = $this->Staff_consent_model->view_consent_details($_POST['consent_id']);
        echo json_encode($data);
    }

    public function view_consent_report(){
        $data = $this->Staff_consent_model->view_consent_report($_POST['consent_id']);
        echo json_encode($data);
    }

    public function send_reminder(){
        $data = $this->Staff_consent_model->get_staff_ids($_POST['consent_id']);
        if(count($data['staffIds'])>0){
            $input_array = array(
                'mode' => 'notification', 
                'title' => 'Staff consent', 
                'source' => 'Staff Consent notification',
                'visible' => 1,
                'send_to' => 'staff',
                'acad_year_id' => $this->yearId,
                'staff_ids'=> $data['staffIds'],
                'staff_url' => site_url('Staff_consent_form_controller'),
                'message' =>"Please respond to the consent form sent to you on". $data['date'] . ". Thank you!"
              );
              $this->load->helper('texting_helper');
              $response = sendText($input_array);
              echo 1;
        }else{
            echo 2;
        }
    }


    public function get_modify_consent_data(){
        $data = $this->Staff_consent_model->get_modify_consent_data($_POST['consent_id']);
        echo json_encode($data);
    }


}

?>