<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  20 April 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_dashboard
 */
class Sales extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('SALES')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('sales/sales_model');
    $this->load->model('student/Student_Model');
    $this->load->library('filemanager');
    $this->config->load('form_elements');
  } 

  public function index(){
    $site_url = site_url();
    $data['tiles'] = array(
        [
          'title' => 'Sales',
          'sub_title' => 'Collect sales for students',
          'icon' => 'svg_icons/sales.svg',
          'url' => $site_url.'sales/sales/sales_collect',
          'permission' => 1
        ],
        [
          'title' => 'Sales History',
          'sub_title' => 'View & Print receipts (Only for existing students)',
          'icon' => 'svg_icons/daybook.svg',
          'url' => $site_url.'sales/sales/sales_history',
          'permission' => 1
        ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
        [
          'title' => 'Daily Transaction',
          'sub_title' => 'Daily Transaction',
          'icon' => 'svg_icons/dailytransaction.svg',
          'url' => $site_url.'sales/sales/daily_transcation_sales',
          'permission' => 1
        ],
        [
          'title' => 'New Student sales',
          'sub_title' => 'New Student sales list',
          'icon' => 'svg_icons/dailytransaction.svg',
          'url' => $site_url.'sales/sales/new_student_sales_list',
          'permission' => $this->authorization->isAuthorized('SALES.NEW_STUDENT_SALES_REPORT')
        ],
        [
          'title' => 'Cancellation Report',
          'sub_title' => 'View cancelled receipt.',
          'icon' => 'svg_icons/cancelationreport.svg',
          'url' => $site_url.'sales/sales/receipt_canceled_report',
          'permission' => 1
        ],
        [
          'title' => 'Non Reconcile Report',
          'sub_title' => 'View Non Reconcile Report.',
          'icon' => 'svg_icons/nonreconciledreport.svg',
          'url' => $site_url.'sales/sales/non_reconciled_report',
          'permission' => $this->authorization->isAuthorized('SALES.NON_RECONCILE_REPORT')
        ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['acc_tiles'] = array(
        [
          'title' => 'Sales Accounts',
          'sub_title' => 'Sales Accounts',
          'icon' => 'svg_icons/account.svg',
          'url' => $site_url.'sales/sales/sales_accounts',
          'permission' => $this->authorization->isSuperAdmin()
        ]
    );
    $data['acc_tiles'] = checkTilePermissions($data['acc_tiles']);

    $data['admin_tiles'] = array(
        [
          'title' => 'Re-generate PDF Receipt',
          'sub_title' => 'Re-generate PDF Receipt',
          'icon' => 'svg_icons/regeneratepdfreceipt.svg',
          'url' => $site_url.'sales/sales/re_generate_pdf',
          'permission' => $this->authorization->isSuperAdmin()
        ]
    );
    $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'sales/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'sales/index_mobile';
    }else{
      $data['main_content'] = 'sales/index';    	
    }

    
    $this->load->view('inc/template', $data);
  }
  
  public function create_sales_item($id = null){
    if ($id != null) {
      $data['edititem'] = $this->sales_model->edit_item($id);
    }
    $data['itemList'] = $this->sales_model->get_items();
    $data['main_content'] = 'sales/items/index';
    $this->load->view('inc/template', $data);  
  }

  public function sales_item_add(){
    $addItems = $this->sales_model->add_items();
    if ($addItems) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
    } else {
        $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('sales/sales/create_sales_item');

  }

  public function sales_item_update($id){
    $addItems = $this->sales_model->update_item($id);
    if ($addItems) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
    } else {
        $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('sales/sales/create_sales_item');
  }

  public function deleteItem($id) {
    $deleteVariant = $this->sales_model->delete_item($id);
    if ($deleteVariant) {
        $this->session->set_flashdata('flashSuccess', 'Deleted Succssfully');
        redirect('items/items/index');
    } else {
        $this->session->set_flashdata('flashError', 'Something Wrong..');
        redirect('items/items/index');
    }
  }


  public function sales_variant($id) {
    $data['itemName'] = $this->sales_model->getItemObject($id);
    $data['select_Dropdown'] = $this->sales_model->getDropdownbyItmeWise($data['itemName']->item_type);
    $data['variantList'] = $this->sales_model->get_variants($id, $data['itemName']->item_type);
    //echo "<pre>"; print_r($data['variantList']); die();
    $data['itemId'] = $id;
    $data['main_content'] = 'sales/variant/index';
    $this->load->view('inc/template', $data);
  }

  public function addVariant($itemId) {
    $result = $this->sales_model->add_variant($itemId);
    if ($result == 1) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
    } else if ($result == 2) {
         $this->session->set_flashdata('flashSuccess', 'Variant Already Present');
    } else {
        $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('sales/sales/sales_variant/'.$itemId);      
  }

  public function editVariant($id, $item_id, $type_id) {
    $data['select_Dropdowntype'] = $this->sales_model->getTypeDetails($id);
    $data['getVariant'] = $this->sales_model->get_variants($id, $type_id);
    $data['editVariant'] = $this->sales_model->edit_variant($id, $item_id, $type_id);
    $data['main_content'] = 'sales/variant/index';
    $this->load->view('inc/template', $data);
  }

  public function updateVariant($id, $item_id) {
    $updateVariant = $this->sales_model->update_variant($id);
    if ($updateVariant) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Updated.');
        redirect('items/variant/index/' . $item_id);
    } else {
        $this->session->set_flashdata('flashError', 'Something Wrong..');
        redirect('sales/sales/sales_variant/' . $item_id);
    }
  }

  public function deleteVariant($item_id,$id) {
    $deleteVariant = $this->sales_model->delete_variants($id, $item_id);
    if ($deleteVariant) {
      $this->session->set_flashdata('flashSuccess', 'Deleted Succssfully');
      redirect('sales/sales/sales_variant/' . $item_id . '/' . $id);
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('sales/sales/sales_variant/' . $item_id . '/' . $id);
    }
  }


  public function search_class_student(){
    $mode = $_POST['mode'];
    switch ($mode) {
      case 'admission_no':
        $admission_no = $_POST['admission_no'];
        $stdData = $this->sales_model->get_sales_student_detailbyId($admission_no, 0);
        break;
      case 'class':
        $classId = $_POST['classId'];
        $stdData = $this->Student_Model->getstdDataByClass($classId,['2']);
        break;
      case 'student':
        $student_id = $_POST['student_id'];
        $stdData = $this->sales_model->get_sales_student_detailbyId(0,$student_id);
        break;
      case 'name':
        $std_id = $_POST['std_id'];
        $stdData = $this->sales_model->get_sales_student_detailbyId(0,$std_id);
        break;
    }
    echo json_encode($stdData);
  }



  public function sales_collect(){
   
    $data['classList'] = $this->Student_Model->getClassNames();
    
    $data['products'] = $this->sales_model->get_prodcuts_all();
    $data['studentNames'] = $this->sales_model->getstudentallNames();
    $allow_new = $this->settings->getSetting('allow_sales_for_new_students');
    $data['manual_receipt'] = $this->settings->getSetting('sales_manual_receipt_number');
    $data['allow_new'] = 0;
    if($allow_new) {
      $data['allow_new'] = 1;
    }
    // echo "<pre>"; print_r($data['products']); die();
    // $data['main_content'] = 'sales/collect/index';
    $data['main_content'] = 'sales/collect/sell';
    $this->load->view('inc/template_fee', $data);
  }

  public function collect_sales() {
    $data['classList'] = $this->Student_Model->getClassNames();
    
    $data['products'] = $this->sales_model->get_prodcuts_all();
    $data['studentNames'] = $this->sales_model->getstudentallNames();
    // echo "<pre>"; print_r($data['products']); die();
    $data['main_content'] = 'sales/collect/sell';
    $this->load->view('inc/template', $data);
  }

  public function collect_sales_fee($std_id){
   
    // echo "<pre>"; print_r($data['products']); die();
    $data['main_content'] = 'sales/collect/transaction/index';
    $this->load->view('inc/template', $data);
  }

  public function fetch_jsonformatVariants(){
    $result = $this->sales_model->get_prodcuts_all();
    echo json_encode($result);
  }

  public function serachitemsIdwiseOption(){
    $itemsId = $this->input->post('itemsId');
    $stdId = $this->input->post('stdId');
    $clasId = $this->input->post('clasId');
    $result = $this->sales_model->get_itemsOptionbyItmesId($itemsId,$stdId,$clasId);
    echo json_encode($result);
  }

  public function serachItmesOptionwiseAmount(){
    $optionItmesId = $this->input->post('optionItmesId');
    $result = $this->sales_model->get_amountByoptiontimesId($optionItmesId);
    echo json_encode($result);
  }

  public function submit_sales_transaction(){

    $input = $this->input->post();
    if ($input['sale_type'] == 'new') {
      $input['student'] = '';
    }
    $catIds = [];
    $products = [];
    foreach ($input['prodcuts'] as $key => $product) {
      list($catId, $productId) = explode('_', $product);
      array_push($catIds,$catId);
      array_push($products,$productId);
    }
    $temp = [];
    foreach ($catIds as $key => $catId) {
      $temp[$catId][] = array(
        'prodcuts'=>$products[$key],
        'variants'=>$input['variants'][$key],
        'quantity'=>$input['quantity'][$key],
        'amount'=>$input['amount'][$key],
        'current_quantity'=>$input['current_quantity'][$key],
      );
    }
    if ($input['final_amount'] != 0) {
      $stransIds = [];
      foreach ($temp as $catId => $prodcuts) {
        $this->db->trans_begin();

        $sTransId = $this->sales_model->insert_sales_transaction($input['student'], $input, $catId, $prodcuts);
        if (empty($sTransId)) {
          $this->db->trans_rollback();
        }else{
          $manual_receipt = $this->settings->getSetting('sales_manual_receipt_number');
          if ($manual_receipt) {
            $result =  $this->sales_model->update_receipt_sale_transcation_manual($sTransId, $catId, $input);
          }else{
            $result =  $this->sales_model->update_receipt_sale_transcation($sTransId, $catId, $input);
          }
         if (empty($result)) {
          $this->db->trans_rollback();
          $this->session->set_flashdata('flashError', 'Receipt book not found');
          redirect('sales/sales/sales_collect');
         }
          array_push($stransIds, $sTransId);
        }
        $this->db->trans_commit();
      }

      $this->session->set_userdata('sTransIds', $stransIds);
      $this->session->set_userdata('std_id', $input['student']);
      $this->session->set_userdata('sale_type', $input['sale_type']);
      $this->session->set_userdata('new_student_name', $input['new_std_name']);
      redirect('sales/sales/receipts_sales');
    }else{
      $this->session->set_flashdata('flashError', 'Enter the Amount');
      redirect('sales/sales/sales_collect');
    }
  }

  public function receipts_sales( $canceled = ''){
    $data['cancel'] = $canceled;
    $sTransIds = $this->session->userdata('sTransIds');
    $std_id = $this->session->userdata('std_id');
    $sale_type = $this->session->userdata('sale_type');
    $new_student_name = $this->session->userdata('new_student_name');
    if (empty($sTransIds)) {
      redirect('sales/sales/sales_collect');
    }
    $sales_data = [];
    foreach ($sTransIds as $key => $stransId) {
      $receipt_data = $this->sales_model->get_sales_receipt_data($stransId, $std_id, $sale_type);
      $result = $this->_create_template_sales_pdf_format($receipt_data, $receipt_data->receipt_template);
      $update =  $this->sales_model->update_html_receipt_sales($result, $stransId);
      if($update) {
        $this->__generateSales_pdf_receipt($result, $stransId);
      }
      array_push($sales_data, $receipt_data);
    }

    $this->session->unset_userdata('sTransIds');
    $data['sales_data'] = $sales_data;
    $data['sales_type'] = $sale_type;
    $data['new_student_name'] = $new_student_name;
    $this->session->unset_userdata('new_student_name');
    $this->session->unset_userdata('sale_type');
    $receiptpath = $this->settings->getSetting('school_short_name');
    $fileCheck = FCPATH."application/views/sales/receipts/".$receiptpath.'.php';
    $data['main_content'] = 'sales/receipts/'.$receiptpath;
    if (!file_exists($fileCheck)) {
      $data['main_content'] = 'sales/receipts/sales_receipts';
    }
    $this->load->view('inc/template_fee', $data);
  }

  private function _create_template_sales_pdf_format($sales_data, $template){

    $sale_type = $this->session->userdata('sale_type');
    if($sale_type == 'existing') {
      $template = str_replace('%%student_name%%', $sales_data->std_data->std_name, $template);
      $class = $sales_data->std_data->class_name.''.$sales_data->std_data->section_name;
      $template = str_replace('%%class%%',$class, $template);
      $template = str_replace('%%class_name%%',$sales_data->std_data->class_name, $template);
    } else if($sale_type == 'new') {
      $new_student_name = $this->session->userdata('new_student_name');
      $template = str_replace('%%student_name%%', $new_student_name, $template);
      $template = str_replace('%%class%%',$sales_data->class_name, $template);
      $template = str_replace('%%class_name%%','NA', $template);
    }else{
      $template = str_replace('%%student_name%%', $sales_data->std_data->std_name, $template);
      $class = $sales_data->std_data->class_name.''.$sales_data->std_data->section_name;
      $template = str_replace('%%class%%',$class, $template);
      $template = str_replace('%%class_name%%',$sales_data->std_data->class_name, $template);
    }

    $template = str_replace('%%receipt_no%%',$sales_data->receipt_no, $template);
    $template = str_replace('%%transaction_date%%', $sales_data->receipt_date, $template);
    $template = str_replace('%%remarks%%', $sales_data->remarks, $template);
    $i=1;
    $totalAmount = 0;
    $sales_part = '<table>';
    $sales_part .= '<tr>';
    $sales_part .= '<td>S. No.</td>';
    $sales_part .= '<td>Category</td>';
    $sales_part .= '<td>HSN/SAC</td>';
    $sales_part .= '<td>Size</td>';
    $sales_part .= '<td>Quantity</td>';
    $sales_part .= '<td>Rate</td>';
    $sales_part .= '<td>Amount</td>';
    $sales_part .= '</tr>';
     foreach ($sales_data->trans as $key => $val) {  
      $totalAmount += $val->amount;
      $sales_part.='<tr>';
      $sales_part.='<td style="vertical-align: middle;">'.$i++.'</td>';
      $sales_part.='<td>'.$val->product_name .' - '.$val->variant_name.'</td>';
      $sales_part.='<td>'.$val->hsn_sac.'</td>';
      $sales_part.='<td></td>';
      $sales_part.='<td>'.$val->quantity.'</td>';
      $sales_part.='<td></td>';
      $sales_part.='<td>'.$val->amount.'</td>';
      $sales_part.='</tr>';
    }

    $sales_part.= '<tr>';
    $sales_part.='<td colspan="6" style="text-align: right;">Total</td>';
    $sales_part.='<td>'.$totalAmount.'</td>'; 
    $sales_part.='</tr>';

    if($sales_data->card_charge_amount != 0) {
      $sales_part.='<tr>';
      $sales_part.='<td colspan="6" style="text-align:right;">Card Charge Amount</td>';
      $sales_part.='<td>'.$sales_data->card_charge_amount.'</td>';
      $sales_part.='</tr>';
    }

    $sales_part.='<tr>';
    $sales_part.='<td colspan="6" style="text-align:right;border: solid 1px #474747;"><strong>Total Fee</strong></td>';
    $sales_part.='<td style="border: solid 1px #474747;">'.($sales_data->total_amount + $sales_data->card_charge_amount).'</td>';
    $sales_part.='</tr>';

    $sales_part .= '</table>';
   
    $amountInWords = $this->getIndianCurrency($sales_data->total_amount + $sales_data->card_charge_amount);


    $payment_mode = '<table style="margin:0">';
      if ($sales_data->payment_type == '7') {
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>Card : </b>'.$sales_data->cheque_dd_number.'</td>';
        $payment_mode .='</tr>';
      }else if($sales_data->payment_type == '9'){
        $payment_mode .='<tr>';
        $payment_mode .='<td>Cash</td>';
        $payment_mode .='</tr>';
      }else if($sales_data->payment_type == '4'){
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>Cheque No : </b>'.$sales_data->cheque_dd_number.'<br>'.'<b>Cheque Bank : </b>' .$sales_data->bank_name.'<br> <b>Cheque Branch : </b> '.$sales_data->bank_branch.'<br> <b>Cheque Date : </b> '.date('d-m-Y',strtotime($sales_data->cheque_dd_date)).'</td>';
        $payment_mode .='</tr>';
      }else if($sales_data->payment_type == '1'){
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>DD No : </b>'.$sales_data->cheque_dd_number.'<br>'.'<b>DD Bank : </b>' .$sales_data->bank_name.'<br> <b>DD Branch : </b> '.$sales_data->bank_branch.'<br> <b>DD Date : </b> '.date('d-m-Y',strtotime($sales_data->cheque_dd_date)).'</td>';         
        $payment_mode .='</tr>';
      }else if($sales_data->payment_type == '8'){
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>Net Banking : </b>'.$sales_data->cheque_dd_number.'</td>';
        $payment_mode .='</tr>';
      }else{
        $payment_mode .='<tr>';
        $payment_mode .='<td>Cash</td>';
        $payment_mode .='</tr>';
      }
    $payment_mode .= '</table>';
    $template = str_replace('%%rupees_in_words%%',ucwords($amountInWords), $template);
    $template = str_replace('%%payment_modes%%',$payment_mode, $template);
    $template = str_replace('%%sales%%',$sales_part, $template);
    return $template;
  }

  private function __generateSales_pdf_receipt($html, $stransId) {
    $school = CONFIG_ENV['main_folder'];
    $path = $school.'/sales_reciepts/'.uniqid().'-'.time().".pdf";

    $bucket = $this->config->item('s3_bucket');

    $status = $this->sales_model->update_sales_path($stransId, $path);
    $page = 'landscape';
    $receipt_for = $this->settings->getSetting('school_short_name');
    if ($receipt_for === 'nhis') {
      $page = 'portrait';
    }
    if ($receipt_for === 'apstrust') {
      $page = 'portrait';
    }
    $page_size = 'a4';
    $curl = curl_init();
    $postData = urlencode($html);
    $username = CONFIG_ENV['job_server_username'];
    $password = CONFIG_ENV['job_server_password'];
    $return_url = site_url().'Callback_Controller/updateSalesPdfLink';

    curl_setopt_array($curl, array(
        CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERPWD => $username . ":" . $password,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
        CURLOPT_HTTPHEADER => array(
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Content-Type: application/x-www-form-urlencoded",
            "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
        ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);
  }

  public function sales_receipt_history($stransId, $std_id, $canceled = ''){
    $data['cancel'] = $canceled;
    $data['traverse_to'] = '1';
    $data['std_id'] = $std_id;
    if($std_id == 0) {
      $data['sales_type'] = 'new';
    } else {
      $data['sales_type'] = 'existing';
    }
    $sales_data[] = $this->sales_model->get_sales_receipt_data($stransId, $std_id, $data['sales_type']);
    $data['sales_data'] = $sales_data;
    if($std_id == 0) {
      $data['new_student_name'] = $sales_data[0]->student_name;
    }
    $receiptpath = $this->settings->getSetting('school_short_name');
    $fileCheck = FCPATH."application/views/sales/receipts/".$receiptpath.'.php';
    $data['main_content'] = 'sales/receipts/'.$receiptpath;
    if (!file_exists($fileCheck)) {   
      $data['main_content'] = 'sales/receipts/sales_receipts';
    }
    $this->load->view('inc/template_fee', $data);
  }

  public function getIndianCurrency(float $number)
  {
      $decimal = round($number - ($no = floor($number)), 2) * 100;
      $hundred = null;
      $digits_length = strlen($no);
      $i = 0;
      $str = array();
      $words = array(0 => '', 1 => 'one', 2 => 'two',
          3 => 'three', 4 => 'four', 5 => 'five', 6 => 'six',
          7 => 'seven', 8 => 'eight', 9 => 'nine',
          10 => 'ten', 11 => 'eleven', 12 => 'twelve',
          13 => 'thirteen', 14 => 'fourteen', 15 => 'fifteen',
          16 => 'sixteen', 17 => 'seventeen', 18 => 'eighteen',
          19 => 'nineteen', 20 => 'twenty', 30 => 'thirty',
          40 => 'forty', 50 => 'fifty', 60 => 'sixty',
          70 => 'seventy', 80 => 'eighty', 90 => 'ninety');
      $digits = array('', 'hundred','thousand','lakh', 'crore');
      while( $i < $digits_length ) {
          $divider = ($i == 2) ? 10 : 100;
          $number = floor($no % $divider);
          $no = floor($no / $divider);
          $i += $divider == 10 ? 1 : 2;
          if ($number) {
              $plural = (($counter = count($str)) && $number > 9) ? '' : null;
              $hundred = ($counter == 1 && $str[0]) ? ' and ' : null;
              $str [] = ($number < 21) ? $words[$number].' '. $digits[$counter]. $plural.' '.$hundred:$words[floor($number / 10) * 10].' '.$words[$number % 10]. ' '.$digits[$counter].$plural.' '.$hundred;
          } else $str[] = null;
      }
      $Rupees = implode('', array_reverse($str));
      $paise = ($decimal) ? "." . ($words[$decimal / 10] . " " . $words[$decimal % 10]) . ' Paise' : '';
      return ($Rupees ? $Rupees . 'Rupees ' : '') . $paise ;
  }

  public function create_receipt_book(){
    $receipt_book = $this->sales_model->get_sales_receipt_books();
    $data['recept_format'] = $this->fee_library->receipt_format_creation_for_sales($receipt_book);
    $data['main_content'] = 'sales/receipt_book';
    $this->load->view('inc/template', $data);
  }

  public function insert_receipts_book(){
    $result = $this->sales_model->insert_receipts_book_sales();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'sales Receipt algo created Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('sales/sales/create_receipt_book');
  }

  public function delete_receipt_book_for_sales($id){
    $result = $this->sales_model->delete_receipts_book_sales($id);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'sale Receipt algo delete Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('sales/sales/create_receipt_book');
  }

  public function serach_prodcut_wise_varints(){
    $productsId = $_POST['productsId'];
    $result = $this->sales_model->get_prodcut_varints($productsId);
    echo json_encode($result);
  }

  public function get_quantity_amount_of_variantId(){
    $vId = $_POST['vId'];
    $result = $this->sales_model->get_varints_price_quantity($vId);
    echo json_encode($result);
  }

  public function daily_transcation_sales(){
    $data['category'] = $this->sales_model->get_prodcut_variant_list_report();
    // echo "<pre>"; print_r($data['category']); die();
    // $result = $this->sales_model->get_daily_transaction_report();
    $data['main_content'] = 'sales/reports/daily_transaction';
    $this->load->view('inc/template_fee', $data);
  }

  public function get_daily_transaction(){
    $fromDate = $_POST['fromDate'];
    $toDate = $_POST['toDate'];
    $product_variants = $_POST['product_variants'];
    $payment_modes = $_POST['payment_modes'];
    $result = $this->sales_model->get_daily_transaction_report($fromDate, $toDate, $product_variants, $payment_modes);
    echo json_encode($result);
  }

  public function sales_history(){
    if (!empty($this->input->post())) {
      $data['sales_history'] = $this->sales_model->get_sales_history_student_wise($this->input->post('student'));
      $data['student_details'] = $this->sales_model->get_sales_history_student_details($this->input->post('student'));
    }
    $data['studentNames'] = $this->sales_model->getstudentallNames();
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['main_content'] = 'sales/reports/history';
    $this->load->view('inc/template', $data);
  }

  public function get_student_id_sales(){
    $admission_no = $_POST['admission_no'];
    $stdData = $this->sales_model->get_sales_student_historyby_id($admission_no, 0);
    echo json_encode($stdData);
  }

  public function get_student_id_sales_studentiId(){
    $student_id = $_POST['student_id'];
    $stdData = $this->sales_model->get_sales_student_historyby_id(0, $student_id);
    echo json_encode($stdData);
  }

   public function sales_receipt_pdf_download($id){ 
    $link = $this->sales_model->download_sales_receipt_path($id);
    $url = $this->filemanager->getFilePath($link);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download('sales.pdf', $data, TRUE);
  }

  public function re_generate_pdf(){
    $data['main_content'] = 'sales/reports/re_geneate';
    $this->load->view('inc/template', $data);  
  }

  public function sales_accounts(){
    $data['sales_varints'] = $this->sales_model->get_varints_name_for_accounts();
    $data['main_content'] = 'sales/reports/accounts';
    $this->load->view('inc/template', $data);  
  }

  public function get_transaction_sales(){
    $fromDate = $_POST['fromDate'];
    $toDate = $_POST['toDate'];
    $result = $this->sales_model->get_daily_transaction_report($fromDate, $toDate);
    echo json_encode($result);
  }

  public function generate_pdf_fee_receipt(){
    $checked_transids = $_POST['checked_transids'];
    foreach ($checked_transids as $key => $transId) {
      $receipt_data = $this->sales_model->get_sales_receipt_data_for_pdf($transId);
      $result = $this->_create_template_sales_pdf_format($receipt_data, $receipt_data->receipt_template);
      $update =  $this->sales_model->update_html_receipt_sales($result, $transId);
      if($update) {
        $this->__generateSales_pdf_receipt($result, $transId);
      }
    }
  }

  public function sales_receits_delete(){
    $salesId = $_POST['salesId'];
    $remarks = $_POST['remarks'];
    echo $this->sales_model->soft_delete_sales_receipt($salesId, $remarks);
  }

  public function update_sale_vendor_code(){
    $result = $this->sales_model->update_sale_vendor_code_by_varints();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    }
    redirect('sales/sales/sales_accounts');
  }
  public function delete_vendor_account($id){
    $result = $this->sales_model->delete_vendor_account_by_id($id);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully deleted.');
    }else{
       $this->session->set_flashdata('flashError', 'Something went wrong...');
    }
    redirect('sales/sales/sales_accounts');
  }

  public function receipt_canceled_report(){
    $data['main_content'] = 'sales/reports/receipt_cancellation';
    $this->load->view('inc/template', $data);
  }

  public function non_reconciled_report(){
    $data['main_content'] = 'sales/reports/non_reconciled_report';
    $this->load->view('inc/template', $data);
  }

  public function get_cancelations_transaction(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $result = $this->sales_model->get_cancelation_transaction_report($from_date, $to_date);
    echo json_encode($result);
  }

  public function new_student_sales_list(){
    $data['new_student_list'] = $this->sales_model->get_new_student_sales_list_report();
    // echo "<pre>"; print_r($data['new_student_list']);die();
    $data['main_content'] = 'sales/reports/new_student_sales';
    $this->load->view('inc/template_fee', $data);
  }

  public function get_reconciled_data(){
    $result = $this->sales_model->get_reconciled_data($_POST['from_date'],$_POST['to_date'],$_POST['reconciled_type']);
    $sales_payment_modes = $this->settings->getSetting('sales_payment_modes');
    foreach($result as $key => $val){
      if(!empty($sales_payment_modes)){
        foreach($sales_payment_modes as $k => $v){
          $pay_mode = explode('_',$v->value)[0];
          if($pay_mode == $val->payment_type){
            $val->payment_type = $v->name;
          }
        }
      }else{
        $payment_mode = $val->payment_type;
        switch ($payment_mode) {
            case "9":
                $val->payment_type = 'Cash';
                break;
            case "4":
                $val->payment_type = 'Cheque';
                break;
            case "1":
                $val->payment_type = 'DD';
                break;
            case "7":
                $val->payment_type = 'Card';
                break;
            case "8":
                $val->payment_type = 'Net Banking';
                break;
            case "10":
                $val->payment_type = 'Online Link';
                break;
            case "11":
                $val->payment_type = 'UPI';
                break;
            default:
                $val->payment_type = 'Cash';
            break;
        }
      }
      
    }
    echo json_encode($result);
  }

  public function submit_reconciled_status(){
    echo $this->sales_model->submit_reconciled_status($_POST);  
  }

  public function reconsilation_failed(){
    echo $this->sales_model->reconsilation_failed($_POST); 
  }
}