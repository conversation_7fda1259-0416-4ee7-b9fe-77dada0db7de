<?php

class Questions extends CI_Controller {

  function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
      }
       $this->load->model('academics/lessonPlan_model', 'lessonplan_model');
       $this->load->model('academics/questions_model');
  }

  public function index() {
    $data['grades'] = $this->lessonplan_model->getAllClasses();
    // echo "<pre>";print_r('skjdf');die();
    $data['main_content'] = 'academics/questions/question_bank';
    $this->load->view('inc/template', $data);
  }

  public function get_subjects(){
    $class_name = $_POST['class_name'];
    $data = $this->questions_model->get_subjects($class_name);
    echo json_encode($data);
  }

  public function add_lesson(){
    $data = $this->questions_model->add_lesson();
    echo json_encode($data);
  }

  public function get_lessons() {
    $subject_id = $_POST['subject_id'];
    $data = $this->questions_model->get_lessons($subject_id);
    echo json_encode($data);
  }

  public function get_sub_topics() {
    $lesson_id = $_POST['lesson_id'];
    $data = $this->questions_model->get_sub_topics($lesson_id);
    echo json_encode($data);
  }

  public function get_topic_questions() {
    $sub_topic_id = $_POST['sub_topic_id'];
    $data = $this->questions_model->get_topic_questions($sub_topic_id);
    echo json_encode($data);
  }

  public function save_new_questions() {
    // echo '<pre>'; print_r($_POST); die();
    $status = $this->questions_model->save_new_questions();
    echo $status;
  }

  public function add_sub_topic() {
    $lesson_id = $_POST['lesson_id'];
    $sub_topic_name = $_POST['sub_topic_name'];
    $sub_topic_id = $this->questions_model->add_sub_topic($lesson_id, $sub_topic_name);
    echo $sub_topic_id;
  }

}