<?php

class Admissionflowv2 extends CI_Controller{
    public function __construct(){
        parent::__construct();
        $this->load->model('parent_model');
        $this->load->model('admissionflowv2_model');
        $this->load->model('feesv2/fees_collection_model');
		$this->load->model('Consent_model');
        $this->load->library('payment');
        $this->load->library('payment_payu');
        $this->load->library('payment_jodo');
		$this->load->library('filemanager');
		$this->load->model('Student_health_model');
        $this->load->model('student/Student_Model', 'stdModel');
        $this->load->model('student/Health_model', 'healthModel');
        $this->config->load('form_elements');
        $this->yearId =  $this->acad_year->getAcadYearId();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
    }

    public function index(){
		$data['callFrom'] = 'shortAdmissionFlow';
        $studentId   = $this->parent_model->getStudentIdOfLoggedInParent();
        $studentData = $this->parent_model->getStudentDataById($studentId);
		
		$studentData = json_decode(json_encode($studentData), true); 

		$required_field = $this->_check_student_profile_mandatory_fields($studentData['stdYearId']);
		$docResult = $this->_check_documents_fields($studentId);
		$consentResult = $this->_check_consent_fields($studentId);
		$healthResult = $this->_check_health_fields($studentId);

		$studentData['user_profile'] = base_url()."/assets/img/blue48px/profile.png";
		if(!empty($studentData['picture_url'])){
			$studentData['user_profile'] = $this->filemanager->getFilePath($studentData['picture_url']); ;
		}

        $parentId = $this->authorization->getAvatarStakeHolderId();
        $data['currentAcadYearId'] = $this->acad_year->getAcadYearId();
    	$data['currentAcadYear'] = $this->acad_year->getAcadYear();

        $data['feeData'] = $this->admissionflowv2_model->check_fee_payment_status($studentId, $data['currentAcadYearId']);
        if($data['feeData']->otp_payment_page_enable == 1 && $data['feeData']->status =='Not Paid'){
            redirect('admissionflowv2/fee_details');
        }
        $data['studentData'] = $studentData;
        $data['required_field'] = $required_field;
        $data['doc_field'] = $docResult;
        $data['consent_field'] = $consentResult;
        $data['health_field'] = $healthResult;
		$data['overall_status'] = [
			'total' => $required_field['total'] + $docResult['total'] + $consentResult['total'] + $healthResult['total'],
			'completed' => $required_field['completed'] + $docResult['completed'] + $consentResult['completed'] + $healthResult['completed']
		];
		$data['overall_status']['percentage'] = ($data['overall_status']['total'] > 0) ? round(($data['overall_status']['completed'] / $data['overall_status']['total']) * 100) : 0;
		// echo "<pre>";print_r($data['required_field']);die();
        if($this->mobile_detect->isTablet()){
            // $this->load->view('dashboard/parent/admissionFlowV2/index_tablet', $data);
			$data['main_content'] = 'dashboard/parent/admissionFlowV2/index_tablet';
        }else if ($this->mobile_detect->isMobile()) {
            // $this->load->view('dashboard/parent/admissionFlowV2/index_mobile', $data);
			$data['main_content'] = 'dashboard/parent/admissionFlowV2/index_mobile';
        }else{
            // $this->load->view('dashboard/parent/admissionFlowV2/index_desktop', $data);
			$data['main_content'] = 'dashboard/parent/admissionFlowV2/index_desktop';
        }
        $this->load->view('inc/template', $data);
    }


	private function _check_student_profile_mandatory_fields($stdYearId){
		$mandatory_fields = $this->parent_model->get_requried_fields();
		if(!empty($mandatory_fields)){
			$result = $this->parent_model->check_mandatory_field_before($stdYearId, $mandatory_fields);
		}
		$count = 0;
		$missingFields = 0;
		$total_fields = 0;
		if(!empty($result)){
			$count = count($result);
			$total_fields = count($mandatory_fields);
			$missingFields = $total_fields - $count;
		}
		$completion_percentage = ($total_fields > 0) ? round(($count / $total_fields) * 100) : 0;
		return  [
			'total' => $total_fields,
			'completed' => $count,
			'percentage' => $completion_percentage,
			'missing_fields' => $missingFields
		];

	}

	private function _check_documents_fields($studentId){
		$documents_fields = $this->parent_model->get_document_types();
        $documents = $this->parent_model->get_studentDocuments($studentId);
		$docCount = 0;
		$missingDocFields = [];
		// $total_document_fields = count($documents_fields);
		$total_document_fields = array_reduce($documents_fields, function($count, $doc) {
			return $count + ($doc->is_mandatory == 1 ? 1 : 0);
		}, 0);
		 // Check documents
		if (!empty($documents_fields)) {
			foreach ($documents_fields as $doc_field) {
				$document_found = false;
				if (!empty($documents)) {
					foreach ($documents as $document) {
						if ($document->document_type == $doc_field->document_name && 
							!empty($document->document_url) && $doc_field->is_mandatory == 1) {
							$document_found = true;
							$docCount++;
							break;
						}
					}
				}
				if (!$document_found && $doc_field->is_mandatory == 1) {
					$missingDocFields[] = $doc_field->document_name;
				}
			}
		}

		return [
			'total' => $total_document_fields,
			'completed' => $docCount,
			'missing_fields' => $missingDocFields
		];
	}

	private function _check_consent_fields($studentId){
		$result = $this->Consent_model->get_consent_form_templates($studentId);
		$consentCount = 0;
		$missingConsentFields = 0;
		$total_consent_fields = count($result);
		foreach ($result as $key => $val) {
			if($val->status !='Not Submitted'){
				$consentCount++;
			}else{
				$missingConsentFields++;
			}
		}
		return [
			'total' => $total_consent_fields,
			'completed' =>  $consentCount,
			'missing_fields' => $missingConsentFields
		];
	}

	private function _check_health_fields($studentId){
		$health_data = $this->healthModel->get_health_details($studentId);
        $submittedHealthCount = $health_data;
        $pendingHealth = 0;
		$vac_master= $this->config->item('vaccine_master');
        $total_health_fields = count($vac_master);
		$pendingHealth = $total_health_fields - $submittedHealthCount;
        return [
            'total' => $total_health_fields,
            'completed' =>  $submittedHealthCount,
            'pending' => $pendingHealth
        ];
	}

    public function fee_details()
	{
		$this->load->helper('fees_helper');
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$fee_blueprints = $this->parent_model->get_published_blueprints($student_id);
		if(empty($fee_blueprints)){
			redirect('auth/logout');
		}
        $blueprint = [];
        foreach($fee_blueprints as $val){
            $blueprint = (array) $val;
        }
        // echo "<pre>";print_r($blueprint);die();
		$input = json_decode(json_encode($blueprint[0]), true);
        // echo "<pre>";print_r($input);die();

		if (!isset($input['fb_id'])) {
			redirect('auth/login');
		}
		$studentData = $this->parent_model->getStudentDataById($student_id);
		
		$studentData = json_decode(json_encode($studentData), true); 

		$data['blueprint_id'] = $input['fb_id'];
		$data['studentData'] = $studentData;
		$data['student_id'] = $student_id;
		$data['std_sch_id'] = $input['std_sch_id'];
		$data['blueprint_name'] = $input['blueprint_name'];
		$data['cohort_student_id'] = $input['cohort_student_id'];
		$data['concession_remarks'] = $this->parent_model->get_concession_remarksby_cohort_std_id($data['cohort_student_id']);
		$data['adjustment_remarks'] = $this->parent_model->get_adjustment_remarksby_cohort_std_id($data['cohort_student_id']);
		$fee_blueprints = $this->parent_model->get_published_blueprints_status($data['student_id'], $data['blueprint_id']);
		$classId = $this->parent_model->get_student_class_id_by_acadyearwise($data['student_id'],$fee_blueprints->acad_year_id);
		$data['online_status'] = $fee_blueprints;
		$data['fee_amount'] = $this->parent_model->get_std_fee_amount_details($input['std_sch_id']);
		$no_of_installments = 0;
		$checked_due_crossinsIds = [];
		$today  = date('Y-m-d');
		$count = 0;
		foreach ($data['fee_amount']['installment_name'] as $key => $value) {
			if ($value['status'] != 'FULL') {
				$no_of_installments++;
			}
			if($value['ins_due_date'] < $today && $value['ins_due_date'] !='0000-00-00' && $value['ins_due_date'] !='1970-01-01'){
				array_push($checked_due_crossinsIds, $key);
			}
			if($value['ins_due_date'] > $today){
				$count++;
			}
			if($count == 0){
                array_push($checked_due_crossinsIds, $key);
            }
		}
		$data['check_recon_status'] = $this->parent_model->check_recon_statusbystdId($data['student_id'], $data['std_sch_id']);
		$data['op_response_code'] = $this->parent_model->check_online_payment_progress_status($data['student_id']);
		$data['blueprint_alog'] = $this->fees_collection_model->get_fee_blueprint_alog($data['std_sch_id'], $classId);
		$data['no_of_installments'] = $no_of_installments;
		$data['checked_due_crossinsIds'] = $checked_due_crossinsIds;
		$feeView = $this->settings->isParentModuleEnabled('FEESV3');
		if ($feeView) {
            $this->load->view('parent/feesv2_installments/newAdmissionFlow/index', $data);
			// $data['main_content'] = 'parent/feesv2_installments/newAdmissionFlow/index';
		} else {
			if ($this->mobile_detect->isTablet()) {
				$data['main_content']    = 'parent/feesv2/tablet_fee_details';
			} else if ($this->mobile_detect->isMobile()) {
				$data['main_content']    = 'parent/feesv2/mobile_fee_details';
			} else {
				$data['main_content']    = 'parent/feesv2/fee_details';
			}
		}
		// $this->load->view('inc/template', $data);
	}

    public function fee_confirm()
	{
		$this->load->helper('fees_helper');
		$input = $this->input->post();
        // echo "<pre>";print_r($input);die();
		$data['fees_installment_id'] = $input['fees_installment_id'];
		$data['blueprint_id'] = $input['blueprint_id'];
		$data['std_sch_id'] = $input['std_sch_id'];
		$data['blueprint_name'] = $input['blueprint_name'];
		$data['student_id'] = $input['student_id'];
		$data['cohort_student_id'] = $input['cohort_student_id'];
		$data['online_status'] = $input['online_payment'];
		$data['discount_amount'] =  $input['discount_amount'];
		$data['partial_amount'] = (isset($input['parital_amount'])) ? $input['parital_amount'] : '';
		$data['enter_amount'] =  (isset($input['enter_amount'])) ? $input['enter_amount'] : '';

		$studentData = $this->parent_model->getStudentDataById($input['student_id']);
		
		$studentData = json_decode(json_encode($studentData), true); 
		$data['studentData'] = $studentData;
		//Commenting this code to disable disabling the button if transaction is in progress.
		// $data['trans_status'] =  $this->parent_model->transaction_status($input['std_sch_id']);
		$feeView = $this->settings->isParentModuleEnabled('FEESV3');
		if ($feeView) {
			$fee_amount = $this->parent_model->get_std_fee_installments_amount_confirm($input['std_sch_id'], $input['fees_installment_id']);
			if (!empty($data['enter_amount'])) {
				$enterAmount = $data['enter_amount'] + $data['discount_amount'];
				$data['fee_amount'] = $this->_split_enter_amount_componentwise($fee_amount, $enterAmount);
			} else {
				$data['fee_amount'] = $this->_split_without_enter_amount_componentwise($fee_amount);
			}
            $this->load->view('parent/feesv2_installments/newAdmissionFlow/fee_collect', $data);
			// $data['main_content'] = 'parent/feesv2_installments/fee_collect';
		} else {
			$data['fee_amount'] = $this->parent_model->get_std_fee_amount_confirm($input['std_sch_id'], $input['fees_installment_id']);
			if ($this->mobile_detect->isTablet()) {
				$data['main_content']    = 'parent/feesv2/tablet_fee_collect';
			} else if ($this->mobile_detect->isMobile()) {
				$data['main_content']    = 'parent/feesv2/mobile_fee_collect';
			} else {
				$data['main_content']    = 'parent/feesv2/fee_collect';
			}

			// if ($this->mobile_detect->isMobile()) {
			// 	$data['main_content']    = 'parent/feesv2/mobile_fee_collect';
			// } else {
			// 	$data['main_content']    = 'parent/feesv2/fee_collect';
			// }
		}
		$data['fee_blueprints'] = $this->parent_model->get_published_blueprints_status($data['student_id'], $data['blueprint_id']);
		// echo "<pre>"; print_r($data['fee_amount']); die();
		// $this->load->view('inc/template', $data);
	}

    private function _split_enter_amount_componentwise($fee_amount, $enterAmount)
	{
		foreach ($fee_amount as $insId => &$value) {
			foreach ($value as $key => $val) {
				$balance = $val->component_amount - $val->component_amount_paid -  $val->concession_amount - $val->concession_amount_paid -  $val->adjustment_amount - $val->adjustment_amount_paid;
				if ($enterAmount >= $balance) {
					$val->allocate_enter_amount = $balance;
					$enterAmount = $enterAmount - $balance;
				} else {
					$val->allocate_enter_amount =  $enterAmount;
					$enterAmount = $enterAmount - $balance;
					if ($enterAmount < 0) {
						//This condition is not possible. If so, it is a bug.
						$enterAmount = 0;
					}
				}
			}
		}
		return $fee_amount;
	}

	private function _split_without_enter_amount_componentwise($fee_amount)
	{
		foreach ($fee_amount as $insId => &$value) {
			foreach ($value as $key => $val) {
				$val->allocate_enter_amount = $val->component_amount - $val->component_amount_paid -  $val->concession_amount - $val->concession_amount_paid -  $val->adjustment_amount - $val->adjustment_amount_paid;
			}
		}
		return $fee_amount;
	}

    public function pay_fee()
	{
		$input = $this->input->post();
        // echo "<pre>";print_r($input);die();
		//This defensive code will kick-in when a second payment is triggered when a payment is in progress.
		//Right now, closing this as it freezes payment if 
		// $check_before_insert = $this->parent_model->check_trans_status($input['fee_student_schedule_id']);
		// if ($check_before_insert) {
		// 	redirect('parent_controller/display_fee_blueprints/');
		// }
		$blue_print = $this->fees_collection_model->ge_blueprint_by_id($input['cohort_student_id']);

		$this->db->trans_begin();
		$schoolName = $this->settings->getSetting('school_short_name'); // temp testing  after remove this code
		if($schoolName =='skalvi'){
			$payment_split = $this->fees_collection_model->get_split_strategy_amount($blue_print->id, $input['split_amount']);
		}else{
			$payment_split = $this->fees_collection_model->get_split_amount($blue_print->id, $input['split_amount']);
		}
		$fTrans = $this->fees_collection_model->insert_fee_transcation($input);

		if (!$fTrans) {
			$this->session->set_flashdata('flashError', 'Transcation failed to insert');
			$this->db->trans_rollback();
			redirect('parent_controller/display_fee_blueprints/');
		} else {
			$this->db->trans_commit();
			$totalFineAmount = 0;
			$totalDiscount = 0;
			if (isset($input['total_fine_amount'])) {
				$totalFineAmount = $input['total_fine_amount'];
			}
			if (isset($input['discount_amount'])) {
				$totalDiscount = $input['discount_amount'];
			}

			foreach ($payment_split->vendors as $key => &$value) {
				if ($key == 0) {
					$value->split_amount_fixed = $value->split_amount_fixed + $totalFineAmount - $totalDiscount;
				}
			}
			
			$transPayAmount = $input['total_amount'] + $totalFineAmount - $totalDiscount;

			$payment_gateway = $this->settings->getSetting('payment_gateway');
            // $payment_gateway = 'payu';
            // echo "<pre>";print_r($payment_gateway);die();
			switch ($payment_gateway) {
				case 'payu':
					$this->payment_payu->init_fee_payment_to_school($transPayAmount, $fTrans, 'AdmissionFlowV2');
					break;
				case 'jodo':
					$payment_split_jodo = $this->fees_collection_model->get_split_amount_jodo($blue_print->id, $input['split_amount']);
					$jodo_split = [];
					foreach ($payment_split_jodo as $key => $val) {
						if($val->amount != 0){
							array_push($jodo_split, $val);
						}
					}
					if($input['payment_type_choose'] =='Flex'){
						$studentData = $this->fees_collection_model->get_student_data_for_jodoby_student_id($input['student_id']);
						$this->payment_jodo->init_fee_payment_to_school_flex($transPayAmount, $fTrans, $jodo_split, $input['payment_type_choose'], $studentData, 'AdmissionFlowV2');
					}
					$this->payment_jodo->init_fee_payment_to_school($transPayAmount, $fTrans, $jodo_split, 'AdmissionFlowV2');
					break;
				default:
					$this->payment->init_payment_to_school($transPayAmount, 'PARENT_FEE', $fTrans, 'admissionflowv2/fee_trans_done', $blue_print->is_split, json_encode($payment_split), 'REDIRECT');
					break;
			}
		}
	}

    public function fee_trans_done()
	{
		/*** 
		 * Sample POST input
		 * Array
				(
						[transaction_status] => SUCCESS
						[source_id] => 12
						[display_message] => Transaction Successful!
						[transaction_id] => <id>
						[transaction_date] = <date>
						[response_type] = IMMEDIATE/RECON
				)
		 * 
		 * 
		 */
		// trigger_error("Response at fee_trans_done");
		// trigger_error(json_encode($_POST));

		if ($_POST['response_type'] === 'IMMEDIATE') {
			$this->__handle_immediate_op_response($_POST);
		} elseif ($_POST['response_type'] === 'DELAYED') {
			// Code Delete because handleing delayed response like an immediate response
			$this->__handle_immediate_op_response($_POST);
		} else {
			$this->__handle_recon_op_response($_POST);
		}
	}


	private function __handle_immediate_op_response($response)
	{
		if ($response['transaction_status'] === 'SUCCESS') {

			$is_receipt_generated = $this->fees_collection_model->is_receipt_generated($response['source_id']);

			// if receipt is already generated, we have already updated the transaction and schedule table. No need to update again. Just show the already generated receipt.
			if ($is_receipt_generated) {
				redirect('admissionflowv2/fee_reciept/' . $response['source_id'] . '/' . $response['transaction_id'] . '/' . $response['transaction_date'] . '/' . $response['transaction_time']);
				return;
			}

			// generate and update receipt number after transcation
			$result =  $this->fees_collection_model->update_trans_student_all_table($response['source_id']);

			// $this->fees_collection_model->update_receipt_transcation_wise($response['source_id']);

			// $this->fees_collection_model->update_student_schedule_all_table($response['source_id']);

			if (!$result) {
				$this->session->set_flashdata('flashError', 'Something went wrong. Will get back to you shortly');
			} else {
				$this->session->set_flashdata('flashSuccess', 'Transcation successful');
			}

			redirect('admissionflowv2/fee_reciept/' . $response['source_id'] . '/' . $response['transaction_id'] . '/' . $response['transaction_date'] . '/' . $response['transaction_time']);
		} else {
			//Online payment failed
			$this->fees_collection_model->update_transcation_status($response['source_id'], 'FAILED');

			// $this->session->set_flashdata('flashError', 'Online payment failed. If your Bank account is credited, check back after sometime');
			$transId = 0;
			if(!empty($response['transaction_id'])){
				$transId = $response['transaction_id'];
			}
			redirect('admissionflowv2/fee_transaction_failed/' . $response['source_id'] . '/' . $transId . '/' . $response['transaction_date'] . '/' . $response['transaction_time'] . '/' . $response['tx_response_code']);
		}
    }

	private function __handle_recon_op_response($response)
	{

		//First check the transaction status from db (Success/Failure)
		$trans_status = $this->parent_model->transaction_statusby_source_id($response['source_id']);

		// Update after response recon status into transcation table
		$this->parent_model->opm_recon_status_update($response['source_id'], $trans_status->status, $response['transaction_status']);

		//Get the transaction status from $response (Success/Failure)
		//Handle Success after Success
		//Handle Failure after Failure

		if ($trans_status->status == $response['transaction_status']) {
			// No Action
		} else {
			//Handle Success after Failure
			if ($trans_status->status === 'SUCCESS') {
				if ($response['transaction_status'] === 'FAILED') {
					$result = $this->fees_collection_model->soft_delete_feereceipt($response['source_id']);
					if ($result) {
						$this->fees_collection_model->update_transcation_status($response['source_id'], 'FAILED');
					}
					//Update the transaction as successful. Update student schedule tables.
				}
				//Handle Failure after Success
			} else {
				if ($trans_status->status === 'FAILED') {
					if ($response['transaction_status'] === 'SUCCESS') {
						$result = $this->fees_collection_model->update_student_schedule_all_table($response['source_id']);
						if ($result) {
						}
					}
				}
			}
		}
		//Update the transaction as failed.
	}

    public function fee_reciept($fTrans, $transaction_id, $transaction_date, $transaction_time, $traverse_to = '')
	{
		// trigger_error("Response at fee_recipt");
		// trigger_error("fTrans: $fTrans");
		// trigger_error("transaction_id $transaction_id");
		// trigger_error("transaction_date: $transaction_date");
		// trigger_error("transaction_time: $transaction_time");


		$this->fees_collection_model->create_pdf_template_for_fee_receipts($fTrans, $transaction_id, $transaction_date, $transaction_time);

		$data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);

		$fee_payment_sms = $this->settings->getSetting('fee_payment_sms');
		if (!empty($fee_payment_sms)) {
			if ($fee_payment_sms->sms_enabled == TRUE) {
				// $this->__sms_fees_payment($fee_payment_sms, $transaction_id, $data['fee_trans']->amount_paid);
			}
		}
		// $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
		// $template = $this->fees_collection_model->get_fee_template_by_blueprint_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);
		$blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);
		if($blue_print->enable_staff_notification == 1){
			$staffIds = json_decode($blue_print->notification_staff_ids);
			if(!empty($staffIds)){
				$this->_staff_notification_parent_fees($staffIds, $blue_print->name, $fee_trans->student->stdName, $fee_trans->student->clsName, $fee_trans->amount_paid);
			}	
		}

		if($blue_print->enable_parent_notification == 1){
			$stake_holder_id = [$this->parent_model->getStudentIdOfLoggedInParent()];
			if(!empty($stake_holder_id)){
				$this->_parent_notification_paid_fees($stake_holder_id, $blue_print->name, $fee_trans->student->stdName, $fee_trans->student->clsName, $fee_trans->amount_paid, $transaction_id);
			}	
		}

		$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		$email_content = $this->parent_model->get_parent_email($student_id,'fees_payment_success_confirmation_email');
		;
		$emailTransArry = [
			'paid_amount'=>$data['fee_trans']->amount_paid,
			'transaction_mode' => $data['fee_trans']->transaction_mode . '(' . $data['fee_trans']->online_tx_mode . ')',
			 'paid_datetime' => $data['fee_trans']->paid_datetime
		];

		if(!empty($email_content)){

			$sent_data = [];
			// Add father's data
			$father_data = new stdClass();
			$father_data->id = $email_content['to_email']->f_id;
			$father_data->email = $email_content['to_email']->father_email;
			$father_data->avatar_type = 2;
			$sent_data[] = $father_data;
			
			// Add mother's data
			$mother_data = new stdClass();
			$mother_data->id = $email_content['to_email']->m_id;
			$mother_data->email = $email_content['to_email']->mother_email;
			$mother_data->avatar_type = 2;
			$sent_data[] = $mother_data;

			$mail_sent = $this->_fee_email_to_parent($email_content,$emailTransArry);
			if($mail_sent){
				$sender_list = [];
				$sender_list['students'] = [
                    [
                        'send_to' => 'Both',
                        'send_to_type' => 'Father',
                        'ids' => $student_id,
                    ],
                    [
                        'send_to' => 'Both',
                        'send_to_type' => 'Mother',
                        'ids' => $student_id,
                    ]
                ];
				
				$email_master_data = array(
                    'subject' => $mail_sent['email_subject'],
                    'body' => $mail_sent['template_content'],
                    'source' => 'Fee Payment Done',
                    'sent_by' => $this->authorization->getAvatarId(),
                    'recievers' => 'Parents',
                    'from_email' => $mail_sent['registered_email'],
                    'files' => null,
                    'acad_year_id' => $this->acad_year->getAcadYearId(),
                    'visible' => 1,
                    'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
                    'sending_status' => 'Completed',
                    'texting_master_id'=>''
				);
				$this->load->model('communication/emails_model');
				$this->load->helper('email_helper');
				$email_master_id = $this->emails_model->saveEmail($email_master_data);
				$status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
				sendEmail($mail_sent['template_content'], $mail_sent['email_subject'], 0, $mail_sent['memberEmail'], $mail_sent['registered_email'], '');
			}
		}
		// $payment_modes = json_decode($blue_print->allowed_payment_modes);

		// $result = $this->_create_template_fee_amount($data['fee_trans'], $template, $transaction_id, $transaction_date, $transaction_time, $payment_modes);
		// $update =  $this->fees_collection_model->update_html_receipt($result, $fTrans);
		// if ($update) {
		// 	$this->testPost($result, $fTrans, $blue_print->receipt_for);
		// }
		redirect('admissionflowv2/fee_reciept_view/' . $fTrans . '/' . $transaction_id . '/' . $transaction_date . '/' . $transaction_time . '/' . $traverse_to);
	}

	public function fee_reciept_view($fTrans, $transaction_id, $transaction_date, $transaction_time, $traverse_to = '')
	{
		$data['traverse_to'] = $traverse_to;
		$data['transaction_id'] = $transaction_id;
		$data['transaction_date'] = $transaction_date;
		$data['transaction_time'] = $transaction_time;
		$data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
		if($data['fee_trans']->no_of_comp->is_transport_request == 1){
			redirect('parent_controller/transport_request');
		}else{
			// $data['main_content'] = 'parent/feesv2/receipt_parent';
            $this->load->view('parent/feesv2_installments/newAdmissionFlow/receipt_parent', $data);
		}
		// $this->load->view('inc/template', $data);
	}


	public function fee_transaction_failed($fTrans, $transaction_id, $transaction_date, $transaction_time, $response_code)
	{
		$data['transaction_id'] = $transaction_id;
		$data['transaction_date'] = $transaction_date;
		$data['transaction_time'] = $transaction_time;
		$data['tx_response_code'] = $response_code;
		// $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
		// $data['main_content'] = 'parent/feesv2/receipt_failed';
		// $this->load->view('inc/template', $data);
        $this->load->view('parent/feesv2_installments/newAdmissionFlow/receipt_failed', $data);
	}

    public function payu_school_cancel_callback(){

    }
}