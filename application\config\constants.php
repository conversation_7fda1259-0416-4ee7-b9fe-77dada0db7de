<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
|--------------------------------------------------------------------------
| Display Debug backtrace
|--------------------------------------------------------------------------
|
| If set to TRUE, a backtrace will be displayed along with php errors. If
| error_reporting is disabled, the backtrace will not display, regardless
| of this setting
|
*/
defined('SHOW_DEBUG_BACKTRACE') OR define('SHOW_DEBUG_BACKTRACE', TRUE);

/*
|--------------------------------------------------------------------------
| File and Directory Modes
|--------------------------------------------------------------------------
|
| These prefs are used when checking and setting modes when working
| with the file system.  The defaults are fine on servers with proper
| security, but you may wish (or even need) to change the values in
| certain environments (Apache running a separate process for each
| user, PHP under CGI with Apache suEXEC, etc.).  Octal values should
| always be used to set the mode correctly.
|
*/
defined('FILE_READ_MODE')  OR define('FILE_READ_MODE', 0644);
defined('FILE_WRITE_MODE') OR define('FILE_WRITE_MODE', 0666);
defined('DIR_READ_MODE')   OR define('DIR_READ_MODE', 0755);
defined('DIR_WRITE_MODE')  OR define('DIR_WRITE_MODE', 0755);

/*
|--------------------------------------------------------------------------
| File Stream Modes
|--------------------------------------------------------------------------
|
| These modes are used when working with fopen()/popen()
|
*/
defined('FOPEN_READ')                           OR define('FOPEN_READ', 'rb');
defined('FOPEN_READ_WRITE')                     OR define('FOPEN_READ_WRITE', 'r+b');
defined('FOPEN_WRITE_CREATE_DESTRUCTIVE')       OR define('FOPEN_WRITE_CREATE_DESTRUCTIVE', 'wb'); // truncates existing file data, use with care
defined('FOPEN_READ_WRITE_CREATE_DESTRUCTIVE')  OR define('FOPEN_READ_WRITE_CREATE_DESTRUCTIVE', 'w+b'); // truncates existing file data, use with care
defined('FOPEN_WRITE_CREATE')                   OR define('FOPEN_WRITE_CREATE', 'ab');
defined('FOPEN_READ_WRITE_CREATE')              OR define('FOPEN_READ_WRITE_CREATE', 'a+b');
defined('FOPEN_WRITE_CREATE_STRICT')            OR define('FOPEN_WRITE_CREATE_STRICT', 'xb');
defined('FOPEN_READ_WRITE_CREATE_STRICT')       OR define('FOPEN_READ_WRITE_CREATE_STRICT', 'x+b');

/*
|--------------------------------------------------------------------------
| Exit Status Codes
|--------------------------------------------------------------------------
|
| Used to indicate the conditions under which the script is exit()ing.
| While there is no universal standard for error codes, there are some
| broad conventions.  Three such conventions are mentioned below, for
| those who wish to make use of them.  The CodeIgniter defaults were
| chosen for the least overlap with these conventions, while still
| leaving room for others to be defined in future versions and user
| applications.
|
| The three main conventions used for determining exit status codes
| are as follows:
|
|    Standard C/C++ Library (stdlibc):
|       http://www.gnu.org/software/libc/manual/html_node/Exit-Status.html
|       (This link also contains other GNU-specific conventions)
|    BSD sysexits.h:
|       http://www.gsp.com/cgi-bin/man.cgi?section=3&topic=sysexits
|    Bash scripting:
|       http://tldp.org/LDP/abs/html/exitcodes.html
|
*/
defined('EXIT_SUCCESS')        OR define('EXIT_SUCCESS', 0); // no errors
defined('EXIT_ERROR')          OR define('EXIT_ERROR', 1); // generic error
defined('EXIT_CONFIG')         OR define('EXIT_CONFIG', 3); // configuration error
defined('EXIT_UNKNOWN_FILE')   OR define('EXIT_UNKNOWN_FILE', 4); // file not found
defined('EXIT_UNKNOWN_CLASS')  OR define('EXIT_UNKNOWN_CLASS', 5); // unknown class
defined('EXIT_UNKNOWN_METHOD') OR define('EXIT_UNKNOWN_METHOD', 6); // unknown class member
defined('EXIT_USER_INPUT')     OR define('EXIT_USER_INPUT', 7); // invalid user input
defined('EXIT_DATABASE')       OR define('EXIT_DATABASE', 8); // database error
defined('EXIT__AUTO_MIN')      OR define('EXIT__AUTO_MIN', 9); // lowest automatically-assigned error code
defined('EXIT__AUTO_MAX')      OR define('EXIT__AUTO_MAX', 125); // highest automatically-assigned error code

define('LEAVE_CATEGORIES', [
	['name' => 'Casual Leave', 'short_name' => 'CL'],
	['name' => 'Earned Leave', 'short_name' => 'EL'],
	['name' => 'Privilege Leave', 'short_name' => 'PL'],
	['name' => 'Sick Leave', 'short_name' => 'SL'],
	['name' => 'Loss Of Pay', 'short_name' => 'LOP'],
	['name' => 'Restricted Holiday', 'short_name' => 'RH'],
    ['name' => 'Compensatory Off', 'short_name' => 'CO'],
    ['name' => 'On Official Duty', 'short_name' => 'OOD'],
    ['name' => 'Commuted Leave', 'short_name' => 'COMML'],
    ['name' => 'Exam Leave', 'short_name' => 'EXL'],
    ['name' => 'Marriage Leave', 'short_name' => 'MRL'],
    ['name' => 'Annual Leave', 'short_name' => 'AL'],
	['name' => 'Maternity Leave', 'short_name' => 'ML'],
	['name' => 'Paternity Leave', 'short_name' => 'PaL'],
	['name' => 'Medical Leave', 'short_name' => 'Med']
]);

define('S3_VERSION', 'V4');

define('MIME_TYPE_EXTENSION', [
	'audio/aac' => 'aac',
	'audio/mpeg' => 'mp3',
	'audio/ogg' => 'ogg',
	'audio/opus' => 'opus',
	'audio/wav' => 'wav',
	'audio/webm' => 'webm',
	'video/x-msvideo' => 'avi',
	'image/bmp' => 'bmp',
	'text/css' => 'css',
	'text/csv' => 'csv',
	'application/msword' => 'doc',
	'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
	'image/gif' => 'gif',
	'text/html' => 'html',
	'image/jpeg' => 'jpeg',
	'text/javascript' => 'js',
	'application/json' => 'json',
	'image/png' => 'png',
	'application/pdf' => 'pdf',
	'application/vnd.ms-powerpoint' => 'ppt',
	'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'pptx',
	'image/svg+xml' => 'svg',
	'image/tiff' => 'tiff',
	'text/plain' => 'txt',
	'image/webp' => 'webp',
	'application/vnd.ms-excel' => 'xls',
	'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx',
	'application/xml' => 'xml',
	'text/xml' => 'xml'
]);

define('HELIUM_LOADING', '<div class="d-flex justify-content-center align-items-center" style="height: 200px;"><div class="spinner-grow text-primary" style="width: 2rem; height: 2rem;" role="status"><span class="visually-hidden">Loading...</span></div></div>');
define('HELIUM_NO_DATA', '<div class="card-body border-radius-lg bg-warning-100 text-center fw-bold">No Data</div>');
define('HELIUM_C_GST', 9);
define('HELIUM_S_GST', 9);
define('HELIUM_RECEIPT_TEMPLATE', '
<style type="text/css">
    table tr th {
        vertical-align: middle;
        border: solid 1px #474747;
        border-collapse: collapse;
        word-wrap: break-word;
        background:#474747;
        color:#fff;
        padding:2px;
        font-size: 12px;
    }

    table tr td {
        vertical-align: middle;
        border: solid 1px #474747;
        border-collapse: collapse;
        word-wrap: break-word;
        padding:4px;
        font-size: 14px;
        height:30px;
    }
    #particular td{
        text-align:center;
    }

    table{
        border: solid 1px #474747;
        border-collapse: collapse;
        width:100%;
        margin-bottom: 1%;
    }
    #header h2{
        margin:0;
    }
    #header p{
        margin:0;
    }
    #label h3 {
        font-size:14px;
    }
    #label h3 strong{
        border: 2px solid #000;
        padding:6px;
    }
    .no-border{
        border:none !important;
    }
    .no-border tr td{
        border:none !important;
    }
</style>

<div style="width: 80%;margin: auto;">
    <div style="text-align: center;">
        <table style="border: none;">
            <tr>
                <td style="width: 30%;border: none;">
                    <img src="https://demoschool.schoolelement.in//assets/img/nextelement_logo.png" alt="logo" width="100%" />
                </td>
                <td style="border: none;vertical-align: top;">
                    <center>
                        <h4 style="font-size: 24px;margin-bottom: 3px;">NextElement Private Limited
                            <br><span style="font-size: 16px;">
                            No.4, 1st cross, 1st main, 
                            <br>K.G Nagar, Bengaluru - 560018 
                            <br>GST: 29AAFCN9488B1Z0</span>
                        </h4>
                    </center>
                </td>
            </tr>
        </table>
    </div>
    <div style="margin-top: 20px;">
        <table class="no-border">
            <tr>
                <td style="text-align: center;"><h3><strong>RECEIPT</strong></h3></td>
            </tr>
            <tr>
                <td><strong>Receipt No.</strong>: %%receipt_number%%</td>
            </tr>
            <tr>
                <td><strong>Course / Batch</strong>: %%course_name%% / %%batch_name%%</td>
            </tr>
            <tr>
                <td><strong>Transaction Date</strong>: %%receipt_date%%</td>
            </tr>
            <tr>
                <td><strong>Name</strong>: %%student_name%%</td>
            </tr>
            %%school_data%%
        </table>    
    </div>

    <div>
        <table>
            %%amount_split%%
            <tr class="no-border">
                <td style="width: 50%;text-align: right;border: none;"><strong>Total</strong></td>
                <td>%%total_payable%%</td>
            </tr>
            <tr class="no-border">
                <td style="text-align: right;border: none;"><strong>Total in words</strong></td>
                <td style="text-transform: capitalize;">%%total_payable_in_words%%</td>
            </tr>
        </table>

        <div style="margin-top: 10px;">
            <p>This is a computer generated receipt and does not need any signature.</p>
        </div>
    </div>
</div>');

//custom global variable
global $staff_permissions;