<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>chhipulusu
 *          <EMAIL>
 *
 * Created:  15 March 2018
 *
 * Description: Controller for Sections - CRUD class teacher, CRUD staff-class/section/subject assignment.
 *
 * Requirements: PHP5 or above
 *
 */

class Multisection_allocator extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
      redirect('dashboard', 'refresh');
    }  
    $this->load->model('timetable/multisection_subject_model');
    $this->load->model('timetable/sss_allocation_model');
    $this->config->load('form_elements');
	}

  public function index() {
    $data['msSubjectList'] = $this->multisection_subject_model->getMSSubjectList();
    $data['main_content']    = 'timetable/multisection/index';
    $this->load->view('inc/template', $data); 
  }

  public function addNewMSGroup() {
    $data['main_content'] = 'timetable/multisection/add_ms_group';
    $this->load->view('inc/template', $data);
  }

  public function submit_ms_group() {
    $result = $this->multisection_subject_model->submitMSGroup();
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Group created successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong. Error code 103.');
    }
    redirect('timetable/multisection_allocator');    
  }

  public function addSubsAndWorkload($msSubId) {
    $data['sectionList'] = $this->multisection_subject_model->getSectionList();
    $data['staffList'] = $this->sss_allocation_model->getStaffDetails();
    $data['roomList'] = $this->sss_allocation_model->getRoomDetails();
    $data['msSubObj'] = $this->multisection_subject_model->getMSSubDetails($msSubId);
    //echo '<pre>';print_r($data['msSubObj']);die();
    $data['subjectColors'] = $this->config->item('subjectColors');
    $data['main_content'] = 'timetable/multisection/add_subs_and_workload';
    $this->load->view('inc/template', $data);     
  }

  public function submit_ms_workload() {
    $result = $this->multisection_subject_model->submitStaffWorkload();
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Subjects and Workload created successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong. Error code 104.');
    }
    redirect('timetable/multisection_allocator');    
  }

  public function allocateMSSubject($msSubId) {
    $csList = $this->multisection_subject_model->getCSList($msSubId);
    
    $isTTInitialized = (int)$this->multisection_subject_model->getIsTTInitialized($csList);
    if ($isTTInitialized == 0) {
      $this->session->set_flashdata('flashInfo', 'All sections timetables are not initialized. Initialize the timetables before you continue. Contact admin for help.');
      redirect('timetable/multisection_allocator');
    }

    $msSubObj = $this->multisection_subject_model->getMSSubjectDetails($msSubId);

    $data['csList'] = $csList;
    $data['csList_json'] = json_encode($csList);
    $data['msSubObj'] = $msSubObj;
    $data['periodData'] = $this->multisection_subject_model->getMSPeriodData($csList, $msSubObj->pSpan);
    // echo '<pre>';print_r($data['msSubObj']);die();
    // echo '<pre>';print_r($data['getMSPeriodData']);die();
    $data['main_content'] = 'timetable/multisection/add_allocation';
    $this->load->view('inc/template', $data);
  }

  public function submit_ms_allocation($pSpan) {
    if ($pSpan == 1)
      $result = $this->multisection_subject_model->submitS1MSAllocation();
    else
      $result = $this->multisection_subject_model->submitS2MSAllocation();
      
    //echo '<pre>';print_r($this->input->post());die();
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Subjects allocated created successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong. Error code 105.');
    }
    redirect('timetable/multisection_allocator');
  }
}
