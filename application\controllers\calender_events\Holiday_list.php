<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Holiday_list
 *
 * <AUTHOR>
 */
class Holiday_list extends CI_Controller{
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('SCHOOL_CALENDAR')) {
            redirect('dashboard', 'refresh');
        }    
        $this->load->model('Calender_events');
        $this->load->model('Holiday_info_model');
    }
   public function index($year = null, $month = null, $day = null) {
//          $year  = (empty($year) || !is_numeric($year))?  date('Y') :  $year;
//  $month = (is_numeric($month) &&  $month > 0 && $month < 13)? $month : date('m');
//  $day   = (is_numeric($day) &&  $day > 0 && $day < 31)?  $day : date('d');
         if ($this->uri->segment(4) == null) {
            $year = date('Y');
        } else {
            $year = $this->uri->segment(4);
        }

        $data['year'] = $year;
//print_r( $data['year']);
        if ($this->uri->segment(5) == null) {
            $month = date('m');
        } else {
            $month = $this->uri->segment(5);
        }
 $data['month'] = $month;//echo $this->uri->segment(5);
          //$data['getMonthevents'] = $this->Calender_events->getMonthEvent($data['year'],  $data['month'],$day);
      $data = array(
'years' => $this->uri->segment(4),
'months' => $this->uri->segment(5),

             'show_next_prev'  => TRUE, 
            'show_next_prev'  => TRUE,
            'next_prev_url'   =>  site_url('calender_events/Holiday_list/index'),
        'start_day'    => 'monday',
        'month_type'   => 'long',
        'day_type'     => 'short',

'template'    => '{table_open}<table class="date table">{/table_open}
           {heading_row_start}&nbsp;{/heading_row_start}
           {heading_previous_cell}<caption><a href="{previous_url}" class="prev_date" title="Previous Month">&lt;&lt;Prev</a>{/heading_previous_cell}
           {heading_title_cell}{heading}{/heading_title_cell}
           {heading_next_cell}<a href="{next_url}" id="trest" class="next_date"  title="Next Month">Next&gt;&gt;</a></caption>{/heading_next_cell}
           {heading_row_end}<col class="weekday" span="5"><col class="weekend_sat"><col class="weekend_sun">{/heading_row_end}
           {week_row_start}<thead><tr>{/week_row_start}
           {week_day_cell}<th>{week_day}</th>{/week_day_cell}
           {week_row_end}</tr></thead><tbody>{/week_row_end}
           {cal_row_start}<tr>{/cal_row_start}
           {cal_cell_start}<td>{/cal_cell_start}
           {cal_cell_content}<div class="date_event detail" val="{day}" ><span class="date">{day}</span><span class="event d{day}">{content}</span></div>{/cal_cell_content}
           {cal_cell_content_today}<div class="active_date_event detail" val="{day}"><span class="date">{day}</span><span class="event d{day}">{content}</span></div>{/cal_cell_content_today}
           {cal_cell_no_content}<div class="no_event detail" val="{day}"><span class="date">{day}</span><span class="event d{day}">&nbsp;</span></div>{/cal_cell_no_content}
           {cal_cell_no_content_today}<div class="active_no_event detail" val="{day}"><span class="date">{day}</span><span class="event d{day}">&nbsp;</span></div>{/cal_cell_no_content_today}
           {cal_cell_blank}&nbsp;{/cal_cell_blank}
           {cal_cell_end}</td>{/cal_cell_end}
           {cal_row_end}</tr>{/cal_row_end}
           {table_close}</tbody></table>{/table_close}');//$this->load->library('calendar');
       $data['getHolidayInfos']=$this->Holiday_info_model->getHolidaysInfo( );     
         
           foreach ($data['getHolidayInfos'] as $getHolidayInfos) {

            $rt = $getHolidayInfos['holiday_date'];
            $k = explode("-", $rt);
$test=preg_replace('/\[(.*)\]/', '', $k[2]);
 //echo "<pre>";print_r($test);die();
            $calenders[] = array(
                (int) ($test) => $getHolidayInfos['name']
            );
            $arraydata = array_filter(array_map('array_filter', $calenders));
            foreach ($arraydata as $key => $srach) {
                foreach ($srach as $key => $val) {
                    $data1[$key] = $val;
                }
            }
            $data['calenders'] = $data1;
            //
        }
      $this->load->library('calendar', $data);
      $data['main_content']    = 'calenderEvents/list_of_holidays';
      $this->load->view('inc/template', $data);      
   }
    public function enterHolidays(){
       
     
    //  $this->load->view('includes_admin/template', $data);      
          
       $day= $_POST['day'];
       $current_month= $_POST['current_month'];
       $current_year= $_POST['current_year'];
          $current_date = $current_year . '-' . $current_month . '-' . $day;
       $day_data= $_POST['day_data'];
       $day_data2= $_POST['day_data2'];
       $data = array( 
        'name'	=>$day_data, 
        'type'=>$day_data2, 
        'holiday_date'	=> $current_date
    );
$holiday_inserted=$this->db->insert('list_of_holidays', $data);
     if($holiday_inserted) {
         //echo "<pre>";print_r( $data['calenders']);die();
          
         echo  " inserted ";
         
     }
     else{
         
     echo  " Not inserted ";
         
    }}
     public function getHolidays(){
       }
       // $data['holidayDetail'] = $this->Calender_events->holidaysInfo(date('Y'),$month,$day);
      //$data['main_content']    = 'admin/activity/calenderEvents/list_of_holidays';
    
}
