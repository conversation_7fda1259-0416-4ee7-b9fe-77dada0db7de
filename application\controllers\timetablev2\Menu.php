<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>chhipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Menu extends CI_Controller {
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('TIMETABLE')) {
      redirect('dashboard', 'refresh');
    }
  }

  //Landing function to show the timetable menu
  public function index() {
    $site_url = site_url();

    $data['tiles'] = array(
      [
        'title' => 'Subjects',
        'sub_title' => 'Add the subjects list that make the timetable',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'timetablev2/subjects',
        'permission' =>  $this->authorization->isAuthorized('TIMETABLE.SUBJECTS')
      ],
      [
        'title' => 'Timetable Settings',
        'sub_title' => 'Settings',
        'icon' => 'svg_icons/constructtimetable.svg',
        'url' => $site_url.'timetablev2/timetable_settings',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.SETTINGS')
      ],
      [
        'title' => 'Manage Timetables',
        'sub_title' => 'Create timetable template; Mass-assign to section, staff and room',
        'icon' => 'svg_icons/managetimetable.svg',
        'url' => $site_url.'timetablev2/template',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.CREATE_TIMETABLE')
      ],
      [
        'title' => 'Clone Timetables',
        'sub_title' => 'Clone complete timetable to another timetable',
        'icon' => 'svg_icons/clonetimetables.svg',
        'url' => $site_url.'timetablev2/clone_template/fetch_all_template_details',
        'permission' => $this->authorization->isSuperAdmin()
      ],
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
      // [
      //   'title' => 'View Staff Timetables',
      //   'sub_title' => 'View daily/weekly timetable of each staff with substitutions',
      //   'icon' => 'svg_icons/clonetimetables.svg',
      //   'url' => $site_url.'timetablev2/timetable_reports/displayAllStaffIndex',
      //   'permission' => $this->authorization->isAuthorized('TIMETABLE.STAFF_TIMETABLE_REPORT')
      // ],
      [
        'title' => 'Print Staff Timetables',
        'sub_title' => 'View/print multiple staff timetables',
        'icon' => 'svg_icons/printstafftimetable.svg',
        'url' => $site_url.'timetablev2/timetable_reports/printStaffTimetables',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.STAFF_TIMETABLE_REPORT')
      ],
      [
        'title' => 'Staff workload report',
        'sub_title' => 'View Staff workload report',
        'icon' => 'svg_icons/staffworkloadreport.svg',
        'url' => $site_url.'timetablev2/timetable_reports/staff_workload_report',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.STAFF_WORKLOAD_REPORT')
      ],
      [
        'title' => 'View Section Day Timetables',
        'sub_title' => 'View daily/weekly timetable of each staff with substitutions',
        'icon' => 'svg_icons/viewsectiondaytimetable.svg',
        'url' => $site_url.'timetablev2/timetable_reports/display_day_section_timetables',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.SECTION_TIMETABLE_REPORT')
      ],
      [
        'title' => 'View Staff Day Timetables',
        'sub_title' => 'View daily/weekly timetable of each staff with substitutions',
        'icon' => 'svg_icons/view.svg',
        'url' => $site_url.'timetablev2/timetable_reports/display_day_staff_timetables',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.STAFF_TIMETABLE_REPORT')
      ],
      [
        'title' => 'Print Section/ Staff Timetables',
        'sub_title' => 'View/Print Section and Section\'s staff timetables',
        'icon' => 'svg_icons/printstafftimetable.svg',
        'url' => $site_url.'timetablev2/timetable_reports/printSectionTimetables',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.SECTION_TIMETABLE_REPORT')
      ],
      // [
      //   'title' => 'Room Timetables',
      //   'sub_title' => 'View/Print Room Timetables',
      //   'icon' => 'svg_icons/printstafftimetable.svg',
      //   'url' => $site_url.'timetable/room_timetable/printRoomTimetables',
      //   'permission' => $this->authorization->isAuthorized('TIMETABLE.ROOM_TIMETABLE_REPORT')
      // ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['administration_tools'] = array(
      [
        'title' => 'Refresh Staff names cache',
        'sub_title' => 'Refresh staff names cache',
        'icon' => 'svg_icons/constructtimetable.svg',
        'url' => $site_url.'timetablev2/administration_tools/staff_names',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Refresh Class names cache',
        'sub_title' => 'Refresh class names cache',
        'icon' => 'svg_icons/resetsession.svg',
        'url' => $site_url.'timetablev2/administration_tools/class_names',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Check Timetable integrity',
        'sub_title' => 'Check timetable integrity',
        'icon' => 'svg_icons/constructtimetable.svg',
        'url' => $site_url.'timetablev2/administration_tools/check_ttv2',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Delete Timetable',
        'sub_title' => 'Delete timetable',
        'icon' => 'svg_icons/assestdiscardreport.svg',
        'url' => $site_url.'timetablev2/administration_tools/delete_template_view',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Check Allocation Tool',
        'sub_title' => 'Check Allocation tool',
        'icon' => 'svg_icons/provisionstaff.svg',
        'url' => $site_url.'timetablev2/administration_tools/check_timetable_allocation_view',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.CREATE_TIMETABLE')
      ],
      [
        'title' => 'Building Master',
        'sub_title' => 'Manage Rooms',
        'icon' => 'svg_icons/buildingmaster.svg',
        'url' => $site_url.'management/management_controller/Buliding_master',
        'permission' => $this->authorization->isAuthorized('TIMETABLE.CREATE_TIMETABLE')
      ],
      [
        'title' => 'Move Staff',
        'sub_title' => 'Move Staff to another Staff',
        'icon' => 'svg_icons/buildingmaster.svg',
        'url' => $site_url.'timetablev2/administration_tools/move_staff_to_other_staff',
        'permission' => $this->authorization->isSuperAdmin()
      ]      
    );
    $data['administration_tools'] = checkTilePermissions($data['administration_tools']);
    
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'timetablev2/menu/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'timetablev2/menu/index_mobile';
    }else{
      $data['main_content']    = 'timetablev2/menu/index';	
    }

    
    $this->load->view('inc/template', $data);
  }

}