<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Admission_flow extends CI_Controller {

	public function __construct() {
	    parent::__construct();
	    if (!$this->ion_auth->logged_in()) {
	      redirect('auth/login', 'refresh');
	    } 
	    $this->load->model('Admission_model');
	    $this->load->library('session');
	    $this->load->helper('captcha');
	  	$this->config->load('form_elements');
	    $this->load->library('filemanager');
	    $this->load->model('student/Student_Model');
	    $this->load->model('feesv2/Fees_blueprint_model','fbm');
	    $this->load->library('fee_library');
	    $this->load->library('payment_application');
      $this->load->model('feesv2/fees_student_model');
      $this->load->model('feesv2/fees_collection_model');
      $this->load->model('feesv2/fees_cohorts_model');
      $this->load->model('parent_activation_model');
      $this->load->model('user_provisioning_model');
      $this->load->model('parent_activation_model');
      $this->load->library('payment');
	}

	public function index(){
		$data['form_data'] = $this->Admission_model->get_admission_applied_std_data();
    $data['currentAcadYearId'] = $this->acad_year->getAcadYearId();
    if ($data['currentAcadYearId'] == $this->settings->getSetting('academic_year_id')) {
      $data['acad_year_id'] = $this->settings->getSetting('academic_year_id');
    }else{
      $data['acad_year_id'] = $this->settings->getSetting('promotion_academic_year_id');
    }
    $data['checkPlaceholder'] = $this->Admission_model->check_placeholderbyAcadearYear($data['currentAcadYearId']);  
		$data['main_content'] = 'admission/flow/index';
  	$this->load->view('inc/template', $data);
	}

	public function get_admission_student_data(){
		// $admission_id = $_POST['admission_id'];
  //   $application_number = $_POST['application_number'];
  //   if (!empty($application_number)) {
  //     $admission_id = $this->Admission_model->get_application_number_wise_admId($application_number);
  //   }
    $result = $this->Admission_model->get_admission_data_by_id();
    echo json_encode($result);
	}
	 public function move_student_data_to_erp(){
    	$classId =  $_POST['classId'];
    	$sectionId =  $_POST['sectionId'];
      $combination_id =  '';
      $combination_name = '';
      if(isset($_POST['combination'])){
        $combination_id = $_POST['combination'];
      }

      if(!empty($combination_id)){
        $combination = $this->Admission_model->get_combination_name($combination_id);
        if(!empty($combination)){
          $combination_id =$combination_id;
          $combination_name =$combination->combination_name;
        }
      }


    	$admission_id =  $_POST['admission_id'];
    	$student_doj =  $_POST['student_doj'];
    	$acad_year =   $_POST['acad_year'];
      // $currentAcadYearId = $this->settings->getSetting('academic_year_id');
    	$result = $this->Admission_model->checkExit_in_student_admission($admission_id);
    	if ($result == 0) {
    		echo 0; // Exits in database
    		return;
    	}
    	$formData = $this->Admission_model->get_std_data_from_admission_formbyId($admission_id);
      $schoolDetails = $this->Admission_model->get_schoolDetailsbyfromid($admission_id);
      $schoolDocuments = $this->Admission_model->get_schoolDocumentsbyfromid($admission_id);
      $combination = $this->Admission_model->get_combinationsbyfromid($admission_id);
      $stdData = $this->_constrcut_student_array($formData, $classId, $sectionId,$combination_name, $acad_year, $student_doj,$combination_id);
      $fatherData = $this->_constrcut_father_array($formData);
      $motherData = $this->_constrcut_mother_array($formData);
      $guardianData = $this->_constrcut_guardian_array($formData);
      $lang_combinationData = $this->_constrcut_lang_com_array($formData, $combination);
 	 	  $config_admission_number = $this->settings->getSetting('admission_number');
      $admission_number_id = $this->settings->getSetting('student_admission_number_receipt_book_id');
      $admission_number_class_wise = $this->settings->getSetting('generate_class_wise_admission_number');
      $lastRecordId = $this->Student_Model->getLastStudentid();
      if($admission_number_class_wise == 1){
        $admission_number_format = $this->Admission_model->get_admission_number_format_by_classId($classId);
      }
      if(!empty($admission_number_id)){
        $stdData['admission_no'] = $this->Student_Model->update_student_admission_no_by_receipt_book($lastRecordId, $admission_number_id);
      } else if($admission_number_class_wise == 1 && (!empty($admission_number_format))){
        $stdData['admission_no'] = $this->Student_Model->update_student_admission_no_by_receipt_book($lastRecordId,$admission_number_format->admission_number_format_id);
      }else {
        if (!$lastRecordId) {
          $lastRecordId = 1;
        } else {
          $lastRecordId = ($lastRecordId->id + 1);
        }
        $params['number'] = sprintf("%0".$config_admission_number->digit_count . "s", $lastRecordId);
        $params['year_of_joining'] = $stdData['admission_acad_year'];
        $params['classid'] = $this->Student_Model->getClassByID($stdData['classid']);   
        $stdData['admission_no'] = $this->_generateAdmissionNo($config_admission_number,$params);
      }

      // $adm_no_exist = $this->db->select('admission_no')->from('student_admission')->get()->row();
      // if(!empty($adm_no_exist)){
      //   echo -1;die();
      //   return;
      // }
      // echo "<pre>"; print_r($stdData); die();
      $this->db->trans_begin();
      $student_uid = $this->Student_Model->addStudentInfo($stdData, $patth=array('file_name'=>$formData->std_photo_uri));
      if($student_uid == 0) {
      	echo 0; // Failed to Insert Student details
      	return;
      }else{
       	$father_uid = $this->Student_Model->addParentInfo($fatherData,$student_uid['stdAdmId'],'Father',$patth=array('file_name'=>$formData->father_photo), $stdData['student_firstname']);
      	if (!$father_uid) {
            echo 0; // Failed to Insert Father details
            return;
      	}
      	$mother_uid = $this->Student_Model->addParentInfo($motherData,$student_uid['stdAdmId'],'Mother',$patth=array('file_name'=>$formData->mother_photo), $stdData['student_firstname']);
 	 	    if (!$mother_uid) {
         	echo 0; // Failed to Insert Mother details
            return;
      	}
        if (!empty($guardianData['first_name'])) {
          $guardian_uid = $this->Student_Model->addParentInfo($guardianData,$student_uid['stdAdmId'],'Guardian',$patth=array('file_name'=>$formData->g_photo_uri), $stdData['student_firstname']);
        }
        $fatherAdd = [];
        if (!empty($formData->f_addr)) {
    	    $fatherAdd = array(
            'address_type'=>'1',
            'avatar_type'=>'2',
            'stakeholder_id'=>$father_uid,
            'Address_line1'=>(!isset($formData->f_addr) || $formData->f_addr == '')? '' : $formData->f_addr,
            'area'=> (!isset($formData->f_area) || $formData->f_area == '')? '' : $formData->f_area,
            'district'=>(!isset($formData->f_district) || $formData->f_district == '')? '' : $formData->f_district,
            'state'=> (!isset($formData->f_state) || $formData->f_state == '')? '' : $formData->f_state,
            'country'=> (!isset($formData->f_county) || $formData->f_county == '')? '' : $formData->f_county,
            'pin_code'=> (!isset($formData->f_pincode) || $formData->f_pincode == '')? '' : $formData->f_pincode,
            'unformatted_address'=>$formData->f_addr.', '.$formData->f_district.', '.$formData->f_state.', '.$formData->f_county.', '.$formData->f_pincode
      	   );
        }
        $motherAdd = [];
        if (!empty($formData->m_addr)) {
   	      $motherAdd = array(
            'address_type'=>'1',
            'avatar_type'=>'2',
            'stakeholder_id'=>$mother_uid,
            'Address_line1'=> (!isset($formData->m_addr) || $formData->m_addr == '')? '' : $formData->m_addr,
            'area'=> (!isset($formData->m_area) || $formData->m_area == '')? '' : $formData->m_area,
            'district'=> (!isset($formData->m_district) || $formData->m_district == '')? '' : $formData->m_district,
            'state'=> (!isset($formData->m_state) || $formData->m_state == '')? '' : $formData->m_state,
            'country'=> (!isset($formData->m_county) || $formData->m_county == '')? '' : $formData->m_county,
            'pin_code'=> (!isset($formData->m_pincode) || $formData->m_pincode == '')? '' : $formData->m_pincode,
            'unformatted_address'=>$formData->m_addr.', '.$formData->m_district.', '.$formData->m_state.', '.$formData->m_county.', '.$formData->m_pincode
       	  );
        }

        $guardianAdd = [];
        if (!empty($formData->g_addr)) {
          $guardianAdd = array(
            'address_type'=>'1',
            'avatar_type'=>'2',
            'stakeholder_id'=>$guardian_uid,
            'Address_line1'=> (!isset($formData->g_addr) || $formData->g_addr == '')? '' : $formData->g_addr,
            'area'=> (!isset($formData->g_area) || $formData->g_area == '')? '' : $formData->g_area,
            'district'=> (!isset($formData->g_district) || $formData->g_district == '')? '' : $formData->g_district,
            'state'=> (!isset($formData->g_state) || $formData->g_state == '')? '' : $formData->g_state,
            'country'=> (!isset($formData->g_county) || $formData->g_county == '')? '' : $formData->g_county,
            'pin_code'=> (!isset($formData->g_pincode) || $formData->g_pincode == '')? '' : $formData->g_pincode,
            'unformatted_address'=>$formData->g_addr.', '.$formData->g_district.', '.$formData->g_state.', '.$formData->g_county.', '.$formData->g_pincode
          );
        }

        $student_present_add = [];
        if (!empty($formData->s_present_addr)) {
          $student_present_add = array(
            'address_type'=>'0',
            'avatar_type'=>'1',
            'stakeholder_id'=>$student_uid['stdAdmId'],
            'Address_line1'=> (!isset($formData->s_present_addr) || $formData->s_present_addr == '')? '' : $formData->s_present_addr,
            'area'=> (!isset($formData->s_present_area) || $formData->s_present_area == '')? '' : $formData->s_present_area,
            'district'=> (!isset($formData->s_present_district) || $formData->s_present_district == '')? '' : $formData->s_present_district,
            'state'=> (!isset($formData->s_present_state) || $formData->s_present_state == '')? '' : $formData->s_present_state,
            'country'=> (!isset($formData->s_present_country) || $formData->s_present_country == '')? '' : $formData->s_present_country,
            'pin_code'=> (!isset($formData->s_present_pincode) || $formData->s_present_pincode == '')? '' : $formData->s_present_pincode,
            'unformatted_address'=>$formData->s_present_addr.', '.$formData->s_present_area.', '.$formData->s_present_district.', '.$formData->s_present_state.', '.$formData->s_present_country.', '.$formData->s_present_pincode
          );
        }
        $student_permanent_add = [];
        if (!empty($formData->s_permanent_addr) ) {
          $student_permanent_add = array(
            'address_type'=>'1',
            'avatar_type'=>'1',
            'stakeholder_id'=>$student_uid['stdAdmId'],
            'Address_line1'=> (!isset($formData->s_permanent_addr) || $formData->s_permanent_addr == '')? '' : $formData->s_permanent_addr,
            'area'=> (!isset($formData->s_permanent_area) || $formData->s_permanent_area == '')? '' : $formData->s_permanent_area,
            'district'=> (!isset($formData->s_permanent_district) || $formData->s_permanent_district == '')? '' : $formData->s_permanent_district,
            'state'=> (!isset($formData->s_permanent_state) || $formData->s_permanent_state == '')? '' : $formData->s_permanent_state,
            'country'=> (!isset($formData->s_permanent_country) || $formData->s_permanent_country == '')? '' : $formData->s_permanent_country,
            'pin_code'=> (!isset($formData->s_permanent_pincode) || $formData->s_permanent_pincode == '')? '' : $formData->s_permanent_pincode,
            'unformatted_address'=>$formData->s_permanent_addr.', '.$formData->s_permanent_area.', '.$formData->s_permanent_district.', '.$formData->s_permanent_state.', '.$formData->s_permanent_country.', '.$formData->s_permanent_pincode
          );
        }
        
        if (!empty($fatherAdd)) {
          $this->Admission_model->insert_father_address_details($fatherAdd);
        }

        if (!empty($motherAdd)) {
          $this->Admission_model->insert_mother_address_details($motherAdd);
        }
     	  
        if (!empty($guardianAdd)) {
          $this->Admission_model->insert_guaridan_address_details($guardianAdd);
        }

        if (!empty($student_present_add)) {
          $this->Admission_model->insert_student_present_address_details($student_present_add);
        }

        if (!empty($student_permanent_add)) {
          $this->Admission_model->insert_student_permanent_address_details($student_permanent_add);
        }

      	// $this->Admission_model->insert_student_health_details($student_uid['stdAdmId'], $formData->student_blood_group);

      	if (!empty($schoolDetails)) {
           $this->Admission_model->insert_schooling_details($schoolDetails, $student_uid['stdAdmId']);
      	}
        // if (!empty($lang_combinationData)) {
        //   $this->Admission_model->insert_lang_combination_details($lang_combinationData, $student_uid['stdAdmId']);
        // }
      	if (!empty($schoolDocuments)) {
        	$this->Admission_model->insert_documents_details($schoolDocuments, $student_uid['stdAdmId']);
      	}
      	if ($this->db->trans_status()) {

            $admission_number_id = $this->settings->getSetting('student_admission_number_receipt_book_id');
            if(!empty($admission_number_id)){
              $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$admission_number_id)->get()->row();
              $this->db->where('id',$admission_number_id);
              $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
            }

            if($admission_number_class_wise == 1){
              if(!empty($admission_number_format)){
                $this->db->where('id',$admission_number_format->id);
                $this->db->update('feev2_receipt_book', array('running_number'=>$admission_number_format->running_number+1));
              }
            }

            $this->db->trans_commit();
            $this->Admission_model->status_change_admission_idInsert('Student added to ERP',$admission_id, $student_uid['stdAdmId']);
            echo $student_uid['stdAdmId'];
            return;
      	} else {
            return 0; //failed
      	}
      }          
    }

 	private function _constrcut_student_array($formData, $classId, $sectionid,$combinations, $acad_year, $doj,$combination_id){
      $board = $this->settings->getSetting('board');
      $boar_opted = '';
      if(!empty($board) && !empty($formData->curriculum_interested_in)){
        foreach($board as $key => $val){
          if(strtolower($val) == strtolower($formData->curriculum_interested_in)){
            $boar_opted = $key;
          }
        }
      }
      $stdData = array();
      $stdData['admission_acad_year'] = $formData->academic_year_applied_for;
      $stdData['student_firstname'] = ucwords(strtolower($formData->std_name));
      $stdData['student_lastname'] = $formData->student_last_name;
      $stdData['student_middle_name'] = $formData->student_middle_name;
      $stdData['student_dob'] = ($formData->dob == '')?null:date('Y-m-d', strtotime($formData->dob));
      $stdData['gender'] = $formData->gender;
      $stdData['nationality'] = ($formData->nationality == '')?'Indian':$formData->nationality;
      $stdData['religion'] =  ($formData->religion == '')?null:$formData->religion;
      $stdData['category'] = ($formData->category == '')?null:$formData->category;
      $stdData['mother_tongue'] =  ($formData->std_mother_tongue == '')?null:$formData->std_mother_tongue;
      $stdData['rteid'] = 2; // if applicatiom form enabled
      $stdData['student_doj'] = $doj;
      $stdData['birth_taluk'] = ($formData->birth_taluk == '')?null:$formData->birth_taluk;
      $stdData['birth_district'] = ($formData->birth_district == '')?null:$formData->birth_district;
      $stdData['caste'] = ($formData->student_caste == '')?null:$formData->student_caste;
      $stdData['std_aadhar'] = ($formData->student_aadhar == '')?null:$formData->student_aadhar;
      $stdData['class_admitted_to'] = null;
      $stdData['admission_year'] = null;
      $stdData['admission_acad_year_id'] = $formData->academic_year_applied_for;
      $stdData['roll_no'] = null;
      $stdData['classid'] = $classId;
      $stdData['classsection'] = $sectionid;
      $stdData['boardingid'] = $formData->boarding; // get config
      $stdData['file_name'] = $formData->std_photo_uri;
      $stdData['acad_year'] = $acad_year;
      // $stdData['acad_year'] = '19';
      $stdData['board'] = $boar_opted; // get config
      $stdData['medid'] = '1'; // get config
      $stdData['acad_year_id'] = $acad_year;
      $stdData['roll_num'] = 0;
      $stdData['contact_no'] = null;
      $stdData['add_status'] = 2;
      $stdData['s_email'] = ($formData->student_email_id == '')? '' :$formData->student_email_id;
      $stdData['ration_card_number'] = ($formData->ration_card_number == '')?null:$formData->ration_card_number;
      $stdData['ration_card_type'] = ($formData->ration_card_type == '')?null:$formData->ration_card_type;
      $stdData['caste_income_certificate_number'] = ($formData->caste_income_certificate_number == '')?null:$formData->caste_income_certificate_number;
      $stdData['extracurricular_activities'] = ($formData->extracurricular_activities == '')?null:$formData->extracurricular_activities;
      $stdData['quota'] = ($formData->student_quota == '')?null:$formData->student_quota;
      $stdData['student_sub_caste'] = ($formData->student_sub_caste == '')?null:$formData->student_sub_caste;
      $stdData['student_mobile_no'] = ($formData->student_mobile_no == '')?null:$formData->student_mobile_no;
      $stdData['blood_group'] = ($formData->student_blood_group == '')?null:$formData->student_blood_group;
      $stdData['donor_name'] = '';
      $stdData['combination'] = $combinations;
      $stdData['s_country_code'] = ($formData->s_country_code == '')?'': $formData->s_country_code;
      $stdData['point_of_contact'] = ($formData->emergency_contact == '')?'': $formData->emergency_contact;
      $stdData['high_quality_url_family'] = ($formData->family_photo == '')?'': $formData->family_photo;
      $stdData['high_quality_url'] = ($formData->std_photo_uri_resize == '')?'': $formData->std_photo_uri_resize;
      $stdData['student_signature'] = ($formData->student_signature == '')?'': $formData->student_signature;
      $stdData['combination_id'] = $combination_id;
      return $stdData;
    }

    private function _constrcut_father_array($formData){
      $fatherData = array();
      $fatherData['first_name'] = $formData->f_name;
      $fatherData['last_name'] = $formData->f_last_name;
      $fatherData['qualification'] = $formData->f_qualification;
      $fatherData['occupation'] = $formData->f_position;
      $fatherData['mobile_no'] = $formData->f_mobile_no;
      $fatherData['aadhar'] = $formData->father_aadhar;
      $fatherData['annual_income'] = $formData->f_annual_gross_income;
      $fatherData['company'] = $formData->f_company_name;
      $fatherData['mother_tongue'] = $formData->father_mother_tongue;
      $fatherData['email'] = $formData->f_email_id;
      $fatherData['designation'] = $formData->f_profession;
      $fatherData['pan_number'] = $formData->f_pan_number;
      $fatherData['signature'] = $formData->f_signature;
      $fatherData['userid'] = '';
      $fatherData['country_code'] = $formData->f_country_code;
      $fatherData['nationality'] = $formData->f_nationality;
      $fatherData['intersted_in'] = ($formData->father_interest == '')?'': $formData->father_interest;
      $fatherData['dob'] = $formData->f_dob;
      $fatherData['caste'] = $formData->father_caste;
      $fatherData['religion'] = $formData->father_religion;
      $fatherData['organization_type'] = $formData->f_type_of_organization;
      return $fatherData;
    }

    private function _constrcut_mother_array($formData){
      $motherData = array();
      $motherData['first_name'] = $formData->m_name;
      $motherData['last_name'] = $formData->m_last_name;
      $motherData['qualification'] = $formData->m_qualification;
      $motherData['occupation'] = $formData->m_position;
      $motherData['mobile_no'] = $formData->m_mobile_no;
      $motherData['aadhar'] = $formData->mother_aadhar;
      $motherData['annual_income'] = $formData->m_annual_gross_income;
      $motherData['company'] = $formData->m_company_name;
      $motherData['mother_tongue'] = $formData->mother_mother_tongue;
      $motherData['email'] = $formData->m_email_id;
      $motherData['designation'] = $formData->m_profession;
      $motherData['pan_number'] = $formData->m_pan_number;
      $motherData['signature'] = $formData->m_signature;
      $motherData['userid'] = '';
      $motherData['country_code'] = $formData->m_country_code;
      $motherData['nationality'] = $formData->m_nationality;
      $motherData['intersted_in'] = ($formData->father_interest == '')?'': $formData->father_interest;
      $motherData['dob'] = $formData->m_dob;
      $motherData['caste'] = $formData->mother_caste;
      $motherData['religion'] = $formData->mother_religion;
      $motherData['organization_type'] = $formData->m_type_of_organization;
      return $motherData;
    }

    public function _constrcut_guardian_array($formData){
      $guardianData = array();
      $guardianData['first_name'] = $formData->g_name;
      $guardianData['last_name'] ='';
      $guardianData['qualification'] = $formData->g_qualification;
      $guardianData['occupation'] = $formData->g_position;
      $guardianData['mobile_no'] = $formData->g_mobile_no;
      $guardianData['aadhar'] = $formData->guardian_aadhar;
      $guardianData['annual_income'] = $formData->g_annual_gross_income;
      $guardianData['company'] = $formData->g_company_name;
      $guardianData['mother_tongue'] = $formData->guardian_mother_tongue;
      $guardianData['email'] = $formData->g_email_id;
      $guardianData['designation'] = $formData->g_profession;
      $guardianData['signature'] = '';
      $guardianData['userid'] = '';
      $guardianData['country_code'] = $formData->g_country_code;
      return $guardianData;
    }

    public function _constrcut_lang_com_array($formData, $combination){
      $lang_comData = array();
      $lang_comData['lang_1_choice'] = (!isset($formData->lang_1_choice) || $formData->lang_1_choice == '')? null : $formData->lang_1_choice;
      $lang_comData['lang_2_choice'] = (!isset($formData->lang_2_choice) || $formData->lang_2_choice == '')? null : $formData->lang_2_choice;
      $lang_comData['lang_3_choice'] = (!isset($formData->lang_3_choice) || $formData->lang_3_choice == '')? null : $formData->lang_3_choice;
      $lang_comData['combination'] =  (!isset($combination->combination) || $combination->combination == '')? null : $combination->combination;
     
      return $lang_comData;
    }

  private function _generateAdmissionNo_class_wise($admission_num_format,$number){
    // echo '<pre>';print_r($admission_num_format);
      $admission_number = '';
      if (!empty($admission_num_format->template_format)) {
        switch ($admission_num_format->template_format) {
          case '1':
            $admission_number = $admission_num_format->infix.sprintf("%'.0".$admission_num_format->digit_count."d",$number).'/'.$admission_num_format->year;
            break;
          case '2':
          $admission_number = $admission_num_format->infix.sprintf("%'.0".$admission_num_format->digit_count."d",$number);
            break;
          case '3':
          $admission_number = $admission_num_format->infix.'/'.$admission_num_format->year.'/'.sprintf("%'.0".$admission_num_format->digit_count."d",$number);
            break;
          case '4':
          $admission_number = $admission_num_format->infix.'/'.$admission_num_format->year.'/'.sprintf("%'.0".$admission_num_format->digit_count."d",$number);
            break;
          default:
           $admission_number = $admission_num_format->infix.sprintf("%'.0".$admission_num_format->digit_count."d",$number);
            break;
        }
    }
    return $admission_number;
  }  

 	private function _generateAdmissionNo($config_admission, $params = []) {
      
      $admission_number = '';

      switch ($config_admission->admission_generation_algo) {
        case 'NPSAGA':
           $admission_number = $params['year_of_joining'].$config_admission->infix.$params['number'];
          break;
        case 'NH': {
            // Greater than 2 takes all the class from 1st to so on.
            if ($params['classid']->type >= 2) 
              $admission_number = $config_admission->infix.$params['number'];
            else 
              $admission_number = 'P'.$params['number'];            
          break;
        }
        case 'NEXTELEMENT': {
          $admission_number = $params['year_of_joining'].$config_admission->infix.$params['number'];
          break;
        }
        case 'YASHASVI': {
          $admission_number = $config_admission->infix.$params['number'];
          break;
        }
        case 'WPL':
        //Infix 'N' if nursery, 'P' if primary, 'H' if high school
          switch ($params['classid']->type) {
            case 1:
              $classType = 'N';
              break;
            case 2:
              $classType = 'P';
              break;
            case 3:
              $classType = 'H';
              break;
          }
          $admission_number =$config_admission->infix.$classType.$params['number'];
          break;
      }

      return $admission_number;

    }

    public function move_student_data_to_temp(){
      $classId =  $_POST['grades'];
      $sectionId =  $_POST['class_section'];
      $admission_id =  $_POST['release_admission_form_id'];
      $joining_date =  $_POST['joining_date'];
      $rte_id = null;
      if(isset($_POST['rte_id'])){
        $rte_id = $_POST['rte_id'];
      } 
      $staff_kid = '';
      $transfer = '';
      $transfer_school = '';
      $staffkid_or_transfer = '';
      if(isset($_POST['staffkid_or_transfer'])){
        $staffkid_or_transfer = $_POST['staffkid_or_transfer'];
      }
      if($staffkid_or_transfer == 'staff_kid'){
        $staff_kid = 1;
      }elseif($staffkid_or_transfer == 'transfer'){
        $transfer = 1;
        $transfer_school = $_POST['transfer_from_school'];
      }
      $combination_id =  '';
      $combination_name = '';
      if(isset($_POST['combination'])){
        $combination_id = $_POST['combination'];
      }

      // if(empty($combination_id)){
      //   $combination_name = $this->Admission_model->get_combination($admission_id);
      // }

      if(!empty($combination_id)){
        $combination = $this->Admission_model->get_combination_name($combination_id);
        if(!empty($combination)){
          $combination_id =$combination->id;
          $combination_name =$combination->combination_name;
        }
      }
      // $student_doj =  $_POST['student_doj'];
      // $acad_year =   $_POST['acad_year'];
      // $currentAcadYearId = $this->settings->getSetting('academic_year_id');
      $result = $this->Admission_model->checkExit_in_Admission_studentTable($admission_id);
      $history = $this->Admission_model->release_offers_history($admission_id);
      if ($result == 0) {
        echo 0; // Exits in database
        return;
      }
      $formData = $this->Admission_model->get_std_data_from_admission_formbyId($admission_id);
      if(isset($_POST['boarding'])){
        $formData->boarding = $_POST['boarding'];
      }
      if(isset($_POST['transportaion'])){
        $formData->transport = $_POST['transportaion'];
      }
      $auto_assign = $this->Admission_model->get_config_val($formData->admission_setting_id) ;
      // echo "<pre>"; print_r($formData); die();
      $schoolDetails = $this->Admission_model->get_schoolDetailsbyfromid($admission_id);
      $schoolDocuments = $this->Admission_model->get_schoolDocumentsbyfromid($admission_id);
      $combination = $this->Admission_model->get_combinationsbyfromid($admission_id);
      $stdData = $this->_constrcut_student_array_temp($formData, $classId, $sectionId,$combination_name, $formData->academic_year_applied_for,$joining_date,$rte_id,$staff_kid,$transfer,$transfer_school,$combination_id);
      $fatherData = $this->_constrcut_father_array($formData);
      $motherData = $this->_constrcut_mother_array($formData);
      $guardianData = $this->_constrcut_guardian_array($formData);
      $lang_combinationData = $this->_constrcut_lang_com_array($formData, $combination);
      $lastRecord = $this->Student_Model->getLastStudentid(); 
      $config_admission_number = $this->settings->getSetting('admission_number');
      $admission_number_id = $this->settings->getSetting('student_admission_number_receipt_book_id');
      $admission_number_class_wise = $this->settings->getSetting('generate_class_wise_admission_number');
      $admission_number_format = $this->Admission_model->get_admission_number_format_by_classId($classId);
      $automation_enrollment_number_id = $this->settings->getSetting('enrollment_number_receipt_book_id');
      $generate_admn_num_after_move_toerp = $this->settings->getSetting('admission_number_generation_while_student_move_to_erp');
      if($automation_enrollment_number_id){
        $stdData['enrollment_number'] = $this->Student_Model->update_student_enrollment_no_by_receipt_book($automation_enrollment_number_id);
      }
      if($generate_admn_num_after_move_toerp == 1){
        if (!$lastRecord) {
          $lastRecordId = 1;
        } else {
          $lastRecordId = ($lastRecord->id + 1);
        }
        $params['number'] = sprintf("%0".$config_admission_number->digit_count . "s", $lastRecordId);
        $params['year_of_joining'] = $stdData['admission_acad_year'];
        $params['classid'] = $this->Student_Model->getClassByID($stdData['classid']);   
        $stdData['admission_no'] = $this->_generateAdmissionNo($config_admission_number,$params);
      }else if(!empty($admission_number_id)){
        $stdData['admission_no'] = $this->Student_Model->update_student_admission_no_by_receipt_book($lastRecord, $admission_number_id);
      }else if($admission_number_class_wise == 1 && (!empty($admission_number_format))){
        $stdData['admission_no'] =$this->Student_Model->update_student_admission_no_by_receipt_book($lastRecord,$admission_number_format->admission_number_format_id);
      }else{
        if (!$lastRecord) {
          $lastRecordId = 1;
        } else {
          $lastRecordId = ($lastRecord->id + 1);
        }
        $params['number'] = sprintf("%0".$config_admission_number->digit_count . "s", $lastRecordId);
        $params['year_of_joining'] = $stdData['admission_acad_year'];
        $params['classid'] = $this->Student_Model->getClassByID($stdData['classid']);   
        $stdData['admission_no'] = $this->_generateAdmissionNo($config_admission_number,$params);
      }
      $this->db->trans_begin();
      $student_uid = $this->Student_Model->addStudentInfo($stdData, $patth=array('file_name'=>$formData->std_photo_uri));
      if($student_uid == 0) {
        echo 0; // Failed to Insert Student details
        return;
      }else{

        $sName = $this->settings->getSetting('school_short_name');
        $len = strlen((string)$student_uid['stdAdmId']);
        $digits = 'SD';
        for ($i = 6 - $len;$i > 0; $i--) { 
          $digits .= '0';
        }
        $digits .= $student_uid['stdAdmId'];
        $qrCode = strtoupper($sName).$digits;

        $this->Student_Model->update_qr_code($student_uid['stdAdmId'],$qrCode);

        $father_uid = $this->Student_Model->addParentInfo($fatherData,$student_uid['stdAdmId'],'Father',$patth=array('file_name'=>$formData->father_photo), $stdData['student_firstname']);
        if (!$father_uid) {
            echo 0; // Failed to Insert Father details
            return;
        }
        $mother_uid = $this->Student_Model->addParentInfo($motherData,$student_uid['stdAdmId'],'Mother',$patth=array('file_name'=>$formData->mother_photo), $stdData['student_firstname']);
        if (!$mother_uid) {
          echo 0; // Failed to Insert Mother details
            return;
        }
        if (!empty($guardianData['first_name'])) {
          $guardian_uid = $this->Student_Model->addParentInfo($guardianData,$student_uid['stdAdmId'],'Guardian',$patth=array('file_name'=>$formData->g_photo_uri), $stdData['student_firstname']);
        }

       
        $fatherAdd = [];
        if (!empty($formData->f_addr)) {
          $fatherAdd = array(
            'address_type'=>'1',
            'avatar_type'=>'2',
            'stakeholder_id'=>$father_uid,
            'Address_line1'=>(!isset($formData->f_addr) || $formData->f_addr == '')? '' : $formData->f_addr,
            'area'=> (!isset($formData->f_area) || $formData->f_area == '')? '' : $formData->f_area,
            'district'=>(!isset($formData->f_district) || $formData->f_district == '')? '' : $formData->f_district,
            'state'=> (!isset($formData->f_state) || $formData->f_state == '')? '' : $formData->f_state,
            'country'=> (!isset($formData->f_county) || $formData->f_county == '')? '' : $formData->f_county,
            'pin_code'=> (!isset($formData->f_pincode) || $formData->f_pincode == '')? '' : $formData->f_pincode,
            'unformatted_address'=>$formData->f_addr.', '.$formData->f_district.', '.$formData->f_state.', '.$formData->f_county.', '.$formData->f_pincode
           );
        }
        $motherAdd = [];
        if (!empty($formData->m_addr)) {
          $motherAdd = array(
            'address_type'=>'1',
            'avatar_type'=>'2',
            'stakeholder_id'=>$mother_uid,
            'Address_line1'=> (!isset($formData->m_addr) || $formData->m_addr == '')? '' : $formData->m_addr,
            'area'=> (!isset($formData->m_area) || $formData->m_area == '')? '' : $formData->m_area,
            'district'=> (!isset($formData->m_district) || $formData->m_district == '')? '' : $formData->m_district,
            'state'=> (!isset($formData->m_state) || $formData->m_state == '')? '' : $formData->m_state,
            'country'=> (!isset($formData->m_county) || $formData->m_county == '')? '' : $formData->m_county,
            'pin_code'=> (!isset($formData->m_pincode) || $formData->m_pincode == '')? '' : $formData->m_pincode,
            'unformatted_address'=>$formData->m_addr.', '.$formData->m_district.', '.$formData->m_state.', '.$formData->m_county.', '.$formData->m_pincode
          );
        }

        $guardianAdd = [];
        if (!empty($formData->g_addr) && !empty($guardianData['first_name'])) {
          $guardianAdd = array(
            'address_type'=>'1',
            'avatar_type'=>'2',
            'stakeholder_id'=>$guardian_uid,
            'Address_line1'=> (!isset($formData->g_addr) || $formData->g_addr == '')? '' : $formData->g_addr,
            'area'=> (!isset($formData->g_area) || $formData->g_area == '')? '' : $formData->g_area,
            'district'=> (!isset($formData->g_district) || $formData->g_district == '')? '' : $formData->g_district,
            'state'=> (!isset($formData->g_state) || $formData->g_state == '')? '' : $formData->g_state,
            'country'=> (!isset($formData->g_county) || $formData->g_county == '')? '' : $formData->g_county,
            'pin_code'=> (!isset($formData->g_pincode) || $formData->g_pincode == '')? '' : $formData->g_pincode,
            'unformatted_address'=>$formData->g_addr.', '.$formData->g_district.', '.$formData->g_state.', '.$formData->g_county.', '.$formData->g_pincode
          );
        }

        $student_present_add = [];
        if (!empty($formData->s_present_addr)) {
          $student_present_add = array(
            'address_type'=>'0',
            'avatar_type'=>'1',
            'stakeholder_id'=>$student_uid['stdAdmId'],
            'Address_line1'=> (!isset($formData->s_present_addr) || $formData->s_present_addr == '')? '' : $formData->s_present_addr,
            'area'=> (!isset($formData->s_present_area) || $formData->s_present_area == '')? '' : $formData->s_present_area,
            'district'=> (!isset($formData->s_present_district) || $formData->s_present_district == '')? '' : $formData->s_present_district,
            'state'=> (!isset($formData->s_present_state) || $formData->s_present_state == '')? '' : $formData->s_present_state,
            'country'=> (!isset($formData->s_present_country) || $formData->s_present_country == '')? '' : $formData->s_present_country,
            'pin_code'=> (!isset($formData->s_present_pincode) || $formData->s_present_pincode == '')? '' : $formData->s_present_pincode,
            'unformatted_address'=>$formData->s_present_addr.', '.$formData->s_present_area.', '.$formData->s_present_district.', '.$formData->s_present_state.', '.$formData->s_present_country.', '.$formData->s_present_pincode
          );
        }
        $student_permanent_add = [];
        if (!empty($formData->s_permanent_addr) ) {
          $student_permanent_add = array(
            'address_type'=>'1',
            'avatar_type'=>'1',
            'stakeholder_id'=>$student_uid['stdAdmId'],
            'Address_line1'=> (!isset($formData->s_permanent_addr) || $formData->s_permanent_addr == '')? '' : $formData->s_permanent_addr,
            'area'=> (!isset($formData->s_permanent_area) || $formData->s_permanent_area == '')? '' : $formData->s_permanent_area,
            'district'=> (!isset($formData->s_permanent_district) || $formData->s_permanent_district == '')? '' : $formData->s_permanent_district,
            'state'=> (!isset($formData->s_permanent_state) || $formData->s_permanent_state == '')? '' : $formData->s_permanent_state,
            'country'=> (!isset($formData->s_permanent_country) || $formData->s_permanent_country == '')? '' : $formData->s_permanent_country,
            'pin_code'=> (!isset($formData->s_permanent_pincode) || $formData->s_permanent_pincode == '')? '' : $formData->s_permanent_pincode,
            'unformatted_address'=>$formData->s_permanent_addr.', '.$formData->s_permanent_area.', '.$formData->s_permanent_district.', '.$formData->s_permanent_state.', '.$formData->s_permanent_country.', '.$formData->s_permanent_pincode
          );
        }
        
        if (!empty($fatherAdd)) {
          $this->Admission_model->insert_father_address_details($fatherAdd);
        }

        if (!empty($motherAdd)) {
          $this->Admission_model->insert_mother_address_details($motherAdd);
        }
        
        if (!empty($guardianAdd)) {
          $this->Admission_model->insert_guaridan_address_details($guardianAdd);
        }

        if (!empty($student_present_add)) {
          $this->Admission_model->insert_student_present_address_details($student_present_add);
        }

        if (!empty($student_permanent_add)) {
          $this->Admission_model->insert_student_permanent_address_details($student_permanent_add);
        }

        // $this->Admission_model->insert_student_health_details($student_uid['stdAdmId'], $formData->student_blood_group);

        if (!empty($schoolDetails)) {
           $this->Admission_model->insert_schooling_details($schoolDetails, $student_uid['stdAdmId']);
        }
        // if (!empty($lang_combinationData)) {
        //   $this->Admission_model->insert_lang_combination_details($lang_combinationData, $student_uid['stdAdmId']);
        // }
        if (!empty($schoolDocuments)) {
          $this->Admission_model->insert_documents_details($schoolDocuments, $student_uid['stdAdmId']);
        }
        if ($this->db->trans_status()) {

            $admission_number_class_wise = $this->settings->getSetting('generate_class_wise_admission_number');

            if($generate_admn_num_after_move_toerp != 1 && $admission_number_class_wise == 1){

              $admission_number_format = $this->Admission_model->get_admission_number_format_by_classId($classId);

              if(!empty($admission_number_format)){
                $this->db->where('id',$admission_number_format->id);
                $this->db->update('feev2_receipt_book', array('running_number'=>$admission_number_format->running_number+1));
              }
            }

            $this->db->trans_commit();
            if($rte_id == 1 || $rte_id == 3 || $staffkid_or_transfer){
              $this->Admission_model->status_change_admission_idInsert('Student added to ERP',$admission_id, $student_uid['stdAdmId']);
            }else{
              $this->Admission_model->status_change_admission_idInsert('Offer Released',$admission_id, $student_uid['stdAdmId']);
            }
           
            $this->Admission_model->update_studId_inhealth_table($admission_id,$student_uid['stdAdmId']);
            $this->Admission_model->update_studId_inhospitalization_table($admission_id,$student_uid['stdAdmId']);
            $this->Admission_model->update_studId_invaccination_table($admission_id,$student_uid['stdAdmId']);
            $admission_number_id = $this->settings->getSetting('student_admission_number_receipt_book_id');
            if($generate_admn_num_after_move_toerp != 1 && !empty($admission_number_id)){
              $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$admission_number_id)->get()->row();
              $this->db->where('id',$admission_number_id);
              $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
            }

            if($automation_enrollment_number_id){
              $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$automation_enrollment_number_id)->get()->row();
              $this->db->where('id',$automation_enrollment_number_id);
              $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
            }

            if (!empty($_POST['offer_ids'])) {
              $this->Admission_model->apply_admission_offers_to_student($admission_id, $student_uid['stdAdmId'], $_POST['offer_ids'], 'Active');
            }
            if (!empty($_POST['seat_number'])) {
              $this->Admission_model->update_seat_allotment_no($_POST['seat_number'], $admission_id, $_POST['seat_allotment_date']);
              $this->_generate_seat_allotment_form_pdf($admission_id,$formData, $seat_allotment_no, $formData->admission_setting_id);
            }
            if($auto_assign->fees_auto_assign_default_credentials != ''){
              $assign_fees =  $this->Admission_model->assinged_fees_for_student($student_uid['stdAdmId']);
              // echo '<pre>';print_r($assign_fees);die();
              if(!empty($assign_fees)){
                foreach ($assign_fees as $bpId => $value) {
                  $res = $this->fees_student_model->insert_cohort_details($bpId, $value['cohort_id'], 'STANDARD', $value['blueprint_installment_type_id'], $value['comp_amount'], $value['concession_amount'], $value['student_id'], '', $value['fine_amount']);
                }
              }
            }
            $email_data = $this->Admission_model->getData_toSend_releaseOffer_email($admission_id);
            $fee_template_path = $this->Admission_model->get_feeTemplate_by_classId($classId);
            if(!empty($email_data)){
              $sent_mail = $this->_send_admission_mail($email_data,$fee_template_path, 'Offer Released');
            }
            return 1;
        } else {
            return 0; //failed
        }
      }          
    }

    private function _send_admission_mail($input,$fee_template_path, $type){
      $this->load->helper('email_helper');
      $this->load->model('communication/emails_model');
      $emailIds = $input['to_emails'];
      $input['template_content'] = str_replace('%%student_name%%', $input['student_name'], $input['template_content']);
      $input['template_content'] = str_replace('%%grade%%', $input['grade_applied_for'], $input['template_content']);
      $input['template_content'] = str_replace('%%father_name%%', $input['father_name'], $input['template_content']);
      $input['template_content'] = str_replace('%%mother_name%%', $input['mother_name'], $input['template_content']);
      $input['template_content'] = str_replace('%%fee_template_link%%', $fee_template_path, $input['template_content']);
      $input['template_content'] = str_replace('%%curriculum_interested%%', $input['curriculum_interested_in'], $input['template_content']);
      $files_array = array();

      if($fee_template_path != '') {
        array_push($files_array, array('name' => 'Fees Structure.pdf', 'path' => $fee_template_path));
      }

      $files_string = '';
      if(!empty($files_array)) {
        $files_string = json_encode($files_array);
      }

      $source = '';
      if($type == 'Move to ERP'){
        $source = 'Confirm Move to ERP from Admissions';
      } else if($type == 'Offer Released'){
        $source = 'Offer Released Email from Admissions';
      }

      $sent_by = $this->authorization->getAvatarStakeHolderId();
      $senderList = array_column($input['to_emails'], 'email');
      $email_master_data = array(
        'subject' => $input['email_subject'],
        'body' => $input['template_content'],
        'source' => $source,
        'sent_by' => $sent_by,
        'recievers' => "Parents",
        'from_email' => $input['registered_email'],
        'files' => empty($files_array) ? '' : json_encode($files_array) ,
        'acad_year_id' => $this->acad_year->getAcadYearID(),
        'visible' => 1,
        'sender_list'=>implode(',',$senderList),
        'sending_status' => 'Completed'
      );

      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      $email_data = [];
      $memberEmail = [];
      foreach ($emailIds as $key => $val) {
        if(empty($val)){
          continue;
        }else{
          array_push($memberEmail, $val['email']);
        }
        $email_obj = new stdClass();
        $email_obj->stakeholder_id = $val['stakeholder_id'];
        $email_obj->avatar_type = $val['avatar_type'];
        $email_obj->email = $val['email'];
        $email_data[] = $email_obj;
      }
      $this->emails_model->save_sending_email_data($email_data,$email_master_id);

      return sendEmail($input['template_content'], $input['email_subject'], $email_master_id, $memberEmail, $input['registered_email'], json_decode($files_string));
    }

    private function _constrcut_student_array_temp($formData, $classId, $sectionid,$combinations, $acad_year,$joining_date,$rte_id,$staff_kid,$transfer,$transfer_school,$combination_id){
      $board = $this->settings->getSetting('board');
      $boar_opted = '';
      if(!empty($board) && $formData->curriculum_interested_in){
        foreach($board as $key => $val){
          if(strtolower($val) == strtolower($formData->curriculum_interested_in)){
            $boar_opted = $key;
          }
        }
      }
      $stdData = array();
      $stdData['admission_acad_year'] = $formData->academic_year_applied_for;
      $stdData['student_firstname'] = ucwords(strtolower($formData->std_name));
      $stdData['student_lastname'] = ucwords(strtolower($formData->student_last_name));
      $stdData['student_middle_name'] = ucwords(strtolower($formData->student_middle_name));
      $stdData['student_dob'] = ($formData->dob == '')?null:date('Y-m-d', strtotime($formData->dob));
      $stdData['gender'] = $formData->gender;
      $stdData['nationality'] = ($formData->nationality == '')?'Indian':$formData->nationality;
      $stdData['religion'] =  ($formData->religion == '')?null:$formData->religion;
      $stdData['category'] = ($formData->category == '')?null:$formData->category;
      $stdData['mother_tongue'] =  ($formData->std_mother_tongue == '')?null:$formData->std_mother_tongue;
      $stdData['rteid'] = ($rte_id == '')? 2 : $rte_id; // if applicatiom form enabled
      $stdData['student_doj'] = $joining_date;
      $stdData['birth_taluk'] = ($formData->birth_taluk == '')?null:$formData->birth_taluk;
      $stdData['birth_district'] = ($formData->birth_district == '')?null:$formData->birth_district;
      $stdData['caste'] = ($formData->student_caste == '')?null:$formData->student_caste;
      $stdData['std_aadhar'] = ($formData->student_aadhar == '')?null:$formData->student_aadhar;
      $stdData['class_admitted_to'] = null;
      $stdData['admission_year'] = null;
      $stdData['admission_acad_year_id'] = $formData->academic_year_applied_for;
      $stdData['roll_no'] = null;
      $stdData['classid'] = $classId;
      $stdData['classsection'] = $sectionid;
      $stdData['boardingid'] = $formData->boarding; // get config
      $stdData['file_name'] = $formData->std_photo_uri;
      $stdData['acad_year'] = $acad_year;
      // $stdData['acad_year'] = '19';
      $stdData['board'] = $boar_opted; // get config
      $stdData['medid'] = '1'; // get config
      $stdData['acad_year_id'] = $acad_year;
      $stdData['roll_num'] = 0;
      $stdData['add_status'] = ($rte_id == 1 || $rte_id == 3) ? 2 : 1; // pending
      $stdData['s_email'] = ($formData->student_email_id == '')? '' :$formData->student_email_id;
      $stdData['ration_card_number'] = ($formData->ration_card_number == '')?null:$formData->ration_card_number;
      $stdData['ration_card_type'] = ($formData->ration_card_type == '')?null:$formData->ration_card_type;
      $stdData['caste_income_certificate_number'] = ($formData->caste_income_certificate_number == '')?null:$formData->caste_income_certificate_number;
      $stdData['extracurricular_activities'] = ($formData->extracurricular_activities == '')?null:$formData->extracurricular_activities;
      $stdData['quota'] = ($formData->student_quota == '')?null:$formData->student_quota;
      $stdData['student_sub_caste'] = ($formData->student_sub_caste == '')?null:$formData->student_sub_caste;
      $stdData['student_mobile_no'] = ($formData->student_mobile_no == '')?null:$formData->student_mobile_no;
      $stdData['blood_group'] = ($formData->student_blood_group == '')?null:$formData->student_blood_group;
      $stdData['donor_name'] = '';
      $stdData['combination'] = $combinations;
      $stdData['language_spoken'] = ($formData->primary_language_spoken == '')?'': (array)$formData->primary_language_spoken;
      $stdData['s_country_code'] = ($formData->s_country_code == '')?'': $formData->s_country_code;
      $stdData['point_of_contact'] = ($formData->emergency_contact == '')?'': $formData->emergency_contact;
      $stdData['staff_kid'] = $staff_kid;
      $stdData['transfer'] = $transfer;
      $stdData['transfer_from_school'] = $transfer_school;
      $stdData['high_quality_url_family'] = ($formData->family_photo == '')?'': $formData->family_photo;
      $stdData['high_quality_url'] = ($formData->std_photo_uri_resize == '')?'': $formData->std_photo_uri_resize;
      $stdData['contact_no'] = $formData->prefered_contact_number;
      $stdData['distance_from_school_to_home_in_km'] = $formData->school_to_home_distance_in_km;
      $stdData['student_signature'] = ($formData->student_signature == '')?'': $formData->student_signature;
      $stdData['second_language_currently_studying'] = ($formData->second_language_currently_studying == '')?'': $formData->second_language_currently_studying;
      $stdData['first_language_choice'] = ($formData->lang_1_choice == '')?'': $formData->lang_1_choice;
      $stdData['second_language_choice'] = ($formData->lang_2_choice == '')?'': $formData->lang_2_choice;
      $stdData['third_language_choice'] = ($formData->lang_3_choice == '')?'': $formData->lang_3_choice;
      $stdData['sts_number'] = ($formData->sats_number == '')?'': $formData->sats_number;
      $stdData['sibling1_name'] = ($formData->sibling_student_name == '')?'': $formData->sibling_student_name;
      $stdData['passport_issued_place'] = ($formData->passport_issued_place == '')?'': $formData->passport_issued_place;
      $stdData['passport_number'] = ($formData->passport_number == '')?'': $formData->passport_number;
      $stdData['passport_validity'] = ($formData->passport_expiry_date == '')?'': $formData->passport_expiry_date;
      $stdData['apaar_id'] = ($formData->apaar_id == '')?'': $formData->apaar_id;
      $transport = 0;
      if($formData->transport == 'Yes'){
        $transport = 1;
      }
      $stdData['transport'] = $transport;
      $stdData['transport_mode'] = ($formData->transportation_mode == '')?'': $formData->transportation_mode;
      $stdData['transportation_additional_details'] = ($formData->transport_addition_details == '')?'': $formData->transport_addition_details;
      $stdData['pen_number'] = ($formData->pen_number == '')?'': $formData->pen_number;
      $stdData['udise_number'] = ($formData->udise_number == '')?'': $formData->udise_number;
      $stdData['student_area_of_strength'] = ($formData->student_area_of_strength == '')?'': $formData->student_area_of_strength;
      $stdData['student_area_of_improvement'] = ($formData->student_area_of_improvement == '')?'': $formData->student_area_of_improvement;
      $stdData['student_hobbies'] = ($formData->student_hobbies == '')?'': $formData->student_hobbies;
      $stdData['did_they_enrolled_in_different_institute_earlier'] = ($formData->did_they_enrolled_in_different_institute_earlier == '')?'': $formData->did_they_enrolled_in_different_institute_earlier;
      $stdData['combination_id'] = $combination_id;

      $student_custom_fields = $this->settings->getSetting('student_admission_custom_fields');
      if(!empty($student_custom_fields) && !empty($formData->custom_field)){
        
        $formData->custom_field = json_decode($formData->custom_field);
        if(!empty($formData->custom_field)){
          foreach($formData->custom_field as $key => $val){
            foreach($student_custom_fields as $k => $v){
              if(strtolower(str_replace('_',' ',$key)) == strtolower($k)){
                $stdData[$v] = $val;
              }
            }
          }
        }
      }
      return $stdData;
    }

    private function _get_admissions_settings_byId($id){
      return  $this->Admission_model->admission_settings_getbyId($id);
    }

    private function _generate_seat_allotment_form_pdf($admission_form_id,$adm_details, $seat_allotment_no, $admission_setting_id){

      $config_val = $this->_get_admissions_settings_byId($admission_setting_id);
      if (!empty($config_val['seat_allotment_form_html'])) {
        $result = $this->_construct_seat_allotment_form_template($adm_details, $config_val['seat_allotment_form_html'], $seat_allotment_no);
        if ($result) {
          return $this->_generate_seat_allotment_pdf_receipt($result, $admission_form_id);
        }
      }
    }
    public function _construct_seat_allotment_form_template($adm_details, $template, $seat_allotment_no){
      $template = str_replace('%%seat_no%%',$seat_allotment_no, $template);
      $template = str_replace('%%date%%', $adm_details->seat_allotment_date, $template);
      $template = str_replace('%%student_name%%',$adm_details->std_name, $template);
      $template = str_replace('%%course%%',$adm_details->grade_applied_for, $template);
      $template = str_replace('%%acadmic_year%%', $this->acad_year->getAcadYearById($adm_details->academic_year_applied_for), $template);    
      return $template;
    }

    private function _generate_seat_allotment_pdf_receipt($html, $afId) {
      $school = CONFIG_ENV['main_folder'];
      $path = $school.'/seat_allotment_letters/'.uniqid().'-'.time().".pdf";
      $bucket = $this->config->item('s3_bucket');
      $status = $this->Admission_model->update_seat_allotment_form_path($afId, $path);
      $page_size = 'a4';
      $page = 'portrait';
      $curl = curl_init();
      $postData = urlencode($html);
      $username = CONFIG_ENV['job_server_username'];
      $password = CONFIG_ENV['job_server_password'];
      $return_url = '';

      curl_setopt_array($curl, array(
          CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_USERPWD => $username . ":" . $password,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
          CURLOPT_HTTPHEADER => array(
              "Accept: application/json",
              "Cache-Control: no-cache",
              "Content-Type: application/x-www-form-urlencoded",
              "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
          ),
      ));

      $response = curl_exec($curl);
      $err = curl_error($curl);
      curl_close($curl);
    }

    public function check_admission_offer_release(){
      $admission_id = $_POST['admission_id'];
      $check = $this->Admission_model->checkExit_in_Admission_studentTable($admission_id);
      $details = $this->Admission_model->get_student_release_details($admission_id);
      if(!empty($details->boarding)){
        $details->boarding = $this->settings->getSetting('boarding')[$details->boarding];
      }
      $offers = $this->Admission_model->get_student_release_offer_details($admission_id);
      echo  json_encode(array('check'=>$check,'details'=>$details,'offers'=>$offers));
    }

    public function check_fees_amount_for_student(){
      $admission_id= $_POST['admission_id'];
      $result = $this->Admission_model->check_in_feesid_and_studentid($admission_id);
      if (empty($result['student']->student_id)) {
        echo 0;
        exit;
      }
      
      $student_id = $result['student']->student_id;
      if (empty($result['fees'])) {
        echo '-1';
        exit;
      }
      $bpId = $result['fees']->blueprint_id;
      // $sibling_connected = $this->Admission_model->get_sibling_connected($student_id);
      if (!empty($result)) {
        $feeDetails = $this->fees_student_model->get_std_fee_cohort($student_id,$bpId);
        if (!empty($feeDetails)) {
          $feeDetails->offer_released_date = $result['student']->offer_released_date;

          echo json_encode(array('feeDetails'=>$feeDetails,'student'=>$student_id));
        }else{

          $student = $this->fees_student_model->get_std_detailsbyId($student_id,$this->acad_year->getAcadYearId());
          // $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($bpId);
          // $filter_columns = json_decode($blue_print->display_fields);
          
          $cohort_details = $this->fees_collection_model->get_all_cohorts_filter($bpId);

          $cohort_id = $this->fees_student_model->determine_cohort($bpId, $student);

          $installments_types = $this->fees_collection_model->get_installment_types($bpId);

          echo json_encode(array('student'=>$student,'cohort_details'=>$cohort_details,'installments_types'=>$installments_types,'cohort_id'=>$cohort_id,'student_id'=>$student_id,'bpId'=>$bpId,'feeDetails'=>''));
        }
       
      }
    }

    public function get_fees_assigned_details(){
      $admission_id= $_POST['admission_id'];
      $result = $this->Admission_model->check_in_feesid_and_studentid($admission_id);
      if (empty($result['student']->student_id)) {
        echo 0;
        exit;
      }
      $student_id = $result['student']->student_id;
      if (empty($result['fees'])) {
        echo '-1';
        exit;
      }
      $bpId = $result['fees']->blueprint_id;
      if (!empty($result)) {
        $feeDetails = $this->fees_student_model->get_std_fee_cohort($student_id,$bpId);
        $parentsIds = $this->Admission_model->getProvisionStudentDetailsbyid($result['student']->student_id);
        $credentials = [];
        foreach($parentsIds as $key =>$val){
          if($val->Active == 1){
            $credentials[$key] = $val;
          }
        }
        echo json_encode(array('feeDetails'=>$feeDetails,'student'=>$student_id,'credentials'=>$credentials));
      }
    }

    public function get_credentials_student_parent_data(){
      $admission_id= $_POST['admission_id'];
      $result = $this->Admission_model->check_in_feesid_and_studentid($admission_id);
      if (empty($result['student']->student_id)) {
        echo 0;
        exit;
      }
      $student_ids = (array)$result['student']->student_id;
      $parentsIds = $this->Admission_model->getProvisionStudentDetailsbyid($student_ids);
      echo json_encode($parentsIds);

    }

     public function getPreview_credentials_admission_flow(){
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS')) {
            redirect('dashboard', 'refresh');
        }
        $student_data = $this->db->select("concat(ifnull(af.std_name,''),' ',ifnull(af.student_middle_name,''),' ',ifnull(af.student_last_name,'')) as student_name,f_name,m_name,curriculum_interested_in,grade_applied_for")->from('admission_forms af')->where('id',$_POST['af_id'])->get()->row();
        $pids = $_POST['pids'];
        $school = $this->settings->getSetting('school_short_name');
        $part1 = $this->settings->getSetting('user_provisioning_manual_cridential_part_1');
        $school_code = $this->settings->getSetting('user_provisioning_school_code');
        $school_name = $this->settings->getSetting('school_name');

        $existingCodes = $this->user_provisioning_model->getExistingCodes();
        $pData = array();
        foreach ($pids as $key => $val) {
            $explodeVal = explode('_', $val);
            $student = $this->parent_activation_model->getPreviewCredentialsData_admission_flow($explodeVal[0]);
            
            if ($explodeVal[1] == 'email') {
                $email_content = $this->user_provisioning_model->get_user_provision_email_template();
            }

            if($explodeVal[1] == 'sms')
                $message_by = $student->mobile;
            else if($explodeVal[1] == 'email')
                $message_by = $student->email;

            $message_for_credit_calculation = '';
            $name = "$student->studentName";
            $encId = $this->generateRandomCode(6);
              $encId = 0;
              $randomString = $this->generatePassword(6);
              $res = $this->user_provisioning_model->resetPassword($student->user_id,$randomString);
              $message = str_replace("%%username%%", $student->username, $part1);
              $message = str_replace("%%password%%", $randomString, $message);
              $message = str_replace("%%school_name%%", $school_name, $message);
              $message = str_replace("%%school_code%%", $school_code, $message);

              if ($explodeVal[1] == 'email') {
                  $message = str_replace("%%student_name%%",$student_data->student_name,(empty($email_content)) ? '' : $email_content->content);
                  $message = str_replace("%%student_grade%%",$student_data->grade_applied_for,$message);
                  $message = str_replace("%%father_name%%",$student_data->f_name,$message);
                  $message = str_replace("%%mother_name%%",$student_data->m_name,$message);
                  $message = str_replace("%%curriculum_interested%%", $student_data->curriculum_interested_in, $message);
                  $message = str_replace("%%username%%", $student->username, $message);
                  $message = str_replace("%%password%%", $randomString, $message);
              }

            if(strlen($message_for_credit_calculation) < strlen($message)) {
                $message_for_credit_calculation = $message;
                //get the largest message for credits calculation
            }

            $pData[] = array(
                'std_id' => $student->std_id,
                'pid' => $student->pid,
                'user_id' => $student->user_id,
                'relation_type' => $student->relation_type,
                'name' => $name,
                'message' => $message,
                'message_by' => $message_by,
                'code' => $encId,
                'send_type' =>$explodeVal[1]
            );

            $is_credits_available = 1;
            if($explodeVal[1] == 'sms') {
                $this->load->helper('texting_helper');
                $is_credits_available = checkCredits($message_for_credit_calculation, count($pData), 'parent');
            }
        }
        echo json_encode(array('preview' => $pData, 'credits_available' => $is_credits_available));
    }

    private function generateRandomCode($length = 6) {
      return substr(str_shuffle(str_repeat($x='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz1234567890', ceil($length/strlen($x)) )),1,$length);
    }

    private function generatePassword($length = 10) {
        return substr(str_shuffle(str_repeat($x='abcdefghijkmnopqrstuvwxyz', ceil($length/strlen($x)) )),1,$length);
    }

    public function confirm_admission_student(){
      $studentAdmissionId = $_POST['studentAdmissionId'];
      $generate_admn_num_after_move_toerp = $this->settings->getSetting('admission_number_generation_while_student_move_to_erp');
      $afId = $_POST['afId'];
      if($generate_admn_num_after_move_toerp == 1){
        $std_class_id = $this->Admission_model->get_std_class_id($studentAdmissionId);
        $lastRecord = $this->Student_Model->getLastStudentid(); 
        $config_admission_number = $this->settings->getSetting('admission_number');
        $admission_number_id = $this->settings->getSetting('student_admission_number_receipt_book_id');
        $admission_number_class_wise = $this->settings->getSetting('generate_class_wise_admission_number');
        $admission_number_format = $this->Admission_model->get_admission_number_format_by_classId($std_class_id);

        if(!empty($admission_number_id)){
          $stdData['admission_no'] = $this->Student_Model->update_student_admission_no_by_receipt_book($lastRecord, $admission_number_id);
        }else if($admission_number_class_wise == 1 && (!empty($admission_number_format))){
          $stdData['admission_no'] =$this->Student_Model->update_student_admission_no_by_receipt_book($lastRecord,$admission_number_format->admission_number_format_id);
        }else{
          if (!$lastRecord) {
            $lastRecordId = 1;
          } else {
            $lastRecordId = $lastRecord->id;
          }
          $params['number'] = sprintf("%0".$config_admission_number->digit_count . "s", $lastRecordId);
          $params['year_of_joining'] = $this->acad_year->getAcadYearID();
          $params['classid'] = $this->Student_Model->getClassByID($std_class_id);   
          $stdData['admission_no'] = $this->_generateAdmissionNo($config_admission_number,$params);
        }

        $this->Admission_model->update_admission_number($studentAdmissionId,$stdData['admission_no']);

        $admission_number_id = $this->settings->getSetting('student_admission_number_receipt_book_id');
        if(!empty($admission_number_id)){
          $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$admission_number_id)->get()->row();
          $this->db->where('id',$admission_number_id);
          $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
        }

        $admission_number_class_wise = $this->settings->getSetting('generate_class_wise_admission_number');

        if($admission_number_class_wise == 1){

          $admission_number_format = $this->Admission_model->get_admission_number_format_by_classId($std_class_id);

          if(!empty($admission_number_format)){
            $this->db->where('id',$admission_number_format->id);
            $this->db->update('feev2_receipt_book', array('running_number'=>$admission_number_format->running_number+1));
          }
        }

      }
      $email_template_data = $this->Admission_model->get_data_to_send_movetoerp_mail($afId);
      if(!empty($email_template_data)){
        $sent_mail = $this->_send_admission_mail($email_template_data,'', 'Move to ERP');
      }
      echo $this->Admission_model->confirm_admission_to_erp_status_change('Student added to ERP',$afId, $studentAdmissionId);
    }

    public function send_parent_provision_credentials_from_admissions(){
        $parentEmails = [];
        $messages = $_POST['messages'];
        $idMessages['Father'] = array();
        $idMessages['Mother'] = array();
        $userIds = array();
        $relationsIds = array();
        foreach ($messages as $ids => $msg) {
            list($stdId, $pId, $relation, $user_id, $sent_type) = explode("_",$ids);
            if ($sent_type =='email' ) {
                $parentEmails[$pId] = $msg;
                $relationsIds[$pId] = $relation;
            }else{
                $idMessages[$relation][$stdId] = $msg;
                $idMessages[$relation]['mobile'] = $_POST['mobiles'][$pId];
            }
            array_push($userIds, $user_id);
        }
        $activeResponse = $this->user_provisioning_model->activateUsers($userIds);
        $result = '0';
        $message = '';
        if($activeResponse == 0) {
            $result = '0';
            $message = 'Unable to activate users';
        }
        if (!empty($parentEmails)) {
            $emailStatus = $this->_communicateByEmail($parentEmails, $relationsIds);
            if($emailStatus == 1) {
                $result = '1';
                $message = 'Successfully Email Sent';
            } else {
                $result = '0';
                $message = 'Something Went Wrong..';
            }
        }
        if (!empty($idMessages['Father']) || !empty($idMessages['Mother'])) {
            $smsStatus = $this->_communicateBySMS($idMessages);
            if($smsStatus == 1) {
                $result = '1';
                $message = 'Successfully SMS Sent';
            } else if($smsStatus == 0){
                $result = '0';
                $message = 'Something Went Wrong..';
            } else if($smsStatus == -1) {
                $result = '-1';
                $message = 'Not enough credits available';
            }
        }
        echo json_encode(array('result'=>$result,'message'=>$message));
    }

    private function _communicateByEmail($parentEmails, $relationsIds) {
      $set = $this->user_provisioning_model->get_user_provision_email_template();
      if(empty($set)){
        return 0;
      }
      $files_array = array();
      if(!empty($set->attached_file)) {
        $files_array[] = ['name' => 'Attached', 'path' => $set->attached_file];
      }

      $files_string = !empty($files_array) ? json_encode($files_array) : '';

      $this->load->model('communication/emails_model');
      $this->load->helper('email_helper');

      foreach ($parentEmails as $pid => $pEmail) {
        $email_ids = [];
        $parentData = $this->Admission_model->getEmailsByParentId($pid);

        if (empty(trim($parentData->email)) || !filter_var(trim($parentData->email), FILTER_VALIDATE_EMAIL)) {
            continue;
        }
        $sender_list = [
            'parents' => [
                'send_to' => trim($parentData->email),
                'send_to_type' => $relationsIds[$pid] ?? '',
                'ids' => $pid
            ]
        ];

        $email_obj = new stdClass();
        $email_obj->stakeholder_id = $parentData->parentId;
        $email_obj->avatar_type = $parentData->avatar_type;
        $email_obj->email = trim($parentData->email);
        $email_data = [$email_obj];
        if(!empty(trim($parentData->email))){
          $email_ids = [$parentData->email];
        }

        $email_master_data = array(
          'subject' => $set->email_subject,
          'body' => $pEmail,
          'source' => 'Parent Activation During Release Offers at Admissions',
          'sent_by' => $this->authorization->getAvatarId(),
          'recievers' => 'Parents',
          'from_email' => $set->registered_email,
          'files' => $files_string,
          'acad_year_id' => $this->acad_year->getAcadYearId(),
          'visible' => 1,
          'sender_list' => json_encode($sender_list),
          'sending_status' => 'Completed'
        );

        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $this->emails_model->save_sending_email_data($email_data,$email_master_id);

        $email = $this->emails_model->getEmailInfo($email_master_id);
        if(!empty($email_ids)){
          sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($files_string));
        }
      }
      return 1;
    }

    private function _communicateBySMS($stdMessages) {
      $sh_type = 'Student';
      $source = 'Parent User Provisioning';
      $sent_by = $this->authorization->getAvatarId();
      $sent_to_str = 'Student Individual';
      if(!empty($stdMessages['Father'])) {
          $input_arr = array();
          $input_arr['student_id_messages'] = $stdMessages['Father'];
          $input_arr['mode'] = 'sms';
          $input_arr['source'] = 'User Provisioning';
          $input_arr['send_to'] = 'Father';
          $res = $this->_send_unique_texts($input_arr);
          if($res['error'] != '') {
              return 0;
          }
      }

      if(!empty($stdMessages['Mother'])) {
          $input_arr = array();
          $input_arr['student_id_messages'] = $stdMessages['Mother'];
          $input_arr['mode'] = 'sms';
          $input_arr['source'] = 'User Provisioning';
          $input_arr['send_to'] = 'Mother';
          $res = $this->_send_unique_texts($input_arr);
          if($res['error'] != '') {
              return 0;
          }
      }
      return 1;
    }

    private function _send_unique_texts($input) {
        $this->load->helper('texting_helper');
        return sendUniqueText($input);
    }

    public function getCombinations(){
      $result = $this->Admission_model->getCombList($_POST);
      echo json_encode($result);
    }
    
    public function getCombinations_new(){
      $result = $this->Admission_model->getCombList_by_class_id($_POST);
      echo json_encode($result);
    }

    public function get_payment_link(){
      $af_id = $this->input->post('af_id');
      $adm_data = $this->db->select('f_mobile_no,sa.id as student_id')->from('admission_forms af')->join('student_admission sa','af.id=sa.admission_form_id')->where('af.id',$af_id)->get()->row();
      $bpId = $this->input->post('bpId');
      $generation_type = $this->input->post('generation_type');
      $amount = '10';
      $input = array(
          'transaction_mode' => 'ONLINE',
          'receipt_date' => $this->Kolkata_datetime()
      );
    
      $student_fee_data = $this->Admission_model->get_student_fee_assinged_data($adm_data->student_id, $bpId);
      $inputMerge = array_merge($student_fee_data, $input);
      $fTransId = $this->Admission_model->insert_fee_transcation_suy($inputMerge, $amount);
      $link = $this->payment->get_payment_link($amount, $adm_data->f_mobile_no, $adm_data->student_id, $fTransId);
      $store_link = $this->Admission_model->store_generated_link($link,$adm_data->student_id,$bpId,$generation_type,$af_id);
      echo  $store_link;
    }
    
    public function Kolkata_datetime(){
      $timezone = new DateTimeZone("Asia/Kolkata" );
      $date = new DateTime();
      $date->setTimezone($timezone );
      $dtobj = $date->format('Y-m-d H:i:s');
      return $dtobj;
    }

    public function get_student_parent_data(){
      $admission_id= $_POST['admission_id'];
      $result = $this->Admission_model->check_in_feesid_and_studentid($admission_id);
      $student_ids = $result['student'];
      $parentsIds = $this->Admission_model->getStudentDetailsbyid($result['student']->student_id);
      // echo '<pre>';print_r($parentsIds);die();
      echo json_encode($parentsIds);
  
    }

    public function getPreview_email_template(){
      // echo '<pre>';print_r($_POST);die();
      $pids = $_POST['pids'];
      $amount= $_POST['amount'];
      $link= $_POST['link'];
      $pData = array();
      foreach ($pids as $key => $val) {
          $explodeVal = explode('_', $val);
          $student = $this->Admission_model->getstudentData($explodeVal[0]);        
          $email_content = $this->Admission_model->get_payment_link_email_template();
          $message_by = $student->email;
          $name = "$student->studentName";
          // $message_for_credit_calculation = '';
          $message = $email_content->content;
          $message = str_replace("%%amount%%",$amount , $message);
          $message = str_replace("%%payment_link%%",$link , $message);
          // echo '<pre>';print_r($message);die();
          // if(strlen($message_for_credit_calculation) < strlen($message)) {
          //     $message_for_credit_calculation = $message;
          //     //get the largest message for credits calculation
          // }
          // echo '<pre>';print_r($message_by);die();
          if($message_by != ''){
            $pData[] = array(
              'std_id' => $student->std_id,
              'pid' => $student->pid,
              'user_id' => $student->user_id,
              'relation_type' => $student->relation_type,
              'name' => $name,
              'message' => $message,
              'message_by' => $message_by,
              'send_type' =>$explodeVal[1]
          );
  
          }
          
      }
      echo json_encode(array('preview' => $pData));
    }

    public function send_payment_link_toParent(){
      $parentEmails = [];
      $messages = $_POST['messages'];
      $userIds = array();
      foreach ($messages as $ids => $msg) {
          list($stdId, $pId, $relation, $user_id, $sent_type) = explode("_",$ids);
          $parentEmails[$pId] = $msg;
          array_push($userIds, $user_id);
      }
      // echo '<pre>';print_r($parentEmails);die();
      $result = '0';
      $message = '';
      
      if (!empty($parentEmails)) {
          $emailStatus = $this->_send_payment_link_ByEmail($parentEmails);
          if($emailStatus == 1) {
              $result = '1';
              $message = 'Successfully Email Sent';
              $this->session->set_flashdata('flashSuccess', 'Successfully Created');
              
          } else {
              $result = '0';
              $message = 'Something Went Wrong..';
              $this->session->set_flashdata('flashError', 'Something went wrong');
          }
      }
      
      echo json_encode(array('result'=>$result,'message'=>$message));
    }

    private function _send_payment_link_ByEmail($parentEmails) {
      $parentIds = array();
      $userIds = array();
      foreach ($parentEmails as $pid => $pEmail) {
          $parentIds[] = $pid;
      }
    
      $emails = $this->user_provisioning_model->getEmailsByParentId($parentIds);
      $mailIds = array();
      $messages = array();
      $usernames = array();
      foreach ($emails as $key => $email) {
          array_push($mailIds, $email->email);
          array_push($usernames, $email->username);
          array_push($messages, $parentEmails[$email->parentId]);
      }
      $set = $this->Admission_model->get_payment_link_email_template();  

      $data = array(
          'send_unique_emails' => 1,
          'message' => $messages,
          'subject' => (empty($set)) ? '' : $set->email_subject,
          'mail_username' => $usernames,
          'email_ids' => $mailIds,
          'template' => 'Parent Credentials'
      );
       return $this->_emailSender_provision($data);
    }

    public function _emailSender_provision($data){
      $set = $this->Admission_model->get_payment_link_email_template();  
      $from_name = $this->settings->getSetting('school_name');     
      $from_email = (empty($set)) ? '' : $set->registered_email;
       // echo "<pre>"; print_r($from_email);die();
      $smtp_user = CONFIG_ENV['smtp_user'];
      $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
      $smtp_host = CONFIG_ENV['smtp_host'];
      $smtp_port = CONFIG_ENV['smtp_port'];

      $data['from_email'] = $from_email;
      $data['from_name'] = $from_name;
      $data['smtp_user'] = $smtp_user;
      $data['smtp_pass'] = $smtp_pass;
      $data['smtp_host'] = $smtp_host;
      $data['smtp_port'] = $smtp_port;
      $data = http_build_query($data);
      $curl = curl_init();

      $username = CONFIG_ENV['job_server_username'];
      $password = CONFIG_ENV['job_server_password'];
      curl_setopt_array($curl, array(
          CURLOPT_URL => CONFIG_ENV['job_server_unique_email_uri'],
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_USERPWD => $username . ":" . $password,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POST => 1,
          CURLOPT_POSTFIELDS => $data,
          CURLOPT_HTTPHEADER => array(
              "Accept: application/json",
              "Cache-Control: no-cache",
              "Content-Type: application/x-www-form-urlencoded",
              "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
          ),
      ));

      $response = curl_exec($curl);

      $err = curl_error($curl);

      curl_close($curl);

      if ($err) {
        return 0;
      } else {
        return 1;
      }
  }

  public function get_move_to_erp_details(){
    $admission_form_id= $_POST['admission_form_id'];
    $result = $this->Admission_model->get_move_to_erp_details_by_data($admission_form_id);
    echo json_encode($result);
  }

  public function get_sibling_data(){
    $result = $this->Admission_model->get_sibling_data($_POST['student_id']);
    echo json_encode($result);
  }

  public function LinkAccounts(){
    $std_ids = $_POST['std_ids'];
    $result =  $this->Admission_model->getStdDetails($std_ids);
    echo json_encode($result);
  }
}