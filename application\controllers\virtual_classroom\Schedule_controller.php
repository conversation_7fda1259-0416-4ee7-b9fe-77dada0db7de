<?php
  /**
   * Name:    Oxygen
   * Author:  <PERSON>
   *          <EMAIL>
   *
   * Created:  17 May 2018
   *
   * Description:  
   *
   * Requirements: PHP5 or above
   *
   */

  defined('BASEPATH') OR exit('No direct script access allowed');

  /**
   * Class Fee
   * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
   * @property CI_Form_validation      $form_validation The form validation library
   */
  class Schedule_controller extends CI_Controller {

    private $year_id;

    public function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isAuthorized('VIRTUAL_CLASSROOM.MODULE')) {
        redirect('dashboard', 'refresh');
      }
      date_default_timezone_set('Asia/Kolkata');
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('virtual_classroom/Schedule_model', 'schedule');
      $this->load->model('class_section');
      $this->load->model('student/Student_Model');
    }

    public function index(){
      $data['is_admin'] = $this->authorization->isAuthorized('VIRTUAL_CLASSROOM.ADMIN');
      $data['create_schedule'] = $this->authorization->isAuthorized('VIRTUAL_CLASSROOM.CREATE_SCHEDULE');
      if ($this->mobile_detect->isMobile()) {
        $data['main_content'] = 'virtual_classroom/schedule/mobile_index.php';
      } else {
        $data['main_content']    = 'virtual_classroom/schedule/index.php';
      }
      $this->load->view('inc/template', $data);  
    }

    public function add_schedule() {
      $data['is_admin'] = $this->authorization->isAuthorized('VIRTUAL_CLASSROOM.ADMIN');
      $data['staff'] = $this->schedule->getAllStaff();
      $data['current_staff'] = $this->authorization->getAvatarStakeHolderId();
      $data['rooms'] = $this->schedule->getClassRooms();
      $data['all_slots'] = $this->schedule->getAllSlots();
      $data['main_content'] = 'virtual_classroom/schedule/new_schedule';
      $this->load->view('inc/template', $data);
    }

    public function edit_schedule($schedule_id) {
      $data['is_admin'] = $this->authorization->isAuthorized('VIRTUAL_CLASSROOM.ADMIN');
      $data['schedule'] = $this->schedule->getSchedule($schedule_id);
      $date = date('Y-m-d', strtotime($data['schedule']->start_time));
      $classroom_code = $data['schedule']->virtual_classroom_code;
      $data['selected_slots'] = $this->schedule->getSelectedSlots($schedule_id);
      $data['staff'] = $this->schedule->getAllStaff();
      $data['current_staff'] = $data['schedule']->created_by;
      $data['rooms'] = $this->schedule->getClassRooms();
      $data['all_slots'] = $this->schedule->getAllSlots();
      $occupied_slots = $this->schedule->getOccupiedSlots($date, $classroom_code);
      $data['occupied_slots'] = [];
      foreach ($occupied_slots as $key => $val) {
        $data['occupied_slots'][] = $val->id;
      }
      // echo "<pre>"; print_r($data); die();
      $data['main_content'] = 'virtual_classroom/schedule/edit_schedule';
      $this->load->view('inc/template', $data);
    }

    public function save_schedule() {
      $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $slot_id = $input['slots'];
      $slot_ids = [$slot_id];
      $host_busy = $this->schedule->checkIsHostBusy($input['host_id'], $slot_ids, $input['schedule_date']);
      if($host_busy) {
        $this->session->set_flashdata('flashError', 'Staff already have class on selected slot');
        redirect('virtual_classroom/schedule_controller/add_schedule');
      } else {
        $slots = $this->schedule->getSlotDataByIds($slot_ids);
        $start = $input['schedule_date'].' '.$slots[0]->start_time;
        $end = $input['schedule_date'].' '.$slots[count($slots) - 1]->end_time;
        $start_time = date('Y-m-d H:i:s', strtotime($start));
        $end_time = date('Y-m-d H:i:s', strtotime($end));
        // echo "<pre>"; print_r($input);die();
        $data = array(
          'name' => $input['name'],
          'description' => $input['description'],
          'virtual_classroom_code' => $input['schedule_room'],
          'start_time' => $start_time,
          'end_time' => $end_time,
          'acad_year_id' => $this->yearId,
          'created_by' => $input['host_id']
        );

        $slot_data = [];
        foreach ($slot_ids as $slot_id) {
          $slot_data[] = array(
            'classroom_code' => $input['schedule_room'],
            'slot_id' => $slot_id,
            'date' => date('Y-m-d', strtotime($input['schedule_date'])),
            'status' => 1
          );
        }
        $status = $this->schedule->save_schedule($data, $slot_data);
        // echo "<pre>"; print_r($data); die();
        if($status) {
          $this->load->helper('texting_helper');

          //Send notification to assigned staff
          $message = "New virtual class schedule Class:".$input['name'].", On ".$input['schedule_date']." from ".date('h:i a', strtotime($start))." to ".date('h:i a', strtotime($end));
          $input_arr = array();
          $input_arr['staff_ids'] = $input['host_id'];
          $input_arr['mode'] = 'notification';
          $input_arr['source'] = 'Virtual Class';
          $input_arr['message'] = $message;
          $response = sendText($input_arr);
          $this->session->set_flashdata('flashSuccess', 'Successful added schedule');
        } else {
          $this->session->set_flashdata('flashError', 'Something went wrong');
        }
        redirect('virtual_classroom/schedule_controller');
      }
    }

    public function update_schedule() {
      $input = $this->input->post();
      $schedule_id = $input['schedule_id'];
      $slot_id = $input['slots'];
      $slot_ids = [$slot_id];
      $host_busy = $this->schedule->checkIsHostBusy($input['host_id'], $slot_ids, $input['schedule_date'], $schedule_id);
      if($host_busy) {
        $this->session->set_flashdata('flashError', 'Staff already have class on selected slot');
        redirect('virtual_classroom/schedule_controller/edit_schedule/'.$schedule_id);
      }
      $slots = $this->schedule->getSlotDataByIds($slot_ids);
      $start = $input['schedule_date'].' '.$slots[0]->start_time;
      $end = $input['schedule_date'].' '.$slots[count($slots) - 1]->end_time;
      $start_time = date('Y-m-d H:i:s', strtotime($start));
      $end_time = date('Y-m-d H:i:s', strtotime($end));
      // echo "<pre>"; print_r($input);die();
      $data = array(
        'name' => $input['name'],
        'description' => $input['description'],
        'virtual_classroom_code' => $input['schedule_room'],
        'start_time' => $start_time,
        'end_time' => $end_time,
        'acad_year_id' => $this->yearId,
        'created_by' => $input['host_id']
      );

      $slot_data = [];
      foreach ($slot_ids as $slot_id) {
        $slot_data[] = array(
          'schedule_id' => $input['schedule_id'],
          'classroom_code' => $input['schedule_room'],
          'slot_id' => $slot_id,
          'date' => date('Y-m-d', strtotime($input['schedule_date'])),
          'status' => 1
        );
      }
      $status = $this->schedule->update_schedule($data, $slot_data, $schedule_id);
      // echo "<pre>"; print_r($data); die();
      if($status) {
        $this->load->helper('texting_helper');

        //Send notification to assigned staff
        $message = "Update to virtual class schedule Class:".$input['name'].", On ".$input['schedule_date']." from ".date('h:i a', strtotime($start))." to ".date('h:i a', strtotime($end));
        $input_staff = array();
        $input_staff['staff_ids'] = $input['host_id'];
        $input_staff['mode'] = 'notification';
        $input_staff['source'] = 'Virtual Class';
        $input_staff['message'] = $message;
        $response = sendText($input_staff);//staff notification

        $schedule = $this->schedule->getSchedule($schedule_id);
        $invitees = $this->schedule->getScheduleParticipants($schedule_id, $this->yearId);
        $notify_array = array();
        $notify_array['mode'] = 'notification';
        $notify_array['send_to'] = 'Both';
        $notify_array['source'] = 'Virtual Class Room';
        $notify_array['message'] = $schedule->name.' class on '.date('d-M h:i a', strtotime($schedule->start_time)).' to '.date('d-M h:i a', strtotime($schedule->end_time)).' is updated';
        if(!empty($invitees['students'])) {
          foreach ($invitees['students'] as $key => $std) {
            $notify_array['student_ids'] = $std->id;
          }
          sendText($notify_array);
          unset($notify_array['student_ids']);
        }
        if(!empty($invitees['staff'])) {
          foreach ($invitees['staff'] as $key => $std) {
            $notify_array['staff_ids'] = $std->id;
          }
          sendText($notify_array);
          unset($notify_array['staff_ids']);
        }
        $this->session->set_flashdata('flashSuccess', 'Successfully updated schedule');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('virtual_classroom/schedule_controller');
    }

    public function getSchedules() {
      $type = $_POST['type'];
      $is_admin = $this->authorization->isAuthorized('VIRTUAL_CLASSROOM.ADMIN');
      $host_id = 0;
      if(!$is_admin) {
        $host_id = $this->authorization->getAvatarStakeHolderId();
      }
      $schedules = $schedules = $this->schedule->getSchedules($host_id, $type);
      $data = array();
      $now = date('Y-m-d H:i:s');
      foreach ($schedules as $key => $sch) {
        $schedules[$key]->session_status = 'Not Started';
        if (strtotime($sch->end_time)<strtotime($now)) {
          $schedules[$key]->session_status = 'Completed';
        } else if((strtotime($sch->start_time)<strtotime($now)) && (strtotime($sch->end_time)>strtotime($now))) {
          $schedules[$key]->session_status = 'Active';
        }
        $sch->date = date('d-M Y', strtotime($sch->start_time));
        $sch->start_time = date('h:i a', strtotime($sch->start_time));
        $sch->end_time = date('h:i a', strtotime($sch->end_time));
        $data[$sch->id] = $sch;
      }
      echo json_encode($data);
    }

    public function cancelSchedule() {
      $schedule_id = $_POST['schedule_id'];
      echo $this->schedule->cancelSchedule($schedule_id);
    }

    public function add_participants($schedule_id) {
      $data['schedule_id'] = $schedule_id;
      $data['schedule'] = $this->schedule->getSchedule($schedule_id);
      $data['sections'] = $this->schedule->getAllClassSections();
      $data['allotted'] = $this->schedule->getAllocatedClassSections($schedule_id);
      $data['staff'] = $this->schedule->getAllStaff();
      $data['participants'] = $this->schedule->getScheduleParticipants($schedule_id, $this->yearId);
      $data['main_content'] = 'virtual_classroom/schedule/add_participants';
      $this->load->view('inc/template', $data);
    }

    public function getOccupiedSlots() {
      $date = date('Y-m-d', strtotime($_POST['date']));
      $classroom_code = $_POST['schedule_room'];
      $data = $this->schedule->getOccupiedSlots($date, $classroom_code);
      $slots = [];
      foreach ($data as $key => $val) {
        $slots[] = $val->id;
      }
      echo json_encode($slots);
    }

    public function getSectionStudents() {
      $section_id = $_POST['section_id'];
      $schedule_id = $_POST['schedule_id'];
      $data['allotted'] = $this->schedule->allottedStudents($schedule_id);
      $data['students'] = $this->schedule->getSectionStudents($section_id, $this->yearId);
      echo json_encode($data);
    }

    public function add_invitees() {
      $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $schedule_id = $input['schedule_id'];
      $type = $input['type'];
      $existing_ids = $this->schedule->getExistingInvitees($schedule_id, $type);
      $sending_ids = [];
      $invitees = [];
      $notify_array = [];
      $class_section_id = NULL;
      if($type == 'class_section') {
        $class_section_id = $input['class_section'];
        $students = $this->schedule->getSectionStudents($class_section_id, $this->yearId);
        foreach ($students as $key => $std) {
          if(!in_array($std->id, $existing_ids)) {
            $sending_ids[] = $std->id;
          }
        }
        $notify_array['student_ids'][] = $std->id;
      } else if($type == 'student') {
        $stdIds = $input['students'];
        $class_section_id = $input['std_sections'];
        foreach ($stdIds as $key => $std_id) {
          if(!in_array($std_id, $existing_ids)) {
            $sending_ids[] = $std_id;
          }
        }
        $notify_array['student_ids'] = $sending_ids;
      } else if($type == 'staff') {
        $staff_ids = $input['staff'];
        foreach ($staff_ids as $key => $staff_id) {
          if(!in_array($staff_id, $existing_ids)) {
            $sending_ids[] = $staff_id;
          }
        }
        $notify_array['staff_ids'] = $sending_ids;
      }

      if(!empty($sending_ids)) {
        foreach ($sending_ids as $key => $id) {
          $invitees[] = array(
            'schedule_id' => $schedule_id,
            'type' => $type,
            'stake_holder_id' => $id
          );
        }
        // $this->schedule->updateSectionsData($schedule_id);
        // echo '<pre>'; print_r($invitees); die();
        $status = $this->schedule->add_participants($invitees, $class_section_id, $schedule_id);
        if($status) {
          $this->schedule->updateSectionsData($schedule_id);
          $this->load->helper('texting_helper');
          $schedule = $this->schedule->getSchedule($schedule_id);
          $notify_array['mode'] = 'notification';
          $notify_array['send_to'] = 'Both';
          $notify_array['source'] = 'Virtual Class Room';
          $notify_array['message'] = $schedule->name.' class has been scheduled on '.date('d-M h:i a', strtotime($schedule->start_time)).' to '.date('d-M h:i a', strtotime($schedule->end_time));
          sendText($notify_array);
          $this->session->set_flashdata('flashSuccess', 'Successful added Invitees');
        } else {
          $this->session->set_flashdata('flashError', 'Something went wrong');
        }
        redirect('virtual_classroom/schedule_controller/add_participants/'.$schedule_id);
      }
      $this->session->set_flashdata('flashInfo', 'Selected Students Already Exists');
      redirect('virtual_classroom/schedule_controller/add_participants/'.$schedule_id);
    }

    public function remove_participant() {
      $schedule_id = $_POST['schedule_id'];
      $status = $this->schedule->remove_participant();
      if($status) {
          $this->schedule->updateSectionsData($schedule_id);
          $this->load->helper('texting_helper');
          $schedule_id = $_POST['schedule_id'];
          $stake_holder_id = $_POST['id'];
          $type = $_POST['type'];
          $schedule = $this->schedule->getSchedule($schedule_id);
          $notify_array = [];
          if($type == 'staff') {
            $notify_array['staff_ids'] = [$stake_holder_id];
          } else {
            $notify_array['student_ids'] = [$stake_holder_id];
          }
          $notify_array['mode'] = 'notification';
          $notify_array['send_to'] = 'Both';
          $notify_array['source'] = 'Virtual Class Room';
          $notify_array['message'] = $schedule->name.' class on '.date('d-M h:i a', strtotime($schedule->start_time)).' to '.date('d-M h:i a', strtotime($schedule->end_time)).' is cancelled for you.';
          sendText($notify_array);
          echo 1;
      } else {
        echo 0;
      }
    }

    public function addNewQuestions(){
      $data['classList'] = $this->Student_Model->getClassNames();
      $data['main_content'] = 'virtual_classroom/schedule/add_questions';
      $this->load->view('inc/template', $data);
    }

    public function save_new_questions(){
      $data = $this->schedule->save_new_questions();
      echo $data;
    }

    public function getAddedQuestions(){
      $data = $this->schedule->getAddedQuestions();
      echo json_encode($data);
    }

    public function getSingleQuestionDetails(){
      $data = $this->schedule->getSingleQuestionDetails();
      echo json_encode($data);
    }

    public function update_questions(){
      $data = $this->schedule->update_questions();
      echo $data;
    }

    public function deleteSingleQuestion(){
      $data = $this->schedule->deleteSingleQuestion();
      echo json_encode($data);
    }

    public function getSubjects(){
      $data = $this->schedule->getSubjects();
      echo json_encode($data);
    }

    public function getLessons(){
      $data = $this->schedule->getLessons();
      echo json_encode($data);
    }

    public function getSubTopics(){
      $data = $this->schedule->getSubTopics();
      echo json_encode($data);
    }

    public function addNewLesson(){
      $data = $this->schedule->addNewLesson();
      echo json_encode($data);
    }

    public function getLessonandSubTopicDetails(){
      $data = $this->schedule->getLessonandSubTopicDetails();
      echo json_encode($data);
    }
    public function licences() {
      if (!$this->authorization->isAuthorized('VIRTUAL_CLASSROOM.ADMIN')) {
        redirect('dashboard', 'refresh');
      }
      $data['licences'] = $this->schedule->getLicences();
      // echo "<pre>"; print_r($data); die();
      $data['main_content'] = 'virtual_classroom/licences/index';
      $this->load->view('inc/template', $data);
    }

    public function release_classroom($classroom_id) {
      $status = $this->schedule->releaseClassRoom($classroom_id);
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully released room');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('virtual_classroom/schedule_controller/licences');
    }

    public function addNewLicence(){
      $data = $this->schedule->addNewLicence();
      echo json_encode($data);
    }

    public function manageSlots(){
      $data['all_slots'] = $this->schedule->getAllSlots();
      // echo "<pre>";print_r($data['all_slots']);die();
      $data['main_content'] = 'virtual_classroom/schedule/manage_slots';
      $this->load->view('inc/template', $data);
    }

    public function addNewSlot(){
      $data = $this->schedule->addNewSlot();
      echo json_encode($data);
    }

    public function getAllAvailableSlots_ajax(){
      $data = $this->schedule->getAllSlots();
      echo json_encode($data);
    }

    public function deleteSingleSlot(){
      $data = $this->schedule->deleteSingleSlot();
      echo json_encode($data);
    }

    public function getSingleSlotDetails(){
      $data = $this->schedule->getSingleSlotDetails();
      echo json_encode($data);
    }

    public function editSingleSlot(){
      $data = $this->schedule->editSingleSlot();
      echo json_encode($data);
    }

    public function addQuestionsToSchedule($schedule_id){
      $data['schedule_id'] = $schedule_id;
      // $data['sections'] = $this->schedule->getAvailableClassSections($schedule_id);
      // $data['staff'] = $this->schedule->getAllStaff();
      $data['schedule'] = $this->schedule->getSchedule($schedule_id);
      // $data['participants'] = $this->schedule->getScheduleParticipants($schedule_id, $this->yearId);
      $data['classList'] = $this->Student_Model->getClassNames();
      $data['scheduled_questions'] = $this->schedule->getSheduledQuestions($schedule_id);
      $data['main_content'] = 'virtual_classroom/schedule/questions_to_schedule';
      $this->load->view('inc/template', $data);
    }

    public function getQuestions(){
      $data = $this->schedule->getQuestions();
      echo json_encode($data);
    }

    public function insertQuestionsToSchedule(){
      $data = $this->schedule->insertQuestionsToSchedule();
      echo json_encode($data);
    }

    public function removeScheduledQuestion(){
      $data = $this->schedule->removeScheduledQuestion();
      echo json_encode($data);
    }

    public function getSingleLicenceDetails(){
      $data = $this->schedule->getSingleLicenceDetails();
      echo json_encode($data);
    }

    public function update_licence_configuration(){
      $data = $this->schedule->update_licence_configuration();
      echo json_encode($data);
    }
}?>