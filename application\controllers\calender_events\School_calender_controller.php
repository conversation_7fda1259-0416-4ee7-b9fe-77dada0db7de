<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of School_calender_controller
 *
 * <AUTHOR>
 */
class School_calender_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }

        $this->load->model('School_calender_Model');
    }

    public function index() {
        //echo $id."fdgdfg";die();
        //   $data['schoolEventsEdit']=$this->School_calender_Model->schoolEventEdit($id);
        $data['schoolEventsInfos'] = $this->School_calender_Model->schoolEventList();
        $data['main_content'] = 'calenderEvents/School_calender';
        $this->load->view('inc/template', $data);
    }

    public function addSchoolEvents() {
        $addSchoolEventsInfos = $this->School_calender_Model->schoolEventsData();
        if ($addSchoolEventsInfos) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Added');
            redirect('calender_events/School_calender_controller/index');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('calender_events/School_calender_controller/index');
        }
    }

    public function editEvent($id) {
        $data['schoolEventsInfos'] = $this->School_calender_Model->schoolEventList();
        $data['schoolEventsEdit'] = $this->School_calender_Model->schoolEventEdit($id);
        $data['main_content'] = 'calenderEvents/School_calender';
        $this->load->view('inc/template', $data);
    }

    public function updateEvent($id) {
        $schoolEventsupdate = $this->School_calender_Model->schoolEventUpdate($id);
        if ($schoolEventsupdate) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Updated');
            redirect('calender_events/School_calender_controller/index');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('calender_events/School_calender_controller/index');
        }
    }

    public function deleteEvent($id) {
        $schoolEventsdelete = $this->School_calender_Model->schoolEventdelete($id);
        if ($schoolEventsdelete) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Delted');
            redirect('calender_events/School_calender_controller/index');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('calender_events/School_calender_controller/index');
        }
    }

}
