<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  05 June 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Fee_concorde extends CI_Controller {

  public function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    // if (!$this->authorization->isModuleEnabled('FEES_CONCORDE')) {
    //   redirect('dashboard', 'refresh');
    // }
    $this->load->model('fees/fee_transaction_concorde');
    $this->load->model('student/Student_Model');
    $this->load->model('fees/fee_transportation_model');
    $this->load->model('transportation_model');
    $this->load->model('fees/fee_transaction_misc');
    $this->load->library('filemanager');
    $this->load->model('fees/Fee_Master_Model');
  }

// Index page fee collection
  public function index(){
    if (empty($classSectionId)){
      $classSectionId = $this->input->post('classSectionId');
    }
    if (empty($classSectionId)){
      $classSectionId = 0;
    }
   
      $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
      $data['getstudents'] = $this->Student_Model->getstudentDetails($classSectionId);
      $data['studentNames'] = $this->Student_Model->getstudentNames();
      $data['main_content'] = 'fees/concorde/index';
      $this->load->view('inc/template', $data);
}
  
  // json get serach by student wise
  public function getStudentByNameAndNo(){
    $isName = $_POST['isName'];
    if($isName) {
      $name = $_POST['name'];
      $stdData = $this->Student_Model->getstdDataByName($name);
    }
    else {
      $adNo = $_POST['ad_no'];
      $stdData = $this->Student_Model->getStudentByAdNo($adNo);
    }
    // print_r($stdData);
    $html = '';
    if(empty($stdData)) 
      $html .= "<h4>Select filter to get student data</h4>";
    else {
      $i = 1;
      $html .= '<table id="customers2" class="table datatable"><thead><tr><th>#</th><th>Student Name</th><th>Admission No</th><th>Section</th><th>Father Name</th><th width="10%">Actions</th></tr></thead><tbody>';
      foreach ($stdData as $key=>$val) {
        $html .= "<tr><td>".$i++."</td>";
        $html .= "<td>".$val->stdName."</td>";
        $html .= "<td>".$val->admission_no."</td>";
        $html .= "<td>".$val->className."/".$val->sectionName."</td>";
        $html .= "<td>".$val->fName."</td>";
        $html .= "<td><a href=".site_url('fees/fee_concorde/studentFeeDetails/'.$val->id)."  class='btn btn-primary'>Collect fees</i></a></td></tr>";
      }
      $html .= '</tbody></table>';
    }
    echo json_encode($html);
  }
  
  // Landing page for 'Students->Collect Fees' 
  public function studentFeeDetails($stdId) {
    //Fetch the Student detail
    $stdObj = $this->fee_transaction_concorde->get_student_detail($stdId);
   
    if ($stdObj['fee_mode'] == 'auto') {
      $filter = $this->fee_library->construct_filter($stdObj);
    }elseif ($stdObj['fee_mode'] == 'manual') {
      $filter = 'student_id='.$stdId;
    }

    $fee_component = $this->fee_transaction_concorde->get_feemasterFilter($filter);
    if (empty($fee_component)) {
      if ($stdObj['fee_mode'] == 'manual') {
       $data['status'] = 'AmtNotAssign';
      }
    }
    //Fetch the Total Acad fees to be paid
    if (!empty($fee_component)) {
      $totalAcadFeeAmount = $fee_component[0]->total_fee;
    }

    //Fetch the Acad fees that he has already paid
    $acadFeeTrnsaction = $this->fee_transaction_concorde->get_academicFeeByStd($stdId);

    if (empty($acadFeeTrnsaction)) {
      $acadFeeTrnsaction = new stdClass();
      $acadFeeTrnsaction->paidStatus = 'Not Paid';
      $acadFeeTrnsaction->total_amount_paid = 0;
      $acadFeeTrnsaction->total_concession = 0;
    } else {
      $acadFeeTrnsaction->paidStatus = ($acadFeeTrnsaction->status == '1')?'Fully Paid':'Partially Paid';
      $acadFeeTrnsaction->total_amount_paid = $acadFeeTrnsaction->total_amount_paid;
      $acadFeeTrnsaction->total_concession = $acadFeeTrnsaction->total_concession;
    }
 
    //Get if Reconciliation Pending
    $lastFeeInstallment = $this->fee_transaction_concorde->getLastFeeInstallment($stdId);

    if (empty($lastFeeInstallment)) {
      $lastFeeInstallment = new stdClass();
      $lastFeeInstallment->reconStatus = 'NoRecon';
    } else {
      $lastFeeInstallment->reconStatus = ($lastFeeInstallment->reconciliation_status == 3)?'Failed':(($lastFeeInstallment->reconciliation_status == 1)?'Recon Pending':'NoRecon');
    }
    //echo "<pre>"; print_r($lastFeeInstallment->reconStatus); die();
    //Fetch the transportation fees that is already paid?
    $transporationFee = $this->fee_transaction_concorde->get_transportationFeeDetailsByStd($stdId);
    if (empty($transporationFee)) {
      $transporationFee = new stdClass();
      $transporationFee->verboseStatus = 'Fees Not Collected';
      $transporationFee->reconStatus = 'NoRecon';
    } else {
      $transporationFee->verboseStatus = ($transporationFee->status == '1' )?'Completed':'Partial';
      $transporationFee->reconStatus = ($transporationFee->reconciliation_status == '1')?'Recon Pending':'NoRecon';
     // $transporationFee->verboseStatus = 'Fees Not Collected';
    }
   
    $transporationFee->stdStop = $this->fee_transaction_concorde->get_stdStopDetailsByStd($stdId);
    
    //Construct the data to be sent to the view
    $acadFee = new stdClass();
    $acadFee->totalAcadFeeAmount = (empty($totalAcadFeeAmount)) ? 0 : $totalAcadFeeAmount;
    $acadFee->feeTransaction = $acadFeeTrnsaction;
    $acadFee->lastInstallment = $lastFeeInstallment;
    $data['acadFee'] = $acadFee;
    $data['transFee'] = $transporationFee;
    //TODO: Show a Misc fee collection option
    //$data['miscFee'] = $this->fee_transaction_concorde->get_miscellaneousFeeDetailsByStd($stdId);
    $data['stdObj'] = $stdObj;
    $feeConfig = $this->settings->getSetting('fees');
    $data['paymentType'] = $feeConfig['allowed_payment_modes'];
    $data['fee_type'] = array();
    foreach ($feeConfig['fee_type'] as $key => $val) {
      $data['fee_type'][$val['name']] = ($val['required'] == 1) ? 1 : 0;
    }
    $data['main_content'] = 'fees/concorde/student_details';
    $this->load->view('inc/template', $data);
  }

  // Student Transportation Stop Fee Amount details;
  private function _stdFeeStructreDetails($stopId,$usageMode){
    $sTransStrutre = $this->fee_transaction_concorde->get_transportationStrutureDetailsBystopWise($stopId,$usageMode);
    return $sTransStrutre;
  }
  
  // fee summary student wise fetch fee details
  public function select_fee($std_id){
    // get fee modes from config
    $feeConfig = $this->settings->getSetting('fees');
    $data['payment_modes'] = $feeConfig['allowed_payment_modes'];
    $data['alloc_type'] = $feeConfig['component_allocation_type_auto'];
    $data['discount_per'] = $feeConfig['discount'];
    $data['criteria'] = $feeConfig['filter_criteria'];
    
    // fetch data from selected student Id;
    $data['stdObj'] = $this->fee_transaction_concorde->get_student_detail($std_id);
    // student dataArry pass the  filter_criteria 
    //$filter = $this->fee_library->construct_filter($data['stdObj']);

    if ($data['stdObj']['fee_mode'] == 'auto') {
      $filter = $this->fee_library->construct_filter($data['stdObj']);
    }elseif ($data['stdObj']['fee_mode'] == 'manual') {
      $filter = 'student_id='.$std_id;
    }
    // fetch fees component table according to filter criteria
    $fee_component = $this->fee_transaction_concorde->get_feemasterFilter($filter);
  
    if (!empty($fee_component)) {
      $data['fee_master_id'] = $fee_component[0]->id;
      $data['fee_master_isFixed'] = $fee_component[0]->is_fixed;
      $data['noOfComponents'] = count($fee_component);
    }

    // get if already paid 
    $previous_paid = $this->fee_transaction_concorde->get_previousPaidBystdId($std_id);
    // get if cocession given 
    $previous_conc = $this->fee_transaction_concorde->get_previousConcessionBystdId($std_id);
    // get if currenct cocession 
    $currenct_conc = $this->fee_transaction_concorde->get_curretConcessionBystdId($std_id);
  
    //Preparing data for display purpose
    $mData=array();
    foreach ($fee_component as $key => $component) {
      $mData[$component->fcm_id] = $component;
    }

    $ppData=array();
    foreach ($previous_paid as $key => $paid) {
      $ppData[$paid->fee_component_id] = $paid;
    }
  
    $pcData=array();
    foreach ($previous_conc as $key => $pConc) {
      $pcData[$pConc->fee_component_id] = $pConc;
    }

    $ccData=array();
    foreach ($currenct_conc as $key => $cConc) {
      $ccData[$cConc->fee_component_id] = $cConc;
    }

    if (!empty($mData)) {
      foreach ($mData as $k => &$fc) {
        $fc = (object) array_merge((array)$fc, array('pAmount'=>isset($ppData[$k]->collected_amount) ? $ppData[$k]->collected_amount : '0' ));
        $fc = (object) array_merge((array)$fc, array('pConc'=> isset($pcData[$k]->receiveConcession) ? $pcData[$k]->receiveConcession : '0' ));
        $fc = (object) array_merge((array)$fc, array('cConc'=> isset($ccData[$k]->currentConcession) ? $ccData[$k]->currentConcession : '0' ));
      }
      $data['fee_component'] = $mData;
    }
    $data['main_content'] = 'fees/concorde/transaction/index';
    $this->load->view('inc/template', $data);
  }

  // submit fees
  public function submit_fee_transaction($std_id) {
    // echo "<pre>"; print_r($this->input->post()); die();
    $fee_master_id = $this->input->post('fee_master_id');

    //Get Payment modes and installment details from config
    $feeConfig = $this->settings->getSetting('fees');
    $payment_modes = $feeConfig['allowed_payment_modes'];
    $installment_template = $feeConfig['fee_installment'];
    $installment_status = $feeConfig['fee_installment_mode'];
    //$isApDiscount = $feeConfig['discount'];

    if ($this->input->post('final_amount')!='0') {
      //Step 1: Update the fee transcation table...
      $masterInsData = $this->__createFeeInstallmentJSON($installment_template,$installment_status);

      $installments_runData = $this->_installmentconsturct($masterInsData,$std_id);
      $this->db->trans_begin();
      $fee_transction = $this->fee_transaction_concorde->fee_transcation_total($fee_master_id,$std_id,$masterInsData,$installments_runData);
      if (empty($fee_transction)) {
        $this->db->trans_rollback();
        $this->session->set_flashdata('flashError', 'Something went wrong');
        redirect('fees/fee_concorde/select_fee/'.$std_id); 
      } 
        
        $fee_installment_id = $this->fee_transaction_concorde->fee_transcation_installment($std_id,$fee_master_id,$payment_modes,$installments_runData);
        if (empty($fee_installment_id)) {
          $this->db->trans_rollback();
          $this->session->set_flashdata('flashError', 'Something went wrong');
          redirect('fees/fee_concorde/select_fee/'.$std_id); 
        }else{
          //Update the fee Component tables...
          $fee_component_id = $this->fee_transaction_concorde->fee_transcation_components($fee_installment_id);
          if (empty($fee_component_id)) {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something went wrong');
            redirect('fees/fee_concorde/select_fee/'.$std_id); 
          }
         
            //Update the fee receipt
          $receipt_number = $this->_fee_receipt($fee_installment_id);
          $receipt_no = $this->fee_transaction_concorde->update_receipt_number($fee_installment_id,$receipt_number);
          if (empty($receipt_no)) {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something went wrong');
            redirect('fees/fee_concorde/select_fee/'.$std_id); 
          }

          $result = $this->db->trans_commit();

          if ($this->db->trans_status()) {
            redirect('fees/fee_concorde/print_receipt/'.$std_id.'/'.$receipt_number);
          } else {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something went wrong');
            redirect('fees/fee_concorde/select_fee/'.$std_id); 
          } 
        }
      } else {
        $this->session->set_flashdata('flashError', 'Please check. final amount');
        redirect('fees/fee_concorde/select_fee/'.$std_id);
      }
  } 

  private function __createFeeInstallmentJSON($installment_template,$installment_status){
     $totalFee_amount = $this->input->post('total_FeeAmount');  

     switch ($installment_status) {
       case 'NUMBER':
          $partsAmount = 0;
          foreach ($installment_template as $key => $parts) {
            $partsAmount += $parts['installment_per'];
            $rParts = $totalFee_amount - $partsAmount;
          }
          $installments = array();
          foreach ($installment_template as $key => $parts) {
            if ($parts['order'] == '1') {
              $installment_amt = $rParts;
            }else{
              $installment_amt = $parts['installment_per'];
            }
            $installments[] = array(
              'name'=> $parts['name'],
              'amount'=>$installment_amt,
              'due_date'=>$parts['due_date']
            ); 
          }
         break;
       case 'STANDARD':
          $installments = array();
          foreach ($installment_template as $key => $parts) {
            $installments[] = array(
              'name'=> $parts['name'],
              'amount'=>$parts['installment_per'] * $totalFee_amount/100,
              'due_date'=>$parts['due_date']
            ); 
        }
        break;
        case 'STANDARD_ROUND':
          $installments = array();
          foreach ($installment_template as $key => $parts) {
            $installments[] = array(
              'name'=> $parts['name'],
              'amount'=>round($parts['installment_per'] * $totalFee_amount/100),
              'due_date'=>$parts['due_date']
            ); 
        }
        break;
       default:
          $this->session->set_flashdata('flashError', 'Something went wrong');
          redirect('fees/fee_concorde/index');
         break;
     }

    return $installments;
  }
  
 
  private function _installmentconsturct($masterInsData,$stdId){
    //Step 1: Get the installment template from config and construct the master fee JSON.

    $pAmount = $this->input->post('paid_amount');
    $cConc =  $this->input->post('total_conc');
    //Step 2: Get the previous installments data; if empty, it is the first installmetnt
    $runInsData = $this->fee_transaction_concorde->get_prevousDatafeeTransaction($stdId);
    if (!empty($runInsData->installmentData)) {
      //This installment is *not* the first installment.

      //Step 3: Get the balance installment amount (using master data and run data).
      $temp = array();
      foreach ($masterInsData as $key => $part) {
        $rPart = new stdClass();
        $rPart->name = $part['name'];
        $rPart->amount = $part['amount'] - $runInsData->installmentData[$key]->amount - $runInsData->installmentData[$key]->concession ;
        $temp[] = $rPart;
      }

      //Step 4: Split the paid amount
      $pAmountSplit =array();
      $rAmount = $pAmount;
      foreach ($temp as $part) {
        $partAmount = $part->amount ;
        $rPart = new stdClass();
          if ($partAmount <= $rAmount) {
            $rPart->amount = $partAmount;
            $rPart->name = $part->name;
            $rAmount = $rAmount - $partAmount;
          } else {
            $rPart->amount = $rAmount;
            $rPart->name = $part->name;
            $rAmount = 0;
          }
        $pAmountSplit[] = $rPart;
      }

      //Step 5: Re-construct the run data with the paid amount split (so that we can split the concession)
       $tempData = array();
      foreach ($runInsData->installmentData as $key => &$data) {
        $part = new stdClass();
        $part->amount = $pAmountSplit[$key]->amount + $data->amount;
        $part->concession = $data->concession;
        $tempData[] = $part; 
      }

      //Step 6: Split the concession
      if (!empty($cConc)) {
        $rAmount = $cConc;
      }else{
        $rAmount =0;
      }
      foreach ($pAmountSplit  as $key => &$part) {
        $rRemPart = $masterInsData[$key]['amount'] - $tempData[$key]->amount - $tempData[$key]->concession;
        if ($rRemPart <= $rAmount) {
          $part->concession = $rRemPart;
          $rAmount = $rAmount - $rRemPart ;
        } else {
          $part->concession = $rAmount ;
          $rAmount = 0;
        }
      }
      $feeJSONConstruct = new stdClass();
      $feeJSONConstruct->installmentData = $pAmountSplit;
      //$feeJSONConstruct->totalConcession = $rAmount;
      return $feeJSONConstruct;
    }else{
      //This installment is the first installment

      //Step 3: Split the paid amount based on master data.
      $pAmountSplit = array();
      $rAmount = $pAmount;
      foreach ($masterInsData  as $part) {
        $rPart = new stdClass();
        if ($part['amount'] <= $rAmount) {
          $rPart->amount = $part['amount'];
          $rAmount = $rAmount - $part['amount'] ;
          $rPart->name = $part['name'];
        } else {
          $rPart->amount = $rAmount ;
          $rAmount = 0;
          $rPart->name = $part['name'];
        }
        $pAmountSplit[] = $rPart;
      }

      //Step 4: Split the concession based on master data and paid amount split data
      $rAmount = $cConc;
      foreach ($pAmountSplit  as $key => &$part) {
        //$rPart = new stdClass();
        $rRemPart = $masterInsData[$key]['amount'] - $part->amount;
        if ($rRemPart <= $rAmount) {
          $part->concession = $rRemPart;
          $rAmount = $rAmount - $rRemPart ;
        } else {
          $part->concession = $rAmount ;
          $rAmount = 0;
        }
      }

      //Step 5: Update the run data with the paid amount split and the total concession
      $feeJSONConstruct = new stdClass();
      $feeJSONConstruct->installmentData = $pAmountSplit;
      //$feeJSONConstruct->totalConcession = $cConc;
      return $feeJSONConstruct;
    }
  
  }
  // generate fee receipt number 
  public function _fee_receipt($fee_id){
    //Generate the Fee Receipt Number
    //Get config data
    $feeConfig = $this->settings->getSetting('fees');
    $receipt_number_gen = $feeConfig['recipt_number_gen'];

    $rTemplate = $feeConfig['receipt_template'];

    //get last id fee transcation installment generate receipt no
    switch ($receipt_number_gen['fee_generation_algo']) {

      case 'WPL':
        $receipt_number = $receipt_number_gen['infix'].sprintf("%'.0".$receipt_number_gen['digit_count']."d\n", $fee_id);
        break;
      case 'NHS':
        $receipt_number = $receipt_number_gen['infix'].sprintf("%'.0".$receipt_number_gen['digit_count']."d\n", $fee_id);
        break;
      case 'concorde':
        $receipt_number = $receipt_number_gen['infix'].sprintf("%'.0".$receipt_number_gen['digit_count']."d\n", $fee_id);
        break;
      case 'RISEHIGH':
        $receipt_number = $receipt_number_gen['infix'].sprintf("%'.0".$receipt_number_gen['digit_count']."d\n", $fee_id);
        break;
      default:
        $receipt_number = $fee_id;
        break;
    }
    return $receipt_number;
  }
  // print receipt // history report same 
 public function print_receipt($stdId,$receipt_number,$traverse_to = ""){
    $data['traverse_to'] = $traverse_to;
    $feeConfig = $this->settings->getSetting('fees');
    $rTemplate = $feeConfig['receipt_template'];
    $data['paymentType'] = $feeConfig['allowed_payment_modes'];
    $data['installment_template'] = $feeConfig['fee_installment'];
    $data['fee_details']= $this->fee_transaction_concorde->get_feedetailsbyfee_id($receipt_number); 
    //echo "<pre>"; print_r($data['fee_details']); die();
    $data['duelistData']= $this->fee_transaction_concorde->get_installmentTemplate($stdId);
    $data['selectedstdId'] = $stdId;
    //echo "<pre>"; print_r($data['fee_details']); die();
    // $this->load->library('pdfmanager');
    // $file = $this->pdfmanager->generatePdf($data,'exportPdf');
    // $this->load->library('filemanager');
    // $this->filemanager->uploadFile($file['path'],$file['filename']); 

    // $file_path = $this->filemanager->getFilePath($file['filename']); 
    // $this->fee_transaction_concorde->update_pdfpath_url($fee_id,$file_path);
   
    // $this->session->set_flashdata('flashSuccess', 'Fee Successfully added');
    $data['main_content'] = 'receipts/'.$rTemplate;
    $this->load->view('inc/template', $data);
  }


  // History view all Academic fee , transportation, miscellaneous 
  public function history_concorde($stdId,$traverse_to){
    $data['traverse_to'] = $traverse_to;
    //$data['selectedClassId'] = $classId;
    $data['stdId'] = $stdId;
    $feeConfig = $this->settings->getSetting('fees');
    $data['paymentType'] = $feeConfig['allowed_payment_modes'];
    $data['payment_history'] = $this->fee_transaction_concorde->getpaymenthistorbystudentwise($stdId);
    //echo "<pre>"; print_r($data['payment_history']); die();
    // Northhill only if fee full payment receipt generation;
    $feeConfig = $this->settings->getSetting('fees');
    $rTemplate = $feeConfig['receipt_template'];

    if ($rTemplate == 'nhs' && !empty(($data['payment_history']['fee_transaction_installment']))) {
      foreach ($data['payment_history']['fee_transaction_installment'] as $key => $installment) {
        if ($installment->reconciliation_status != 3) {
          if ($installment->status == 1) {
            $data['rFull'] ='show';
          }
        }
      }
    }
    $data['main_content'] = 'fees/concorde/history';
    $this->load->view('inc/template', $data);
  }

  public function fullprint_receipt($stdId,$traverse_to = ""){
    $data['traverse_to'] = $traverse_to;
    $feeConfig = $this->settings->getSetting('fees');
    $rTemplate = $feeConfig['full_receipt_template'];
    $data['paymentType'] = $feeConfig['allowed_payment_modes'];   
    $data['full_fee_details']= $this->fee_transaction_concorde->get_feeFull_detailsbystdId($stdId); 
    //echo "<pre>"; print_r($data['full_fee_details']); die();
    $data['duelistData']= $this->fee_transaction_concorde->get_installmentTemplate($stdId);
    $data['selectedstdId'] = $stdId;
    //echo "<pre>"; print_r($data['fee_details']); die();

    $data['main_content'] = 'receipts/'.$rTemplate;
    $this->load->view('inc/template', $data);
  }
  // Concession related set authorized 
  public function concession($std_id,$traverse_to){
    $feeConfig = $this->settings->getSetting('fees');
    $data['alloc_typeforConce'] = $feeConfig['cComponent_allocation_type_auto'];
    $data['discount_per'] = $feeConfig['discount'];
    $data['traverse_to'] = $traverse_to;
    $data['selectedStd'] = $std_id;
     // fetch data from selected student Id;
    $data['std_details'] = $this->fee_transaction_concorde->get_student_detail($std_id);
    
    if ($data['std_details']['fee_mode'] == 'auto') {
      $filter = $this->fee_library->construct_filter($data['std_details']);
    }elseif ($data['std_details']['fee_mode'] == 'manual') {
      $filter = 'student_id='.$std_id;
    }
 
    // fetch fees master table according to filter criteria
    $fee_component = $this->fee_transaction_concorde->get_feemasterFilter($filter);
     if (!empty($fee_component)) {
     $data['fee_master_id'] = $fee_component[0]->id;
     $data['fee_master_isFixed'] = $fee_component[0]->is_fixed;
     $data['noOfComponents'] = count($fee_component);
    }
    if (!empty($fee_component)) {
      // get if already paid 
      $previous_paid = $this->fee_transaction_concorde->get_previousPaidBystdId($std_id);
      
      // get if cocession given 
      $previous_conc = $this->fee_transaction_concorde->get_previousConcessionBystdId($std_id);
      
      // get if currenct cocession 
      $currenct_conc = $this->fee_transaction_concorde->get_curretConcessionBystdId($std_id);
      
      foreach ($fee_component as $key => $component) {
        $mData[$component->fcm_id] = $component;
      }

      $ppData=array();
      foreach ($previous_paid as $key => $paid) {
        $ppData[$paid->fee_component_id] = $paid;
      }
    
      $pcData=array();
      foreach ($previous_conc as $key => $pConc) {
        $pcData[$pConc->fee_component_id] = $pConc;
      }

      $ccData=array();
      foreach ($currenct_conc as $key => $cConc) {
        $ccData[$cConc->fee_component_id] = $cConc;
      }

      foreach ($mData as $k => &$fc) {
        $fc = (object) array_merge((array)$fc, array('pAmount'=>isset($ppData[$k]->collected_amount) ? $ppData[$k]->collected_amount : null ));
        $fc = (object) array_merge((array)$fc, array('pConc'=> isset($pcData[$k]->receiveConcession) ? $pcData[$k]->receiveConcession : null ));
        $fc = (object) array_merge((array)$fc, array('cConc'=> isset($ccData[$k]->currentConcession) ? $ccData[$k]->currentConcession : null ));
      }
      $data['fee_component'] = $mData;
      $data['main_content'] = 'fees/concorde/fee_concession/index';
      $this->load->view('inc/template', $data);
    } else {
      //We shouldn't reach this point as the structure should take care of all combinations. If we reach, this is a bug!!
      $this->session->set_flashdata('flashInfo', 'No matching Fee Structure exists for this student. Please contact your administrator with the Student Admission number');
      redirect('fees/fee_concorde');
    }
  }

  public function submit_fee_concession($std_id, $fee_master_id){
    $traverse_to = $this->input->post('traverse_to');
    
    $result = $this->fee_transaction_concorde->insert_concession($std_id, $fee_master_id);
    if($result){ 
      $this->session->set_flashdata('flashSuccess', 'Concession Applied Successfully');
    }
    else{
      $this->session->set_flashdata('flashError', 'No changes');
    }
    //Navigate back to the appropriate page
    if ($traverse_to == '0') {
      redirect('fees/fee_concorde/studentFeeDetails/'.$std_id);
    }else{
      redirect('fees/fee_concorde/select_fee/'.$std_id);
    }
  }

  // Reconsilation Fee 
  public function reconsilation_fee() {
   $rec_paid = $this->fee_transaction_concorde->update_recon_status_as_done();
   if ($rec_paid) {
     // echo "1";
      $this->session->set_flashdata('flashSuccess', 'Reconcilliation successfully');
    }else{
      // echo "2";
      $this->session->set_flashdata('flashError', 'Something went wrong');
    } 
  }

   // Reconsilation Faild Fee 
  public function reconsilation_Faildfee(){
    $feeNumber = $this->input->post('feeNumber');
    $stdId = $this->input->post('stdId');
    $result = $this->fee_transaction_concorde->update_recon_status_as_faild($feeNumber,$stdId);
    if ($result) {
      echo "1";
    }else{
      echo "0";
    }
  }


   //soft delete standard
  public function soft_delete(){
    $feeNumber = $this->input->post('feeNumber');
    $stdId = $this->input->post('stdId');
    $result = $this->fee_transaction_concorde->soft_deleteStandard($feeNumber,$stdId);
    if ($result) {
      echo "1";
    }else{
      echo "0";
    }
  }

  // public function reconsilation_Faildfee() {
  //  $rec_paid = $this->fee_transaction_concorde->update_recon_status_as_faild();
  //  if ($rec_paid) {
  //    // echo "1";
  //     $this->session->set_flashdata('flashWarning', 'Reconcilliation Faild successfully');
  //   }else{
  //     // echo "2";
  //     $this->session->set_flashdata('flashError', 'Something went wrong');
  //   } 
  // }

  // Reconsilation Fee 
  public function reconsilation_TransportationFee() {
   $rec_paid = $this->fee_transaction_concorde->update_recon_status_asTransport_done();
   if ($rec_paid) {
     // echo "1";
      $this->session->set_flashdata('flashSuccess', 'Reconcilliation successfully');
    }else{
      // echo "2";
      $this->session->set_flashdata('flashError', 'Something went wrong');
    } 
  }

  // Transportation
   public function feeTransportation($std_id){
      $data['trans_fees'] = $this->fee_transportation_model->get_transportationStdDetails($std_id);
      if ($data['trans_fees'] =='feeStruture') {
        $this->session->set_flashdata('flashInfo', 'Fee Structure not found');
        redirect('fees/fee_concorde/studentFeeDetails/'.$std_id);
      }
      if (empty($data['trans_fees']->pAmount) && empty($data['trans_fees']->concession) && empty($data['trans_fees']->total_amount)) {
          $pAmount = 0;
          $total_concession = 0;
          $tAmount = 0;
      }else{
        $pAmount = $data['trans_fees']->pAmount;
        $total_concession = $data['trans_fees']->concession;
        $tAmount = $data['trans_fees']->total_amount;
      }
      $data['amountPaid'] = $tAmount - $pAmount - $total_concession;
      $feeConfig = $this->settings->getSetting('fees');
      $data['payment_modes'] = $feeConfig['allowed_payment_modes'];
      $data['isGPRS'] = $feeConfig['transport_gprs'];
      $data['isConcession'] = $feeConfig['transport_concession'];
      $data['stdId'] = $std_id;
      $data['main_content'] = 'fees/concorde/transportation/index';
      $this->load->view('inc/template', $data); 
  }

  public function submit_transportation_transaction($std_id){
    $pAmount =  $this->input->post('amount');
    $previousCalucate =  $this->input->post('trans_amount') - $this->input->post('preAmount');
    $feeConfig = $this->settings->getSetting('fees');
    $payment_modes = $feeConfig['allowed_payment_modes'];
    if($pAmount <= $previousCalucate) {
      $receiptNo = $this->fee_transportation_model->fee_transportation_receiptNo();
      //echo "<pre>"; print_r($receiptNo); die();
      $result = $this->fee_transportation_model->insert_fee_transportation($std_id,$receiptNo,$payment_modes);
      if($result){
        //$this->session->set_flashdata('flashSuccess', 'Successfully inserted');
        redirect('fees/fee_concorde/fee_receipt/'.$receiptNo.'/'.'1'.'/'.$std_id);
      }
    }else{
      $this->session->set_flashdata('flashError', 'Please check enter amount');
      redirect('fees/fee_concorde/feeTransportation/'.$std_id);
    } 
  
  }

  public function fee_receipt($receiptNo,$traverse_to,$std_id){
    $data['traverse_to'] = $traverse_to;
    $data['std_id'] = $std_id;
    $feeConfig = $this->settings->getSetting('fees');
    $rTemplate = $feeConfig['receipt_template_trans'];
    $data['paymentType'] = $feeConfig['allowed_payment_modes'];
    $data['trans_details'] = $this->fee_transportation_model->get_transportationFees($receiptNo);
    //echo "<pre>"; print_r($data['trans_details']); die();
    $data['total_paid'] = $this->fee_transportation_model->get_transportationFeestotalAmount($std_id);
   // TODO PDF Created
    // $this->load->library('pdfmanager');
    // $file = $this->pdfmanager->generatePdf($data,'exportPdf');
    
    // $this->load->library('filemanager');
    // $this->filemanager->uploadFile($file['path'],$file['filename']); 

    // $file_path = $this->filemanager->getFilePath($file['filename']); 
    // $this->fee_transaction_two->update_pdfpath_url($fee_id,$file_path);
    $data['main_content'] = 'receipts/'.$rTemplate;
    $this->load->view('inc/template', $data);
  }


  // concorde miscellaneous
  public function feeMiscellaneous($std_id){

    $data['variants'] = $this->fee_transaction_misc->get_varients_names();
    $feeConfig = $this->settings->getSetting('fees');
    $data['payment_modes'] = $feeConfig['allowed_payment_modes'];
    // fetch data from selected student Id;
    $data['std_details'] = $this->fee_transaction_misc->get_student_detail($std_id);
    $data['main_content'] = 'fees/concorde/miscellaneous/index';
    $this->load->view('inc/template', $data);
  }

  public function fetch_jsonformatVariants(){
    $result = $this->fee_transaction_misc->get_varients_names();
    echo json_encode($result);
  }
  public function serachitemsIdwiseOption(){
    $itemsId =  $this->input->post('itemsId');
    $stdId =  $this->input->post('stdId');
    $clasId =  $this->input->post('clasId');
    $result = $this->fee_transaction_misc->get_itemsOptionbyItmesId($itemsId,$stdId,$clasId);
    echo json_encode($result);
  }
  public function serachItmesOptionwiseAmount(){
    $optionItmesId =  $this->input->post('optionItmesId');
    $result = $this->fee_transaction_misc->get_amountByoptiontimesId($optionItmesId);
    echo json_encode($result);
  }

  public function submit_miscellaneous_transaction($std_id){ 
    $final_amount = $this->input->post('final_amount');
    if ($final_amount != 0) {
      $receiptNo = $this->fee_transaction_misc->fee_variants_receiptNo();
      $result = $this->fee_transaction_misc->insert_variants($std_id,$receiptNo);
      if($result){ 
        redirect('fees/fee_concorde/fee_receipt_misc/'.$receiptNo.'/'.$std_id);
        $this->session->set_flashdata('flashSuccess', 'Successfully inserted');
      }
      else{
        $this->session->set_flashdata('flashError', 'No changes');
      }
      redirect('fees/fee_misc');
    }else{
      $this->session->set_flashdata('flashError', 'Check the final amount before submit');
      redirect('fees/fee_concorde/feeMiscellaneous/'.$std_id.'/'.$classId);
    }
  }

  public function fee_receipt_misc($receiptNo,$std_id,$traverse_to=""){
    $data['traverse_to'] = $traverse_to;
    $data['std_id'] = $std_id;
    $feeConfig = $this->settings->getSetting('fees');
    $rTemplate = $feeConfig['receipt_template_misc'];
    $data['paymentType'] = $feeConfig['allowed_payment_modes'];
    $data['misc_details'] = $this->fee_transaction_misc->get_misecellaneousFees($receiptNo);
   // TODO PDF Created
    // $this->load->library('pdfmanager');
    // $file = $this->pdfmanager->generatePdf($data,'exportPdf');
    
    // $this->load->library('filemanager');
    // $this->filemanager->uploadFile($file['path'],$file['filename']); 

    // $file_path = $this->filemanager->getFilePath($file['filename']); 
    // $this->fee_transaction_two->update_pdfpath_url($fee_id,$file_path);

    $data['main_content'] = 'receipts/'.$rTemplate;
    $this->load->view('inc/template', $data);
  }

  public function assignAmountforIndividualStd($stdId){
  //Get the filter criteria for the school and populate the data
    $data['stdObj'] = $this->fee_transaction_concorde->get_student_detail($stdId);
    $filter = 'student_id='.$stdId;
    $data['fee_component'] = $this->fee_transaction_concorde->get_feemasterFilter($filter);
    //echo "<pre>"; print_r($data['fee_component']); die();
    //Get the fee components and prepare the data
    $data['fcomponentsArr'] = $this->settings->getSetting('fees')['components'];
    $data['main_content'] = 'fees/concorde/individual_amt';
    $this->load->view('inc/template', $data);
  }

  public function submit_FeeindividualStd($stdId){
    $result = $this->fee_transaction_concorde->insertFeeStructureIndividualStd($stdId);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Submit Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('fees/fee_concorde/studentFeeDetails/'.$stdId);
  }

  public function update_FeeindividualStd($stdId){
    $result = $this->fee_transaction_concorde->updateFeeStructureIndividualStd($stdId);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Submit Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('fees/fee_concorde/studentFeeDetails/'.$stdId);
  }

}

