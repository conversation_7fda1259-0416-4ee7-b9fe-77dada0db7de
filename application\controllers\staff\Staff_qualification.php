<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  16 July 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Staff_qualification extends CI_Controller {

    public function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
        }

        if (!$this->authorization->isAuthorized('STAFF.VIEW_QUALIFICATION')) { 
            redirect('dashboard', 'refresh');
        }
        
        $this->load->model('staff/staff_qualification_model');
    }

    public function index(){
        $staff = $this->staffcache->getStaffCache();
        $data['staffId'] = $staff->staffId;
        $data['staff_ql_data'] = $this->staff_qualification_model->get_all_qualifiationbyStaffwise($staff->staffId);
        $data['main_content'] = 'staff/staff_qualification/index';
        $data['permitQualEdit'] = $this->settings->getSetting('staff_profile_enableQualificationEdit');
        $this->load->view('inc/template', $data);
    }

    public function add($staffId){
        $data['staffId'] = $staffId;
        $data['main_content'] = 'staff/staff_qualification/add';
        $this->load->view('inc/template', $data);
    }

    public function edit($id,$staffId){
        $data['staffId'] = $staffId;
        $data['edit_qual'] = $this->staff_qualification_model->edit_qualification_byStaff($id,$staffId);
        $data['main_content'] = 'staff/staff_qualification/edit';
        $this->load->view('inc/template', $data);
    }

    public function insert_qualification($staffId){
        $result = $this->staff_qualification_model->insert_qualification_data($staffId);
        if ($result) {
           $this->session->set_flashdata('flashSuccess', 'Qualification Successfully Inserted.');
        }else{
            $this->session->set_flashdata('flashError', 'Unable to Insert Qualification Details.');
        }
        redirect('staff/staff_qualification');
    }

    public function update_qualification($id,$staffId){
        $result = $this->staff_qualification_model->update_qualification_data($id,$staffId);
        if ($result) {
           $this->session->set_flashdata('flashSuccess', 'Qualification Successfully Inserted.');
        }else{
            $this->session->set_flashdata('flashError', 'Unable to Insert Qualification Details.');
        }
        redirect('staff/staff_qualification');
    }

    public function delete_qualification($id,$staffId){
        $result = $this->staff_qualification_model->delete_qualification_data($id,$staffId);
        if ($result) {
           $this->session->set_flashdata('flashSuccess', 'Qualification Successfully Deleted.');
        }else{
            $this->session->set_flashdata('flashError', 'Something went wrong');
        }
        redirect('staff/staff_qualification');
    }
}