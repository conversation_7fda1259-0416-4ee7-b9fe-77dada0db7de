<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of index
 *
 * <AUTHOR>
 */
class feedback_controller Extends CI_Controller {

    function __construct() {
        parent::__construct();
        $this->load->model('student_feedback/Feedback_student');
    }

    public function index() {
         $data['classList'] = $this->Feedback_student->getClassList();
           $data['staffListInfo'] = $this->Feedback_student->staffListData();
        $data['questionList'] = $this->Feedback_student->QuestionInfo();
        $data['main_content'] = 'student_feedback/index';
        $this->load->view('inc/template', $data);
    }

    public function questionsAddPage() {
     $data['main_content'] = 'student_feedback/add';
        $this->load->view('inc/template', $data);
    }
 public function staffData() {
           $data['staffNameList'] = $this->Feedback_student->staffList();
        $data['main_content'] = 'student_feedback/addstaff';
        $this->load->view('inc/template', $data);
    }
    
    public function surveyPage() {
         $data['surveyPageListResult'] = $this->Feedback_student->surveyPageListInfo(); 
      $data['main_content'] = 'student_feedback/Survey';
        $this->load->view('inc/template', $data);  
    }   
    public function designSurveyPage() {
        $data['designSurveyPageListResult'] = $this->Feedback_student->designSurveyPageList(); 
        
        
          $data['staffNameList'] = $this->Feedback_student->staffList();
          $data['QuestionList'] = $this->Feedback_student->QuestionList();
       $data['main_content'] = 'student_feedback/desginSurvey';
        $this->load->view('inc/template', $data);  
    }
    public function addStaff() {
          $addStaffName = $this->Feedback_student->addStaffList();
          if ($addStaffName) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
           
        } else {

            $this->session->set_flashdata('flashSuccess', 'Failed To Insert .');
           
        }
         redirect('student_feedback/feedback_controller/index');
    }
    public function addQuestions() {

        $add_Result = $this->Feedback_student->addQuestion();
        if ($add_Result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
           
        } else {

            $this->session->set_flashdata('flashSuccess', 'Failed To Insert .');
           
        }
         redirect('student_feedback/feedback_controller/designSurveyPage');
    }

    public function deletequestion($id) {
        $del_Result = $this->Feedback_student->deleteQuestion($id);
        if ($del_Result) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Deleted.');
         
        } else {

            $this->session->set_flashdata('flashSuccess', 'Something went wrong .');
           
        }
         redirect('student_feedback/feedback_controller/index');
    }
      public function getClassesSections() {

        if (isset($_POST['classid'])) {
            $classid = $_POST['classid'];
            $getclassectioninfo = $this->Feedback_student->getsectionList($classid);
            echo json_encode($getclassectioninfo);
        }
    }
     
    
   
    public function addsurveyPage() {
      $surveyQuestions = $this->Feedback_student->addSurveyPageQuestions();
       if ($surveyQuestions) {
            $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
          
        } else {

            $this->session->set_flashdata('flashSuccess', 'Something went wrong .');
           
        }
          redirect('student_feedback/feedback_controller/surveyPage');
    }   
    
  
}
