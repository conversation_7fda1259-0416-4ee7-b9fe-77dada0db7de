<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Studentleave_controller
 *
 * <AUTHOR>
 */
class Studentapplyleave_admin_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->load->model('student/Student_leave_model');
    }

    public function index() {

        $data['getFullStudentDetail'] = $this->Student_leave_model->getStudentLeaveInfoAll();
        //  echo "<pre>";print_r($data['getFullStudentDetail']);die();
        $data['main_content'] = 'leave_approval/edit_studentleaveapporval';
        $this->load->view('inc/template', $data);
    }


    public function updateleave($stuid, $rowid) {

        $data['getupdatedInfo'] = $this->Student_leave_model->getStudentLeavebyId($stuid, $rowid);
//echo "<pre>";print_r($data['getFulldatanew']);die();
        $data['main_content'] = 'leave_approval/getapporval_info';
        $this->load->view('inc/template', $data);
    }

    public function update_leaveapproval_application($rowid, $sudent_id) {
        $updateFulldata = $this->Student_leave_model->updateleave($rowid, $sudent_id);
        if ($updateFulldata) {
            $this->session->set_flashdata('flashSuccess', 'Leave Updated Successfully');
            redirect('student/Studentapplyleave_admin_controller/index');
        } else {
            $this->session->set_flashdata('flashSuccess', 'Leave Not updated  ');
            redirect('student/Studentapplyleave_controller/index');
        }
    }

}
