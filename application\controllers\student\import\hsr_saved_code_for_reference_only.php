<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Student_controller
 *
 * <AUTHOR>
 */
class north_import_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
      //This code is just for reference. Hence redirecting to auth/login if somebody tries to get in unintentionally.
        redirect('auth/login', 'refresh');
    }   
   private function __marshalClassId($id, $iClassName) {

    //Promote him
    switch ($iClassName) {
      case 'Class 1':
        $pClassName = '1';
        break;
      case 'Class 2':
        $pClassName = '2';
        break;
      case 'Class 3':
        $pClassName = '3';
        break;
      case 'Class 4':
        $pClassName = '4';
        break;
      case 'Class 5':
        $pClassName = '5';
        break;
      case 'Class 6':
        $pClassName = '6';
        break;
      case 'Class 7':
        $pClassName = '7';
        break;
      case 'Class 8':
        $pClassName = '8';
        break;
      case 'Class 9':
        $pClassName = '9';
        break;
        case 'Class 10':
        $pClassName = '10';
        break;
      case 'Class 11':
        $pClassName = '11';
        break;
      case 'Class 12':
        $pClassName = '12';
        return 0;
        break;
      default:
        echo 'Problem found in Class; ';
        echo 'ID : ' . $id . '; Input value: ' . $iClassName;die();
        break;
    }
  
    //Add class Id and also promote them!!
    $classes = $this->db->select('id, class_name')
      ->from('class')
      ->get()->result();
  
    $found = 0;
    foreach ($classes as $class) {
      if ($class->class_name == $pClassName) {
        $found = $class->id;
      }
    }

    if ($found==0) {
      echo 'Problem found in Class; ';
      echo 'ID : ' . $id . '; Input value: ' . $iClassName;die();
    }
    return $found; //Return the promoted class Id.
  }

   private function __marshalSectionId($iClassId,$section_name) {
    $result = $this->db->select('id')
      ->from('class_section')
      ->where('class_id', $iClassId)
      ->where('section_name', $section_name)
      ->get()->row();
    
    return $result->id; 
  }

    private function __marshalGender($id, $gender) {
      if ($gender == 'Male') return 'M';
      if ($gender == 'Female') return 'F';
    }


  //submit student data from a excel import
  public function importStudentData () {
    $fromId = $this->input->post('from_id');
    $toId = $this->input->post('to_id');

    for ($id = $fromId; $id <= $toId; $id++) {
      $dbData = $this->db->select('*')->from('hsr_student_raw_data_2018_19')->where('id',$id)->get()->row();
      if (empty($dbData)) continue;
      //Marshal data
      $result = new stdClass();
      //Student Table
      $result->roll_num = $dbData->roll_no;
      $result->admission_no = $dbData->admission_no;
      $result->student_firstname = $dbData->student_firstname;
      $result->student_lastname = ''; 
      $result->dob = $dbData->dob;
      $result->gender = $this->__marshalGender($id, $dbData->gender);
      $newClassId = $this->__marshalClassId($id, $dbData->term_name);
      $newSectionId = $this->__marshalSectionId($newClassId, $dbData->section_name);
      //Marshal class Id
      if ($newClassId == 0) {
        $isAlumni = 1;
      } else {
        $isAlumni = 0;
      }
      $result->class_id = $newClassId;
      $result->class_section_id = $newSectionId;
      //Admission Status
      if ($isAlumni) {
        $result->add_status = 4;
      } else {
        $result->add_status = 2;
      }
      
      $result->nationality = ucwords(strtolower($dbData->nationality));
      $result->religion = ucwords(strtolower($dbData->religion));
      $result->category = '';
      $result->mother_tongue=$dbData->mother_tongue;
      $result->boardingid = '1';
      $result->contact_no = $dbData->student_contact_no;
      $result->student_doj = $dbData->date_of_admission;
      $result->caste = ucwords(strtolower($dbData->caste));
      $result->std_aadhar = $dbData->aadhaar_no;
      $result->class_admitted_to = $dbData->class_joined;
      $result->student_house = $dbData->student_house;
      $result->fee_mode = 'auto';
      $result->language_1_in_primary = '';
      $result->language_2_in_primary = $dbData->language_2_in_primary;
      $result->language_3_in_primary = $dbData->language_3_in_primary;
      $result->blood_group = $dbData->blood_group;
      $result->medid = '1';
      $result->board = '2'; 
      $result->s_email = $dbData->student_email;
      $result->haveSibling=-1;
      $result->rteid='';
      $result->birth_taluk='';
      $result->birth_district='';
      $result->admidType='1';

      // Parent Table

      $result->f_userid = '';
      $result->m_userid = '';
      $result->f_first_name =  $dbData->f_first_name;
      $result->m_first_name =  $dbData->m_first_name;
      $result->f_email =  $dbData->f_email;
      $result->m_email =  $dbData->m_email;
      $result->f_mobile_no =  $dbData->f_mobile_no;
      $result->m_mobile_no =  $dbData->m_mobile_no;
      $result->f_mother_tongue =  $dbData->f_mother_tongue;
      $result->m_mother_tongue =  $dbData->m_mother_tongue;
      $result->f_residential_address =  $dbData->f_residential_address;
      $result->m_residential_address =  $dbData->m_residential_address;

      $result->f_home_pin =  $dbData->f_home_pin;
      $result->f_home_phone =  $dbData->f_home_phone;
      $result->f_profession =  $dbData->f_profession;
      $result->f_designation =  $dbData->f_designation;
      $result->f_company_name_address =  $dbData->f_company_name_address;
      $result->f_company_address =  $dbData->f_company_address;
      $result->f_office_phone_number =  $dbData->f_office_phone_number;
      $result->m_home_phone =  $dbData->m_home_phone;
      $result->m_designation =  $dbData->m_designation;
      $result->m_profession =  $dbData->m_profession;
      $result->m_company_name_address =  $dbData->m_company_name_address;
      $result->m_company_address =  $dbData->m_company_address;
      $result->m_office_phone_number =  $dbData->m_office_phone_number;
      $result->family_doctor_name =  $dbData->family_doctor_name;
      $result->family_doctor_mobile_number =  $dbData->family_doctor_mobile_number;
      $result->is_physically_challenged =  $dbData->is_physically_challenged;

      $result->f_last_name = '';
      $result->m_last_name = '';

      $result->classsection = '';
      $result->f_aadhar='';
      $result->m_aadhar='';
      $result->m_annual_income='';
      $result->donor_name='';
      $result->f_qualification='';
      $result->f_occupation='';
      $result->f_annual_income='';
      echo "<pre>"; print_r($result);die();
      // $this->__submitStudent($result, 'import', 1);
    }

    redirect('student/Student_controller');
    //echo 'All Good!!';die();
  }

}
