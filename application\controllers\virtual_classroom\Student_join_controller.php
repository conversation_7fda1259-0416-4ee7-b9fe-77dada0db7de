<?php
  /**
   * Name:    Oxygen
   * Author:  <PERSON>
   *          <EMAIL>
   *
   * Created:  17 May 2018
   *
   * Description:  
   *
   * Requirements: PHP5 or above
   *
   */

  defined('BASEPATH') OR exit('No direct script access allowed');

  /**
   * Class Fee
   * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
   * @property CI_Form_validation      $form_validation The form validation library
   */
  class Student_join_controller extends CI_Controller {

    private $year_id;

    public function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
        redirect('dashboard', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      $this->load->model('parent_model');
      $this->load->model('virtual_classroom/Virtual_classroom_model','vcm');
      $this->load->library('openvidu');
      $this->load->library('filemanager');
    }

    public function index(){
      $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
      $std_data = $this->parent_model->getStudentDataById($studentId);
      
      // $data['schedule_list'] = $this->vcm->get_schedule_data_student($studentId, $std_data->sectionId);

    if ($this->mobile_detect->isMobile()) {
      $data['main_content']    = 'virtual_classroom/parent_desktop/mobile_index.php';
    } else {
      $data['main_content']    = 'virtual_classroom/parent_desktop/index.php';
    }

      $this->load->view('inc/template', $data);  
    }

    public function join_window() {
      $data['schedule_id'] = $_POST['schedule_id'];
      // echo "<pre>"; print_r($data['schedule_id']); die();
      $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
      $data['schedule_room'] = $this->vcm->getRoomByScheduleId($data['schedule_id']);
      $data['student'] = $this->vcm->getStudentData($studentId);
      $data['student']->webcam_avatar = ($data['student']->webcam_avatar)?$this->filemanager->getFilePath($data['student']->webcam_avatar):'';
      if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'virtual_classroom/join_class/mobile_student_join_window.php';
      } else {
        $data['main_content']    = 'virtual_classroom/join_class/student_join_window.php';
      }
      $this->load->view('inc/template', $data);  
    }

    public function getSchedules() {
      $type = $_POST['type'];
      $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
      $schedules = $schedules = $this->vcm->getStudentSchedules($studentId, $type);
      $data = array();
      $now = date('Y-m-d H:i:s');
      foreach ($schedules as $key => $sch) {
        $schedules[$key]->session_status = 'Not Started';
        if (strtotime($sch->end_time)<strtotime($now)) {
          $schedules[$key]->session_status = 'Completed';
        } else if((strtotime($sch->start_time)<strtotime($now)) && (strtotime($sch->end_time)>strtotime($now))) {
          $schedules[$key]->session_status = 'Active';
        }
        $sch->date = date('d-M Y', strtotime($sch->start_time));
        $sch->start_time = date('h:i a', strtotime($sch->start_time));
        $sch->end_time = date('h:i a', strtotime($sch->end_time));
        $data[$sch->id] = $sch;
      }
      echo json_encode($data);
    }

    private function __checkStudentInSession($session_id, $studentId) {
      $session_data = $this->openvidu->get_session_details($session_id);
      $session = json_decode($session_data);
      if(!empty($session)) {
        $connections = $session->connections->content;
        foreach ($connections as $key => $con) {
          $c_data = json_decode($con->clientData);
          if($c_data->role == 'student') {
            list($role, $student_id) = explode("_", $c_data->clientId);
            if($studentId == $student_id) {
              return 1;
            }
          }
        }
      }
      return 0;
      // echo 'Data: <pre>'; print_r($session->connections); die();
    }

    public function join_class() {
      $data['schedule_id'] = $_POST['schedule_id'];
      $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
      $std_data = $this->parent_model->getStudentDataById($studentId);
      $data['student_name'] = $std_data->stdName;
      $data['schedule_id'] = $data['schedule_id'];

      $data['schedule_details'] = $this->vcm->get_schedule_detail($data['schedule_id']);
      // $stduent_in_session = 0 ;
      $stduent_in_session = $this->__checkStudentInSession($data['schedule_details']->openvidu_session_id, $studentId);
      //check is student already joined to session
      if($stduent_in_session) {
        $data['main_content']    = 'virtual_classroom/parent_desktop/duplicate_entry.php';
      } else {
        $video_session = $this->openvidu->create_session($data['schedule_details']->openvidu_session_id);

        $data['client_id'] = 'student_' . $studentId;

        $data['student_list'] = $this->vcm->get_invitee_list($data['schedule_details']->id, '');

        $video_session = $this->openvidu->create_session($data['schedule_details']->openvidu_session_id);
        $data['video_session_id'] = $video_session->session_id;

        if ($this->mobile_detect->isMobile()) {
          $data['main_content']    = 'virtual_classroom/parent_desktop/video/mobile/mobile_index.php';
        } else {
          $data['main_content']    = 'virtual_classroom/parent_desktop/video/desktop/index.php';
        }
      }
      $this->load->view('inc/template_virtualClass', $data);
    }

    //Creates a new OpenVidu session
    public function create_token () {
      $session_id = $_POST['session_id'];
      echo $this->openvidu->create_token($session_id, 100, 100);
    }

    public function save_answer() {
      $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
      $status = $this->vcm->saveStudentAnswer($student_id);
      echo $status;
    }

    public function get_questions() {
      $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
      $schedule_id = $_POST['schedule_id'];
      $questions = $this->vcm->getScheduleQuestions($student_id, $schedule_id);
      echo json_encode($questions);
    }

    public function s3FileUpload($file) {
      if ($file['tmp_name'] == '' || $file['name'] == '') {
        return ['status' => 'empty', 'file_name' => ''];
      }
      $path = $this->filemanager->uploadFile($file['tmp_name'], $file['name'], 'student_avatar');
      return $path;
    }

    public function save_avatar() {
      $file = $_FILES['avatar'];
      $path = $this->s3FileUpload($file);
      if($path['file_name'] != '') {
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $status = $this->vcm->save_student_avatar($student_id, $path['file_name']);
        echo $status;
      } else {
        echo 0;
      }
    }

  }

?>