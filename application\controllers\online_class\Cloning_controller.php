<?php

  defined('BASEPATH') OR exit('No direct script access allowed');

  class Cloning_controller extends CI_Controller {

    private $yearId;

    public function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isAuthorized('ONLINE_CLASS.MODULE')) {
        redirect('dashboard', 'refresh');
      }
      $this->yearId = $this->acad_year->getAcadYearId();
      date_default_timezone_set('Asia/Kolkata');
      $this->load->model('online_class/Schedule_model', 'schedule');
      $this->load->model('online_class/Cloning_model', 'clone');
      $this->load->model('class_section');
      $this->load->model('student/Student_Model');
      $this->load->library('filemanager');
    }

    public function index(){
      $data['rooms'] = $this->schedule->getClassRooms();
      $data['main_content'] = 'online_class/clone/clone_schedule';
      $this->load->view('inc/template', $data);
    }
    
    public function checkScheduleAvailable(){
      $clone_date = $_POST['clone_date'];
      $room_id = json_decode($_POST['room_id']);
      $data = $this->clone->checkScheduleAvailable($clone_date,$room_id);
      echo json_encode($data);
    }

    public function getSchedules(){
      $schedule_date = $_POST['schedule_date'];
      $room_id = json_decode($_POST['room_id']);
      $data['schedule'] = $this->clone->getSchedules($schedule_date,$room_id);
      echo json_encode($data);
    }

    public function getSelectedSchedules(){
      $schedule_ids_string = $_POST['schedule_ids_string'];
      $schedule_ids = json_decode($schedule_ids_string);
      $data['schedule'] = $this->clone->getSelectedSchedules($schedule_ids);
      echo json_encode($data);
    }

    public function cloneSingleSchedule(){
      $schedule_id = $_POST['schedule_id'];
      $clone_date = $_POST['clone_date'];
      $clone_date_org = date('Y-m-d',strtotime($clone_date));
      $prev_slots = $this->clone->getSlotsData($schedule_id);
      $prev_schedule = $this->clone->getScheduleData($schedule_id);
      $start_time = $clone_date_org.' '.$prev_slots->start_time;
      $end_time = $clone_date_org.' '.$prev_slots->end_time;
      $is_duplicate = $this->clone->checkScheduleExists($prev_schedule, $start_time, $end_time);
      if($is_duplicate) {
        echo 1;
      } else {
        $prev_participants = $this->clone->getParticipants($schedule_id);
        $schedule_data = array(
          'name' => $prev_schedule->name,
          'description' => $prev_schedule->description,
          'acad_year_id' =>$this->yearId,
          'created_by' =>$prev_schedule->created_by,
          'start_time' =>$start_time,
          'end_time' =>$end_time,
          'classroom_id' =>$prev_schedule->classroom_id,
          'status' =>'created'
        );
        $schedule_id_org =$this->clone->insertSingleSchedule($schedule_data,$prev_slots,$clone_date_org,$prev_participants);
        $students_notsend = $this->schedule->getNotificationsNotSentStudents($this->yearId);
        $staff_notsend = $this->schedule->getNotificationsNotSentStaff($this->yearId);
        if(empty($students_notsend) && empty($staff_notsend)) {
          echo $schedule_id_org;
        } else {
          foreach ($students_notsend as $key => $value) {
            $sending_ids[] = $value->stu_id;
          }
          foreach ($staff_notsend as $key => $value) {
            $sending_ids1[] = $value->staff_id;
          }
          if(!empty($sending_ids)) {
            $notify_array['student_ids'] = $sending_ids;
          }
          if(!empty($sending_ids1)) {
            $notify_array['staff_ids'] = $sending_ids1;
          }

          $this->load->helper('texting_helper');
          $schedule = $this->schedule->getSchedule($schedule_id_org);
          $notify_array['mode'] = 'notification';
          $notify_array['send_to'] = 'Both';
          $notify_array['source'] = 'Online Class';
          $notify_array['student_url'] = site_url('online_class/student_join_controller');
          $notify_array['staff_url'] = site_url('online_class/schedule_controller');
          $notify_array['message'] = $schedule->name.' class has been scheduled on '.local_time($schedule->start_time, 'd-M h:i a').' to '.local_time($schedule->end_time, 'd-M h:i a');
          sendText($notify_array);
          echo json_encode($schedule_id_org);
        }
      }
    }
}

?>