<?php

  defined('BASEPATH') OR exit('No direct script access allowed');

  class conference extends CI_Controller {

    public function __construct() {
      parent::__construct();
      $this->load->model('conference_model');
    }

    public function index(){
      $json_data = file_get_contents('php://input');
      $data = $this->conference_model->insertData1($json_data);
    }

    public function getData(){
      $data['openvidu_data'] = $this->conference_model->getData();
      $data['main_content'] = 'virtual_classroom/openvidu_view';
      $this->load->view('inc/template', $data);
    }

  }

?>