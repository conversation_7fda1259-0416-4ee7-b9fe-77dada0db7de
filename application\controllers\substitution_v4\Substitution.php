<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Substitution extends CI_Controller {
	function __construct() {
    parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('SUBSTITUTION') && $this->authorization->isAuthorized('SUBSTITUTION.MODULE')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('timetablev2/Substitution_model', 'substitution');
    }

    public function index() {
        $data['staff_data'] = $this->substitution->get_staff_list();
        $data['main_content']    = 'substitution_v4/substitution/index.php';
        $this->load->view('inc/template', $data);
    }

    public function manage_exemption() {
        $data['staff_data'] = $this->substitution->get_staff_list();
        $data['main_content']    = 'substitution_v4/manage_exemption/manage_Exemption.php';
        $this->load->view('inc/template', $data);
    }

    public function save_substitution_exemption(){
        $staff_id = $_POST['staff_id'];
        $exemption_type = $_POST['exemption_type'];
        $from_date = $_POST['from_date'];
        $to_date = $_POST['to_date'];
        echo($this->substitution->save_substitution_exemption($staff_id, $exemption_type, $from_date, $to_date));
    }

    public function show_substitution_exemption(){
        echo json_encode($this->substitution->show_substitution_exemption());
    }

    public function delete_exempt_staff(){
        $staff_id = $_POST['staff_id'];
        echo $this->substitution->delete_exempt_staff($staff_id);
    }

    public function add_substitution_day() {
        $date = date('Y-m-d', strtotime($_POST['date']));
        $substitution_id = $this->substitution->add_substitution_day($date);
        echo json_encode($substitution_id);
    }

    public function change_publish_status() {
        $substitution_id = $_POST['substitution_id'];
        $res = $this->substitution->change_publish_status();
        if($res == 1) {
            $staff_ids = $this->substitution->get_substituted_staff($substitution_id);
            if(!empty($staff_ids)) {
                $this->load->helper('texting_helper');
                $notify_array = array();
                $notify_array['staff_ids'] = $staff_ids;
                $notify_array['title'] = 'Substitution Added';
                $notify_array['message'] = 'You are assigned for substitution';
                $notify_array['mode'] = 'notification';
                $notify_array['source'] = 'Substitution';
                $notify_array['staff_url'] = site_url('timetablev2/template/staff_timetable_v2');
                sendText($notify_array);
            }
        }
        echo $res;
    }

    public function get_substitution_inputs() {
        $substitution_id = $_POST['substitution_id'];
        $substitutions = $this->substitution->get_substitution_inputs($substitution_id);
        echo json_encode($substitutions);
    }

    public function substitute_staff() {
        $status = $this->substitution->substitute_staff();
        echo $status;
    }

    public function change_allocation() {
        if($_POST['status'] === 'Published'){
            $staff_ids = $this->substitution->get_staff_to_remove();
            if(!empty($staff_ids)) {
                $this->load->helper('texting_helper');
                $notify_array = array();
                $notify_array['staff_ids'] = $staff_ids;
                $notify_array['title'] = 'Substitution Added';
                $notify_array['message'] = 'You substitution has been removed';
                $notify_array['mode'] = 'notification';
                $notify_array['source'] = 'Substitution';
                $notify_array['staff_url'] = site_url('timetablev2/template/staff_timetable_v2');
                sendText($notify_array);
            }
            $status = $this->substitution->change_allocation();
            $res = $this->substitution->change_publish_status();
            if($res == 1) {
                $staff_ids = $this->substitution->get_substituted_staff($_POST['substitution_id']);
                if(!empty($staff_ids)) {
                    $this->load->helper('texting_helper');
                    $notify_array = array();
                    $notify_array['staff_ids'] = $staff_ids;
                    $notify_array['title'] = 'Substitution Added';
                    $notify_array['message'] = 'You are assigned a new substitution';
                    $notify_array['mode'] = 'notification';
                    $notify_array['source'] = 'Substitution';
                    $notify_array['staff_url'] = site_url('timetablev2/template/staff_timetable_v2');
                    sendText($notify_array);
                }
            }
            echo $status;
        }
        else{
            $status = $this->substitution->change_allocation();
            echo $status;
        }
    }

    public function perform_substitution() {
        $staff_id = $_POST['staff_id'];
        $data['sub_input_id'] = $_POST['sub_input_id'];
        $data['substitution_id'] = $_POST['substitution_id'];
        $data['substitution_date'] = $_POST['substitution_date'];
        $data['staff'] = $this->substitution->get_staff_data($staff_id);
        $data['templates'] = $this->substitution->get_active_templates();
        echo json_encode($data);
    }

    public function get_staff_section_timetables() {
        $template_id = $_POST['template_id'];
        $week_day = $_POST['week_day'];
        $sub_input_id = $_POST['sub_input_id'];
        $staff_id = $_POST['staff_id'];
        $substitution_date = $_POST['substitution_date'];
        $sections = $this->substitution->get_substitutions_sections($sub_input_id, $template_id);
        $ttArr = array ();
        $staff_list = $this->substitution->get_staff_list();
        $staff_names = [];
        foreach ($staff_list as $stf) {
            $staff_names[$stf->staff_id] = $stf->staff_name;
        }
        $current_staff_name = $staff_names[$staff_id];
        $sub_data = $this->substitution->get_staff_substitutions_data($sub_input_id);
        foreach ($sections as $section) {
            $temp = new stdClass();
            $temp->csId = $section->id;
            $temp->csName = $section->class_name.''.$section->section_name;
            $tta =  $this->substitution->getSectionTTByWeekDay($section->id, $week_day, $template_id);
            if($tta){
                $temp->tt_exists = 1;
                $temp->tta =$tta;
                foreach ($tta as $period) {
                    $p_id = $period->period_id;
                    $period->staff_name = $period->staff_longname_list;
                    $period->substitute_status = 0;//not substitutable
                    if(array_key_exists($p_id, $sub_data)) {
                        $period->ids = count($sub_data[$p_id]);
                        $period->substitute_status = 1;//substitutable
                        $period->sub_output_id = $sub_data[$p_id]->id;
                        $period->old_staff_period_id = $sub_data[$p_id]->staff_period_id;
                        if($sub_data[$p_id]->new_sub_staff_id) {
                            $period->substitute_status = 2;//substituted
                            $period->new_sub_staff_id = explode(",", $sub_data[$p_id]->new_sub_staff_id);
                            // $period->new_sub_staff_id = $sub_data[$p_id]->new_sub_staff_id;
                            if(count($period->new_sub_staff_id)>1){
                                foreach($period->new_sub_staff_id as $new_sub_staff_id){
                                    $period->sub_staff_name[] = $staff_names[$new_sub_staff_id];
                                }
                            }
                            else{
                                $period->sub_staff_name = $staff_names[$period->new_sub_staff_id[0]];
                            }
                        }
                    }
                }
            } else{
            $temp->tt_exists = 0;
            }
            $ttArr[] = $temp;
        }
        echo json_encode($ttArr);
    }

    public function add_inputs_from_leaves() {
        $date = date('Y-m-d', strtotime($_POST['date']));
        $substitution_id = $_POST['substitution_id'];
        $leaves = $this->substitution->get_staff_leaves_data($date, $substitution_id);
        $times = $this->settings->getSetting('timetable_times');
        if(empty($times)) {
            $times = new stdClass();
            $times->start = date('H:i:s', strtotime('8:00 am'));
            $times->middle = date('H:i:s', strtotime('12:00 pm'));
            $times->end = date('H:i:s', strtotime('4:00 pm'));
        } else {
            $times->start = date('H:i:s', strtotime($times->start));
            $times->middle = date('H:i:s', strtotime($times->middle));
            $times->end = date('H:i:s', strtotime($times->end));
        }
        $inp = array();
        foreach ($leaves as $leave) {
            $input = array();
            $input['date'] = $date;
            $input['substitution_id'] = $substitution_id;
            $input['source_type'] = 'Leave';
            $input['created_by'] = $this->authorization->getAvatarStakeHolderId();
            $input['acad_year_id'] = $this->acad_year->getAcadYearId();
            $input['start_time'] = $times->start;
            $input['end_time'] = $times->end;
            if($leave->leave_for == 'morning') {
                $input['end_time'] = $times->middle;
            } else if($leave->leave_for == 'noon') {
                $input['start_time'] = $times->middle;
            }
            $input['staff_id'] = $leave->staff_id;
            $input['source_id'] = $leave->id;
            $input['leave_status'] = $leave->status;
            $inp[] = $input;
        }
        $a = $this->substitution->save_substitution_data($inp);
        // $status = $this->substitution->add_inputs_from_leaves($date, $substitution_id);
        echo $a;
    }

    public function save_adhoc_substitution() {
        $inp=array();
        $input['date'] = date('Y-m-d', strtotime($_POST['date']));
        $input['staff_id'] = $_POST['staff_id'];
        $input['substitution_id'] = $_POST['substitution_id'];
        $already_added = $this->substitution->checkSubstitutionAdded($input['staff_id'], $input['date']);
        if($already_added) {
          $status = 5; //staff already added
        } else {
            $input['start_time'] = date('H:i:s', strtotime($_POST['time_in']));
            $input['end_time'] = date('H:i:s', strtotime($_POST['time_out']));
            $input['source_type'] = 'Ad-hoc addition';
            $input['created_by'] = $this->authorization->getAvatarStakeHolderId();
            $input['acad_year_id'] = $this->acad_year->getAcadYearId();
            $inp[] = $input;
            $status = $this->substitution->save_substitution_data($inp);
        }
        echo $status;
    }

    public function show_biometric_data(){
        $substitution_date = date('Y-m-d', strtotime($_POST['substitution_date']));
        $inp = [];
        $substitution_id = $_POST['substitution_id'];
        $acad_year_id = $this->acad_year->getAcadYearId();
        $staff_ids = $this->substitution->get_biometric_data($substitution_date, $acad_year_id);
        echo json_encode($staff_ids);
    }

    public function get_biometric_data(){
        $substitution_date = date('Y-m-d', strtotime($_POST['substitution_date']));
        $inp = [];
        $substitution_id = $_POST['substitution_id'];
        $acad_year_id = $this->acad_year->getAcadYearId();
        $staff_ids = $_POST['staff_ids'];
        foreach ($staff_ids as $key => $staff_id){
            $already_added = $this->substitution->checkSubstitutionAdded($staff_id, $substitution_date);
            if ($already_added) {
                unset($staff_ids[$key]);
            }
        }
        foreach ($staff_ids as $staff_id) {
            $input = [];

            $input['start_time'] = '07:30:00';
            $input['end_time'] = '17:00:00';
            $input['source_type'] = 'Ad-hoc addition';
            $input['date'] = $substitution_date;
            $input['staff_id'] = $staff_id;
            $input['acad_year_id'] = $this->acad_year->getAcadYearId();
            $input['substitution_id'] = $substitution_id;
            $inp[] = $input;
        }
        $status = $this->substitution->save_substitution_data($inp);
        echo json_encode($status);
    }

    public function get_section_staff_timetables(){
        $template_id = $_POST['template_id'];
        $week_day = $_POST['week_day'];
        $section_id = $_POST['section_id'];
        $substitution_id = $_POST['substitution_id'];
        $staff_list = $this->substitution->get_section_staff_list($section_id);
        $sub_data = $this->substitution->get_staff_substitutions_data_by_id($substitution_id);
        $substituted_staff = $this->substitution->get_substitutable_staff($substitution_id);
        $ttArr = array ();
        foreach ($staff_list as $staff) {
            if(in_array($staff->staff_id, $substituted_staff)) continue;
            $temp = new stdClass();
            $temp->staff_id = $staff->staff_id;
            $temp->staff_name = $staff->staff_name;
            $temp->staff_exception = $staff->staff_exception;
            $tta = $this->substitution->get_staff_template_periods_subjects($template_id, $substitution_id, $staff->staff_id, $week_day);
            if($tta){
                $temp->tt_exists = 1;
                foreach ($tta as &$period) {
                $stf_id = $staff->staff_id;
                if(array_key_exists($stf_id, $sub_data)) {
                    $p_id = $period->period_id;
                    $period->substitute_status = 0;//not substituted
                    if($this->_is_exists($p_id, $sub_data[$stf_id])) {
                    $period->substitute_status = 1;//substituted
                    $period->sub_staff_name[] = $sub_data[$stf_id][$p_id]->staff_name;
                    $period->sub_sec_name = $sub_data[$stf_id][$p_id]->class_section_name;
                    }
                }
                }
                $temp->tta = $tta;
            }
            else{
                $temp->tt_exists = 0;
            }
            $ttArr[] = $temp;
        }

        function compareStaffByException($a, $b) {
            if ($a->staff_exception == $b->staff_exception) {
                return 0;
            }
            return ($a->staff_exception > $b->staff_exception) ? 1 : -1;
        }
        usort($ttArr, 'compareStaffByException');
        $data['staffTT'] = $ttArr;
        echo json_encode($data);
    }

    public function remove_substitution() {
        $status = $this->substitution->remove_substitution();
        echo $status;
    }

    public function delete_adhoc_staff() {
        $sin_id = $_POST['sin_id'];
        echo $this->substitution->delete_adhoc_staff($sin_id);
    }

    private function _is_exists($stp_id, $sub_stp_arr) {
        foreach ($sub_stp_arr as $sub_stp_arr_obj) {
            $temp_arr = explode(',',$sub_stp_arr_obj->sub_staff_period_id);
            if (in_array($stp_id, $temp_arr)) {
                return 1;
            }
        }
        return 0;
    }

    public function cache(){
        $template_id = $_POST['template_id'];
        $today_date = $_POST['date'];
        $res = $this->substitution->cache($template_id, $today_date);
        echo json_encode($res);
    }

    public function auto_subs(){
        $substitution_date = $_POST['subs_date'];
        $week_day = $_POST['week_day'];
        $staff_id = $_POST['staff_id'];
        $template_id = $_POST['template_id'];
        $res = $this->substitution->auto_subs($substitution_date, $week_day, $staff_id, $template_id);
        echo json_encode($res);
    }

    public function deallocate_staff(){
        $period_id = $_POST['period_id'];
        $class_section_name = $_POST['cs_name'];
        $class_section_id = $_POST['cs_id'];
        $staff_id = $_POST['staff_id'];
        $week_day = $_POST['week_day'];
        $date = date('Y-m-d', strtotime($_POST['date']));
        $old_sub_name = $_POST['old_sub_name'];
        $template_id = $_POST['template_id'];
        $old_sub_id = $_POST['old_sub_id'];
        $res = $this->substitution->deallocate_staff($period_id, $class_section_name, $class_section_id, $staff_id, $week_day, $date, $old_sub_name, $template_id, $old_sub_id);
        echo $res;
    }

    public function get_template_id(){
        echo json_encode($this->substitution->get_template_id());
    }

}