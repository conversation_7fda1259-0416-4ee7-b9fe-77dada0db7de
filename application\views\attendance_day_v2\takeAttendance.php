<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2') ?>">Attendance V2</a></li>
  <li>Student Attendance</li>
</ul>
<?php //echo '<pre>'; print_r($class_section); die(); ?>
<?php date_default_timezone_set('Asia/Kolkata'); ?>

<div class="col-lg-12">
<div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('attendance_day_v2/Attendance_day_v2'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a>
            Student Attendance

          </h3>
        </div>
      </div>
    </div>
    <form id="form_data" method="post">
  <div class="card-body pt-1" style="padding: 10px 0px">
      <div class="card-body pt-1 d-flex align-items-end" style="padding:0px;">

        <div class="col-md-2">
          <div class="form-group">
            <label for="pwd">Section</label>
            <select name="classsecID" id="sectionid" class="form-control input-md" required="">
                  <option value="">Select Section</option>
                  <?php foreach ($class_section as $key => $cls_section) { ?>
                      <option  value="<?= $cls_section->classID.'_'.$cls_section->sectionID ?>"><?= $cls_section->class_name . '' . $cls_section->section_name ?></option>
                  <?php } ?>
              </select>
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            <label for="select_date">Date</label>
            <input
              type="text"
              class="form-control datePicker"
              id="select_date"
              name="selected_date"
              placeholder="Select Date"
              required
              value="<?php echo date('d-m-Y'); ?>"
              <?php
               if (!$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.TAKE_ATTENDANCE_FOR_PREVIOUS_DATES')): ?>
                min="<?php echo date('d-m-Y'); ?>"
              <?php endif; ?>
              max="<?php echo date('d-m-Y'); ?>"
            >
          </div>
        </div>

           <button type="button" class="btn btn-primary" onclick="takeAttendance()">Take Attendance</button>
      </div>
  </div>
        </form>
                  <div id="displayattendance" style="margin-top:2rem" >
                  </div>

                  <div id="nodataattendance" style="margin-top:2rem" >
                  </div>


        </div>


</div>
</div>

<!-- Code For Sending Notification -->
<div class="modal fade" id="notify-modal" role="dialog">
  <div class="modal-dialog">
    <form id="student-messages-form">
      <div class="modal-content" style="">
        <div class="modal-header">
          <h4 class="modal-title">Notify Absentees & Late-comers by Sms/Notification</h4>
        </div>
        <div class="modal-body">
          <!-- <input type="hidden" id="notify-attendance-master-id" value="0"> -->
          <div class="form-group" style="font-size: 16px;">
            <label class="radio-inline" for="notification">
              <input style="height: 18px; width: 18px;" type="radio" name="communication_mode" id="notification"
                value="notification" <?php echo ($notification_mode == 'notif-only' || $notification_mode == 'both') ? 'checked=""' : '' ?>>&nbsp;Notification
            </label>

            <label class="radio-inline" for="sms">
              <input style="height: 18px; width: 18px;" type="radio" name="communication_mode" id="sms" value="sms"
                <?php echo ($notification_mode == 'sms-only') ? 'checked=""' : '' ?>>&nbsp;SMS
            </label>

            <select id="text_send_to" class="form-control" name="text_send_to" style="margin: 0 0 0 15px;width: 16%;display:none">
              <option value="Both">Both</option>
              <option value="Father">Father</option>
              <option value="Mother">Mother</option>
              <option value="preferred">Preferred Parent</option>
            </select>
          </div>
          <div id="notify-content" style="overflow-y:auto;max-height:450px;">
          </div>
        </div>
        <div class="modal-footer">
          <button style="width: 120px;" type="button" class="btn btn-warning my-0" data-dismiss="modal" id="cancelButton">Cancel</button>
          <button id="confirmBtn" onclick="send_messages()" style="width: 120px;" type="button"
            class="btn btn-primary my-0">Confirm</button>
        </div>
      </div>
    </form>
  </div>
</div>

<!-- History Modal Start -->

<div id="view-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="display: none;">
  <div class="modal-dialog" style="width:60%;margin:auto;top:25%">
     <div class="modal-content">

        <div class="modal-header">
           <h4 class="modal-title"> Attendance History </h4>
           <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
        </div>

        <div class="modal-body mobile">
           <div id="modal-loader" style="display: none; text-align: center;">
           <!-- ajax loader -->
           <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>">
           </div>
           <b><p id="studentDetails"></p></b>
           <div id="dynamic-content">
            </div>

        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        </div>

    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

<script>
document.addEventListener("DOMContentLoaded", function () {
    flatpickr("#timepicker", {
        enableTime: true,
        noCalendar: true,
        dateFormat: "h:i K",
        time_24hr: false,
    });
});

document.addEventListener("DOMContentLoaded", function () {
    var cookies = document.cookie.split(';').reduce((acc, cookie) => {
        var [key, value] = cookie.split('=').map(c => c.trim());
        acc[key] = value;
        return acc;
    }, {});
    if (cookies.selected_section) {
        $('#sectionid').val(cookies.selected_section);
    }

    $('#sectionid').on('change', function () {
        var selectedSection = $(this).val();
        if (selectedSection) {
            document.cookie = "selected_section=" + selectedSection + "; path=/;";
        }
    });
});

  function takeAttendance(){
    var form = $('#form_data')[0];
    var fileFormData = new FormData(form);
    var selectedSec = $('#sectionid').val();
    var validateDate = $('#select_date').val();
    if(selectedSec =='' || validateDate ==''){
      Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Both class and Date should be Selected',
                confirmButtonText: 'OK'
            });
            return false;
    }

    $('#displayattendance').html('<div class="no-data-display">Loading...</div>');
    $.ajax({
        url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/getAssignedDate'); ?>',
        type: 'post',
        data: {'selectedDate':validateDate,'selectedSec':selectedSec},
        success: function(data){
            data = JSON.parse(data);

            if(!data) {
                var html = '<div class="no-data-display" style="margin:1rem;">No sessions assigned for the class</div>';
                $('#nodataattendance').html(html);
                $('#displayattendance').html('');
                return false;
            }

            if(data.is_locked === false) {
                var html = '<div class="no-data-display" style="margin:1rem;">You need to lock the calendar template assigned to this class to proceed further with mapping and events</div>';
                $('#nodataattendance').html(html);
                $('#displayattendance').html('');
                return false;
            }

            getAttendanceData(fileFormData);
        }
    });
}

function getAttendanceData(fileFormData) {
    $.ajax({
        url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/getstudentsclassWise'); ?>',
        type: 'post',
        data: fileFormData,
        processData: false,
        contentType: false,
        success: function(response) {
            try {
                var data = JSON.parse(response);
                var html = '';
                if (data.error) {
                    html = '<div class="no-data-display" style="margin:1rem;">' + data.message + '</div>';
                    $('#nodataattendance').html(html);
                    $('#displayattendance').html('');
                    return false;
                }

                if (data.students && data.students.isHoliday) {
                    html = '<div class="no-data-display">Selected date (' + $('#select_date').val() + ') is marked as a holiday in the calendar. Attendance cannot be taken on this day.</div>';
                    $('#nodataattendance').html(html);
                    $('#displayattendance').html('');
                    return false;
                }

                if (data.students && data.students.students && data.students.students.summary &&
                    data.students.students.summary.sessionCount <= 0) {
                    html = '<div class="no-data-display" style="margin:1rem;">No sessions have been configured for this class.</div>';
                    $('#nodataattendance').html(html);
                    $('#displayattendance').html('');
                    return false;
                }

                if (!data.students || !data.students.students ||
                    !data.students.students.student_data || data.students.students.student_data.length == 0) {
                    html = '<div class="no-data-display" style="margin:1rem;">No students found in this class.</div>';
                    $('#nodataattendance').html(html);
                    $('#displayattendance').html('');
                    return false;
                } else if (data.students.students.summary.isTaken == 1) {
                    construct_edit_attendance(data);
                } else {
                    construct_add_attendance(data);
                }
                $('#nodataattendance').html(html);
            } catch (e) {
                console.error('An error occurred:', e);
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'An unexpected error occurred. Please try again later.',
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', status, error);
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Failed to fetch attendance data. Please try again later.',
                confirmButtonText: 'OK'
            });
        }
    });
}

function construct_add_attendance(data) {
    var emptycheck = $('#sectionid').val();
    var dateemptycheck = $('#select_date').val();
    var absent = <?php echo (json_encode($absent_reasons)) ?>;

    var sessionCount = 2;
    if (data.students && data.students.students && data.students.students.summary && data.students.students.summary.sessionCount) {
        sessionCount = parseInt(data.students.students.summary.sessionCount);
    }

    var html = '';

   

    if (data.students && data.students.isHoliday) {
        html = '<div class="no-data-display">Selected date (' + $('#select_date').val() + ') is marked as a holiday in the calendar. Attendance cannot be taken on this day.</div>';
        $('#nodataattendance').html(html);
        $('#displayattendance').html('');
        return;
    }


    if (!data.students || !data.students.students || !data.students.students.student_data || data.students.students.student_data.length === 0) {
        html = '<div class="no-data-display">No students found in this class.</div>';
        $('#nodataattendance').html(html);
        $('#displayattendance').html('');
        return;
    }


    if (sessionCount <= 0) {
        html = '<div class="no-data-display">No sessions have been configured for this class.</div>';
        $('#nodataattendance').html(html);
        $('#displayattendance').html('');
        return;
    }
    html += '<div class="panel-body"><form id="attData" method="post"><table class="table table-bordered" id="attendance-table">';
    html += '<thead><tr><th>#</th><th>Student Name </th><th>Father Name</th>';

    if(sessionCount > 1) {
        html += '<th>Morning</th><th>Afternoon</th>';
    } else {
        html += '<th>Full Day</th>';
    }
    html += '<th>Absent Reason</th></tr></thead>';

    html += '<tbody>';

    for (var s = 0; s < data.students.students.student_data.length; s++) {
      var fatherNo = data.students.students.student_data[s].FatherNo;
      var displayFatherNo = (fatherNo === null || fatherNo === '') ? '-' : fatherNo;
        html += '<tr>';
        html += '<td>' + (s + 1) + '</td>';
        html += '<td>' + data.students.students.student_data[s].std_name + '<input type="hidden" name="attendance[' + data.students.students.student_data[s].id + '][Std_name]" value="' + data.students.students.student_data[s].std_name + '"></td>';
        html += '<td>' + data.students.students.student_data[s].fatherName + '<br>(' + displayFatherNo + ')</td>';

        html += '<td><label class="switch"><input type="hidden" name="attendance[' + data.students.students.student_data[s].id + '][attendance_morning]" value="0"><input class="morning" data-index="' + s + '" checked value="1" type="checkbox" name="attendance[' + data.students.students.student_data[s].id + '][attendance_morning]"/><span></span></label></td>';

        if(sessionCount > 1) {
            html += '<td><label class="switch"><input type="hidden" name="attendance[' + data.students.students.student_data[s].id + '][attendance_afternoon]" value="0"><input class="afternoon" data-index="' + s + '" checked value="1" type="checkbox" name="attendance[' + data.students.students.student_data[s].id + '][attendance_afternoon]"/><span></span></label></td>';
        }

        html += '<input type="hidden" value="' + emptycheck + '" name="class_sec"/><input type="hidden" value="' + dateemptycheck + '" name="date"/>';
        html += '<input type="hidden" name="session_count" id="session_count" value="' + sessionCount + '"/>';

        html += '<td><select class="form-control" id="reason_data' + s + '" disabled onchange="store_reason(' + s + ')"><option value="">Select Absent Reasons</option>';
        for(var i=0; i< absent.length; i++){
            var consider = (absent[i].consider_as_present == 1) ? 'Present' : 'Absent';
            var colorClass = (consider == 'Present') ? 'colorClass' : '';
            html += '<option value="' + absent[i].id + '">' + absent[i].reasons + ' - <span class="' + colorClass + '">' + consider + '</span></option>';
        }
        html += '</select><input type="hidden" id="hidden_reason' + s + '" name="attendance[' + data.students.students.student_data[s].id + '][reasons]" /><input type="hidden" id="hidden_reason_id' + s + '" name="attendance[' + data.students.students.student_data[s].id + '][reasons_id]" /></td>';
        html += '</tr>';
    }
    html += '</tbody></table>';
    html += '<center><button class="btn btn-primary" type="button" id="take_attendance" onclick="sendattData()" >Submit</button>';
    html += '<button class="btn btn-danger">Cancel</button></center>';
    html += '</form></div>';

    $('#displayattendance').html(html);

    // Initialize DataTable with dom: 'Bfrtip' for search box at top right, and remove the search label
    $('#attendance-table').DataTable({
        dom: '<"top"f>rt<"bottom"ip><"clear">', // Only the input box, no label
        paging: false,
        info: false,
        searching: true,
        ordering: false,
        buttons: []
    });

  

    if(sessionCount > 1) {
        $('.morning, .afternoon').on('change', function() {
            var row = $(this).closest('tr');
            var index = $(this).data('index');
            var isChecked = $(this).is(':checked');

            if ($(this).hasClass('morning')) {
                $('.afternoon[data-index="' + index + '"]').prop('checked', isChecked);
            } else {
                $('.morning[data-index="' + index + '"]').prop('checked', isChecked);
            }

            var anyUnchecked = row.find('.morning, .afternoon').is(':not(:checked)');
            row.find('#reason_data' + index).prop('disabled', !anyUnchecked);
        });
    } else {
        $('.morning').on('change', function() {
            var row = $(this).closest('tr');
            var index = $(this).data('index');
            var isChecked = $(this).is(':checked');
            row.find('#reason_data' + index).prop('disabled', isChecked);
        });
    }
}

  function construct_edit_attendance(data){
    console.log(data);
    var edit_absent  = <?php echo (json_encode($absent_reasons)) ?>;
    var sessionCount = 2;
    if(data.students && data.students.students && data.students.students.summary && data.students.students.summary.sessionCount){
        sessionCount = parseInt(data.students.students.summary.sessionCount);
    }
    var html = '';

    

    html += '<div class="panel-body">';
    html += '<div class="card-body pt-1" style="background-color: white;margin-bottom:2rem">'
         + '<label class="col-md-2">Class: <strong>' + data.students.students.summary.class_name + ' ' + data.students.students.summary.section_name + '</strong></label>'
         + '<label class="col-md-2">Taken By: <strong>' + data.students.students.summary.taken_by + '</strong></label>'
         + '<label class="col-md-2">Date: <strong>' + data.students.students.summary.taken_date + '</strong></label>'
         + '<label class="col-md-2">Total Absent: <strong class="total-absent-count">' + data.students.students.summary.absent.afternoon_absent_count + '</strong></label>'
         + '<label class="col-md-2">Total Students: <strong>' + data.students.students.student_data.length + '</strong></label>'
         + '</div>';


    html += '<input type="hidden" name="session_count" id="session_count" value="' + sessionCount + '"/>';

    if(sessionCount > 1){
      html += '<table class="table table-bordered" id="attendance-table"><thead><tr>'
           + '<th>#</th><th>Student Name</th><th>Father Name</th><th>Morning</th><th>Afternoon</th>'
           + '<th>Absent Reason</th><th>Is Late</th><th>History</th></tr></thead><tbody>';
    } else {
      html += '<table class="table table-bordered" id="attendance-table"><thead><tr>'
           + '<th>Sl</th><th>Student Name</th><th>Father Name</th><th>Full Day</th>'
           + '<th>Absent Reason</th><th>Is Late</th><th>History</th></tr></thead><tbody>';
    }

    for (var s = 0; s < data.students.students.student_data.length; s++) {
          var fatherNo = data.students.students.student_data[s].FatherNo;
      var displayFatherNo = (fatherNo === null || fatherNo === '') ? '-' : fatherNo;
        html += '<tr class="student-row">';
        html += '<td>' + (s + 1) + '</td>';
        html += '<td>' + data.students.students.student_data[s].std_name
              + '<input type="hidden" name="attendance[' + data.students.students.student_data[s].id + '][Std_name]" value="' + data.students.students.student_data[s].std_name + '"></td>';
        html += '<td>' + data.students.students.student_data[s].fatherName
              + '<br>(' + displayFatherNo + ')</td>';

        if(sessionCount > 1){
            var morningchecked = data.students.students.student_data[s].morning == 1 ? 'checked' : '';
            var afternoonchecked = data.students.students.student_data[s].afternoon == 1 ? 'checked' : '';
            html += '<td><label class="switch"><input type="hidden" name="attendance[' + data.students.students.student_data[s].id + '][attendance_morning]" value="0">'
                 + '<input onchange="update_att_student_wise(' + data.students.students.student_data[s].id + ','
                 + data.students.students.summary.sessionID + ',' + data.students.students.student_data[s].att_std_primary_id
                 + ', \'morning_session_status\', \'Morning\')" id="morning_session_status_'+data.students.students.student_data[s].att_std_primary_id
                 + '" class="edit_morning" data-index="' + s + '" ' + morningchecked + ' type="checkbox" '
                 + 'name="attendance[' + data.students.students.student_data[s].id + '][attendance_morning]"/><span></span></label></td>';
            html += '<td><label class="switch"><input type="hidden" name="attendance[' + data.students.students.student_data[s].id + '][attendance_afternoon]" value="0">'
                 + '<input onchange="update_att_student_wise(' + data.students.students.student_data[s].id + ','
                 + data.students.students.summary.sessionID + ',' + data.students.students.student_data[s].att_std_primary_id
                 + ', \'afternoon_session_status\', \'Afternoon\')" id="afternoon_session_status_'+data.students.students.student_data[s].att_std_primary_id
                 + '" class="edit_afternoon" data-index="' + s + '" ' + afternoonchecked + ' type="checkbox" '
                 + 'name="attendance[' + data.students.students.student_data[s].id + '][attendance_afternoon]"/><span></span></label></td>';
        } else {
            var fullDayChecked = data.students.students.student_data[s].morning == 1 ? 'checked' : '';
            html += '<td><label class="switch"><input type="hidden" name="attendance[' + data.students.students.student_data[s].id + '][attendance_morning]" value="0">'
                 + '<input onchange="update_att_student_wise(' + data.students.students.student_data[s].id + ','
                 + data.students.students.summary.sessionID + ',' + data.students.students.student_data[s].att_std_primary_id
                 + ', \'morning_session_status\', \'Full Day\')" id="morning_session_status_'+data.students.students.student_data[s].att_std_primary_id
                 + '" class="edit_morning" data-index="' + s + '" ' + fullDayChecked + ' type="checkbox" '
                 + 'name="attendance[' + data.students.students.student_data[s].id + '][attendance_morning]"/><span></span></label></td>';
        }

        html += '<td><select class="form-control" id="edit_reason_data'+s+'" data-index="' + s + '" onchange="absent_reason(' + s + ','
             + data.students.students.student_data[s].id + ',' + data.students.students.summary.sessionID + ','
             + data.students.students.student_data[s].att_std_primary_id + ')"><option value="">Select Absent Reasons</option>';
        for(var i = 0; i < edit_absent.length; i++){
            var consider = (edit_absent[i].consider_as_present == 1) ? 'Present' : 'Absent';
            var selected = data.students.students.student_data[s].overide_remarks == edit_absent[i].reasons + ' - ' + consider ? 'selected' : '';
            html += '<option value="'+edit_absent[i].id+'" '+selected+'>'+edit_absent[i].reasons+' - '+consider+'</option>';
        }
        html += '</select></td>';
        html += '<td><label class="switch"><input type="hidden" name="attendance[' + data.students.students.student_data[s].id + '][islate]" value="0">'
             + '<input onchange="update_att_student_wise(' + data.students.students.student_data[s].id + ','
             + data.students.students.summary.sessionID + ',' + data.students.students.student_data[s].att_std_primary_id
             + ', \'is_late\', \'Late\')" id="is_late_'+data.students.students.student_data[s].att_std_primary_id
             + '" class="is_Late" data-index="'+s+'" ' + (data.students.students.student_data[s].is_late == 1 ? 'checked' : '')
             + ' type="checkbox" name="attendance[' + data.students.students.student_data[s].id + '][islate]"/><span></span></label></td>';
        html += '<td><a data-toggle="modal" data-target="#view-modal" data-index="'
             + data.students.students.student_data[s].att_std_primary_id
             + '" id="getUser" class="btn btn-xs btn-info"><i class="fa fa-eye"></i></a></td>';
        html += '</tr>';
    }
    html += '</tbody></table></div>';
    $('#displayattendance').html(html);

    // Initialize DataTable with dom: 'Bfrtip' for search box at top right, and remove the search label
    $('#attendance-table').DataTable({
        dom: '<"top"f>rt<"bottom"ip><"clear">', // Only the input box, no label
        paging: false,
        info: false,
        searching: true,
        ordering: false,
        buttons: []
    });

   

    $('.edit_morning,.edit_afternoon').on('change', handleSwitchChange)

            $('tr').each(function() {
                var row = $(this);
                var index = row.find('.edit_morning, .edit_afternoon').data('index');
                if (index === undefined) return;
                var reasonField = row.find('#edit_reason_data' + index);
                if (!reasonField.length) return;


                var hasAfternoonSession = row.find('.edit_afternoon').length > 0;

                if (hasAfternoonSession) {

                    var morningPresent = row.find('.edit_morning').is(':checked');
                    var afternoonPresent = row.find('.edit_afternoon').is(':checked');
                    var bothSessionsPresent = morningPresent && afternoonPresent;


                    reasonField.prop('disabled', bothSessionsPresent);


                    if (bothSessionsPresent && reasonField.val() !== '') {
                        reasonField.val('');
                    }
                } else {

                    var fullDayPresent = row.find('.edit_morning').is(':checked');


                    reasonField.prop('disabled', fullDayPresent);


                    if (fullDayPresent && reasonField.val() !== '') {
                        reasonField.val('');
                    }
                }
            });


            updateTotalAbsentCount();

    // $('.is_Late').on('change', function() {
    //     var row = $(this).closest('tr');
    //     var studentId = $(this).closest('tr').find('input[name*="[Std_name]"]').attr('name').match(/\[(\d+)\]/)[1];
    //             var sessionId = row.find('input[name="sessionId"]').val();
    //     var attId = $(this).closest('tr').find('.edit_morning').attr('id').replace('morning_session_status_', '');
    //     var isChecked = $(this).is(':checked');
    //     var index = $(this).data('index');
    //             var reasonField = row.find('#edit_reason_data' + index);


    //             var hasAfternoonSession = row.find('.edit_afternoon').length > 0;

    //             if(isChecked) {

    //                 var sessionCount = 2;
    //                 if ($('#session_count').length) {
    //                     sessionCount = parseInt($('#session_count').val());
    //                 }


    //                 var morningCheckbox = row.find('.edit_morning');
    //                 morningCheckbox.prop('checked', true);


    //                 if (hasAfternoonSession && sessionCount > 1) {
    //                     var afternoonCheckbox = row.find('.edit_afternoon');
    //                     afternoonCheckbox.prop('checked', true);
    //                 }


    //                 reasonField.prop('disabled', true);
    //                 if (reasonField.val() !== '') {
    //                     reasonField.val('');
    //                 }


    //                 var updates = {
    //                     morning_session_status: 1,
    //                     absent_remarks: null,
    //                     override_present: 0
    //                 };


    //                 if (sessionCount > 1) {
    //                     updates.afternoon_session_status = 1;
    //                 }


    //                 update_att_student_wise(studentId, sessionId, attId, 'is_late', 'Late', '', updates);
    //             } else {

    //                 update_att_student_wise(studentId, sessionId, attId, 'is_late', 'Late');
    //             }


    //             updateTotalAbsentCount();
    // });

    $('.edit_morning, .edit_afternoon').on('change', function() {
        var row = $(this).closest('tr');
        var index = $(this).data('index');
        var bothUnchecked = !row.find('.edit_morning').is(':checked') && !row.find('.edit_afternoon').is(':checked');

        if(bothUnchecked) {
            row.find('.is_Late').prop('checked', false);
        }
    });
  }

  function handleSwitchChange() {
    var row = $(this).closest('tr');
    var index = $(this).data('index');
    var isChecked = $(this).is(':checked');
    var reasonField = row.find('select[data-index="' + index + '"]');


    var hasAfternoonSession = row.find('.edit_afternoon').length > 0;

    if (hasAfternoonSession) {

        var morningPresent = row.find('.edit_morning').is(':checked');
        var afternoonPresent = row.find('.edit_afternoon').is(':checked');
        var bothSessionsPresent = morningPresent && afternoonPresent;


        reasonField.prop('disabled', bothSessionsPresent);


        if (bothSessionsPresent && reasonField.val() !== '') {
            reasonField.val('');
        }
    } else {

        var fullDayPresent = row.find('.edit_morning').is(':checked');


        reasonField.prop('disabled', fullDayPresent);


        if (fullDayPresent && reasonField.val() !== '') {
            reasonField.val('');
        }
    }


    updateTotalAbsentCount();
}

function update_att_student_wise(student_id, session_id, att_id, session_column, markedAs, reason='', additionalUpdates={}) {
    var history_data = '';
    var sessionValue = '';
    var $checkbox = $('#' + session_column + '_' + att_id);
    if(reason != '') {
        sessionValue = reason;
        session_column = 'absent_remarks';
        history_data = reason;
        proceedWithAjaxCall();
    } else {
        sessionValue = $checkbox.is(':checked') ? 1 : 0;

        if(session_column === 'is_late') {
            if(sessionValue === 1) {
                // First show confirmation dialog
                Swal.fire({
                    title: 'Are you sure?',
                    text: "You want to mark this student as late!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, confirm it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Get current time
                        const now = new Date();
                        const currentHour = now.getHours() % 12 || 12; // Convert to 12-hour format
                        const currentMinute = now.getMinutes();
                        const currentAmPm = now.getHours() >= 12 ? 'PM' : 'AM';

                        // Then show time input for late students
                        Swal.fire({
                            title: 'Enter Late Time',
                            html: `
                                <div style="display: flex; justify-content: space-between; align-items: center; gap: 12px; background: #f8f9fa; padding: 12px; border-radius: 8px;">
                                    <div style="flex: 1;">
                                        <div style="margin-bottom: 6px; text-align: center; font-size: 13px; color: #555; font-weight: 500;">Hour</div>
                                        <select id="lateHours" class="swal2-input" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer;">
                                            ${Array.from({length: 12}, (_, i) => `<option value="${i+1}" ${i+1 === currentHour ? 'selected' : ''}>${i+1}</option>`).join('')}
                                        </select>
                                    </div>
                                    
                                    <div style="flex: 1;">
                                        <div style="margin-bottom: 6px; text-align: center; font-size: 13px; color: #555; font-weight: 500;">Minute</div>
                                        <select id="lateMinutes" class="swal2-input" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer;">
                                            ${Array.from({length: 60}, (_, i) => `<option value="${i}" ${i === currentMinute ? 'selected' : ''}>${i.toString().padStart(2, '0')}</option>`).join('')}
                                        </select>
                                    </div>
                                    
                                    <div style="flex: 1;">
                                        <div style="margin-bottom: 6px; text-align: center; font-size: 13px; color: #555; font-weight: 500;">AM/PM</div>
                                        <select id="lateAmPm" class="swal2-input" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer;">
                                            <option value="AM" ${currentAmPm === 'AM' ? 'selected' : ''} style="color: #0066cc;">AM</option>
                                            <option value="PM" ${currentAmPm === 'PM' ? 'selected' : ''} style="color: #cc3300;">PM</option>
                                        </select>
                                    </div>
                                </div>
                            `,
                            focusConfirm: false,
                            preConfirm: () => {
                                const hours = document.getElementById('lateHours').value;
                                const minutes = document.getElementById('lateMinutes').value;
                                const ampm = document.getElementById('lateAmPm').value;
                                if (!hours || !minutes || !ampm) {
                                    Swal.showValidationMessage('Please select a valid time');
                                    return false;
                                }
                                return `${hours}:${minutes.padStart(2, '0')} ${ampm}`;
                            },
                            showCancelButton: true,
                            confirmButtonText: 'Submit',
                            cancelButtonText: 'Cancel',
                            allowOutsideClick: false
                        }).then((timeResult) => {
                            if (timeResult.isConfirmed) {
                                const lateTime = timeResult.value;
                                
                                var sessionCount = 2;
                                if ($('#session_count').length) {
                                    sessionCount = parseInt($('#session_count').val());
                                }

                                // Force toggle the late status
                                $checkbox.prop('checked', true);

                                additionalUpdates = {
                                    morning_session_status: 1,
                                    absent_remarks: null,
                                    override_present: 0,
                                    late_time: lateTime,
                                    is_late: 1  // Force late status to true
                                };

                                
                                if (sessionCount > 1) {
                                    additionalUpdates.afternoon_session_status = 1;
                                    history_data = 'Marked as Late and Present for both sessions';
                                } else {
                                    history_data = 'Marked as Late and Present for full day';
                                }
                                
                                proceedWithAjaxCall();
                            } else {
                                // If time selection is cancelled, revert the checkbox
                                $checkbox.prop('checked', false);
                            }
                        });
                    } else {
                        // If user cancels, revert the checkbox
                        $checkbox.prop('checked', false);
                    }
                });
                return;
            } else {
                // When unchecking late, show confirmation first
                Swal.fire({
                    title: 'Are you sure?',
                    text: "You want to remove late status for this student!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, confirm it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        additionalUpdates = { 
                            is_late: 0,
                            late_time: null 
                        };
                        history_data = 'Changed from Late to not late';
                        proceedWithAjaxCall();
                    } else {
                        // If user cancels, keep the checkbox checked
                        $checkbox.prop('checked', true);
                    }
                });
                return;
            }
        } else {
            // For other attendance changes, show confirmation
            Swal.fire({
                title: 'Are you sure?',
                text: "You want to change this student's attendance status!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, confirm it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    if(sessionValue == 1) {
                        history_data = 'Marked as ' + markedAs + ' Present';
                    } else {
                        history_data = 'Marked as ' + markedAs + ' Absent';
                        additionalUpdates = { is_late: 0 };
                    }
                    proceedWithAjaxCall();
                } else {
                    // If user cancels, revert the checkbox
                    $checkbox.prop('checked', !sessionValue);
                }
            });
            return;
        }
    }

    function proceedWithAjaxCall() {
        $.ajax({
            url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/update_student_att_data'); ?>',
            type: 'POST',
            data: {
                'student_id': student_id,
                'session_id': session_id,
                'att_id': att_id,
                'session_column': session_column,
                'sessionValue': sessionValue,
                'history_data': history_data,
                'additionalUpdates': additionalUpdates
            },
            success: function(data){
                console.log('Update response:', data);
                try {
                    // Try to parse JSON response
                    var response = typeof data === 'string' ? JSON.parse(data) : data;

                    if (response.status == 1) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success!',
                            text: response.message || 'Attendance updated successfully.',
                            confirmButtonText: 'Great!'
                        }).then(() => {
                            if(additionalUpdates.morning_session_status !== undefined) {
                                $('#morning_session_status_' + att_id).prop('checked', additionalUpdates.morning_session_status === 1);
                            }
                            if(additionalUpdates.afternoon_session_status !== undefined) {
                                $('#afternoon_session_status_' + att_id).prop('checked', additionalUpdates.afternoon_session_status === 1);
                            }
                            if(additionalUpdates.is_late !== undefined) {
                                $('#is_late_' + att_id).prop('checked', additionalUpdates.is_late === 1);
                            }
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Update Failed!',
                            text: response.error || 'Failed to update attendance.',
                            confirmButtonText: 'OK'
                        });
                    }
                } catch (e) {
                    // Fallback for non-JSON responses (backward compatibility)
                    if (data == '1' || data === true || data === 1) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success!',
                            text: 'Attendance updated successfully.',
                            confirmButtonText: 'Great!'
                        }).then(() => {
                            if(additionalUpdates.morning_session_status !== undefined) {
                                $('#morning_session_status_' + att_id).prop('checked', additionalUpdates.morning_session_status === 1);
                            }
                            if(additionalUpdates.afternoon_session_status !== undefined) {
                                $('#afternoon_session_status_' + att_id).prop('checked', additionalUpdates.afternoon_session_status === 1);
                            }
                            if(additionalUpdates.is_late !== undefined) {
                                $('#is_late_' + att_id).prop('checked', additionalUpdates.is_late === 1);
                            }
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Update Failed!',
                            text: 'Failed to update attendance. Server returned: ' + data,
                            confirmButtonText: 'OK'
                        });
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr.responseText);
                try {
                    var errorResponse = JSON.parse(xhr.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Connection Error!',
                        text: errorResponse.error || 'Failed to connect to server. Please try again.',
                        confirmButtonText: 'OK'
                    });
                } catch (e) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Connection Error!',
                        text: 'Failed to update attendance. Please try again.',
                        confirmButtonText: 'OK'
                    });
                }
                // Revert checkbox on error
                $checkbox.prop('checked', !sessionValue);
            }
        });
    }
}

function absent_reason(rowid, student_id, session_id, att_id){
  var reason = $('#edit_reason_data' + rowid).find('option:selected').text();
  update_att_student_wise(student_id, session_id, att_id,'','',reason);
}

function updateTotalAbsentCount() {

  var totalStudents = $('.student-row').length;
  var absentCount = 0;


  var hasAfternoonSession = $('.edit_afternoon').length > 0;

  if (hasAfternoonSession) {

    $('.student-row').each(function() {
      var morningPresent = $(this).find('.edit_morning').is(':checked');
      var afternoonPresent = $(this).find('.edit_afternoon').is(':checked');

      if (!morningPresent && !afternoonPresent) {
        absentCount += 1;
      } else if (!morningPresent || !afternoonPresent) {
        absentCount += 0.5;
      }
      // If both present, add 0
    });
  } else {

    $('.student-row').each(function() {
      var fullDayPresent = $(this).find('.edit_morning').is(':checked');

      if (!fullDayPresent) {
        absentCount += 1;
      }
    });
  }
  // Show up to 1 decimal place if needed
  $('.total-absent-count').text(absentCount % 1 === 0 ? absentCount : absentCount.toFixed(1));
}

    function  store_reason(id){
      var selectedOption = $('#reason_data'+id+' option:selected');
      var name = selectedOption.text();
      var reasonId = selectedOption.val();
      $('#hidden_reason' + id).val(name);
      $('#hidden_reason_id' + id).val(reasonId);
    }



function sendattData() {
    Swal.fire({
        title: 'Are you sure?',
        text: "You are about to submit attendance for this class and date. Do you want to proceed?",
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, submit'
    }).then((result) => {
        if (result.isConfirmed) {
            $("#take_attendance").attr('disabled', true).html('Please Wait...');

            var classSec = $('#sectionid').val();
            var selectedDate = $('#select_date').val();

            if (!classSec || !selectedDate) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Section and Date are required.',
                    confirmButtonText: 'OK'
                });
                $("#take_attendance").attr('disabled', false).html('Submit');
                return;
            }

            var [classId, sectionId] = classSec.split('_');

            $('.morning, .afternoon').each(function () {
                var checkbox = $(this);
                var isChecked = checkbox.is(':checked');
                var name = checkbox.attr('name');
                if (!isChecked) {
                    if (!$(`#attData input[type="hidden"][name="${name}"]`).length) {
                        $('#attData').append(`<input type="hidden" name="${name}" value="0">`);
                    }
                }
            });

            if (!$('#attData input[name="class_sec"]').length) {
                $('#attData').append(`<input type="hidden" name="class_sec" value="${classSec}">`);
            }
            if (!$('#attData input[name="date"]').length) {
                $('#attData').append(`<input type="hidden" name="date" value="${selectedDate}">`);
            }
            if (!$('#attData input[name="att_taken_date"]').length) {
                $('#attData').append(`<input type="hidden" name="att_taken_date" value="${selectedDate}">`);
            }
            if (!$('#attData input[name="class_id"]').length) {
                $('#attData').append(`<input type="hidden" name="class_id" value="${classId}">`);
            }
            if (!$('#attData input[name="section_id"]').length) {
                $('#attData').append(`<input type="hidden" name="section_id" value="${sectionId}">`);
            }

            var form = $('#attData')[0];
            var attFormData = new FormData(form);

            $.ajax({
                url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/addAttData'); ?>',
                type: 'POST',
                data: attFormData,
                processData: false,
                contentType: false,
                success: function (data) {
                    let parsedData = JSON.parse(data);
                    if(parsedData > 0){
                      Swal.fire({
                          icon: 'success',
                          title: 'Success!',
                          text: 'Attendance submitted successfully.',
                          confirmButtonText: 'OK'
                      }).then(() => {
                          const enable_notification = "<?php echo $enable_notification; ?>";
                          if (enable_notification == 1) {
                              notify_students(data);
                          } else {
                            takeAttendance();
                          }
                      });
                    } else {
                      Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Failed to submit attendance. Please try again.',
                        confirmButtonText: 'OK'
                      }).then(()=>{
                        takeAttendance();
                      });
                    }
                },
                error: function (xhr, status, error) {
                    console.error('AJAX Error:', xhr.responseText);
                    $("#take_attendance").attr('disabled', false).html('Submit');
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Failed to submit attendance. Please try again.',
                        confirmButtonText: 'OK'
                    }).then(()=>{
                      takeAttendance();
                    });
                }
            });
        }
    });
}


  function notify_students(data) {
    const school_name = '<?php echo $this->settings->getSetting('school_name') ?>';
    const selectedDate= $('#select_date').val();
    const insert_id= data.trim();
    $.ajax({
      url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/getabsent_stds'); ?>',
      type: 'post',
      data: {
        'selectedDate':selectedDate,
        'insert_id':insert_id
      },
      success: function (data) {
        let students = JSON.parse(data);
        if (students?.length) {
          $("#notify-modal").modal('show');
          $('#notify-modal').one('hidden.bs.modal', function () {
              $("#take_attendance").attr('disabled', false).html('Submit');
              takeAttendance();
          });
          $('#cancelButton').off('click').on('click', function () {
            $("#take_attendance").attr('disabled', false).html('Submit');
            $('#notify-modal').hide();
            takeAttendance();
          });
          html = `<table class="table">
                      <thead>
                        <tr>
                          <th>#</th>
                          <th style="min-width: 120px;">Student</th>
                          <th>Section</th>
                          <th>Reason</th>
                          <th>Mobile No.</th>
                          <th>Relation</th>
                          <th>Message</th>
                        </tr>
                      </thead>
                      <tbody>`;

          students?.forEach((s,i)=>{
            let absentTemplate="<?php echo $this->settings->getSetting('student_attendancev2_day_attendance_absentee_message'); ?>";
            if(!absentTemplate){
              absentTemplate="Your ward %student_name% of %class_section% is absent today. Regards- Principal - %school_name%-Nextelement";
            }

            
            absentTemplate=absentTemplate.replaceAll("%student_name%",`${s.std_name}`);
            absentTemplate=absentTemplate.replaceAll("%class_section%",`${s.class_Section}`);
            absentTemplate=absentTemplate.replaceAll("%date%",`${s.attendance_taken_date}`);
            absentTemplate=absentTemplate.replaceAll("%school_name%",`${school_name}`);

            html += `
              <tr>
                  <td>${++i}</td>
                  <td>${s.std_name}</td>
                  <td>${s.class_Section}</td>
                  <td>Absent</td>
                  <td>${s.parentMobile}</td>
                  <td>${s.parentRelation}</td>
                  <td>
                      ${absentTemplate}
                      <input type="hidden" name="student_messages[${s.student_id}]" value="${absentTemplate}" />
                      <input type="hidden" name="session_insert_id" value="${insert_id}" />
                  </td>
              </tr>
            `;
          });

          html += `</tbody>
                </table>`;

          $("#notify-content").html(html);
        }else{
          takeAttendance();
        }
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function send_messages() {
    // Validate form before sending
    const communicationMode = $('input[name="communication_mode"]:checked').val();
    // Count how many student_messages[] are present (since they're hidden inputs, not checkboxes)
    const studentMessages = $('input[name^="student_messages["]').length;

    if (!communicationMode) {
      Swal.fire({
        icon: 'warning',
        title: 'Validation Error',
        text: 'Please select a communication mode (SMS or Notification)',
        confirmButtonText: 'OK'
      });
      return;
    }

    if (studentMessages === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'Validation Error',
        text: 'Please select at least one student to send messages to',
        confirmButtonText: 'OK'
      });
      return;
    }

    if (communicationMode === 'sms') {
      const textSendTo = $('select[name="text_send_to"]').val();
      if (!textSendTo) {
        Swal.fire({
          icon: 'warning',
          title: 'Validation Error',
          text: 'Please select who to send SMS to (Father, Mother, or Both)',
          confirmButtonText: 'OK'
        });
        return;
      }
    }

    $("#confirmBtn").attr('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Sending...');
    $("#take_attendance").attr('disabled', true);
    let formData = new FormData(document.getElementById('student-messages-form'));

    $.ajax({
      url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/send_messages'); ?>',
      type: 'post',
      data: formData,
      cache: false,
      contentType: false,
      processData: false,
      timeout: 30000, // 30 second timeout
      success: function (data) {
        $("#confirmBtn").attr('disabled', false).html('Confirm');
        $("#take_attendance").attr('disabled', false).html('Submit');
        $("#notify-modal").modal('hide');

        try {
          let response = JSON.parse(data);
          if (response.status == 1) {
            Swal.fire({
              icon: 'success',
              title: 'Success!',
              text: 'Messages sent successfully to parents.',
              confirmButtonText: 'Great!'
            }).then(()=>{
              takeAttendance();
            });
          } else {
            Swal.fire({
              icon: "error",
              title: 'Message Sending Failed',
              text: response.error || 'Failed to send messages',
              confirmButtonText: 'OK'
            }).then(()=>{
              takeAttendance();
            });
          }
        } catch (e) {
          console.error('Error parsing response:', e);
          Swal.fire({
            icon: "error",
            title: 'Error',
            text: 'Invalid response from server',
            confirmButtonText: 'OK'
          }).then(()=>{
            takeAttendance();
          });
        }
      },
      error: function (xhr, status, error) {
        $("#take_attendance").attr('disabled', false).html('Submit');
        $("#confirmBtn").attr('disabled', false).html('Confirm');
        $("#notify-modal").modal('hide');
        console.error('AJAX Error:', xhr.responseText);

        Swal.fire({
          icon: "error",
          title: 'Connection Error',
          text: 'Failed to connect to server. Please check your internet connection and try again.',
          confirmButtonText: 'OK'
        }).then(()=>{
          takeAttendance();
        });
      }
    });
  }

  $("#student-messages-form").click(e => {
      const msgType = e.target;

      if (msgType.type === "radio") {
          if (msgType.id === "sms") {
              $("#text_send_to").css("display", "inline-block");
          } else {
              $("#text_send_to").css("display", "none");
          }
      }
  });



$(document).on('click', '#getUser', function(e){
  e.preventDefault();
  var uid = $(this).data('index');
  $('#dynamic-content').html('');
  $('#modal-loader').hide();

  $.ajax({
        url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/getAttendanceHistory'); ?>',
        type: 'POST',
        data: 'id='+uid,
        dataType: 'html',
        success: function (data) {
          var data = JSON.parse(data);
          console.log(data);
          if(data.length ==0){
            html = '<div class="no-data-display">No History Found for Selected Student </div>';
          }else{
          html = '';
          html +='<table class="table table-bordered" cellspacing="0" width="100%">';
          html +=' <thead><tr>';
          html +='<th>Taken By</th><th>Day</th><th>Time</th><th>Remarks</th>';
          html +='</thead><tbody>';
          for (s = 0; s < data.length; s++) {
              var event_datetime = data[s].event_date;
              var parts = event_datetime.split(" "); // ['17-Jun-2025', '12:31', 'PM']

              var date_part = parts[0];
              var time_part = parts[1] + ' ' + parts[2]; // '12:31 PM'

              html += '<tr><td>' + data[s].taken_by + '</td>';
              html += '<td>' + date_part + '</td>';
              html += '<td>' + time_part + '</td>';
              html += '<td>' + data[s].history_data + '</td></tr>';
          }

        html +='</tbody></table>';
          }
          $("#dynamic-content").html(html);
        }
  })
});
</script>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
        <script>
            $(document).ready(function() {
                $('.datePicker').datepicker({
                    format: 'dd-mm-yyyy',
                    autoclose: true,
                    todayHighlight: true,
                    orientation: "bottom auto",
                    endDate: new Date()
                });
            });
        </script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<style type="text/css">
.title_edit_class {
    margin-left:-14px
}
.new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    float: left;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}
.status-tag {
    padding: 2px 8px;
    border-radius: 16px;
}
.success-status {
    r-color: #cdffcf;
}
.warning-status {
    background-color: blanchedalmond;
}
.default-status {
    background-color: #eaeaea;
}
.danger-status {
    background-color: #ffab9d;
}
.btn {
    margin-right: 4px;
    border-radius: 1.2rem;
}
.btn .fa {
    margin-right: 0px;
}
.btn-warning, .btn-info {
    width:  90px;
}
.content-div {
    padding: 5px 5px;
    border: 2px solid #ccc;
    border-radius: 10px;
    word-wrap: break-word;
}

.modal-dialog {
    width:60%;
    margin: auto;
}

.colorClass  {
    color: red;
}
</style>




