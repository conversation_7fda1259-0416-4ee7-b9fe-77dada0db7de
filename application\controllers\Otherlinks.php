<?php

class Otherlinks extends CI_Controller {

  public function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('OTHERLINKS')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('Otherlinks_model', 'otherlinks');
  }

  public function index() {
    
  }

  
}

?>
