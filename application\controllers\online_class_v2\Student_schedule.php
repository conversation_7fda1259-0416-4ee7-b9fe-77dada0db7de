<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Student_schedule extends CI_Controller {
    private $yearId;
    public function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->yearId = $this->acad_year->getAcadYearId();
        $this->load->library('staff_getter');
        $this->load->model('online_class_v2/Student_schedule_model', 'student_schedule');
        $this->load->model('class_section');
    }

    public function index() {
        $data['show_copy_link'] = 1;
        $show_link = $this->settings->getSetting('online_class_show_copy_link');
        if($show_link != '') {
            $data['show_copy_link'] = $show_link;
        }
        $this->load->model('Parent_model','parent_model');
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($student_id);
        $modules = json_decode($this->settings->getSetting('deactivation_modules'));
        $data['is_deactivated'] = 0;
        if($is_deactivated && in_array('Online Class', $modules)) {
            $data['is_deactivated'] = 1;
        }
        else {
            $data['is_deactivated'] = 0;
        }
        $data['is_mobile'] = 0;
        if ($this->mobile_detect->isTablet()) {
          $data['is_mobile'] = 1;
          $data['main_content'] = 'online_class_v2/student/tablet_schedules.php';
        }else if($this->mobile_detect->isMobile()){
          $data['is_mobile'] = 1;
          $data['main_content'] = 'online_class_v2/student/mobile_schedules.php';
        }else{
          $data['main_content'] = 'online_class_v2/student/schedules.php';  	
        }



        // if($this->mobile_detect->isMobile()) {
        //   $data['is_mobile'] = 1;
        //   $data['main_content'] = 'online_class_v2/student/mobile_schedules.php';
        // }else{
        //   $data['main_content'] = 'online_class_v2/student/schedules.php';
        // }
        $this->load->view('inc/template', $data);
    }

    public function get_schedules() {
        $this->load->model('parent_model');
        $schedule_date = date('Y-m-d', strtotime($_POST['schedule_date']));
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['schedules'] = $this->student_schedule->get_schedules($schedule_date, $student_id);
        echo json_encode($data);
    }

    public function join_class() {
        $invitee_id = $_POST['invitee_id'];
        echo $this->student_schedule->join_class($invitee_id);
    }

}